package cn.shrise.radium.dingdingservice.resp;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DdGetRosterInfoResp {

    private String corpId;
    private String userId;
    private List<FieldData> fieldDataList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldData {
        private String fieldName;
        private String fieldCode;
        private String groupId;
        private List<FieldValue> fieldValueList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldValue {
        private Long itemIndex;
        private String label;
        private String value;
    }

    public static List<DdGetRosterInfoResp> fromJson(String json) {
        return JSON.parseArray(json, DdGetRosterInfoResp.class);
    }
}

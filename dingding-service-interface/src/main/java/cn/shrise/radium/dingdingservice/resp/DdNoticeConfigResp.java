package cn.shrise.radium.dingdingservice.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DdNoticeConfigResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("消息类型")
    private String msgType;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("状态")
    private Boolean enabled;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("修改时间")
    private Instant gmtModified;
}

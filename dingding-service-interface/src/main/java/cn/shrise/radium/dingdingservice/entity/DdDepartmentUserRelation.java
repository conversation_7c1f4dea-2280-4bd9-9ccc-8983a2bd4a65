package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "dd_department_user_relation", indexes = {
        @Index(name = "uni_dept_staff", columnList = "account_type, dept_id, staff_id", unique = true)
})
@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DdDepartmentUserRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "account_type", nullable = false)
    private Integer accountType;

    @Column(name = "dept_id", nullable = false)
    private Integer deptId;

    @Column(name = "staff_id", nullable = false)
    private String staffId;

    @Column(name = "dept_order")
    private Long deptOrder;


}
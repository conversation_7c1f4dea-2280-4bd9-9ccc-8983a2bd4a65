package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "dd_dimission_info", indexes = {
        @Index(name = "uni_account_staff", columnList = "company_type, staff_id", unique = true)
})
public class DdDimissionInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "account_type", nullable = false)
    private Integer accountType;

    @Column(name = "staff_id", nullable = false, length = 64)
    private String staffId;

    @Lob
    @Column(name = "source_data")
    private String sourceData;

    @Column(name = "last_work_day")
    private Instant lastWorkDay;

    @Lob
    @Column(name = "dept_list")
    private String deptList;

    @Column(name = "reason_memo", length = 256)
    private String reasonMemo;

    @Column(name = "pre_status")
    private Integer preStatus;

    @Column(name = "handover_user_id", length = 64)
    private String handoverUserId;

    @Column(name = "status")
    private Integer status;

    @Column(name = "main_dept_name", length = 64)
    private String mainDeptName;

    @Column(name = "main_dept_id")
    private Integer mainDeptId;

    @Lob
    @Column(name = "voluntary_reason")
    private String voluntaryReason;

    @Lob
    @Column(name = "passive_reason")
    private String passiveReason;

}
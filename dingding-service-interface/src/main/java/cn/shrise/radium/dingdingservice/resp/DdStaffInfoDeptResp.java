package cn.shrise.radium.dingdingservice.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DdStaffInfoDeptResp {

    private Long id;

    private String staffId;

    private String jobNumber;

    private String name;

    private List<String> deptName;

    private String sexType;

    private String employeeStatus;

    private String profileData;

    private String certificate;

    private String certificateNo;

    private Boolean gwShow;

    private Instant lastWorkDay;
}

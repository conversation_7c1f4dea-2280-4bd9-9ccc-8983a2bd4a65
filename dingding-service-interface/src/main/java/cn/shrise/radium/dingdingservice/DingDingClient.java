package cn.shrise.radium.dingdingservice;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ServiceConstant;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.dingdingservice.constant.DingTalkAgentForEnum;
import cn.shrise.radium.dingdingservice.entity.DdNoticeConfigRecord;
import cn.shrise.radium.dingdingservice.entity.DdRosterInfo;
import cn.shrise.radium.dingdingservice.entity.DdStaffInfo;
import cn.shrise.radium.dingdingservice.property.CompanyDingTalkProperties;
import cn.shrise.radium.dingdingservice.req.CreateDimissionInfoReq;
import cn.shrise.radium.dingdingservice.req.UpdateDingDingStaffGwShowReq;
import cn.shrise.radium.dingdingservice.resp.DdGetRosterInfoResp;
import cn.shrise.radium.dingdingservice.resp.DdNoticeConfigResp;
import cn.shrise.radium.dingdingservice.resp.DdRosterResp;
import cn.shrise.radium.dingdingservice.resp.DdStaffInfoDeptResp;
import com.aliyun.dingtalkhrm_1_0.models.QueryHrmEmployeeDismissionInfoResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

/**
 * <AUTHOR>
 */
@FeignClient(name = ServiceConstant.DINGDING_SERVICE)
public interface DingDingClient {

//    @ApiOperation("回调的基类方法")
//    @GetMapping("ding_talk/notify_base")
//    BaseResult<String> processCheckSignature(
//            @RequestParam @ApiParam("消息签名") String msgSignature,
//            @RequestParam @ApiParam("时间戳") String timestamp,
//            @RequestParam @ApiParam("随机数，由企业自行生成") String nonce,
//            @RequestParam @ApiParam("加密消息体") String msg,
//            @RequestParam @ApiParam("企业微信公司类型") Integer accountType,
//            @RequestParam @ApiParam("应用ID") Long agentId);

    @ApiOperation("企业微信消息加解密")
    @GetMapping("ding_talk/decrypt_notify")
    BaseResult<String> processDecryptNotify(
            @RequestParam @ApiParam("消息签名") String msgSignature,
            @RequestParam @ApiParam("时间戳") String timestamp,
            @RequestParam @ApiParam("随机数，由企业自行生成") String nonce,
            @RequestParam @ApiParam("加密消息体") String msg,
            @RequestParam @ApiParam("企业微信公司类型") Integer accountType,
            @RequestParam @ApiParam("区分回调类型") DingTalkAgentForEnum agentFor);

    @ApiOperation("获取钉钉员工信息")
    @GetMapping("dingding/staff/{ddId}")
    BaseResult<DdStaffInfo> getStaffInfoById(@PathVariable @ApiParam("钉钉员工id") Long ddId);

    @ApiOperation("获取钉钉员工信息")
    @GetMapping("dingding/staff/byCode")
    BaseResult<DdStaffInfo> getStaffInfoByCode(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("钉钉类型") Integer accountType,
            @RequestParam @ApiParam("应用类型") String agentFor,
            @RequestParam @ApiParam("code") String authCode);

    @ApiOperation("获取指定数量的钉钉员工信息")
    @GetMapping("dingding/staff/byCount")
    BaseResult<List<DdStaffInfo>> getStaffInfoByCount(
            @RequestParam @ApiParam("钉钉类型") Integer accountType,
            @RequestParam @ApiParam("pkId") Long pkId,
            @RequestParam(required = false) @ApiParam("是否在职") Boolean isEnabled,
            @RequestParam @ApiParam("获取条数") Integer count);

    @ApiOperation("获取钉钉应用配置信息")
    @GetMapping("dingding/config")
    BaseResult<CompanyDingTalkProperties.AppConfig> getDdConfigInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("钉钉类型") Integer accountType,
            @RequestParam @ApiParam("应用类型") String agentFor);

    @ApiOperation("获取钉钉应用配置信息")
    @GetMapping("dingding/config/byCompanyType")
    BaseResult<List<CompanyDingTalkProperties.DingTalkProperties>> getDdConfigInfoByCompanyType(
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @ApiOperation("获取钉钉用户信息Map")
    @GetMapping("dingding/staff/map")
    Map<Long, DdStaffInfo> getStaffInfoMap(@RequestBody BatchReq<Long> req);

    @ApiOperation("钉钉员工管理")
    @GetMapping("dingding/staff/list")
    PageResult<List<DdStaffInfoDeptResp>> getDdStaffInfoPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("性别") String sexType,
            @RequestParam(required = false) @ApiParam("是否展示在官网") Boolean gwShow,
            @RequestParam(required = false) @ApiParam("员工状态") Integer employeeStatus,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("dingding/staff/by/name")
    @ApiOperation("钉钉姓名模糊搜索")
    List<Long> searchByName(@RequestParam(required = false) @ApiParam("搜索内容") String searchContent);

    @ApiOperation("钉钉姓名模糊搜索/员工工号精确搜索")
    @GetMapping("dingding/staff/info/search")
    BaseResult<List<DdStaffInfoDeptResp>> getDdStaffInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("搜索内容") String searchContent);

    @GetMapping("dingding/staff/detail")
    @ApiOperation("钉钉员工详情")
    BaseResult<List<DdRosterResp>> getDingDingStaffDetail(@RequestParam @ApiParam("staffId") String staffId);

    @ApiOperation("获取官网展示的员工列表")
    @GetMapping("dingding/staff/gw")
    PageResult<List<DdStaffInfo>> getDingDingStaffGwShowList(
            @RequestParam Integer companyType,
            @RequestParam Boolean gwShow,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size
    );

    @ApiOperation("修改员工官网展示状态")
    @PostMapping("dingding/staff/gw")
    BaseResult<Void> updateDingDingStaffGwShow(@RequestBody @Valid UpdateDingDingStaffGwShowReq req);

    @ApiOperation("批量获取钉钉花名册")
    @PostMapping("dingding/roster/batch")
    BaseResult<List<DdRosterInfo>> getDingDingRosterList(@RequestBody @Valid BatchReq<String> req);

    @ApiOperation("批量获取钉钉花名册信息")
    @PostMapping("dingding/roster/batch/Info")
    BaseResult<List<DdGetRosterInfoResp>> batchRosterInfo(@RequestParam @ApiParam("钉钉类型") Integer accountType,
                                                          @RequestParam @ApiParam("应用类型") String agentFor,
                                                          @RequestBody @Valid BatchReq<String> req) throws Exception;

    @ApiOperation("批量创建/更新钉钉花名册信息")
    @PostMapping("dingding/roster/batch/createOrUpdate")
    BaseResult<String> batchCreateOrUpdateRosterList(@RequestBody @Valid BatchReq<DdRosterInfo> req);

    @ApiOperation("批量获取钉钉离职信息")
    @PostMapping("dingding/dimission/batch/list")
    QueryHrmEmployeeDismissionInfoResponse batchDingDingDimissionInfo(@RequestParam @ApiParam("钉钉类型") Integer accountType,
                                                                      @RequestParam @ApiParam("应用类型") String agentFor,
                                                                      @RequestBody @Valid BatchReq<String> req) throws Exception;

    @ApiOperation("批量创建/更新钉钉离职信息")
    @PostMapping("dingding/dimission/batch/createOrUpdate")
    BaseResult<String> batchCreateOrUpdateDimissionList(@RequestBody @Valid BatchReq<CreateDimissionInfoReq> req);

    @PostMapping("dingding-notice/create")
    @ApiOperation("创建钉钉提醒配置")
    BaseResult<Void> createDingDingNoticeConfig(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("编号") String number,
            @RequestParam @ApiParam("消息类型") String msgType,
            @RequestParam @ApiParam("描述") String description,
            @RequestParam @ApiParam("消息内容") String content
    );

    @PostMapping("dingding-notice/update")
    @ApiOperation("修改钉钉提醒配置")
    BaseResult<Void> updateDingDingNoticeConfig(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("配置id") Long configId,
            @RequestParam @ApiParam("消息类型") String msgType,
            @RequestParam @ApiParam("描述") String description,
            @RequestParam @ApiParam("消息内容") String content
    );

    @PostMapping("dingding-notice/enable")
    @ApiOperation("启用/禁用钉钉提醒配置")
    BaseResult<Void> enableDingDingNoticeConfig(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("配置id") Long configId,
            @RequestParam @ApiParam("启用/禁用") Boolean enabled
    );

    @GetMapping("dingding-notice/list")
    @ApiOperation("钉钉提醒配置列表")
    PageResult<List<DdNoticeConfigResp>> getDingDingNoticeConfigList(
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

    @GetMapping("dingding-notice/record/list")
    @ApiOperation("钉钉提醒配置操作记录列表")
    PageResult<List<DdNoticeConfigRecord>> getDingDingNoticeConfigRecordList(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

}

package cn.shrise.radium.dingdingservice.constant;

import java.util.HashMap;
import java.util.Map;

public class DingTalkRosterColumnMap {
    public static final Map<String, String> columnMap = new HashMap<String, String>() {{
        put("主要工作经历1（单位名称）", "mainWorkExperienceOne");
        put("主要工作经历2（单位名称）", "mainWorkExperienceTwo");
        put("主要工作经历3（单位名称）", "mainWorkExperienceThree");
        put("执业证书", "practiceCertificate");
        put("执业编号", "practiceNumber");
        put("实习部门岗位", "internshipDeptPosition");
        put("部门及岗位1", "deptPositionOne");
        put("同行经验", "peerExperience");
        put("岗位调整前（职位）", "beforePostAdjustPosition");
        put("岗位调整前（部门）", "beforePostAdjustDept");
        put("岗位调整后（职位）", "afterPostAdjustPosition");
        put("岗位调整后（部门）", "afterPostAdjustDept");
    }};
}

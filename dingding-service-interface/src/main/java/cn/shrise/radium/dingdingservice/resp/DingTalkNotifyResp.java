package cn.shrise.radium.dingdingservice.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DingTalkNotifyResp {

    private String eventType;

    private Map<String, Object> fieldsMap;

    private Integer accountType;


}

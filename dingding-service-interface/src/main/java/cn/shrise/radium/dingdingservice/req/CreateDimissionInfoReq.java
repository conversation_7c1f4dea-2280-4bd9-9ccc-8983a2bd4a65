package cn.shrise.radium.dingdingservice.req;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.dingdingservice.resp.DtDimissionInfoResp;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CreateDimissionInfoReq {

    @NotNull
    private Integer companyType;

    @NotNull
    private Integer accountType;

    @NotNull
    private String staffId;

    private String sourceData;

    private Instant lastWorkDay;

    private String deptList;

    private String reasonMemo;

    private Integer preStatus;

    private String handoverUserId;

    private Integer status;

    private String mainDeptName;

    private Integer mainDeptId;

    private String voluntaryReason;

    private String passiveReason;

    public static CreateDimissionInfoReq of(DtDimissionInfoResp dtDimissionInfoResp){
        CreateDimissionInfoReq req = new CreateDimissionInfoReq();
        BeanUtils.copyProperties(dtDimissionInfoResp,req);
        req.setStaffId(dtDimissionInfoResp.getUserId());
        if (ObjectUtil.isNotEmpty(dtDimissionInfoResp.getLastWorkDay())){
            req.setLastWorkDay(Instant.ofEpochMilli(dtDimissionInfoResp.getLastWorkDay()));
        }
        req.setDeptList(JSONUtil.toJsonStr(dtDimissionInfoResp.getDeptList()));
        req.setVoluntaryReason(JSONUtil.toJsonStr(dtDimissionInfoResp.getVoluntaryReason()));
        req.setPassiveReason(JSONUtil.toJsonStr(dtDimissionInfoResp.getPassiveReason()));
        return req;
    }
}

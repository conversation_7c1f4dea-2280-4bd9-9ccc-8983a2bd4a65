package cn.shrise.radium.dingdingservice.req;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class UpdateDingDingStaffGwShowReq {

    @NotNull
    private Long id;

    @NotNull
    private Boolean gwShow;

    @NotNull
    private Integer operatorId;
}

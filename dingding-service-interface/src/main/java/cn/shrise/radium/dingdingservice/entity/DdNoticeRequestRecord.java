package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.Instant;

@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "dd_notice_request_record", schema = "dingding_db")
public class DdNoticeRequestRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "number", nullable = false, length = 128)
    private String number;

    @Column(name = "request_body", length = 2048)
    private String requestBody;

    @Column(name = "response_result", length = 2048)
    private String responseResult;

    @Column(name = "is_success")
    private Boolean isSuccess;

}
package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "dd_staff_info", indexes = {
        @Index(name = "idx_staff", columnList = "staff_id"),
        @Index(name = "uni_staff", columnList = "account_type, staff_id", unique = true)
})
@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DdStaffInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "account_type", nullable = false)
    private Integer accountType;

    @Column(name = "staff_id", nullable = false, length = 64)
    private String staffId;

    @Column(name = "union_id", length = 64)
    private String unionId;

    @Column(name = "name", length = 16)
    private String name;

    @Column(name = "avatar", length = 128)
    private String avatar;

    @Column(name = "state_code", length = 32)
    private String stateCode;

    @Column(name = "manager_id", length = 64)
    private String managerId;

    @Column(name = "mobile", length = 64)
    private String mobile;

    @Column(name = "hide_mobile")
    private Boolean hideMobile;

    @Column(name = "telephone", length = 16)
    private String telephone;

    @Column(name = "job_number", length = 64)
    private String jobNumber;

    @Column(name = "title", length = 64)
    private String title;

    @Column(name = "email", length = 64)
    private String email;

    @Column(name = "work_place", length = 32)
    private String workPlace;

    @Lob
    @Column(name = "remark")
    private String remark;

    @Column(name = "exclusive_account")
    private Boolean exclusiveAccount;

    @Column(name = "org_email", length = 64)
    private String orgEmail;

    @Lob
    @Column(name = "dept_id_list")
    private String deptIdList;

    @Lob
    @Column(name = "dept_order_list")
    private String deptOrderList;

    @Lob
    @Column(name = "extension")
    private String extension;

    @Column(name = "hired_date")
    private Long hiredDate;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "real_authed")
    private Boolean realAuthed;

    @Column(name = "senior")
    private Boolean senior;

    @Column(name = "admin")
    private Boolean admin;

    @Column(name = "boss")
    private Boolean boss;

    @Lob
    @Column(name = "leader_in_dept")
    private String leaderInDept;

    @Lob
    @Column(name = "role_list")
    private String roleList;

    @Lob
    @Column(name = "union_emp_ext")
    private String unionEmpExt;

    @Column(name = "enabled", nullable = false, columnDefinition = "integer default 1")
    private Boolean enabled;

    @Column(name = "gw_show", nullable = false, columnDefinition = "integer default 0")
    private Boolean gwShow;
}
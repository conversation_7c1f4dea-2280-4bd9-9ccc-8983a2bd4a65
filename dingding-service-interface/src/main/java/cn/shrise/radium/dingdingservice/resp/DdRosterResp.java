package cn.shrise.radium.dingdingservice.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DdRosterResp {

    private String groupId;
    private List<RosterResp> rosterList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RosterResp {
        private String fieldCode;
        private String fieldName;
        private Long itemIndex;
        private String fieldLabel;
        private String fieldValue;
    }
}

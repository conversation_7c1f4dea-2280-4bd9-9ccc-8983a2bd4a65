package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "dd_department_info", indexes = {
        @Index(name = "uni_dept", columnList = "account_type, dept_id", unique = true),
        @Index(name = "idx_dept", columnList = "dept_id")
})
@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DdDepartmentInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "account_type", nullable = false)
    private Integer accountType;

    @Column(name = "dept_id", nullable = false)
    private Integer deptId;

    @Column(name = "name", length = 16)
    private String name;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "source_identifier", length = 16)
    private String sourceIdentifier;

    @Column(name = "create_dept_group")
    private Boolean createDeptGroup;

    @Column(name = "auto_add_user")
    private Boolean autoAddUser;

    @Column(name = "auto_approve_apply")
    private Boolean autoApproveApply;

    @Column(name = "from_union_org")
    private Boolean fromUnionOrg;

    @Lob
    @Column(name = "tags")
    private String tags;

    @Column(name = "dept_order")
    private Long deptOrder;

    @Column(name = "dept_group_chat_id", length = 64)
    private String deptGroupChatId;

    @Column(name = "group_contain_sub_dept")
    private Boolean groupContainSubDept;

    @Column(name = "org_dept_owner", length = 32)
    private String orgDeptOwner;

    @Lob
    @Column(name = "dept_manager_userid_list")
    private String deptManagerUserIdList;

    @Column(name = "outer_dept")
    private Boolean outerDept;

    @Lob
    @Column(name = "outer_permit_depts")
    private String outerPermitDepts;

    @Lob
    @Column(name = "outer_permit_users")
    private String outerPermitUsers;

    @Column(name = "hide_dept")
    private Boolean hideDept;

    @Lob
    @Column(name = "user_permits")
    private String userPermits;

    @Lob
    @Column(name = "dept_permits")
    private String deptPermits;

    @Column(name = "enabled", nullable = false, columnDefinition = "integer default 1")
    private Boolean enabled;

}
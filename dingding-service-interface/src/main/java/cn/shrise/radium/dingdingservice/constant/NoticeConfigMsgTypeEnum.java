package cn.shrise.radium.dingdingservice.constant;

public enum NoticeConfigMsgTypeEnum {

    TEXT("text","文本"),
    VOICE("voice","语音"),
    IMAGE("image","图片"),
    OA("oa","OA"),
    FILE("file","文本"),
    ACTION_CARD("action_card","卡片"),
    LINK("link","链接"),
    MARKDOWN("markdown","Markdown"),
    ;

    private final String type;
    private final String value;

    public String getType() {
        return this.type;
    }

    public String getValue() {
        return this.value;
    }

    NoticeConfigMsgTypeEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }

    public static NoticeConfigMsgTypeEnum findMsgType(String type){
        for (NoticeConfigMsgTypeEnum value : NoticeConfigMsgTypeEnum.values()) {
            if (value.type.equals(type)){
                return value;
            }
        }
        return null;
    }
}

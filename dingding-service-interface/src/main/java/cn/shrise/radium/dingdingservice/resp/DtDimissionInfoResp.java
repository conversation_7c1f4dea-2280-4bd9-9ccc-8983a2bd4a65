package cn.shrise.radium.dingdingservice.resp;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DtDimissionInfoResp {

    // 员工userId
    private String userId;
    // 最后工作日
    private Long lastWorkDay;
    // 离职部门列表
    private List<DeptList> deptList;
    // 离职原因备注
    private String reasonMemo;
    // 离职前工作状态 1：待入职；2：试用期；3：正式
    private Integer preStatus;
    // 离职交接人的userId
    private String handoverUserId;
    // 离职状态 1：待离职;2：已离职;3：非待离职或非已离职;4：已提交离职审批单，审批单暂未通过
    private Integer status;
    // 离职前主部门名称
    private String mainDeptName;
    // 离职前主部门ID
    private Integer mainDeptId;
    // 主动原因
    private List<String> voluntaryReason;
    // 被动原因
    private List<String> passiveReason;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeptList {
        // 部门路径
        @SerializedName("dept_path")
        private String deptPath;
        // 部门ID
        @SerializedName("dept_id")
        private Integer deptId;
    }
}

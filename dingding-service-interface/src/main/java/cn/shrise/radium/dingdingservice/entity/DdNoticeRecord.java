package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.Instant;

@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "dd_notice_record", schema = "dingding_db")
public class DdNoticeRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "event_type", nullable = false)
    private Integer eventType;

    @Column(name = "event_id", nullable = false)
    private Long eventId;

    @Column(name = "user_id", nullable = false)
    private Integer userId;

    @Column(name = "config_id")
    private Long configId;

    @Column(name = "content", length = 2048)
    private String content;

    @Column(name = "request_number", length = 128)
    private String requestNumber;

    @Column(name = "status", nullable = false)
    private Integer status;

}
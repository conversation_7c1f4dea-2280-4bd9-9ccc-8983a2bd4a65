package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.Instant;

@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "dd_notice_config", schema = "dingding_db", indexes = {
        @Index(name = "uk_number", columnList = "number", unique = true)
})
public class DdNoticeConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "number", nullable = false, length = 128)
    private String number;

    @Column(name = "msg_type", nullable = false, length = 64)
    private String msgType;

    @Column(name = "description", length = 1024)
    private String description;

    @Column(name = "content", nullable = false, length = 1024)
    private String content;

    @Column(name = "enabled", nullable = false, insertable = false, columnDefinition = "integer default 1")
    private Boolean enabled;

}
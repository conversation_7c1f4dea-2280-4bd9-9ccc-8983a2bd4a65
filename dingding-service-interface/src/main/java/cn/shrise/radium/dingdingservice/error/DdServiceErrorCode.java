package cn.shrise.radium.dingdingservice.error;

import cn.shrise.radium.common.base.BaseError;

public enum DdServiceErrorCode implements BaseError {

    FAILURE(-1, "Failure"),
    SUCCESS(0,"Success"),
    RECORD_NOT_EXISTED(1000, "Record Not Existed"),
    RECORD_EXISTED(1001, "Record Has Existed"),
    PARAM_INVALID(10000, "参数错误"),
    ;

    private final Integer code;
    private final String msg;

    DdServiceErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

}

package cn.shrise.radium.dingdingservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "dd_roster_info", indexes = {
        @Index(name = "uni_account_staff", columnList = "account_type, staff_id", unique = true)
})
@Data
@Builder
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DdRosterInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "account_type", nullable = false)
    private Integer accountType;

    @Column(name = "staff_id", nullable = false, length = 64)
    private String staffId;

    @Lob
    @Column(name = "profile_data")
    private String profileData;

    @Column(name = "name", length = 32)
    private String name;

    @Column(name = "email", length = 64)
    private String email;

    @Lob
    @Column(name = "dept_id_list")
    private String deptIdList;

    @Column(name = "main_dept_id", length = 16)
    private String mainDeptId;

    @Lob
    @Column(name = "dept")
    private String dept;

    @Column(name = "main_dept", length = 16)
    private String mainDept;

    @Column(name = "position", length = 16)
    private String position;

    @Column(name = "job_number", length = 32)
    private String jobNumber;

    @Column(name = "work_place", length = 128)
    private String workPlace;

    @Lob
    @Column(name = "remark")
    private String remark;

    @Column(name = "confirm_join_time", length = 16)
    private String confirmJoinTime;

    @Column(name = "employee_type", length = 16)
    private String employeeType;

    @Column(name = "employee_status", length = 16)
    private String employeeStatus;

    @Column(name = "probation_period_type", length = 16)
    private String probationPeriodType;

    @Column(name = "regular_time", length = 16)
    private String regularTime;

    @Column(name = "position_level", length = 16)
    private String positionLevel;

    @Column(name = "real_name", length = 16)
    private String realName;

    @Column(name = "birth_time", length = 16)
    private String birthTime;

    @Column(name = "sex_type", length = 16)
    private String sexType;

    @Column(name = "nation_type", length = 16)
    private String nationType;

    @Column(name = "cert_address", length = 64)
    private String certAddress;

    @Column(name = "cert_end_time", length = 16)
    private String certEndTime;

    @Column(name = "marriage", length = 16)
    private String marriage;

    @Column(name = "join_working_time", length = 16)
    private String joinWorkingTime;

    @Column(name = "residence_type", length = 16)
    private String residenceType;

    @Column(name = "address", length = 16)
    private String address;

    @Column(name = "political_status", length = 16)
    private String politicalStatus;

    @Column(name = "personal_si", length = 32)
    private String personalSi;

    @Column(name = "personal_hf", length = 32)
    private String personalHf;

    @Column(name = "highest_edu", length = 16)
    private String highestEdu;

    @Column(name = "graduate_school", length = 32)
    private String graduateSchool;

    @Column(name = "graduation_time", length = 16)
    private String graduationTime;

    @Column(name = "major", length = 16)
    private String major;

    @Column(name = "contract_company_name", length = 32)
    private String contractCompanyName;

    @Column(name = "contract_type", length = 16)
    private String contractType;

    @Column(name = "first_contract_start_time", length = 16)
    private String firstContractStartTime;

    @Column(name = "first_contract_end_time", length = 16)
    private String firstContractEndTime;

    @Column(name = "now_contract_start_time", length = 16)
    private String nowContractStartTime;

    @Column(name = "now_contract_end_time", length = 16)
    private String nowContractEndTime;

    @Column(name = "contract_period_type", length = 16)
    private String contractPeriodType;

    @Column(name = "contract_renew_count", length = 16)
    private String contractRenewCount;

    @Column(name = "have_child")
    private Boolean haveChild;

    @Column(name = "child_name", length = 16)
    private String childName;

    @Column(name = "child_sex", length = 4)
    private String childSex;

    @Column(name = "child_birth_date", length = 16)
    private String childBirthDate;

    @Column(name = "mobile", length = 64)
    private String mobile;

    @Column(name = "cert_no", length = 64)
    private String certNo;

    @Column(name = "plain_cert_no", length = 64)
    private String plainCertNo;

    @Column(name = "report_manager", length = 16)
    private String reportManager;

    @Column(name = "entry_age", length = 16)
    private String entryAge;

    @Column(name = "age", length = 16)
    private String age;

    @Column(name = "work_age", length = 16)
    private String workAge;

    @Column(name = "main_work_experience_one", length = 32)
    private String mainWorkExperienceOne;

    @Column(name = "main_work_experience_two", length = 32)
    private String mainWorkExperienceTwo;

    @Column(name = "main_work_experience_three", length = 32)
    private String mainWorkExperienceThree;

    @Column(name = "practice_certificate", length = 16)
    private String practiceCertificate;

    @Column(name = "practice_number", length = 16)
    private String practiceNumber;

    @Column(name = "internship_dept_position", length = 32)
    private String internshipDeptPosition;

    @Column(name = "dept_position_one", length = 32)
    private String deptPositionOne;

    @Column(name = "peer_experience", length = 32)
    private String peerExperience;

    @Column(name = "before_post_adjust_position", length = 32)
    private String beforePostAdjustPosition;

    @Column(name = "before_post_adjust_dept", length = 32)
    private String beforePostAdjustDept;

    @Column(name = "after_post_adjust_position", length = 32)
    private String afterPostAdjustPosition;

    @Column(name = "after_post_adjust_dept", length = 32)
    private String afterPostAdjustDept;

}
package cn.shrise.radium.dingdingservice.constant;

public enum DingDingEmployeeStatusEnum {

    PROBATION(1,"试用"),
    FORMAL(2,"正式"),
    WAITING_LEAVE(3,"待离职"),
    LEAVE(4,"离职"),
    ;

    private final Integer code;
    private final String status;

    DingDingEmployeeStatusEnum(Integer code, String status) {
        this.code = code;
        this.status = status;
    }

    public static String findStatusByCode(Integer code){

        for (DingDingEmployeeStatusEnum value : DingDingEmployeeStatusEnum.values()) {
            if (value.code.equals(code)){
                return value.status;
            }
        }
        return null;
    }
}

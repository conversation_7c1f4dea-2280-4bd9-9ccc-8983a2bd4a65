package cn.shrise.radium.dingdingservice.property;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "dingtalk")
public class CompanyDingTalkProperties {

    @NestedConfigurationProperty
    private Map<Integer, List<DingTalkProperties>> config;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DingTalkProperties{
        private Integer accountType;
        private String corpId;
        private String corpName;
        private List<AppConfig> appConfigs;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppConfig{
        private Long agentId;
        private String agentFor;
        private String appKey;
        private String appSecret;
        private String aesKey;
        private String token;
        private String ownerKey;
    }
}

package cn.shrise.radium.tradeservice.repository;

import cn.shrise.radium.tradeservice.entity.TdStockDiagnoseInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/28 19:54
 * @Desc:
 **/
public interface StockDiagnoseInfoRepository extends JpaRepository<TdStockDiagnoseInfo, Long>, QuerydslPredicateExecutor<TdStockDiagnoseInfo> {
}

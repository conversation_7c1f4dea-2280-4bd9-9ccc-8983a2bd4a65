package cn.shrise.radium.tradeservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.tradeservice.entity.QTdNoticeSubInfo;
import cn.shrise.radium.tradeservice.entity.TdNoticeSubInfo;
import cn.shrise.radium.tradeservice.entity.TdNoticeSubRecord;
import cn.shrise.radium.tradeservice.repository.NoticeSubInfoRepository;
import cn.shrise.radium.tradeservice.req.CreateSubReq;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class NoticeSubService {

    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;

    private final NoticeSubInfoRepository subInfoRepository;

    private final QTdNoticeSubInfo tdNoticeSubInfo = QTdNoticeSubInfo.tdNoticeSubInfo;

    @Transactional(rollbackOn = Exception.class)
    public void createOrUpdateOne(Integer companyType, Integer customerId, Integer noticeType, Boolean enabled) {
        TdNoticeSubInfo info = TdNoticeSubInfo.builder()
                .companyType(companyType)
                .customerId(customerId)
                .noticeType(noticeType)
                .enabled(enabled)
                .build();
        String sqlStr = SqlUtil.onDuplicateKeyUpdateSql(Collections.singletonList(info));
        jdbcTemplate.batchUpdate(sqlStr);
    }

    @Transactional(rollbackOn = Exception.class)
    public void createOrUpdateList(List<TdNoticeSubInfo> infoList) {
        if (ObjectUtils.isNotEmpty(infoList)) {
            String sqlStr = SqlUtil.onDuplicateKeyUpdateSql(infoList);
            jdbcTemplate.batchUpdate(sqlStr);
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void createSubInfo(Integer companyType, Integer customerId, List<CreateSubReq> noticeList, Integer operatorId) {
        List<TdNoticeSubInfo> infoList = new ArrayList<>();
        List<TdNoticeSubRecord> recordList = new ArrayList<>();
        for (CreateSubReq req : noticeList) {
            TdNoticeSubInfo info = TdNoticeSubInfo.builder()
                    .companyType(companyType)
                    .customerId(customerId)
                    .noticeType(req.getNoticeType())
                    .enabled(req.getIsSub())
                    .build();
            infoList.add(info);
            TdNoticeSubRecord record = TdNoticeSubRecord.builder()
                    .isSub(req.getIsSub())
                    .operatorId(operatorId)
                    .noticeType(req.getNoticeType())
                    .customerId(customerId)
                    .companyType(companyType)
                    .createTime(Instant.now())
                    .build();
            recordList.add(record);
        }
        String infoSql = SqlUtil.onDuplicateKeyUpdateSql(infoList);
        if (infoSql != null) {
            jdbcTemplate.batchUpdate(infoSql);
        }

        String recordSql = SqlUtil.onDuplicateKeyUpdateSql(recordList);
        if (recordSql != null) {
            jdbcTemplate.batchUpdate(recordSql);
        }
    }

    public List<TdNoticeSubInfo> getSubList(Integer userId, Boolean isSub) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(tdNoticeSubInfo.customerId.eq(userId));
        if (ObjectUtil.isNotNull(isSub)) {
            builder.and((tdNoticeSubInfo.enabled).eq(isSub));
        }
        return queryFactory
                .selectFrom(tdNoticeSubInfo)
                .where(builder)
                .fetch();
    }

    public List<TdNoticeSubInfo> findByCustomer(Set<Integer> customerIds) {
        return queryFactory.select(tdNoticeSubInfo)
                .from(tdNoticeSubInfo)
                .where(tdNoticeSubInfo.customerId.in(customerIds), tdNoticeSubInfo.enabled.isFalse())
                .fetch();
    }
}

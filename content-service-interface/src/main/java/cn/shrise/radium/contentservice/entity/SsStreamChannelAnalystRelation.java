package cn.shrise.radium.contentservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Table(name = "ss_stream_channel_analyst_relation", indexes = {
        @Index(name = "idx_analyst_id", columnList = "analyst_id"),
        @Index(name = "idx_channel_id", columnList = "channel_id")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsStreamChannelAnalystRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false)
    private Instant gmtModified;

    @Column(name = "channel_id")
    private Long channelId;

    @Column(name = "analyst_id")
    private Integer analystId;

    @Column(name = "enabled")
    private Boolean enabled;
}
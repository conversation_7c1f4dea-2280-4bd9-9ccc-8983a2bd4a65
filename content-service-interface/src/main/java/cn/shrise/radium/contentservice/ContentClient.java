package cn.shrise.radium.contentservice;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ServiceConstant;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.cls.entity.LianSubject;
import cn.shrise.radium.contentservice.cls.entity.LianV1Article;
import cn.shrise.radium.contentservice.dto.SsRoomLotteryDetailDto;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.req.*;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.contentservice.resp.cls.*;
import cn.shrise.radium.contentservice.resp.index.QuoteIndexResp;
import com.aliyun.oss.model.ListObjectsV2Result;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.vod.model.v20170321.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@FeignClient(name = ServiceConstant.CONTENT_SERVICE)
public interface ContentClient {

    @ApiOperation("获取直播间奖品列表")
    @GetMapping("room/prize")
    PageResult<List<SsRoomPrize>> getRoomPrizeList(
            @RequestParam(required = false) @ApiParam("奖品ID") Long id,
            @RequestParam(required = false) @ApiParam("启用禁用") Boolean isEnabled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("新增奖品")
    @PostMapping("room/prize")
    BaseResult<SsRoomPrize> createRoomPrize(
            @RequestBody @Valid CreateRoomPrizeReq req);

    @ApiOperation("修改奖品状态")
    @PutMapping("room/prize")
    BaseResult<SsRoomPrize> updateRoomPrize(
            @RequestParam(required = false) @ApiParam("奖品ID") Long id,
            @RequestParam(required = false) @ApiParam("启用禁用") Boolean isEnabled);

    @ApiOperation("获取我的奖品列表")
    @GetMapping("room/prize/my")
    PageResult<List<MyLotteryPrizeResp>> findMyPrizeList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("获取单个中奖明细")
    @GetMapping("room/prize/detail")
    BaseResult<MyLotteryPrizeResp> findOnePrize(
            @RequestParam @ApiParam("抽奖明细id") Long detailId);

    @ApiOperation("参与抽奖活动")
    @PostMapping("room/lottery/participate")
    BaseResult<Void> participateLottery(
            @RequestBody @Validated CreateParticipateLotteryReq req);

    @ApiOperation("获取抽奖活动列表")
    @GetMapping("room/lottery")
    PageResult<List<RoomLotteryResp>> getRoomLottery(
            @RequestParam(required = false) @ApiParam("直播室ID") List<Long> roomList,
            @RequestParam(required = false) @ApiParam("场次Id") Long sceneId,
            @RequestParam(required = false) @ApiParam("活动状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("销售ID") Integer salesId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("room/lottery/valid")
    @ApiOperation("获取有效抽奖活动")
    BaseResult<List<RoomLotteryResp>> getValidLotteryList(
            @RequestParam @ApiParam("场次id") Long sceneId);

    @ApiOperation("获取中奖列表")
    @GetMapping("room/lottery/winner")
    PageResult<List<LotteryWinnerResp>> getLotteryWinnerList(
            @RequestParam @ApiParam("抽奖活动id") Long lotteryId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("room/lottery/detail")
    @ApiOperation("获取抽奖活动详情")
    BaseResult<LotteryDetailResp> getRoomLotteryDetail(
            @RequestParam @ApiParam("抽奖活动ID") Long lotteryId,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId);

    @ApiOperation("获取直播室名称")
    @GetMapping("room/name")
    BaseResult<List<LiveRoomNameResp>> getLiveRoomName(
            @RequestParam(required = false) @ApiParam("直播室ID") Collection<Long> roomIds,
            @RequestParam(required = false) @ApiParam("是否直播") Boolean isLive,
            @RequestParam(required = false) @ApiParam("启用禁用") Boolean isDeleted,
            @RequestParam(required = false) @ApiParam("直播室类型") Integer roomType,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("销售ID") Integer salesId);

    @PostMapping("room/lottery")
    @ApiOperation("创建抽奖活动")
    BaseResult<String> createRoomLottery(
            @RequestBody @Valid CreateRoomLotteryReq req);

    @PutMapping("room/lottery/status")
    @ApiOperation("修改活动状态")
    BaseResult<String> updateRoomLotteryStatus(
            @RequestParam(required = true) @ApiParam("活动ID") Long lotteryId,
            @RequestParam(required = true) @ApiParam("活动状态") RoomLotteryType status,
            @RequestParam(required = true) @ApiParam("用户ID") Integer userId);

    @PutMapping("room/lottery")
    @ApiOperation("编辑抽奖活动")
    BaseResult<String> updateRoomLottery(
            @RequestBody @Valid UpdateRoomLotteryReq req);

    @GetMapping("room/lottery/{lotteryId}")
    @ApiOperation("获取抽奖活动")
    BaseResult<SsRoomLottery> findOneRoomLottery(
            @PathVariable Long lotteryId);

    @GetMapping("room/lottery/detailList")
    @ApiOperation("获取抽奖详情列表")
    PageResult<List<SsRoomLotteryDetailDto>> getRoomLotteryDetailList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("直播室ID") Long roomId,
            @RequestParam(required = false) @ApiParam("场次ID") Long sceneId,
            @RequestParam(required = false) @ApiParam("活动ID") Long lotteryId,
            @RequestParam(required = false) @ApiParam("奖品ID") Long prizeId,
            @RequestParam(required = false) @ApiParam("关联的后台用户ID") Integer relationServerId,
            @RequestParam(required = false) @ApiParam("销售ID") Integer salesId,
            @RequestParam(required = false) @ApiParam("客户ID") List<Integer> customerList,
            @RequestParam(required = false) @ApiParam("是否中奖") Boolean isWin,
            @RequestParam(required = false) @ApiParam("是否已处理") Boolean isHandled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size
    );

    @PostMapping("room/lottery/detail/{detailId}/markHandled")
    @ApiOperation("标记抽奖详情为已处理")
    BaseResult<String> markRoomLotteryDetailHandled(
            @PathVariable @ApiParam("抽奖详情ID") Long detailId,
            @RequestParam @ApiParam("收件人电话（掩码)") String mobile,
            @RequestParam @ApiParam("收件人电话id") Long mobileId,
            @RequestParam @ApiParam("收件人地区") String region,
            @RequestParam @ApiParam("收件人地址") String address,
            @RequestParam @ApiParam("收件人姓名") String name
    );

    @GetMapping("room/lottery/operate/record")
    @ApiOperation("获取活动操作日志")
    BaseResult<List<RoomLotteryOperateResp>> getRoomLotteryOperateRecord(
            @RequestParam(required = true) @ApiParam("活动ID") Long lotteryId);

    @GetMapping("oss/config")
    @ApiOperation("生成sts token")
    BaseResult<AssumeRoleResponse> getOssConfigInfo();

    @GetMapping("oss/token/{isVideo}")
    @ApiOperation("oss上传临时凭证")
    BaseResult<OssTokenResp> genOssToken(@PathVariable Boolean isVideo);

    @GetMapping("room/{roomId}")
    @ApiOperation("获取直播室详情")
    BaseResult<SsLiveRoom> getLiveRoomInfo(@PathVariable Long roomId);

    @ApiOperation("废掉签字")
    @PostMapping("sign/abandon")
    BaseResult<String> abandonSign(
            @RequestParam @ApiParam("AccountUserId") String accountUserId);

    @ApiOperation("根据id废掉签字")
    @PostMapping("sign/abandonById")
    BaseResult<String> abandonSignById(
            @RequestParam @ApiParam("SignId") Integer signId);

    @GetMapping("sign")
    @ApiOperation("获取签字信息")
    BaseResult<SsEsignInfo> getEsignInfo(@RequestParam Integer companyType,
                                         @RequestParam String flowId);

    @GetMapping("sign/byId")
    @ApiOperation("根据id获取签字信息")
    BaseResult<SsEsignInfo> getSignInfoById(@RequestParam Integer signId);

    @GetMapping("sign/batch")
    @ApiOperation("批量获取签字信息")
    BaseResult<List<SsEsignInfo>> getBatchSignInfo(@RequestParam @ApiParam("签字ID列表") Collection<Integer> signIds);

    @PostMapping("sign/create")
    @ApiOperation("创建签字信息")
    BaseResult<SsEsignInfo> createSignInfo(
            @RequestParam Integer company,
            @RequestParam String userId,
            @RequestParam String accountId,
            @RequestParam Integer signType,
            @RequestParam Long payCompanyId,
            @RequestParam String appId);

    @PostMapping("sign/update")
    @ApiOperation("更新签字信息")
    BaseResult<String> updateSignInfo(
            @RequestParam Integer signId,
            @RequestBody @Validated UpdateSignInfoReq info);

    @Deprecated
    @ApiOperation("重新测评")
    @PostMapping("evaluation/redo")
    BaseResult<String> evaluationRedo(
            @RequestParam @ApiParam("EvaluationId") Integer evaluationId,
            @RequestParam @ApiParam("CompanyType") Integer companyType);

    @GetMapping("evaluation/users")
    @ApiOperation("获取用户的评测")
    BaseResult<SsCustomerEvaluation> getUserEvaluation(@RequestParam Integer userId);

    @GetMapping("evaluation/getUserLast")
    @ApiOperation("获取用户最后一次的评测")
    BaseResult<SsCustomerEvaluation> getUserLastEvaluation(@RequestParam Integer userId);

    @GetMapping("room/all")
    @ApiOperation("获取所有直播室")
    BaseResult<List<SsLiveRoom>> getAllLiveRoom(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否过滤删除") Boolean filterDeleted);

    @GetMapping("evaluation/user_choice")
    @ApiOperation("获取用户的评测选项")
    BaseResult<SsSurveyChoice> getUserChoice(
            @RequestParam @ApiParam("评测id") Integer evaluationId,
            @RequestParam @ApiParam("选项") String content);

    @GetMapping("evaluation/choice/options")
    @ApiOperation("获取评测题目选项")
    BaseResult<List<SsSurveyChoice>> getEvaluationChoiceList(
            @RequestParam @ApiParam("评测id") Integer evaluationId,
            @RequestParam @ApiParam("题目类型") Integer surveyType,
            @RequestParam @ApiParam("第几题") Integer order);

    @GetMapping("evaluation/identity_number")
    @ApiOperation("通过身份证号获取客户评估")
    BaseResult<SsCustomerEvaluation> findByIdentityNumber(
            @RequestParam Integer companyType,
            @RequestParam String identityNumber);

    @GetMapping("evaluation/getCustomerEvaluation")
    @ApiOperation("通过id获取客户评估")
    BaseResult<SsCustomerEvaluation> findById(
            @RequestParam Integer id);

    @GetMapping("room/service/introduction")
    @ApiOperation("获取直播室服务简介")
    BaseResult<ServiceIntroduction> getLiveRoomServiceIntroduction(Integer serviceId);

    @PostMapping("room/service/introduction/number")
    @ApiOperation("获取直播室服务简介列表")
    BaseResult<List<ServiceIntroduction>> getLiveRoomServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req,
            @RequestParam(required = false, defaultValue = "false") Boolean sorted);

    default BaseResult<List<ServiceIntroduction>> getLiveRoomServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req) {
        return getLiveRoomServiceIntroductionList(companyType, req, false);
    }

    @PostMapping("room/service/introduction")
    @ApiOperation("获取直播室服务简介列表")
    BaseResult<List<ServiceIntroduction>> getLiveRoomServiceIntroductionList(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("evaluation/topic")
    @ApiOperation("获取题目列表")
    BaseResult<List<SsSurveyTopic>> getTopicList(
            @RequestParam @ApiParam("测评类型") Integer surveyType);

    @GetMapping("evaluation/choice")
    @ApiOperation("根据题目获取选项信息")
    BaseResult<List<SsSurveyChoice>> getChoiceList(
            @RequestParam @ApiParam("题目id") Long topicId);

    @PostMapping("evaluation/submitEvaluation")
    @ApiOperation("提交客户评估")
    BaseResult<Boolean> submitEvaluation(@RequestBody SubmitEvaluationReq req);

    @PostMapping("evaluation/finish")
    @ApiOperation("提交客户评估")
    BaseResult<Void> finishEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("测评类型") Integer surveyType);

    @PostMapping("evaluation/answer/save")
    @ApiOperation("保存测评选项")
    BaseResult<Void> saveEvaluationAnswer(
            @RequestBody SaveEvaluationAnswerReq req);

    @GetMapping("evaluation/answer/get")
    @ApiOperation("获取测评选项")
    BaseResult<EvaluationAnswerResp> getEvaluationAnswer(
            @RequestParam @ApiParam("测评id") Integer evaluationId);

    @GetMapping("evaluation/getCustomerEvaluation/{userId}")
    @ApiOperation("通过userid获取客户评估")
    BaseResult<SsCustomerEvaluation> getCustomerEvaluation(
            @PathVariable Integer userId);

    @GetMapping("evaluation/createCustomerEvaluation")
    @ApiOperation("为用户创建评测")
    BaseResult<SsCustomerEvaluation> createCustomerEvaluation(
            @RequestParam Integer userId,
            @RequestParam Integer companyType,
            @RequestParam String mobile);

    @PostMapping("evaluation/updateCustomerEvaluation")
    @ApiOperation("更新客户评估")
    BaseResult<Boolean> updateCustomerEvaluation(
            @RequestBody @ApiParam("更新的属性") UpdateCustomerEvaluationReq req);

    @GetMapping("stream/all")
    @ApiOperation("获取所有直播室")
    BaseResult<List<LiveStreamChannel>> getAllLiveStream(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否过滤删除") Boolean filterDeleted);

    @GetMapping("stream/channel/{id}")
    @ApiOperation("获取直播流频道")
    BaseResult<SsStreamChannel> getStreamChannel(
            @PathVariable @ApiParam("直播流Id") Long id);

    @PostMapping("stream/introduction")
    @ApiOperation("获取直播流服务简介列表")
    BaseResult<List<ServiceIntroduction>> getStreamChannelServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req);

    @GetMapping("stream/channel/getStreamChannels")
    @ApiOperation("获取直播流频道列表")
    PageResult<List<SsStreamChannel>> getStreamChannels(
            @RequestParam(required = false) @ApiParam("直播流Id列表") List<Long> ids,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @PostMapping("stream/channel/creatStreamChannel")
    @ApiOperation("创建直播流频道列表")
    BaseResult<ContentErrorCode> creatStreamChannel(
            @RequestParam Integer companyType,
            @RequestBody @Valid EditStreamChannelReq req);

    @PostMapping("stream/channel/updateStreamChannel")
    @ApiOperation("更新直播流频道列表")
    BaseResult<ContentErrorCode> updateStreamChannel(@RequestBody @Valid EditStreamChannelReq req);

    @PostMapping("stream/channel/addStreamChannelAnalyst")
    @ApiOperation("为直播流添加投顾老师")
    BaseResult addStreamChannelAnalyst(@Validated @RequestBody StreamChannelAnalystReq req);

    @PostMapping("stream/channel/deleteStreamChannelAnalystRelation")
    @ApiOperation("删除直播流投顾老师关系")
    BaseResult deleteStreamChannelAnalystRelation(@RequestParam @ApiParam("需要删除的关系id") List<Long> Ids);

    @GetMapping("stream/channel/getStreamChannelAnalystRelations")
    @ApiOperation("获取直播流对映老师关系")
    BaseResult<List<SsStreamChannelAnalystRelation>> getStreamChannelAnalystRelations(@RequestParam @ApiParam("直播流频道ID") Long channelId);

    @GetMapping("stream/channel/analysts")
    @ApiOperation("获取直播流频道下的老师")
    BaseResult<List<SsAnalystInfo>> getStreamChannelAnalystList(
            @RequestParam @ApiParam("直播流频道ID") Long channelId);

    @PostMapping("stream/channel/addStreamChannelManager")
    @ApiOperation("为直播流配置处理人")
    BaseResult addStreamChannelManager(@Validated @RequestBody StreamServiceManagerReq req);

    @GetMapping("stream/channel/getStreamChannelManagerRelation")
    @ApiOperation("获取直播流对映处理人关系")
    BaseResult<List<SsStreamChannelOperatorRelation>> getStreamChannelManagerRelations(
            @RequestParam(required = false) @ApiParam("直播流频道ID") Long channelId,
            @RequestParam(required = false) @ApiParam("处理人ID") Integer operatorId);

    @PostMapping("stream/channel/deleteStreamChannelManagerRelation")
    @ApiOperation("删除直播流处理人关系")
    BaseResult deleteStreamChannelManagerRelation(@RequestParam @ApiParam("需要删除的关系id") List<Long> Ids);

    @GetMapping("evaluation/record/evaluationRecord")
    @ApiOperation("获取用户测评操作记录")
    PageResult<List<EvaluationRecord>> getEvaluationRecord(
            @RequestParam @ApiParam("更新的属性") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("evaluation/record/evaluationIdRecord")
    @ApiOperation("获取用户认证操作记录")
    PageResult<List<EvaluationIdRecord>> getEvaluationIdRecord(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("evaluation/record/redoEvaluation")
    @ApiOperation("重新测评")
    BaseResult<SsCustomerEvaluation> redoEvaluation(
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("操作人") Integer userId);

    @GetMapping("evaluation/record/evaluationIdRedo")
    @ApiOperation("重新认证")
    BaseResult<String> evaluationIdRedo(
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("操作人") Integer userId,
            @RequestParam @ApiParam("是否废掉测评次数") Boolean isAbolish);

    @GetMapping("evaluation/record/getEvaluation")
    @ApiOperation("获取测评信息")
    PageResult<List<SsCustomerEvaluation>> getEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("evaluation/record/getIdEvaluation")
    @ApiOperation("获取认证信息")
    PageResult<List<SsCustomerEvaluation>> getIdEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("evaluation/record/evaluationIdVerify")
    @ApiOperation("认证审核")
    BaseResult<String> evaluationIdVerify(
            @RequestBody IdVerifyReq req);

    @GetMapping("evaluation/evaluationData")
    @ApiOperation("获取测评pdf信息")
    BaseResult<Map<String, Object>> evaluationDate(
            @RequestParam @ApiParam("测评id") Integer evaluationId);

    @GetMapping("analyst/{id}")
    @ApiOperation("获取投顾老师详情")
    BaseResult<SsAnalystInfo> getAnalystInfo(@PathVariable Integer id);

    @PostMapping("analyst/batch")
    @ApiOperation("批量获取投顾老师详情")
    BaseResult<List<SsAnalystInfo>> getAnalystInfoList(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("analyst/list")
    @ApiOperation("获取投顾老师列表")
    PageResult<List<SsAnalystInfo>> getAnalystInfoListPage(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("是否官网展示") Boolean gwShow,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @PostMapping("analyst/create")
    @ApiOperation("创建投顾老师")
    BaseResult<String> createAnalystInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestBody @Valid EditAnalystInfoReq req);

    @PostMapping("analyst/update")
    @ApiOperation("编辑投顾老师信息")
    BaseResult<String> updateAnalystInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestBody @Valid EditAnalystInfoReq req);

    @PostMapping("analyst/delete")
    @ApiOperation("启用/禁用老师")
    BaseResult<String> deleteAnalystInfo(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("老师id") Integer id,
            @RequestParam @ApiParam("启用/禁用") Boolean enabled);

    @GetMapping("analyst/modify/list")
    @ApiOperation("老师修改记录列表")
    PageResult<List<SsAnalystModifyRecord>> getAnalystModifyRecordPage(
            @RequestParam @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size);

    @PostMapping("room-message")
    @ApiOperation("新增直播室消息")
    BaseResult<SsLiveRoomMessage> addLiveRoomMessage(
            @RequestBody @Valid EditRoomMessageReq req);

    @GetMapping("content/analysts")
    @ApiOperation("获取投顾老师列表")
    BaseResult<List<SsAnalystInfo>> getAnalystInfoList(
            @RequestParam(required = false) @ApiParam("Id列表") List<Integer> Ids,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("删除状态") Boolean isEnabled);

    default BaseResult<List<SsAnalystInfo>> getAnalystInfoList(List<Integer> ids, Integer companyType) {
        return getAnalystInfoList(ids, companyType, null);
    }

    @GetMapping("content/channel/analysts")
    @ApiOperation("获取投顾老师列表")
    BaseResult<List<SsAnalystInfo>> getChannelAnalystInfoList(
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("删除状态") Boolean isEnabled);

    @GetMapping("content/stock/pool")
    @ApiOperation("获取选股宝股票池列表")
    BaseResult<List<SsStockPool>> getStockPoolList(
            @RequestParam @ApiParam("查看日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate dateTime,
            @RequestParam(required = false) @ApiParam("股票类型(10-涨停池/20-连板池)") Integer type);

    /**
     * 获取解盘列表
     *
     * @param companyType 公司类型
     * @param userId      用户id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param channelId   频道id
     * @param current     页码
     * @param size        数量
     * @return 解盘消息列表
     */
    @GetMapping("stream/message/list")
    @ApiOperation("解盘列表")
    PageResult<List<StreamMessageResp>> getStreamMessagePage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户") Integer userId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

    @GetMapping("stream/message/audit/list")
    @ApiOperation("解盘审核列表")
    PageResult<List<StreamMessageResp>> getStreamMessageAuditList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("是否审核") Boolean isAudit,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size);

    @PostMapping("stream/message/audit")
    @ApiOperation("解盘消息审核")
    BaseResult<Void> auditStreamMessage(
            @RequestParam @ApiParam("审核人id") Integer userId,
            @RequestParam @ApiParam("消息id") Long messageId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("审核原因") String refuseRemark);

    @GetMapping("stream/message/analyst")
    @ApiOperation("前端获取对应老师的解盘列表")
    BaseResult<List<SsStreamMessage>> getStreamMessageList(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam List<Integer> analystIds,
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(required = false) @ApiParam("开始时间") Instant openTime,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size);

    /**
     * 创建解盘消息
     *
     * @param req 请求体
     * @return 解盘消息
     */
    @PostMapping("stream/message")
    @ApiOperation("创建解盘消息")
    BaseResult<SsStreamMessage> createMessage(@RequestBody StreamMessageReq req);

    /**
     * 删除解盘消息
     *
     * @param id 消息id
     * @return 无返回
     */
    @DeleteMapping("stream/message/{id}")
    @ApiOperation("删除消息")
    BaseResult<Void> deleteMessage(@PathVariable Long id);

    /**
     * 根据解盘消息id获取解盘消息
     *
     * @param id 解盘消息id
     * @return 解盘消息
     */
    @GetMapping("stream/message/{id}")
    @ApiOperation("根据解盘消息id获取解盘消息")
    BaseResult<SsStreamMessage> getStreamMessage(@PathVariable Long id);

    /**
     * 根据消息id获取评论列表
     *
     * @param messageId 消息id
     * @param isChoice  是否精选
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param current   页码
     * @param size      分页数量
     * @return 评论列表
     */
    @GetMapping("stream/message/comment/list")
    @ApiOperation("评论列表")
    PageResult<List<SsStreamMessageComment>> getCommentPage(
            @RequestParam @ApiParam("消息id") Long messageId,
            @RequestParam(required = false) @ApiParam("是否精选") Boolean isChoice,
            @RequestParam(required = false) @ApiParam("是否已审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

    @PostMapping("stream/message/comment/batch")
    @ApiOperation("批量获取解盘精选评论")
    BaseResult<List<SsStreamMessageComment>> getStreamMessageCommentList(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false, defaultValue = "1") Integer auditStatus,
            @RequestParam(required = false, defaultValue = "true") Boolean isChose,
            @RequestParam(required = false, defaultValue = "3") Integer perSize);

    default BaseResult<List<SsStreamMessageComment>> getStreamMessageCommentList(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false, defaultValue = "true") Boolean isChose,
            @RequestParam(required = false, defaultValue = "3") Integer perSize) {
        return getStreamMessageCommentList(req, null, isChose, perSize);
    }

    default BaseResult<List<SsStreamMessageComment>> getStreamMessageCommentList(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false, defaultValue = "3") Integer perSize) {
        return getStreamMessageCommentList(req, true, perSize);
    }

    /**
     * 删除评论
     *
     * @param id 评论id
     * @return 无返回
     */
    @DeleteMapping("stream/message/comment/{id}")
    @ApiOperation("删除评论")
    BaseResult<Void> deleteMessageComment(@PathVariable Long id);

    /**
     * 修改精选状态
     *
     * @param id       评论id
     * @param isChoice 是否精选
     * @return 无返回
     */
    @PutMapping("stream/message/comment/{id}")
    @ApiOperation("修改精选状态")
    BaseResult<Void> updateChoice(
            @PathVariable Long id,
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("是否精选") Boolean isChoice);

    @PostMapping("stream/message/comment/statistics")
    @ApiOperation("统计解盘评论数量")
    BaseResult<List<StreamMessageStatistics>> getStreamMessageCommentStatistics(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false) Integer auditStatus,
            @RequestParam(required = false) Boolean isChose
    );


    default BaseResult<List<StreamMessageStatistics>> getStreamMessageCommentStatistics(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false) Integer auditStatus) {
        return getStreamMessageCommentStatistics(req, auditStatus, null);
    }

    default BaseResult<List<StreamMessageStatistics>> getStreamMessageCommentStatistics(
            @RequestBody @Valid BatchReq<Long> req) {
        return getStreamMessageCommentStatistics(req, null, null);
    }

    /**
     * 回复评论
     *
     * @param id       评论id
     * @param content  评论内容
     * @param answerId 评论人
     * @return 无返回
     */
    @PutMapping("stream/message/answer/{id}")
    @ApiOperation("回复评论")
    BaseResult<Void> updateAnswer(
            @PathVariable Long id,
            @RequestParam @ApiParam("回复内容") String content,
            @RequestParam @ApiParam("回复人") Integer answerId);

    /**
     * 获取用户相关频道列表
     *
     * @param userId      用户id
     * @param companyType 公司类型
     * @return 频道列表
     */
    @GetMapping("stream/channel/list/{userId}")
    @ApiOperation("获取用户相关频道列表")
    BaseResult<List<SsStreamChannel>> getChannelList(
            @PathVariable Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PostMapping("stream/message/like/statistics")
    @ApiOperation("统计解盘点赞数量")
    BaseResult<List<StreamMessageStatistics>> getStreamMessageLikeStatistics(@RequestBody @Valid BatchReq<Long> req);

    @PostMapping("stream/message/users/like/statistics")
    @ApiOperation("统计用户解盘是否点赞")
    BaseResult<List<StreamMessageLikeStatistics>> getUserStreamMessageLikeStatistics(
            @RequestParam(required = false) Integer userId,
            @RequestBody @Valid BatchReq<Long> req);

    @PutMapping("stream/message/like")
    @ApiOperation("修改解盘点赞状态")
    BaseResult<String> updateStreamMessageLike(@RequestBody @Valid UpdateStreamMessageLikeReq req);

    /**
     * 发送评论
     *
     * @param messageId  消息id
     * @param customerId 用户id
     * @param content    评论内容
     * @return 评论是否成功
     */
    @PutMapping("stream/message/{messageId}/comment")
    @ApiOperation("发送评论")
    BaseResult<Boolean> sendComment(
            @PathVariable Long messageId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer customerId,
            @RequestParam @ApiParam("评论内容") String content);

    /**
     * 前端获取评论列表
     *
     * @param messageId 消息id
     * @param current   页码
     * @param size      分页数量
     * @return 评论列表
     */
    @GetMapping("stream/message/comment/page")
    @ApiOperation("前端评论列表")
    PageResult<List<SsStreamMessageComment>> getCommentPage(
            @RequestParam @ApiParam("消息id") Long messageId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

    @GetMapping("video/list")
    PageResult<List<SsSalesVideoRecord>> getRecord(@RequestParam(required = false) Integer companyType,
                                                   @RequestParam(required = false) Integer creatorId,
                                                   @RequestParam(required = false) String title,
                                                   @RequestParam(defaultValue = "1") Integer current,
                                                   @RequestParam(defaultValue = "10") Integer size);

    @GetMapping("video/list/filter")
    @ApiOperation("获取视频列表")
    PageResult<List<SsSalesVideoRecord>> getSalesVideoByFilter(
            @RequestParam Integer companyType,
            @RequestParam(required = false) String videoType,
            @RequestParam(required = false) List<Integer> creatorIdList,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Instant startTime,
            @RequestParam(required = false) Instant endTime,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size);

    @GetMapping("vod/cate/list")
    @ApiOperation("获取vod分类列表")
    BaseResult<List<GetCategoriesResponse.Category>> getCompanyCateList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false, defaultValue = "-1") @ApiParam("分类ID") Long cateId
    );

    default BaseResult<List<GetCategoriesResponse.Category>> getCompanyCateList(
            @RequestParam @ApiParam("公司类型") Integer companyType
    ) {
        return getCompanyCateList(companyType, null);
    }

    @GetMapping("vod/transcode/list")
    @ApiOperation("获取转码模组列表")
    BaseResult<List<ListTranscodeTemplateGroupResponse.TranscodeTemplateGroup>> getTranscodeTemplateGroupList();

    @GetMapping("vod/video/create")
    @ApiOperation("获取视频上传地址和凭证")
    BaseResult<CreateUploadVideoResponse> createUploadVideo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("创建人id") Integer creatorId,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("视频类型") String videoType,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL);

    @GetMapping("vod/video/create/normal")
    @ApiOperation("获取视频上传地址和凭证")
    BaseResult<CreateUploadVideoResponse> createUploadVideoNormal(
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL);

    @PostMapping("vod/video/upload")
    @ApiOperation("获取vod视频上传地址和凭证")
    BaseResult<VodCreateUploadResp> vodCreateUploadVideo(@RequestBody @Valid VodCreateUploadReq vodCreateUploadReq);

    @PostMapping("vod/video/update")
    @ApiOperation("更新视频信息")
    BaseResult<Void> updateVideoInfo(
            @RequestParam @ApiParam("视频id") String videoId,
            @RequestParam(required = false) @ApiParam("标题") String title,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL);

    @GetMapping("vod/video/upload/refresh")
    @ApiOperation("刷新视频上传凭证")
    BaseResult<RefreshUploadVideoResponse> refreshUploadVideo(
            @RequestParam @ApiParam("视频id") String videoId);

    @GetMapping("vod/video/auth")
    @ApiOperation("获取播放凭证")
    BaseResult<GetVideoPlayAuthResponse> getVideoPlayAuth(
            @RequestParam @ApiParam("视频id") String videoId);

    @GetMapping("vod/video/play")
    @ApiOperation("获取播放信息")
    BaseResult<GetPlayInfoResponse> getVideoPlayInfo(
            @RequestParam @ApiParam("视频id") String videoId);

    @PostMapping("vod/video/upload/result")
    @ApiOperation("视频上传结果")
    BaseResult<Void> handleUploadVideoResult(
            @RequestParam @ApiParam("body") String body);

    /**
     * 提交客户评估基本信息
     *
     * @param req 用户基本信息
     * @return 操作结果
     */
    @PostMapping("evaluation/submitEvaluationUserInfo")
    @ApiOperation("提交客户评估基本信息")
    BaseResult<Boolean> submitEvaluationUserInfo(@RequestBody @Valid EvaluationUserInfoReq req);


    @PostMapping("vod/short/video/upload/result")
    @ApiOperation("短视频上传结果")
    BaseResult<Void> handleUploadShortVideoResult(
            @RequestParam @ApiParam("body") String body);

    @PostMapping("vod/short/video/tag/result")
    @ApiOperation("短视频智能标签结果")
    BaseResult<Void> handleShortVideoTagResult(
            @RequestBody @ApiParam("body") String body);

    @GetMapping("vod/video/search")
    @ApiOperation("视频搜索")
    PageResult<List<SearchMediaResponse.Media>> vodVideoSearch(
            @RequestParam(required = false) @ApiParam("标题") String title,
            @RequestParam @ApiParam("分类id, 全部：-1") Long cateId,
            @RequestParam @ApiParam("是否顺序") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("状态") String status,
            @RequestParam(required = false) @ApiParam("开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("news/channel/getNewsChannels")
    @ApiOperation("获取新闻频道列表")
    BaseResult<List<NewsChannelResp>> getNewsChannels();

    @GetMapping("news/getNewsList")
    @ApiOperation("获取指定频道新闻")
    PageResult<List<SsNew>> getNewsList(
            @RequestParam @ApiParam("频道id") Collection<String> channelNumber,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("news/getNewsList/labelList")
    @ApiOperation("获取指定频道新闻")
    PageResult<List<SsNew>> getNewsListByLabelList(
            @RequestParam @ApiParam("频道id") Collection<String> channelNumber,
            @RequestParam @ApiParam("股票代码") Collection<String> labelList,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("news/getNewsList/label")
    @ApiOperation("获取指定频道新闻")
    PageResult<List<SsNew>> getNewsListByLabel(
            @RequestParam @ApiParam("频道id") Collection<String> channelNumber,
            @RequestParam @ApiParam("股票代码") String label,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("news/getNewsById")
    @ApiOperation("获取指定新闻信息")
    BaseResult<SsNew> getNewsById(
            @RequestParam @ApiParam("新闻id") Long id);

    @GetMapping("news/getLastNews")
    @ApiOperation("获取上一条指定新闻信息")
    BaseResult<SsNew> getLastNews(
            @RequestParam @ApiParam("新闻id") Long id,
            @RequestParam @ApiParam("新闻发布时间") Instant publishTime,
            @RequestParam @ApiParam("新闻频道ID") Long channelId);

    @GetMapping("news/getNextNews")
    @ApiOperation("获取下一条指定新闻信息")
    BaseResult<SsNew> getNextNews(
            @RequestParam @ApiParam("新闻id") Long id,
            @RequestParam @ApiParam("新闻发布时间") Instant publishTime,
            @RequestParam @ApiParam("新闻频道ID") Long channelId);

    @GetMapping("news/express")
    @ApiOperation("获取新闻快讯")
    PageResult<List<SsExpressResp>> getExpressResp(
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("video/getVideoList")
    @ApiOperation("内容管理-短视频")
    PageResult<List<SsShortVideoResp>> getShortVideoList(
            @RequestParam(required = false) @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("video/getTeamVideoList")
    @ApiOperation("内容主短视频列表")
    PageResult<List<SsShortVideoResp>> getTeamShortVideoList(
            @RequestParam @ApiParam("内容主可见范围") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @PostMapping("video/delete")
    @ApiOperation("删除短视频")
    BaseResult<String> deleteShortVideo(
            @RequestParam @ApiParam("视频id") Long id,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId
    );

    @GetMapping("video/getCommentList")
    @ApiOperation("获取短视频评论列表")
    PageResult<List<SsShortVideoComment>> getShortVideoCommentList(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("是否精选") Boolean isChose,
            @RequestParam(required = false) @ApiParam("发送开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("发送结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @PostMapping("video/choseComment")
    @ApiOperation("精选短视频评论")
    BaseResult<String> choseShortVideoComment(
            @RequestParam @ApiParam("评论id") Long commentId,
            @RequestParam @ApiParam("是否精选") Boolean isChose);

    @PostMapping("video/deleteComment")
    @ApiOperation("删除短视频评论")
    BaseResult<String> deleteShortVideoComment(@RequestParam @ApiParam("评论id") Long commentId);

    @PostMapping("video/comment")
    @ApiParam("短视频发表评论")
    BaseResult<String> shortVideoComment(
            @RequestParam @ApiParam("视频ID") Long videoId,
            @RequestParam @ApiParam("评论人ID") Integer customerId,
            @RequestParam @ApiParam("评论内容") String content);

    @PostMapping("video/comment/statistics")
    @ApiOperation("获取短视频评论统计")
    BaseResult<List<CountStatistics>> getShortVideoCommentCountStatistics(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false) Integer auditStatus,
            @RequestParam(required = false) Boolean isChose);

    @PostMapping("video/create")
    @ApiOperation("创建视频")
    BaseResult<CreateUploadVideoResponse> createVideo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("老师ID") Integer analystId,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("文件标题") String fileTitle,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL);

    @PostMapping("video/team")
    @ApiOperation("内容主上传视频")
    BaseResult<CreateTeamShortVideoResp> createTeamShortVideo(
            @RequestBody @Valid CreateTeamShortVideoReq createTeamShortVideoReq
    );

    @PostMapping("video/update")
    @ApiOperation("编辑视频")
    BaseResult<String> updateVideo(
            @RequestParam @ApiParam("视频ID") Long id,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL);

    @PostMapping("video/tag/create")
    @ApiOperation("创建短视频标签数据")
    BaseResult<String> createShortVideoTag(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("mediaId") String mediaId,
            @RequestParam(required = false) @ApiParam("短视频Id") Long videoId);

    @GetMapping("video/tag/reacquire")
    @ApiOperation("重新获取短视频标签数据")
    BaseResult<String> reacquireShortVideoTag(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("mediaId") String mediaId,
            @RequestParam @ApiParam("短视频Id") Long videoId);

    @GetMapping("video/tag/getTagByVideoIds")
    @ApiOperation("根据短视频id获取相应标签数据")
    BaseResult<List<SsShortVideoTag>> getTagByShortVideoIds(@RequestParam @ApiParam("短视频Id") List<Long> videoIds);

    @PostMapping("video/like/or/unlike")
    @ApiOperation("视频点赞/取消点赞")
    BaseResult<String> videoLikeOrUnlike(
            @RequestParam @ApiParam("视频ID") Long videoId,
            @RequestParam @ApiParam("用户ID") Integer customerId,
            @RequestParam @ApiParam("点赞/取消点赞") Boolean enabled);

    @PostMapping("video/like/count/statistics")
    @ApiOperation("获取短视频点赞统计")
    BaseResult<List<CountStatistics>> getShortVideoLikeCountStatistics(@RequestBody BatchReq<Long> req);

    @PostMapping("video/like/operation/statistics")
    @ApiOperation("获取短视频点赞操作统计")
    BaseResult<List<LikeStatistics>> getShortVideoLikeOperationStatistics(
            @RequestBody BatchReq<Long> req,
            @RequestParam(required = false) Integer userId);

    @GetMapping("vod/sts/token")
    @ApiOperation("获取STS临时token")
    BaseResult<StsTokenResp> getStsTokenResp();

    @PostMapping("course/video/create")
    @ApiOperation("创建课程视频")
    BaseResult<CreateUploadVideoResponse> createVideoCourse(
            @RequestParam @ApiParam("系列id") Integer seriesId,
            @RequestParam @ApiParam("目录id") Long catalogId,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam @ApiParam("视频时长") Double videoDuration,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL);

    @PostMapping("course/video/edit")
    @ApiOperation("编辑视频")
    BaseResult<String> editVideo(
            @RequestParam @ApiParam("id") Integer id,
            @RequestParam @ApiParam("标题") String title);

    @PutMapping("news/express/like")
    @ApiOperation("修改快讯点赞状态")
    BaseResult<String> updateExpressLike(@RequestBody @Valid UpdateExpressLikeReq req);

    @PutMapping("news/like")
    @ApiOperation("修改新闻点赞状态")
    BaseResult<String> updateNewsLike(@RequestBody @Valid UpdateNewsLikeReq req);

    @GetMapping("news/likeCount")
    @ApiOperation("获取新闻点赞量")
    List<NewsLikeCount> getNewsLikeCount(@RequestBody @Valid Collection<Long> newsIds);

    @GetMapping("news/isLike")
    @ApiOperation("获取新闻是否点赞")
    List<NewsUserIsLike> getNewsUserIsLike(@RequestParam(required = false) Integer userId, @RequestBody @Valid Collection<Long> newsIds);

    @PostMapping("news/addViewCount")
    @ApiOperation("新闻浏览量+1")
    BaseResult<String> addViewCount(@RequestParam @ApiParam("新闻浏览量+1") Long newsId);

    @PostMapping("article/list")
    @ApiOperation("免费文章列表")
    PageResult<List<ArticleInfoResp>> getArticleList(
            @RequestParam @ApiParam("栏目编号逗号隔开") String typeNumbers,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("article/info")
    @ApiOperation("获取文章详情")
    BaseResult<ArticleInfoResp> getArticleInfo(
            @RequestParam @ApiParam("文章编号") String number,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId);

    @PostMapping("article/like")
    @ApiOperation("文章点赞（取消）")
    BaseResult<String> articleLike(
            @RequestParam @ApiParam("文章编号") String number,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId);

    @GetMapping("analyst/gw/list")
    @ApiOperation("官网获取投顾老师列表")
    BaseResult<List<SsAnalystInfo>> getGwAnalystInfoList();

    @GetMapping("article/team/page")
    @ApiOperation("内容主文章列表")
    PageResult<List<SsArticle>> getContentTeamArticlePage(
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("文章状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("状态") List<Integer> statusList,
            @RequestParam(required = false) @ApiParam("排序字段") String field,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size);

    default PageResult<List<SsArticle>> getContentTeamArticlePage(
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("文章状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("排序字段") String field,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return getContentTeamArticlePage(teamIds, companyType, status, startTime, endTime, null, field, isAsc, current, size);
    }

    @PostMapping("article/create")
    @ApiOperation("发布文章")
    BaseResult<Void> createArticle(
            @RequestBody CreateArticleReq req);

    @PostMapping("article/update")
    @ApiOperation("编辑文章")
    BaseResult<Void> updateArticle(
            @RequestBody UpdateArticleReq req);

    @PostMapping("article/edit")
    @ApiOperation("编辑文章")
    BaseResult<Void> editArticle(
            @RequestBody EditArticleReq req);

    @GetMapping("article/comment/page")
    @ApiOperation("文章评论列表")
    PageResult<List<SsArticleComment>> getArticleCommentPage(
            @RequestParam @ApiParam("文章id") Long articleId,
            @RequestParam @ApiParam("是否已审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("评论状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size);

    @GetMapping("article/comment")
    @ApiOperation("获取文章评论列表")
    PageResult<List<SsArticleComment>> getArticleCommentList(
            @RequestParam @ApiParam("文章id") Long articleId,
            @RequestParam(required = false) @ApiParam("评论状态") Integer auditFlag,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size);

    @PostMapping("article/comment/statistics")
    @ApiOperation("统计文章评论数量")
    BaseResult<List<CountStatistics>> getArticleCommentStatistics(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false) @ApiParam("枚举AuditStatusConstant") Integer auditFlag);

    @PostMapping("article/comment/audit")
    @ApiOperation("审核评论")
    BaseResult<Void> auditArticleComment(
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("评论id") Integer commentId,
            @RequestParam @ApiParam("审核状态") Integer status);

    @PostMapping("article/comment")
    @ApiOperation("发布评论")
    BaseResult<SsArticleComment> createArticleComment(@RequestBody @Valid CreateArticleCommentReq req);

    @GetMapping("stream/message/team/list")
    @ApiOperation("内容主解盘列表")
    PageResult<List<SsStreamMessage>> getTeamStreamMessagePage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("状态") List<Integer> statusList,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    default PageResult<List<SsStreamMessage>> getTeamStreamMessagePage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return getTeamStreamMessagePage(companyType, teamIds, auditStatus, startTime, endTime, null, current, size);
    }

    @GetMapping("stream/message/team/latest")
    @ApiOperation("获取内容主最新解盘")
    BaseResult<List<SsStreamMessage>> getLatestTeamStreamMessageList(
            @RequestParam Integer companyType,
            @RequestParam Long teamId,
            @RequestParam(required = false) @ApiParam("状态") Integer status,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("数量") Long size
    );

    @PostMapping("stream/message/team/create")
    @ApiOperation("创建内容主解盘消息")
    BaseResult<SsStreamMessage> createTeamStreamMessage(@RequestBody CreateTeamStreamMessageReq req);

    @PutMapping("stream/message/audit/comment/{id}")
    @ApiOperation("审核评论")
    BaseResult<Void> auditComment(
            @PathVariable Long id,
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus);

    @PostMapping("green/text/scan")
    @ApiOperation("文本内容安全检测(阿里云检测服务1.0)")
    BaseResult<GreenTextScanResp> textScan(@RequestBody GreenTextScanReq greenTextScanReq);

    @PostMapping("green/text/moderation")
    @ApiOperation("文本内容安全检测(阿里云文本审核增强版)")
    BaseResult<TextModerationResp> textModeration(@RequestBody TextModerationReq textModerationReq);

    @PostMapping("green/batch")
    @ApiOperation("内容安全根据id批量查询map")
    BaseResult<Map<Long, SsTextScanResult>> batchGetResultMap(@RequestBody BatchReq<Long> req);

    @GetMapping("green/labels")
    @ApiOperation("获取内容安全审核结果标签")
    BaseResult<List<ModerationLabelItem>> getModerationLabels();

    @DeleteMapping("stream/message/team/{id}")
    @ApiOperation("删除内容主解盘")
    BaseResult<Void> deleteTeamMessage(
            @PathVariable Long id,
            @RequestParam @ApiParam("用户id") Integer userId
    );

    @GetMapping("video/team/commentList")
    @ApiOperation("获取内容主视频评论列表")
    PageResult<List<SsShortVideoComment>> getTeamVideoCommentList(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("video/team/commentList")
    @ApiOperation("获取内容主视频评论列表")
    PageResult<List<SsShortVideoComment>> getTeamVideoCommentList(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("评论开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("评论结束时间") Instant endTime,
            @RequestParam(required = false) @ApiParam("是否精选") Boolean isChose,
            @RequestParam(required = false) @ApiParam("是否审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size);

    default PageResult<List<SsShortVideoComment>> getTeamVideoCommentList(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("是否精选") Boolean isChose,
            @RequestParam(required = false) @ApiParam("是否审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("评论开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("评论结束时间") Instant endTime,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size) {
        return getTeamVideoCommentList(videoId, null, startTime, endTime, isChose, audit, auditStatus, current, size);
    }

    @PostMapping("video/team/comment")
    @ApiOperation("内容主视频发表评论")
    BaseResult<String> teamVideoComment(
            @RequestParam @ApiParam("视频ID") Long videoId,
            @RequestParam @ApiParam("评论人ID") Integer customerId,
            @RequestParam @ApiParam("评论内容") String content,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PutMapping("video/team/comment")
    @ApiOperation("内容主视频评论审核")
    BaseResult<Void> updateTeamVideoComment(@RequestBody @Valid UpdateTeamShortVideoCommentReq updateTeamShortVideoCommentReq);

    @GetMapping("video/team/info")
    @ApiOperation("获取内容主视频信息")
    BaseResult<TeamShortVideoResp> getTeamVideoInfo(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId);

    @GetMapping("stream/message/team")
    @ApiOperation("按内容主获取解盘列表")
    PageResult<List<SsStreamMessage>> getTeamStreamMessageList(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("审核状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size);

    @GetMapping("article/team")
    @ApiOperation("获取内容主文章列表")
    PageResult<List<SsArticle>> getTeamArticleList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("内容主id列表") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("文章状态") Integer articleStatus,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("根据创建时间排序") Boolean articleOrderByCreate);

    @GetMapping("article/team/latest")
    @ApiOperation("获取内容主最新文章")
    BaseResult<List<SsArticle>> getLatestTeamArticleList(
            @RequestParam Integer companyType,
            @RequestParam Long teamId,
            @RequestParam(required = false) @ApiParam("文章状态") Integer articleStatus,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("文章数量") Long size
    );

    @GetMapping("article/team/next")
    @ApiOperation("获取内容主当前文章下一篇文章")
    BaseResult<SsArticle> getTeamNextArticle(
            @RequestParam Long teamId,
            @RequestParam Long articleId,
            @RequestParam(required = false) @ApiParam("文章状态") List<Integer> articleStatuses
    );

    @GetMapping("article/team/previous")
    @ApiOperation("获取内容主当前文章上一篇文章")
    BaseResult<SsArticle> getTeamPreviousArticle(
            @RequestParam Long teamId,
            @RequestParam Long articleId,
            @RequestParam(required = false) @ApiParam("文章状态") List<Integer> articleStatuses
    );

    @PostMapping("article/like/count/statistics")
    @ApiOperation("获取文章点赞统计")
    BaseResult<List<CountStatistics>> getArticleLikeCountStatistics(@RequestBody BatchReq<Long> req);

    @PostMapping("article/like/operation/statistics")
    @ApiOperation("获取文章点赞操作统计")
    BaseResult<List<LikeStatistics>> getArticleLikeOperationStatistics(
            @RequestBody BatchReq<Long> req,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Integer wxId);

    @GetMapping("video/team/list")
    @ApiOperation("视频列表")
    PageResult<List<SsShortVideo>> getTeamVideoList(
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("视频状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("排序字段") String field,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("状态") List<Integer> statusList,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    default PageResult<List<SsShortVideo>> getTeamVideoList(
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("视频状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("排序字段") String field,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return getTeamVideoList(teamIds, companyType, status, startTime, endTime, field, isAsc, null, current, size);
    }

    @GetMapping("video/team/latest")
    @ApiOperation("获取内容主最新视频")
    BaseResult<List<SsShortVideo>> getLatestTeamVideoList(
            @RequestParam Integer companyType,
            @RequestParam Long teamId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("数量") Long size
    );

    @GetMapping("teams/public")
    @ApiOperation("获取内容主配置")
    BaseResult<PublicConfig> getTeamPublicConfig();

    @GetMapping("teams/banner")
    @ApiOperation("获取观大盘配置")
    BaseResult<String> getTeamBannerConfig();

    @GetMapping("article/info/byId")
    @ApiOperation("获取文章详情")
    BaseResult<SsArticle> getArticleInfoById(
            @RequestParam @ApiParam("文章id") Long id);

    @GetMapping("vod/video/transcode")
    @ApiOperation("获取视频转码状态")
    BaseResult<SsVideoTranscode> getVideoTranscode(@RequestParam String videoId);

    @GetMapping("evaluation/findByNumber")
    @ApiOperation("通过number获取客户评估")
    BaseResult<SsCustomerEvaluation> findByNumber(
            @RequestParam String number);

    @PostMapping("green/text/chat/audit")
    @ApiOperation("文本内容安全检测(企微聊天记录专用)")
    BaseResult<TextChatAuditResp> textChatAudit(@RequestBody TextChatAuditReq req);

    @PostMapping("vip-notice/createOrUpdate")
    @ApiOperation("创建/编辑vip公告")
    BaseResult<Void> createOrUpdateVipNotice(
            @RequestBody @Valid CreateOrUpdateVipNoticeReq req);

    @PostMapping("vip-notice/enable")
    @ApiOperation("启用/禁用vip公告")
    BaseResult<Void> enableVipNotice(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("ID") Long id,
            @RequestParam @ApiParam("产品类型") Integer product,
            @RequestParam @ApiParam("启用/禁用") Boolean enable);

    @GetMapping("vip-notice/list")
    @ApiOperation("获取vip公告列表")
    PageResult<List<SsVipNotice>> getVipNoticeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("产品类型") Integer product,
            @RequestParam(required = false) @ApiParam("状态") Boolean enable,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("vip-notice")
    @ApiOperation("获取vip公告")
    BaseResult<SsVipNotice> getVipNotice(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("产品类型") Integer product);

    @ApiOperation("客户投诉与建议")
    @GetMapping("customer/complaint/by/isHandle")
    PageResult<List<SsCustomerComplaint>> getCustomerComplaintList(
            @RequestParam @ApiParam("是否处理") Boolean isHandle,
            @RequestParam(required = false) @ApiParam("来源") String source,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @ApiOperation("处理/客户投诉与建议")
    @PostMapping("customer/complaint/createOrUpdate")
    BaseResult<Void> createOrUpdate(
            @RequestParam(required = false) @ApiParam("id") Long id,
            @RequestParam(required = false) @ApiParam("wxId") Integer wxId,
            @RequestParam(required = false) @ApiParam("userId") Integer userId,
            @RequestParam(required = false) @ApiParam("内容") String content,
            @RequestParam(required = false) @ApiParam("图片列表") List<String> images,
            @RequestParam(required = false) @ApiParam("审核人id") Integer auditId,
            @RequestParam(required = false) @ApiParam("来源") String source,
            @RequestParam(required = false) @ApiParam("处理结果") String handleResult);

    @GetMapping("advert/list")
    @ApiOperation("广告位列表")
    PageResult<List<SsAdvert>> getAdvertList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("启用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("标签") String label,
            @RequestParam(required = false) @ApiParam("分类ID") Long categoryId,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size);

    @PostMapping("advert/create")
    @ApiOperation("创建广告")
    BaseResult<Void> createAdvert(
            @RequestBody @Valid CreateAdvertReq req);

    @PutMapping("advert/edit")
    @ApiOperation("编辑广告")
    BaseResult<Void> editAdvert(@RequestBody @Valid EditAdvertReq req);

    @PutMapping("advert/enabled/{id}")
    @ApiOperation("启用禁用")
    BaseResult<Void> enabledAdvert(
            @PathVariable Integer id,
            @RequestParam @ApiParam("是否启用") Boolean enabled);

    @DeleteMapping("advert/deleted/{id}")
    @ApiOperation("删除广告")
    BaseResult<Void> deletedAdvert(@PathVariable Integer id);

    @GetMapping("advert/label/list")
    @ApiOperation("广告位标签列表")
    BaseResult<List<String>> getAdvertLabelList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("启用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("分类ID") Long categoryId);

    @GetMapping("advert")
    @ApiOperation("按标签获取广告位列表")
    BaseResult<List<SsAdvert>> getAdvertList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("标签列表") List<String> labels,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled
    );

    @ApiOperation("获取客户投诉与建议详情")
    @GetMapping("customer/complaint/info")
    BaseResult<SsCustomerComplaint> getCustomerComplaintInfo(@RequestParam @ApiParam("id") Long id);

    @PostMapping("material/create")
    @ApiOperation("上传素材")
    BaseResult<Void> createMaterial(@RequestBody @Valid CreateMaterialReq req);

    @PostMapping("material/update")
    @ApiOperation("修改素材内容")
    BaseResult<Void> updateMaterial(@RequestBody @Valid UpdateMaterialReq req);

    @PostMapping("material/delete")
    @ApiOperation("删除素材")
    BaseResult<Void> deleteMaterial(
            @RequestParam @ApiParam("素材id") Long materialId,
            @RequestParam @ApiParam("操作人id") Integer operatorId);

    @PostMapping("material/visible")
    @ApiOperation("修改可见范围")
    BaseResult<Void> updateMaterialVisibleConfig(@RequestBody @Valid UpdateMaterialVisibleConfigReq req);

    @PostMapping("material/list/creator")
    @ApiOperation("获取素材列表")
    PageResult<List<SsMaterial>> getCreatorMaterialListPage(
            @RequestBody @Validated MaterialListReq req);

    @GetMapping("material/list/marketing")
    @ApiOperation("获取营销素材列表")
    PageResult<List<SsMaterial>> getMarketingMaterialListPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("部门id") Set<Integer> departmentList,
            @RequestParam(required = false) @ApiParam("素材类型") Integer contentType,
            @RequestParam(required = false) @ApiParam("创建开始时间") Instant createStartTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") Instant createEndTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("material/list/promotion")
    @ApiOperation("获取推广素材列表")
    PageResult<List<SsMaterial>> getPromotionMaterialListPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("素材类型") Integer contentType,
            @RequestParam(required = false) @ApiParam("创建开始时间") Instant createStartTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") Instant createEndTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("material/list/brand")
    @ApiOperation("获取品宣素材列表")
    PageResult<List<SsMaterial>> getBrandMaterialListPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("素材类型") Integer contentType,
            @RequestParam(required = false) @ApiParam("创建开始时间") Instant createStartTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") Instant createEndTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("material/department")
    @ApiOperation("获取推广素材可见部门")
    BaseResult<List<SsMaterialDepartment>> getDepartmentByMaterial(
            @RequestParam @ApiParam("素材ids") List<Long> materialList);

    @PostMapping("material/audit")
    @ApiOperation("素材审核")
    BaseResult<Void> auditMaterial(@RequestBody @Valid MaterialAuditReq req);

    @PostMapping("material/pre/audit")
    @ApiOperation("素材预审核")
    BaseResult<Void> preAuditMaterial(@RequestBody @Valid MaterialAuditReq req);

    @GetMapping("material/operate/record")
    @ApiOperation("素材获取操作记录")
    BaseResult<List<SsMaterialOperateRecord>> getMaterialOperateRecord(@RequestParam @ApiParam("素材id") Long materialId);

    @GetMapping("material/getMaterial/{materialId}")
    @ApiOperation("获取素材")
    BaseResult<SsMaterial> getMaterial(
            @PathVariable @ApiParam("素材id") Long materialId);

    @PostMapping("material/content/batch")
    @ApiOperation("根据素材id批量获取素材内容")
    BaseResult<Map<Long, List<SsMaterialContent>>> batchGetMaterialContent(
            @RequestParam(required = false) @ApiParam("是否可用") Boolean enabled,
            @RequestBody BatchReq<Long> req);

    @GetMapping("analyst/audit/list")
    @ApiOperation("老师审核管理列表")
    PageResult<List<SsAnalystAuditApply>> getAuditRecordPage(
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size);

    @PostMapping("analyst/audit/apply")
    @ApiOperation("审核老师申请")
    BaseResult<Void> auditAnalystApply(
            @RequestParam @ApiParam("申请id") Long applyId,
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("审核状态(通过:1 不通过：2)") Integer auditStatus);

    @GetMapping("article/operate/record")
    @ApiOperation("获取文章操作记录")
    BaseResult<List<SsArticleOperateRecord>> getOperateRecord(
            @RequestParam @ApiParam("文章id") Long articleId);

    @GetMapping("keyword/libs/list")
    @ApiOperation("获取词库")
    BaseResult<List<KeywordLibResp>> getListKeywordLibs();

    @DeleteMapping("keyword/libs/{libId}")
    @ApiOperation("清空词库")
    BaseResult<Boolean> deleteKeywordLib(@PathVariable String libId);

    @PostMapping("keyword/add/keywords")
    @ApiOperation("词库添加关键词")
    BaseResult<KeywordsResultResp> addKeywordToLib(
            @RequestBody @Valid KeywordReq req);

    @GetMapping("keyword/list")
    @ApiOperation("获取关键词列表")
    PageResult<List<KeywordResp>> getListKeywords(
            @RequestParam @ApiParam("词库id") String libId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size,
            @RequestParam @ApiParam("是否升序") Boolean isAsc);

    @DeleteMapping("keyword")
    @ApiOperation("删除关键词")
    BaseResult<Boolean> deleteKeyword(
            @RequestBody @Valid DeleteKeywordReq req);


    @GetMapping("app/version/page")
    @ApiOperation("APP版本发布管理")
    PageResult<List<SsAppVersion>> getAppVersionPage(
            @RequestParam @ApiParam("设备") Integer platform,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size);

    @GetMapping("app/version/last")
    @ApiOperation("获取最新APP版本")
    BaseResult<SsAppVersion> getAppVersionLast(
            @RequestParam @ApiParam("设备") Integer platform);

    @PostMapping("app/version/create")
    @ApiOperation("新增发布单")
    BaseResult<Void> addAppVersion(@RequestBody AppVersionReq req);

    @PostMapping("app/version/update")
    @ApiOperation("编辑发布单")
    BaseResult<Void> updateAppVersion(@RequestBody UpdateVersionReq req);

    @GetMapping("video/operate/record")
    @ApiOperation("获取短视频操作记录")
    BaseResult<List<SsVideoOperateRecord>> getVideoOperateRecord(
            @RequestParam @ApiParam("短视频id") Long videoId);

    @GetMapping("stream/message/operate/record")
    @ApiOperation("获取观点操作记录")
    BaseResult<List<SsStreamOperateRecord>> getStreamOperateRecord(
            @RequestParam @ApiParam("解盘id") Long streamId);

    @GetMapping("teams/audit")
    @ApiOperation("内容主审核")
    BaseResult<String> teamContentAudit(
            @RequestParam @ApiParam("ID") Long id,
            @RequestParam @ApiParam("模块类型") Integer moduleType,
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("是否审核通过") Boolean isAudit,
            @RequestParam(required = false) @ApiParam("审核理由") String auditRemark);

    @GetMapping("material/content/{materialContentId}")
    @ApiOperation("根据id获取素材内容")
    BaseResult<SsMaterialContent> getMaterialContentById(
            @PathVariable @ApiParam("素材内容id") Long materialContentId);

    @GetMapping("material/get-material-content")
    @ApiOperation("根据materialId获取素材内容")
    BaseResult<List<SsMaterialContent>> getContentByMaterialId(
            @RequestParam @ApiParam("素材id") Long materialId);

    @PostMapping("evaluation/batch/user")
    @ApiOperation("根据userid批量获取客户评估")
    BaseResult<List<SsCustomerEvaluation>> getCustomerEvaluationList(
            @RequestBody BatchReq<Integer> req);

    @PostMapping("origin-material/create")
    @ApiOperation("创建好评素材")
    BaseResult<Void> createOriginMaterial(
            @RequestBody @Valid CreateOriginMaterialReq req);

    @PostMapping("origin-material/delete")
    @ApiOperation("删除好评素材")
    BaseResult<Void> deleteOriginMaterial(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("好评素材id") Long materialId);

    @GetMapping("origin-material/operate/record")
    @ApiOperation("获取好评素材操作记录")
    BaseResult<List<SsOriginMaterialOperateRecord>> getOriginMaterialOperateRecord(
            @RequestParam @ApiParam("好评素材id") Long materialId);

    @GetMapping("origin-material/list")
    @ApiOperation("获取好评素材列表")
    PageResult<List<SsOriginMaterial>> getOriginMaterialList(
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("频道id") List<Long> channelIds,
            @RequestParam(required = false) @ApiParam("分类") Integer category,
            @RequestParam(required = false) @ApiParam("状态") Boolean status,
            @RequestParam(required = false) @ApiParam("审核状态列表") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("origin-material-channel/batch")
    @ApiOperation("根据频道ID批量获取频道map")
    BaseResult<Map<Long, SsOriginMaterialChannel>> batchGetOriginMaterialChannelMap(@RequestBody BatchReq<Long> req);

    @GetMapping("origin-material-channel/list")
    @ApiOperation("获取好评素材频道列表")
    PageResult<List<SsOriginMaterialChannel>> getOriginMaterialChannelList(
            @RequestParam Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("origin-material-channel/create")
    @ApiOperation("创建好评素材频道")
    BaseResult<ContentErrorCode> createOriginMaterialChannel(@RequestBody @Valid CreateOriginMaterialChannelReq req);

    @PutMapping("origin-material-channel/update")
    @ApiOperation("编辑好评素材频道")
    BaseResult<ContentErrorCode> updateOriginMaterialChannel(@RequestBody @Valid EditOriginMaterialChannelReq req);

    @GetMapping("origin-material-channel/visible/list")
    @ApiOperation("获取好评素材可见频道列表")
    BaseResult<List<SsOriginMaterialChannel>> getOriginMaterialVisibleChannelList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("用户部门id列表") List<Integer> departmentIdList);

    @PostMapping("origin-material-channel/set-visible")
    @ApiOperation("设置好评素材频道可见范围")
    BaseResult<ContentErrorCode> setOriginMaterialVisible(@RequestBody @Valid OriginMaterialManagerReq req);

    @GetMapping("origin-material-channel/relation/user")
    @ApiOperation("获取好评素材频道处理人列表")
    BaseResult<List<SsOriginMaterialChannelUserRelation>> getOriginMaterialUserRelations(
            @RequestParam @ApiParam("好评素材频道id") Long channelId
    );

    @GetMapping("origin-material-channel/relation/department")
    @ApiOperation("获取好评素材频道部门列表")
    BaseResult<List<SsOriginMaterialChannelDepartmentRelation>> getOriginMaterialDeptRelations(
            @RequestParam @ApiParam("好评素材频道id") Long channelId
    );

    @GetMapping("origin-material")
    @ApiOperation("获取好评素材")
    BaseResult<SsOriginMaterial> getOriginMaterial(@RequestParam @ApiParam("好评素材频道id") Long materialId);

    @GetMapping("software-package/latest")
    @ApiOperation("获取最新安装包")
    BaseResult<SoftwarePackageResp> LatestPackage(@RequestParam @ApiParam("软件包类型：10-国诚智投,20-决策家PC,30-决策家Android") Integer packageType);

    @GetMapping("software-package/list")
    @ApiOperation("软件包管理")
    PageResult<List<SoftwarePackageResp>> packageList(
            @RequestParam @ApiParam("软件包类型：10-国诚智投,20-决策家PC,30-决策家Android") Integer packageType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("software-package/add")
    @ApiOperation("新增软件包")
    BaseResult<Void> addPackage(@RequestBody @Valid SoftwarePackageReq softwarePackageReq);

    @PostMapping("software-package/update-enable")
    @ApiOperation("更新软件包状态")
    BaseResult<Void> updatePackageEnable(
            @RequestParam Long id,
            @RequestParam @ApiParam("软件包状态") Boolean enabled);

    @GetMapping("software-package/info")
    @ApiOperation("软件包详情")
    BaseResult<SoftwarePackageResp> packageInfo(@RequestParam Long id);

    @GetMapping("analyst/contract-advisor/detail")
    @ApiOperation("合同投顾老师")
    BaseResult<SsContractAnalystRecord> getContractAdvisor(
            @RequestParam @ApiParam("合同类型") Integer contractType
    );

    @PostMapping("analyst/contract-advisor/update")
    @ApiOperation("更新合同投顾老师")
    BaseResult<Void> updateContractAdvisor(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestParam @ApiParam("合同投顾老师") String content,
            @RequestParam @ApiParam("合同类型") Integer contractType);

    @GetMapping("analyst/contract-advisor/operate-record")
    @ApiOperation("合同投顾老师操作记录")
    BaseResult<List<SsContractAnalystRecord>> getContractAdvisorOperateRecord(
            @RequestParam @ApiParam("合同类型") Integer contractType
    );

    @GetMapping("analyst/contract-advisor/info")
    @ApiOperation("获取合同投顾老师")
    BaseResult<String> getContractAdvisorInfo(
            @RequestParam @ApiParam("sku类型") Integer productLevel
    );

    @GetMapping("sign/operate/record/list")
    @ApiOperation("修改模板操作记录")
    BaseResult<List<SsEsignTemplateRecord>> getESignTemplateOperateRecordList(
            @RequestParam @ApiParam("场景编号") Integer signType,
            @RequestParam @ApiParam("应用ID") String appId);

    @GetMapping("sign/template/list")
    @ApiOperation("模板列表")
    PageResult<List<TemplateResp>> getTemplateList(@RequestParam(required = false) @ApiParam("应用ID") String appId, @RequestParam(required = false) @ApiParam("启用状态") Boolean enabled, @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
                                                   @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size);

    @GetMapping("sign/template/info/list")
    @ApiOperation("模板信息列表")
    BaseResult<List<SsEsignTemplate>> getTemplateInfoList();

    @PostMapping("sign/template/update")
    @ApiOperation("修改模板")
    BaseResult<Void> templateIdUpdate(
            @RequestParam @ApiParam("场景编号") Integer signType,
            @RequestParam @ApiParam("应用ID") String appId,
            @RequestParam @ApiParam("模板ID") String templateId,
            @RequestParam @ApiParam("操作人ID") Integer operatorId);

    @GetMapping("sign/get/template-id")
    @ApiOperation("获取签字模板ID")
    BaseResult<String> getTemplateId(
            @RequestParam @ApiParam("场景编号") Integer signType,
            @RequestParam @ApiParam("应用ID") String appId);

    @GetMapping("sign/template/appId")
    @ApiOperation("根据模板ID获取appId")
    BaseResult<String> getTemplateAppId(@RequestParam @ApiParam("模板id") String templateId);

    @PostMapping("sign/template/enabled")
    @ApiOperation("是否启用")
    BaseResult<Void> setTemplateEnabled(@RequestParam @ApiParam("模板id") String templateId, @RequestParam @ApiParam("状态") Boolean enabled);

    @PostMapping("sign/template/create")
    @ApiOperation("新增合同模板")
    BaseResult<SsEsignTemplate> createTemplate(@RequestParam @ApiParam("应用ID") String appId, @RequestParam @ApiParam("模板id") String templateId, @RequestParam(required = false) @ApiParam("模板名称") String templateName);

    @GetMapping("sign/template/id")
    @ApiOperation("根据id获取模板信息")
    BaseResult<SsEsignTemplate> getTemplateById(@RequestParam @ApiParam("模板ID") String templateId);

    @GetMapping("material/same-content")
    @ApiOperation("获取相同内容的素材")
    PageResult<List<SameMaterialContentResp>> getSameMaterialContent(
            @RequestParam @ApiParam("素材id") Long materialId,
            @RequestParam @ApiParam("素材内容md5") String contentMd5,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("oss/file-url")
    @ApiOperation("获取文件url")
    BaseResult<URL> getFileUrl(@RequestParam @ApiParam("originUrl") String originUrl);

    @GetMapping("oss/file-url")
    @ApiOperation("获取文件url")
    BaseResult<URL> getFileUrl(
            @RequestParam @ApiParam("originUrl") String originUrl,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("bucket类型：10-robot，20-promotion") Integer type);

    @GetMapping("promotion-resource/folder-list")
    @ApiOperation("推广资源文件夹列表")
    BaseResult<ListObjectsV2Result> getPromotionResourceFolder(
            @RequestParam(required = false) @ApiParam("分页游标") String cursor,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("promotion-resource/file-list")
    @ApiOperation("推广资源文件列表")
    BaseResult<ListObjectsV2Result> getPromotionResourceFile(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam(required = false) @ApiParam("分页token") String cursor,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("promotion-resource/folder-create")
    @ApiOperation("新建文件夹")
    BaseResult<Void> createPromotionResourceFolder(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam @ApiParam("用户id") Integer userId);

    @PostMapping("promotion-resource/folder-delete")
    @ApiOperation("删除文件夹")
    BaseResult<Void> deletePromotionResourceFolder(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam @ApiParam("用户id") Integer userId);

    @PostMapping("promotion-resource/file-delete")
    @ApiOperation("删除文件")
    BaseResult<Void> deletePromotionResourceFile(
            @RequestParam @ApiParam("文件完整路径") String path,
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("stream/stock/list")
    @ApiOperation("入选股票")
    BaseResult<List<StockMessageResp>> getChosenStocks(
            @RequestParam @ApiParam("内容主编号") String number,
            @RequestParam @ApiParam("n天内入选股票") Integer days,
            @RequestParam @ApiParam Integer companyType
    );

    @GetMapping("avatar/list")
    @ApiOperation("数字人列表")
    BaseResult<List<AvatarInfoResp>> getAvatarList(@RequestParam @ApiParam("模型类型 2d 只查询2d人物  3d只查询3d人物") String modelType);

    @GetMapping("avatar/video/list")
    @ApiOperation("数字人视频列表")
    PageResult<List<AvatarVideoInfoResp>> getAvatarVideoList(
            @RequestParam(required = false) @ApiParam("类型 1:3D  3:2D") Integer type,
            @RequestParam(required = false) @ApiParam("状态 1: 等待执行 2: 执行中 3: 成功 4：失败 不传默认查询全部状态") Integer status,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("avatar/video/create")
    @ApiOperation("创建数字人视频")
    BaseResult<Void> createAvatarVideo(@RequestBody CreateAvatarVideoReq req);

    @GetMapping("quote-index/list")
    @ApiOperation("获取指标信息列表")
    BaseResult<List<QuoteIndexResp>> getIndexList(
            @RequestParam @ApiParam("类型: 10-分时线图， 20-k线蜡烛图") Integer type,
            @RequestParam @ApiParam("位置: 1-主图， 2-副图") Integer position,
            @RequestParam(required = false) @ApiParam("上/下架") Boolean enabled);

    @PostMapping("quote-index/create")
    @ApiOperation("创建指标")
    BaseResult<Void> createIndex(@RequestBody QuoteIndexInfoReq req);

    @PostMapping("quote-index/edit")
    @ApiOperation("编辑指标")
    BaseResult<Void> editIndex(@RequestBody QuoteIndexInfoReq req);

    @PostMapping("quote-index/set-enabled")
    @ApiOperation("设置上下架状态")
    BaseResult<Void> setEnabled(@RequestParam Long id,
                                @RequestParam @ApiParam("上/下架") Boolean enabled,
                                @RequestParam(required = false) @ApiParam("排序值") Integer sort);

    @PostMapping("quote-index/update-sort")
    @ApiOperation("更新指标排序")
    BaseResult<Void> updateIndexSort(@RequestParam String param);

    @PostMapping("quote-index/type-and-position")
    @ApiOperation("根据id获取当前类型和位置")
    BaseResult<QuoteIndexResp> getTypeAndPosition(@RequestParam Long id);

    @PostMapping("quote-index/max-sort")
    @ApiOperation("获取当前类型和位置下最大排序值")
    BaseResult<Integer> getMaxSort(@RequestParam @ApiParam("类型: 10-分时线图， 20-k线蜡烛图") Integer type,
                                   @RequestParam @ApiParam("位置: 1-主图， 2-副图") Integer position);

    @GetMapping("promotion-resource/link-url")
    @ApiOperation("推广资源分享链接")
    BaseResult<String> promotionResourceLinkUrl(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam @ApiParam("天数类型（1-1天，3-3天，7-7天，999-永久）") Integer dayType,
            @RequestParam(required = false) @ApiParam("wxId") Integer wxId);

    @GetMapping("promotion-resource/folder-info")
    @ApiOperation("获取文件信息")
    BaseResult<SsPromotionResourceShareLink> getPromotionResourceShareLink(@RequestParam @ApiParam("链接编号") String number);

    @GetMapping("admin-app-version/list")
    @ApiOperation("版本管理列表")
    BaseResult<List<AdminAppVersionResp>> adminAppVersionList(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type);

    @PostMapping("admin-app-version/create")
    @ApiOperation("新增版本")
    BaseResult<Void> addAdminAppVersion(@RequestBody AdminAppVersionReq req);

    @PostMapping("admin-app-version/update")
    @ApiOperation("编辑版本")
    BaseResult<Void> updateAdminAppVersion(@RequestBody AdminAppVersionReq req);

    @ApiOperation("版本上架/下架")
    @PostMapping("admin-app-version/set-status")
    BaseResult<Void> updateAdminAppVersionStatus(@RequestParam @ApiParam("false:下架，true：上架") Boolean enabled,
                                                 @RequestParam @ApiParam("id") Long id);

    @ApiOperation("检查更新版本")
    @GetMapping("admin-app-version/check-update-version")
    BaseResult<AdminAppVersionInfoResp> checkAdminAppUpdateVersion(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type,
                                                                   @RequestParam @ApiParam("版本号") String version);

    @ApiOperation("获取最新版本")
    @GetMapping("admin-app-version/last")
    BaseResult<AdminAppVersionResp> getAdminAppVersionLast(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type);

    @GetMapping("quote-index/filter-list")
    @ApiOperation("获取指标信息列表")
    BaseResult<List<SsQuoteIndex>> getIndexListByFilter(
            @RequestParam(required = false) @ApiParam("类型: 10-分时线图， 20-k线蜡烛图") Integer type,
            @RequestParam(required = false) @ApiParam("位置: 1-主图， 2-副图") Integer position,
            @RequestParam(required = false) @ApiParam("上/下架") Boolean enabled);

    @GetMapping("promotion-resource/wx-link-list")
    @ApiOperation("推广资源领取记录")
    PageResult<List<PromotionResourceWxLinkResp>> promotionResourceWxLinkList(
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("promotion-resource/my-folder-list")
    @ApiOperation("获取我的文件夹列表")
    BaseResult<List<PromotionResourceFolderResp>> getMyPromotionResourceFolder(@RequestParam Integer wxId);
    @GetMapping("material/getMaterialByIdList")
    @ApiOperation("根据素材id批量获取素材")
    BaseResult<List<SsMaterial>> getMaterialByIdList(@RequestParam @ApiParam("营销素材id") List<Long> materialIds);

    @GetMapping("origin-material/getOriginMaterialByIdList")
    @ApiOperation("根据素材id批量获取好评素材")
    BaseResult<List<SsOriginMaterial>> getOriginMaterialByIdList(@RequestParam @ApiParam("好评素材id") List<Long> originMaterialIds);

    @GetMapping("material/getMaterialIdsMapByOriginMaterialIds")
    @ApiOperation("获取引用该好评素材的营销素材id列表")
    BaseResult<Map<Long, List<Long>>> getMaterialIdsMapByOriginMaterialIds(@RequestParam @ApiParam("好评素材列表") List<Long> originMaterialIds);

    @PostMapping("origin-material/audit")
    @ApiOperation("好评素材审核")
    BaseResult<Void> auditOriginMaterial(@RequestBody OriginMaterialAuditReq req);

    @PostMapping("material/relate-origin-material")
    @ApiOperation("关联好评素材")
    BaseResult<Void> relateOriginMaterial(@RequestBody RelateOriginMaterialReq req);

    @ApiOperation("财联社大涨股列表")
    @GetMapping("cls/stock/plate-list")
    BaseResult<List<ClsStockPlateResp>> clsStockPlateList(@RequestParam(required = false) @ApiParam("日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate date);

    @ApiOperation("财联社热点文章列表")
    @GetMapping("cls/article/recommend-list")
    BaseResult<List<ClsRecommendArticleResp>> clsArticleRecommendList(@RequestParam @ApiParam("条数") Integer size);

    @ApiOperation("获取最近涨停板块日期")
    @GetMapping("cls/latest-date")
    BaseResult<LocalDate> getLatestDate();

    @GetMapping("advert/category/list")
    @ApiOperation("广告分类列表")
    PageResult<List<SsAdvertCategory>> getAdvertCategoryList(
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enable,
            @RequestParam(required = false) @ApiParam("是否按创建时间顺序排序") Boolean isAsc,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size);

    @PostMapping("advert/category/create")
    @ApiOperation("新建分类")
    BaseResult<Void> createCategory(
            @RequestBody @Valid SsAdvertCategoryCreateReq req);

    @PostMapping("advert/category/update")
    @ApiOperation("修改分类")
    BaseResult<Void> updateCategory(
            @RequestBody @Valid SsAdvertCategoryUpdateReq req);

    @PostMapping("advert/category/enable")
    @ApiOperation("启用/禁用营销位分类")
    BaseResult<Void> getAdvertCategoryEnable(
            @RequestBody @Valid SsAdvertCategoryEnableReq req);

    @PostMapping("advert/category/batch")
    @ApiOperation("根据分类ID批量获取分类信息")
    BaseResult<Map<Long, SsAdvertCategory>> batchGetAdvertCategoryMap(@RequestBody BatchReq<Long> req);

    @ApiOperation("根据文章id批量获取文章列表")
    @PostMapping("cls/article/batch")
    BaseResult<List<LianV1Article>> batchArticleList(@RequestBody BatchReq<Integer> req);

    @ApiOperation("财联社最新预警文章")
    @GetMapping("cls/article/last-warning")
    BaseResult<LianV1Article> clsLastWarningArticle();

    @ApiOperation("财联社文章列表")
    @GetMapping("cls/article/list")
    BaseResult<List<ClsArticleInfoResp>> clsArticleList(
            @RequestParam(required = false) @ApiParam("文章类型(0:文章 -1:快讯)") Integer type,
            @RequestParam(required = false) @ApiParam("截止时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime dateTime,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size);

    @ApiOperation("根据文章id批量获取话题")
    @PostMapping("cls/article/subject-batch-by-article")
    BaseResult<Map<Integer, List<LianSubject>>> batchArticleSubjectMap(@RequestBody BatchReq<Integer> req);

    @ApiOperation("财联社文章详情")
    @GetMapping("cls/article/info")
    BaseResult<ClsArticleInfoResp> clsArticleInfo(@RequestParam @ApiParam("文章id") Integer articleId);

    @ApiOperation("财联社快讯最新数量")
    @GetMapping("cls/article/express-count")
    BaseResult<Integer> clsArticleExpressCount(
            @RequestParam @ApiParam("当前快讯最后时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime dateTime);

    @ApiOperation("财联社话题文章列表")
    @GetMapping("cls/article/subject-list")
    BaseResult<List<ClsArticleInfoResp>> clsSubjectArticleList(
            @RequestParam @ApiParam("话题id") Integer subjectId,
            @RequestParam(required = false) @ApiParam("截止时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime dateTime,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size);

    @ApiOperation("股票新闻详情")
    @GetMapping("cls/stock/news-info")
    BaseResult<ClsStockNewsInfoResp> clsStockNewsInfo(@RequestParam @ApiParam("新闻id") Integer id);

    @ApiOperation("股票研报详情")
    @GetMapping("cls/stock/research-info")
    BaseResult<ClsStockResearchInfoResp> clsStockResearchInfo(@RequestParam @ApiParam("研报id") Long id);

    @ApiOperation("股票公告详情")
    @GetMapping("cls/stock/announce-info")
    BaseResult<ClsStockAnnounceInfoResp> clsStockAnnounceInfo(@RequestParam @ApiParam("公告id") Integer id);

    @ApiOperation("股票新闻列表")
    @GetMapping("cls/stock/news-list")
    BaseResult<List<ClsStockNewsInfoResp>> clsStockNewsList(
            @RequestParam @ApiParam("股票代码") List<String> stockCodeList,
            @RequestParam(required = false) @ApiParam("上一页最小的id") Long cursor,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size);

    @ApiOperation("股票研报列表")
    @GetMapping("cls/stock/research-list")
    BaseResult<List<ClsStockResearchInfoResp>> clsStockResearchList(
            @RequestParam @ApiParam("股票代码") List<String> stockCodeList,
            @RequestParam(required = false) @ApiParam("上一页最小的id") Long cursor,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size);

    @ApiOperation("股票公告列表")
    @GetMapping("cls/stock/announce-list")
    BaseResult<List<ClsStockAnnounceInfoResp>> clsStockAnnounceList(
            @RequestParam @ApiParam("股票代码") List<String> stockCodeList,
            @RequestParam(required = false) @ApiParam("上一页最小的id") Long cursor,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size);

    @GetMapping("live-room/video-list")
    @ApiOperation("通过number获取视频列表")
    PageResult<List<SsLiveRoomVideoInfo>> getLiveRoomVideoList(
            @RequestParam @ApiParam("编号") String number,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size);

    @GetMapping("live-room/video-info")
    @ApiOperation("通过id获取视频详情")
    BaseResult<SsLiveRoomVideoInfo> getLiveRoomVideoInfo(@RequestParam @ApiParam Long id);
}

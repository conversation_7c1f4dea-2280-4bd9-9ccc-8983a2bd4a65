package cn.shrise.radium.contentservice.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2024/6/24 11:07
 * @Desc:
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class SoftwarePackageResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("软件包类型：10-国诚智投,20-决策家PC,30-决策家Android")
    private Integer packageType;

    @ApiModelProperty("版本号")
    private String version;

    @ApiModelProperty("更新内容")
    private String Content;

    @ApiModelProperty("是否上架")
    private Boolean enabled;

    @ApiModelProperty("安装包地址")
    private String url;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("软件包名")
    private String name;
}

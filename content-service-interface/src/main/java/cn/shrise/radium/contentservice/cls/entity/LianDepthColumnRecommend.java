package cn.shrise.radium.contentservice.cls.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * @Author: tang<PERSON>ajun
 * @Date: 2025/3/7 14:54
 * @Desc:
 **/
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "lian_depth_column_recommend",
        indexes = {
                @Index(name = "index_depth_column_reomment_column_id_article_id_uindex", columnList = "column_id, article_id, pid", unique = true)
        }
)
public class LianDepthColumnRecommend {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "column_id", nullable = false)
    private Integer columnId;

    @Column(name = "article_id", nullable = false)
    private Integer articleId;

    @Column(name = "pid", nullable = false)
    private Integer pid;

    @Column(name = "sort_score", nullable = false)
    private Integer sortScore;

}

package cn.shrise.radium.contentservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "ss_room_lottery_detail", indexes = {
        @Index(name = "uk_user_lottery", columnList = "user_id, lottery_id", unique = true),
        @Index(name = "ss_room_lottery_detail_ibfk4_idx", columnList = "lottery_id"),
        @Index(name = "ss_room_lottery_detail_ibfk2_idx", columnList = "sales_id"),
        @Index(name = "ss_room_lottery_detail_ibfk1_idx", columnList = "user_id"),
        @Index(name = "ss_room_lottery_detail_ibfk3_idx", columnList = "prize_id")
})
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsRoomLotteryDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "user_id", nullable = false)
    private Integer userId;

    @Column(name = "join_time", nullable = false)
    private Instant joinTime;

    @Column(name = "prize_id")
    private Long prizeId;

    @Column(name = "lottery_id", nullable = false)
    private Long lotteryId;

    @Column(name = "is_handled")
    private Boolean isHandled;

    @Column(name = "is_win")
    private Boolean isWin;

    @Column(name = "sales_id")
    private Integer salesId;

    @Column(name = "mobile", length = 128)
    private String mobile;

    @Column(name = "region", length = 128)
    private String region;

    @Lob
    @Column(name = "address")
    private String address;

    @Column(name = "name", length = 64)
    private String name;

    @Column(name = "mobile_id")
    private Long mobileId;

}
package cn.shrise.radium.contentservice.cls.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.Instant;

/**
 * @Author: tang<PERSON>ajun
 * @Date: 2025/3/6 10:51
 * @Desc:
 **/
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "lian_stock_pool",
        indexes = {
                @Index(name = "stock_code", columnList = "stock_code, cdate, pool"),
                @Index(name = "idx_stock_pool_1", columnList = "cdate, pool"),
                @Index(name = "index_stock_pool_code_cdate", columnList = "stock_code, cdate, plate_id", unique = true)
        }
)
public class LianStockPool {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "stock_code", nullable = false)
    private String stockCode;

    @Column(name = "cdate", nullable = false)
    private Instant cdate;

    @Column(name = "strength", nullable = false)
    private Integer strength;

    @Column(name = "up_time", nullable = false)
    private Instant upTime;

    @Column(name = "plate_id", nullable = false)
    private Integer plateId;

    @Column(name = "customize_plate", nullable = false)
    private String customizePlate;

    @Column(name = "up_reason", nullable = false)
    private String upReason;

    @Column(name = "up_count", nullable = false)
    private Integer upCount;

    @Column(name = "pool", nullable = false)
    private Integer pool;
}

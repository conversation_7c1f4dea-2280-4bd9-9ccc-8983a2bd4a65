package cn.shrise.radium.secureservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.secureservice.service.MobileMatchRecordService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/4/21, 星期一
 **/

@Slf4j
@RestController
@RequestMapping("mobile-match-record")
@RequiredArgsConstructor
public class MobileMatchRecordController {

    private final MobileMatchRecordService mobileMatchRecordService;

    @PostMapping
    @ApiOperation("手机号匹配记录")
    BaseResult<Void> mobileMatchRecord(
            @RequestParam @ApiParam("手机号") String mobile,
            @RequestParam @ApiParam("用户类型") Integer userType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("菜单") String menuName,
            @RequestParam(required = false) @ApiParam("产品类型") Integer productType,
            @RequestParam(required = false) @ApiParam("ip") String ip) {
        mobileMatchRecordService.createMobileMatchRecord(mobile, userType, userId, menuName, productType, ip);
        return BaseResult.successful();
    }

}

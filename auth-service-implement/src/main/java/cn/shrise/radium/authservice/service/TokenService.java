package cn.shrise.radium.authservice.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.authservice.config.bean.RsaKeyConfig;
import cn.shrise.radium.authservice.config.bean.RsaKeyConfigMap;
import cn.shrise.radium.authservice.constant.ErrorConstant;
import cn.shrise.radium.authservice.constant.TokenTypeConstant;
import cn.shrise.radium.authservice.entity.*;
import cn.shrise.radium.authservice.req.*;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.authservice.util.AccountUtils;
import cn.shrise.radium.authservice.util.LoginRedisUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.ServerProperties;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.UserTypeConstant;
import cn.shrise.radium.userservice.entity.UcLoginSession;
import cn.shrise.radium.userservice.entity.UcServerExt;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.req.CreateRefreshTokenReq;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.NpWorkWxUser;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.entity.UcWxAccount;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.Verification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.authservice.constant.TokenTypeConstant.ADMIN;
import static cn.shrise.radium.common.constant.JwtPayloadConstant.*;
import static cn.shrise.radium.userservice.constant.UserTypeConstant.GUEST;

@Slf4j
@Service
@RequiredArgsConstructor
public class TokenService {

    private final UserClient userClient;
    private final WxClient wxClient;
    private final WorkwxClient workwxClient;
    private final RsaKeyConfigMap rsaKeyConfigMap;

    private final ServerProperties serverProperties;
    private final LoginRedisUtil loginRedisUtil;

    private final static List<Integer> PCProductTypeList = Arrays.asList(182, 184);

    public AccessToken generateAccessToken(GenerateAccessTokenReq req) {
        String tokenType = req.getTokenType();
        RsaKeyConfig rsaKeyConfig = rsaKeyConfigMap.get(tokenType)
                .orElseThrow(IllegalArgumentException::new);

        String env = serverProperties.getEnv();
        //过期时间
        Instant expiresAt = req.getExpires() != null ? Instant.ofEpochSecond(req.getExpires())
                : Instant.now().plus(rsaKeyConfig.getExpire());

        AccessTokenPayload basicPayload = AccessTokenPayload.builder()
                .companyType(req.getCompanyType())
                .productType(req.getProductType())
                .userId(req.getUserId())
                .userType(req.getUserType())
                .nickname(req.getNickname())
                .avatarUrl(req.getAvatarUrl())
                .ddId(req.getDdId())
                .env(env)
                .build();

        //merge token payload
        Map<String, Object> payload = mergePayload(basicPayload, req.getExt());

        String token = JWT.create()
                .withKeyId(rsaKeyConfig.getKeyId())
                .withSubject(rsaKeyConfig.getSubject())
                .withIssuer(rsaKeyConfig.getIssuer())
                .withExpiresAt(Date.from(expiresAt))
                .withPayload(payload)
                .sign(rsaKeyConfig.getAlgorithm());
        //App登录互踢
//        if (req.getProductType().equals(ProductTypeEnum.PT_App.getCode())) {
//            log.info("创建App登录信息：userId:{}, token:{}", req.getUserId(), token);
//            loginRedisUtil.setLoginRecord(req.getUserId(), req.getProductType(), token);
//        }
        return new AccessToken(token, expiresAt);
    }

    public AccessToken generateUserAccessToken(GenerateUserAccessTokenReq req) {
        Integer productType = req.getProductType();
        Integer userId = req.getUserId();
        Integer wxId = req.getWxId();
        Integer accountType = req.getAccountType();

        if (ObjectUtils.allNull(userId, wxId)) {
            throw new BusinessException("userId和wxId不能同时为空");
        }

        UcUsers user;
        UcWxExt wxExt;
        UcServerExt serverExt;
        if (Objects.nonNull(userId)) {
            user = userClient.getUser(userId).orElseThrow();
            wxExt = wxClient.getUserWxExt(userId).orElse(null);
            serverExt = userClient.getServerExtById(userId).orElse(null);
        } else {
            wxExt = wxClient.getWxExt(wxId).orElseThrow();
            user = Objects.nonNull(wxExt.getUserId()) ?
                    userClient.getUser(wxExt.getUserId()).orElse(null) : null;
            serverExt = Objects.nonNull(wxExt.getUserId()) ?
                    userClient.getServerExtById(userId).orElse(null) : null;
        }

        return generateUserAccessToken(productType, accountType, user, wxExt, serverExt, null);
    }

    public AccessToken generateUserAccessToken(Integer productType, Integer accountType, UcUsers user, UcWxExt wxExt,
                                               UcServerExt serverExt, String openId) {
        String tokenType = TokenTypeConstant.USER;
        Integer userId = Objects.nonNull(user) ? user.getId() : null;
        String nickName = Objects.nonNull(user) ? user.getNickName() : null;
        String avatarUrl = Objects.nonNull(user) ? user.getAvatarUrl() : null;
        Long ddId = Objects.nonNull(serverExt) ? serverExt.getDdId() : null;
        Integer wxId = Objects.nonNull(wxExt) ? wxExt.getId() : null;
        Integer userType = Objects.isNull(user) ? GUEST : user.getUserType();
        Integer companyType = Objects.nonNull(user) ? user.getCompanyType() : (Objects.nonNull(wxExt) ? wxExt.getCompanyType() : null);


        // 获取微信用户信息
        Map<String, Object> wxPayload = getWxExtPayload(userId, accountType, wxId, openId);

        GenerateAccessTokenReq generateTokenReq = GenerateAccessTokenReq.builder()
                .tokenType(tokenType)
                .userId(userId)
                .productType(productType)
                .companyType(companyType)
                .userType(userType)
                .nickname(nickName)
                .avatarUrl(avatarUrl)
                .ddId(ddId)
                .ext(wxPayload)
                .build();
        return generateAccessToken(generateTokenReq);
    }

    public AccessToken generateWorkWxUserAccessToken(GenerateWorkWxUserAccessTokenReq req) {
        Long workWxId = req.getWorkWxId();

        NpWorkWxUser workWxUser = workwxClient.getWorkWxAccount(workWxId).orElseThrow();
        return generateWorkWxUserAccessToken(workWxUser);
    }

    public AccessToken generateWorkWxUserAccessToken(NpWorkWxUser workWxUser) {
        Integer companyType = workWxUser.getCompanyType();
        Integer userId = workWxUser.getBelongId();
        String wxAccount = workWxUser.getWxAccount();
        Integer accountType = workWxUser.getAccountType();
        UcUsers user = Objects.nonNull(userId) ? userClient.getUser(userId).orElseThrow() : null;
        String nickName = Objects.nonNull(user) ? user.getNickName() : null;
        String avatarUrl = Objects.nonNull(user) ? user.getAvatarUrl() : null;
        UcServerExt serverExt = Objects.nonNull(userId) ? userClient.getServerExtById(userId).orElse(null) : null;

        WorkWxPayload workWxPayload = WorkWxPayload.builder()
                .workWxId(workWxUser.getId())
                .wxAccount(wxAccount)
                .accountType(accountType)
                .build();
        GenerateAccessTokenReq generateAccessTokenReq = GenerateAccessTokenReq.builder()
                .companyType(companyType)
                .userType(UserTypeConstant.STAFF)
                .userId(userId)
                .nickname(nickName)
                .avatarUrl(avatarUrl)
                .ddId(Objects.nonNull(serverExt) ? serverExt.getDdId() : null)
                .tokenType(ADMIN)
                .ext(BeanUtil.beanToMap(workWxPayload))
                .build();
        return generateAccessToken(generateAccessTokenReq);
    }


    public RefreshToken generateRefreshToken(String tokenType, Integer userId, Integer productType) {
        RsaKeyConfig rsaKeyConfig = rsaKeyConfigMap.get(tokenType)
                .orElseThrow(IllegalArgumentException::new);
        boolean isPCProduct = PCProductTypeList.contains(productType);
        Duration refreshExpire = (isPCProduct && rsaKeyConfig.getPcRefreshExpire() != null) ?
                rsaKeyConfig.getPcRefreshExpire() : rsaKeyConfig.getRefreshExpire();
        Instant expire = Instant.now().plus(refreshExpire);
        CreateRefreshTokenReq req = CreateRefreshTokenReq.builder()
                .userId(userId)
                .productType(productType)
                .expire(expire)
                .build();
        BaseResult<UcLoginSession> refreshTokenResult = userClient.createRefreshToken(req);
        if (!refreshTokenResult.isSuccess()) {
            throw new BusinessException(refreshTokenResult);
        }
        final UcLoginSession loginSession = refreshTokenResult.getData();

        return RefreshToken.builder()
                .value(loginSession.getId().getLoginSessionId())
                .expire(expire)
                .build();
    }

    public AccessToken refreshToken(RefreshTokenReq req) {
        Integer userId = req.getUserId();
        String refreshToken = req.getRefreshToken();

        return verifyRefreshToken(userId, refreshToken, (loginSession) -> {
            BaseResult<UcUsers> getUserResult = userClient.getUser(userId);
            if (getUserResult.isFail()) {
                throw new BusinessException(getUserResult);
            }
            UcUsers user = getUserResult.getData();
            ErrorConstant userStatus = AccountUtils.checkAdminUserStatus(user);
            if (!userStatus.equals(ErrorConstant.SUCCESS)) {
                throw new BusinessException(userStatus);
            }
            UcServerExt serverExt = userClient.getServerExtById(userId).orElse(null);
            GenerateAccessTokenReq generateTokenReq = GenerateAccessTokenReq.builder()
                    .tokenType(req.getTokenType())
                    .companyType(user.getCompanyType())
                    .userType(user.getUserType())
                    .userId(user.getId())
                    .nickname(user.getNickName())
                    .avatarUrl(user.getAvatarUrl())
                    .ddId(Objects.nonNull(serverExt) ? serverExt.getDdId() : null)
                    .productType(req.getProductType())
                    .ext(req.getExt())
                    .build();
            return generateAccessToken(generateTokenReq);
        });
    }

    public AccessToken refreshAccessToken(RefreshAccessTokenReq req) {
        String tokenType = req.getTokenType();
        String accessToken = req.getAccessToken();
        String refreshToken = req.getRefreshToken();

        RsaKeyConfig rsaKeyConfig = rsaKeyConfigMap.get(tokenType)
                .orElseThrow(IllegalArgumentException::new);
        Duration refreshExpire = rsaKeyConfig.getRefreshExpire();
        AccessTokenPayload payload = decodeAccessToken(tokenType, accessToken, refreshExpire.getSeconds());

        if (Objects.equals(tokenType, TokenTypeConstant.USER)) {
            return refreshUserAccessToken(payload, refreshToken);
        } else if (Objects.equals(tokenType, ADMIN)) {
            return refreshAdminAccessToken(payload, refreshToken);
        } else {
            throw new BusinessException("不支持的token类型");
        }

    }

    public AccessToken refreshUserAccessToken(AccessTokenPayload payload, String refreshToken) {
        String tokenType = TokenTypeConstant.USER;
        Integer payloadUserId = payload.getUserId();
        Integer payloadWxId = payload.getWxId();
        Integer bindUserId = null;

        //检查用户是否绑定/解绑
        if (Objects.nonNull(payloadWxId)) {
            UcWxExt wxExt = wxClient.getWxExt(payloadWxId).orElseThrow();
            bindUserId = wxExt.getUserId();
        }

        Integer userId;
        if (ObjectUtils.isNotEmpty(bindUserId) && !Objects.equals(payloadUserId, bindUserId)) {
            log.warn("current wxId: {} has been changed from userId: {} to userId: {}",
                    payloadWxId, payloadUserId, bindUserId);
            userId = bindUserId;
        } else {
            userId = payloadUserId;
        }
        UcUsers user = Objects.nonNull(userId) ? userClient.getUser(userId).orElseThrow() : null;
        String nickName = Objects.nonNull(user) ? user.getNickName() : null;
        String avatarUrl = Objects.nonNull(user) ? user.getAvatarUrl() : null;
        UcServerExt serverExt = Objects.nonNull(userId) ? userClient.getServerExtById(userId).orElse(null) : null;

        Map<String, Object> ext = BeanUtil.beanToMap(payload);
        Map<String, Object> filterExt = filterExt(ext);
        GenerateAccessTokenReq.GenerateAccessTokenReqBuilder builder = GenerateAccessTokenReq.builder()
                .tokenType(tokenType)
                .userType(payload.getUserType())
                .companyType(payload.getCompanyType())
                .productType(payload.getProductType())
                .userId(userId)
                .nickname(nickName)
                .avatarUrl(avatarUrl)
                .ddId(Objects.nonNull(serverExt) ? serverExt.getDdId() : null)
                .ext(filterExt);

        //无userId或者无refreshToken 仅更新用户信息
        if (Objects.isNull(bindUserId) || ObjectUtils.isEmpty(refreshToken)) {
            GenerateAccessTokenReq generateTokenReq = builder
                    .expires(payload.getExp())
                    .build();
            return generateAccessToken(generateTokenReq);
        }

        return verifyRefreshToken(bindUserId, refreshToken, (loginSession) -> {
            GenerateAccessTokenReq generateTokenReq = builder.build();
            return generateAccessToken(generateTokenReq);
        });
    }

    public AccessToken refreshAdminAccessToken(AccessTokenPayload payload, String refreshToken) {
        String tokenType = ADMIN;
        Integer userId = payload.getUserId();
        UcUsers user = Objects.nonNull(userId) ? userClient.getUser(userId).orElseThrow() : null;
        String nickName = Objects.nonNull(user) ? user.getNickName() : null;
        String avatarUrl = Objects.nonNull(user) ? user.getAvatarUrl() : null;
        UcServerExt serverExt = Objects.nonNull(userId) ? userClient.getServerExtById(userId).orElse(null) : null;

        Map<String, Object> ext = BeanUtil.beanToMap(payload);
        Map<String, Object> filterExt = filterExt(ext);

        return verifyRefreshToken(userId, refreshToken, (loginSession) -> {
            GenerateAccessTokenReq generateTokenReq = GenerateAccessTokenReq.builder()
                    .tokenType(tokenType)
                    .userType(payload.getUserType())
                    .userId(userId)
                    .nickname(nickName)
                    .avatarUrl(avatarUrl)
                    .ddId(Objects.nonNull(serverExt) ? serverExt.getDdId() : null)
                    .companyType(payload.getCompanyType())
                    .productType(payload.getProductType())
                    .ext(filterExt)
                    .build();
            return generateAccessToken(generateTokenReq);
        });
    }

    private Map<String, Object> filterExt(Map<String, Object> ext) {
        Set<String> filterSet = new HashSet<>(Arrays.asList(
                "userId", "tokenType", "companyType", "productType",
                "iss", "exp", "sub", "aud", "nbf", "iat", "jti"));
        if (ObjectUtils.isEmpty(ext)) {
            return Collections.emptyMap();
        }
        Map<String, Object> newExt = new HashMap<>();
        ext.forEach((k, v) -> {
            if (!filterSet.contains(k)) {
                newExt.put(k, v);
            }
        });
        return newExt;
    }

    private AccessToken verifyRefreshToken(Integer userId, String refreshToken,
                                           Function<UcLoginSession, AccessToken> refreshTokenFunction) {
        BaseResult<UcLoginSession> refreshTokenResult = userClient.getRefreshToken(userId, refreshToken);
        UcLoginSession data = refreshTokenResult.orElseThrow();
        if (data == null) {
            throw new BusinessException(ErrorConstant.REFRESH_TOKEN_INVALID);
        }

        Instant expiredTime = data.getExpiredTime();
        if (Instant.now().isAfter(expiredTime)) {
            throw new BusinessException(ErrorConstant.REFRESH_TOKEN_INVALID);
        }
        return refreshTokenFunction.apply(data);
    }

    public AccessTokenPayload decodeAccessToken(String tokenType, String accessToken, Long acceptExpiresAt) {
        RsaKeyConfig rsaKeyConfig = rsaKeyConfigMap.get(tokenType)
                .orElseThrow(IllegalArgumentException::new);
        String issuer = rsaKeyConfig.getIssuer();
        Verification jwtVerifierBuilder = JWT.require(rsaKeyConfig.getAlgorithm())
                .withIssuer(issuer)
                .acceptExpiresAt(acceptExpiresAt);


        if (Objects.nonNull(accessToken)) {
            jwtVerifierBuilder.acceptExpiresAt(acceptExpiresAt);
        }
        JWTVerifier jwtVerifier = jwtVerifierBuilder.build();

        DecodedJWT decodedJWT;
        try {
            decodedJWT = jwtVerifier.verify(accessToken);
        } catch (JWTVerificationException e) {
            throw new BusinessException(ErrorConstant.ACCESS_TOKEN_INVALID);
        }

        AccessTokenPayload payload = new AccessTokenPayload();
        payload.setIss(decodedJWT.getIssuer());
        payload.setAud(decodedJWT.getAudience());
        payload.setSub(decodedJWT.getSubject());

        //second
        Long expires = decodedJWT.getExpiresAt() != null ? decodedJWT.getExpiresAt().toInstant().getEpochSecond() : null;
        payload.setExp(expires);

        Map<String, Claim> claims = decodedJWT.getClaims();
        Claim companyTypeClaim = claims.get(PAYLOAD_COMPANY_TYPE);
        Claim userIdClaim = claims.get(PAYLOAD_USER_ID);
        Claim userTypeClaim = claims.get(PAYLOAD_USER_TYPE);
        Claim nicknameClaim = claims.get(PAYLOAD_NICKNAME);
        Claim avatarUrlClaim = claims.get(PAYLOAD_AVATAR_URL);
        Claim productTypeClaim = claims.get(PAYLOAD_PRODUCT_TYPE);
        Claim wxIdClaim = claims.get(PAYLOAD_WX_ID);
        Claim accountTypeClaim = claims.get(PAYLOAD_ACCOUNT_TYPE);
        Claim openIdClaim = claims.get(PAYLOAD_OPEN_ID);
        Claim envClaim = claims.get(PAYLOAD_ENV);
        Claim workWxIdClaim = claims.get(PAYLOAD_WORK_WX_ID);
        Claim workWxAccountClaim = claims.get(PAYLOAD_WORK_WX_ACCOUNT);
        Claim ddIdClaim = claims.get(PAYLOAD_DINGDING_ID);

        if (companyTypeClaim != null) {
            payload.setCompanyType(companyTypeClaim.asInt());
        }
        if (userIdClaim != null) {
            payload.setUserId(userIdClaim.asInt());
        }
        if (userTypeClaim != null) {
            payload.setUserType(userTypeClaim.asInt());
        }
        if (nicknameClaim != null) {
            payload.setNickname(nicknameClaim.asString());
        }
        if (avatarUrlClaim != null) {
            payload.setAvatarUrl(avatarUrlClaim.asString());
        }
        if (productTypeClaim != null) {
            payload.setProductType(productTypeClaim.asInt());
        }
        if (wxIdClaim != null) {
            payload.setWxId(wxIdClaim.asInt());
        }
        if (accountTypeClaim != null) {
            payload.setAccountType(accountTypeClaim.asInt());
        }
        if (envClaim != null) {
            payload.setEnv(envClaim.asString());
        }
        if (openIdClaim != null) {
            payload.setOpenId(openIdClaim.asString());
        }
        if (workWxIdClaim != null) {
            payload.setWorkWxId(workWxIdClaim.asLong());
        }
        if (workWxAccountClaim != null) {
            payload.setWxAccount(workWxAccountClaim.asString());
        }
        if (ddIdClaim != null) {
            payload.setDdId(ddIdClaim.asLong());
        }
        //App登录互踢
//        String token = loginRedisUtil.getLoginRecord(payload.getUserId(), payload.getProductType());
//        if (ObjectUtils.isNotEmpty(token) && !token.equals(accessToken)) {
//            log.info("App登录互踢：userId:{}, token:{}, token2:{}", payload.getUserId(), token, accessToken);
//            throw new BusinessException(ErrorConstant.APP_LOGIN_OUT);
//        }
        return payload;
    }

    private Map<String, Object> mergePayload(AccessTokenPayload accessTokenPayload, Map<String, Object> ext) {
        Map<String, Object> payloadMap = BeanUtil.beanToMap(accessTokenPayload, false, true);
        if (ObjectUtils.isEmpty(ext)) {
            return payloadMap;
        }

        //合并属性 不允许ext的属性覆盖payload的属性
        ext.forEach((key, value) -> {
            if (!payloadMap.containsKey(key)) {
                payloadMap.put(key, value);
            }
        });

        return payloadMap.entrySet().stream()
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public AccessToken refreshAppAccessToken(RefreshAccessTokenReq req) {
        String tokenType = req.getTokenType();
        String accessToken = req.getAccessToken();
        String refreshToken = req.getRefreshToken();

        RsaKeyConfig rsaKeyConfig = rsaKeyConfigMap.get(tokenType)
                .orElseThrow(IllegalArgumentException::new);
        Duration refreshExpire = rsaKeyConfig.getRefreshExpire();
        AccessTokenPayload payload = decodeAccessToken(tokenType, accessToken, refreshExpire.getSeconds());

        if (Objects.equals(tokenType, TokenTypeConstant.USER)) {
            return refreshUserAppAccessToken(payload, refreshToken);
        } else {
            throw new BusinessException("不支持的token类型");
        }

    }

    public AccessToken refreshUserAppAccessToken(AccessTokenPayload payload, String refreshToken) {
        String tokenType = TokenTypeConstant.USER;
        Integer payloadUserId = payload.getUserId();
        Integer payloadWxId = payload.getWxId();

        if (ObjectUtils.isEmpty(payloadUserId)) {
            throw new BusinessException(ErrorConstant.ACCESS_TOKEN_INVALID);
        }

        //检查用户是否绑定/解绑 微信
        UcWxExt wxExt = wxClient.getUserWxExt(payloadUserId).orElse(new UcWxExt());
        Integer bindWxId = wxExt.getId();

        UcUsers user = userClient.getUser(payloadUserId).orElseThrow();

        if (!user.getEnabled()) {
            throw new BusinessException(ErrorConstant.ACCOUNT_CANCELED);
        }

        if (ObjectUtils.allNotNull(payloadWxId, bindWxId) && !Objects.equals(payloadWxId, bindWxId)) {
            log.warn("current userId: {} has been changed from wxId: {} to wxId: {}",
                    payloadUserId, payloadWxId, bindWxId);
        }
        if (ObjectUtils.isNotEmpty(wxExt.getId())) {
            Integer wxId = wxExt.getId();
            payload.setWxId(wxId);
        } else {
            payload.setWxId(null);
        }
        Map<String, Object> ext = BeanUtil.beanToMap(payload);
        Map<String, Object> filterExt = filterExt(ext);
        GenerateAccessTokenReq.GenerateAccessTokenReqBuilder builder = GenerateAccessTokenReq.builder()
                .tokenType(tokenType)
                .userType(payload.getUserType())
                .companyType(payload.getCompanyType())
                .productType(payload.getProductType())
                .userId(payloadUserId)
                .nickname(user.getNickName())
                .avatarUrl(user.getAvatarUrl())
                .ext(filterExt);

        //无refreshToken 仅更新用户信息
        if (ObjectUtils.isEmpty(refreshToken)) {
            GenerateAccessTokenReq generateTokenReq = builder
                    .expires(payload.getExp())
                    .build();
            return generateAccessToken(generateTokenReq);
        }

        return verifyRefreshToken(payloadUserId, refreshToken, (loginSession) -> {
            GenerateAccessTokenReq generateTokenReq = builder.build();
            return generateAccessToken(generateTokenReq);
        });
    }

    /**
     * app用户绑定wx获取新token
     *
     * @param req
     * @return
     */
    public GenerateTokenResp appBindWxAccessToken(GenerateAppUserBindWxAccessTokenReq req) {
        String tokenType = TokenTypeConstant.USER;
        UcUsers user = null;
        UcWxExt wxExt = null;
        if (Objects.nonNull(req.getUserId())) {
            user = userClient.getUser(req.getUserId()).orElseThrow();
            wxExt = wxClient.getUserWxExt(req.getUserId()).orElse(null);
        }
        //生成accessToken
        AccessToken accessToken = generateUserAccessToken(req.getProductType(), req.getAccountType(), user, wxExt, null, null);
        //生成refreshToken
        RefreshToken refreshToken = generateRefreshToken(tokenType, wxExt.getUserId(), req.getProductType());
        return GenerateTokenResp.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .build();
    }

    public Map<String, Object> getWxExtPayload(Integer userId, Integer accountType, Integer wxId, String openId) {
        UcWxExt wxExt = null;
        Integer accountId = null;
        if (Objects.nonNull(userId)) {
            wxExt = wxClient.getUserWxExt(userId).orElse(null);
        } else if (Objects.nonNull(wxId)) {
            wxExt = wxClient.getWxExt(wxId).orElse(null);
        }
        if (ObjectUtil.isAllNotEmpty(accountType, openId)) {
            accountId = wxClient.getWxAccount(accountType, openId).orElse(null).getWxAccount().getId();
        }

        // 获取微信用户信息
        Integer authorizationAccountType = accountType;
        if (Objects.nonNull(wxExt)) {
            wxId = wxExt.getId();
            if (accountType == null) {
                UcWxAccount wxAccount = wxClient.getAuthorizationWxAccount(wxExt.getCompanyType(), wxId)
                        .orElseThrow(() -> new BusinessException("该用户未在相应授权号上授权"));
                authorizationAccountType = wxAccount.getAccountType();
                accountId = wxAccount.getId();
            }

        }
        WxPayload payload = WxPayload.builder()
                .openId(openId)
                .accountType(authorizationAccountType)
                .wxId(wxId)
                .accountId(accountId)
                .build();
        return BeanUtil.beanToMap(payload);
    }

    public BaseResult<Duration> getRefreshExpire(String tokenType) {
        RsaKeyConfig rsaKeyConfig = rsaKeyConfigMap.get(tokenType)
                .orElseThrow(IllegalArgumentException::new);
        Duration refreshExpire = rsaKeyConfig.getRefreshExpire();
        return BaseResult.success(refreshExpire);
    }
}

package cn.shrise.radium.orderservice.properties.template;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TemplateProperties {
    /**
     * 模板名
     */
    private String name;
    /**
     * 模板value
     */
    private String value;
    /**
     * 模板color
     */
    private String color;
}

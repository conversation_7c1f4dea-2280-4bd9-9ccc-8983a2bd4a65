package cn.shrise.radium.orderservice.properties.template;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ConfigurationProperties(prefix = "order.open")
public class OrderOpenTemplateProperties {

    @NestedConfigurationProperty
    private Map<Integer, List<OpenTemplateProperties>> templates;
}

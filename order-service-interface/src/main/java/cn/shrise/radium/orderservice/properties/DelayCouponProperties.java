package cn.shrise.radium.orderservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "delay-coupon")
@EnableConfigurationProperties
public class DelayCouponProperties {
    /**
     * 订单的过期时长
     * ns用于纳秒
     * ms用于毫秒
     * s用于秒
     * m用于分
     * h用于小时
     * d应用日
     */
    private Long expire;

    public Long getExpireToMillis() {
        return Duration.ofDays(expire).toMillis();
    }
}

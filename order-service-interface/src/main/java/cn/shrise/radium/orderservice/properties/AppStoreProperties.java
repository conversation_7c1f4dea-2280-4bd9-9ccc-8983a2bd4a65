package cn.shrise.radium.orderservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "appstore")
@EnableConfigurationProperties
public class AppStoreProperties {

    private String appName;

    private String bundleId;

    private String verifyEndpoint;

    private String secret;

    private String productId;

    private Integer skuId;

    private Integer salesId;

    private Boolean enabled;

}

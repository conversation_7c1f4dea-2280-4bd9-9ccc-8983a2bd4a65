package cn.shrise.radium.orderservice.dto;

import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.orderservice.entity.RsArticleCourse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ArticleCourseDetailDto {

    private RsArticleCourse articleCourse;

    private ArticleSeries articleSeries;

    private List<String> codeList;
}

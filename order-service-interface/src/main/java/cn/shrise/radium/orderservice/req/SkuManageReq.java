package cn.shrise.radium.orderservice.req;

import cn.shrise.radium.common.base.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel("sku管理列表筛选条件")
@NoArgsConstructor
@AllArgsConstructor
public class SkuManageReq extends BasePageReq {

    @ApiModelProperty("是否线上(类型)")
    private Boolean isOnline;

    @ApiModelProperty("产品等级(属性)")
    private Integer productLevel;

    @ApiModelProperty("属性(分类)")
    private Integer category;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("公司类型")
    private Integer companyType;

    @ApiModelProperty("是否广告")
    private Boolean isPromotion;

    @ApiModelProperty("sku搜索内容")
    private String content;
}

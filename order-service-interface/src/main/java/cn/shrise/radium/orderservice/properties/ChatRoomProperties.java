package cn.shrise.radium.orderservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 聊天室相关配置
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "chatroom")
public class ChatRoomProperties {

    /**
     * 问诊室提问次数
     */
    private Integer qaCount;

    /**
     * 案例股票关注次数
     */
    private Integer stockCount;

}

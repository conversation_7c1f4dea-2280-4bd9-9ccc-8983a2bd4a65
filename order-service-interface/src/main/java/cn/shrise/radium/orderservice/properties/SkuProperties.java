package cn.shrise.radium.orderservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "sku")
public class SkuProperties {

    private List<Config> config;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Config {

        private Integer productLevel;

        private Integer vipLevel;
    }
}

package cn.shrise.radium.orderservice.constant;

import cn.shrise.radium.common.exception.BusinessException;

import static cn.shrise.radium.orderservice.constant.ResultConstant.ENUM_NOT_FOUND;

/**
 * <AUTHOR>
 */
public enum PayTypeConstantEnum {
    /**
     * 支付方式：
     * wx:微信,
     * ali:支付宝,
     * bank:银行转账,
     * union:银联支付,
     * iap:苹果内购,
     */
    WX(10, "微信支付"),
    ALI(20, "支付宝"),
    BANK(30, "转账"),
    ALLIN(40, "银联支付"),
    IAP(50, "苹果内购"),
    TL_WX(60, "通联微信支付"),
    L<PERSON><PERSON>(70, "拉卡拉微信支付"),
    LKL_ALI(80, "拉卡拉支付宝支付")
    ;

    PayTypeConstantEnum(Integer value, String status) {
        this.value = value;
        this.status = status;
    }

    private final Integer value;

    public String getStatus() {
        return status;
    }

    private final String status;

    public Integer getValue() {
        return value;
    }

    public static PayTypeConstantEnum getEnum(int value) {
        for (PayTypeConstantEnum payTypeConstantEnum : values()) {
            if (payTypeConstantEnum.getValue() == value) {
                return payTypeConstantEnum;
            }
        }
        throw new BusinessException(ENUM_NOT_FOUND);
    }
}

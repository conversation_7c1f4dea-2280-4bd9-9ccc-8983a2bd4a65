package cn.shrise.radium.orderservice.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Getter
public enum RefundFlowOperateTypeEnum {
    /**
     * 退款单审核操作类型
     */
    RFOT_Create(0, "创建"),
    RFOT_Assign(10, "分配"),
    RFOT_Audit(20, "客服审核"),
    RFOT_Confirm(30, "商务确认"),
    RFOT_Refund(40, "财务退款"),
    RFOT_Close(50, "退款关闭"),
    RFOT_COMPLETED(60, "退款完成");

    private final Integer value;
    private final String msg;

    RefundFlowOperateTypeEnum(Integer value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public static String getMsg(Integer value) {
        for (RefundFlowOperateTypeEnum val : RefundFlowOperateTypeEnum.values()) {
            if (val.value == value) {
                return val.msg;
            }
        }
        return null;
    }
}

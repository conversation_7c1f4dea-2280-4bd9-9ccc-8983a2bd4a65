package cn.shrise.radium.orderservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "rs_course_coupon_dept_relation", indexes = {
        @Index(name = "idx_department_id", columnList = "department_id"),
        @Index(name = "idx_plan_id", columnList = "plan_id")
})
public class CourseCouponDeptRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "plan_id", nullable = false)
    private Integer planId;

    @Column(name = "department_id", nullable = false)
    private Integer departmentId;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

}
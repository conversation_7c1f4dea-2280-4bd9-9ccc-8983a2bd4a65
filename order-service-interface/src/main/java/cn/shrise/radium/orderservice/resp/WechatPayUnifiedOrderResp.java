package cn.shrise.radium.orderservice.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatPayUnifiedOrderResp {

    /**
     * mweb_url h5支付跳转链接
     */
    private String mWebUrl;

    /**
     * trade_type为NATIVE时有返回，用于生成二维码，展示给用户进行扫码支付
     */
    private String codeUrl;

    private String signType;
    private String paySign;
    private String partnerId;
    private String appId;
    /**
     * 由于package为java保留关键字，因此改为packageValue. 前端使用时记得要更改为package
     */
    private String packageValue;
    private String timeStamp;
    private String nonceStr;
}

package cn.shrise.radium.orderservice.properties;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "alipay")
@EnableConfigurationProperties
public class AlipayProperties {

    private Map<Integer, AlipayProperties.Config> configs;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Config {

        private String appId;

        private String serverUrl;

        private String appPrivateKeyPath;

        private String alipayPublicCertPath;

        private String alipayRootCertPath;

        private String appCertPublicKeyPath;

        private Boolean isLocal;
    }
}

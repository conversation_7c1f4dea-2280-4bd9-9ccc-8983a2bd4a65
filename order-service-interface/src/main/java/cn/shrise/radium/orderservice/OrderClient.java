package cn.shrise.radium.orderservice;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ServiceConstant;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.entity.SkuServiceIntroduction;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.Tuple2;
import cn.shrise.radium.orderservice.constant.OrderErrorCode;
import cn.shrise.radium.orderservice.constant.OrderStatusEnum;
import cn.shrise.radium.orderservice.constant.RefundSignStatusEnum;
import cn.shrise.radium.orderservice.dto.*;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import cn.shrise.radium.orderservice.properties.esign.CompaniesConfig;
import cn.shrise.radium.orderservice.properties.esign.SignConfig;
import cn.shrise.radium.orderservice.properties.merchant.Merchant;
import cn.shrise.radium.orderservice.properties.vip.*;
import cn.shrise.radium.orderservice.req.*;
import cn.shrise.radium.orderservice.req.CreateAlipaySubOrderReq;
import cn.shrise.radium.orderservice.req.CreateUnionPaySubOrderReq;
import cn.shrise.radium.orderservice.req.allinpay.CreateAllinPaySubOrderReq;
import cn.shrise.radium.orderservice.req.complaint.OrderComplaintReplyReq;
import cn.shrise.radium.orderservice.req.lkl.CreateLklPaySubOrderReq;
import cn.shrise.radium.orderservice.req.payment.*;
import cn.shrise.radium.orderservice.resp.*;
import cn.shrise.radium.orderservice.resp.allinpay.AllinPayOrderResponse;
import cn.shrise.radium.orderservice.resp.allinpay.AllinRefundOrderResponse;
import cn.shrise.radium.orderservice.resp.allinpay.CreateAllinPaySubOrderResp;
import cn.shrise.radium.orderservice.resp.complaint.ComplaintDetailWxResp;
import cn.shrise.radium.orderservice.resp.complaint.ComplaintNegotiationWxResp;
import cn.shrise.radium.orderservice.resp.complaint.OrderComplaintWxResp;
import cn.shrise.radium.orderservice.resp.delaycoupon.ESignDelayCouponStartResp;
import cn.shrise.radium.orderservice.resp.gift.GiftCouponInfoResp;
import cn.shrise.radium.orderservice.resp.gift.GiftOrderInfoResp;
import cn.shrise.radium.orderservice.resp.payment.PaymentTypeResp;
import cn.shrise.radium.orderservice.resp.payment.TransferOrderResp;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.util.DateUtils.DEFAULT_PATTERN_DATE;

/**
 * <AUTHOR>
 */
@FeignClient(name = ServiceConstant.ORDER_SERVICE)
public interface OrderClient {

    @PostMapping("orders/edit")
    @ApiOperation("订单归属信息表修改")
    BaseResult<Void> updateOrderBelong(
            @RequestParam Integer orderId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("操作人id") Integer operateId,
            @RequestParam(required = false) @ApiParam("归属主任") Integer headId,
            @RequestParam(required = false) @ApiParam("归属经理") Integer managerId,
            @RequestParam(required = false) @ApiParam("归属总监") Integer directorId
    );

    @ApiOperation("获取订单归属信息列表")
    @GetMapping("orders/getOrderBelongList")
    PageResult<List<OrderBelongDto>> getOrderBelongList(
            @RequestParam(required = false) Instant startCreateTime,
            @RequestParam(required = false) Instant endCreateTime,
            @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("订单类型") Integer payType,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size
    );

    @ApiOperation("获取订单归属信息操作列表")
    @GetMapping("orders/getOrderBelongOperateRecordList")
    BaseResult<List<RsOrderBelongOperateRecord>> getOrderBelongOperateRecordList(@RequestParam @ApiParam("订单id") Integer orderId);

    @ApiOperation("获取主订单列表")
    @GetMapping("orders")
    PageResult<List<RsCourseOrder>> getOrderList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("skuID") Integer skuId,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size,
            @SortDefault(sort = "id", direction = Sort.Direction.DESC) @ApiParam("排序字段") Sort sort);

    default PageResult<List<RsCourseOrder>> getOrderList(Integer companyType, Integer userId, Integer orderStatus) {
        return getOrderList(companyType, userId, orderStatus, true, null, 1, 20, null);
    }

    default PageResult<List<RsCourseOrder>> getOrderList(Integer companyType, Integer userId, Integer orderStatus, Integer skuId) {
        return getOrderList(companyType, userId, orderStatus, true, skuId, 1, 20, null);
    }

    @ApiOperation("获取订单")
    @GetMapping("orders/{id}")
    BaseResult<RsCourseOrder> getOrder(@PathVariable Integer id);

    @ApiOperation("通过订单号获取订单")
    @GetMapping("orders/number/{orderNumber}")
    BaseResult<RsCourseOrder> getOrder(@PathVariable String orderNumber);

    @ApiOperation("通过主订单号获取订单")
    @GetMapping("orders/company/number")
    BaseResult<RsCourseOrder> getOrder(@RequestParam Integer companyType,
                                       @RequestParam String orderNumber);

    @ApiOperation("通过订单号获取订单(批量)")
    @GetMapping("orders/number/batch")
    BaseResult<List<RsCourseOrder>> getOrderList(@RequestParam List<String> orderNumbers);

    @ApiOperation("通过子订单号获取子订单(批量)")
    @GetMapping("orders/sub/number/batch")
    BaseResult<List<CourseSubOrder>> getCourseSubOrderList(@RequestParam List<String> subOrderNumbers);

    @ApiOperation("通过退款单号获取退款单")
    @GetMapping("refunds/number/{refundNumber}")
    BaseResult<RsCourseRefundOrder> getRefundOrder(@PathVariable String refundNumber);

    @ApiOperation("通过ID获取订单(全部属性)")
    @GetMapping("orders/{id}/full")
    BaseResult<FullOrder> getFullOrder(@PathVariable Integer id);

    @ApiOperation("按订单id获取订单回访")
    @GetMapping("orders/{id}/feedback")
    BaseResult<RsOrderFeedback> getOrderFeedback(@PathVariable Integer id);

    @ApiOperation("同步订单状态")
    @PostMapping("orders/{id}/sync")
    BaseResult<Void> syncOrder(@PathVariable Integer id);

    @ApiOperation("通过订单号获取订单(全部属性)")
    @GetMapping("orders/number/{orderNumber}/full")
    BaseResult<FullOrder> getFullOrder(@PathVariable String orderNumber);

    @ApiOperation("通过订单号获取订单(全部属性)")
    @GetMapping("orders/company/number/full")
    BaseResult<FullOrder> getFullOrderByCompanyNumber(@RequestParam Integer companyType,
                                                      @RequestParam String orderNumber);

    @ApiOperation("通过子订单流水号获取订单(全部属性)")
    @GetMapping("orders/sub/serialNumber/full")
    BaseResult<FullOrder> getFullOrderBySerialNumber(@RequestParam String serialNumber);

    @PostMapping("orders")
    @ApiOperation("创建主订单")
    BaseResult<RsCourseOrder> createOrder(@RequestBody @Valid CreateOrderReq req);

    @PostMapping("orders/noAuth")
    @ApiOperation("创建主订单(无需授权)")
    BaseResult<RsCourseOrder> createOrderNoAuth(@RequestBody @Valid CreateOrderReq req);

    @ApiOperation("获取订单")
    @GetMapping("orders/byFilter")
    BaseResult<OrderInfoDto> findOrderByFilter(@RequestParam @ApiParam("公司类型") Integer companyType,
                                               @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
                                               @RequestParam(required = false) @ApiParam("订单ID") Integer orderId,
                                               @RequestParam(required = false) @ApiParam("订单状态") OrderStatusEnum status);

    @ApiOperation("获取用户未完成的订单")
    @GetMapping("orders/unfinished")
    BaseResult<UnFinishedOrderResp> getUserUnFinishedOrderList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("SkuID") Integer skuId,
            @RequestParam @ApiParam("销售Id") Integer salesId);

    /**
     * 获取用户未签字的订单
     *
     * @param companyType 公司类型
     * @param userId      用户id
     * @param category    sku分类
     * @return 未签字订单
     */
    @ApiOperation("获取用户未签字的订单")
    @GetMapping("orders/unsigned")
    BaseResult<List<RsCourseOrder>> getUserUnSignedOrderList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("sku分类") Integer category);

    @GetMapping("orders/getByWxSku")
    @ApiOperation("根据wxId,skuId查询订单")
    BaseResult<RsCourseOrder> getOrderByWxSku(
            @RequestParam Integer companyType,
            @RequestParam Integer wxId,
            @RequestParam Integer skuId,
            @RequestParam Integer orderStatus);

    @GetMapping("orders/get-by-wx-sku-number")
    @ApiOperation("根据wxId,skuNumber查询订单")
    BaseResult<RsCourseOrder> getOrderByWxSkuNumber(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Integer wxId,
            @RequestParam(required = false) Integer wxAccountId,
            @RequestParam String skuNumber,
            @RequestParam Integer status,
            @RequestParam Integer days);

    /**
     * 根据查询获取销售相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @GetMapping("orders/sales")
    BaseResult<List<SalesOrderInfo>> getSalesOrderInfoList(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel);

    /**
     * 根据查询获取销售相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @GetMapping("refunds/sales")
    BaseResult<List<SalesRefundInfo>> getSalesRefundInfoList(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel);

    /**
     * 根据查询获取销售相关有效订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @GetMapping("refunds/valid/sales")
    BaseResult<List<SalesRefundInfo>> getSalesValidRefundInfoList(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel);

    /**
     * 根据查询获取销售相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @GetMapping("orders/sales/total")
    BaseResult<List<SalesOrderInfo>> getSalesOrderInfoTotal(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel);

    /**
     * 根据查询获取销售相关退款订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @GetMapping("refunds/sales/total")
    BaseResult<List<SalesRefundInfo>> getSalesRefundInfoTotal(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel);

    /**
     * 根据查询获取销售相关有效退款订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @GetMapping("refunds/valid/sales/total")
    BaseResult<List<SalesRefundInfo>> getSalesValidRefundInfoTotal(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel);

    /**
     * 根据查询获取部门相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @param isAnalyst  是否使用分析型数据库
     * @return 订单信息
     */
    @GetMapping("orders/dept")
    BaseResult<List<DepartmentOrderInfo>> getDeptOrderInfoList(@RequestParam("startTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam("endTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam Boolean isAnalyst);

    /**
     * 根据查询获取相关订单信息
     *
     * @param companyType 公司类型
     * @param userId
     * @return 订单信息
     */
    @GetMapping("orders/findAllByUser")
    BaseResult<List<RsCourseOrder>> findAllByUser(@RequestParam Integer companyType, @RequestParam Integer userId);

    /**
     * 根据查询获取部门相关订单信
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单产品级别
     * @param isAnalyst  是否使用分析型数据库
     * @return 订单信息
     */
    @GetMapping("orders/dept/total")
    BaseResult<List<DepartmentOrderInfo>> getDeptOrderInfoTotal(@RequestParam("startTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam("endTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam Boolean isAnalyst);

    /**
     * 根据查询获取部门相关退款订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单产品级别
     * @param isAnalyst  是否使用分析型数据库
     * @return 订单信息
     */
    @ApiOperation("获取退款订单信息")
    @GetMapping("refunds/dept")
    BaseResult<List<DepartmentRefundInfo>> getDeptRefundInfoList(@RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam Boolean isAnalyst);

    /**
     * 根据查询获取部门相关有效退款订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单产品级别
     * @param isAnalyst  是否使用分析型数据库
     * @return 订单信息
     */
    @ApiOperation("获取有效退款订单信息")
    @GetMapping("refunds/valid/dept")
    BaseResult<List<DepartmentRefundInfo>> getDeptValidRefundInfoList(@RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam Boolean isAnalyst);

    /**
     * 根据查询获取部门相关退款订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单产品级别
     * @param isAnalyst  是否使用分析型数据库
     * @return 订单信息
     */
    @GetMapping("refunds/dept/total")
    BaseResult<List<DepartmentRefundInfo>> getDeptRefundInfoTotal(@RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam Boolean isAnalyst);

    /**
     * 根据查询获取部门相关有效退款订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单产品级别
     * @param isAnalyst  是否使用分析型数据库
     * @return 订单信息
     */
    @GetMapping("refunds/valid/dept/total")
    BaseResult<List<DepartmentRefundInfo>> getDeptValidRefundInfoTotal(@RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam Boolean isAnalyst);

    @PostMapping("refunds/batch")
    @ApiOperation("批量获取退款单")
    BaseResult<List<RsCourseRefundOrder>> batchGetRefund(@RequestBody BatchReq<Integer> req);

    @PostMapping("refunds/batch/refundId")
    @ApiOperation("批量获取退款单")
    BaseResult<List<RsCourseRefundOrder>> batchGetRefundByRefundId(@RequestBody BatchReq<Integer> req);

    @ApiOperation("根据订单Id批量获取退款列表")
    @PostMapping("refunds/batch/orderId")
    BaseResult<List<RsCourseRefundOrder>> getRefundList(
            @RequestBody BatchReq<Integer> req,
            @RequestParam(required = false) Integer status);

    @GetMapping("invoice")
    @ApiOperation("获取发票列表")
    PageResult<List<Tuple2<RsCourseOrder, RsInvoice>>> getInvoiceList(
            @RequestParam(required = false) @ApiParam("订单id") Integer orderId
    );

    @GetMapping("invoice")
    @ApiOperation("获取发票列表")
    PageResult<List<Tuple2<RsCourseOrder, RsInvoice>>> getInvoiceList(
            @RequestParam(required = false) @ApiParam("订单id") Integer orderId,
            @RequestParam(required = false) @ApiParam("开票状态") Integer status,
            @RequestParam(required = false) @ApiParam("支付方式") Integer payType,
            @RequestParam(required = false) @ApiParam("备注") String remark,
            @RequestParam(required = false) @ApiParam("是否退款") Boolean isRefund,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("搜索(订单编号、用户id)") String search,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

    @GetMapping("invoice/{invoiceId}")
    @ApiOperation("获取发票")
    BaseResult<RsInvoice> getInvoice(@PathVariable Long invoiceId);

    @PostMapping("invoice")
    @ApiOperation("创建发票")
    BaseResult<RsInvoice> createInvoice(@RequestBody @Valid CreateInvoiceReq req);

    @PostMapping("invoice/batch")
    @ApiOperation("批量获取发票")
    BaseResult<Map<Integer, RsInvoice>> batchGetInvoice(@RequestBody BatchReq<Integer> req);

    @PostMapping("invoice/batch")
    @ApiOperation("批量获取发票")
    BaseResult<Map<Integer, RsInvoice>> batchGetInvoice(@RequestParam Integer idType, @RequestBody BatchReq<Integer> req);

    @GetMapping("invoice/order/id")
    @ApiOperation("通过订单ID获取发票")
    BaseResult<List<RsInvoice>> getInvoiceByOrderId(@RequestParam @ApiParam("订单id") Integer orderId);

    @PatchMapping("invoice/{invoiceId}")
    @ApiOperation("更新发票")
    BaseResult<RsInvoice> updateInvoice(@PathVariable Long invoiceId,
                                        @RequestBody @Valid UpdateInvoiceReq req);

    @GetMapping("sku/{id}")
    @ApiOperation("获取sku")
    BaseResult<RsSku> getSku(@PathVariable Integer id);

    @GetMapping("sku/{skuId}/material")
    @ApiOperation("获取sku的素材")
    BaseResult<List<SkuMaterialItem>> getSkuMaterialList(
            @PathVariable Integer skuId,
            @RequestParam(required = false) Integer category,
            @RequestParam(required = false) Integer position,
            @RequestParam(required = false) Boolean enabled);

    default BaseResult<List<SkuMaterialItem>> getSkuMaterialList(Integer skuId) {
        return getSkuMaterialList(skuId, null, null, true);
    }

    @GetMapping("sku/{skuId}/service")
    @ApiOperation("获取sku的服务项")
    BaseResult<List<RsSkuService>> getSkuServiceItemList(
            @PathVariable Integer skuId,
            @RequestParam(required = false) Integer serviceType,
            @RequestParam(required = false, defaultValue = "true") Boolean enabled);

    @GetMapping("sku/{skuId}/service/introduction")
    @ApiOperation("获取sku的服务项介绍")
    BaseResult<List<SkuServiceIntroduction>> getSkuServiceIntroductionList(
            @PathVariable Integer skuId,
            @RequestParam(required = false) Integer serviceType,
            @RequestParam(required = false, defaultValue = "true") Boolean enabled);

    default BaseResult<List<SkuServiceIntroduction>> getSkuServiceIntroductionList(Integer skuId) {
        return getSkuServiceIntroductionList(skuId, null, true);
    }

    @GetMapping("sku/number/{skuNumber}")
    @ApiOperation("通过编号获取sku")
    BaseResult<RsSku> getSku(@PathVariable String skuNumber, @RequestParam Integer companyType);

    @GetMapping("sku/users")
    @ApiOperation("获取用户可见的sku")
    PageResult<List<SkuDto>> getUserSkuList(@RequestParam Integer userId,
                                            @RequestParam(required = false) Integer status,
                                            @RequestParam(required = false) String skuName,
                                            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
                                            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @PostMapping("sku/batch")
    @ApiOperation("批量获取sku")
    BaseResult<Map<Integer, RsSku>> batchGetSkuMap(@RequestBody BatchReq<Integer> req);

    @PostMapping("sku/batch/list")
    @ApiOperation("根据ID批量获取sku列表")
    BaseResult<List<RsSku>> batchGetSkuList(@RequestBody BatchReq<Integer> req);

    @PostMapping("sku/batch/number")
    @ApiOperation("根据number批量获取sku列表")
    BaseResult<List<RsSku>> batchGetSkuList(@RequestBody BatchReq<String> req,
                                            @RequestParam Integer companyType,
                                            @RequestParam(required = false) Integer status,
                                            @RequestParam(required = false) Boolean isPromotion);

    @GetMapping("sku/material/{id}")
    @ApiOperation("根据id获取sku素材信息")
    BaseResult<SkuMaterial> getSkuMaterial(@PathVariable Long id);

    @GetMapping("sku/material")
    @ApiOperation("获取sku素材列表")
    PageResult<List<SkuMaterial>> getSkuMaterialList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer category,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size,
            @ApiIgnore Sort sort);

    @PostMapping("sku/material")
    @ApiOperation("创建sku素材信息")
    BaseResult<SkuMaterial> createSkuMaterial(@RequestBody @Valid CreateSkuMaterialReq req);

    @PostMapping("sku/material/batch")
    @ApiOperation("批量创建sku素材信息")
    BaseResult<Void> createSkuMaterial(@RequestBody @Valid BatchReq<CreateSkuMaterialReq> req);

    @PostMapping("sku/material/batchMap")
    @ApiOperation("批量获取sku素材信息Map")
    BaseResult<Map<Integer, List<SkuMaterialItem>>> batchSkuMaterialMap(@RequestBody @Valid BatchReq<Integer> req);

    @PatchMapping("sku/material/{id}")
    @ApiOperation("更新sku素材信息")
    BaseResult<Void> updateSkuMaterial(
            @PathVariable Long id,
            @RequestBody @Valid UpdateSkuMaterialReq req);

    @PostMapping("sku/material/cover/batch")
    @ApiOperation("批量获取SKU的封面")
    BaseResult<List<SkuCoverResp>> getSkuCoverList(@RequestBody @Valid BatchReq<Integer> req);

    @ApiOperation("获取员工订单排行榜")
    @GetMapping("orders/user/volume")
    BaseResult<List<UserOrderVolumeResp>> getUserOrderVolume(
            @RequestBody List<Integer> userIds,
            @RequestParam(required = true) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = true) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "10") @ApiParam("显示数量") Integer size,
            @RequestParam(defaultValue = "1") @ApiParam("前端后端") Integer category);

    @GetMapping("wholeSelectOrder/{searchNumber}")
    @ApiOperation("获取主订单号")
    BaseResult<Integer> getOrderId(
            @RequestParam Integer companyType,
            @PathVariable String searchNumber);

    @GetMapping("wholeSelectOrder/search/{pkId}")
    @ApiOperation("全局搜索获取订单信息")
    BaseResult<WholeSelectOrderInfo> getWholeOrder(@PathVariable Integer pkId);

    /**
     * 商务审核管理列表
     *
     * @param manageReq 查询条件
     * @return 商务审核管理列表分页
     */
    @ApiOperation("获取商务审核管理列表")
    @PostMapping("orders/auditManage")
    PageResult<List<AuditManageResp>> getAuditManageList(
            @ApiParam @RequestBody(required = false) @Validated AuditManageReq manageReq);

    @ApiOperation("获取商务审核管理列表")
    @PostMapping("orders/export/auditManage")
    BaseResult<List<AuditManageResp>> getAuditManageList(
            @ApiParam @RequestBody(required = false) @Validated ExportBusinessAuditReq manageReq);

    /**
     * 分配商务
     *
     * @param operatorId
     * @param auditorId
     * @param orderIds
     * @return
     */
    @ApiOperation("分配商务")
    @PatchMapping("orders/assign")
    BaseResult<Boolean> swAssign(@RequestParam @ApiParam("分配人ID") Integer operatorId,
                                 @RequestParam @ApiParam("商务审核人ID") Integer auditorId,
                                 @RequestParam @ApiParam("订单ID") List<Integer> orderIds);

    @ApiOperation("查询审核流程记录")
    @GetMapping("orders/flow/{orderId}")
    List<OrderFlowRecord> findByOrderId(@PathVariable Integer orderId);

    @ApiOperation("通过子订单ID获取子订单")
    @GetMapping("orders/sub/{subOrderId}")
    BaseResult<CourseSubOrder> getSubOrder(@PathVariable Integer subOrderId);

    @ApiOperation("通过子订单号获取子订单")
    @GetMapping("orders/sub/number/{subOrderNumber}")
    BaseResult<CourseSubOrder> getSubOrder(@PathVariable String subOrderNumber);

    @ApiOperation("通过子订单号获取大额转账信息")
    @GetMapping("orders/sub/online-transfer-info")
    BaseResult<RsOnlineTransferInfo> getSubOrderOnlineTransferInfo(@RequestParam Integer subOrderId);

    @ApiOperation("通过主订单ID获取子订单列表")
    @GetMapping("orders/{orderId}/sub")
    BaseResult<List<CourseSubOrder>> getSubOrderList(
            @PathVariable Integer orderId,
            @RequestParam(required = false) Integer subOrderStatus);

    @ApiOperation("通过主订单ID获取子订单信息")
    @GetMapping("orders/{orderId}/subInfo")
    BaseResult<List<SubOrderDto>> findSubOrderList(
            @PathVariable Integer orderId,
            @RequestParam(required = false) Integer subOrderStatus);

    /**
     * 根据订单id获取相关子订单信息
     *
     * @param orderId 订单id
     * @return 相关子订单信息
     */
    default BaseResult<List<CourseSubOrder>> getSubOrderList(@PathVariable Integer orderId) {
        return getSubOrderList(orderId, OrderStatusEnum.PASSED.getValue());
    }

    @ApiOperation("通过主订单号获取子订单列表")
    @GetMapping("orders/number/{orderNumber}/sub")
    BaseResult<List<CourseSubOrder>> getSubOrderList(
            @PathVariable String orderNumber,
            @RequestParam(required = false) Integer subOrderStatus);

    /**
     * 通过主订单号获取子订单列表
     *
     * @param orderNumber 主订单号
     * @return
     */
    default BaseResult<List<CourseSubOrder>> getSubOrderList(String orderNumber) {
        return getSubOrderList(orderNumber, null);
    }

    @ApiOperation("查询子订单信息（所有属性）")
    @PostMapping("orders/sub/full")
    BaseResult<List<FullSubOrder>> getFullSubOrderList(@RequestBody BatchReq<Integer> req);

    @ApiOperation("创建子订单")
    @PostMapping("orders/sub")
    BaseResult<CreateSubOrderResp> createSubOrder(@RequestBody CreateSubOrderReq req);

    @ApiOperation("更新子订单")
    @PutMapping("orders/sub/{subOrderId}")
    BaseResult<Void> updateSubOrder(@PathVariable Integer subOrderId, @RequestBody UpdateSubOrderReq req);

    @ApiOperation("关闭子订单")
    @PutMapping("orders/sub/close/{subOrderId}")
    BaseResult<Boolean> closeSubOrder(@PathVariable Integer subOrderId);

    @ApiOperation("通过主订单号获取已支付子订单金额")
    @GetMapping("orders/sub/allAmount")
    BaseResult<Integer> getAllSubPaidAmount(@RequestParam Integer orderId);

    @ApiOperation("通过主订单号批量获取已支付子订单金额")
    @GetMapping("orders/sub/batchAllAmount")
    BaseResult<Map<Integer, Integer>> batchGetAllSubPaidAmount(@RequestParam List<Integer> orderIdList);

    /**
     * 获取订单相关信息
     *
     * @param id 订单id
     * @return 订单相关信息
     */
    @ApiOperation("获取订单相关信息")
    @GetMapping("orders/info/{id}")
    BaseResult<OrderInfoResp> getOrderInfo(@PathVariable Integer id);

    @ApiOperation("统计用户订单数量")
    @GetMapping("orders/{userId}/count")
    BaseResult<UserStatisticsCount> getOrderCount(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion);

    @ApiOperation("统计用户订单数量")
    @GetMapping("orders/company/userId/count")
    BaseResult<UserStatisticsCount> getCompanyOrderCount(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion);

    default BaseResult<UserStatisticsCount> getOrderCount(Integer userId, Integer orderStatus) {
        return getOrderCount(userId, orderStatus, false);
    }

    @ApiOperation("统计用户订单数量")
    @PostMapping("orders/count")
    BaseResult<List<UserStatisticsCount>> getOrderCount(
            @RequestBody @Valid @ApiParam("用户id列表") BatchReq<Integer> req,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion);

    default BaseResult<List<UserStatisticsCount>> getOrderCount(BatchReq<Integer> req, Integer orderStatus) {
        return getOrderCount(req, orderStatus, false);
    }

    /**
     * 根据订单id获取退款信息
     *
     * @param orderId 订单id
     * @return 退款信息
     */
    @ApiOperation("退款信息")
    @GetMapping("refunds/{orderId}")
    BaseResult<List<RsCourseRefundOrder>> getRefundList(
            @PathVariable Integer orderId,
            @RequestParam(required = false) Integer status);

    default BaseResult<List<RsCourseRefundOrder>> getRefundList(Integer orderId) {
        return getRefundList(orderId, null);
    }

    @ApiOperation("按范围获取退款列表")
    @GetMapping("refunds/range")
    BaseResult<List<RsCourseRefundOrder>> getRefundList(
            @RequestParam Integer cursor,
            @RequestParam(required = false) Integer status,
            @RequestParam Integer limit);

    @ApiOperation("统计用户退款订单数量")
    @GetMapping("refunds/{userId}/count")
    BaseResult<UserStatisticsCount> getRefundOrderCount(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("退款订单状态") Integer refundStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion);

    @ApiOperation("统计用户退款订单数量")
    @GetMapping("refunds/company/userId/count")
    BaseResult<UserStatisticsCount> getCompanyRefundOrderCount(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("退款订单状态") Integer refundStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion);

    default BaseResult<UserStatisticsCount> getRefundOrderCount(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("退款订单状态") Integer refundStatus) {
        return getRefundOrderCount(userId, refundStatus, false);
    }

    @ApiOperation("统计用户退款订单数量")
    @PostMapping("refunds/count")
    BaseResult<List<UserStatisticsCount>> getRefundOrderCount(
            @RequestBody @Valid @ApiParam("用户id列表") BatchReq<Integer> req,
            @RequestParam(required = false) @ApiParam("退款订单状态") Integer refundStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion);

    default BaseResult<List<UserStatisticsCount>> getRefundOrderCount(
            @RequestBody @Valid @ApiParam("用户id列表") BatchReq<Integer> req,
            @RequestParam(required = false) @ApiParam("退款订单状态") Integer refundStatus) {
        return getRefundOrderCount(req, refundStatus, false);
    }

    /**
     * 微信支付回调通知处理
     *
     * @param accountType 公众号类型
     * @param xmlData     加密消息体
     * @return success
     */
    @ApiOperation("微信支付回调通知处理")
    @PostMapping(value = "wx_pay/notify/order")
    BaseResult<String> wxPayOrderNotifyHandle(
            @RequestParam @ApiParam("微信类型") Integer accountType,
            @RequestParam @ApiParam("加密消息体") String xmlData);

    /**
     * 支付宝支付回调通知处理
     *
     * @param parameterMap http请求参数
     * @return 处理结果
     */
    @ApiOperation("支付宝支付回调通知处理")
    @PostMapping(value = "alipay/notify/order/{companyType}")
    BaseResult<Boolean> alipayNotifyHandle(
            @RequestParam @ApiParam("http请求参数") Map<String, String> parameterMap, @PathVariable Integer companyType);

    @ApiOperation("创建支付宝H5支付子订单")
    @PostMapping("alipay/h5/orders/sub")
    BaseResult<CreateAlipaySubOrderResp> createAlipaySubOrder(@RequestBody @Valid CreateAlipaySubOrderReq req);

    @ApiOperation("创建回传记录")
    @PostMapping(value = "convert/create")
    BaseResult<OsErrorCode> createConvert(
            @RequestParam @ApiParam("订单id") Integer orderId,
            @RequestParam @ApiParam("转化类型") Integer convertType,
            @RequestParam @ApiParam("转化值") String convertValue,
            @RequestParam @ApiParam("内容") String content,
            @RequestParam @ApiParam("结果") String resultCode,
            @RequestParam(required = false) @ApiParam("头条clickid") String ttClickId,
            @RequestParam(required = false) @ApiParam("快手callback") String ksCallback,
            @RequestParam(required = false) @ApiParam("广点通clickid") String gdtClickID);

    @ApiOperation("查询回传记录")
    @GetMapping(value = "convert/find_one")
    BaseResult<RsWxWebOrderConvertRecord> findConvertOneByOrderId(
            @RequestParam @ApiParam("订单id") Integer orderId);

    @GetMapping("export_record/unfinished/{userId}")
    @ApiOperation("查找未完成的导出记录")
    BaseResult<RsFileExportRecord> getUnfinishedExportRecord(@PathVariable Integer userId);

    @GetMapping("export_record")
    @ApiOperation("获取所有导出记录")
    PageResult<List<RsFileExportRecord>> getExportRecordList(
            @RequestParam Integer companyType, @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @PostMapping("export_record")
    @ApiOperation("新增一条导出记录")
    BaseResult<RsFileExportRecord> addExportRecord(@RequestBody RsFileExportRecord record);

    @PutMapping("export_record/{recordId}")
    @ApiOperation("修改导出记录")
    BaseResult<String> updateExportRecord(@PathVariable Integer recordId, @RequestBody RsFileExportRecord record);

    @PutMapping("export_record/{planId}/status")
    @ApiOperation("修改导出状态")
    BaseResult<String> updateExportRecordStatus(@PathVariable Integer planId, @RequestParam Integer status);

    @ApiOperation("修改订单开票状态")
    @PutMapping("orders/invoice/status")
    void updateInvoiceStatus(
            @RequestParam(required = false) Integer orderId,
            @RequestParam(required = false) Integer invoiceStatus);

    @ApiOperation("修改订单备注")
    @PutMapping("orders/update/remark")
    BaseResult<OsErrorCode> updateRemarkStatus(
            @RequestParam Integer orderId,
            @RequestParam String remark);

    @ApiOperation("修改订单免签")
    @PostMapping("orders/update/avoidSign")
    BaseResult<OsErrorCode> updateAvoidSign(
            @RequestParam Integer orderId,
            @RequestParam Boolean isAvoidSign);

    @ApiOperation("关闭订单")
    @PostMapping("orders/close")
    BaseResult<OsErrorCode> closeOrder(
            @RequestParam @ApiParam("订单ID") Integer orderId,
            @RequestParam @ApiParam("操作人id") Integer operatorId);

    @ApiOperation("获取销售订单明细")
    @GetMapping("orders/sales/order/detail")
    PageResult<List<SalesOrderDetailResp>> getSalesOrderDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("获取部门订单明细")
    @GetMapping("orders/dept/order/detail")
    PageResult<List<SalesOrderDetailResp>> getDeptOrderDetail(
            @RequestParam @ApiParam("部门ID") Integer deptId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("获取销售退款订单明细")
    @GetMapping("refunds/sales/refund/detail")
    PageResult<List<SalesRefundDetailResp>> getSalesRefundDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("获取销售有效退款订单明细")
    @GetMapping("refunds/sales/valid/refund/detail")
    PageResult<List<SalesRefundDetailResp>> getSalesValidRefundDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("获取部门退款订单明细")
    @GetMapping("refunds/dept/refund/detail")
    PageResult<List<SalesRefundDetailResp>> getDeptRefundDetail(
            @RequestParam @ApiParam("部门ID") Integer deptId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("获取部门有效退款订单明细")
    @GetMapping("refunds/dept/valid/refund/detail")
    PageResult<List<SalesRefundDetailResp>> getDeptValidRefundDetail(
            @RequestParam @ApiParam("部门ID") Integer deptId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("asr/record")
    @ApiOperation("查询语音识别任务")
    BaseResult<RsAsrTaskRecord> getAsrTaskRecord(@RequestParam @ApiParam("任务ID") String taskId,
                                                 @RequestParam(required = false) @ApiParam("消息ID") String msgId);

    @ApiOperation("查询语音识别任务")
    default BaseResult<RsAsrTaskRecord> getAsrTaskRecord(String taskId) {
        return getAsrTaskRecord(taskId, null);
    }

    ;

    @PostMapping("asr/record")
    @ApiOperation("创建语音识别任务")
    BaseResult<RsAsrTaskRecord> createAsrTaskRecord(@RequestBody @Valid CreateAsrTaskRecordReq req);

    @PatchMapping("asr/record")
    @ApiOperation("更新语音识别任务")
    BaseResult<String> updateAsrTaskRecord(@RequestBody @Valid UpdateAsrTaskRecordReq req);

    @ApiOperation("微信退款请求")
    @GetMapping(value = "wx_pay/refund")
    BaseResult<String> wxPayRefund(@RequestParam @ApiParam("退款订单的pkId") Integer refundId,
                                   @RequestParam @ApiParam("当前财务id") Integer operatorId);

    @ApiOperation("根据退款单ID获取退款流程进度")
    @PostMapping("refunds/process")
    BaseResult<List<RefundFlowRecordResp>> getRsRefundFlowRecordListByRefundId(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("微信退款回调通知处理")
    @PostMapping(value = "wx_pay/notify/refund")
    BaseResult<String> wxRefundOrderNotifyHandle(
            @RequestParam @ApiParam("微信类型") Integer mchType,
            @RequestParam @ApiParam("加密消息体") String xmlData);

    @ApiOperation("微信统一下单")
    @PostMapping("wx_pay/unified")
    BaseResult<WechatPayUnifiedOrderResp> wechatPayUnifiedOrder(@RequestBody @Valid WechatPayUnifiedOrderReq req);

    @ApiOperation("创建微信支付子订单")
    @PostMapping("wx_pay/unified/orders/sub")
    BaseResult<CreateWechatPaySubOrderResp> createWechatPaySubOrder(@RequestBody @Valid CreateWechatPaySubOrderReq req);

    @ApiOperation("查询微信退款状态")
    @PostMapping("payment/wx-pay/refund/status")
    BaseResult<Boolean> checkWechatPayRefundStatus(
            @RequestParam @ApiParam("商户id") Long merchantId,
            @RequestParam @ApiParam("订单号") String orderNumber);

    @ApiOperation("查询退款状态")
    @PostMapping("payment/ali-pay/refund/status")
    BaseResult<Boolean> checkAlipayRefundStatus(
            @RequestParam @ApiParam("商户id") Long merchantId,
            @RequestParam @ApiParam("订单号") String orderNumber);

    @ApiOperation("关闭微信订单")
    @PostMapping("wx_pay/unified/orders/close")
    BaseResult<Boolean> closeWechatPayOrder(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("商户类型") Integer mchType,
            @RequestParam @ApiParam("微信id") Integer wxId,
            @RequestParam @ApiParam("订单号") String orderNumber);

    @ApiOperation("关闭支付宝订单")
    @PostMapping(value = "alipay/order/close")
    BaseResult<Boolean> alipayCloseOrder(
            @RequestParam Integer companyType,
            @RequestParam @ApiParam("商户类型") Integer mchType,
            @RequestParam @ApiParam("订单号") String orderNumber);

    @PostMapping("refunds/order")
    @ApiOperation("生成退款单")
    BaseResult<Tuple2<RsCourseRefundOrder, Integer>> genRefundOrder(@RequestBody @Valid CreateRefundOrderReq req);

    @ApiOperation("查询归属于该销售或商务节点下的所有退款订单")
    @PostMapping("refunds/sales/relate")
    PageResult<List<SalesRelateRefundResp>> getSalesRelateRefundList(
            @RequestBody(required = false) @Valid @ApiParam("销售id列表") List<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("当前商务ID") List<Integer> creatorIds,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("退款订单状态") Integer refundStatus,
            @RequestParam(required = false) @ApiParam("退款单号") String refundNumber,
            @RequestParam(required = false) @ApiParam("主订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("开始创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStartTime,
            @RequestParam(required = false) @ApiParam("结束创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEndTime,
            @RequestParam(required = false) @ApiParam("开始退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundStartTime,
            @RequestParam(required = false) @ApiParam("结束退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundEndTime,
            @RequestParam(required = false) @ApiParam("产品属性") Integer productLevel,
            @RequestParam(required = false) @ApiParam("退款状态集合，不同的页面输入字符串不同，销售-我的相关退款、发起退款、退款订单管理无需输入；退款订单审核输入refundOrderAudit；退款订单确认输入refundOrderConfirm；财务-订单退款输入financeRefundOrder") String refundStatusSet,
            @RequestParam(required = false) @ApiParam("审核人的ID") Integer auditorId,
            @RequestParam(required = false) @ApiParam("确认书状态") Integer readStatus,
            @RequestParam(required = false) @ApiParam("退款合同签字状态") RefundSignStatusEnum refundSignStatus,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

    @ApiOperation("关闭退款订单")
    @PatchMapping("refunds/order/close")
    BaseResult<RsRefundFlowRecord> closeRefundOrder(@RequestParam @ApiParam("当前商务ID") Integer userId,
                                                    @RequestParam @ApiParam("当前退款单ID") Integer refundId);

    @ApiOperation("获取订单详情")
    @GetMapping("refunds/order/id")
    BaseResult<OrderDetailResp> getOrderDetailRespByRefundId(
            @RequestParam(required = false) @ApiParam("退款单ID") Integer refundId,
            @RequestParam(required = false) @ApiParam("订单ID") Integer orderId);

    @ApiOperation("通过订单ID获取已退款金额")
    @GetMapping("refunds/amount")
    BaseResult<Integer> getRefundAmountByOrderId(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @ApiOperation("通过退款单ID获取评论信息")
    @GetMapping("refunds/comment")
    BaseResult<List<RsCourseRefundComment>> getRsCourseRefundCommentByRefundId(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("发评论")
    @PostMapping("refunds/comment")
    BaseResult<String> createCourseRefundComment(
            @RequestBody @Valid CreateCourseRefundCommentReq req);

    @ApiOperation("通过退款单ID获取退款信息")
    @GetMapping("refunds")
    BaseResult<RefundOrderResp> getRefundOrderRespByRefundId(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("获取历史退款记录")
    @GetMapping("refunds/record")
    BaseResult<List<RefundOrderResp>> getRefundOrderRecord(
            @RequestParam @ApiParam("退款单ID") Integer orderId,
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus);

    @GetMapping("customer_service")
    @ApiOperation("获取客户所有服务")
    PageResult<List<CustomerServiceDto>> getCustomerService(
            @RequestParam @ApiParam("客户id") Integer customerId,
            @RequestParam @ApiParam("是否隐藏已过期服务") Boolean hideExpired,
            @RequestParam(required = false) @ApiParam("服务类型") Integer serviceType,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @PostMapping("customer_service/{recordId}/close")
    @ApiOperation("关闭服务")
    BaseResult<String> closeCustomerService(
            @PathVariable @ApiParam("记录id") Integer recordId,
            @RequestParam @ApiParam("操作者id") Integer operatorId,
            @RequestParam(required = false) @ApiParam("关闭原因") String reason);

    @ApiOperation("分配退款单")
    @PostMapping("refunds/distribute")
    BaseResult<String> distributeRefundOrder(
            @RequestParam @ApiParam("退款单ID") Integer refundId,
            @RequestParam @ApiParam("分配对象ID") Integer auditorId,
            @RequestParam @ApiParam("分配人ID") Integer assignId);

    @ApiOperation("再分配退款单")
    @PostMapping("refunds/distribute/again")
    BaseResult<String> distributeRefundOrderAgain(
            @RequestParam @ApiParam("分配新对象ID") Integer newAuditorId,
            @RequestParam @ApiParam("分配旧对象ID") Integer oldAuditorId,
            @RequestParam @ApiParam("分配人ID") Integer assignId);

    @ApiOperation("审核通过退款单")
    @PostMapping("refunds/audit")
    BaseResult<String> auditRefundOrder(
            @RequestParam @ApiParam("退款单ID") Integer refundId,
            @RequestParam @ApiParam("审核人ID") Integer auditorId,
            @RequestParam @ApiParam("是否关闭服务") Boolean isCloseService);

    @ApiOperation("确认退款单")
    @PostMapping("refunds/confirm")
    BaseResult<String> confirmRefundOrder(
            @RequestParam @ApiParam("退款单ID") Integer refundId,
            @RequestParam @ApiParam("确认人ID") Integer confirmId);

    @ApiOperation("开始确认书签字")
    @PostMapping("refunds/start/confirm/sign")
    BaseResult<String> startConfirmSignRefundOrder(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("本次退款计划")
    @GetMapping("refunds/plan")
    BaseResult<List<RefundPlanResp>> getRefundPlan(
            @RequestParam(required = false) @ApiParam("订单ID") Integer orderId,
            @RequestParam(required = false) @ApiParam("退款单ID") Integer refundId,
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus);

    @ApiOperation("退款计划重试")
    @PostMapping("refunds/plan/retry")
    BaseResult<Void> retryRefundPlan(@RequestBody @Valid RetryRefundPlanReq req);

    @ApiOperation("重新签字")
    @PostMapping("refunds/sign/again")
    BaseResult<String> signAgain(
            @RequestParam @ApiParam("退款签字审核ID") Integer refundOrderSignId,
            @RequestParam @ApiParam("签字ID") Integer signId,
            @RequestParam @ApiParam("退款订单ID") Integer refundId);

    @ApiOperation("确认签字合同")
    @PostMapping("refunds/confirm/contract")
    BaseResult<String> auditSign(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("开始签字合同")
    @PostMapping("refunds/start/sign")
    BaseResult<String> startSign(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("商户订单详情")
    @GetMapping("orders/detail")
    BaseResult<List<OrderExtInfoDto>> getOrderExtInfoDto(
            @RequestParam @ApiParam("订单ID") Integer orderId,
            @RequestParam @ApiParam("退款金额") Integer refundAmount);

    @ApiOperation("创建订单流程记录")
    @PostMapping("orders/flow/create")
    BaseResult<OsErrorCode> createOrderFlowRecord(
            @RequestParam Integer orderId,
            @RequestParam Integer userId,
            @RequestParam Integer operateType);

    @ApiOperation("导出合规批量分配记录")
    @GetMapping("orders/flow/batch/assign/record/export")
    List<HgBatchAssignRecordResp> getHgBatchAssignRecord(
            @RequestParam Long batchId);

    @ApiOperation("合规审核批量分配")
    @PostMapping("orders/flow/batch/assign/find")
    BaseResult<BatchAssignResp> getOrderCount(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer autoAuditStatus,
            @RequestParam(required = false) @ApiParam("支付开始时间") Instant startPayTime,
            @RequestParam(required = false) @ApiParam("支付结束时间") Instant endPayTime,
            @RequestParam(required = false) Integer productLevel);

    @ApiOperation("合规审核批量分配-筛选合规待分配订单")
    @PostMapping("orders/flow/batch/assign")
    BaseResult<Void> batchAssign(
            @RequestBody BatchAssignReq batchAssignReq);

    @GetMapping("orders/flow/batch/assign/record/list")
    @ApiOperation("批量分配记录列表")
    PageResult<List<RsOrderAssignBatchRecord>> getRsOrderAssignBatchRecord(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("更新订单签字审核状态")
    @PostMapping("orders/sign/audit/status")
    BaseResult<OsErrorCode> updateSignAuditStatus(
            @RequestParam @ApiParam("订单签字记录ID") Integer orderSignAuditId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus);

    @ApiOperation("创建订单签字审核记录")
    @PostMapping("orders/sign/audit/create")
    BaseResult<OsErrorCode> createSignAudit(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @ApiOperation("根据订单id获取签字信息")
    @PostMapping("orders/getSignInfo")
    BaseResult<List<RsCourseOrderSign>> getSignByOrderId(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @ApiOperation("按订单id获取签字信息")
    @GetMapping("orders/orderSign/orderId")
    BaseResult<RsCourseOrderSign> getSignInfoByOrderId(
            @RequestParam @ApiParam("订单id") Integer orderId);

    @ApiOperation("获取订单和标签关系")
    @PostMapping("orders/getTagRelation")
    BaseResult<List<RsCourseOrderTagRelation>> findTagByOrder(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @ApiOperation("商务审核")
    @PostMapping("orders/sw/audit")
    BaseResult<OrderErrorCode> swAudit(
            @RequestParam @ApiParam("审核人ID") Integer userId,
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @ApiOperation("关闭商务审核")
    @PostMapping("orders/sw/close")
    BaseResult<OrderErrorCode> closeSwAudit(
            @RequestParam @ApiParam("操作人ID") Integer userId,
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @GetMapping("orders/outcall")
    @ApiOperation("通过订单获取外呼信息")
    BaseResult<RsOrderOutCallRelation> getOrderOutCallRelation(@RequestParam Integer orderId);

    @PostMapping("orders/outcall")
    @ApiOperation("创建订单与外呼任务关联")
    BaseResult<RsOrderOutCallRelation> createOrderOutCallRelation(@RequestBody @Valid CreateOrderOutCallRelationReq req);

    @GetMapping("orders/merchants")
    @ApiOperation("获取商户列表")
    BaseResult<List<Merchant>> getMerchantList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer payType);

    default BaseResult<List<Merchant>> getMerchantList(Integer companyType) {
        return getMerchantList(companyType, null);
    }

    @GetMapping("orders/merchants/{id}")
    @ApiOperation("根据MchType获取商户信息")
    BaseResult<Merchant> getMerchant(@PathVariable Integer id);

    @GetMapping("customer_service/all_common")
    @ApiOperation("获取所有主服务")
    BaseResult<List<RsCommonService>> getAllCommonService(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled);

    @GetMapping("customer_service/all_course")
    @ApiOperation("获取所有付费课程服务")
    BaseResult<List<RsCourseSeries>> getAllCourseService(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled);

    @GetMapping("customer_service/course")
    @ApiOperation("课程系列管理")
    PageResult<List<RsCourseSeries>> getRsCourseSeriesPage(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("权限类别") Integer categoryType,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @PostMapping("customer_service/update")
    @ApiOperation("创建/编辑课程系列")
    BaseResult<String> createOrUpdate(
            @RequestParam(required = false) @ApiParam("ID") Integer id,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("编号") String number,
            @RequestParam(required = false) @ApiParam("名称") String name,
            @RequestParam(required = false) @ApiParam("类别") Integer categoryType,
            @RequestParam(required = false) @ApiParam("营销课权限类别") Integer level,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled);

    @GetMapping("customer_service/option")
    @ApiOperation("获取服务项信息")
    BaseResult<List<RsCourseServiceOption>> getServiceOptionList(
            @RequestParam @ApiParam("服务类型") Integer serviceType,
            @RequestParam @ApiParam("是否启用") Set<Integer> serviceIds);

    @PostMapping("customer_service/open")
    @ApiOperation("开通服务项")
    BaseResult<String> openService(
            @RequestParam @ApiParam("服务项id") Integer seriesId,
            @RequestParam @ApiParam("服务类型") Integer serviceType,
            @RequestParam @ApiParam("用户id") Integer customerId,
            @RequestParam @ApiParam("开通时长") Integer periodDay,
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam(required = false) @ApiParam("备注") String remark);

    @GetMapping("customer_service/filter")
    @ApiOperation("根据条件筛选服务")
    BaseResult<List<RsCommonService>> getCommonServiceByFilter(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("服务编号") Collection<String> numberList);

    @ApiOperation("订单签字审核信息")
    @GetMapping("orders/sign/audit_info")
    BaseResult<RsCourseOrderSign> getOrderSignAuditInfo(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @ApiOperation("获取所有订单签字审核信息")
    @GetMapping("orders/sign/all_audit_info")
    BaseResult<List<RsCourseOrderSign>> getAllOrderSignByOrderId(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @PostMapping("orders/transfer/flow")
    @ApiOperation("创建转账单")
    BaseResult<TransferOrderFlow> createTransferOrder(@RequestBody @Valid CreateTransferOrderFlowReq req);

    @PatchMapping("orders/transfer/flow/{id}")
    @ApiOperation("确认转账单")
    BaseResult<Void> confirmTransferOrder(@PathVariable Long id, @RequestBody @Valid UpdateTransferOrderFlowReq req);

    @DeleteMapping("orders/transfer/flow/{id}")
    @ApiOperation("关闭转账单")
    BaseResult<Void> closeTransferOrder(@PathVariable Long id, @RequestBody @Valid UpdateTransferOrderFlowReq req);

    @GetMapping("orders/transfer/flow/{id}")
    @ApiOperation("根据id获取转账单审核流程")
    BaseResult<TransferOrderFlow> getTransferOrderFlow(@PathVariable Long id);

    @GetMapping("orders/transfer/flow/sub/{subOrderId}")
    @ApiOperation("根据子订单ID获取转账单审核流程")
    BaseResult<TransferOrderFlow> getTransferOrderFlow(@PathVariable Integer subOrderId);

    @GetMapping("orders/{orderId}/transfer/flow")
    @ApiOperation("根据订单ID获取转账单审核流程")
    PageResult<List<TransferOrder>> getOrderTransferFlowList(
            @PathVariable Integer orderId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("orders/transfer/flow")
    @ApiOperation("获取转账单审核流程列表")
    PageResult<List<TransferOrderFlow>> getTransferOrderFlowList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer flowStatus,
            @RequestParam(required = false) Integer creatorId,
            @RequestParam(required = false) Instant startTime,
            @RequestParam(required = false) Instant endTime,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("orders/transfer/flow/search")
    @ApiOperation("搜索转账单审核流程列表")
    PageResult<List<TransferOrderFlow>> searchTransferOrderFlow(
            @RequestParam Integer companyType,
            @RequestParam Integer creatorId,
            @RequestParam(required = false) String orderNumber,
            @RequestParam(required = false) String subOrderNumber,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("orders/transfer/flow/record")
    @ApiOperation("获取转账单审核流程记录列表")
    PageResult<List<TransferOrderFlowRecord>> getTransferOrderFlowRecordList(
            @RequestParam Integer subOrderId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @PostMapping("orders/transfer/flow/record")
    @ApiOperation("创建转账单审核流程记录")
    BaseResult<TransferOrderFlowRecord> createTransferOrderFlowRecord(@RequestBody @Valid CreateTransferOrderFlowRecordReq req);

    @GetMapping("id_card/verify")
    @ApiOperation("三要素验证")
    BaseResult<Result> verify(
            @RequestParam @ApiParam("身份证号") String idNumber,
            @RequestParam @ApiParam("姓名") String name,
            @RequestParam @ApiParam("手机号") String mobile);

    @GetMapping("service/introduction")
    @ApiOperation("获取服务简介")
    BaseResult<ServiceIntroduction> getServiceIntroduction(
            @RequestParam Integer serviceType,
            @RequestParam Integer serviceId);

    @PostMapping("service/introduction")
    @ApiOperation("批量获取同类服务简介")
    BaseResult<List<ServiceIntroduction>> getServiceIntroductionList(
            @RequestParam Integer serviceType,
            @RequestBody BatchReq<Integer> req);

    @PostMapping("service/introduction/batch")
    @ApiOperation("批量获取服务简介")
    BaseResult<List<ServiceIntroduction>> getServiceIntroductionList(
            @RequestBody @Valid BatchReq<ServiceKey> req);

    @GetMapping("service/option")
    @ApiOperation("获取服务项")
    BaseResult<RsCourseServiceOption> getServiceOption(
            @RequestParam Integer serviceType,
            @RequestParam Integer serviceId,
            @RequestParam(required = false, defaultValue = "true") Boolean enabled);

    default BaseResult<RsCourseServiceOption> getServiceOption(Integer serviceType, Integer serviceId) {
        return getServiceOption(serviceType, serviceId, true);
    }

    @PostMapping("service/option")
    @ApiOperation("创建服务项")
    BaseResult<RsCourseServiceOption> createServiceOption(@RequestBody @Valid CreateServiceOptionReq req);


    @DeleteMapping("service/option/{id}")
    @ApiOperation("删除服务项")
    BaseResult<Void> deleteServiceOption(@PathVariable Integer id);

    @PostMapping("service/option/list")
    @ApiOperation("批量获取同类服务项")
    BaseResult<List<RsCourseServiceOption>> getServiceOptionList(
            @RequestParam Integer serviceType,
            @RequestBody BatchReq<Integer> req);

    @GetMapping("service/option/batch")
    @ApiOperation("批量获取服务项")
    BaseResult<List<RsCourseServiceOption>> getServiceOptionList(
            @RequestBody @Valid BatchReq<ServiceKey> req);

    @GetMapping("coupons/{id}")
    @ApiOperation("根据id获取优惠券")
    BaseResult<CourseCoupon> getCoupon(@PathVariable Integer id);

    @GetMapping("coupons/users/{userId}")
    @ApiOperation("获取用户优惠券列表")
    BaseResult<List<CourseCoupon>> getUserCouponList(
            @PathVariable Integer userId,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Boolean enabled);

    default BaseResult<List<CourseCoupon>> getUserCouponList(Integer userId) {
        return getUserCouponList(userId, null, true);
    }

    @GetMapping("coupons/users/{userId}/available")
    @ApiOperation("获取用户可用优惠券")
    PageResult<List<CourseCoupon>> getUserAvailableCouponList(
            @RequestParam Integer companyType,
            @PathVariable Integer userId,
            @RequestParam(required = false) Integer skuId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("coupons/plan/{id}")
    BaseResult<CourseCouponPlan> getCouponPlan(@PathVariable Integer id);

    @GetMapping("coupons/plan/getBySalesIdOrDeptIds")
    @ApiOperation("获取销售和部门优惠券方案")
    PageResult<List<CourseCouponPlan>> getCouponPlanBySalesIdOrDeptIds(
            @RequestParam Integer salesId,
            @RequestParam List<Integer> deptIds,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("coupons/templates/{id}")
    @ApiOperation("获取优惠券模板")
    BaseResult<CourseCouponTemplate> getCouponTemplate(@PathVariable Integer id);

    @PostMapping("coupons/templates/batch")
    @ApiOperation("批量获取优惠券模板")
    BaseResult<List<CourseCouponTemplate>> getCouponTemplateList(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("coupons/templates/batchCouponTemplate")
    @ApiOperation("批量获取优惠券模板")
    BaseResult<List<CourseCouponTemplate>> batchCouponTemplate(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("coupons/templates/sku/{skuId}")
    @ApiOperation("获取sku所有可用的优惠券模板")
    BaseResult<List<CourseCouponTemplate>> getSkuCouponTemplateList(
            @RequestParam Integer companyType,
            @PathVariable Integer skuId,
            @RequestParam(required = false) Boolean enabled);

    @PostMapping("coupons/update/{id}")
    @ApiOperation("根据id更新优惠券")
    BaseResult<Void> updateCoupon(
            @PathVariable Integer id,
            @RequestBody UpdateCouponReq req);

    @PostMapping("sku/getList")
    @ApiOperation("获取sku列表")
    PageResult<List<RsSku>> getSkuList(
            @RequestBody @Validated SkuManageReq req);

    @ApiOperation("获取子订单列表")
    @GetMapping("orders/sub/list")
    PageResult<List<SubOrderDto>> getSubOrderByFilter(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer orderId,
            @RequestParam(required = false) Integer orderStatus,
            @RequestParam(required = false) List<Integer> salesIds,
            @RequestParam(required = false) Boolean isPromotion,
            @RequestParam(required = false) Instant startCreateTime,
            @RequestParam(required = false) Instant endCreateTime,
            @RequestParam(required = false) Instant startPayTime,
            @RequestParam(required = false) Instant endPayTime,
            @RequestParam(required = false) String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("通过主订单id获取子订单列表")
    @PostMapping("orders/batch/sub")
    BaseResult<List<CourseSubOrder>> getSubOrderList(
            @RequestBody @Valid BatchReq<Integer> orderIds,
            @RequestParam(value = "subOrderStatus", required = false) Integer subOrderStatus
    );

    @PostMapping("sku/createSku")
    @ApiOperation("后台创建sku")
    BaseResult<RsSku> createSku(@RequestBody CreateSkuAndSkuMaterialReq req);

    @PostMapping("sku/createPromotionSku")
    @ApiOperation("创建广告sku")
    BaseResult<RsSku> createPromotionSku(@RequestBody CreatePromotionSkuReq req);

    @PostMapping("sku/updatePromotionSku")
    @ApiOperation("更新广告sku")
    BaseResult<Boolean> updatePromotionSku(@RequestBody UpdatePromotionSkuReq req);

    @GetMapping("sku/getSkuChangeRecord")
    @ApiOperation("获取sku操作记录列表")
    BaseResult<List<SkuChangeRecord>> getSkuChangeRecord(
            @RequestParam @ApiParam("skuId") Integer skuId);

    @PostMapping("sku/updateSku")
    @ApiOperation("更新sku")
    BaseResult<Boolean> updateSku(@RequestBody UpdateSkuReq req);

    @GetMapping("sku/getSkuAndMaterial")
    @ApiOperation("通过ID获取sku详情")
    BaseResult<SkuAndMaterialResp> getSkuAndMaterial(@RequestParam @ApiParam("skuId") Integer skuId);

    @PostMapping("sku/setUserVisible")
    @ApiOperation("设置员工可见范围")
    BaseResult<Boolean> setUserVisible(@RequestBody SkuUserVisibleReq req);

    @PostMapping("sku/setDeptVisible")
    @ApiOperation("设置部门可见范围")
    BaseResult<Boolean> setDeptVisible(@RequestBody SkuDeptVisibleReq req);

    @GetMapping("sku/onSale")
    @ApiOperation("sku上架")
    BaseResult<Boolean> onSale(
            @RequestParam @ApiParam("skuId") Integer skuId,
            @RequestParam @ApiParam("operatorId") Integer operatorId);

    @GetMapping("sku/offSale")
    @ApiOperation("sku下架")
    BaseResult<Boolean> offSale(
            @RequestParam @ApiParam("skuId") Integer skuId,
            @RequestParam @ApiParam("operatorId") Integer operatorId);

    @PostMapping("sku/service/config")
    @ApiOperation("配置服务期")
    BaseResult<Boolean> configService(@RequestBody SkuServiceReq req);

    @ApiOperation("查询主订单列表")
    @GetMapping("orders/list")
    PageResult<List<OrderInfoDto>> getOrderByFilter(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer orderStatus,
            @RequestParam(required = false) Integer payType,
            @RequestParam(required = false) List<Integer> salesIds,
            @RequestParam(required = false) Boolean isPromotion,
            @RequestParam(required = false) Instant startCreateTime,
            @RequestParam(required = false) Instant endCreateTime,
            @RequestParam(required = false) Instant startPayTime,
            @RequestParam(required = false) Instant endPayTime,
            @RequestParam(required = false) String searchContent,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Integer productLevel,
            @RequestParam(required = false) @ApiParam("sku列表") List<Integer> skuList,
            @RequestParam(required = false) @ApiParam("近60天是否添加高级助教") Boolean isMarkWxUser,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("查询销售主订单列表")
    @GetMapping("orders/list/sales")
    PageResult<List<RsCourseOrder>> getOrderBySales(
            @RequestParam Integer companyType,
            @RequestBody @Valid @ApiParam("salesIds") BatchReq<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("是否有相关退款") Boolean isRefund,
            @RequestParam(required = false) Instant startPayTime,
            @RequestParam(required = false) Instant endPayTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("orders/hg/auditManage")
    @ApiOperation("订单合规审核管理新")
    PageResult<List<OrderInfoDto>> getOrderHgAuditManage(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer auditorId,
            @RequestParam(required = false) List<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startPayTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endPayTime,
            @RequestParam(required = false) Integer feedbackStatus,
            @RequestParam(required = false) Integer productLevel,
            @RequestParam(required = false) List<Integer> auditStatus,
            @RequestParam(required = false) @ApiParam("机审状态") Integer autoAuditStatus,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("orders/hg/auto-audit-info")
    @ApiOperation("获取自动审核信息")
    BaseResult<List<RsOrderAutoAuditInfo>> getOrderHgAutoAuditInfo(
            @RequestParam @ApiParam("订单id") Integer orderId);

    @GetMapping("sku/visible/userList")
    @ApiOperation("获取可见范围员工列表")
    BaseResult<List<RsSkuUserRelation>> getUserList(
            @RequestParam @ApiParam("skuId") Integer skuId);

    @GetMapping("sku/visible/deptList")
    @ApiOperation("获取可见范围部门列表")
    BaseResult<List<SkuDeptRelation>> getDeptList(
            @RequestParam @ApiParam("skuId") Integer skuId);

    @ApiOperation("生成签字pdf")
    @PostMapping(value = "pdf/generateSignPdf")
    BaseResult<String> generateSignPdf(
            @RequestParam @ApiParam("订单id") Integer orderId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @ApiOperation("对公转账标记")
    @PostMapping("wx_pay/transferRefund")
    BaseResult<String> transferRefund(@RequestBody @Valid RefundMarkReq req);

    @ApiOperation("拉取服务对应用户")
    @PostMapping("orders/course/service")
    Set<Integer> filterService(@RequestBody @Valid FilterOrderServiceReq req);

    @GetMapping("researchReport/getArticleSeries")
    @ApiOperation("获取研报栏目列表")
    PageResult<List<ArticleSeriesResp>> getArticleSeries(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("产品栏目类型") Integer seriesType,
            @RequestParam @ApiParam("栏目类型") Integer type,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("article/series/{id}")
    @ApiOperation("获取文章栏目")
    BaseResult<ArticleSeries> getArticleSeries(@PathVariable Integer id);

    @GetMapping("article/series/number/{number}")
    @ApiOperation("获取文章栏目")
    BaseResult<ArticleSeries> getArticleSeries(@RequestParam Integer companyType, @PathVariable String number);

    @GetMapping("researchReport/filterArticleSeries")
    @ApiOperation("筛选栏目列表")
    BaseResult<List<ArticleSeries>> filterArticleSeries(
            @RequestParam(required = false) @ApiParam("栏目Id列表") Collection<Integer> ids,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("TeamId") Integer teamId,
            @RequestParam(required = false) @ApiParam("栏目类型") Integer seriesType);

    @PostMapping("researchReport/creatOrUpdateArticleSeries")
    @ApiOperation("创建/编辑研报栏目")
    BaseResult creatOrUpdateArticleSeries(@Validated @RequestBody ArticleSeriesReq req);

    @PostMapping("researchReport/addAnalyst")
    @ApiOperation("为栏目添加投顾老师")
    BaseResult addResearchReportAnalyst(@Validated @RequestBody AnalystReq req);

    @PostMapping("researchReport/deleteAnalystRelation")
    @ApiOperation("删除投顾老师关系")
    BaseResult deleteResearchReportAnalystRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids);

    @GetMapping("researchReport/getAnalystRelation")
    @ApiOperation("获取栏目对映老师关系")
    BaseResult<List<ArticleSeriesAnalystRelation>> getResearchReportAnalystRelations(
            @RequestParam @ApiParam("栏目ID") Integer seriesId);

    @PostMapping("researchReport/addManager")
    @ApiOperation("为栏目配置处理人")
    BaseResult addResearchReportManager(@Validated @RequestBody ManagerReq req);

    @GetMapping("researchReport/getManagerRelation")
    @ApiOperation("获取栏目对映处理人关系")
    BaseResult<List<ArticleSeriesOperatorRelation>> getResearchReportManagerRelations(
            @RequestParam(required = false) @ApiParam("栏目ID") Integer seriesId,
            @RequestParam(required = false) @ApiParam("处理人ID") Integer operatorId);

    @PostMapping("researchReport/deleteManagerRelation")
    @ApiOperation("删除栏目处理人关系")
    BaseResult deleteResearchReportManagerRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids);

    @GetMapping("researchReport/getArticleTag")
    @ApiOperation("获取标签列表")
    BaseResult<List<ArticleTag>> getArticleTag(
            @RequestParam @ApiParam("栏目Id") Integer seriesId,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enable);

    @PostMapping("researchReport/creatOrUpdateArticleTag")
    @ApiOperation("创建/编辑栏目标签")
    BaseResult<ArticleTagResp> creatOrUpdateArticleTag(
            @RequestParam(required = false) @ApiParam("标签Id") Long tagId,
            @RequestParam @ApiParam("栏目Id") Integer seriesId,
            @RequestParam @ApiParam("标签名称") String tagName);

    @PostMapping("researchReport/disabledArticleTag")
    @ApiOperation("禁用栏目标签")
    BaseResult disabledArticleTag(
            @RequestParam(required = false) @ApiParam("标签Id") Long tagId,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enable);

    @GetMapping("vip/properties")
    @ApiOperation("获取vip配置")
    BaseResult<VipProperties> getVipProperties(@RequestParam Integer companyType);

    @GetMapping("vip/columns")
    @ApiOperation("获取自定义栏目配置")
    BaseResult<List<VipColumn>> getVipColumnList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) String type);

    @GetMapping("vip/packages")
    @ApiOperation("获取vip服务包列表")
    BaseResult<List<VipPackage>> getVipPackageList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer level);

    @GetMapping("vip/packages/numbers")
    @ApiOperation("获取vip服务包列表")
    BaseResult<List<VipPackage>> getVipPackageByNumbers(
            @RequestParam Integer companyType,
            @RequestParam Collection<String> numbers);

    @GetMapping("vip/packages/{number}")
    @ApiOperation("获取vip服务包")
    BaseResult<VipPackage> getVipPackage(@RequestParam Integer companyType, @PathVariable String number);

    @GetMapping("vip/packages/{number}/config")
    @ApiOperation("获取vip服务包的配置")
    BaseResult<VipPackageServiceConfig> getVipPackageConfig(
            @RequestParam Integer companyType,
            @PathVariable String number);

    @GetMapping("vip/packages/{number}/services")
    @ApiOperation("获取用户vip服务包的首页")
    BaseResult<UserVipPackageInfo> getUserVipPackage(
            @RequestParam Integer companyType,
            @RequestParam Integer userId,
            @PathVariable String number);

    default BaseResult<List<VipPackage>> getVipPackageList(Integer companyType) {
        return getVipPackageList(companyType, null);
    }

    @GetMapping("vip/packages/{number}/services/introduction")
    @ApiOperation("获取vip服务包的服务简介")
    BaseResult<List<ServiceIntroduction>> getVipPackageServiceIntroductionList(
            @RequestParam Integer companyType,
            @PathVariable String number,
            @RequestParam String type,
            @RequestParam Boolean isFree);

    @GetMapping("vip/packages/columns")
    @ApiOperation("获取用户已有的栏目编号")
    BaseResult<List<ServiceIntroduction>> getUserVipSubscriptionColumnList(
            @RequestParam Integer companyType,
            @RequestParam Integer userId,
            @RequestParam String type);

    @GetMapping("vip/packages/subscriptions")
    @ApiOperation("获取用户已有的服务")
    BaseResult<List<UserSubscriptionServiceItem>> getUserVipSubscriptionServiceList(
            @RequestParam Integer companyType,
            @RequestParam Integer userId,
            @RequestParam String type);

    @GetMapping("vip/packages/free")
    @ApiOperation("获取免费服务")
    BaseResult<List<UserSubscriptionServiceItem>> getFreeSubscriptionServiceList(
            @RequestParam Integer companyType,
            @RequestParam String type);

    @GetMapping("vip/packages/noPermission")
    @ApiOperation("获取服务包不可见配置")
    BaseResult<Map<String, Map<String, Integer>>> getVipPackageNoPermissionConfig(
            @RequestParam Integer companyType);

    @GetMapping("vip/packages/user/{userId}/config")
    @ApiOperation("获取用户vip服务包的配置")
    BaseResult<List<VipPackage>> getUserVipPackageConfig(
            @RequestParam Integer companyType,
            @PathVariable Integer userId);

    @GetMapping("vip/packages/banners")
    @ApiOperation("获取vip服务包的banner")
    BaseResult<List<VipPackageBanner>> getVipPackageBannerList(
            @RequestParam Integer companyType,
            @RequestParam String number);

    @GetMapping("sku/{skuId}/vip/subscriptions")
    @ApiOperation("获取sku配置的服务项")
    BaseResult<List<SkuVipSubscription>> getSkuVipSubscriptionList(@PathVariable Integer skuId);

    @GetMapping("sku/{skuId}/vip/packages")
    @ApiOperation("获取sku配置的服务包")
    BaseResult<List<SkuVipPackage>> getSkuVipPackageList(
            @RequestParam Integer companyType,
            @PathVariable Integer skuId);

    @PostMapping("sku/vip/packages")
    @ApiOperation("sku配置服务包")
    BaseResult<Void> createOrUpdateSkuVipSubscription(@RequestBody @Valid CreateOrUpdateSkuVipSubscriptionReq req);

    @PostMapping("sku/strategy/sub")
    @ApiOperation("策略sku配置服务项")
    BaseResult<Void> createOrUpdateStrategySkuSub(@RequestBody @Valid CreateOrUpdateSkuStrategySubReq req);

    @GetMapping("vip/packages/number/chatroom")
    @ApiOperation("获取vip服务包编号")
    BaseResult<Set<String>> getChatVipPackageNumber(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("聊天室编号") String chatRoomNumber);

    @PostMapping("vip/subscriptions/manual")
    @ApiOperation("人工开通服务包")
    BaseResult<VipSubscription> manualCreateOrRenewVipSubscription(@RequestBody @Valid ManualCreateOrRenewVipSubscriptionReq req);

    @DeleteMapping("vip/subscriptions/manual")
    @ApiOperation("人工关闭服务包")
    BaseResult<Void> manualCancelVipSubscription(@RequestBody @Valid ManualCancelVipSubscriptionReq req);

    @GetMapping("vip/subscriptions")
    @ApiOperation("获取用户的服务")
    BaseResult<List<VipSubscription>> getUserVipSubscriptionList(
            @RequestParam Integer userId,
            @RequestParam(required = false) Boolean isExpired);

    default BaseResult<List<VipSubscription>> getUserVipSubscriptionList(Integer userId) {
        return getUserVipSubscriptionList(userId, false);
    }

    @GetMapping("vip/subscriptions/number/{number}")
    @ApiOperation("获取用户指定编号的服务")
    BaseResult<VipSubscription> getUserVipSubscription(
            @RequestParam Integer userId,
            @PathVariable String number,
            @RequestParam(required = false, defaultValue = "false") Boolean isExpired);

    default BaseResult<VipSubscription> getUserVipSubscription(Integer userId, String number) {
        return getUserVipSubscription(userId, number, false);
    }

    @GetMapping("vip/subscriptions/level/{level}")
    @ApiOperation("获取用户指定级别未过期服务")
    BaseResult<VipSubscription> getUserVipSubscription(
            @RequestParam Integer userId,
            @PathVariable Integer level);

    @PostMapping("vip/subscriptions/level/{level}/batch")
    @ApiOperation("批量获取用户指定级别服务")
    BaseResult<List<VipSubscription>> batchUserVipSubscription(
            @PathVariable Integer level,
            @RequestBody @Valid BatchReq<Integer> req,
            @RequestParam(required = false) Boolean isExpired);

    @GetMapping("vip/subscriptions/record/list")
    @ApiOperation("获取vip产品开通记录列表")
    PageResult<List<VipSubscriptionRecord>> getVipSubscriptionRecordList(
            @RequestParam(required = false) @ApiParam("用户编号") Collection<Integer> userId,
            @RequestParam(required = false) @ApiParam("服务包编号") String number,
            @RequestParam(required = false) @ApiParam("操作类型") Integer operateType,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("vip/subscriptions/range")
    @ApiOperation("通过id范围批量获取用户的服务")
    BaseResult<List<VipSubscription>> getRangeVipSubscription(
            @RequestParam Long startId,
            @RequestParam Long endId);

    @GetMapping("vip/subscriptions/first")
    @ApiOperation("获取服务的第一条记录")
    BaseResult<VipSubscription> getFirstVipSubscription();

    @GetMapping("vip/subscriptions/last")
    @ApiOperation("获取服务的最后一条记录")
    BaseResult<VipSubscription> getLastVipSubscription();

    @PostMapping("vip/subscriptions/batch/users")
    @ApiOperation("通过用户id范围批量获取用户的服务")
    BaseResult<List<VipSubscription>> getUserVipSubscriptionList(
            @RequestBody @Valid BatchReq<Integer> req,
            @RequestParam(required = false) Boolean enabled);

    @GetMapping("vip/packages/number")
    @ApiOperation("获取vip服务包编号")
    BaseResult<Set<String>> getVipPackageNumber(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道编号") String portfolioNumber);

    @GetMapping("article/course")
    @ApiOperation("研报管理-栏目文章列表")
    PageResult<List<ArticleCourseDto>> getArticleCourseList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道列表") Collection<Integer> seriesIdList,
            @RequestParam(required = false) @ApiParam("频道") Integer seriesId,
            @RequestParam(required = false) @ApiParam("筛选") String searchText,
            @RequestParam(required = false) @ApiParam("发布状态") Integer status,
            @RequestParam(required = false) @ApiParam("排序类型 1 创建时间  2 发布时间") Integer orderType,
            @RequestParam(required = false) @ApiParam("时间类型 1 创建时间  2 发布时间") Integer timeType,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("是否逆序") Boolean isDesc,
            @RequestParam(required = false) @ApiParam("是否置顶") Boolean isTop,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("article/course/series")
    @ApiOperation("前端按栏目，标签获取文章列表")
    BaseResult<List<FullArticleCourse>> getArticleCourseList(
            @RequestParam Integer companyType,
            @RequestParam List<Integer> seriesIds,
            @RequestParam @ApiParam("是否优先展示置顶") Boolean orderByTop,
            @RequestParam(required = false) Long tagId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("article/course/list")
    @ApiOperation("获取营销栏目文章列表")
    PageResult<List<RsArticleCourse>> getArticleCourseList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("栏目编号") String seriesNumber,
            @RequestParam(required = false) @ApiParam("每页条数") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size);

    @GetMapping("article/course/detail")
    @ApiOperation("获取营销栏目文章详情")
    BaseResult<RsArticleCourse> getArticleCourseDetail(
            @RequestParam @ApiParam("文章id") Long id);

    @GetMapping("article/course/next")
    @ApiOperation("获取营销栏目当前文章的下一篇文章")
    BaseResult<RsArticleCourse> getNextArticleCourse(
            @RequestParam @ApiParam("栏目id") Integer seriesId,
            @RequestParam @ApiParam("发布时间") Instant releaseTime,
            @RequestParam(required = false) @ApiParam("文章状态") List<Integer> articleStatuses);

    @GetMapping("article/course/previous")
    @ApiOperation("获取营销栏目当前文章的上一篇文章")
    BaseResult<RsArticleCourse> getPreviousArticleCourse(
            @RequestParam @ApiParam("栏目id") Integer seriesId,
            @RequestParam @ApiParam("发布时间") Instant releaseTime,
            @RequestParam(required = false) @ApiParam("文章状态") List<Integer> articleStatuses);

    @GetMapping("article/course/series/top")
    @ApiOperation("前端按栏目获取精彩回顾")
    BaseResult<List<FullArticleCourse>> getArticleCourseTopList(
            @RequestParam Integer companyType,
            @RequestParam List<Integer> seriesIds,
            @RequestParam(required = false, defaultValue = "4") Integer size);

    @GetMapping("article/course/stock")
    @ApiOperation("前端按股票代码获取文章列表")
    BaseResult<List<FullArticleCourse>> getCodeArticleCourseList(
            @RequestParam Integer companyType,
            @RequestParam List<Integer> seriesIds,
            @RequestParam String code,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @PostMapping("article/course/series/batch")
    @ApiOperation("根据栏目编号批量获取文章列表")
    BaseResult<List<FullArticleCourse>> getArticleCourseList(
            @RequestParam Integer companyType,
            @RequestBody @Validated BatchReq<String> req,
            @RequestParam(required = false, defaultValue = "2") Integer perSize);

    @GetMapping("article/course/tags")
    @ApiOperation("获取文章标签列表")
    BaseResult<List<ArticleTag>> getArticleTagList(@RequestParam Integer seriesId);

    @PostMapping("article/course")
    @ApiOperation("研报管理-栏目文章 新增")
    BaseResult<String> addArticleCourse(@Validated @RequestBody EditArticleCourseReq req);

    @PostMapping("article/course/{articleId}")
    @ApiOperation("研报管理-栏目文章 修改")
    BaseResult<String> updateArticleCourse(@PathVariable Long articleId, @Validated @RequestBody EditArticleCourseReq req);

    @DeleteMapping("article/course/{articleId}")
    @ApiOperation("研报管理-栏目文章 删除")
    BaseResult<String> deleteArticleCourse(@PathVariable Long articleId);

    @GetMapping("article/course/setTop")
    @ApiOperation("研报管理-栏目文章 置顶")
    BaseResult<String> setArticleCourseTop(@RequestParam @ApiParam("文章编号") Long articleId,
                                           @RequestParam @ApiParam("是否置顶") Boolean isTop);

    @GetMapping("article/course/number/{number}")
    @ApiOperation("前端获取文章详情")
    BaseResult<ArticleCourseDetailDto> getArticleCourseDetail(@PathVariable String number);

    @GetMapping("article/course/relation-article")
    @ApiOperation("获取股票相关文章")
    BaseResult<List<RsArticleStockRelation>> getStockRelationArticleList(@RequestParam @ApiParam("公司") Integer companyType,
                                                                         @RequestParam @ApiParam("系列类型") Integer seriesType,
                                                                         @RequestParam @ApiParam("系列类型") Integer seriesId,
                                                                         @RequestBody @ApiParam("股票列表") Collection<String> codeList);


    @GetMapping("coupons/gain")
    @ApiOperation("领取优惠券活动方案")
    BaseResult<CourseCoupon> gainCoupon(@RequestParam String planNumber,
                                        @RequestParam(required = false) Integer salesId,
                                        @RequestParam Integer userId,
                                        @RequestParam Integer companyType);

    @GetMapping("coupons/query")
    @ApiOperation("查询优惠券")
    BaseResult<List<CourseCoupon>> queryCoupon(
            @RequestParam String planNumber,
            @RequestParam Integer companyType,
            @RequestParam Integer userId);

    @GetMapping("coupons/plan/find/all/activity")
    @ApiOperation("获取所有活动名称")
    BaseResult<List<CouponPlanResp>> getAllCouPonPlan(@RequestParam @ApiParam Integer companyType,
                                                      @RequestParam(required = false) @ApiParam Integer salesId);

    @GetMapping("coupons/list")
    @ApiOperation("优惠券-领取明细总览")
    PageResult<List<CouponResp>> getCouponRespList(@RequestParam(required = false) Integer companyType,
                                                   @RequestParam(required = false) Integer category,
                                                   @RequestParam(required = false) Integer salesId,
                                                   @RequestParam(required = false) Integer planId,
                                                   @RequestParam(required = false) @ApiParam("创建时间开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStartTime,
                                                   @RequestParam(required = false) @ApiParam("创建时间结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEndTime,
                                                   @RequestParam(required = false) Integer userId,
                                                   @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
                                                   @RequestParam(required = false, defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("coupons/plan/number")
    @ApiOperation("查询优惠券")
    BaseResult<CourseCouponPlan> getCouponPlan(@RequestParam String number, @RequestParam Integer companyType);

    @GetMapping("coupons/plan/getCouponsPlanList")
    @ApiOperation("优惠券活动列表")
    PageResult<List<CourseCouponPlan>> getCouponsPlanList(
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Integer status,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size);

    @GetMapping("coupons/plan/batchCouponsPlanSkuRelationByPlanId")
    @ApiOperation("获取优惠券活动与SkuId关系")
    BaseResult<List<CourseCouponPlanSkuRelation>> batchCouponsPlanSkuRelationByPlanId(@RequestBody BatchReq<Integer> req);

    @GetMapping("coupons/plan/skuList/{planId}")
    @ApiOperation("根据活动id获取sku列表")
    BaseResult<List<RsSku>> getSkuByPlanId(@PathVariable Integer planId);

    @PostMapping("coupons/plan/createCouponPlan")
    @ApiOperation("创建优惠券活动")
    BaseResult<String> createCouponPlan(@RequestBody @Valid CreateCouponPlanReq req);

    @PostMapping("coupons/plan/updateCouponPlan")
    @ApiOperation("更新优惠券活动")
    BaseResult<String> updateCouponPlan(@RequestBody @Valid UpdateCouponPlanReq req);

    @GetMapping("coupons/plan/onCouponPlan")
    @ApiOperation("优惠券活动上架")
    BaseResult<Boolean> onCouponPlan(@RequestParam @ApiParam("活动Id") Integer planId);

    @GetMapping("coupons/plan/offCouponPlan")
    @ApiOperation("优惠券活动下架")
    BaseResult<Boolean> offCouponPlan(@RequestParam @ApiParam("活动Id") Integer planId);

    @GetMapping("coupons/plan/setCouponPlanScope")
    @ApiOperation("设置优惠券活动范围")
    BaseResult<String> setCouponPlanScope(@RequestParam @ApiParam("活动Id") Integer planId,
                                          @RequestParam @ApiParam("销售Id列表") List<Integer> saleIds);

    @PostMapping("coupons/plan/setCouponPlanDeptScope")
    @ApiOperation("设置优惠券活动部门范围")
    BaseResult<String> setCouponPlanDeptScope(@RequestParam @ApiParam("活动Id") Integer planId,
                                              @RequestParam @ApiParam("部门Id列表") List<Integer> deptIds);

    @GetMapping("coupons/plan/getCouponPlanSalesRelation")
    @ApiOperation("获取优惠券活动对应的销售信息")
    BaseResult<List<CourseCouponSalesRelation>> getCouponPlanSalesRelation(@RequestParam @ApiParam("活动Id") Integer planId);

    @GetMapping("coupons/plan/getCouponPlanDeptRelation")
    @ApiOperation("获取优惠券活动对应的部门信息")
    BaseResult<List<CourseCouponDeptRelation>> getCouponPlanDeptRelation(@RequestParam(required = false) @ApiParam("活动Id") Integer planId,
                                                                         @RequestParam(required = false) @ApiParam("部门Id列表") List<Integer> deptIds);

    @GetMapping("coupons/plan/manualIssuedCoupon")
    @ApiOperation("手动发放优惠券")
    BaseResult<List<CourseCoupon>> manualIssuedCoupon(@RequestParam @ApiParam("活动Id") Integer planId,
                                                      @RequestParam @ApiParam("用户Id列表") List<Integer> userIds,
                                                      @RequestParam @ApiParam("公司类型") Integer companyType,
                                                      @RequestParam @ApiParam("销售Id") Integer salesId);

    @ApiOperation("查询已成交的订单")
    @GetMapping("orders/getDealtOrder")
    RsCourseOrder getDealtOrder(
            @RequestParam @ApiParam("微信ID") Integer wxId);

    @ApiOperation("查询订单扩展信息")
    @GetMapping("orders/getOrderExt")
    RsCourseOrderExt getOrderExt(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    /**
     * 查询用户五分钟内的相同挡位的订单
     *
     * @param userId 用户id
     * @param skuId  skuId
     * @return 订单
     */
    @ApiOperation("查询用户五分钟内的相同挡位的订单")
    @GetMapping("orders/getUserFinishedOrder")
    BaseResult<RsCourseOrder> getUserFinishedOrder(
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("SkuID") Integer skuId);

    @ApiOperation("按id范围获取订单列表")
    @PostMapping("orders/range")
    BaseResult<List<FullOrder>> getRangeFullOrderList(
            @RequestParam Integer startId,
            @RequestParam Integer endId,
            @RequestParam(required = false) Integer companyType,
            @RequestParam(required = false) Integer orderStatus);

    @ApiOperation("获取第一条订单")
    @GetMapping("orders/first")
    BaseResult<RsCourseOrder> getFirstOrder();

    @ApiOperation("获取最后一条订单")
    @GetMapping("orders/last")
    BaseResult<RsCourseOrder> getLastOrder();

    @ApiOperation("按用户id批量获取订单")
    @PostMapping("orders/batch/users")
    BaseResult<List<FullOrder>> getUserOrderList(
            @RequestBody @Valid BatchReq<Integer> req,
            @RequestParam(required = false) Integer orderStatus,
            @RequestParam(required = false) Boolean isPromotion);

    @ApiOperation("获取用户二三四档已完成订单")
    @GetMapping("orders/finish")
    BaseResult<List<FeedbackResp>> getFullOrderListByUserId(
            @RequestParam Integer companyType,
            @RequestParam Integer userId);

    @PostMapping("vip/subscriptions/unclaimedList")
    @ApiOperation("获取未认领客户管理")
    PageResult<List<VipSubscription>> getUnclaimedList(
            @RequestBody @Validated UnclaimedCustomerReq req);

    @GetMapping({"vip/subscriptions/{userId}"})
    @ApiOperation("获取用户的服务")
    BaseResult<List<VipSubscription>> getUserVipSubscription(
            @PathVariable Integer userId);

    @PostMapping("vip/subscriptions/batch")
    @ApiOperation("批量获取用户的服务")
    BaseResult<List<VipSubscription>> batchVipSubscription(
            @RequestParam Set<Integer> userSet,
            @RequestParam(required = false) @ApiParam("是否过期") Boolean isExpired);

    @GetMapping("vip/subscriptions/page")
    @ApiOperation("分页获取用户的服务")
    PageResult<List<VipSubscription>> pageVipSubscription(
            @RequestParam Set<Integer> userSet,
            @RequestParam(required = false) @ApiParam("是否过期") Boolean isExpired,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("vip/subscriptions/{level}/getVipSubscriptionCount")
    @ApiOperation("获取服务包开通用户数量")
    BaseResult<VipSubscriptionCountResp> getVipSubscriptionCount(@PathVariable @ApiParam("服务包类别") Integer level);

    @GetMapping("vip/subscriptions/pageFilterUser")
    @ApiOperation("分页筛选开通服务用户id列表")
    PageResult<List<Integer>> pageFilterUser(@RequestParam(required = false) @ApiParam("用户编号") Collection<Integer> userId,
                                             @RequestParam(required = false) @ApiParam("服务包编号") String number,
                                             @RequestParam(required = false) @ApiParam("是否到期") Boolean isExpired,
                                             @RequestParam(required = false) Integer current,
                                             @RequestParam(required = false) Integer size);

    @GetMapping("vip/subscriptions/pageFilterVipSubscription")
    @ApiOperation("分页筛选开通服务列表")
    PageResult<List<VipSubscription>> pageFilterVipSubscription(@RequestParam(required = false) @ApiParam("用户编号") Integer userId,
                                                                @RequestParam @ApiParam("服务包编号") Collection<String> numbers,
                                                                @RequestParam(required = false) @ApiParam("是否到期") Boolean isExpired,
                                                                @RequestParam(required = false) Integer current,
                                                                @RequestParam(required = false) Integer size);

    @PostMapping("vip/subscriptions/filterUserByNumber")
    @ApiOperation("分页筛选开通服务用户id列表")
    BaseResult<List<Integer>> filterUserByNumber(
            @RequestBody FilterSubUserReq req);

    @ApiOperation("通过sku等级获取订单")
    @GetMapping("orders/by/sku/level")
    BaseResult<List<RsCourseOrder>> getRsCourseOrderListBySkuLevel(
            @RequestParam @ApiParam("前端后端") Integer category,
            @RequestParam @ApiParam("flagTime") Instant flagTime);

    /**
     * 获取订单及sku信息
     *
     * @param companyType 公司类型
     * @param userId      用户id
     * @param orderStatus 订单状态
     * @param isPromotion 是否广告单
     * @param current     页码
     * @param size        数量
     * @return 订单及sku信息
     */
    @ApiOperation("获取订单sku列表")
    @GetMapping("orders/orderSku")
    PageResult<List<OrderSkuInfo>> getOrderSkuPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("是否广告单") Boolean isPromotion,
            @RequestParam(required = false) @ApiParam("sku产品类型") Integer productLevel,
            @RequestParam(required = false) @ApiParam("订单支付开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startDate,
            @RequestParam(required = false) @ApiParam("订单支付结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endDate,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size);

    default PageResult<List<OrderSkuInfo>> getOrderSkuPage(Integer companyType, Integer userId, Integer orderStatus, Boolean isPromotion, Integer current, Integer size) {
        return getOrderSkuPage(companyType, userId, orderStatus, isPromotion, null, null, null, current, size);
    }

    @ApiOperation("获取订单sku列表不分页")
    @GetMapping("orders/orderSkuList")
    List<OrderSkuInfo> getOrderSkuList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("是否广告单") Boolean isPromotion);

    /**
     * 按订单id批量获取签字信息
     *
     * @param req 订单id
     * @return 签字信息
     */
    @ApiOperation("按订单id批量获取签字信息")
    @PostMapping("orders/batch/orders")
    BaseResult<List<RsCourseOrderSign>> getSignList(
            @RequestBody @Valid BatchReq<Integer> req);

    /**
     * 根据sku的id获取sku的封面
     *
     * @param skuId id
     * @return 封面
     */
    @GetMapping("sku/material/cover/{skuId}")
    @ApiOperation("获取SKU的封面")
    BaseResult<SkuCoverResp> getSkuCover(@PathVariable Integer skuId);

    @GetMapping("coupons/list/{userId}")
    @ApiOperation("获取用户优惠券列表")
    BaseResult<List<CourseCoupon>> getUserCoupon(
            @PathVariable Integer userId,
            @RequestParam(required = false) Boolean isAvailable,
            @RequestParam(required = false) Boolean enabled);

    @ApiOperation("获取退款单")
    @GetMapping("refunds/sub/orderList")
    BaseResult<List<RsCourseRefundOrderSub>> findRefundOrderSubList(
            @RequestParam(required = false) @ApiParam("主订单ID") Integer mainOrderId,
            @RequestParam(required = false) @ApiParam("子订单ids") List<Integer> subOrderIdList,
            @RequestParam(required = false) @ApiParam("退款单ID") Integer refundId,
            @RequestParam(required = false) @ApiParam("退款状态") Integer status,
            @RequestParam(required = false) @ApiParam("状态列表") List<Integer> statusList);

    @ApiOperation("获取签字url")
    @PostMapping(value = "pdf/signUrl")
    BaseResult<String> getSignUrl(
            @RequestParam @ApiParam("订单id") Integer orderId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @GetMapping("sku/getPage")
    @ApiOperation("获取sku列表弹窗")
    PageResult<List<RsSku>> getPage(
            @RequestParam Integer companyType,
            @RequestParam(required = false) @ApiParam("状态") Integer status,
            @RequestParam(required = false) @ApiParam("产品等级(属性)") Integer productLevel,
            @RequestParam(required = false) @ApiParam("搜索内容") String content,
            @RequestParam(required = false) @ApiParam("是否过滤智投sku (true则不显示智投sku)") Boolean isFilterStrategy,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("coupons/templates/skuList/{templateId}")
    @ApiOperation("根据模板id获取sku列表")
    BaseResult<List<RsSku>> getSkuByTemplateId(
            @PathVariable Integer templateId);

    @GetMapping("sku/activity/page")
    @ApiOperation("获取促销活动页面")
    PageResult<List<SkuActivityDto>> getSkuActivityPage(
            @RequestParam(required = false) @ApiParam("状态") Integer status,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    /**
     * 创建促销活动
     *
     * @param req 请求参数
     * @return 促销活动
     */
    @PostMapping("sku/activity/create")
    @ApiOperation("创建促销活动")
    BaseResult<RsSkuActivity> createSkuActivity(@RequestBody @Valid CreateSkuActivityReq req);

    /**
     * 更新促销活动
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @PostMapping("sku/activity/update")
    @ApiOperation("更新促销活动")
    BaseResult<Boolean> updateSkuActivity(@RequestBody @Valid UpdateSkuActivityReq req);

    /**
     * 取消活动
     *
     * @param activityId 活动id
     * @return 无参
     */
    @PatchMapping("sku/activity/{activityId}")
    @ApiOperation("取消活动")
    BaseResult<Void> cancelActivity(@PathVariable Integer activityId);

    /**
     * 根据id获取促销活动
     *
     * @param activityId 活动id
     * @return 促销活动
     */
    @GetMapping("sku/activity/{activityId}")
    @ApiOperation("根据id获取促销活动")
    BaseResult<RsSkuActivity> findById(@PathVariable Integer activityId);

    /**
     * 获取促销活动素材
     *
     * @param activityId 活动id
     * @param category   分类
     * @param position   位置
     * @param enabled    是否启用
     * @return 素材
     */
    @GetMapping("sku/activity/material")
    @ApiOperation("获取促销活动的素材")
    BaseResult<List<SkuMaterialItem>> getSkuActivityMaterialList(
            @RequestParam @ApiParam("活动id") Integer activityId,
            @RequestParam(required = false) @ApiParam("素材分类") Integer category,
            @RequestParam(required = false) @ApiParam("素材位置") Integer position,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled);

    default BaseResult<List<SkuMaterialItem>> getSkuActivityMaterialList(Integer activityId) {
        return getSkuActivityMaterialList(activityId, null, null, true);
    }

    /**
     * 获取优惠券模板列表
     *
     * @param companyType 公司类型
     * @param enable      是否启用
     * @param current     页码
     * @param size        数量
     * @return 优惠劵模板
     */
    @GetMapping("coupons/templates/page")
    @ApiOperation("获取优惠劵模板列表")
    PageResult<List<CourseCouponTemplate>> getCouponTemplatePage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enable,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    /**
     * 根据skuId获取促销活动
     *
     * @param skuId skuId
     * @return 促销活动
     */
    @GetMapping("sku/activity/skuId/{skuId}")
    @ApiOperation("根据skuId获取促销活动")
    BaseResult<RsSkuActivity> getActivityBySkuId(@PathVariable Integer skuId);

    @PostMapping("coupons/plan/batch")
    @ApiOperation("批量获取优惠券活动")
    BaseResult<List<CourseCouponPlan>> getCouponPlanList(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("coupons/plan/sku/{skuId}")
    @ApiOperation("获取sku所有可用的优惠券活动")
    BaseResult<List<CourseCouponPlan>> getSkuCouponPlanList(
            @PathVariable Integer skuId,
            @RequestParam Integer companyType,
            @RequestParam(required = false) Boolean enabled);

    /**
     * 创建银联支付H5支付子订单
     *
     * @param req 请求体
     * @return 响应
     */
    @ApiOperation("创建银联支付H5支付子订单")
    @PostMapping("union_pay/h5/orders/sub")
    BaseResult<CreateUnionPaySubOrderResp> createUnionPaySubOrder(@RequestBody @Valid CreateUnionPaySubOrderReq req);

    @PostMapping("course/catalog/create")
    @ApiOperation("新建目录")
    BaseResult<String> createCourseCatalog(
            @RequestParam Integer seriesId,
            @RequestParam Integer companyType,
            @RequestParam String name);

    @PostMapping("course/catalog/edit")
    @ApiOperation("编辑目录")
    BaseResult<String> editCatalog(
            @RequestParam Long id,
            @RequestParam(required = false) @ApiParam("目录名称") String name,
            @RequestParam(required = false) Boolean enabled);

    @GetMapping("course/catalog/list")
    @ApiOperation("查询目录列表")
    BaseResult<List<CourseCatalogResp>> getCatalogList(
            @RequestParam @ApiParam("系列id") Integer seriesId,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Boolean visible);

    @PostMapping("course/catalog/orderBy")
    @ApiOperation("目录排序")
    BaseResult<String> orderByCatalog(
            @RequestParam @ApiParam("系列id") Integer seriesId,
            @RequestParam @ApiParam("目录顺序") String catalogIds,
            @RequestParam @ApiParam("排序列表") String orderParams);

    @GetMapping("course/series/getCourseSeriesInfo")
    @ApiOperation("课程系列详情")
    BaseResult<RsCourseSeries> getCourseSeriesInfo(
            @RequestParam Integer companyType,
            @RequestParam String number);

    @PostMapping("course/series/updateInfo")
    @ApiOperation("课程装修")
    BaseResult<String> updateSeriesInfo(
            @RequestBody @Valid UpdateSeriesReq req);

    @PostMapping("course/video/create")
    @ApiOperation("新建课程视频")
    BaseResult<String> createVideoCourse(
            @RequestBody @Valid createVideoCourseReq req);

    @PostMapping("course/video/edit")
    @ApiOperation("编辑视频")
    BaseResult<String> editVideo(
            @RequestParam String videoId,
            @RequestParam(required = false) @ApiParam("标题") String title,
            @RequestParam(required = false) Double duration,
            @RequestParam(required = false) String coverUrl,
            @RequestParam(required = false) Boolean enabled);

    @PostMapping("course/video/updateVisitCount")
    @ApiOperation("编辑浏览量")
    BaseResult<String> updateVisitCount(
            @RequestParam Integer id);

    @PostMapping("course/catalog/map")
    @ApiOperation("获取目录map")
    Map<Long, RsCourseCatalog> getCatalogMapByIds(@RequestParam Set<Long> catalogIdSet);

    @ApiOperation("按id获取订单回访")
    @GetMapping("orders/feedback/{id}")
    BaseResult<RsOrderFeedback> getOrderFeedback(@PathVariable Long id);

    @PostMapping("orders/feedback/batch")
    @ApiOperation("按订单id批量获取订单回访")
    BaseResult<List<RsOrderFeedback>> getOrderFeedbackList(
            @RequestBody @Valid BatchReq<Integer> req
    );

    @GetMapping("orders/feedbackData")
    @ApiOperation("获取回访pdf信息")
    BaseResult<Map<String, Object>> feedbackData(
            @RequestParam @ApiParam("订单id") Integer orderId);

    @GetMapping("orders/get-feedback-data")
    @ApiOperation("获取回访pdf信息")
    BaseResult<Map<String, Object>> getFeedbackData(
            @RequestParam @ApiParam("订单id") Integer orderId);

    @GetMapping("refunds/order/data")
    @ApiOperation("获取订单退款pdf信息")
    BaseResult<Map<String, Object>> getOrderRefundData(
            @RequestParam @ApiParam("退款单id") Integer refundId);

    @PostMapping("refunds/read")
    @ApiOperation("确认退费确认书")
    BaseResult<OsErrorCode> readRefundPdf(
            @RequestParam @ApiParam("退款单id") Integer refundId);

    @ApiOperation("回访问卷提交保存")
    @PostMapping("orders/feedback/create")
    BaseResult<String> createFeedback(
            @RequestBody @Validated FeedbackReq req);

    @GetMapping("orders/sku/detail")
    @ApiOperation("前端回访问卷-获取订单详情")
    BaseResult<OrderSkuDetailResp> getOrderSkuDetail(
            @RequestParam @ApiParam("userId") Integer userId,
            @RequestParam @ApiParam("订单编号") String orderNumber);

    @GetMapping("orders/sku-detail")
    @ApiOperation("前端回访问卷-获取订单详情(新测评)")
    BaseResult<OrderSkuDetailResp> newGetOrderSkuDetail(
            @RequestParam @ApiParam("userId") Integer userId,
            @RequestParam @ApiParam("订单编号") String orderNumber);

    /**
     * 获取服务延期申请列表
     *
     * @param creatorId     创建者
     * @param auditStatus   审核状态
     * @param confirmStatus 确认状态
     * @param content       搜索内容
     * @param current       页码
     * @param size          分页数量
     * @return 服务延期申请列表
     */
    @GetMapping("delay_coupon/apply/page")
    @ApiOperation("获取服务延期申请列表")
    PageResult<List<DelayCouponApplyResp>> getDelayCouponApplyPage(
            @RequestParam(required = false) @ApiParam("创建人") Integer creatorId,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("确认状态") Integer confirmStatus,
            @RequestParam(required = false) @ApiParam("签字状态") Integer signStatus,
            @RequestParam(required = false) @ApiParam("搜索内容") String content,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("delay_coupon/info")
    @ApiOperation("延期券详情")
    BaseResult<DelayCouponResp> getDelayCouponInfo(
            @RequestParam Long applyId,
            @RequestParam Integer userId);

    @PostMapping("delay_coupon/apply/create")
    @ApiOperation("创建申请")
    BaseResult<DelayCouponApplyResp> applyDelayCoupon(
            @RequestBody @Validated DelayCouponApplyReq req);

    @GetMapping("delay_coupon/salesVisible")
    @ApiOperation("销售可见延期劵")
    BaseResult<List<DelayCoupon>> getSalesVisibleDelayCoupon(
            @RequestParam @ApiParam("销售id") Integer salesId,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("是否启用") Boolean isEnable
    );

    @GetMapping("delay_coupon/apply/{applyId}")
    @ApiOperation("获取服务延期申请明细")
    BaseResult<DelayCouponApplyDto> getDelayCouponApplyDetail(@PathVariable Long applyId);

    @PostMapping("delay_coupon/apply/{applyId}/audit")
    @ApiOperation("审核延期劵申请")
    BaseResult<DelayCouponApply> auditDelayCouponApply(
            @PathVariable @ApiParam("延期申请id") Long applyId,
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus);

    @GetMapping("delay_coupon/page")
    @ApiOperation("获取延期劵列表")
    PageResult<List<DelayCoupon>> getDelayCouponPage(
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @PostMapping("delay_coupon/create")
    @ApiOperation("创建延期劵")
    BaseResult<DelayCoupon> createDelayCoupon(@RequestBody CreateDelayCouponReq req);

    @ApiOperation("延期券签字")
    @PostMapping("sign/delay_coupon")
    BaseResult<ESignDelayCouponStartResp> startDelayCouponSign(
            @RequestParam Long applyId,
            @RequestParam Integer userId);

    @ApiOperation("生成签字pdf")
    @PostMapping(value = "sign/delay_coupon/generateSignPdf")
    BaseResult<String> generateSignPdf(
            @RequestParam @ApiParam("延期券申请id") Long applyId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @ApiOperation("获取签字url")
    @PostMapping("sign/delay_coupon/signUrl")
    BaseResult<String> getSignUrl(
            @RequestParam @ApiParam("延期券申请id") Long applyId,
            @RequestParam Integer companyType);

    @GetMapping("delay_coupon/visible/sales/{couponId}")
    @ApiOperation("获取延期劵已可见员工")
    BaseResult<List<DelayCouponSalesRelation>> getDelayCouponSalesRelation(@PathVariable Long couponId);

    @GetMapping("delay_coupon/visible/dept/{couponId}")
    @ApiOperation("获取延期劵已可见部门")
    BaseResult<List<DelayCouponDeptRelation>> getDelayCouponDeptRelation(@PathVariable Long couponId);

    @PostMapping("delay_coupon/visible/sales/set")
    @ApiOperation("设置员工可见范围")
    BaseResult<Boolean> setUserVisible(@RequestBody DelayCouponSalesVisibleReq req);

    @PostMapping("delay_coupon/visible/dept/set")
    @ApiOperation("设置部门可见范围")
    BaseResult<Boolean> setDeptVisible(@RequestBody DelayCouponDeptVisibleReq req);

    @PostMapping("delay_coupon/update")
    @ApiOperation("修改启用")
    BaseResult<Boolean> updateEnable(
            @RequestParam @ApiParam("延期劵id") Long delayCouponId,
            @RequestParam @ApiParam("是否启用") Boolean enable);

    @GetMapping("chat_room/config")
    @ApiOperation("获取聊天室配置")
    BaseResult<ChatRoomConfigResp> getChatRoomConfig();

    @GetMapping("asr/record/one")
    @ApiOperation("获取语音识别任务")
    RsAsrTaskRecord getRecord(
            @RequestParam @ApiParam("消息类型") Integer msgType,
            @RequestParam @ApiParam("消息ID") String msgId);

    @PostMapping("appStore/verifyReceipt")
    @ApiOperation("验证苹果内购票据并创建订单")
    BaseResult<List<CreateSubOrderResp>> appStoreVerifyReceipt(@RequestBody @Valid AppStoreVerifyReceiptReq req);

    @GetMapping("appStore/inApps/config")
    @ApiOperation("获取app store内购配置")
    BaseResult<AppStoreInAppsConfigResp> getAppStoreInAppsConfig();

    @ApiOperation("批量获取退款签字")
    @PostMapping("refunds/batch/sign")
    BaseResult<List<RsCourseRefundSign>> getRsCourseRefundSign(
            @RequestBody BatchReq<Integer> req);

    @ApiOperation("获取退款签字")
    @PostMapping("refunds/sign")
    BaseResult<RsCourseRefundSign> getRefundSignById(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("获取退款订单sku列表不分页")
    @GetMapping("refunds/orderSkuList")
    List<RefundOrderSkuInfo> getRefundOrderSkuList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("订单编号") String orderNumber);

    @ApiOperation("通过退款单ID获取订单ID")
    @PostMapping("refunds/by/refundId")
    BaseResult<Integer> getOrderIdByRefundId(
            @RequestParam @ApiParam("退款单ID") Integer refundId);

    @ApiOperation("处理发票回调结果")
    @PostMapping("invoice/pdf/info")
    BaseResult<Void> invoiceInfoHandle(@RequestBody @Valid InvoiceInfoResp req);

    @ApiOperation("发票回调结果失败信息")
    @PostMapping("invoice/pdf/fail/info")
    BaseResult<Void> saveFailInfo(@RequestBody @Valid InvoiceInfoResp req);

    @PostMapping("gift/good/create")
    @ApiOperation("创建礼赠商品")
    BaseResult<Void> createGiftGood(@RequestBody CreateGiftCouponReq req);

    @PostMapping("gift/good/update")
    @ApiOperation("编辑礼赠商品")
    BaseResult<Void> updateGiftGood(@RequestBody UpdateGiftCouponReq req);

    @PutMapping("gift/good/status/{id}")
    @ApiOperation("修改礼赠商品状态")
    BaseResult<Void> setGiftGoodStatus(
            @PathVariable @ApiParam("id") Long id,
            @RequestParam @ApiParam("状态") Integer status);

    @GetMapping("gift/coupon/list/{userId}")
    @ApiOperation("礼赠奖品列表")
    BaseResult<List<GiftCouponResp>> getGiftCouponRespList(@PathVariable @ApiParam("用户ID") Integer userId);

    @GetMapping("gift/good/findByNumber")
    @ApiOperation("通过number获取礼赠商品")
    BaseResult<RsGiftGood> findGiftGoodByNumber(
            @RequestParam @ApiParam("编号") String number,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("状态") Integer status);

    @PostMapping("gift/coupon/apply")
    @ApiOperation("客户申请电子券")
    BaseResult<List<GiftOrderInfoResp>> applyGiftCoupon(
            @RequestParam @ApiParam("礼赠奖品编号") String number,
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PostMapping("gift/coupon/receive")
    @ApiOperation("客户领取电子券")
    BaseResult<Void> receiveGiftCoupon(
            @RequestParam @ApiParam("礼赠奖品编号") String number,
            @RequestParam @ApiParam("订单ID") Integer orderId,
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @GetMapping("gift/good/list")
    @ApiOperation("获取礼赠商品列表")
    PageResult<List<RsGiftGood>> getGiftGoodList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size);

    @GetMapping("gift/good/getGiftGoodList")
    @ApiOperation("获取礼赠商品列表")
    BaseResult<List<RsGiftGood>> getGiftGoodList(
            @RequestParam @ApiParam("礼品id集合") Set<Long> goodIdSet
    );

    @PostMapping("gift/coupon/good/statistics")
    @ApiOperation("统计已发放电子劵数量")
    BaseResult<List<GiftCouponStatistics>> getGiftCouponStatistics(@RequestBody @Valid BatchReq<Long> req);

    @GetMapping("gift/coupon/manage/list")
    @ApiOperation("电子劵码管理")
    PageResult<List<RsGiftCoupon>> getGiftCouponList(
            @RequestParam @ApiParam("礼赠商品id") Long goodId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size);

    @GetMapping("gift/coupon/render/list")
    @ApiOperation("获取电子码领用信息")
    PageResult<List<RsGiftCouponRender>> getGiftCouponRenderList(
            @RequestParam(required = false) @ApiParam("礼赠商品id") Long goodId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size);

    @PostMapping("gift/coupon/batch")
    @ApiOperation("根据电子券领用ID批量获取电子券码信息")
    BaseResult<Map<Long, List<GiftCouponInfoResp>>> batchGetCouponMap(@RequestBody BatchReq<Long> req);

    @GetMapping("gift/coupon/export/list")
    @ApiOperation("导出电子劵码信息")
    BaseResult<List<RsGiftCoupon>> exportGiftCoupon(@RequestBody ExportGiftCouponReq req);

    @PostMapping("gift/coupon/grand")
    @ApiOperation("手动发放电子劵")
    BaseResult<Void> grandGiftCoupon(@RequestBody @Valid GrandGiftCouponReq req);


    @PostMapping("orders/freeze")
    @ApiOperation("冻结订单")
    BaseResult<Void> freezeOrder(
            @RequestParam @ApiParam("订单号") String orderNumber,
            @RequestParam @ApiParam("操作人id") Integer operatorId);

    @GetMapping("refunds/remainAmount")
    @ApiOperation("剩余可退金额")
    BaseResult<Integer> getRemainAmount(
            @RequestParam @ApiParam("订单号") String orderNumber);

    @GetMapping("delay_coupon/apply/list/user")
    @ApiOperation("延期劵申请列表")
    BaseResult<List<DelayCouponApplyResp>> getDelayCouponApplyByUser(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("申请类型") Integer applyType);

    @GetMapping("orders/flow/extract/detail")
    @ApiOperation("获取销售订单抽取结果")
    BaseResult<List<AssignDetailResp>> getExtractDetail(
            @RequestParam @ApiParam("批次ID") Long batchId);

    @GetMapping("orders/flow/assign/detail")
    @ApiOperation("获取合规订单分配结果")
    BaseResult<List<AssignDetailResp>> getAssignDetail(
            @RequestParam @ApiParam("批次ID") Long batchId);

    @PostMapping("orders/flow/assign/hg")
    @ApiOperation("分配合规")
    BaseResult<Void> assignHg(
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("合规审核人ID") Integer hgAuditorId,
            @RequestParam @ApiParam("分配人ID") Integer hgAssignId);

    @PostMapping("orders/flow/reallocate/hg")
    @ApiOperation("再次分配")
    BaseResult<Void> reallocateHg(@RequestBody BatchAssignReq req);

    @GetMapping("orders/flow/auditing/hg")
    @ApiOperation("再次分配-获取合规待审核订单")
    PageResult<List<OrderInfoDto>> auditingHg(
            @RequestParam(required = false) @ApiParam("审核人") Integer auditorId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("orders/flow/auditing")
    @ApiOperation("审核")
    BaseResult<Void> auditing(
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("通过/关闭") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("内容") String content);

    @PostMapping("orders/fix/create")
    @ApiOperation("创建修正订单")
    BaseResult<String> createFixOrder(@RequestBody @Valid CreateOrderFixReq req);

    @ApiOperation("获取订单修正信息记录")
    @GetMapping("orders/fix/recordList")
    PageResult<List<RsOrderFixRecord>> getOrderFixRecordList(
            @RequestParam @ApiParam("订单修正id") Long fixId,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @ApiOperation("获取订单修正信息列表")
    @GetMapping("orders/fix/infoList")
    PageResult<List<RsOrderFixInfo>> getOrderFixInfoList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("订单id") Integer orderId,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @ApiOperation("通过订单Id获取订单(批量)")
    @GetMapping("orders/id/batch")
    BaseResult<List<RsCourseOrder>> batchGetOrderListById(@RequestParam List<Integer> orderIdList);

    @ApiOperation("强制标记")
    @PostMapping("wx_pay/forceRefundMark")
    BaseResult<String> forceRefundMark(@RequestBody @Valid RefundMarkReq req);

    @ApiOperation("创建通联支付子订单")
    @PostMapping("allin_pay/order")
    BaseResult<CreateAllinPaySubOrderResp> createAllinPaySubOrder(@RequestBody @Valid CreateAllinPaySubOrderReq req);

    @PostMapping("delay_coupon/apply/change")
    @ApiOperation("服务变更申请")
    BaseResult<DelayCouponApplyResp> changeDelayCoupon(DelayCouponApplyReq req);

    @GetMapping("vip/subscriptions/filter/user")
    @ApiOperation("获取服务对应用户")
    BaseResult<List<Integer>> filterVipSubscription(
            @RequestParam @ApiParam("服务包编号") Set<String> numberList,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否过期") Boolean isExpired,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("分页数量") Integer size);

    @GetMapping("vip/packages/number/room")
    @ApiOperation("获取直播室vip服务包编号")
    BaseResult<List<String>> getRoomVipPackageNumber(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("直播室编号") String roomNumber);

    @GetMapping("vip/packages/number/poolCase")
    @ApiOperation("获取股票池vip服务包编号")
    BaseResult<List<String>> getPoolCaseVipPackageNumber(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("股票池编号") String poolCaseNumber);

    @ApiOperation("创建人脸识别")
    @PostMapping(value = "face-auth/create")
    BaseResult<FaceAuthResp> createFaceAuth(
            @RequestBody @Valid CreateFaceAuthReq req);

    @ApiOperation("获取人脸识别结果")
    @GetMapping(value = "face-auth/result")
    BaseResult<FaceAuthResultResp> getFaceAuthResult(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("上下文id") String contextId);

    @ApiOperation("更新人脸识别")
    @PostMapping(value = "face-auth/update")
    BaseResult<Void> updateFaceAuth(
            @RequestParam @ApiParam("上下文id") String contextId,
            @RequestParam @ApiParam("是否成功") Boolean isSuccess,
            @RequestParam @ApiParam("认证结果校验码") String verifyCode);


    @PostMapping("orders/sign/no/need")
    @ApiOperation("订单标记无需签字")
    BaseResult<Void> noNeedSign(
            @RequestParam @ApiParam("订单id") Integer orderId,
            @RequestParam @ApiParam("用户id") Integer userId);

    @ApiOperation("按订单id获取人工回访记录")
    @GetMapping("orders/feedback/artificial/{orderId}")
    BaseResult<RsOrderArtificialFeedback> getArtificialFeedback(@PathVariable Integer orderId);

    @ApiOperation("按订单id获取人工回访记录(批量)")
    @GetMapping("orders/feedback/artificial/batch")
    BaseResult<List<RsOrderArtificialFeedback>> batchGetArtificialFeedback(@RequestParam List<Integer> orderIdList);

    @PostMapping("orders/feedback/artificial/create")
    @ApiOperation("标记需要人工回访")
    BaseResult<String> createArtificialFeedback(
            @RequestParam Integer userId,
            @RequestParam Integer orderId);

    @PostMapping("orders/feedback/artificial/strategy/create")
    @ApiOperation("智投人工回访结果")
    BaseResult<String> createArtificialStrategyFeedback(
            @RequestParam Integer userId,
            @RequestParam Integer orderId,
            @RequestParam @ApiParam("是否通过") Boolean isPass,
            @RequestParam @ApiParam("备注") String remark,
            @RequestParam(required = false) @ApiParam("视频链接") String videoUrl);

    @PostMapping("orders/feedback/artificial/update")
    @ApiOperation("标记人工回访成功")
    BaseResult<String> updateArtificialFeedback(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("人工回访id") Long id,
            @RequestParam @ApiParam("是否通过") Boolean isPass,
            @RequestParam @ApiParam("备注") String remark,
            @RequestParam(required = false) @ApiParam("视频链接") String videoUrl);

    @GetMapping("orders/getContractOrderInfo")
    @ApiOperation("获取合同的订单信息")
    BaseResult<OrderContractDetail> getContractOrderInfo(
            @RequestParam @ApiParam("订单号") String orderNumber);

    @PostMapping("invoice/sync/status")
    @ApiOperation("同步发票状态")
    BaseResult<Void> syncInvoiceStatus(
            @RequestParam @ApiParam("发票id") Long invoiceId);

    @GetMapping("invoice/request/record")
    @ApiOperation("获取订单发票请求记录")
    PageResult<List<RsOrderInvoiceRequestRecord>> getInvoiceRequestRecord(
            @RequestParam @ApiParam("订单id") Integer orderId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("拉卡拉聚合扫码-交易通知通知处理")
    @PostMapping(value = "lkl/trade/notify")
    BaseResult<String> lklNotifyHandle(LklOrderNotifyReq req);

    @ApiOperation("获取支付方式")
    @GetMapping("pay_type")
    BaseResult<List<PayTypeResp>> getPayType(
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否支付宝页面") Boolean isAlipayPage);

    @ApiOperation("创建拉卡拉微信支付子订单")
    @PostMapping("lkl/wx/order")
    BaseResult<CreateLklPaySubOrderResp> createLklWxPaySubOrder(@RequestBody @Valid CreateLklPaySubOrderReq req);

    @ApiOperation("创建拉卡拉支付宝支付子订单")
    @PostMapping("lkl/alipay/order")
    BaseResult<CreateLklPaySubOrderResp> createLklAliPaySubOrder(@RequestBody @Valid CreateLklPaySubOrderReq req);

    @ApiOperation("关闭拉卡拉微信支付子订单")
    @PostMapping("lkl/close/order")
    BaseResult<Boolean> lklPayCloseOrder(
            @RequestParam @ApiParam("订单号") String orderNumber,
            @RequestParam @ApiParam("请求ip地址") String ip);

    @GetMapping("gift/introduce/list")
    @ApiOperation("礼品介绍列表")
    PageResult<List<RsGiftIntroduce>> getGiftIntroduceList(
            @RequestParam @ApiParam("礼品id") Long goodId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size);

    @PostMapping("gift/introduce/create")
    @ApiOperation("新增礼品介绍")
    BaseResult<Void> createGiftIntroduce(@RequestBody @Valid CreateIntroduceReq req);

    @PostMapping("gift/introduce/enabled")
    @ApiOperation("启用/禁用礼品介绍")
    BaseResult<Void> enabledIntroduce(@RequestParam @ApiParam("序号id") Long id,
                                      @RequestParam @ApiParam("启用/禁用") Boolean enabled);

    @GetMapping("gift/introduce/detail/list")
    @ApiOperation("礼品详情介绍列表")
    BaseResult<List<RsGiftIntroduce>> getGiftIntroduceDetailList(@RequestParam @ApiParam("礼品id") Long goodId);

    @PostMapping("gift/introduce/picture")
    @ApiOperation("介绍图片")
    BaseResult<String> getGiftIntroducePicture(@RequestParam @ApiParam("序号id") Long id);

    @ApiOperation("关闭订单")
    @PostMapping(value = "allin_pay/order/close")
    BaseResult<Boolean> allinPayCloseOrder(
            @RequestParam Integer companyType,
            @RequestParam @ApiParam("商户类型") Integer mchType,
            @RequestParam @ApiParam("原通联交易流水号") String paymentAccount);

    @GetMapping("gift/coupon/usage/detail")
    @ApiOperation("电子券码使用详情")
    BaseResult<RsGiftCouponUsageDetail> getGiftCouponUsageDetail(@RequestParam @ApiParam("电子券码") String code);

    @GetMapping("gift/coupon/usage/detailById")
    @ApiOperation("电子券码使用详情")
    BaseResult<RsGiftCouponUsageDetail> getGiftCouponUsageDetailById(@RequestParam @ApiParam("电子券id") Long pkid);

    @GetMapping("gift/coupon/receive/detail")
    @ApiOperation("礼赠领取信息")
    BaseResult<GiftCouponResp> giftCouponReceiveDetail(
            @RequestParam @ApiParam("订单id") Integer orderId,
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("sku/strategy/skuList")
    @ApiOperation("获取策略关联的sku")
    BaseResult<List<SkuStrategy>> getSkuStrategy(
            @RequestParam @ApiParam("策略id") Long strategyId);

    @GetMapping("sku/strategy/sku-list")
    @ApiOperation("获取策略关联的sku")
    BaseResult<List<SkuStrategy>> getSkuStrategyList(
            @RequestParam @ApiParam("策略id") Long strategyId,
            @RequestParam(required = false) @ApiParam("策略状态:1-待上架,2-已上架,3-已下架") Integer status);

    @GetMapping("sku/strategy/info")
    @ApiOperation("获取sku策略信息")
    BaseResult<SkuStrategy> getSkuStrategyBySku(
            @RequestParam @ApiParam("策略id") Integer skuId);

    @PostMapping("sku/strategy/batch")
    @ApiOperation("批量获取sku策略信息")
    BaseResult<List<SkuStrategy>> batchSkuStrategyBySku(@RequestBody BatchReq<Integer> req);

    @PostMapping("orders/delivery/create")
    @ApiOperation("创建订单物流信息")
    BaseResult<Void> createDelivery(@RequestBody @Valid CreateOrderDeliveryReq req);

    @PostMapping("orders/delivery/batch")
    @ApiOperation("批量查询订单物流信息")
    BaseResult<List<RsOrderDeliveryInfo>> batchGetDelivery(@RequestBody @Valid @ApiParam("订单id列表") BatchReq<Integer> req);

    @ApiOperation("查询广告订单列表")
    @GetMapping("promotion-orders/list")
    PageResult<List<PromotionOrderResp>> getPromotionOrderList(
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @RequestParam(required = false) @ApiParam("是否退款") Boolean isRefund,
            @RequestParam(required = false) @ApiParam("物流状态") Integer deliveryStatus,
            @RequestParam(required = false) @ApiParam("下单开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startPayTime,
            @RequestParam(required = false) @ApiParam("下单结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endPayTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("分页数量") Integer size);

    @ApiOperation("修改发货状态")
    @PostMapping("promotion-orders/update/delivery-status")
    BaseResult<Void> updateDeliveryStatus(
            @RequestParam @ApiParam("订单编号") Integer orderId,
            @RequestParam @ApiParam("物流状态") Integer deliveryStatus);

    @PostMapping("gift/coupon/cancel")
    @ApiOperation("作废电子券码")
    BaseResult<Void> cancelGiftCoupon(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("电子劵ID") Long couponId);

    @GetMapping("gift/coupon/operate/record")
    @ApiOperation("电子券码操作记录")
    BaseResult<List<RsCouponOperateRecord>> getCouponOperateRecord(
            @RequestParam @ApiParam("电子券id") Long couponId);

    @GetMapping("coupons/order-buy-coupon")
    @ApiOperation("根据支付的优惠券订单编号查询发放的优惠券信息")
    BaseResult<CouponResp> orderBuyCoupon(@RequestParam @ApiParam("订单编号") Integer orderId,
                                          @RequestParam @ApiParam("公司类型") Integer companyType);


    @GetMapping("orders/sub-order-amount-sum")
    @ApiOperation("获取订单已支付金额")
    BaseResult<Map<Integer, Integer>> geSubOrderAmountSumMap(
            @RequestParam @ApiParam("订单id") List<Integer> orderIds
    );

    @GetMapping("refunds/subRefundNumber/{refundNumber}")
    @ApiOperation("获取子退款单详情")
    BaseResult<RsCourseRefundOrderSub> getRefundSubInfo(
            @PathVariable String refundNumber
    );

    @ApiOperation("解析支付回调请求")
    @PostMapping(value = "allin_pay/order-response")
    BaseResult<AllinPayOrderResponse> getAllinPayOrderResponse(
            @RequestParam Integer companyType,
            @RequestBody @ApiParam("回调信息") String notifyBody,
            @RequestParam @ApiParam("mchType") Integer mchType);

    @ApiOperation("解析退款回调请求")
    @PostMapping(value = "allin_pay/refund-response")
    BaseResult<AllinRefundOrderResponse> getAllinRefundOrderResponse(
            @RequestParam Integer companyType,
            @RequestBody @ApiParam("回调信息") String notifyBody,
            @RequestParam @ApiParam("mchType") Integer mchType);

    @GetMapping("strategy/sign-url")
    @ApiOperation("获取签字链接")
    BaseResult<String> getStrategyApplySignUrl(
            @RequestParam @ApiParam("智投增值包申请id") Long applyId,
            @RequestParam Integer companyType);

    @PostMapping("vip-coupon/template/create")
    @ApiOperation("创建服务体验券模板")
    BaseResult<Void> createVipCouponTemplate(@RequestBody @Valid CreateVipCouponTemplateReq req);

    @PostMapping("vip-coupon/template/update")
    @ApiOperation("编辑服务体验券模板")
    BaseResult<Void> updateVipCouponTemplate(@RequestBody @Valid UpdateVipCouponTemplateReq req);

    @GetMapping("vip-coupon/template/list")
    @ApiOperation("分页获取服务体验券模板")
    PageResult<List<RsVipCouponTemplate>> getVipCouponTemplateList(
            @RequestParam Integer companyType,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("vip-coupon/template/visible/sales-list")
    @ApiOperation("获取服务体验券员工列表")
    BaseResult<List<RsVipCouponTemplateSalesRelation>> getVipCouponTemplateSalesRelation(
            @RequestParam @ApiParam("体验券模板id") Long couponTemplateId);

    @GetMapping("vip-coupon/template/visible/dept-list")
    @ApiOperation("获取服务体验券部门可见列表")
    BaseResult<List<RsVipCouponTemplateDeptRelation>> getVipCouponTemplateDeptRelation(
            @RequestParam @ApiParam("体验券模板id") Long couponTemplateId);

    @PostMapping("vip-coupon/template/visible/set")
    @ApiOperation("设置服务体验券可见范围")
    BaseResult<Void> setCouponTemplateSalesVisible(@RequestBody @Valid VipCouponTemplateVisibleReq req);

    @PostMapping("vip-coupon/template/set-enable")
    @ApiOperation("修改服务体验券模板启用状态")
    BaseResult<Void> setCouponTemplateEnable(
            @RequestParam @ApiParam("体验券模板id") Long couponTemplateId,
            @RequestParam @ApiParam("是否启用") Boolean enable);

    @PostMapping("vip-coupon/page")
    @ApiOperation("分页获取服务体验券列表")
    PageResult<List<VipCouponResp>> vipCouponPage(@RequestBody @Valid VipCouponReq req);

    @GetMapping("vip-coupon/info")
    @ApiOperation("获取服务体验券详情")
    BaseResult<VipCouponResp> vipCouponInfo(@RequestParam @ApiParam("券id") Long couponId);

    @GetMapping("vip-coupon/visible/template/list/sales")
    @ApiOperation("获取当前销售可见体验券模板列表")
    BaseResult<List<RsVipCouponTemplate>> getVipCouponTemplateSalesVisible(
            @RequestParam @ApiParam("销售id") Integer salesId,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("是否启用") Boolean isEnable);

    @PostMapping("vip-coupon/apply")
    @ApiOperation("领取服务体验券")
    BaseResult<VipCouponInfoResp> applyVipCoupon(
            @RequestParam Integer companyType,
            @RequestParam Integer customerId,
            @RequestBody @Validated VipCouponInfoReq req);

    @GetMapping("vip-coupon/template/info")
    @ApiOperation("获取服务体验券模板详情")
    BaseResult<VipCouponTemplateInfoResp> vipCouponTemplateInfo(@RequestParam @ApiParam("模板id") Long templateId);

    @GetMapping("vip-coupon/info/number")
    @ApiOperation("根据number获取服务体验券详情")
    BaseResult<RsVipCoupon> getVipCouponInfo(
            @RequestParam @ApiParam("客户id") Integer customerId,
            @RequestParam @ApiParam("体验券number") String number);

    @PostMapping("payment/order/main")
    @ApiOperation("创建主订单")
    BaseResult<RsCourseOrder> createMainOrder(@RequestBody @Valid CreateMainOrderReq req);

    @GetMapping("pay-company/get-by-weight")
    @ApiOperation("根据权重获取主体id")
    BaseResult<Long> getPayCompanyByWeight();

    @ApiOperation("支付主体管理列表")
    @GetMapping("pay-company/list")
    BaseResult<List<RsPayCompany>> getPaymentCompanyList();

    @ApiOperation("设置营业信息")
    @PostMapping("pay-company/set-business-contract")
    BaseResult<Void> setPaymentCompanyBusinessContract(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestParam @ApiParam("主体id") Long id, @RequestParam @ApiParam("营业信息") String businessContract);

    @ApiOperation("设置银行信息")
    @PostMapping("pay-company/set-bank-contract")
    BaseResult<Void> setPaymentCompanyBankContract(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestParam @ApiParam("主体id") Long id, @RequestParam @ApiParam("银行信息") String bankContract);

    @ApiOperation("设置权重")
    @PostMapping("pay-company/set-weight")
    BaseResult<Void> setPaymentCompanyWeight(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestParam @ApiParam("主体id") Long id, @RequestParam @ApiParam("权重") Integer weight);

    @ApiOperation("操作记录")
    @GetMapping("pay-company/operate-record")
    BaseResult<List<RsPayCompanyModifyRecord>> getPaymentCompanyOperateRecord(@RequestParam @ApiParam("主体id") Long id);

    @ApiOperation("商户号管理列表")
    @GetMapping("payment/merchant/list")
    PageResult<List<RsMerchantInfo>> getMerchantList(
            @RequestParam(required = false) @ApiParam("支付主体id") Long payCompanyId,
            @RequestParam(required = false) @ApiParam("平台") Integer platform,
            @RequestParam(required = false) @ApiParam("分类") Integer category,
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size);

    @ApiOperation("创建商户")
    @GetMapping("payment/merchant/create")
    BaseResult<Void> createMerchant(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestBody @Valid PaymentMerchantReq req);

    @ApiOperation("设置商户名称")
    @GetMapping("payment/merchant/set-name")
    BaseResult<Void> setMerchantName(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestParam @ApiParam("id") Long id, @RequestParam @ApiParam("名称") String name);

    @ApiOperation("设置权重")
    @GetMapping("payment/merchant/set-weight")
    BaseResult<Void> setMerchantWeight(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestParam @ApiParam("主体id") Long id, @RequestParam @ApiParam("权重") Integer weight);

    @ApiOperation("设置状态")
    @GetMapping("payment/merchant/set-enabled")
    BaseResult<Void> setMerchantEnabled(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestParam @ApiParam("商户id") Long id, @RequestParam @ApiParam("状态") Boolean enabled);

    @ApiOperation("设置配置")
    @GetMapping("payment/merchant/set-config")
    BaseResult<Void> setMerchantConfig(@RequestParam @ApiParam("操作人id") Integer operatorId, @RequestParam @ApiParam("商户id") Long id, @RequestParam @ApiParam("配置") String config);

    @ApiOperation("操作记录")
    @GetMapping("payment/merchant/operate-record")
    BaseResult<List<RsMerchantInfoModifyRecord>> getMerchantOperateRecord(@RequestParam @ApiParam("商户id") Long id);

    @GetMapping("pay-company/by/id")
    @ApiOperation("根据主体id获取主体信息")
    BaseResult<RsPayCompany> getPayCompanyById(@RequestParam @ApiParam("主体id") Long payCompanyId);

    @PostMapping("pay-company/batch")
    @ApiOperation("根据ID批量获取支付主题信息")
    BaseResult<List<RsPayCompany>> batchPayCompanyList(
            @RequestBody BatchReq<Long> req);

    @PostMapping("payment/merchant/batchPayCompany")
    @ApiOperation("根据收款商户批量获取支付主体信息")
    BaseResult<List<RsMerchantInfo>> getMerchantInfoList(@RequestParam(required = false) @ApiParam("收款商户") Collection<Long> merchantIds);

    @ApiOperation("获取支付主体信息")
    @GetMapping("payment/merchant/info")
    BaseResult<RsMerchantInfo> getMerchantInfo(@RequestParam @ApiParam("商户id") Long merchantId);

    @ApiOperation("获取支付方式")
    @GetMapping("payment/pay-type")
    BaseResult<List<PaymentTypeResp>> getPaymentType(
            @RequestParam @ApiParam("主体id") Long payCompanyId,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("是否微信页面") Boolean isWxPage);

    @ApiOperation("新解析支付回调请求")
    @PostMapping(value = "payment/allinPay/order-response")
    BaseResult<AllinPayOrderResponse> getPaymentAllinPayOrderResponse(
            @RequestBody @ApiParam("回调信息") String notifyBody,
            @RequestParam @ApiParam("merchantId") Long merchantId);

    @ApiOperation("新解析退款回调请求")
    @PostMapping(value = "payment/allinPay/refund-response")
    BaseResult<AllinRefundOrderResponse> getPaymentAllinRefundOrderResponse(
            @RequestBody @ApiParam("回调信息") String notifyBody,
            @RequestParam @ApiParam("merchantId") Long merchantId);

    @GetMapping("e-sign/config")
    @ApiOperation("获取签字业务配置")
    BaseResult<List<SignConfig>> getSignConfig();

    @GetMapping("e-sign/companies/config")
    @ApiOperation("获取主体配置")
    BaseResult<List<CompaniesConfig>> getCompaniesConfig();

    //新版支付
    @ApiOperation("创建微信原生支付子订单")
    @PostMapping("payment/wx-pay/sub-order")
    BaseResult<CreateWechatPaySubOrderResp> createWxPaySubOrder(@RequestBody @Valid CreateWxPaySubOrderReq req);

    @ApiOperation("创建通联微信支付子订单")
    @PostMapping("payment/allin/wx-pay/sub-order")
    BaseResult<CreateAllinPaySubOrderResp> createAllinWxPaySubOrder(@RequestBody @Valid CreateAllinWxPaySubOrderReq req);

    @ApiOperation("创建支付宝原生支付子订单")
    @PostMapping("payment/alipay/sub-order")
    BaseResult<CreateAlipaySubOrderResp> createAlipayOriginSubOrder(@RequestBody @Valid cn.shrise.radium.orderservice.req.payment.CreateAlipaySubOrderReq req);

    @ApiOperation("创建银联支付子订单")
    @PostMapping("payment/union-pay/sub-order")
    BaseResult<CreateUnionPaySubOrderResp> createUnionPaySubOrderNew(@RequestBody @Valid cn.shrise.radium.orderservice.req.payment.CreateUnionPaySubOrderReq req);

    @ApiOperation("创建拉卡拉微信支付子订单")
    @PostMapping("payment/lkl/wx-pay/sub-order")
    BaseResult<CreateLklPaySubOrderResp> createLklWechatPaySubOrder(@RequestBody @Valid CreateLklWxPaySubOrderReq req);

    @ApiOperation("创建拉卡拉支付宝支付子订单")
    @PostMapping("payment/lkl/alipay/sub-order")
    BaseResult<CreateLklPaySubOrderResp> createLklAlipaySubOrder(@RequestBody @Valid CreateLklAlipaySubOrderReq req);

    @ApiOperation("创建大额转账子订单（拉卡拉）")
    @PostMapping("payment/lkl/online-transfer/sub-order")
    BaseResult<CreateLklOnlineTransferSubOrderResp> createLklOnlineTransferSubOrder(@RequestBody @Valid CreateLklOnlineTransferSubOrderReq req);

    @ApiOperation("获取合同模板中控件详情")
    @PostMapping("pdf/docTemplates")
    JSONObject getDocTemplates(@RequestParam @ApiParam("appId") String appId, @RequestParam @ApiParam("模板ID") String templateId);

    @ApiOperation("获取线上退款单")
    @GetMapping("payment/refund/unfinished-online")
    BaseResult<List<RefundSubInfoDto>> getUnfinishedOnlineRefundList(@RequestParam @ApiParam("退款主订单id") Integer refundOrderId);

    @GetMapping("payment/merchant/platform")
    @ApiOperation("获取平台下所有商户号")
    BaseResult<List<RsMerchantInfo>> getMerchantListByPlatform(
            @RequestParam @ApiParam("平台id") Integer platform,
            @RequestParam @ApiParam("分类") Integer category);

    @PostMapping("orders/transfer/flow/create")
    @ApiOperation("创建转账单")
    BaseResult<Void> createTransferOrder(@RequestBody @Valid CreateTransferOrderReq req);

    @GetMapping("payment/close-order")
    @ApiOperation("关闭订单")
    BaseResult<Void> closeOrder(
            @RequestParam @ApiParam("主订单id") String orderNumber,
            @RequestParam @ApiParam("操作人id") Integer operatorId);

    @ApiOperation("关闭子订单")
    @PostMapping("payment/sub-orders/close")
    BaseResult<Boolean> closePaymentSubOrder(@RequestParam @ApiParam("子订单号") Integer subOrderId);

    @PostMapping("order-complaint/auto-reply/is-open/update")
    @ApiOperation("开启/关闭自动回复")
    BaseResult<Void> complaintAutoReplyIsOpenUpdate(
            @RequestParam @ApiParam("模板ID") Long id,
            @RequestParam @ApiParam("开启/关闭") Boolean isOpen,
            @RequestParam @ApiParam("操作人ID") Integer operatorId);

    @PostMapping("order-complaint/auto-reply/update")
    @ApiOperation("编辑回复模板内容")
    BaseResult<Void> complaintAutoReplyUpdate(
            @RequestParam @ApiParam("模板ID") Long id,
            @RequestParam @ApiParam("模板内容") String content,
            @RequestParam @ApiParam("操作人ID") Integer operatorId);

    @PostMapping("refunds/bank-info/create")
    @ApiOperation("添加银行卡信息")
    void createOrderBankInfo(
            @RequestBody @Valid CreateBankReq req,
            @RequestParam @ApiParam("订单ID") Integer orderId,
            @RequestParam @ApiParam("姓名") String name);

    @ApiOperation("修改银行卡信息")
    @PostMapping("refunds/bank-info/update")
    BaseResult<Void> updateRefundBankInfo(@RequestBody RefundBankUpdateReq req);

    @GetMapping("refunds/refund-bank-info")
    @ApiOperation("订单退款银行卡信息")
    BaseResult<RsRefundBankInfo> orderRefundBankInfo(
            @RequestParam @ApiParam("订单ID") Integer orderId);

    @ApiOperation("订单退款银行卡操作记录")
    @GetMapping("refunds/bank-info/record")
    PageResult<List<RefundBankRecordResp>> orderRefundBankRecord(@RequestParam @ApiParam("订单id") Integer orderId,
                                                                 @RequestParam(required = false) @ApiParam("页码") Integer current,
                                                                 @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @PostMapping("order-complaint/auto-reply/operate/record")
    @ApiOperation("自动回复模板操作记录")
    BaseResult<List<RsComplaintReplyTemplateRecord>> getAutoReplyOperateRecord(
            @RequestParam @ApiParam("模板ID") Long id);

    @GetMapping("order-complaint/auto-reply")
    @ApiOperation("自动回复模板")
    BaseResult<RsOrderComplaintReplyTemplate> getComplaintAutoReply(
            @RequestParam @ApiParam("模板ID") Long id);

    @GetMapping("order-complaint/wx/list")
    @ApiOperation("商户投诉列表-微信")
    PageResult<List<OrderComplaintWxResp>> getOrderComplaintWxList(
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("投诉状态") String complaintStatus,
            @RequestParam(required = false) @ApiParam("商户ID") Long merchantId,
            @RequestParam(required = false) @ApiParam("商户分类") Integer category,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("order-complaint/wx/detail")
    @ApiOperation("投诉详情-微信")
    BaseResult<ComplaintDetailWxResp> getComplaintDetailWx(
            @RequestParam @ApiParam("投诉ID") Long id);

    @GetMapping("order-complaint/wx/negotiation/list")
    @ApiOperation("微信投诉协商历史")
    PageResult<List<ComplaintNegotiationWxResp>> getComplaintNegotiationWx(
            @RequestParam @ApiParam("投诉ID") Long id,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("order-complaint/wx/mobile")
    @ApiOperation("查看联系方式")
    BaseResult<String> getMobileByComplaintId(
            @RequestParam @ApiParam("投诉ID") Long id,
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PostMapping("order-complaint/customer/reply")
    @ApiOperation("回复客户")
    BaseResult<Void> customerReply(@RequestBody @Valid OrderComplaintReplyReq req);

    @PostMapping("order-complaint/handle/completed")
    @ApiOperation("处理完成")
    BaseResult<Void> handleCompleted(
            @RequestParam @ApiParam("投诉ID") Long id,
            @RequestParam @ApiParam("操作人ID") Integer operatorId
    );

    @GetMapping("order-complaint/wx/operate/record")
    @ApiOperation("微信商户投诉操作记录")
    BaseResult<List<RsOrderComplaintWechatOperateRecord>> getComplaintWxOperateRecord(
            @RequestParam @ApiParam("投诉ID") Long id);

    @GetMapping("orders/get-order-by-wx-user")
    @ApiOperation("根据wxId,userId查询订单")
    BaseResult<List<RsCourseOrder>> getOrderByWxIdAndUserId(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId,
            @RequestParam(required = false) @ApiParam("订单状态") List<Integer> status);

    @GetMapping("id_card/verify/base")
    @ApiOperation("二要素验证")
    BaseResult<Result> verifyBase(@RequestParam @ApiParam("身份证号") String idNumber, @RequestParam @ApiParam("姓名") String name);

    @ApiOperation("人脸识别认证")
    @PostMapping(value = "id_card/face-verify")
    BaseResult<CreateFaceVerifyResp> faceVerify(
            @RequestBody @Valid CreateFaceVerifyReq req);

    @ApiOperation("获取人脸识别结果")
    @GetMapping(value = "id_card/face-verify/result")
    BaseResult<FaceAuthResultResp> getFaceVerifyResult(
            @RequestParam @ApiParam("flowId") String flowId);

    @GetMapping("orders/mark-record-list")
    @ApiOperation("查询标记2.0销售订单列表")
    PageResult<List<RsCourseOrderExt>> getMarkRecordList(
            @RequestParam(required = false) @ApiParam("标记开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startMarkDate,
            @RequestParam(required = false) @ApiParam("标记结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endMarkDate,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchText,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @GetMapping("coupons/me")
    @ApiOperation("获取用户优惠券列表")
    BaseResult<List<CourseCoupon>> getMyCouponList(@RequestParam Integer userId, @RequestParam(required = false) Integer status);

    @ApiOperation("湖仓查询销售主订单列表")
    @PostMapping("orders/sales-order-list")
    PageResult<List<RsCourseOrder>> getCourseOrderSalesByFilter(
            @RequestParam Integer companyType,
            @RequestBody @Valid @ApiParam("salesIds") BatchReq<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("是否有相关退款") Boolean isRefund,
            @RequestParam(required = false) @ApiParam("支付开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startPayTime,
            @RequestParam(required = false) @ApiParam("支付结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endPayTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("创建连连微信支付子订单")
    @PostMapping("payment/llian/wx-pay/sub-order")
    BaseResult<CreateLLianPaySubOrderResp> createLLianWechatPaySubOrder(@RequestBody @Valid CreateLLianPaySubOrderReq req);

    @ApiOperation("创建连连支付宝支付子订单")
    @PostMapping("payment/llian/alipay/sub-order")
    BaseResult<CreateLLianPaySubOrderResp> createLLianAliPaySubOrder(@RequestBody @Valid CreateLLianPaySubOrderReq req);

    @GetMapping("strategy-order-link/list")
    @ApiOperation("获取购买链接列表")
    PageResult<List<RsStrategyOrderLink>> getStrategyLinkList(
            @RequestParam @ApiIgnore("公司类型") Integer companyType,
            @RequestParam @ApiIgnore("用户id") Integer userId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("strategy-order-link/customer/useful")
    @ApiOperation("获取客户可用购买链接")
    BaseResult<RsStrategyOrderLink> getUsefulLink(
            @RequestParam @ApiIgnore("客户id") Integer customerId,
            @RequestParam @ApiIgnore("策略id") Long strategyId);

    @PostMapping("strategy-order-link/create")
    @ApiOperation("创建购买链接")
    BaseResult<RsStrategyOrderLink> createStrategyLink(
            @RequestParam @ApiIgnore("公司类型") Integer companyType,
            @RequestParam @ApiIgnore("用户id") Integer userId,
            @RequestParam @ApiParam("客户id") Integer customerId,
            @RequestParam @ApiParam("产品id") Integer skuId,
            @RequestParam @ApiParam("购买类型") Integer buyType,
            @RequestParam(required = false) @ApiParam("资金量") Long fund,
            @RequestParam(required = false) @ApiParam("当前资金量") Long currentFund,
            @RequestParam(required = false) @ApiParam("服务到期时间") Instant serviceExpireTime,
            @RequestParam(required = false) @ApiParam("当前服务到期时间") Instant currentServiceExpireTime,
            @RequestParam @ApiParam("支付金额") Long payAmount,
            @RequestParam @ApiParam("计算公式") String formula);

    @PostMapping("strategy-order-link/disable")
    @ApiOperation("废弃客户购买链接")
    BaseResult<Void> disableStrategyLink(
            @RequestParam @ApiIgnore("订单id") Integer orderId);

    @GetMapping("strategy-order-link/number")
    @ApiOperation("根据number获取客户购买链接")
    BaseResult<RsStrategyOrderLink> getStrategyLinkByNumber(
            @RequestParam @ApiIgnore("编号") String number);

    @PostMapping("strategy-order-link/update/order")
    @ApiOperation("更新客户购买链接订单号")
    BaseResult<Void> updateStrategyLinkOrder(
            @RequestParam @ApiIgnore("编号") String number,
            @RequestParam @ApiIgnore("订单id") Integer orderId);

    @GetMapping("strategy-order-link/by/order")
    @ApiOperation("通过订单id获取购买链接")
    BaseResult<RsStrategyOrderLink> getStrategyLinkByOrderId(
            @RequestParam @ApiIgnore("订单id") Integer orderId);

    @GetMapping("refunds/manual-list")
    @ApiOperation("需人工处理的退款列表")
    PageResult<List<RefundPlanResp>> getRefundManualList(
            @RequestParam(required = false) @ApiParam("退款创建开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createStartTime,
            @RequestParam(required = false) @ApiParam("退款创建结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createEndTime,
            @RequestParam(required = false) @ApiParam("支付主体ID") Long payCompanyId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("refunds/manual-list/export")
    @ApiOperation("需人工处理的退款列表(无需分页)")
    BaseResult<List<RefundPlanResp>> getRefundManualListExport(
            @RequestParam(required = false) @ApiParam("退款创建开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createStartTime,
            @RequestParam(required = false) @ApiParam("退款创建结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createEndTime,
            @RequestParam(required = false) @ApiParam("支付主体ID") Long payCompanyId);

    @ApiOperation("批量标记退款")
    @PostMapping("wx_pay/refund-batch-mark")
    BaseResult<RefundBatchMarkResp> refundBatchMark(@RequestBody @Valid RefundBatchMarkReq req);

    @ApiOperation("合规审核批量分配（平均分配）")
    @PostMapping("orders/flow/batch/average-assign")
    BaseResult<Void> batchAverageAssign(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestBody BatchAverageAssignReq req);

    @ApiOperation("合规审核-筛选待分配订单")
    @PostMapping("orders/flow/department-order-count")
    BaseResult<List<BatchAverageAssignResp>> filterAverageAssignOrder(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestBody BatchAverageAssignFilterReq req);

    @PostMapping("strategy-order-link/update-service-expire-time")
    @ApiOperation("更新策略链接服务到期时间")
    BaseResult<Void> updateServiceExpireTime(
            @RequestParam @ApiIgnore("策略链接id") Long id,
            @RequestParam @ApiParam("服务到期时间") Instant serviceExpireTime);

    @ApiOperation("查看转账单列表")
    @GetMapping("payment/transfer-order-list")
    BaseResult<List<TransferOrderResp>> getTransferOrderList(
            @RequestParam @ApiParam("主体id") Long payCompanyId);

    @ApiOperation("获取推广支付方式")
    @GetMapping("payment/pay-type/promotion")
    BaseResult<Long> getPromotionPaymentType();


    @GetMapping("vip/packages/info-list")
    @ApiOperation("用户VIP服务信息")
    BaseResult<List<UserVipPackageInfo>> getSubscriptionsInfoList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId);

    @PostMapping("vip/packages/getServiceTypeList")
    @ApiOperation("获取服务包对应的权限")
    BaseResult<Set<String>> getServiceTypeList(@RequestParam Integer companyType,
                                               @RequestParam @ApiParam("服务包") Set<String> numbers);

    @GetMapping("push/subscription/open-check")
    @ApiOperation("开启推送订阅检查")
    BaseResult<Set<String>> openPushSubscriptionCheck(@RequestParam @ApiParam("服务包编号") String number);

    @GetMapping("push/subscription/cancel-check")
    @ApiOperation("取消推送订阅检查")
    BaseResult<Set<String>> cancelPushSubscriptionCheck(@RequestParam @ApiParam("用户id") Integer userId,
                                                        @RequestParam @ApiParam("服务包编号") String number);

    @GetMapping("sku/product-level-age/list")
    @ApiOperation("sku年龄限制列表")
    BaseResult<List<SkuProductLevelAge>> getProductLevelAgeList();

    @GetMapping("sku/product-level-age/get")
    @ApiOperation("sku年龄限制")
    BaseResult<SkuProductLevelAge> getProductLevelAge(@RequestParam @ApiParam("产品等级") Integer productLevel);

    @PostMapping("sku/product-level-age/update")
    @ApiOperation("sku年龄限制修改")
    BaseResult<Void> updateProductLevelAge(@RequestBody ProductLevelAgeUpdateReq req);

    @GetMapping("sku/product-level-age/record-list")
    @ApiOperation("sku年龄限制修改记录列表")
    BaseResult<List<SkuProductLevelAgeRecord>> getProductLevelAgeRecordList(
            @RequestParam @ApiParam("产品等级") Integer productLevel
    );

}

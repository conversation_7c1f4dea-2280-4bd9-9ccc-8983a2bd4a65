package cn.shrise.radium.orderservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class VipCouponTemplateVisibleReq {

    @ApiModelProperty(value = "销售id集合")
    private List<Integer> salesList;

    @ApiModelProperty(value = "部门id集合")
    private List<Integer> deptList;

    @NotNull(message = "体验券模板Id不能为空")
    @ApiModelProperty(value = "体验券模板id")
    private Long couponTemplateId;
}

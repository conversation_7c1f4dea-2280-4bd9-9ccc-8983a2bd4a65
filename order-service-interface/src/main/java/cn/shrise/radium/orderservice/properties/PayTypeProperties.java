package cn.shrise.radium.orderservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@RefreshScope
@ConfigurationProperties(prefix = "pay-type")
public class PayTypeProperties {

    private List<PayType> configs;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PayType {

        private String number;

        private String name;

        private Boolean isWeight;

        private Boolean isOther = false;

        private Boolean alipayPage = false;

        private List<PayTypeItem> items;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PayTypeItem {

        private String number;

        private String name;

        private Integer type;

        private Integer weight;
    }
}

package cn.shrise.radium.orderservice.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("部门退款信息类")
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentRefundInfo {

    private Integer departmentId;

    private Long refundCount;

    private Long refundAmount;

    private Long dzId;

}

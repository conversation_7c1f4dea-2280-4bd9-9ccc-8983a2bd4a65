package cn.shrise.radium.orderservice.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tan<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/9/5 11:23
 * @Desc:
 **/
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class VipCouponTemplateInfoResp {

    @ApiModelProperty(value = "体验券模板名称")
    private String name;

    @ApiModelProperty(value = "体验天数")
    private Integer period;

    @ApiModelProperty(value = "简介")
    private String brief;

    @ApiModelProperty(value = "运营图")
    private String imageUrl;

}

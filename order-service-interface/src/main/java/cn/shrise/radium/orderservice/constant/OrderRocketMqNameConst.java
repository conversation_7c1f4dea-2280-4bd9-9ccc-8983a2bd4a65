package cn.shrise.radium.orderservice.constant;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@UtilityClass
public class OrderRocketMqNameConst {

    /**
     * 支付回调topic
     */
    public static final String TOPIC_PAY_NOTIFY = "payNotify";


    @UtilityClass
    public static final class TagType {
        /**
         * 微信h5支付订单处理
         */
        public static final String TAG_WX_H5_PAY_ORDER_NOTIFY = "wxH5PayOrderNotifyProcess";
        /**
         * 支付订单处理
         */
        public static final String TAG_PAY_ORDER_NOTIFY = "payOrderNotifyProcess";
        /**
         * 订单扩展信息
         */
        public static final String TAG_ORDER_INFO_EXT = "orderInfoExtProcess";
        /**
         * 订单开通服务处理
         */
        public static final String TAG_OPEN_ORDER_SERVICE = "openOrderServiceProcess";
        /**
         * 支付订单站内信处理
         */
        public static final String TAG_SEND_PAY_AGENT = "sendPayAgentNotify";
        /**
         * 订单模板消息处理
         */
        public static final String TAG_SEND_PAY_SUCCESS_MSG = "sendPaySuccessMsgProcess";
        /**
         * h5支付订单回传
         */
        public static final String H5_ORDER_CONVERT = "h5OrderConvert";

        /**
         * 微信公众号支付回调
         */
        public static final String WX_ACCOUNT_ORDER_CONVERT = "wxAccountOrderConvert";

        /**
         * 微信订单退款服务处理
         */
        public static final String WX_ORDER_REFUND = "refundOrderServiceProcess";

        /**
         * 支付宝订单退款服务处理
         */
        public static final String ALI_ORDER_REFUND = "handleAlipayRefundOrder";

        /**
         * 银联支付订单退款服务处理
         */
        public static final String UNION_ORDER_REFUND = "handleUnionRefundOrder";

        //通联订单回调
        public static final String TAG_ALLIN_PAY_ORDER_NOTIFY = "allin_pay_order_notify";
        //通联退款回调
        public static final String TAG_ALLIN_REFUND_NOTIFY = "allin_refund_notify";
        //拉卡拉聚合扫码-交易通知
        public static final String TAG_LKL_TRADE_NOTIFY = "lkl_trade_notify";

        /**
         * 订单补充信息
         */
        public static final String ORDER_SUPPLY_INFO_TAG = "order_supply_info";

        /**
         * 营销订单自动退款
         */
        public static final String PROMOTION_ORDER_REFUND_TAG = "promotion_order_refund";

        /**
         * 订单自动审核处理
         */
        public static final String TAG_ORDER_AUTO_AUDIT = "orderAutoAudit";


    }

    @UtilityClass
    public static final class MqGroupType {
        public static final String GID_REFUND_WX_ORDER = "GID_refundOrderServiceProcess";
        public static final String GID_REFUND_ALI_ORDER = "GID_handleAlipayRefundOrder";
        /**
         * 微信h5支付订单处理
         */
        public static final String GROUP_WX_H5_PAY_ORDER_NOTIFY = "GID_wxH5PayOrderNotifyProcess";
        /**
         * 支付订单处理
         */
        public static final String GROUP_PAY_ORDER_NOTIFY = "GID_payOrderNotifyProcess";
        /**
         * 订单扩展信息
         */
        public static final String GROUP_ORDER_INFO_EXT = "GID_orderInfoExtProcess";
        /**
         * 订单开通服务处理
         */
        public static final String GROUP_OPEN_ORDER_SERVICE = "GID_openOrderServiceProcess";
        /**
         * 支付订单站内信处理
         */
        public static final String GROUP_SEND_PAY_AGENT = "GID_sendPayAgentNotify";
        /**
         * 订单模板消息处理
         */
        public static final String GROUP_SEND_PAY_SUCCESS_MSG = "GID_sendPaySuccessMsgProcess";
        /**
         * 营销订单自动退款
         */
        public static final String GROUP_PROMOTION_ORDER_REFUND = "GID_promotionOrderRefund";
        /**
         * 订单自动审核处理
         */
        public static final String GROUP_ORDER_AUTO_AUDIT = "GID_orderAutoAudit";

        //通联订单回调
        public static final String GROUP_ALLIN_PAY_ORDER_NOTIFY = "GID_allinPayOrderNotify";
        //通联退款结果回调
        public static final String GROUP_ALLIN_REFUND_NOTIFY = "GID_allinRefundNotify";

        //拉卡拉聚合扫码-交易通知
        public static final String GROUP_LKL_TRADE_NOTIFY = "GID_lklTradeNotify";
    }

}

package cn.shrise.radium.orderservice.resp;

import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.orderservice.entity.RsArticleCourse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FullArticleCourse {

    private RsArticleCourse article;

    private ArticleSeries series;

    private List<String> codeList;
}

package cn.shrise.radium.orderservice.entity;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@Builder
@Getter
@Setter
@Entity
@Table(name = "rs_order_invoice_request_record", schema = "resource_db", indexes = {
        @Index(name = "idx_order_id", columnList = "order_id")
})
@NoArgsConstructor
@AllArgsConstructor
public class RsOrderInvoiceRequestRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "order_id")
    private Integer orderId;

    @Lob
    @Column(name = "msg_body")
    private String msgBody;

    @Lob
    @Column(name = "request_result")
    private String requestResult;

}
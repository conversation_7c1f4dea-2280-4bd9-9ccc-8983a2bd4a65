package cn.shrise.radium.orderservice.properties.vip;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VipPackageStyle {

    private String number;

    private String type;

    private Boolean hot = false;

    private List<String> tags = Collections.emptyList();

    private String title;

    private String cover;

    private String pcCover;

    private String tagColor;

    private String pcVipTitleImgUrl;
}

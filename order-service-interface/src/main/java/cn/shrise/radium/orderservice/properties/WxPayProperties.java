package cn.shrise.radium.orderservice.properties;

import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayProperties {

    private Map<Integer, Config> configs;

    private Map<Integer, Integer> atToMch;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Config {
        /**
         * 设置微信公众号或者小程序等的appid
         */
        private String appId;

        /**
         * 微信支付商户号
         */
        private String mchId;

        /**
         * 微信支付商户密钥
         */
        private String mchKey;

        /**
         * apiclient_cert.p12证书文件的绝对路径，或者如果放在项目中，请以classpath:开头指定
         */
        private String keyPath;

        /**
         * 支付回调地址
         */
        private String payNotifyUrl;

        /**
         * 退款回调地址
         */
        private String refundNotifyUrl;
    }

    /**
     * 通过公众号找商户信息
     * @param at
     * @return
     */
    public WxPayProperties.Config getPayPropertiesByAt(Integer at){
        if (this.atToMch.containsKey(at)){
            int mt = this.atToMch.get(at);
            return getPayPropertiesByMchType(mt);
        } else {
            throw new BusinessException(OsErrorCode.NOT_FOUND_MCHTYPE.getCode(), "未找到商户号, 公众号at: " + at);
        }
    }

    /**
     * 通过商户号找商户信息
     * @param mt
     * @return
     */
    public WxPayProperties.Config getPayPropertiesByMchType(Integer mt){
        if (this.getConfigs().containsKey(mt)){
            return this.getConfigs().get(mt);
        } else {
            throw new BusinessException(OsErrorCode.NOT_FOUND_MCHTYPE.getCode(), "未找到商户号, 商户号mt: " + mt);
        }
    }
}

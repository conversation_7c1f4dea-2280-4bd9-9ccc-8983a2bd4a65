package cn.shrise.radium.userservice.service;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.userservice.dto.DepartmentRankPlanDto;
import cn.shrise.radium.userservice.entity.UcDepartmentRankPlan;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class DepartmentRankPlanServiceTest {

    @Autowired
    DepartmentRankPlanService service;

    @Test
    void findAllByFilter() {
        PageResult<List<UcDepartmentRankPlan>> res = service.findAllByFilter(45, true, null, 1, 10);
        System.out.println(res);
    }

    @Test
    void findAllDetailByFilter(){
        PageResult<List<DepartmentRankPlanDto>> res = service.findAllDetailByFilter(45, true, null, 1, 10);
        System.out.println(res);
    }
}
package cn.shrise.radium.userservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.userservice.constant.WxChangeType;
import cn.shrise.radium.userservice.dto.UserWxExtDto;
import cn.shrise.radium.userservice.dto.WxDTO;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.repository.UcWxChangeRecordRepository;
import cn.shrise.radium.userservice.repository.WxAccountRepository;
import cn.shrise.radium.userservice.repository.WxExtRepository;
import cn.shrise.radium.userservice.resp.WxAccountResp;
import cn.shrise.radium.wxservice.entity.QUcCustomerExt;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WxService {

    private final JPAQueryFactory queryFactory;
    private final WxExtRepository ucWxExtRepository;
    private final WxAccountRepository wxAccountRepository;
    private final UcWxChangeRecordRepository wxChangeRecordRepository;

    private final WxExtRepository extRepository;
    private final QUcWxAccount qUcWxAccount = QUcWxAccount.ucWxAccount;
    private final QUcWxExt qUcWxExt = QUcWxExt.ucWxExt;
    private final QUcUsers qUcUsers = QUcUsers.ucUsers;
    private final QUcCustomerExt qUcCustomerExt = QUcCustomerExt.ucCustomerExt;

    public Optional<WxDTO> getWxInfo(Integer accountType, String unionId) {
        Tuple record = queryFactory
                .select(qUcWxAccount, qUcWxExt)
                .from(qUcWxAccount).leftJoin(qUcWxExt)
                .on(qUcWxAccount.wxId.eq(qUcWxExt.id))
                .where(qUcWxAccount.accountType.eq(accountType))
                .where(qUcWxExt.unionId.eq(unionId))
                .fetchOne();

        if (Objects.isNull(record)) {
            return Optional.empty();
        }
        return Optional.of(new WxDTO(record.get(qUcWxAccount), record.get(qUcWxExt)));
    }

    public Optional<UcWxExt> getWxExt(Integer companyType, String unionId) {
        UcWxExt ucWxExt = queryFactory
                .selectFrom(qUcWxExt)
                .where(qUcWxExt.companyType.eq(companyType))
                .where(qUcWxExt.unionId.eq(unionId))
                .fetchOne();
        return Optional.ofNullable(ucWxExt);
    }

    public Optional<UcWxExt> findByUnionId(Integer companyType, String unionId) {
        QUcWxExt qUcWxExt = QUcWxExt.ucWxExt;
        BooleanBuilder builder = new BooleanBuilder();

        if (ObjectUtil.isNotEmpty(companyType)) {
            builder.and(qUcWxExt.companyType.eq(companyType));
        }
        if (ObjectUtil.isNotEmpty(unionId)) {
            builder.and(qUcWxExt.unionId.eq(unionId));
        }

        return ucWxExtRepository.findOne(builder);
    }

    public Optional<UcWxExt> findByUserId(Integer companyType, Integer userId) {
        QUcWxExt qUcWxExt = QUcWxExt.ucWxExt;
        BooleanBuilder builder = new BooleanBuilder();

        if (ObjectUtil.isNotEmpty(companyType)) {
            builder.and(qUcWxExt.companyType.eq(companyType));
        }
        if (ObjectUtil.isNotEmpty(userId)) {
            builder.and(qUcWxExt.userId.eq(userId));
        }

        return ucWxExtRepository.findOne(builder);
    }

    public UcWxExt getWxExtInfo(Integer userId) {
        return extRepository.findByUserId(userId);
    }

    public Optional<UcWxExt> findById(Integer wxId) {
        return ucWxExtRepository.findById(wxId);
    }

    public List<UcWxAccount> findAccountByWxId(Integer accountType, Integer wxId, Set<Integer> wxIds) {
        JPAQuery<UcWxAccount> query = queryFactory.select(qUcWxAccount)
                .from(qUcWxAccount);
        if (ObjectUtil.isNotNull(accountType)) {
            query = query.where(qUcWxAccount.accountType.eq(accountType));
        }
        if (ObjectUtil.isNotNull(wxId)) {
            query = query.where(qUcWxAccount.wxId.eq(wxId));
        }
        if (ObjectUtil.isNotNull(wxIds)) {
            query = query.where(qUcWxAccount.wxId.in(wxIds));
        }

        return query.fetch();
    }

    public Optional<UcWxAccount> findOneByOpenId(Integer accountType, String openId) {
        QUcWxAccount qUcWxAccount = QUcWxAccount.ucWxAccount;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUcWxAccount.accountType.eq(accountType));
        builder.and(qUcWxAccount.openId.eq(openId));

        return wxAccountRepository.findOne(builder);
    }

    public List<UcWxAccount> findAllByWxId(Integer wxId) {
        QUcWxAccount qUcWxAccount = QUcWxAccount.ucWxAccount;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUcWxAccount.wxId.eq(wxId));

        Iterable<UcWxAccount> accountIterable = wxAccountRepository.findAll(builder);
        List<UcWxAccount> accountList = new ArrayList<>();
        accountIterable.forEach(accountList::add);
        return accountList;
    }

    public List<WxDTO> filterByUserIds(Integer accountType, Collection<Integer> userIds) {
        List<Tuple> query = queryFactory.select(qUcWxExt, qUcWxAccount)
                .from(qUcWxExt)
                .leftJoin(qUcWxAccount)
                .on(qUcWxAccount.wxId.eq(qUcWxExt.id))
                .where(qUcWxExt.userId.in(userIds).and(qUcWxAccount.accountType.eq(accountType)))
                .fetch();
        List<WxDTO> infoList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(query)) {
            for (Tuple tuple : query) {
                WxDTO dto = new WxDTO(tuple.get(qUcWxAccount), tuple.get(qUcWxExt));
                infoList.add(dto);
            }
        }
        return infoList;
    }

    public UserWxAccount findByOpenId(Integer accountType, String openId) {
        QUcUsers ucUsers = QUcUsers.ucUsers;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUcWxAccount.accountType.eq(accountType));
        builder.and(qUcWxAccount.openId.eq(openId));
        Tuple fetch = queryFactory.select(ucUsers, qUcWxExt, qUcWxAccount.openId, qUcWxAccount.subscribeScene)
                .from(qUcWxAccount)
                .leftJoin(qUcWxExt)
                .on(qUcWxAccount.wxId.eq(qUcWxExt.id))
                .leftJoin(ucUsers)
                .on(qUcWxExt.userId.eq(ucUsers.id))
                .where(builder)
                .fetchOne();
        if (fetch != null) {
            return UserWxAccount.builder()
                    .users(fetch.get(ucUsers))
                    .wxExt(fetch.get(qUcWxExt))
                    .openId(fetch.get(qUcWxAccount.openId))
                    .subscribeScene(fetch.get(qUcWxAccount.subscribeScene))
                    .build();
        }
        return null;
    }

    public List<WxAccountResp> filterByUserIds(Integer accountType, Set<Integer> userIds) {
        List<Tuple> query = queryFactory.select(qUcWxExt.id, qUcWxExt.userId, qUcWxAccount.openId)
                .from(qUcWxExt)
                .leftJoin(qUcWxAccount)
                .on(qUcWxAccount.wxId.eq(qUcWxExt.id))
                .where(qUcWxExt.userId.in(userIds).and(qUcWxAccount.accountType.eq(accountType)))
                .fetch();
        List<WxAccountResp> infoList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(query)) {
            infoList = query.stream().map(i -> WxAccountResp.builder()
                            .wxId(i.get(qUcWxExt.id))
                            .userId(i.get(qUcWxExt.userId))
                            .openId(i.get(qUcWxAccount.openId)).build())
                    .collect(Collectors.toList());
        }
        return infoList;
    }

    public List<UserWxExtDto> filterWxExtByUserIds(Set<Integer> userIds) {
        List<Tuple> records = queryFactory.select(qUcUsers, qUcWxExt)
                .from(qUcUsers)
                .leftJoin(qUcWxExt)
                .on(qUcUsers.id.eq(qUcWxExt.userId))
                .where(qUcUsers.id.in(userIds))
                .fetch();
        return records.stream().map(i -> UserWxExtDto.builder()
                .ucUsers(i.get(qUcUsers))
                .ucWxExt(i.get(qUcWxExt))
                .build()).collect(Collectors.toList());
    }

    /**
     * 根据用户解绑微信
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<String> unbindWx(Integer wxId) {

        Integer userId = null;
        queryFactory.update(qUcWxExt)
                .where(qUcWxExt.id.eq(wxId))
                .set(qUcWxExt.userId, userId)
                .execute();

        return BaseResult.success();
    }

    @Transactional
    public void emptyUserId(Integer userId, Integer wxId, Integer operatorId, Integer productType) {

        JPAQuery<UcWxExt> query = queryFactory.selectFrom(qUcWxExt);
        JPAUpdateClause jpaUpdateClause = queryFactory.update(qUcWxExt)
                .setNull(qUcWxExt.userId);
        if (ObjectUtil.isNotNull(userId)) {
            query.where(qUcWxExt.userId.eq(userId));
            jpaUpdateClause.where(qUcWxExt.userId.eq(userId));
        } else if (ObjectUtil.isNotNull(wxId)) {
            query.where(qUcWxExt.id.eq(wxId));
            jpaUpdateClause.where(qUcWxExt.id.eq(wxId));
        }
        UcWxExt wxExt = query.fetchOne();
        jpaUpdateClause.execute();
        UcWxChangeRecord record = UcWxChangeRecord.builder()
                .customerId(ObjectUtil.isNotEmpty(wxExt.getUserId()) ? wxExt.getUserId() : null)
                .changeType(WxChangeType.UN_BIND)
                .productType(productType)
                .wxId(wxExt.getId())
                .operatorId(operatorId)
                .build();
        wxChangeRecordRepository.save(record);

    }

    public BaseResult<List<UcWxExt>> batchWxExtByUnionId(Integer companyType, Collection<String> unionIdList) {
        List<UcWxExt> fetch = queryFactory.selectFrom(qUcWxExt)
                .where(qUcWxExt.companyType.eq(companyType))
                .where(qUcWxExt.unionId.in(unionIdList))
                .fetch();
        return BaseResult.success(fetch);
    }

    public BaseResult<Map<String, Boolean>> batchGetTradedUnion(Collection<String> unionIdList, Boolean isTraded, Integer companyType) {
        JPAQuery<Tuple> query = queryFactory.select(qUcWxExt.unionId, qUcCustomerExt.isTraded)
                .from(qUcWxExt)
                .leftJoin(qUcCustomerExt)
                .on(qUcWxExt.userId.eq(qUcCustomerExt.id))
                .where(qUcWxExt.companyType.eq(companyType))
                .where(qUcWxExt.unionId.in(unionIdList))
                .where(qUcWxExt.unionId.isNotNull())
                .where(qUcWxExt.userId.isNotNull());
        List<Tuple> fetch = query.fetch();
        Map<String, Boolean> map = null;
        if (ObjectUtil.isNotEmpty(isTraded)) {
            if (isTraded) {
                map = fetch.stream().filter(e -> ObjectUtil.equal(e.get(qUcCustomerExt.isTraded), isTraded)).collect(Collectors.toMap(k -> k.get(qUcWxExt.unionId), v -> v.get(qUcCustomerExt.isTraded) == null ? false : v.get(qUcCustomerExt.isTraded)));
            } else {
                map = fetch.stream().filter(e -> !ObjectUtil.equal(e.get(qUcCustomerExt.isTraded), true)).collect(Collectors.toMap(k -> k.get(qUcWxExt.unionId), v -> v.get(qUcCustomerExt.isTraded) == null ? false : v.get(qUcCustomerExt.isTraded)));
            }
        } else {
            map = fetch.stream().collect(Collectors.toMap(k -> k.get(qUcWxExt.unionId), v -> v.get(qUcCustomerExt.isTraded) == null ? false : v.get(qUcCustomerExt.isTraded)));
        }
        return BaseResult.success(map);
    }

    public UcWxAccount findAccountInfoByUnionId(String unionId) {
        return queryFactory.selectFrom(qUcWxAccount).leftJoin(qUcWxExt).on(qUcWxAccount.wxId.eq(qUcWxExt.id))
                .where(qUcWxExt.unionId.eq(unionId)).fetchOne();
    }
}

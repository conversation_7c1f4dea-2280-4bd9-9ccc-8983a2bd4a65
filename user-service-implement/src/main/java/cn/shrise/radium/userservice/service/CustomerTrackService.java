package cn.shrise.radium.userservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.userservice.dto.CustomerTrackEventSummaryDto;
import cn.shrise.radium.userservice.entity.UcCustomerTrackEventSummary;
import cn.shrise.radium.userservice.req.CustomerTrackEventSummaryReq;
import cn.shrise.radium.userservice.util.MetaClassUtil;
import cn.shrise.radium.userservice.util.TrackEventRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerTrackService {

    private final JdbcTemplate jdbcTemplate;
    private final TrackEventRedisUtils trackEventRedisUtils;


    public PageResult<List<CustomerTrackEventSummaryDto>> getCustomerTrackEventSummary(CustomerTrackEventSummaryReq req) {
        String sql = "from auth_db.uc_customer_track_event_summary as summary use index (idx_app_page_sales_access) " +
                "left outer join auth_db.uc_user_wwx_sales_relation as relation on (relation.wx_id = summary.wx_id and relation.user_id = summary.user_id) " +
                "where summary.company_type = :companyType " +
                "and relation.sales_id in (:relationSales) " +
                "and relation.enabled = 1 " +
                "and summary.sales_id in (:summarySales) " +
                "and summary.app_id = :appId " +
                "and summary.page_id = :pageId ";

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (ObjectUtil.isNotEmpty(req.getStartTime())) {
            sql += "and summary.access_time >= '" + req.getStartTime().format(df) + "' ";
        }
        if (ObjectUtil.isNotEmpty(req.getEndTime())) {
            sql += "and summary.access_time < '" + req.getEndTime().format(df) + " 23:59:59' ";
        }
        if (ObjectUtil.isNotEmpty(req.getIsTraded()) && req.getIsTraded()) {
            sql += "and summary.user_id not in (SELECT ext.UID from auth_db.uc_customer_ext as ext where ext.IsTraded = 1) ";
        }

        String sqlDetail = "SELECT distinct summary.* " + sql + " order by summary.access_time desc limit :limit offset :offset ";
        String sqlCount = "SELECT count(distinct summary.id) " + sql;

        Map<String, Object> args = new HashMap<>();
        args.put("companyType", req.getCompanyType());
        args.put("relationSales", req.getSalesIdList());
        args.put("appId", req.getAppId());
        args.put("pageId", req.getPageId());
        args.put("limit", req.getSize());
        args.put("offset", (req.getCurrent() - 1) * req.getSize());
        if (req.getIsOpen()) {
            // 应用公开
            args.put("summarySales", -1);
        } else {
            // 应用非公开
            args.put("summarySales", req.getSalesIdList());
        }

        NamedParameterJdbcTemplate givenParamJdbcTemp = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<Map<String, Object>> fetch = givenParamJdbcTemp.queryForList(sqlDetail, args);
        List<UcCustomerTrackEventSummary> results = fetch.stream().map(e -> {
            try {
                return MetaClassUtil.mapToObj(e, UcCustomerTrackEventSummary.class);
            } catch (Exception ex) {
                return null;
            }
        }).collect(Collectors.toList());

        Long count = givenParamJdbcTemp.queryForList(sqlCount, args, Long.class).get(0);
        List<CustomerTrackEventSummaryDto> dtoList = results.stream().map(f -> CustomerTrackEventSummaryDto.builder().trackEventSummary(f)
                .build()).collect(Collectors.toList());

        if (ObjectUtil.isNotEmpty(dtoList)) {
            List<Integer> userIdList = dtoList.stream().filter(e -> ObjectUtil.isAllNotEmpty(e.getTrackEventSummary(), e.getTrackEventSummary().getUserId())).map(e -> e.getTrackEventSummary().getUserId()).collect(Collectors.toList());
            Map<Integer, String> onLinePageMap = getUserOnLine(userIdList, req.getAppId());
            dtoList.stream().forEach(dto -> {
                dto.setOnLine(onLinePageMap.getOrDefault(dto.getTrackEventSummary().getUserId(), null));
            });
        }
        return PageResult.success(dtoList, Pagination.of(req.getCurrent(), req.getSize(), count));
    }

    private Map<Integer, String> getUserOnLine(List<Integer> wxIdList, String appId) {

        Map<Integer, String> map = new HashMap<>();
        wxIdList.forEach(wxId -> {
            String userPage = trackEventRedisUtils.getUserPage(wxId, appId);
            map.put(wxId, userPage);
        });
        return map;
    }
}

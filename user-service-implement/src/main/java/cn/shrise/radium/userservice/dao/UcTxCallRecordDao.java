package cn.shrise.radium.userservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.secureservice.SecureServiceClient;
import cn.shrise.radium.userservice.constant.CallStatusConstant;
import cn.shrise.radium.userservice.dto.TxCallRecordRespDto;
import cn.shrise.radium.userservice.entity.QUcTxCallRecord;
import cn.shrise.radium.userservice.entity.QUcTxCallSeatInfo;
import cn.shrise.radium.userservice.entity.QUcUsers;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.SalesCallStatisticsResp;
import cn.shrise.radium.userservice.resp.call.TxCallRecordResp;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class UcTxCallRecordDao {

    private final JPAQueryFactory queryFactory;
    private final JPAQueryFactory jpaQueryAdbLakeHouse;
    private final QUcTxCallRecord qUcTxCallRecord = QUcTxCallRecord.ucTxCallRecord;
    private final QUcTxCallSeatInfo qUcTxCallSeatInfo = QUcTxCallSeatInfo.ucTxCallSeatInfo;
    private final QUcUsers ucUsers = QUcUsers.ucUsers;
    private final SecureServiceClient secureServiceClient;

    public PageResult<List<TxCallRecordRespDto>> findAllByFilter(Long seatId, Integer serverId, Long mobileId, Boolean isCallIn, Integer callStatus,
                                                                 LocalDateTime startTime, LocalDateTime endTime,
                                                                 String searchText, Integer current, Integer size) {
        JPAQuery<TxCallRecordRespDto> query = queryFactory.select(
                Projections.bean(TxCallRecordRespDto.class,
                        qUcTxCallRecord.startTime.as("callTime"),
                        qUcTxCallSeatInfo.staffNumber.as("workNumber"),
                        qUcTxCallSeatInfo.name,
                        qUcTxCallRecord.customerMobile,
                        qUcTxCallRecord.mobileId,
                        qUcTxCallRecord.serverId,
                        qUcTxCallRecord.isCallIn,
                        qUcTxCallRecord.callStatus,
                        qUcTxCallRecord.customRecordUrl,
                        qUcTxCallRecord.seatId,
                        qUcTxCallRecord.id.as("callId"),
                        qUcTxCallRecord.seatPhone.as("phone"),
                        qUcTxCallRecord.mobileId))
                .from(qUcTxCallRecord)
                .leftJoin(qUcTxCallSeatInfo).on(qUcTxCallRecord.seatId.eq(qUcTxCallSeatInfo.id));

        if (ObjectUtil.isNotEmpty(serverId)) {
            query.where(qUcTxCallRecord.serverId.eq(serverId));
        }
        if (ObjectUtil.isNotEmpty(seatId)) {
            query.where(qUcTxCallRecord.seatId.eq(seatId));
        }
        if (ObjectUtil.isNotEmpty(mobileId)) {
            query.where(qUcTxCallRecord.mobileId.eq(mobileId));
        }
        if (ObjectUtil.isNotEmpty(callStatus)) {
            query.where(qUcTxCallRecord.callStatus.eq(callStatus));
        }
        if (ObjectUtil.isNotEmpty(isCallIn)) {
            query.where(qUcTxCallRecord.isCallIn.eq(isCallIn));
        }
        if (ObjectUtil.isNotEmpty(startTime)) {
            query.where(qUcTxCallRecord.startTime.goe(startTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (ObjectUtil.isNotEmpty(endTime)) {
            query.where(qUcTxCallRecord.startTime.lt(endTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (ObjectUtil.isNotEmpty(searchText)) {
            Long mobileIdSearch = null;
            if (PhoneUtil.isMobile(searchText)) {
                mobileIdSearch = secureServiceClient.getMobileId(searchText).getData();
            } else if (DesensitizeUtil.isValidUserCode(searchText)) {
                int userId = DesensitizeUtil.maskToId(searchText);
                UcUsers users = queryFactory.selectFrom(ucUsers).where(ucUsers.id.eq(userId)).where(ucUsers.mobileId.isNotNull()).fetchOne();
                if (users != null) {
                    mobileIdSearch = users.getMobileId();
                }
            }
            if (ObjectUtil.isNotNull(mobileIdSearch)) {
                query.where(qUcTxCallSeatInfo.name.eq(searchText)
                        .or(qUcTxCallSeatInfo.staffNumber.eq(searchText))
                        .or(qUcTxCallRecord.seatPhone.eq(searchText))
                        .or(qUcTxCallRecord.mobileId.eq(mobileIdSearch)));

            } else {
                query.where(qUcTxCallSeatInfo.name.eq(searchText)
                        .or(qUcTxCallSeatInfo.staffNumber.eq(searchText))
                        .or(qUcTxCallRecord.seatPhone.eq(searchText))
                        .or(qUcTxCallRecord.customerMobile.eq(searchText)));
            }

        }

        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();

        query.orderBy(qUcTxCallRecord.startTime.desc(), qUcTxCallRecord.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        List<TxCallRecordRespDto> records = query.fetch();
        return PageResult.success(records, Pagination.of(current, size, total));
    }

    public List<SalesCallStatisticsResp> getSalesCallStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        List<Tuple> fetch = jpaQueryAdbLakeHouse.select(qUcTxCallRecord.serverId,
                qUcTxCallRecord.id.count(),
                qUcTxCallRecord.duration.sum())
                .from(qUcTxCallRecord)
                .where(qUcTxCallRecord.startTime.goe(startTime.atZone(ZoneId.systemDefault()).toInstant()))
                .where(qUcTxCallRecord.startTime.lt(endTime.atZone(ZoneId.systemDefault()).toInstant()))
                .where(qUcTxCallRecord.callStatus.eq(CallStatusConstant.CS_Received))
                .groupBy(qUcTxCallRecord.serverId)
                .fetch();
        List<SalesCallStatisticsResp> salesCallStatisticsResps = new ArrayList<>();
        if (!ObjectUtils.isEmpty(fetch)) {
            for (Tuple tuple : fetch) {
                SalesCallStatisticsResp salesCallStatisticsResp = new SalesCallStatisticsResp(tuple.get(qUcTxCallRecord.serverId) == null ? 0 : tuple.get(qUcTxCallRecord.serverId), tuple.get(qUcTxCallRecord.id.count()), tuple.get(qUcTxCallRecord.duration.sum()).longValue());
                salesCallStatisticsResps.add(salesCallStatisticsResp);
            }
        }
        return salesCallStatisticsResps;
    }
    public TxCallRecordResp findRecordByCallId(Long callId) {
        JPAQuery<TxCallRecordResp> query = queryFactory.select(
                        Projections.bean(TxCallRecordResp.class,
                                qUcTxCallRecord.startTime.as("callTime"),
                                qUcTxCallSeatInfo.staffNumber.as("workNumber"),
                                qUcTxCallSeatInfo.name,
                                qUcTxCallRecord.customerMobile,
                                qUcTxCallRecord.serverId,
                                qUcTxCallRecord.isCallIn,
                                qUcTxCallRecord.callStatus,
                                qUcTxCallRecord.mobileId,
                                qUcTxCallRecord.customRecordUrl,
                                qUcTxCallRecord.id.as("callId"),
                                qUcTxCallRecord.seatPhone.as("phone")))
                .from(qUcTxCallRecord)
                .leftJoin(qUcTxCallSeatInfo).on(qUcTxCallRecord.seatId.eq(qUcTxCallSeatInfo.id));
        query.where(qUcTxCallRecord.id.eq(callId));

        return query.fetchOne();
    }

}

package cn.shrise.radium.userservice.conf;

import cn.shrise.radium.common.properties.AccessKeyOtherProperties;
import cn.shrise.radium.common.properties.AliyunProperties;
import com.aliyun.opensearch.OpenSearchClient;
import com.aliyun.opensearch.SearcherClient;
import com.aliyun.opensearch.sdk.generated.OpenSearch;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(AccessKeyOtherProperties.class)
public class OpenSearchAutoConfiguration {

    private final AliyunProperties aliyunProperties;
    private final AccessKeyOtherProperties accessKeyOtherProperties;

    /**
     * 创建OpenSearchClient对象
     * @return
     */
    @Bean
    @ConditionalOnMissingBean
    public OpenSearchClient openSearchClient() {
        OpenSearch openSearch = new OpenSearch();
        openSearch.setAccessKey(accessKeyOtherProperties.getAccessKeyId());
        openSearch.setSecret(accessKeyOtherProperties.getAccessKeySecret());
        openSearch.setHost(aliyunProperties.getOpenSearch().getEndpoint());

        return new OpenSearchClient(openSearch);
    }

    /**
     * 创建SearcherClient对象
     * @return
     */
    @Bean
    @ConditionalOnMissingBean
    public SearcherClient searcherClient() {
        return new SearcherClient(openSearchClient());
    }
}

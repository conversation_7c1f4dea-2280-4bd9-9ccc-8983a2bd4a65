package cn.shrise.radium.userservice.repository;

import cn.shrise.radium.userservice.entity.UcVerifyCaptchaRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * <AUTHOR>
 */
public interface UcVerifyCaptchaRecordRepository extends JpaRepository<UcVerifyCaptchaRecord, Long>, QuerydslPredicateExecutor<UcVerifyCaptchaRecord> {
}

package cn.shrise.radium.userservice.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.repository.CustomerTagGroupRepository;
import cn.shrise.radium.userservice.repository.CustomerTagRecordRepository;
import cn.shrise.radium.userservice.repository.CustomerTagRepository;
import cn.shrise.radium.userservice.req.CreateTagGroupReq;
import cn.shrise.radium.userservice.req.CreateTagReq;
import cn.shrise.radium.userservice.resp.CustomerTagRecordResp;
import cn.shrise.radium.userservice.resp.CustomerTagResp;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.PersistenceException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.userservice.constant.UserErrorCode.TAG_EXISTED;
import static cn.shrise.radium.userservice.constant.UserErrorCode.TAG_GROUP_EXISTED;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerTagService {

    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;
    private final CustomerTagRepository customerTagRepository;
    private final CustomerTagGroupRepository customerTagGroupRepository;
    private final CustomerTagRecordRepository customerTagRecordRepository;

    private final QUcCustomerTag qUcCustomerTag = QUcCustomerTag.ucCustomerTag;
    private final QUcCustomerTagGroup qUcCustomerTagGroup = QUcCustomerTagGroup.ucCustomerTagGroup;
    private final QUcCustomerTagRelation qUcCustomerTagRelation = QUcCustomerTagRelation.ucCustomerTagRelation;

    private final QUcCustomerTagRecord qUcCustomerTagRecord = QUcCustomerTagRecord.ucCustomerTagRecord;

    private final QUcCustomerTagGroupRoleRelation relation = QUcCustomerTagGroupRoleRelation.ucCustomerTagGroupRoleRelation;

    public Optional<UcCustomerTag> getCustomerTag(Integer id) {
        return customerTagRepository.findById(id);
    }

    public List<UcCustomerTag> getCustomerTagList(Integer userId) {
        JPAQuery<UcCustomerTag> query = queryFactory.select(qUcCustomerTag).from(qUcCustomerTagRelation)
                .leftJoin(qUcCustomerTag)
                .on(qUcCustomerTagRelation.tagId.eq(qUcCustomerTag.id))
                .leftJoin(qUcCustomerTagGroup)
                .on(qUcCustomerTag.groupId.eq(qUcCustomerTagGroup.id))
                .where(qUcCustomerTagRelation.userId.eq(userId))
                .where(qUcCustomerTagRelation.enabled.eq(true))
                .where(qUcCustomerTagGroup.enabled.eq(true))
                .where(qUcCustomerTag.enabled.eq(true));
        return query.fetch();
    }

    public List<UcCustomerTag> getCustomerTagList(Integer groupId, Boolean groupEnabled, Boolean tagEnabled) {
        JPAQuery<UcCustomerTag> query = queryFactory.selectFrom(qUcCustomerTag)
                .leftJoin(qUcCustomerTagGroup)
                .on(qUcCustomerTag.groupId.eq(qUcCustomerTagGroup.id))
                .where(qUcCustomerTag.groupId.eq(groupId));

        if (ObjectUtils.isNotEmpty(groupEnabled)) {
            query.where(qUcCustomerTagGroup.enabled.eq(groupEnabled));
        }
        if (ObjectUtils.isNotEmpty(tagEnabled)) {
            query.where(qUcCustomerTag.enabled.eq(tagEnabled));
        }
        return query.fetch();
    }

    public Optional<UcCustomerTagGroup> getCustomerTagGroup(Integer id) {
        return customerTagGroupRepository.findById(id);
    }

    public Optional<UcCustomerTagGroup> getCustomerTagGroup(Integer companyType, String name) {
        return customerTagGroupRepository.findByCompanyTypeAndName(companyType, name);
    }

    public List<UcCustomerTagGroup> getCustomerTagGroupList(Integer companyType, Boolean isGlobal, Boolean isUnion,
                                                            Boolean isRadio, Boolean enabled) {
        JPAQuery<UcCustomerTagGroup> query = queryFactory.selectFrom(qUcCustomerTagGroup)
                .where(qUcCustomerTagGroup.companyType.eq(companyType));

        if (ObjectUtils.isNotEmpty(enabled)) {
            query.where(qUcCustomerTagGroup.enabled.eq(enabled));
        }

        if (ObjectUtils.isNotEmpty(isGlobal)) {
            query.where(qUcCustomerTagGroup.isGlobal.eq(isGlobal));
        }

        if (ObjectUtils.isNotEmpty(isUnion)) {
            query.where(qUcCustomerTagGroup.isUnion.eq(isUnion));
        }

        if (ObjectUtils.isNotEmpty(isRadio)) {
            query.where(qUcCustomerTagGroup.isRadio.eq(isRadio));
        }

        return query.fetch();
    }

    public List<CustomerTagResp> getGroupTagList(Integer companyType, Long roleId, Boolean isGlobal, Boolean isRadio, Boolean groupEnabled, Boolean tagEnable) {
        JPAQuery<CustomerTagResp> query = queryFactory.select(Projections.bean(CustomerTagResp.class,
                        qUcCustomerTagGroup.id.as("id"),
                        qUcCustomerTagGroup.name.as("name"),
                        qUcCustomerTagGroup.isRadio.as("isRadio"),
                        qUcCustomerTagGroup.enabled.as("enabled")))
                .from(qUcCustomerTagGroup)
                .where(qUcCustomerTagGroup.companyType.eq(companyType));

        if (ObjectUtils.isNotEmpty(groupEnabled)) {
            query.where(qUcCustomerTagGroup.enabled.eq(groupEnabled));
        }

        if (ObjectUtils.isNotEmpty(isGlobal)) {
            query.where(qUcCustomerTagGroup.isGlobal.eq(isGlobal));
        }

        if (ObjectUtils.isNotEmpty(isRadio)) {
            query.where(qUcCustomerTagGroup.isRadio.eq(isRadio));
        }

        if (ObjectUtils.isNotEmpty(roleId)) {
            List<UcCustomerTagGroupRoleRelation> relationList = getCustomerTagGroupByRole(roleId);
            if (CollectionUtil.isNotEmpty(relationList)) {
                Set<Integer> groupSet = relationList.stream().map(UcCustomerTagGroupRoleRelation::getGroupId)
                        .collect(Collectors.toSet());
                query.where(qUcCustomerTagGroup.id.in(groupSet));
            } else {
                return Collections.emptyList();
            }
        }

        List<CustomerTagResp> groupList = query.fetch();

        Set<Integer> groupIdSet = groupList.stream().map(CustomerTagResp::getId).collect(Collectors.toSet());
        Map<Integer, List<UcCustomerTag>> groupTagMap = getGroupTagMap(groupIdSet, tagEnable);

        for (CustomerTagResp customerTagResp : groupList) {
            if (groupTagMap.containsKey(customerTagResp.getId())) {
                customerTagResp.setTagList(groupTagMap.get(customerTagResp.getId()));
            }
        }
        return groupList;
    }

    public Page<CustomerTagResp> getGroupTagPage(Integer companyType, Boolean groupEnabled, Boolean tagEnable, Pageable pageable) {
        JPAQuery<CustomerTagResp> query = queryFactory.select(Projections.bean(CustomerTagResp.class,
                        qUcCustomerTagGroup.id.as("id"),
                        qUcCustomerTagGroup.name.as("name"),
                        qUcCustomerTagGroup.isRadio.as("isRadio"),
                        qUcCustomerTagGroup.enabled.as("enabled")))
                .from(qUcCustomerTagGroup)
                .where(qUcCustomerTagGroup.companyType.eq(companyType));

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUcCustomerTagGroup.companyType.eq(companyType));

        if (ObjectUtils.isNotEmpty(groupEnabled)) {
            query.where(qUcCustomerTagGroup.enabled.eq(groupEnabled));
        }

        Page<UcCustomerTagGroup> groupPage = customerTagGroupRepository.findAll(builder, pageable);

        Set<Integer> groupIdSet = groupPage.getContent().stream().map(UcCustomerTagGroup::getId).collect(Collectors.toSet());
        Map<Integer, List<UcCustomerTag>> groupTagMap = getGroupTagMap(groupIdSet, tagEnable);
        List<CustomerTagResp> customerTagRespList = new ArrayList<>();
        for (UcCustomerTagGroup group : groupPage.getContent()) {
            CustomerTagResp customerTagResp = CustomerTagResp.of(group);
            if (groupTagMap.containsKey(group.getId())) {
                customerTagResp.setTagList(groupTagMap.get(customerTagResp.getId()));
            }
            customerTagRespList.add(customerTagResp);
        }
        return new PageImpl<>(customerTagRespList, pageable, groupPage.getTotalElements());
    }

    private Map<Integer, List<UcCustomerTag>> getGroupTagMap(Set<Integer> groupIdSet, Boolean tagEnable) {
        JPAQuery<UcCustomerTag> query = queryFactory.selectFrom(qUcCustomerTag)
                .where(qUcCustomerTag.groupId.in(groupIdSet));
        if (ObjectUtil.isNotEmpty(tagEnable)) {
            query.where(qUcCustomerTag.enabled.eq(tagEnable));
        }
        return query.fetch()
                .stream()
                .collect(Collectors.groupingBy(UcCustomerTag::getGroupId));
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCustomerTag(Integer companyType, Integer userId, Integer operator, List<Integer> tagList) {

        //组内标签互斥
        Map<Object, Object> tagMap = queryFactory.selectFrom(qUcCustomerTag)
                .leftJoin(qUcCustomerTagGroup)
                .on(qUcCustomerTag.groupId.eq(qUcCustomerTagGroup.id))
                .where(qUcCustomerTagGroup.isRadio.eq(true))
                .where(qUcCustomerTag.id.in(tagList))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(UcCustomerTag::getGroupId))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (CollectionUtil.isNotEmpty(tagMap)) {
            throw new BusinessException("互斥标签组内只能单选");
        }

        List<Integer> userTagList = queryFactory.select(qUcCustomerTagRelation.tagId)
                .from(qUcCustomerTagRelation)
                .where(qUcCustomerTagRelation.enabled.eq(true))
                .where(qUcCustomerTagRelation.userId.eq(userId))
                .fetch();

        List<Integer> deleteList = userTagList.stream()
                .filter(x -> !tagList.contains(x))
                .collect(Collectors.toList());

        List<Integer> addList = tagList.stream()
                .filter(x -> !userTagList.contains(x))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(deleteList)) {
            //修改关系为false
            List<UcCustomerTagRelation> relations = new ArrayList<>();
            List<UcCustomerTagRecord> records = new ArrayList<>();
            for (Integer tagId : deleteList) {
                UcCustomerTagRelation relation = UcCustomerTagRelation
                        .builder()
                        .tagId(tagId)
                        .userId(userId)
                        .enabled(false)
                        .build();
                relations.add(relation);

                UcCustomerTagRecord record = UcCustomerTagRecord
                        .builder()
                        .tagId(tagId)
                        .userId(userId)
                        .operator(operator)
                        .companyType(companyType)
                        .enabled(false)
                        .build();
                records.add(record);
            }
            String relationSql = SqlUtil.onDuplicateKeyUpdateSql(relations);
            if (ObjectUtil.isNotEmpty(relationSql)) {
                jdbcTemplate.execute(relationSql);
            }

            String recordSql = SqlUtil.batchInsertSql(records);
            if (ObjectUtil.isNotEmpty(recordSql)) {
                jdbcTemplate.execute(recordSql);
            }
        }

        if (CollectionUtil.isNotEmpty(addList)) {
            //修改关系为true
            List<UcCustomerTagRelation> relations = new ArrayList<>();
            List<UcCustomerTagRecord> records = new ArrayList<>();
            for (Integer tagId : addList) {
                UcCustomerTagRelation relation = UcCustomerTagRelation
                        .builder()
                        .tagId(tagId)
                        .userId(userId)
                        .enabled(true)
                        .build();
                relations.add(relation);

                UcCustomerTagRecord record = UcCustomerTagRecord
                        .builder()
                        .tagId(tagId)
                        .userId(userId)
                        .operator(operator)
                        .companyType(companyType)
                        .enabled(true)
                        .build();
                records.add(record);
            }
            String relationSql = SqlUtil.onDuplicateKeyUpdateSql(relations);
            if (ObjectUtil.isNotEmpty(relationSql)) {
                jdbcTemplate.execute(relationSql);
            }

            String recordSql = SqlUtil.batchInsertSql(records);
            if (ObjectUtil.isNotEmpty(recordSql)) {
                jdbcTemplate.execute(recordSql);
            }
        }

    }

    @Transactional
    public UcCustomerTagRecord createCustomerTagRecord(Integer companyType, Integer userId, Integer tagId, Boolean enabled) {
        UcCustomerTagRecord entity = UcCustomerTagRecord.builder()
                .userId(userId)
                .tagId(tagId)
                .enabled(enabled)
                .companyType(companyType)
                .build();
        return customerTagRecordRepository.save(entity);
    }

    /**
     * 给用户添加/删除标签
     */
    @Transactional
    public void createOrUpdateCustomerTagRelation(Integer userId, Integer tagId, Boolean enabled) {
        UcCustomerTagRelation relation = UcCustomerTagRelation.builder()
                .tagId(tagId)
                .userId(userId)
                .enabled(enabled)
                .build();
        String sql = SqlUtil.onDuplicateKeyUpdateSql(relation);
        jdbcTemplate.execute(sql);
    }

    /**
     * 给用户添加/删除标签，创建记录
     */
    @Transactional
    public void createOrUpdateCustomerTag(Integer companyType, Integer userId, Integer tagId, Boolean enabled) {
        createOrUpdateCustomerTagRelation(userId, tagId, enabled);
        createCustomerTagRecord(companyType, userId, tagId, enabled);
    }

    @Transactional
    public void addAndRemoveCustomerTag(Integer userId, Integer addTagId, Integer removeTagId) {
        if (addTagId != null) {
            createOrUpdateCustomerTagRelation(userId, addTagId, true);
        }

        if (removeTagId != null) {
            createOrUpdateCustomerTagRelation(userId, removeTagId, false);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public UcCustomerTagGroup createCustomerTagGroup(CreateTagGroupReq req) {
        try {
            UcCustomerTagGroup tagGroup = UcCustomerTagGroup.builder()
                    .name(req.getName())
                    .companyType(req.getCompanyType())
                    .isGlobal(req.getIsGlobal())
                    .isRadio(req.getIsRadio())
                    .enabled(true)
                    .build();
            return customerTagGroupRepository.save(tagGroup);
        } catch (DataIntegrityViolationException e) {
            throw new BusinessException(TAG_GROUP_EXISTED);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCustomerTagGroup(Integer groupId, Boolean isEnabled) {
        queryFactory.update(qUcCustomerTagGroup)
                .set(qUcCustomerTagGroup.enabled, isEnabled)
                .where(qUcCustomerTagGroup.id.eq(groupId))
                .execute();

        queryFactory.update(qUcCustomerTag)
                .set(qUcCustomerTag.enabled, isEnabled)
                .where(qUcCustomerTag.groupId.eq(groupId))
                .execute();
    }

    @Transactional(rollbackFor = Exception.class)
    public void createCustomerTag(CreateTagReq req) {
        if (ObjectUtil.isNotEmpty(req.getGroupName())) {
            try {
                queryFactory.update(qUcCustomerTagGroup)
                        .set(qUcCustomerTagGroup.name, req.getGroupName())
                        .where(qUcCustomerTagGroup.id.eq(req.getGroupId()))
                        .execute();
            } catch (PersistenceException e) {
                throw new BusinessException(TAG_GROUP_EXISTED);
            }
        }


        if (ObjectUtil.isNotEmpty(req.getTagList())) {
            try {
                List<UcCustomerTag> updateList = new ArrayList<>();
                List<UcCustomerTag> addList = new ArrayList<>();
                for (UcCustomerTag tag : req.getTagList()) {
                    tag.setCompanyType(req.getCompanyType());
                    tag.setGroupId(req.getGroupId());
                    if (ObjectUtil.isNotEmpty(tag.getId())) {
                        updateList.add(tag);
                    } else {
                        addList.add(tag);
                    }
                }
                if (CollectionUtil.isNotEmpty(updateList)) {
                    String sql = SqlUtil.onDuplicateKeyUpdateSql(updateList, true);
                    jdbcTemplate.execute(sql);
                }
                if (CollectionUtil.isNotEmpty(addList)) {
                    String sql = SqlUtil.batchInsertSql(addList);
                    jdbcTemplate.execute(sql);
                }
            } catch (DuplicateKeyException e) {
                throw new BusinessException(TAG_EXISTED);
            }
        }
    }

    public Page<CustomerTagRecordResp> getCustomerTagRecord(Integer companyType, Integer userId, Pageable pageable) {
        JPAQuery<CustomerTagRecordResp> query = queryFactory.select(Projections.bean(CustomerTagRecordResp.class,
                        qUcCustomerTagRecord.id,
                        qUcCustomerTagRecord.tagId,
                        qUcCustomerTag.name.as("tagName"),
                        qUcCustomerTagRecord.userId,
                        qUcCustomerTagRecord.enabled,
                        qUcCustomerTagRecord.companyType,
                        qUcCustomerTagRecord.createTime,
                        qUcCustomerTagRecord.operator))
                .from(qUcCustomerTagRecord)
                .leftJoin(qUcCustomerTag)
                .on(qUcCustomerTagRecord.tagId.eq(qUcCustomerTag.id))
                .where(qUcCustomerTagRecord.userId.eq(userId))
                .where(qUcCustomerTagRecord.companyType.eq(companyType));
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        List<CustomerTagRecordResp> recordResp = query.offset((pageable.getOffset())).limit(pageable.getPageSize())
                .orderBy(qUcCustomerTagRecord.createTime.asc(), qUcCustomerTagRecord.id.asc())
                .fetch();
        return new PageImpl<>(recordResp, pageable, total);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setOperateRange(Integer groupId, List<Long> roleList) {
        queryFactory.delete(relation)
                .where(relation.groupId.eq(groupId))
                .execute();
        if (CollectionUtil.isEmpty(roleList)) {
            return;
        }
        List<UcCustomerTagGroupRoleRelation> relationList = new ArrayList<>();
        for (Long roleId : roleList) {
            UcCustomerTagGroupRoleRelation groupRoleRelation = UcCustomerTagGroupRoleRelation.builder()
                    .roleId(roleId)
                    .groupId(groupId)
                    .build();
            relationList.add(groupRoleRelation);
        }
        if (CollectionUtil.isNotEmpty(relationList)) {
            String sql = SqlUtil.batchInsertSql(relationList);
            jdbcTemplate.execute(sql);
        }
    }

    public List<UcCustomerTagGroupRoleRelation> getOperateRange(Integer groupId) {
        return queryFactory.selectFrom(relation)
                .where(relation.groupId.eq(groupId))
                .fetch();

    }

    public List<UcCustomerTagGroupRoleRelation> getCustomerTagGroupByRole(Long roleId) {
        return queryFactory.selectFrom(relation)
                .where(relation.roleId.eq(roleId))
                .fetch();

    }

    @Transactional(rollbackFor = Exception.class)
    public void createBatchCustomerTag(Integer companyType, List<Integer> userIdList, Integer operator, List<Integer> tagList) {

        //组内标签互斥
        Map<Object, Object> tagMap = queryFactory.selectFrom(qUcCustomerTag)
                .leftJoin(qUcCustomerTagGroup)
                .on(qUcCustomerTag.groupId.eq(qUcCustomerTagGroup.id))
                .where(qUcCustomerTagGroup.isRadio.eq(true))
                .where(qUcCustomerTag.id.in(tagList))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(UcCustomerTag::getGroupId))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (CollectionUtil.isNotEmpty(tagMap)) {
            throw new BusinessException("互斥标签组内只能单选");
        }

        List<UcCustomerTagRelation> relations = new ArrayList<>();
        List<UcCustomerTagRecord> records = new ArrayList<>();
        for (Integer userId : userIdList) {
            for (Integer tagId : tagList) {
                UcCustomerTagRelation relation = UcCustomerTagRelation
                        .builder()
                        .tagId(tagId)
                        .userId(userId)
                        .enabled(true)
                        .build();
                relations.add(relation);

                UcCustomerTagRecord record = UcCustomerTagRecord
                        .builder()
                        .tagId(tagId)
                        .userId(userId)
                        .operator(operator)
                        .companyType(companyType)
                        .enabled(true)
                        .build();
                records.add(record);
            }
        }

        String relationSql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        if (ObjectUtil.isNotEmpty(relationSql)) {
            jdbcTemplate.execute(relationSql);
        }

        String recordSql = SqlUtil.batchInsertSql(records);
        if (ObjectUtil.isNotEmpty(recordSql)) {
            jdbcTemplate.execute(recordSql);
        }
    }

    public PageResult<List<CustomerTagRecordResp>> getTagRecordByOperator(Integer operatorId, Integer current, Integer size) {
        JPAQuery<CustomerTagRecordResp> query = queryFactory.select(Projections.bean(CustomerTagRecordResp.class,
                        qUcCustomerTagRecord.id,
                        qUcCustomerTagRecord.tagId,
                        qUcCustomerTag.name.as("tagName"),
                        qUcCustomerTagRecord.userId,
                        qUcCustomerTagRecord.enabled,
                        qUcCustomerTagRecord.companyType,
                        qUcCustomerTagRecord.createTime,
                        qUcCustomerTagRecord.operator))
                .from(qUcCustomerTagRecord)
                .leftJoin(qUcCustomerTag)
                .on(qUcCustomerTagRecord.tagId.eq(qUcCustomerTag.id))
                .where(qUcCustomerTagRecord.operator.eq(operatorId));
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(qUcCustomerTagRecord.createTime.desc(), qUcCustomerTagRecord.id.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));

    }
}

package cn.shrise.radium.userservice.util;

import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.userservice.resp.TrackEventDetail;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.shrise.radium.common.util.DateUtils.DEFAULT_PATTERN_DATE_MINUTES;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TrackEventRedisUtils {

    private final StringRedisTemplate redisTemplate;

    private final String userKey = "track_event:user:%d:%s";

    private final String trackEventSummaryKey = "track_event:summary:%s";

    private final String trackEventKey = "track_event:%s";

    private final String summaryKey = "summary:%s";

    /**
     * 5 minute
     */
    public static int USER_KEY_EXPIRE = 5;

    public String getUserPage(Integer userId, String appId) {
        return redisTemplate.opsForValue().get(String.format(this.userKey, userId, appId));
    }

    public void setUserPage(Integer userId, String appId, String pageId) {
        redisTemplate.opsForValue().set(String.format(this.userKey, userId, appId), pageId, USER_KEY_EXPIRE, TimeUnit.MINUTES);
    }

    public Map<Object, Object> getDetailOnTrackEventSummary(String summaryKey) {
        return redisTemplate.opsForHash().entries(String.format(this.trackEventKey, summaryKey));
    }

    public void addDetailOnTrackEventSummary(String flagTime, String hashKey, TrackEventDetail detail) {
        redisTemplate.opsForHash().put(String.format(this.trackEventSummaryKey, flagTime), hashKey, JSON.toJSONString(detail));
    }

    public void deleteTrackEventSummaryKey(String summaryKey) {
        redisTemplate.delete(String.format(this.trackEventKey, summaryKey));
    }

    public String getFlagTime(LocalDateTime dateTime) {
        return DateUtils.formatterLocalDateTimeToString(dateTime, "yyyy-MM-dd HH:mm");
    }

    public void setFlagTimeKey(LocalDateTime dateTime) {
        String flagTimeKey = String.format(this.summaryKey, getFlagTime(dateTime));
        if (!Boolean.TRUE.equals(redisTemplate.hasKey(flagTimeKey))) {
            int timeout = (62 - dateTime.getSecond()) * 1000;
            redisTemplate.opsForValue().set(flagTimeKey, flagTimeKey, timeout, TimeUnit.MILLISECONDS);
        }
    }
}

package cn.shrise.radium.userservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.repository.UcStaffRoleRepository;
import cn.shrise.radium.userservice.resp.staffrole.StaffRoleResp;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.userservice.error.UsErrorCode.STAFF_ROLE_NAME_EXISTED;
import static cn.shrise.radium.userservice.error.UsErrorCode.STAFF_ROLE_NUMBER_EXISTED;

@Repository
@Slf4j
@RequiredArgsConstructor
public class UcStaffRoleDao {

    private final JPAQueryFactory queryFactory;
    private final UcStaffRoleRepository ucStaffRoleRepository;
    private final QUcStaffRole qUcStaffRole = QUcStaffRole.ucStaffRole;
    private final QUcStaffRoleRelation qUcStaffRoleRelation= QUcStaffRoleRelation.ucStaffRoleRelation;

    public UcStaffRole createOne(String name, String number) {
        if (ObjectUtil.isNotNull(getByName(name))) {
            throw new BusinessException(STAFF_ROLE_NAME_EXISTED);
        }
        if (ObjectUtil.isNotNull(getByNumber(number))) {
            throw new BusinessException(STAFF_ROLE_NUMBER_EXISTED);
        }
        return ucStaffRoleRepository.save(UcStaffRole.builder().name(name).number(number).build());
    }

    public UcStaffRole getById(Long id) {
        return queryFactory.selectFrom(qUcStaffRole)
                .where(qUcStaffRole.id.eq(id))
                .fetchOne();
    }

    public UcStaffRole getByNumber(String number) {
        return queryFactory.selectFrom(qUcStaffRole)
                .where(qUcStaffRole.number.eq(number))
                .fetchOne();
    }

    public UcStaffRole getByName(String name) {
        return queryFactory.selectFrom(qUcStaffRole)
                .where(qUcStaffRole.name.eq(name))
                .fetchOne();
    }

    @Transactional
    public void updateEnable(Long id, Boolean enabled) {
        queryFactory.update(qUcStaffRole)
                .set(qUcStaffRole.enabled, enabled)
                .where(qUcStaffRole.id.eq(id))
                .execute();
    }

    public PageResult<List<StaffRoleResp>> getList(Boolean enabled, Integer current, Integer size) {
        JPAQuery<StaffRoleResp> query = queryFactory.select(Projections.bean(
                StaffRoleResp.class,
                qUcStaffRole.id,
                qUcStaffRole.name,
                qUcStaffRole.number,
                qUcStaffRole.enabled,
                qUcStaffRole.gmtCreate,
                qUcStaffRole.gmtModified
                )).from(qUcStaffRole);
        if (ObjectUtil.isNotNull(enabled)) {
            query.where(qUcStaffRole.enabled.eq(enabled));
        }

        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();
        query.orderBy(qUcStaffRole.id.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public List<UcStaffRole> getRoleList(Collection<Long> roleIds) {
        if (ObjectUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        final Set<Long> idSet = roleIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return queryFactory.selectFrom(qUcStaffRole)
                .where(qUcStaffRole.id.in(idSet))
                .fetch();
    }

    public List<UcStaffRole> getListByUser(Integer userId) {
        List<UcStaffRole> fetch = queryFactory.select(qUcStaffRole)
                .from(qUcStaffRole)
                .leftJoin(qUcStaffRoleRelation)
                .on(qUcStaffRole.id.eq(qUcStaffRoleRelation.roleId))
                .where(qUcStaffRoleRelation.userId.eq(userId))
                .fetch();
        return fetch;
    }

}

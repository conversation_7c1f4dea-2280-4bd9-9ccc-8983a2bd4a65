package cn.shrise.radium.userservice.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.RedisUtil;
import cn.shrise.radium.userservice.property.JustCallProperty;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;

@Service
@RequiredArgsConstructor
public class JustCallUtils {

    private final RedisUtil redisUtil;
    private final JustCallProperty justCallProperty;

    private String md5(String str) {
        return DigestUtils.md5DigestAsHex(str.getBytes(StandardCharsets.UTF_8)).toLowerCase();
    }

    private String getIndexAccessToken() {
        String tokenKey = String.format("justCall:index:%s", justCallProperty.getExt());
        String token = redisUtil.getKey(tokenKey);
        if (ObjectUtil.isNotNull(token)) {
            return token;
        }
        String path = "api/auth/index";
        String url = String.format("http://%s/%s?username=%s&password=%s",
                justCallProperty.getHost(), path, justCallProperty.getAccount(),
                this.md5(justCallProperty.getPassword()));
        String body = HttpUtil.get(url);
        JSONObject result = JSONObject.parseObject(body);
        if (result.getInteger("errno") == 0) {
            JSONObject data = result.getJSONObject("result");
            token = data.getString("access_token");
            Long expiresIn = data.getLong("expired_in");
            redisUtil.setKey(tokenKey, token, expiresIn);
            return token;
        } else {
            throw new BusinessException(result.getInteger("errno"), result.getString("errmsg"));
        }
    }

    private String getLoginAccessToken() {
        String tokenKey = String.format("justCall:login:%s", justCallProperty.getExt());
        String token = redisUtil.getKey(tokenKey);
        if (ObjectUtil.isNotNull(token)) {
            return token;
        }
        String path = "api/auth/login";
        String url = String.format("http://%s/%s?username=%s&password=%s",
                justCallProperty.getHost(), path, justCallProperty.getAccount(),
                this.md5(justCallProperty.getPassword()));
        String body = HttpUtil.get(url);
        JSONObject result = JSONObject.parseObject(body);
        if (result.getInteger("errno") == 0) {
            JSONObject data = result.getJSONObject("result");
            token = data.getString("access_token");
            Long expiresIn = data.getLong("expired_in");
            redisUtil.setKey(tokenKey, token, expiresIn);
            return token;
        } else {
            throw new BusinessException(result.getInteger("errno"), result.getString("errmsg"));
        }
    }

    public JSONObject makingCall(String ext, String phone) {
        String path = "api/just-ari";
        String url = String.format("http://%s/%s?access_token=%s",
                justCallProperty.getHost(), path, this.getIndexAccessToken());
        HashMap<String, Object> data = new HashMap<>();
        data.put("control", "click_call");
        data.put("caller", ext);
        data.put("callee", phone);

        String body = HttpUtil.post(url, JSONObject.toJSONString(data));
        JSONObject result = JSONObject.parseObject(body);
        if (result.getInteger("errno") == 0) {
            return result.getJSONObject("result");
        } else {
            throw new BusinessException(result.getInteger("errno"), result.getString("errmsg"));
        }
    }

    public JSONObject getInfoList(String startStamp, Integer page, Integer pageSize) {
        String path = "api/cdr/info/list";
        String url = String.format("http://%s/%s?access_token=%s&page=%d&pagesize=%d",
                justCallProperty.getHost(), path, this.getIndexAccessToken(), page, pageSize);
        HashMap<String, Object> data = new HashMap<>();
        data.put("start_stamp", startStamp);
        data.put("sort", "start_stamp");
        String body = HttpUtil.get(url, data);
        JSONObject result = JSONObject.parseObject(body);
        if (result.getInteger("errno") == 0) {
            return result.getJSONObject("result");
        } else {
            throw new BusinessException(result.getInteger("errno"), result.getString("errmsg"));
        }
    }
}

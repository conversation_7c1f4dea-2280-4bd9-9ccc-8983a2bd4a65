package cn.shrise.radium.userservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.userservice.dao.UcStaffRoleDao;
import cn.shrise.radium.userservice.dao.UcStaffRoleRecordDao;
import cn.shrise.radium.userservice.dao.UcStaffRoleRelationDao;
import cn.shrise.radium.userservice.dao.UcStaffRoleRelationRecordDao;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.resp.staffrole.StaffRoleResp;
import cn.shrise.radium.userservice.service.StaffRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

@Api
@RestController
@RequestMapping("staff-role")
@RequiredArgsConstructor
public class StaffRoleController {

    private final StaffRoleService staffRoleService;
    private final UcStaffRoleDao staffRoleDao;
    private final UcStaffRoleRecordDao staffRoleRecordDao;
    private final UcStaffRoleRelationDao staffRoleRelationDao;
    private final UcStaffRoleRelationRecordDao staffRoleRelationRecordDao;

    @PostMapping("role/create")
    @ApiOperation("新建角色")
    public BaseResult<Void> createStaffRole(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色名称") String name,
            @RequestParam @ApiParam("角色编号") String number) {
        staffRoleService.createRole(name, number, operatorId);
        return BaseResult.successful();
    }

    @PostMapping("role/enable")
    @ApiOperation("启用/禁用角色")
    public BaseResult<Void> enableStaffRole(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色id") Long roleId,
            @RequestParam @ApiParam("启用/禁用") Boolean enabled) {
        staffRoleService.enableRole(roleId, enabled, operatorId);
        return BaseResult.successful();
    }

    @GetMapping("role/list")
    @ApiOperation("角色列表")
    public PageResult<List<StaffRoleResp>> getStaffRoleList(
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size) {
        return staffRoleDao.getList(enabled, current, size);
    }

    @GetMapping("role/by-user")
    @ApiOperation("根据用户获取角色列表")
    public BaseResult<List<UcStaffRole>> getStaffRoleListByUser(@RequestParam @ApiParam("用户id") Integer userId) {
        return BaseResult.success(staffRoleDao.getListByUser(userId));
    }


    @PostMapping("role/batch")
    @ApiOperation("根据角色ID批量获取角色map")
    BaseResult<Map<Long, UcStaffRole>> batchGetStaffRoleMap(@RequestBody BatchReq<Long> req) {
        List<UcStaffRole> roleList = staffRoleDao.getRoleList(req.getValues());
        final Map<Long, UcStaffRole> roleMap = roleList.stream()
                .collect(Collectors.toMap(UcStaffRole::getId, Function.identity()));
        return BaseResult.success(roleMap);
    }

    @GetMapping("role/record/list")
    @ApiOperation("角色操作列表")
    public PageResult<List<UcStaffRoleRecord>> getStaffRoleRecordList(
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size) {
        return staffRoleRecordDao.getList(current, size);
    }

    @PostMapping("relation/create")
    @ApiOperation("新增角色成员")
    public BaseResult<Void> createStaffRoleRelation(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色id") Long roleId,
            @RequestParam @ApiParam("成员id列表") List<Integer> userIdList) {
        staffRoleService.createRoleRelation(roleId, userIdList, operatorId);
        return BaseResult.successful();
    }

    @PostMapping("relation/disable")
    @ApiOperation("删除角色成员")
    public BaseResult<Void> disableStaffRoleRelation(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色id") Long roleId,
            @RequestParam @ApiParam("成员id") Integer userId) {
        staffRoleService.disableRoleRelation(roleId, userId, operatorId);
        return BaseResult.successful();
    }

    @GetMapping("relation/list")
    @ApiOperation("角色成员列表")
    public PageResult<List<UcStaffRoleRelation>> getStaffRoleRelationList(
            @RequestParam(required = false) @ApiParam("角色编号列表") List<String> roleNumberList,
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size) {
        return staffRoleRelationDao.getList(roleNumberList, enabled, current, size);
    }

    @GetMapping("relation/list/by-number")
    @ApiOperation("根据角色编号获取角色成员列表")
    public BaseResult<List<UcStaffRoleRelation>> getStaffRoleRelationListByNumber(
            @RequestParam(required = false) @ApiParam("角色编号") String roleNumber,
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled) {
        return staffRoleRelationDao.getList(roleNumber, enabled);
    }

    @GetMapping("relation/role/list")
    @ApiOperation("按角色获取角色成员列表")
    public BaseResult<List<UcStaffRoleRelation>> getStaffRoleRelationListByRoleId(
            @RequestParam @ApiParam("角色id") Long roleId) {
        List<UcStaffRoleRelation> relationList = staffRoleRelationDao.getByRoleId(roleId, true);
        return BaseResult.success(relationList);
    }

    @GetMapping("relation/record/list")
    @ApiOperation("角色成员操作列表")
    public PageResult<List<UcStaffRoleRelationRecord>> getStaffRoleRelationRecordList(
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size) {
        return staffRoleRelationRecordDao.getList(current, size);
    }
}

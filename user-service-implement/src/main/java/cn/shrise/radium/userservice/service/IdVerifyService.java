package cn.shrise.radium.userservice.service;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsCustomerEvaluation;
import cn.shrise.radium.contentservice.req.UpdateCustomerEvaluationReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.resp.Result;
import cn.shrise.radium.userservice.entity.UcIdCardInfo;
import cn.shrise.radium.userservice.entity.UcIdCardRecord;
import cn.shrise.radium.userservice.error.YunDunErrorCodeEnum;
import cn.shrise.radium.userservice.util.VerifyUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;

import static cn.shrise.radium.contentservice.constant.IdVerifyStatusConstant.PASS;
import static cn.shrise.radium.userservice.constant.IdVerifyType.*;
import static cn.shrise.radium.userservice.constant.UserErrorCode.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IdVerifyService {

    private final ContentClient contentClient;

    private final IdCardService idCardService;

    private final OrderClient orderClient;

    private final Integer ID_CARD = 100;

    private final UserService userService;

    private final IdCardVerifyService idCardVerifyService;

    private final VerifyUtils verifyUtils;

    public Boolean verifyIdCardEsign(Integer evaluationId, String identityNumber, String name, String mobile, Integer userId) {
        String str = getBirthDay(identityNumber);
        LocalDate birthDay = StringUtils.isNotBlank(str) ? DateUtils.formatterStringToLocalDate(str, DateUtils.DEFAULT_PATTERN_DATE) : null;
        Result result = orderClient.verify(identityNumber, name, mobile).orElseThrow();
        String mobilePhone = DesensitizeUtil.mobilePhone(mobile);
        if (result.getCode().equals(0)) {
            log.info("E签宝3要素校验成功， 姓名:{}， verify_id: {}", name, result.getVerifyId());
            UcIdCardRecord esgin = UcIdCardRecord.builder()
                    .idNumber(identityNumber)
                    .idName(name)
                    .verifyType(IVT_THREE)
                    .isSuccess(true)
                    .verifyChannel("esgin")
                    .createTime(Instant.now())
                    .build();
            idCardService.createOne(esgin);
            updateInfo(evaluationId, identityNumber, name, mobilePhone, userId, birthDay);
            return true;
        } else {
            UcIdCardRecord esgin = UcIdCardRecord.builder()
                    .idNumber(identityNumber)
                    .idName(name)
                    .mobile(mobilePhone)
                    .verifyType(IVT_THREE)
                    .isSuccess(false)
                    .createTime(Instant.now())
                    .verifyChannel("esgin")
                    .returnCode(String.valueOf(result.getCode()))
                    .build();
            idCardService.createOne(esgin);
            log.error("E签宝3要素校验失败, 姓名:{}, code: {},message:{}", name, result.getCode(), result.getMessage());
            throw new BusinessException(result.getCode(), result.getMessage());
        }
    }

    public Boolean verifyIdCard(Integer evaluationId, String identityNumber, String name, String mobile, Integer userId) throws Exception {
        LocalDate birthDay = getBirthDay(identityNumber) != null ? DateUtils.formatterStringToLocalDate(getBirthDay(identityNumber), DateUtils.DEFAULT_PATTERN_DATE) : null;
//        UcIdCardInfo idCardInfo = idCardService.findByNumber(identityNumber, mobile);
//        if (idCardInfo != null) {
//            if (idCardInfo.getIdName().equals(name)) {
//                updateInfo(evaluationId, identityNumber, name, mobile, userId, birthDay);
//                return true;
//            } else {
//                throw new BusinessException(ID_VERIFY_FAILURE);
//            }
//        }

        JSONObject verify = verifyUtils.verifyIdCard(identityNumber, mobile, name);
        if (verify.getInteger("code").equals(200)) {//调试成功
            log.info("云盾3要素调试成功， 姓名:{}， verify_id: {}", name, verify.getString("requestId"));
            JSONObject data = verify.getJSONObject("data");
            if (data.getInteger("bizCode").equals(1)) {//校验一致
//                UcIdCardInfo info = UcIdCardInfo.builder()
//                        .idNumber(identityNumber)
//                        .idName(name)
//                        .mobile(AESUtil.encrypt(mobile))
//                        .createTime(Instant.now())
//                        .updateTime(Instant.now())
//                        .build();
//                idCardService.createOne(info);
                UcIdCardRecord yundun = UcIdCardRecord.builder()
                        .idNumber(identityNumber)
                        .idName(name)
                        .verifyType(IVT_THREE)
                        .isSuccess(true)
                        .verifyChannel("yundun")
                        .createTime(Instant.now())
                        .build();
                idCardService.createOne(yundun);
                updateInfo(evaluationId, identityNumber, name, DesensitizeUtil.mobilePhone(mobile), userId, birthDay);
                return true;
            } else if (data.getInteger("bizCode").equals(2) || data.getInteger("bizCode").equals(3)) {//校验不一致或查无记录
                UcIdCardRecord yundun = UcIdCardRecord.builder()
                        .idNumber(identityNumber)
                        .idName(name)
                        .mobile(DesensitizeUtil.mobilePhone(mobile))
                        .verifyType(IVT_THREE)
                        .isSuccess(false)
                        .createTime(Instant.now())
                        .verifyChannel("yundun")
                        .returnCode(data.getString("subCode"))
                        .build();
                idCardService.createOne(yundun);
                throw new BusinessException(data.getInteger("subCode"), YunDunErrorCodeEnum.getEnum(data.getInteger("subCode")).getMsg());
            } else {//其他
                throw new BusinessException("其他原因");
            }
        } else {//调试失败
            log.info("云盾3要素调试失败， 姓名:{}，verify_id: {}，message:{}", name, verify.getString("requestId"), verify.getString("message"));
            YunDunErrorCodeEnum msg = YunDunErrorCodeEnum.getEnum(verify.getInteger("code"));
            throw new BusinessException(verify.getInteger("code"), msg == null ? verify.getString("message") : msg.getMsg());
        }
    }

    public void updateFaceAuthIdCard(Integer evaluationId, String identityNumber, String name, Boolean isSuccess) {
        if (isSuccess) {
            LocalDate birthDay = getBirthDay(identityNumber) != null ? DateUtils.formatterStringToLocalDate(getBirthDay(identityNumber), DateUtils.DEFAULT_PATTERN_DATE) : null;
//            UcIdCardInfo idCardInfo = idCardService.findByNumber(identityNumber, null);
//            if (ObjectUtil.isNull(idCardInfo)) {
//                UcIdCardInfo info = UcIdCardInfo.builder()
//                        .idNumber(identityNumber)
//                        .idName(name)
//                        .createTime(Instant.now())
//                        .updateTime(Instant.now())
//                        .build();
//                idCardService.createOne(info);
//            }
            UpdateCustomerEvaluationReq build = UpdateCustomerEvaluationReq.builder()
                    .id(evaluationId)
                    .identityType(ID_CARD)
                    .identityNumber(identityNumber)
                    .sex(IdcardUtil.getGenderByIdCard(identityNumber))
                    .idVerifyType(IVT_FACE)
                    .idVerifyStatus(PASS)
                    .name(name)
                    .build();
            contentClient.updateCustomerEvaluation(build);
            SsCustomerEvaluation evaluation = contentClient.findById(evaluationId).getData();
            userService.updateUser(evaluation.getUserId(), name, birthDay, IdcardUtil.getGenderByIdCard(identityNumber));
        }
        UcIdCardRecord record = UcIdCardRecord.builder()
                .idNumber(identityNumber)
                .idName(name)
                .verifyType(IVT_FACE)
                .isSuccess(isSuccess)
                .verifyChannel("faceauth")
                .createTime(Instant.now())
                .build();
        idCardService.createOne(record);
    }

    private void updateInfo(Integer evaluationId, String identityNumber, String name, String mobile, Integer userId, LocalDate birthDay) {
        UpdateCustomerEvaluationReq build = UpdateCustomerEvaluationReq.builder()
                .id(evaluationId)
                .mobile(mobile)
                .identityType(ID_CARD)
                .identityNumber(identityNumber)
                .sex(IdcardUtil.getGenderByIdCard(identityNumber))
                .idVerifyType(IVT_THREE)
                .idVerifyStatus(PASS)
                .name(name)
                .build();
        contentClient.updateCustomerEvaluation(build);
        userService.updateUser(userId, name, birthDay, IdcardUtil.getGenderByIdCard(identityNumber));
    }


    public Boolean verifyOtherEsign(Integer evaluationId, String identityNumber, String name, Integer idCardType, Integer userId) {
        String str = getBirthDay(identityNumber);
        LocalDate birthDay = StringUtils.isNotBlank(str) ? DateUtils.formatterStringToLocalDate(str, DateUtils.DEFAULT_PATTERN_DATE) : null;
        contentClient.findById(evaluationId).orElseThrow();
        if (idCardType.equals(ID_CARD)) {
            userService.updateUser(userId, name, birthDay, IdcardUtil.getGenderByIdCard(identityNumber));
            Result result = orderClient.verifyBase(identityNumber, name).orElseThrow();
            if (result.getCode().equals(0)) {
                log.info("E签宝2要素校验成功， 姓名:{}， verify_id: {}", name, result.getVerifyId());
                UcIdCardRecord yundun = UcIdCardRecord.builder()
                        .idNumber(identityNumber)
                        .idName(name)
                        .verifyType(IVT_TWO)
                        .isSuccess(true)
                        .verifyChannel("esgin")
                        .createTime(Instant.now())
                        .build();
                idCardService.createOne(yundun);
            } else {
                UcIdCardRecord yundun = UcIdCardRecord.builder()
                        .idNumber(identityNumber)
                        .idName(name)
                        .verifyType(IVT_TWO)
                        .isSuccess(false)
                        .createTime(Instant.now())
                        .verifyChannel("esgin")
                        .returnCode(String.valueOf(result.getCode()))
                        .build();
                idCardService.createOne(yundun);
                log.error("E签宝2要素校验失败, 姓名:{}, code: {},message:{}", name, result.getCode(), result.getMessage());
                throw new BusinessException(result.getCode(), result.getMessage());
            }
        } else {
            userService.updateUser(userId, name, null, null);
        }
        UpdateCustomerEvaluationReq build = UpdateCustomerEvaluationReq.builder()
                .id(evaluationId)
                .identityType(idCardType)
                .identityNumber(identityNumber)
                .idVerifyType(IVT_TWO)
                .idVerifyStatus(PASS)
                .name(name)
                .build();
        contentClient.updateCustomerEvaluation(build);
        return true;
    }

    public Boolean verifyOther(Integer evaluationId, String identityNumber, String name, Integer idCardType, Integer userId) throws Exception {
        LocalDate birthDay = getBirthDay(identityNumber) != null ? DateUtils.formatterStringToLocalDate(getBirthDay(identityNumber), DateUtils.DEFAULT_PATTERN_DATE) : null;
        BaseResult<SsCustomerEvaluation> result = contentClient.findById(evaluationId);
        if (result.isFail()) {
            throw new BusinessException(FAILURE);
        }
//        UcIdCardInfo idCardInfo = idCardService.findByNumber(identityNumber, null);
//        if (idCardInfo != null) {
//            if (idCardInfo.getIdName().equals(name)) {
//                UpdateCustomerEvaluationReq build = UpdateCustomerEvaluationReq.builder()
//                        .id(evaluationId)
//                        .identityType(ID_CARD)
//                        .identityNumber(identityNumber)
//                        .sex(IdcardUtil.getGenderByIdCard(identityNumber))
//                        .idVerifyType(IVT_TWO)
//                        .idVerifyStatus(PASS)
//                        .name(name)
//                        .build();
//                contentClient.updateCustomerEvaluation(build);
//                userService.updateUser(userId, name, birthDay, IdcardUtil.getGenderByIdCard(identityNumber));
//                return true;
//            } else {
//                throw new BusinessException(ID_NAME_VERIFY_FAILURE);
//            }
//        }

        if (idCardType.equals(ID_CARD)) {
            userService.updateUser(userId, name, birthDay, IdcardUtil.getGenderByIdCard(identityNumber));
            JSONObject jsonObject = verifyUtils.verifyIdName(name, identityNumber);
            if (jsonObject.getInteger("code").equals(200)) {//调试成功
                log.info("云盾2要素调试成功， 姓名:{}， verify_id: {}", name, jsonObject.getString("requestId"));
                JSONObject data = jsonObject.getJSONObject("data");
                if (data.getInteger("bizCode").equals(1)) {//校验一致
//                    UcIdCardInfo info = UcIdCardInfo.builder()
//                            .idNumber(identityNumber)
//                            .idName(name)
//                            .createTime(Instant.now())
//                            .updateTime(Instant.now())
//                            .build();
//                    idCardService.createOne(info);
                    UcIdCardRecord yundun = UcIdCardRecord.builder()
                            .idNumber(identityNumber)
                            .idName(name)
                            .verifyType(IVT_TWO)
                            .isSuccess(true)
                            .verifyChannel("yundun")
                            .createTime(Instant.now())
                            .build();
                    idCardService.createOne(yundun);
                } else {
                    UcIdCardRecord yundun = UcIdCardRecord.builder()
                            .idNumber(identityNumber)
                            .idName(name)
                            .verifyType(IVT_TWO)
                            .isSuccess(false)
                            .createTime(Instant.now())
                            .verifyChannel("yundun")
                            .returnCode(data.getString("bizCode"))
                            .build();
                    idCardService.createOne(yundun);
                    throw new BusinessException(ID_NAME_VERIFY_FAILURE);
                }
            } else {//调试失败
                log.info("云盾2要素调试失败， 姓名:{}，verify_id: {}，message:{}", name, jsonObject.getString("requestId"), jsonObject.getString("message"));
                YunDunErrorCodeEnum msg = YunDunErrorCodeEnum.getEnum(jsonObject.getInteger("code"));
                throw new BusinessException(jsonObject.getInteger("code"), msg == null ? jsonObject.getString("message") : msg.getMsg());
            }
        } else {
            userService.updateUser(userId, name, null, null);
        }
        UpdateCustomerEvaluationReq build = UpdateCustomerEvaluationReq.builder()
                .id(evaluationId)
                .identityType(idCardType)
                .identityNumber(identityNumber)
                .idVerifyType(IVT_TWO)
                .idVerifyStatus(PASS)
                .name(name)
                .build();
        contentClient.updateCustomerEvaluation(build);
        return true;
    }

    public static String getBirthDay(String id) {
        String year;
        String month;
        String day;
        //正则匹配身份证号是否是正确的，15位或者17位数字+数字/x/X
        if (id.matches("^\\d{15}|\\d{17}[\\dxX]$")) {
            year = id.substring(6, 10);
            month = id.substring(10, 12);
            day = id.substring(12, 14);
        } else {
            return null;
        }
        return year + "-" + month + "-" + day;
    }


}

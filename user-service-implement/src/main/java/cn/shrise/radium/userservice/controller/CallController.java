package cn.shrise.radium.userservice.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.base.BaseError;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.RedisUtil;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.userservice.constant.CallSeatTypeConstant;
import cn.shrise.radium.userservice.dao.*;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.property.TencentCccProperties;
import cn.shrise.radium.userservice.req.UpdateTxSeatReq;
import cn.shrise.radium.userservice.resp.call.*;
import cn.shrise.radium.userservice.service.CallService;
import cn.shrise.radium.userservice.service.ServerExtService;
import cn.shrise.radium.userservice.service.UserService;
import cn.shrise.radium.userservice.util.TencentCccUtils;
import com.tencentcloudapi.ccc.v20200210.models.CreateSDKLoginTokenResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.RocketMQConstant.TAG_TX_CALL_SEAT;
import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_USER;
import static cn.shrise.radium.common.util.LockUtils.getTxCallSeatLockKey;
import static cn.shrise.radium.userservice.constant.CallSeatTypeConstant.TENCENT;


@Api
@Slf4j
@RestController
@RequestMapping("call")
@RequiredArgsConstructor
public class CallController {

    private final UcTxCallRecordDao ucTxCallRecordDao;
    private final UserService userService;
    private final TencentCccProperties cccProperties;
    private final TencentCccUtils tencentCccUtils;
    private final UcJustCallRecordDao ucJustCallRecordDao;
    private final ServerExtService serverExtService;
    private final UcTxCallSeatDao ucTxCallSeatDao;
    private final UcJustCallSeatInfoDao justCallSeatInfoDao;
    private final UcHandleFlagDao ucHandleFlagDao;
    private final RocketMqUtils rocketMqUtils;
    private final RedisUtil redisUtil;
    private final CallService callService;
    private final static Long EXPIRE_TIME = 60L;
    private final static int UPDATE_SEAT_FLAG = 50001;

    @GetMapping("tencent/call-list")
    @ApiOperation("获取腾讯云通话记录")
    public PageResult<List<TxCallRecordResp>> getTxCallRecordList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("手机号id") Long mobileId,
            @RequestParam(required = false) @ApiParam("通话类型（呼入/呼出）") Boolean isCallIn,
            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
            @RequestParam(required = false) @ApiParam("通话时间（开始时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("通话时间（结束时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchText,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size) {
        return callService.getTxCallRecordList(companyType, userId, mobileId, isCallIn, callStatus, startTime, endTime, searchText, current, size);
    }

    @GetMapping("tencent/call-config")
    @ApiOperation("获取打电话配置")
    public BaseResult<TxCallConfigResp> getCallConfig(
            @RequestParam @ApiParam("坐席邮箱") String seatMail) {
        TxCallConfigResp resp = TxCallConfigResp.builder()
                .sdkAppId(cccProperties.getSdkAppId())
                .build();
        CreateSDKLoginTokenResponse tokenResponse = tencentCccUtils.createSDKLoginToken(seatMail, false);
        if (ObjectUtil.isNotEmpty(tokenResponse)) {
            resp.setToken(tokenResponse.getToken());
            resp.setSdkUrl(tokenResponse.getSdkURL());
        }
        return BaseResult.success(resp);
    }

    @GetMapping("just/call-list")
    @ApiOperation("获取集时云通话记录")
    PageResult<List<JustCallRecordResp>> getJustCallRecordList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("客户手机号id") Long mobileId,
            @RequestParam(required = false) @ApiParam("通话类型（呼入/呼出）") Boolean isCallIn,
            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
            @RequestParam(required = false) @ApiParam("通话时间（开始时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("通话时间（结束时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size) {
        Long seatId = null;
        if (ObjectUtil.isNotEmpty(userId)) {
            UcServerExt serverExt = serverExtService.getServerExt(userId).orElse(new UcServerExt());
            seatId = serverExt.getSeatID();
            if (!(Objects.equals(serverExt.getSeatType(), CallSeatTypeConstant.JUST) && ObjectUtil.isNotEmpty(seatId))) {
                return PageResult.empty();
            }
        }

        PageResult<List<JustCallRecordResp>> result = ucJustCallRecordDao.findAllByFilter(seatId, null, mobileId, isCallIn, callStatus, startTime, endTime, searchContent, current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }

        // 获取客户信息
        Set<Long> customerMobileIds = result.getData().stream().map(JustCallRecordResp::getMobileId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        List<UcUsers> userList = userService.getUserListByMobileIdList(companyType, customerMobileIds, true);
        Map<Long, UcUsers> customerMap = userList.stream().collect(Collectors.toMap(UcUsers::getMobileId, x -> x, (existing, replacement) -> replacement));

        // 设置客户信息及手机号解密脱敏
        result.getData().forEach(t -> {
            t.setCustomerId(customerMap.getOrDefault(t.getMobileId(), new UcUsers()).getId());
            t.setCustomerNumber(customerMap.getOrDefault(t.getMobileId(), new UcUsers()).getNumber());
            t.setCustomerMobile(t.getCustomerMobile());
        });
        return result;
    }

    @GetMapping("user-just-seat/info")
    @ApiOperation("获取用户集时云坐席信息")
    BaseResult<UcJustCallSeatInfo> getUserJustCallSeatInfo(
            @RequestParam(required = false) @ApiParam("用户id") Integer userId) {
        return BaseResult.success(serverExtService.getUserJustCallSeatInfo(userId));

    }

    @GetMapping("user-tx-seat/info")
    @ApiOperation("获取用户集时云坐席信息")
    BaseResult<UcTxCallSeatInfo> getUserTxCallSeatInfo(
            @RequestParam(required = false) @ApiParam("用户id") Integer userId) {
        return BaseResult.success(serverExtService.getUserTxCallSeatInfo(userId));
    }

    @PostMapping("seat/bind")
    @ApiOperation("绑定或解绑坐席用户")
    public BaseResult<String> bindSeatUser(
            @RequestParam @ApiParam("坐席id") Long seatId,
            @RequestParam @ApiParam("坐席类型") Integer seatType,
            @RequestParam(required = false) @ApiParam("销售id（该参数不传即为解绑操作）") Integer serverId) {
        if (ObjectUtil.isNotEmpty(serverId)) {//绑定操作
            if (!serverExtService.checkCanBindSeat(serverId, seatId)) {
                return BaseResult.create(BaseError.RECORD_EXISTED_CODE, "当前用户已分配坐席，请修改其他用户绑定");
            }
            serverExtService.bindSeatInfo(serverId, seatId, seatType);
            return BaseResult.success();
        } else {//解绑操作
            serverExtService.unbindSeatInfo(seatId, seatType);
            return BaseResult.success();
        }
    }

    @GetMapping("seat/tx-call/list")
    @ApiOperation("获取腾讯云坐席列表")
    PageResult<List<TencentSeatResp>> getTencentCallSeatList(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        PageResult<List<UcTxCallSeatInfo>> tencentCallSeatListRes = ucTxCallSeatDao.getTencentCallSeatList(current, size);
        if (CollUtil.isEmpty(tencentCallSeatListRes.getData())) {
            return PageResult.success(Collections.emptyList(), tencentCallSeatListRes.getPagination());
        }
        List<UcTxCallSeatInfo> txCallSeatInfos = tencentCallSeatListRes.getData();
        List<Long> seatIds = txCallSeatInfos.stream().map(UcTxCallSeatInfo::getId).collect(Collectors.toList());
        List<UcServerExt> serverExts = serverExtService.getServerExt(seatIds, TENCENT);
        Map<String, Integer> seatIdToServerIdMap = serverExts.stream().collect(Collectors.toMap(t -> StrUtil.format("{}-{}", t.getSeatType(), t.getSeatID()), UcServerExt::getId));
        List<UcUsers> userList = userService.getUserList(seatIdToServerIdMap.values());
        Map<Integer, UcUsers> userMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, t -> t));
        List<TencentSeatResp> tencentSeatResps = txCallSeatInfos.stream().map(seatInfo -> {
            Integer serverId = seatIdToServerIdMap.get(StrUtil.format("{}-{}", TENCENT, seatInfo.getId()));
            return TencentSeatResp.builder()
                    .id(seatInfo.getId())
                    .userId(serverId)
                    .userName(Objects.isNull(serverId) ? null : userMap.getOrDefault(serverId, new UcUsers()).getRealName())
                    .userNumber(Objects.isNull(serverId) ? null : userMap.getOrDefault(serverId, new UcUsers()).getNumber())
                    .name(seatInfo.getName())
                    .mail(seatInfo.getMail())
                    .staffNumber(seatInfo.getStaffNumber())
                    .lastModifyTime(seatInfo.getLastModifyTimestamp())
                    .isDelete(!seatInfo.getIsEnabled())
                    .assignedPhone(seatInfo.getAssignedPhone())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(tencentSeatResps, tencentCallSeatListRes.getPagination());
    }

    @GetMapping("seat/just-call/list")
    @ApiOperation("获取集时云坐席列表")
    public PageResult<List<JustCallSeatResp>> getJustCallSeatList(
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size) {
        PageResult<List<UcJustCallSeatInfo>> result = justCallSeatInfoDao.getJustCallSeatList(current, size);
        if (!result.isPresent()) {
            return PageResult.success(Collections.emptyList(), result.getPagination());
        }
        List<UcJustCallSeatInfo> justCallSeatInfos = result.getData();
        List<Long> seatIds = justCallSeatInfos.stream().map(UcJustCallSeatInfo::getId).collect(Collectors.toList());
        List<UcServerExt> serverExts = serverExtService.getServerExt(seatIds, CallSeatTypeConstant.JUST);
        Map<String, Integer> seatIdToServerIdMap = serverExts.stream().collect(Collectors.toMap(t -> StrUtil.format("{}-{}", t.getSeatType(), t.getSeatID()), UcServerExt::getId));
        List<UcUsers> userList = userService.getUserList(seatIdToServerIdMap.values());
        Map<Integer, UcUsers> userMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, t -> t));
        List<JustCallSeatResp> respList = justCallSeatInfos.stream().map(seatInfo -> {
            Integer serverId = seatIdToServerIdMap.get(StrUtil.format("{}-{}", CallSeatTypeConstant.JUST, seatInfo.getId()));
            return JustCallSeatResp.builder()
                    .id(seatInfo.getId())
                    .userId(serverId)
                    .userName(userMap.getOrDefault(serverId, new UcUsers()).getRealName())
                    .phoneExt(seatInfo.getPhoneExt())
                    .ctiUserName(seatInfo.getCtiUserName())
                    .ctiPassword(seatInfo.getCtiPassword())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }

    @PostMapping("seat/just-call/create")
    @ApiOperation("添加集时云坐席")
    public BaseResult<String> createSeat(
            @RequestBody @Valid CreateJustCallSeatReq req) {
        UcJustCallSeatInfo info = UcJustCallSeatInfo.builder()
                .phoneExt(req.getPhoneExt())
                .ctiUserName(req.getCtiUserName())
                .ctiPassword(req.getCtiPassword())
                .isEnabled(true)
                .build();
        justCallSeatInfoDao.createOne(info);
        return BaseResult.success();
    }

    @PostMapping("seat/tencent/update")
    @ApiOperation("更新腾讯云坐席信息")
    public BaseResult<String> updateTxSeat(@RequestParam @ApiParam("销售id") Integer serverId) {
        String serverKey = getTxCallSeatLockKey(serverId);
        Boolean locked = redisUtil.getLock(serverKey, EXPIRE_TIME);
        if (!locked) {
            return BaseResult.create(BaseError.RECORD_EXISTED_CODE, "上次更新未完成，请稍候");
        }
        ucHandleFlagDao.createOrUpdateOne(UPDATE_SEAT_FLAG, serverId, Instant.now());
        UpdateTxSeatReq req = UpdateTxSeatReq.builder()
                .flag(UPDATE_SEAT_FLAG)
                .serverId(serverId)
                .build();
        rocketMqUtils.send(TOPIC_USER, TAG_TX_CALL_SEAT, req);
        return BaseResult.success();
    }

    @GetMapping("seat/tencent/get-update-result")
    @ApiOperation("查询腾讯云坐席更新结果")
    public BaseResult<UpdateTxSeatResult> getUpdateTxResult() {
        UcHandleFlag flag = ucHandleFlagDao.findById(UPDATE_SEAT_FLAG);
        if (ObjectUtil.isEmpty(flag)) {
            return BaseResult.success(null);
        }
        UpdateTxSeatResult seatResult = UpdateTxSeatResult.builder()
                .updateTime(flag.getUpdateTime())
                .completeTime(flag.getFlagTime())
                .build();
        return BaseResult.success(seatResult);
    }

    @GetMapping("just/get-call-record")
    @ApiOperation("获取通话记录")
    public BaseResult<UcJustCallRecord> getJustCallRecord(
            @RequestParam @ApiParam("callId") String callId) {
        UcJustCallRecord justCallRecord = ucJustCallRecordDao.findByCallId(callId);
        return BaseResult.success(justCallRecord);
    }

    @PostMapping("seat/tencent/update-assigned-phone")
    @ApiOperation("更新腾讯云坐席分配号码")
    BaseResult<Void> updateTxSeatAssignedPhone(@RequestParam @ApiParam("坐席id") Long seatId,
                                               @RequestParam(required = false) @ApiParam("分配号码") String assignedPhone) {
        if (ObjectUtil.isEmpty(assignedPhone)) {
            ucTxCallSeatDao.updateAssignedPone(seatId, assignedPhone);
            return BaseResult.successful();
        }
        Pattern pattern = Pattern.compile("^\\d+$");
        if (assignedPhone.length() < 2 || assignedPhone.length() > 20) {
            return BaseResult.failed("号码长度不在2到20");
        } else if (!pattern.matcher(assignedPhone).matches()) {
            return BaseResult.failed("包含除数字之外字符");
        }

        UcTxCallSeatInfo ucTxCallSeatInfo = ucTxCallSeatDao.findByAssignedPhone(assignedPhone);
        if (ObjectUtil.isNotEmpty(ucTxCallSeatInfo)) {
            return BaseResult.failed("分配号码重复");
        } else {
            ucTxCallSeatDao.updateAssignedPone(seatId, assignedPhone);
            return BaseResult.successful();
        }
    }

    @ApiOperation("按通话id获取腾讯云通话记录")
    @GetMapping("tencent/record-info")
    public BaseResult<TxCallRecordResp> getTxCallRecordByCallId(
            @RequestParam @ApiParam("callId") Long callId) {
        TxCallRecordResp txCallRecordResp = ucTxCallRecordDao.findRecordByCallId(callId);
        if (ObjectUtil.isNotEmpty(txCallRecordResp)) {
            UcUsers users = new UcUsers();
            if (ObjectUtil.isNotEmpty(txCallRecordResp.getServerId())) {
                users = userService.getUser(txCallRecordResp.getServerId()).orElseGet(UcUsers::new);
            }
            txCallRecordResp.setSalesName(users.getRealName());
            Map<String, UcTxCallSeatInfo> mailMap = ucTxCallSeatDao.findAllByMail(new ArrayList<>(Collections.singleton(txCallRecordResp.getCustomerMobile())));
            if (mailMap.containsKey(txCallRecordResp.getCustomerMobile())) {
                txCallRecordResp.setCustomerMobile(mailMap.get(txCallRecordResp.getCustomerMobile()).getName());
            }
        }
        return BaseResult.success(txCallRecordResp);
    }

    @ApiOperation("按通话id对应客户电话号码获取腾讯云通话记录列表")
    @GetMapping("tencent/record-list/by-call-id")
    public PageResult<List<TxCallRecordResp>> getTxCallRecordByCustomerMobileOfCallId(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = true) @ApiParam("callId") Long callId,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size) {
        TxCallRecordResp txCallRecordResp = ucTxCallRecordDao.findRecordByCallId(callId);
        Long mobileId = txCallRecordResp.getMobileId();

        if (ObjectUtil.isEmpty(mobileId)) {
            return PageResult.success();
        }
        return getTxCallRecordList(companyType, null, mobileId, null, null, null, null, null, current, size);
    }
}

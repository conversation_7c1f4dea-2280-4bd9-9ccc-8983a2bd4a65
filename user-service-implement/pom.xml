<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.shrise.radium</groupId>
        <artifactId>user-service</artifactId>
        <version>5.0625.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>user-service-implement</artifactId>
    <version>${project.parent.version}</version>
    <name>user-service-implement</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>content-service-interface</artifactId>
            <version>3.0808.2.1</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>order-service-interface</artifactId>
            <version>5.0227.2.0</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>user-service-interface</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>redisson-spring-data-27</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-21</artifactId>
            <version>3.17.0</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.17.1</version>
        </dependency>
        <dependency>
            <groupId>com.blazebit</groupId>
            <artifactId>blaze-persistence-integration-querydsl-expressions</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.blazebit</groupId>
            <artifactId>blaze-persistence-integration-hibernate-5.4</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-resolver-dns-native-macos</artifactId>
            <version>4.1.85.Final</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.lindorm</groupId>
            <artifactId>lindorm-all-client</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.15</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>wx-service-interface</artifactId>
            <version>4.0514.1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>marketing-service-interface</artifactId>
            <version>3.0225.3.5</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>spring-boot-starter-aliyun-dts</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>workwx-service-interface</artifactId>
            <version>3.0610.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>dingding-service-interface</artifactId>
            <version>3.0705.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.opensearch</groupId>
            <artifactId>aliyun-sdk-opensearch</artifactId>
            <version>3.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.1020</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>secure-service-interface</artifactId>
            <version>4.0628.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-captcha20230305</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- Thymeleaf 核心库 -->
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf</artifactId>
            <version>3.1.2.RELEASE</version>
        </dependency>
        <!-- Thymeleaf Spring 集成 -->
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf-spring5</artifactId>
            <version>3.1.2.RELEASE</version>
        </dependency>

        <!-- Flying Saucer (OpenPDF) -->
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf-openpdf</artifactId>
            <version>9.1.22</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.parent.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>

package cn.shrise.radium.complianceservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyNoticeAuditReq {

    @ApiModelProperty(value = "审核人id", hidden = true)
    private Integer userId;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("内容")
    private String remark;

    @ApiModelProperty("图片")
    private List<String> imageUrls;
}

spring:
  datasource:
    username: ydl058
    password: ydl058058
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    open-in-view: false
    show-sql: true
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

  cloud:
    function:
      definition: contentNotifyDelay;handleDrawLottery;
    stream:
      bindings:
        contentNotifyDelay-out-0:
          destination: contentNotifyDelay
          content-type: application/json
        handleDrawLottery-in-0:
          destination: contentNotifyDelay
          group: GID_handleDrawLottery
          content-type: application/json
      rocketmq:
        bindings:
          contentNotifyDelay-out-0:
            producer:
              # 延时消息必须为true  详见com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQMessageHandler.handleMessageInternal()
              sync: true
          handleDrawLottery-in-0:
            consumer:
              tags: draw_lottery

radium:
  webOssConfig:
    regionId: "cn-hangzhou"
    accesskeyId: "LTAI5t8WtM1ADaVyiyB6PWE2"
    secret: "******************************"
    videoEndPoint: "http://gs-stream-input.oss-cn-shanghai.aliyuncs.com"
    fileEndPoint: "http://gs-file-src.oss-cn-shanghai.aliyuncs.com"
    roleArn: "acs:ram::1952650058542185:role/streaminput"
    roleSessionName: "temp-session"
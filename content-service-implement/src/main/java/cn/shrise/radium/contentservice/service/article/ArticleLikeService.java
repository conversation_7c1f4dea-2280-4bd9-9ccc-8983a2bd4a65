package cn.shrise.radium.contentservice.service.article;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.entity.QSsArticleLike;
import cn.shrise.radium.contentservice.entity.SsArticleLike;
import cn.shrise.radium.contentservice.repository.ArticleLikeRepository;
import cn.shrise.radium.contentservice.resp.CountStatistics;
import cn.shrise.radium.contentservice.resp.LikeStatistics;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPADeleteClause;
import com.querydsl.jpa.impl.JPAInsertClause;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleLikeService {

    private final JPAQueryFactory queryFactory;
    private final ArticleLikeRepository articleLikeRepository;
    private final QSsArticleLike qSsArticleLike = QSsArticleLike.ssArticleLike;

    public Map<Long, Long> getLikeCount(@NonNull Collection<Long> articleIds) {
        List<Tuple> query = queryFactory.select(qSsArticleLike.articleId, qSsArticleLike.id.count().longValue())
                .from(qSsArticleLike)
                .where(qSsArticleLike.articleId.in(articleIds))
                .groupBy(qSsArticleLike.articleId)
                .fetch();
        Map<Long, Long> articleLikeMap = new HashMap<>();
        if (query != null) {
            articleLikeMap = query.stream().collect(Collectors.toMap(t -> t.get(qSsArticleLike.articleId), t -> t.get(qSsArticleLike.id.count())));
        }
        return articleLikeMap;
    }

    public List<CountStatistics> getArticleLikeCountStatistics(Collection<Long> articleIds) {
        if (ObjectUtils.isEmpty(articleIds)) {
            return Collections.emptyList();
        }
        Map<Long, Long> map = getLikeCount(articleIds);
        return articleIds.stream()
                .map(id -> new CountStatistics(id, map.getOrDefault(id, 0L)))
                .collect(Collectors.toList());
    }

    public List<SsArticleLike> findExistedByUser(@NonNull List<Long> articleList, Integer userId, Integer wxId) {
        if (Objects.isNull(userId) && Objects.isNull(wxId)) {
            return Collections.emptyList();
        }

        JPAQuery<SsArticleLike> query = queryFactory.selectFrom(qSsArticleLike)
                .where(qSsArticleLike.articleId.in(articleList));

        if (ObjectUtil.isNotEmpty(userId)) {
            query.where(qSsArticleLike.userId.eq(userId));
        }else if (ObjectUtil.isNotEmpty(wxId)) {
            query.where(qSsArticleLike.wxId.eq(wxId));
        }
        return query.fetch();
    }

    public List<LikeStatistics> getArticleLikeOperationStatistics(Collection<Long> articleList, Integer userId, Integer wxId) {
        if (ObjectUtils.isEmpty(articleList)) {
            return Collections.emptyList();
        }
        List<SsArticleLike> articleLikeList = findExistedByUser(new ArrayList<>(articleList), userId, wxId);
        Set<Long> likeMap = articleLikeList.stream().map(SsArticleLike::getArticleId).collect(Collectors.toSet());
        return articleList.stream()
                .map(id -> new LikeStatistics(id, likeMap.contains(id)))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void processArticleLike(@NonNull Long articleId, Integer userId, Integer wxId) {
        JPAQuery<SsArticleLike> query = queryFactory.selectFrom(qSsArticleLike)
                .where(qSsArticleLike.articleId.eq(articleId));

        if (ObjectUtil.isNotEmpty(userId)) {
            query.where(qSsArticleLike.userId.eq(userId));
        }else if (ObjectUtil.isNotEmpty(wxId)) {
            query.where(qSsArticleLike.wxId.eq(wxId));
        }
        SsArticleLike articleLike = query.fetchOne();
        if (ObjectUtil.isNotEmpty(articleLike)) {
            JPADeleteClause deleteQuery = queryFactory.delete(qSsArticleLike)
                    .where(qSsArticleLike.articleId.eq(articleId));
            if (ObjectUtil.isNotEmpty(userId)) {
                deleteQuery.where(qSsArticleLike.userId.eq(userId));
            }else if (ObjectUtil.isNotEmpty(wxId)) {
                deleteQuery.where(qSsArticleLike.wxId.eq(wxId));
            }
            deleteQuery.execute();
        }else {
            SsArticleLike.SsArticleLikeBuilder builder = SsArticleLike.builder().articleId(articleId).createTime(Instant.now());
            if (ObjectUtil.isNotEmpty(userId)) {
                builder.userId(userId);
            }else if (ObjectUtil.isNotEmpty(wxId)) {
                builder.wxId(wxId);
            }
            articleLikeRepository.save(builder.build());
        }
    }
}

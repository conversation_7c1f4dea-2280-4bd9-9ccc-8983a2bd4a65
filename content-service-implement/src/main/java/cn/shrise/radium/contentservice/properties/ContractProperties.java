package cn.shrise.radium.contentservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties
public class ContractProperties {


    private List<ContractConfig> contract;

    @Data
    public static class ContractConfig {

        private Integer contractType;

        private List<Integer> productLevels;
    }

    public Integer getContactType(Integer productLevel) {
        for (ContractConfig contractConfig : contract) {
            if (contractConfig.getProductLevels().contains(productLevel)) {
                return contractConfig.getContractType();
            }
        }
        return null;
    }
}
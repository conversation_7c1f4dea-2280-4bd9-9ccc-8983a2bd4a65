package cn.shrise.radium.contentservice.service.material;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.JsonUtils;
import cn.shrise.radium.common.util.UrlUtil;
import cn.shrise.radium.contentservice.constant.AuditStatusConstant;
import cn.shrise.radium.contentservice.constant.MaterialCategoryConstant;
import cn.shrise.radium.contentservice.constant.MaterialVisibleConstant;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.repository.SsMaterialOperateRecordRepository;
import cn.shrise.radium.contentservice.repository.SsMaterialRepository;
import cn.shrise.radium.contentservice.req.*;
import cn.shrise.radium.contentservice.resp.SameMaterialContentResp;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MaterialService {
    private final JPAQueryFactory queryFactory;
    private final SsMaterialRepository materialRepository;
    private final QSsMaterial qSsMaterial = QSsMaterial.ssMaterial;
    private final QSsMaterialDepartment qSsMaterialDepartment = QSsMaterialDepartment.ssMaterialDepartment;

    private final MaterialDepartmentService materialDepartmentService;
    private final MaterialContentService materialContentService;
    private final QSsMaterialOperateRecord qSsMaterialOperateRecord = QSsMaterialOperateRecord.ssMaterialOperateRecord;
    private final SsMaterialOperateRecordRepository ssMaterialOperateRecordRepository;
    private final QSsOriginMaterial qSsOriginMaterial= QSsOriginMaterial.ssOriginMaterial;

    @Transactional
    public void createOne(CreateMaterialReq req) {
        SsMaterial material = new SsMaterial();
        BeanUtil.copyProperties(req, material);
        material.setAuditStatus(AuditStatusConstant.AUDIT_EXECUTING);
        if (ObjectUtil.isNotEmpty(req.getCategory()) && (ObjectUtil.equal(req.getCategory(), MaterialCategoryConstant.PROMOTION_CUSTOMER) || ObjectUtil.equal(req.getCategory(), MaterialCategoryConstant.BRAND_TEXT)) ) {
            material.setVisibleType(MaterialVisibleConstant.ALL);
        }
        String allText = req.getMaterialContents().stream()
                .filter(i -> Objects.equals(i.getContentType(), 10))
                .map(CreateMaterialReq.MaterialContent::getContent)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(""));
        if (ObjectUtil.isNotEmpty(allText)) {
            material.setContentMd5(UrlUtil.md5ByHex(allText));
        }
        SsMaterial one = materialRepository.save(material);
        if (ObjectUtil.equals(req.getVisibleType(), MaterialVisibleConstant.DEPARTMENT)) {
            materialDepartmentService.createList(one.getId(), req.getDepartmentList());
        }
        materialContentService.createList(one.getId(), req.getMaterialContents());
        createOneOperateRecord(one.getId(), req.getCreatorId(), "创建了素材");
    }

    @Transactional
    public void updateOne(UpdateMaterialReq req) {
        SsMaterial material = new SsMaterial();
        BeanUtil.copyProperties(req, material);
        SsMaterial one = materialRepository.save(material);
        materialContentService.updateList(one.getId(), req.getMaterialContents());
    }

    @Transactional
    public void updateVisibleConfig(UpdateMaterialVisibleConfigReq req) {
        materialDepartmentService.deleteByMaterial(req.getMaterialId());
        queryFactory.update(qSsMaterial)
                .where(qSsMaterial.id.eq(req.getMaterialId()))
                .set(qSsMaterial.visibleType, req.getVisibleType())
                .execute();
        if (ObjectUtil.equals(req.getVisibleType(), MaterialVisibleConstant.DEPARTMENT) && ObjectUtil.isNotEmpty(req.getDepartmentList())) {
            materialDepartmentService.createList(req.getMaterialId(), req.getDepartmentList());
        }
    }

    public PageResult<List<SsMaterial>> getMaterialListPage(Integer companyType, List<Integer> creatorList, Integer category,
                                                            Integer contentType, List<Integer> auditStatusList,
                                                            Instant createStartTime, Instant createEndTime,
                                                            Boolean isAuditPage, Integer current, Integer size) {

        JPAQuery<SsMaterial> query = queryFactory.select(qSsMaterial).from(qSsMaterial)
                .where(qSsMaterial.companyType.eq(companyType));

        if (ObjectUtil.isNotNull(creatorList)) {
            query.where(qSsMaterial.creatorId.in(creatorList));
        }
        if (ObjectUtil.isNotNull(category) && !ObjectUtil.equal(category, "")) {
            query.where(qSsMaterial.category.eq(category));
        }
        if (ObjectUtil.isNotNull(contentType)) {
            query.where(qSsMaterial.contentType.eq(contentType));
        }
        if (ObjectUtil.isNotEmpty(auditStatusList) && ObjectUtil.isNotEmpty(auditStatusList.get(0))) {
            query.where(qSsMaterial.auditStatus.in(auditStatusList));
        }
        if (ObjectUtil.isNotNull(createStartTime)) {
            query.where(qSsMaterial.gmtCreate.goe(createStartTime));
        }
        if (ObjectUtil.isNotNull(createEndTime)) {
            query.where(qSsMaterial.gmtCreate.lt(createEndTime));
        }
        if (isAuditPage) {
            query.where(qSsMaterial.auditStatus.notIn(Arrays.asList(AuditStatusConstant.PRE_AUDIT_NOT_PASS, AuditStatusConstant.AUDIT_DELETE)));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsMaterial.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public PageResult<List<SsMaterial>> getMaterialListPage(Integer companyType, Integer userId, Set<Integer> departmentList,
                                                            Integer contentType, Instant createStartTime, Instant createEndTime,
                                                            Integer current, Integer size) {
        List<Long> materialIds = queryFactory.select(qSsMaterialDepartment.materialId).from(qSsMaterialDepartment)
                .where(qSsMaterialDepartment.departmentId.in(departmentList)).fetch();

        JPAQuery<SsMaterial> query = queryFactory.select(qSsMaterial).from(qSsMaterial)
                .where(qSsMaterial.companyType.eq(companyType))
                .where(qSsMaterial.auditStatus.eq(AuditStatusConstant.AUDIT_PASS).or(qSsMaterial.auditStatus.eq(AuditStatusConstant.PRE_AUDIT_PASS)))
                .where(qSsMaterial.category.eq(MaterialCategoryConstant.MARKETING_SERVICE))
                .where(qSsMaterial.visibleType.eq(MaterialVisibleConstant.ALL)
                        .or(qSsMaterial.visibleType.eq(MaterialVisibleConstant.USER).and(qSsMaterial.creatorId.eq(userId)))
                        .or(qSsMaterial.visibleType.eq(MaterialVisibleConstant.DEPARTMENT).and(qSsMaterial.id.in(materialIds))));

        if (ObjectUtil.isNotNull(contentType)) {
            query.where(qSsMaterial.contentType.eq(contentType));
        }
        if (ObjectUtil.isNotNull(createStartTime)) {
            query.where(qSsMaterial.gmtCreate.goe(createStartTime));
        }
        if (ObjectUtil.isNotNull(createEndTime)) {
            query.where(qSsMaterial.gmtCreate.lt(createEndTime));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsMaterial.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public PageResult<List<SsMaterial>> getMaterialListPage(Integer companyType, Integer contentType, Integer category,
                                                            Instant createStartTime, Instant createEndTime,
                                                            Integer current, Integer size) {

        JPAQuery<SsMaterial> query = queryFactory.select(qSsMaterial)
                .from(qSsMaterial)
                .where(qSsMaterial.companyType.eq(companyType))
                .where(qSsMaterial.auditStatus.eq(AuditStatusConstant.AUDIT_PASS).or(qSsMaterial.auditStatus.eq(AuditStatusConstant.PRE_AUDIT_PASS)))
                .where(qSsMaterial.category.eq(category));

        if (ObjectUtil.isNotNull(contentType)) {
            query.where(qSsMaterial.contentType.eq(contentType));
        }
        if (ObjectUtil.isNotNull(createStartTime)) {
            query.where(qSsMaterial.gmtCreate.goe(createStartTime));
        }
        if (ObjectUtil.isNotNull(createEndTime)) {
            query.where(qSsMaterial.gmtCreate.lt(createEndTime));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsMaterial.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional(rollbackFor = Exception.class)
    public void auditMaterial(MaterialAuditReq req) {

        if (ObjectUtil.equal(req.getAuditStatus(), AuditStatusConstant.AUDIT_NOT_PASS) && ObjectUtil.isEmpty(req.getAuditRemark())) {
            // 审核拒绝
            throw new BusinessException("请提交拒绝原因!");
        }

        JPAUpdateClause updateClause = queryFactory.update(qSsMaterial)
                .where(qSsMaterial.id.eq(req.getMaterialId()))
                .set(qSsMaterial.auditorId, req.getAuditorId())
                .set(qSsMaterial.auditStatus, req.getAuditStatus())
                .set(qSsMaterial.auditTime, Instant.now());

        if (ObjectUtil.isNotEmpty(req.getAuditRemark())) {
            updateClause.set(qSsMaterial.auditRemark, req.getAuditRemark());
        }
        if (ObjectUtil.isNotEmpty(req.getAuditImageList())) {
            updateClause.set(qSsMaterial.imageList, JsonUtils.toJson(req.getAuditImageList()));
        }
        updateClause.execute();
        createOneOperateRecord(req.getMaterialId(), req.getAuditorId(), ObjectUtil.equal(req.getAuditStatus(), AuditStatusConstant.AUDIT_NOT_PASS) ? "修改状态为{复审拒绝}" : "修改状态为{复审通过}");
    }

    @Transactional(rollbackFor = Exception.class)
    public void preAuditMaterial(MaterialAuditReq req) {

        if (ObjectUtil.equal(req.getAuditStatus(), AuditStatusConstant.PRE_AUDIT_NOT_PASS) && ObjectUtil.isEmpty(req.getAuditRemark())) {
            // 审核拒绝
            throw new BusinessException("请提交拒绝原因!");
        }

        JPAUpdateClause updateClause = queryFactory.update(qSsMaterial)
                .where(qSsMaterial.id.eq(req.getMaterialId()))
                .set(qSsMaterial.preAuditorId, req.getAuditorId())
                .set(qSsMaterial.auditStatus, req.getAuditStatus())
                .set(qSsMaterial.preAuditTime, Instant.now());

        if (ObjectUtil.isNotEmpty(req.getAuditRemark())) {
            updateClause.set(qSsMaterial.auditRemark, req.getAuditRemark());
        }
        if (ObjectUtil.isNotEmpty(req.getAuditImageList())) {
            updateClause.set(qSsMaterial.imageList, JsonUtils.toJson(req.getAuditImageList()));
        }
        updateClause.execute();
        createOneOperateRecord(req.getMaterialId(), req.getAuditorId(), ObjectUtil.equal(req.getAuditStatus(), AuditStatusConstant.PRE_AUDIT_NOT_PASS) ? "修改状态为{初审拒绝}" : "修改状态为{初审通过}");
    }

    public SsMaterial getMaterialById(Long id) {
        return materialRepository.findById(id).orElse(null);
    }

    public void deleteMaterialById(Long id, Integer operatorId) {
        SsMaterial ssMaterial = materialRepository.findById(id).orElse(null);
        if (ObjectUtil.isNotEmpty(ssMaterial)) {
            if (Objects.equals(ssMaterial.getAuditStatus(), AuditStatusConstant.AUDIT_EXECUTING)) {
                ssMaterial.setAuditStatus(AuditStatusConstant.AUDIT_DELETE);
                materialRepository.save(ssMaterial);
                createOneOperateRecord(id, operatorId, "修改状态为{已删除}");
            } else {
                throw new BusinessException(ContentErrorCode.UN_DELETE);
            }
        } else {
            throw new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED);
        }

    }

    public void createOneOperateRecord(Long materialId, Integer operatorId, String content) {
        SsMaterialOperateRecord build = SsMaterialOperateRecord.builder()
                .materialId(materialId)
                .operatorId(operatorId)
                .content(content)
                .build();
        ssMaterialOperateRecordRepository.save(build);
    }

    public List<SsMaterialOperateRecord> getMaterialOperateRecord(Long materialId) {
        return queryFactory.select(qSsMaterialOperateRecord)
                .from(qSsMaterialOperateRecord)
                .where(qSsMaterialOperateRecord.materialId.eq(materialId))
                .orderBy(qSsMaterialOperateRecord.gmtCreate.asc())
                .fetch();
    }

    public PageResult<List<SameMaterialContentResp>> getContentListByMd5(Long materialId, String contentMd5, Instant startTime, Instant endTime, Integer current, Integer size) {
        JPAQuery<SameMaterialContentResp> query = queryFactory
                .select(Projections.bean(SameMaterialContentResp.class,
                        qSsMaterial.id.as("materialId"),
                        qSsMaterial.gmtCreate.as("createTime"),
                        qSsMaterial.auditorId,
                        qSsMaterial.creatorId,
                        qSsMaterial.auditStatus))
                .from(qSsMaterial)
                .where(qSsMaterial.contentMd5.eq(contentMd5))
                .where(qSsMaterial.id.ne(materialId));
        if (ObjectUtil.isNotEmpty(startTime)) {
            query = query.where(qSsMaterial.gmtCreate.goe(startTime));
        }
        if (ObjectUtil.isNotEmpty(endTime)) {
            query = query.where(qSsMaterial.gmtCreate.lt(endTime));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsMaterial.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public List<SsMaterial> getMaterialByIdList(List<Long> materialIds) {
        if (ObjectUtil.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        return queryFactory.select(qSsMaterial)
                .from(qSsMaterial)
                .where(qSsMaterial.id.in(materialIds))
                .fetch();
    }

    public Map<Long, List<Long>> getMaterialIdsMapByOriginMaterialIds(List<Long> originMaterialIds) {
        if (ObjectUtil.isEmpty(originMaterialIds)) {
            return Collections.emptyMap();
        }

        List<Tuple> tuples = queryFactory.select(qSsMaterial.originMaterialId, qSsMaterial.id)
                .from(qSsMaterial)
                .where(qSsMaterial.originMaterialId.in(originMaterialIds))
                .fetch();

        return tuples.stream()
                .collect(Collectors.groupingBy(
                        tuple -> tuple.get(qSsMaterial.originMaterialId),
                        Collectors.mapping(tuple -> tuple.get(qSsMaterial.id), Collectors.toList())
                ));
    }

    @Transactional
    public void relateOriginMaterial(RelateOriginMaterialReq req) {
        materialRepository.findById(req.getMaterialId())
                .orElseThrow(() -> new BusinessException("营销素材不存在"));
        SsOriginMaterial ssOriginMaterial = queryFactory.selectFrom(qSsOriginMaterial)
                .where(qSsOriginMaterial.id.eq(req.getOriginMaterialId())).fetchOne();
        if (ObjectUtil.isEmpty(ssOriginMaterial)) {
            throw new BusinessException("好评素材不存在");
        }
        queryFactory.update(qSsMaterial)
                .set(qSsMaterial.originMaterialId, req.getOriginMaterialId())
                .where(qSsMaterial.id.eq(req.getMaterialId()))
                .execute();
        createOneOperateRecord(req.getMaterialId(), req.getOperatorId(), "关联了原始素材{" + ssOriginMaterial.getId() + "}");
        if (ObjectUtil.isEmpty(ssOriginMaterial.getLevel()) && ObjectUtil.isNotEmpty(req.getOriginMaterialLevel())) {
            queryFactory.update(qSsOriginMaterial)
                    .set(qSsOriginMaterial.level, req.getOriginMaterialLevel())
                    .where(qSsOriginMaterial.id.eq(req.getOriginMaterialId()))
                    .execute();
        }
    }

}

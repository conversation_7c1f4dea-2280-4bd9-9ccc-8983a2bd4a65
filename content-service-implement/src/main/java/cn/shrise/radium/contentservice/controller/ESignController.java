package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.contentservice.entity.SsEsignInfo;
import cn.shrise.radium.contentservice.entity.SsEsignTemplate;
import cn.shrise.radium.contentservice.entity.SsEsignTemplateRecord;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.req.UpdateSignInfoReq;
import cn.shrise.radium.contentservice.resp.TemplateResp;
import cn.shrise.radium.contentservice.service.ESignService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

@RestController
@RequestMapping("sign")
@RequiredArgsConstructor
public class ESignController {
    private final ESignService eSignService;

    @ApiOperation("废掉签字")
    @PostMapping("abandon")
    public BaseResult<String> abandonSign(
            @RequestParam @ApiParam("AccountUserId") String accountUserId
    ) {
        ContentErrorCode contentErrorCode = eSignService.abandonSign(accountUserId);
        return BaseResult.create(contentErrorCode.getCode(), contentErrorCode.getMsg());
    }

    @ApiOperation("根据id废掉签字")
    @PostMapping("abandonById")
    public BaseResult<String> abandonSignById(
            @RequestParam @ApiParam("SignId") Integer signId
    ) {
        eSignService.abandonSign(signId);
        return BaseResult.create(ContentErrorCode.SUCCESS);
    }

    @GetMapping
    @ApiOperation("获取签字信息")
    public BaseResult<SsEsignInfo> getEsignInfo(@RequestParam Integer companyType,
                                                @RequestParam String flowId) {
        SsEsignInfo esignInfo = eSignService.getEsignInfo(companyType, flowId)
                .orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(esignInfo);
    }

    @GetMapping("byId")
    @ApiOperation("根据id获取签字信息")
    public BaseResult<SsEsignInfo> getSignInfoById(@RequestParam Integer signId) {
        SsEsignInfo signInfo = eSignService.findOneById(signId);
        return BaseResult.success(signInfo);
    }

    @GetMapping("batch")
    @ApiOperation("批量获取签字信息")
    BaseResult<List<SsEsignInfo>> getBatchSignInfo(
            @RequestParam @ApiParam("签字ID列表") Collection<Integer> signIds
    ) {
        List<SsEsignInfo> ssEsignInfos = eSignService.getBatchSignInfo(signIds);
        return BaseResult.success(ssEsignInfos);
    }

    @PostMapping("create")
    @ApiOperation("创建签字信息")
    public BaseResult<SsEsignInfo> createSignInfo(
            @RequestParam Integer company,
            @RequestParam String userId,
            @RequestParam String accountId,
            @RequestParam Integer signType,
            @RequestParam Long payCompanyId,
            @RequestParam String appId) {
        SsEsignInfo signInfo = eSignService.createOne(company, userId, accountId, signType, payCompanyId, appId);
        return BaseResult.success(signInfo);
    }

    @PostMapping("update")
    @ApiOperation("更新签字信息")
    public BaseResult<String> updateSignInfo(
            @RequestParam Integer signId,
            @RequestBody @Validated UpdateSignInfoReq info) {
        eSignService.updateById(signId, info);
        return BaseResult.create(ContentErrorCode.SUCCESS);
    }

    @GetMapping("operate/record/list")
    @ApiOperation("修改模板操作记录")
    public BaseResult<List<SsEsignTemplateRecord>> getESignTemplateOperateRecordList(
            @RequestParam @ApiParam("场景编号") Integer signType,
            @RequestParam @ApiParam("应用ID") String appId) {
        List<SsEsignTemplateRecord> record = eSignService.getESignTemplateOperateRecordList(signType, appId);
        return BaseResult.success(record);
    }

    @PostMapping("template/update")
    @ApiOperation("修改模板")
    public BaseResult<Void> templateIdUpdate(
            @RequestParam @ApiParam("场景编号") Integer signType,
            @RequestParam @ApiParam("应用ID") String appId,
            @RequestParam @ApiParam("模板ID") String templateId,
            @RequestParam @ApiParam("操作人ID") Integer operatorId) {
        eSignService.templateIdUpdate(signType, appId, templateId, operatorId);
        return BaseResult.successful();
    }

    @GetMapping("template/list")
    @ApiOperation("模板列表")
    public PageResult<List<TemplateResp>> getTemplateList(
            @RequestParam(required = false) @ApiParam("应用ID") String appId,
            @RequestParam(required = false) @ApiParam("启用状态") Boolean enabled,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return eSignService.getTemplateList(appId, enabled, current, size);
    }

    @GetMapping("template/info/list")
    @ApiOperation("模板信息列表")
    public BaseResult<List<SsEsignTemplate>> getTemplateInfoList() {
        List<SsEsignTemplate> resp = eSignService.getTemplateInfoList();
        return BaseResult.success(resp);
    }

    @GetMapping("template/appId")
    @ApiOperation("根据模板ID获取appId")
    public BaseResult<String> getTemplateAppId(
            @RequestParam @ApiParam("模板id") String templateId) {
        String appId = eSignService.getTemplateAppId(templateId);
        return BaseResult.success(appId);
    }

    @PostMapping("template/enabled")
    @ApiOperation("是否启用")
    public BaseResult<Void> setTemplateEnabled(
            @RequestParam @ApiParam("模板id") String templateId,
            @RequestParam @ApiParam("状态") Boolean enabled) {
        eSignService.setTemplateEnabled(templateId, enabled);
        return BaseResult.successful();
    }

    @PostMapping("template/create")
    @ApiOperation("新增合同模板")
    public BaseResult<SsEsignTemplate> createTemplate(
            @RequestParam @ApiParam("应用ID") String appId,
            @RequestParam @ApiParam("模板id") String templateId,
            @RequestParam(required = false) @ApiParam("模板名称") String templateName
    ) {
        SsEsignTemplate ssEsignTemplate = eSignService.createTemplate(appId, templateId, templateName);
        return BaseResult.success(ssEsignTemplate);
    }

    @GetMapping("get/template-id")
    @ApiOperation("获取签字模板ID")
    public BaseResult<String> getTemplateId(
            @RequestParam @ApiParam("场景编号") Integer signType,
            @RequestParam @ApiParam("应用ID") String appId) {
        String templateId = eSignService.getTemplateId(signType, appId);
        return BaseResult.success(templateId);
    }

    @GetMapping("template/id")
    @ApiOperation("根据id获取模板信息")
    public BaseResult<SsEsignTemplate> getTemplateById(@RequestParam @ApiParam("模板ID") String templateId) {
        SsEsignTemplate ssEsignTemplate = eSignService.getTemplateById(templateId);
        return BaseResult.success(ssEsignTemplate);
    }
}

package cn.shrise.radium.contentservice.service.evaluation;

import cn.shrise.radium.contentservice.entity.*;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ChoiceService {

    private final JPAQueryFactory queryFactory;

    private final QSsEvaluationAnswer evaluationAnswer = QSsEvaluationAnswer.ssEvaluationAnswer;
    private final QSsSurveyChoice surveyChoice = QSsSurveyChoice.ssSurveyChoice;
    private final QSsSurveyTopic surveyTopic = QSsSurveyTopic.ssSurveyTopic;

    public SsSurveyChoice findUserChoice(Integer evaluationId, String content) {
        return queryFactory.select(surveyChoice).from(evaluationAnswer)
                .leftJoin(surveyChoice).on(evaluationAnswer.choiceId.eq(surveyChoice.id))
                .leftJoin(surveyTopic).on(surveyChoice.topicId.eq(surveyTopic.id))
                .where(evaluationAnswer.evaluationId.eq(evaluationId))
                .where(surveyTopic.content.eq(content)).fetchOne();
    }

    public List<SsSurveyTopic> getTopicList(Integer surveyType) {
        return queryFactory.selectFrom(surveyTopic)
                .where(surveyTopic.surveyType.eq(surveyType))
                .where(surveyTopic.disabled.eq(false))
                .fetch();
    }

    /**
     * 获取题目
     * @param surveyType
     * @param order 第几题
     * @return
     */
    public Optional<SsSurveyTopic> getTopicList(Integer surveyType, Integer order) {
        return getTopicList(surveyType).stream().skip(order - 1).findFirst();
    }

    public List<SsSurveyChoice> getChoiceList(Long topicId) {
        return queryFactory.selectFrom(surveyChoice)
                .where(surveyChoice.topicId.eq(topicId))
                .fetch();
    }


    public List<SsSurveyChoice> getEvaluationChoiceList(Integer evaluationId, Integer surveyType, Integer order) {
        Optional<SsSurveyTopic> optional = getTopicList(surveyType, order);

        if (!optional.isPresent()) {
            return Collections.emptyList();
        }
        SsSurveyTopic topic = optional.get();
        return getEvaluationChoiceList(evaluationId, topic.getId());
    }

    public List<SsSurveyChoice> getEvaluationChoiceList(Integer evaluationId, Long topicId) {
        return queryFactory.select(surveyChoice)
                .from(evaluationAnswer)
                .leftJoin(surveyChoice)
                .on(evaluationAnswer.choiceId.eq(surveyChoice.id))
                .where(evaluationAnswer.evaluationId.eq(evaluationId))
                .where(surveyChoice.topicId.eq(topicId))
                .fetch();
    }
}

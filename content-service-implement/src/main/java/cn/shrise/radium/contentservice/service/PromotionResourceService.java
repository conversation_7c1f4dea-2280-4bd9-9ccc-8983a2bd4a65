package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.QSsPromotionResourceShareLink;
import cn.shrise.radium.contentservice.entity.QSsPromotionResourceWxLink;
import cn.shrise.radium.contentservice.entity.SsPromotionResourceShareLink;
import cn.shrise.radium.contentservice.entity.SsPromotionResourceWxLink;
import cn.shrise.radium.contentservice.resp.PromotionResourceFolderResp;
import cn.shrise.radium.contentservice.resp.PromotionResourceWxLinkResp;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class PromotionResourceService {

    private final OSS ossClient;
    @Value(value = "${promotion.oss.bucket.name}")
    private String bucketName;
    private final JPAQueryFactory queryFactory;
    private final QSsPromotionResourceShareLink promotionResourceShareLink = QSsPromotionResourceShareLink.ssPromotionResourceShareLink;
    private final QSsPromotionResourceWxLink promotionResourceWxLink = QSsPromotionResourceWxLink.ssPromotionResourceWxLink;
    private final JdbcTemplate jdbcTemplate;

    @Value(value = "${promotion.resource.link.url}")
    private String linkUrl;

    public ListObjectsV2Result listObjectsV2Result(String keyPrefix, String delimiter, Integer maxKeys, String nextContinuationToken) {

        ListObjectsV2Request listObjectsV2Request = new ListObjectsV2Request(bucketName);
        listObjectsV2Request.withMaxKeys(maxKeys);
        if (keyPrefix != null) {
            listObjectsV2Request.setStartAfter(keyPrefix);
            listObjectsV2Request.setPrefix(keyPrefix);
        }
        if (delimiter != null) {
            listObjectsV2Request.setDelimiter(delimiter);
        }
        listObjectsV2Request.setContinuationToken(nextContinuationToken);
        return ossClient.listObjectsV2(listObjectsV2Request);
    }

    public void createPromotionResourceFolder(String folderName, Integer userId) {
        log.info("创建推广资源文件夹：{},userId:{}", folderName, userId);
        boolean isExist = ossClient.doesObjectExist(bucketName, folderName + "/");
        if (isExist) {
            throw new BusinessException("该文件夹名已存在，请重新编辑");
        }
        String content = "";
        // 创建PutObjectRequest对象。
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, folderName + "/", new ByteArrayInputStream(content.getBytes()));
        ossClient.putObject(putObjectRequest);
    }

    public void deletePromotionResourceFolder(String folderName, Integer userId) {
        boolean isExist = ossClient.doesObjectExist(bucketName, folderName + "/");
        if (!isExist) {
            throw new BusinessException("该文件夹名已删除，请刷新重试");
        }
        log.info("删除推广资源文件夹：{},userId:{}", folderName, userId);
        // 删除目录及目录下的所有文件。
        String nextMarker = null;
        ObjectListing objectListing = null;
        do {
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName)
                    .withPrefix(folderName + "/")
                    .withMarker(nextMarker);

            objectListing = ossClient.listObjects(listObjectsRequest);
            if (objectListing.getObjectSummaries().size() > 0) {
                List<String> keys = new ArrayList<>();
                for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
                    keys.add(s.getKey());
                }
                DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName).withKeys(keys).withEncodingType("url");
                DeleteObjectsResult deleteObjectsResult = ossClient.deleteObjects(deleteObjectsRequest);
                deleteObjectsResult.getDeletedObjects();
            }

            nextMarker = objectListing.getNextMarker();
        } while (objectListing.isTruncated());
    }

    public void deletePromotionResourceFile(String path, Integer userId) {
        boolean isExist = ossClient.doesObjectExist(bucketName, path);
        if (!isExist) {
            throw new BusinessException("该文件名已删除，请刷新重试");
        }
        log.info("删除推广资源文件：{},userId:{}", path, userId);
        ossClient.deleteObject(bucketName, path);
    }

    @Transactional
    public String promotionResourceLinkUrl(String folderName, Integer dayType, Integer wxId) {
        if (ObjectUtil.isNotNull(wxId)) {
            SsPromotionResourceWxLink ssPromotionResourceWxLink = queryFactory.select(promotionResourceWxLink).from(promotionResourceWxLink)
                    .where(promotionResourceWxLink.wxId.eq(wxId))
                    .where(promotionResourceWxLink.folderName.eq(folderName))
                    .fetchOne();
            if (ObjectUtil.isNotEmpty(ssPromotionResourceWxLink)) {
                return ssPromotionResourceWxLink.getLinkNumber();
            }
            String number = getNumber(folderName, dayType);
            SsPromotionResourceWxLink build = SsPromotionResourceWxLink.builder()
                    .folderName(folderName)
                    .wxId(wxId)
                    .linkNumber(number)
                    .build();
            String sql = SqlUtil.batchInsertSql(Collections.singletonList(build));
            jdbcTemplate.execute(sql);
            return number;
        }
        String number = getNumber(folderName, dayType);
        return linkUrl + number;
    }

    private String getNumber(String folderName, Integer dayType) {
        LocalDateTime expireTime;
        if (dayType == 999) {
            expireTime = LocalDate.of(2100, 1, 1).atStartOfDay();
        } else {
            expireTime = LocalDate.now().atStartOfDay().plusDays(dayType);
        }
        SsPromotionResourceShareLink exist = queryFactory.select(promotionResourceShareLink).from(promotionResourceShareLink)
                .where(promotionResourceShareLink.folderName.eq(folderName))
                .where(promotionResourceShareLink.expireTime.eq(expireTime.atZone(ZoneId.systemDefault()).toInstant()))
                .where(promotionResourceShareLink.enabled.eq(true))
                .fetchFirst();
        if (ObjectUtil.isNotEmpty(exist)) {
            return exist.getNumber();
        }
        String number = UUID.randomUUID().toString().replaceAll("-", "");
        SsPromotionResourceShareLink build = SsPromotionResourceShareLink.builder()
                .folderName(folderName)
                .number(number)
                .expireTime(expireTime.atZone(ZoneId.systemDefault()).toInstant())
                .enabled(true)
                .build();
        String sql = SqlUtil.batchInsertSql(Collections.singletonList(build));
        jdbcTemplate.execute(sql);
        return number;
    }

    public SsPromotionResourceShareLink getPromotionResourceShareLink(String number) {
        return queryFactory.select(promotionResourceShareLink).from(promotionResourceShareLink)
                .where(promotionResourceShareLink.number.eq(number))
                .where(promotionResourceShareLink.enabled.eq(true))
                .fetchFirst();
    }

    public PageResult<List<PromotionResourceWxLinkResp>> promotionResourceWxLinkList(Integer current, Integer size) {
        JPAQuery<PromotionResourceWxLinkResp> query = queryFactory.select(Projections.bean(PromotionResourceWxLinkResp.class,
                        promotionResourceWxLink.folderName,
                        promotionResourceWxLink.wxId,
                        promotionResourceWxLink.linkNumber,
                        promotionResourceWxLink.gmtCreate,
                        promotionResourceShareLink.expireTime)).from(promotionResourceWxLink)
                .leftJoin(promotionResourceShareLink)
                .on(promotionResourceWxLink.linkNumber.eq(promotionResourceShareLink.number))
                .orderBy(promotionResourceWxLink.gmtCreate.desc());
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public BaseResult<List<PromotionResourceFolderResp>> getMyPromotionResourceFolder(Integer wxId) {
        List<PromotionResourceFolderResp> fetch = queryFactory.select(Projections.bean(PromotionResourceFolderResp.class,
                        promotionResourceWxLink.folderName,
                        promotionResourceWxLink.wxId,
                        promotionResourceWxLink.linkNumber,
                        promotionResourceWxLink.gmtCreate,
                        promotionResourceShareLink.expireTime)).from(promotionResourceWxLink)
                .leftJoin(promotionResourceShareLink)
                .on(promotionResourceWxLink.linkNumber.eq(promotionResourceShareLink.number))
                .where(promotionResourceWxLink.wxId.eq(wxId))
                .orderBy(promotionResourceWxLink.gmtCreate.desc())
                .fetch();
        return BaseResult.success(fetch);
    }
}

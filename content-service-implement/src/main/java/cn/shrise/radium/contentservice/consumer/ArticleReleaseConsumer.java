package cn.shrise.radium.contentservice.consumer;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.service.article.ArticleService;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = ContentServiceConst.CONTENT_TOPIC, consumerGroup = ContentServiceConst.MqGroupType.GID_articleRelease,
        selectorType = ExpressionType.TAG, selectorExpression = ContentServiceConst.MqTagType.ARTICLE_RELEASE)
public class ArticleReleaseConsumer implements MessageListener {

    private final ArticleService articleService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        Long articleId = JSON.parseObject(new String(message.getBody()), Long.class);
        articleService.releaseArticle(articleId);
        return Action.CommitMessage;
    }
}

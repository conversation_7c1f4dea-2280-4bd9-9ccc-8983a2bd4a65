package cn.shrise.radium.contentservice.consumer;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.service.business.LotteryService;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = ContentServiceConst.CONTENT_TOPIC,
        consumerGroup = ContentServiceConst.MqGroupType.GID_DRAW_LOTTERY,
        selectorExpression = ContentServiceConst.MqTagType.DRAW_LOTTERY)
public class LotteryConsumer implements MessageListener {

    private final LotteryService lotteryService;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        Long value = JSON.parseObject(message.getBody(), Long.class);
        log.info("活动{}准备开奖", value);
        lotteryService.handleDrawLottery(value);
        return Action.CommitMessage;
    }
}

package cn.shrise.radium.contentservice.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.contentservice.dao.SsRoomLotteryDetailDao;
import cn.shrise.radium.contentservice.dto.UserRoomLotteryDetailDto;
import cn.shrise.radium.contentservice.entity.SsRoomLottery;
import cn.shrise.radium.contentservice.entity.SsRoomLotteryDetail;
import cn.shrise.radium.contentservice.properties.RoomLotteryProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.SelectorType;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static cn.shrise.radium.common.constant.RocketMQ5Constant.*;
import static cn.shrise.radium.contentservice.service.RoomLotteryService.LOTTERY_PRIZE_COUNT_KEY;


@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(topic = TOPIC_COMMON_DELAY, consumerGroup = GID_CONTENT_LOTTERY_JOIN,
        selectorType = SelectorType.TAG, selectorExpression = TAG_CONTENT_LOTTERY_JOIN)
public class LotteryJoinConsumer implements RocketMQListener<SsRoomLottery> {

    private final SsRoomLotteryDetailDao ssRoomLotteryDetailDao;
    private final RoomLotteryProperty  roomLotteryProperty;
    private final StringRedisTemplate stringRedisTemplate;
    private final RocketMqUtils rocketMqUtils;
    private final String LOTTERY_LOCK_KEY = "lock:lottery:%d";

    @Override
    public void onMessage(SsRoomLottery lottery) {
        long timeMillis = System.currentTimeMillis();
        Long lotteryId = lottery.getId();
       String lockKey = String.format(LOTTERY_LOCK_KEY, lotteryId);
        Boolean alreadyProcessed = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", Duration.ofSeconds(10));
        if (alreadyProcessed == null || !alreadyProcessed) {
            log.error("抽奖详情消息已处理重复消费，lotteryId:{}", lotteryId);
            rocketMqUtils.convertAndSend(TOPIC_COMMON_DELAY, TAG_CONTENT_LOTTERY_JOIN, 3 * 1000, lottery);
            return;
        }
        processLotteryPrize(lottery);
        stringRedisTemplate.delete(lockKey);
        log.info("用户参与抽奖批处理结束lotteryId:{}time:{}", lotteryId, System.currentTimeMillis() - timeMillis);
    }

    private void processLotteryPrize(SsRoomLottery lottery) {
        Map<Long, List<String>> lotteriesMap = roomLotteryProperty.getLotteries();
        Optional<Long> optional = lotteriesMap.entrySet().stream()
                .filter(entry -> entry.getValue().contains(lottery.getNumber()))
                .map(Map.Entry::getKey)
                .findFirst();
        Long lotteryConfigId = null;
        if (!optional.isPresent()) {
            log.error("活动编号配置不存在,number:{}", lottery.getNumber());
            return;
        } else {
            lotteryConfigId = optional.get();
        }

        List<UserRoomLotteryDetailDto> dtoList = ssRoomLotteryDetailDao.getRoomLotteryDetailWithUserJoinCount(lottery.getId(), lotteriesMap.get(lotteryConfigId));
        log.info("用户参与抽奖批处理开始lotteryId:{},size:{}", lottery.getId(), dtoList.size());
        if (ObjectUtil.isNotEmpty(dtoList)) {
            List<Long> first = new ArrayList<>();
            List<Long> second  = new ArrayList<>();
            List<SsRoomLotteryDetail> third = new ArrayList<>();
            for (UserRoomLotteryDetailDto dto : dtoList) {
                Integer userJoinCount = dto.getUserJoinCount();
                SsRoomLotteryDetail detail = dto.getDetail();
                if (userJoinCount == 0) {
                    first.add(detail.getId());
                } else if (userJoinCount == 1) {
                    second.add(detail.getId());
                } else if (userJoinCount == 2) {
                    third.add(detail);
                } else {
                    log.error("用户参与抽奖次数超过3次,detail:{}", JSON.toJSONString(dto.getDetail()));
                }
            }

            if (ObjectUtil.isNotEmpty(first)) {
                List<List<Long>> partition = Lists.partition(first, 2000);
                partition.forEach(ids -> {
                    ssRoomLotteryDetailDao.updateLotteryDetail(ids, 63L);
                });
            }
            if (ObjectUtil.isNotEmpty(second)) {
                List<List<Long>> partition = Lists.partition(second, 2000);
                partition.forEach(ids -> {
                    ssRoomLotteryDetailDao.updateLotteryDetail(ids, 62L);
                });
            }
            if (ObjectUtil.isNotEmpty(third)) {
                String format = String.format(LOTTERY_PRIZE_COUNT_KEY, lotteryConfigId, lottery.getNumber());
                Map<String, String> redisEntries = stringRedisTemplate.<String, String>opsForHash().entries(format);
                List<Long> prizeList = new ArrayList<>();
                for (Map.Entry<String, String> entry : redisEntries.entrySet()) {
                    long id = Long.parseLong(entry.getKey());
                    int count = Integer.parseInt(entry.getValue());
                    for (int i = 0; i < count; i++) {
                        prizeList.add(id);
                    }
                }
                // 打乱奖品数组
                Collections.shuffle(prizeList);
                Map<Long, Integer> winPrizeMap = new HashMap<>();
                for (int i = 0; i < third.size(); i++) {
                    SsRoomLotteryDetail detail = third.get(i);
                    if (i < prizeList.size()) {
                        detail.setPrizeId(prizeList.get(i));
                        winPrizeMap.put(detail.getPrizeId(), winPrizeMap.getOrDefault(prizeList.get(i), 0) + 1);
                    } else {
                        detail.setPrizeId(64L);
                        log.error("奖品抽取失败，返回安慰奖,detail:{}", JSON.toJSONString(detail));
                    }
                    detail.setIsWin(true);
                }
                decreasePrizeStockAtomicBatch(winPrizeMap, format);
                ssRoomLotteryDetailDao.updateLotteryDetailBatch(third);
            }
        }
        if (lottery.getStartTime().plus(lottery.getDuration() * 60 + 10, ChronoUnit.SECONDS).isAfter(Instant.now())) {
            rocketMqUtils.convertAndSend(TOPIC_COMMON_DELAY, TAG_CONTENT_LOTTERY_JOIN, 3 * 1000, lottery);
        } else {
            log.info("活动抽奖批处理结束,lotteryId:{}", lottery.getId());
        }
    }

    private void decreasePrizeStockAtomicBatch( Map<Long, Integer> winPrizeMap, String key) {
        /*String luaScript =
                "local key = KEYS[1];" +
                        "local results = {};" +
                        "for i = 1, #ARGV do " +
                        "  local field = ARGV[i];" +
                        "  local stock = redis.call('hget', key, field);" +
                        "  if stock and tonumber(stock) > 0 then " +
                        "    redis.call('hincrby', key, field, -1);" +
                        "    table.insert(results, 1); " +  // 1 表示成功
                        "  else " +
                        "    table.insert(results, 0); " +  // 0 表示失败
                        "  end " +
                        "end;" +
                        "return results;";

        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(luaScript);
        redisScript.setResultType(List.class);

        List<String> args = prizeIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());

        List<Object> rawResult  = stringRedisTemplate.execute(
                redisScript,
                Collections.singletonList(key),
                args.toArray(new String[0])
        );

        return rawResult.stream()
                .map(obj -> {
                    if (obj instanceof Number) {
                        return ((Number) obj).longValue();
                    } else {
                        return Long.parseLong(obj.toString());
                    }
                })
                .collect(Collectors.toList());*/
        for (Map.Entry<Long, Integer> entry : winPrizeMap.entrySet()) {
            Long increment = stringRedisTemplate.opsForHash().increment(key, String.valueOf(entry.getKey()), -entry.getValue());
            log.info("奖品库存减少成功,key:{},entry:{},value:{}", key, JSON.toJSONString(entry), increment);
        }
    }

}

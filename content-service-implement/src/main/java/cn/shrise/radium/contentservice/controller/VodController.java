package cn.shrise.radium.contentservice.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.contentservice.conf.VodConfiguration;
import cn.shrise.radium.contentservice.constant.VodBehaviorConstant;
import cn.shrise.radium.contentservice.entity.SsVideoTranscode;
import cn.shrise.radium.contentservice.properties.VideoConfigProperty;
import cn.shrise.radium.contentservice.req.CreateSsSalesVideoRecordReq;
import cn.shrise.radium.contentservice.req.VodCreateUploadReq;
import cn.shrise.radium.contentservice.resp.StsTokenResp;
import cn.shrise.radium.contentservice.resp.VodCreateUploadResp;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import cn.shrise.radium.contentservice.service.vod.AliVodService;
import cn.shrise.radium.contentservice.service.SsSalesVideoRecordService;
import cn.shrise.radium.contentservice.service.VideoTranscodeService;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.vod.model.v20170321.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("vod")
@RequiredArgsConstructor
public class VodController {

    private final AliVodService aliVodService;
    private final SsSalesVideoRecordService salesVideoRecordService;
    private final VodConfiguration vodConfiguration;
    private final VideoConfigProperty videoConfigProperty;
    private final VideoTranscodeService videoTranscodeService;
    private final ShortVideoService shortVideoService;

    @GetMapping("cate/list")
    @ApiOperation("获取vod分类列表")
    public BaseResult<List<GetCategoriesResponse.Category>> getCompanyCateList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false, defaultValue = "-1") @ApiParam("分类ID") Long cateId
    ) {
        List<GetCategoriesResponse.Category> companyCateList = aliVodService.getCompanyCateList(companyType, cateId);
        return BaseResult.success(companyCateList);
    }

    @GetMapping("transcode/list")
    @ApiOperation("获取转码模组列表")
    public BaseResult<List<ListTranscodeTemplateGroupResponse.TranscodeTemplateGroup>> getTranscodeTemplateGroupList() {
        List<ListTranscodeTemplateGroupResponse.TranscodeTemplateGroup> transcodeTemplateGroupList = aliVodService.getTranscodeTemplateGroupList();
        return BaseResult.success(transcodeTemplateGroupList);
    }

    @GetMapping("video/create")
    @ApiOperation("获取视频上传地址和凭证")
    public BaseResult<CreateUploadVideoResponse> createUploadVideo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("创建人id") Integer creatorId,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("视频类型") String videoType,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL) {
        CreateUploadVideoResponse uploadVideo = aliVodService.createUploadVideo(title, fileName, cateId, templateGroupId,
                description, coverURL, "vod/sales/video/upload", VodBehaviorConstant.SsSalesVideoRecord);
        CreateSsSalesVideoRecordReq req = CreateSsSalesVideoRecordReq.builder()
                .companyType(companyType)
                .creatorId(creatorId)
                .requestId(uploadVideo.getRequestId())
                .uploadAuth(uploadVideo.getUploadAuth())
                .uploadAddress(uploadVideo.getUploadAddress())
                .videoId(uploadVideo.getVideoId())
                .title(title)
                .videoType(videoType)
                .build();
        salesVideoRecordService.createOne(req);
        return BaseResult.success(uploadVideo);
    }

    @GetMapping("video/create/normal")
    @ApiOperation("获取视频上传地址和凭证")
    public BaseResult<CreateUploadVideoResponse> createUploadVideoNormal(
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL) {
        CreateUploadVideoResponse uploadVideo = aliVodService.createUploadVideo(title, fileName, cateId, templateGroupId,
                description, coverURL, null, null);
        return BaseResult.success(uploadVideo);
    }

    @PostMapping("video/upload")
    @ApiOperation("获取vod视频上传地址和凭证")
    public BaseResult<VodCreateUploadResp> vodCreateUploadVideo(@RequestBody @Valid VodCreateUploadReq vodCreateUploadReq) {
        VodCreateUploadResp uploadVideo = aliVodService.vodCreateUploadVideo(vodCreateUploadReq);
        return BaseResult.success(uploadVideo);
    }

    @PostMapping("video/update")
    @ApiOperation("更新视频信息")
    public BaseResult<Void> updateVideoInfo(
            @RequestParam @ApiParam("视频id") String videoId,
            @RequestParam(required = false) @ApiParam("标题") String title,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL) {
        UpdateVideoInfoResponse updateVideo = aliVodService.updateVideoInfo(videoId, title, description, coverURL);
        return BaseResult.successful();
    }

    @GetMapping("video/upload/refresh")
    @ApiOperation("刷新视频上传凭证")
    public BaseResult<RefreshUploadVideoResponse> refreshUploadVideo(
            @RequestParam @ApiParam("视频id") String videoId) {
        RefreshUploadVideoResponse refreshUploadVideoResponse = aliVodService.refreshUploadVideo(videoId);
        return BaseResult.success(refreshUploadVideoResponse);
    }

    @GetMapping("video/auth")
    @ApiOperation("获取播放凭证")
    public BaseResult<GetVideoPlayAuthResponse> getVideoPlayAuth(
            @RequestParam @ApiParam("视频id") String videoId) {
        GetVideoPlayAuthResponse videoPlayAuth = aliVodService.getVideoPlayAuth(videoId);
        return BaseResult.success(videoPlayAuth);
    }

    @GetMapping("video/play")
    @ApiOperation("获取播放信息")
    public BaseResult<GetPlayInfoResponse> getVideoPlayInfo(
            @RequestParam @ApiParam("视频id") String videoId) {
        GetPlayInfoResponse videoPlayInfo = aliVodService.getPlayInfo(videoId);
        return BaseResult.success(videoPlayInfo);
    }

    @PostMapping("video/upload/result")
    @ApiOperation("视频上传结果")
    public BaseResult<Void> handleUploadVideoResult(
            @RequestParam @ApiParam("body") String body) {
        aliVodService.videoUploadResult(body);
        return BaseResult.successful();
    }

    @PostMapping("short/video/upload/result")
    @ApiOperation("短视频上传结果")
    public BaseResult<Void> handleUploadShortVideoResult(
            @RequestParam @ApiParam("body") String body) {
        shortVideoService.shortVideoUploadResult(body);
        return BaseResult.successful();
    }

    @GetMapping("sts/token")
    @ApiOperation("获取STS临时token")
    public BaseResult<StsTokenResp> getStsTokenResp() throws ClientException, ParseException {
        AssumeRoleResponse assumeRoleResponse = vodConfiguration.getAssumeRoleResponse();
        StsTokenResp stsTokenResp = new StsTokenResp();
        if (ObjectUtil.isNotNull(assumeRoleResponse)) {
            BeanUtil.copyProperties(assumeRoleResponse.getCredentials(), stsTokenResp);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
            Date date = format.parse(stsTokenResp.getExpiration());
            Instant instant = date.toInstant();
            stsTokenResp.setOutTime(instant.plusMillis(TimeUnit.HOURS.toMillis(8)).getEpochSecond());
        }
        stsTokenResp.setRegion(videoConfigProperty.getRegionId());
        return BaseResult.success(stsTokenResp);
    }

    @PostMapping("short/video/tag/result")
    @ApiOperation("短视频智能标签结果")
    public BaseResult<Void> handleShortVideoTagResult(
            @RequestBody @ApiParam("body") String body) {
        aliVodService.handleShortVideoTagResult(body);
        return BaseResult.successful();
    }

    @GetMapping("video/search")
    @ApiOperation("视频搜索")
    public PageResult<List<SearchMediaResponse.Media>> vodVideoSearch(
            @RequestParam(required = false) @ApiParam("标题") String title,
            @RequestParam @ApiParam("分类id, 全部：-1") Long cateId,
            @RequestParam @ApiParam("是否顺序") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("状态") String status,
            @RequestParam(required = false) @ApiParam("开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return aliVodService.searchMedia(title, cateId, isAsc, status, startTime, endTime, current, size);
    }

    @GetMapping("video/transcode")
    @ApiOperation("获取视频转码状态")
    public BaseResult<SsVideoTranscode> getVideoTranscode(@RequestParam String videoId) {
        SsVideoTranscode ssVideoTranscode = videoTranscodeService.getVideoTranscode(videoId)
                .orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(ssVideoTranscode);
    }
}

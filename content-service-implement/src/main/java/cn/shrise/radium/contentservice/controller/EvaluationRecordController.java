package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.contentservice.entity.EvaluationIdRecord;
import cn.shrise.radium.contentservice.entity.EvaluationRecord;
import cn.shrise.radium.contentservice.entity.SsCustomerEvaluation;
import cn.shrise.radium.contentservice.req.IdVerifyReq;
import cn.shrise.radium.contentservice.service.evaluation.CustomerEvaluationService;
import cn.shrise.radium.contentservice.service.evaluation.EvaluationRecordService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("evaluation/record")
@RequiredArgsConstructor
public class EvaluationRecordController {

    private final EvaluationRecordService recordService;

    private final CustomerEvaluationService customerEvaluationService;

    @GetMapping("evaluationRecord")
    @ApiOperation("获取用户测评操作记录")
    public PageResult<List<EvaluationRecord>> getEvaluationRecord(
            @RequestParam @ApiParam("更新的属性") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return recordService.getByUser(userId,current,size);
    }

    @GetMapping("evaluationIdRecord")
    @ApiOperation("获取用户认证操作记录")
    public PageResult<List<EvaluationIdRecord>> getEvaluationIdRecord(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return recordService.getEvaluationIdRecord(userId,current,size);
    }

    @GetMapping("redoEvaluation")
    @ApiOperation("重新测评")
    public BaseResult<SsCustomerEvaluation> redoEvaluation(
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("操作人") Integer userId) {
        SsCustomerEvaluation evaluation = recordService.evaluationRedo(evaluationId, userId);
        evaluation.setIdentityNumber(AESUtil.decrypt(evaluation.getIdentityNumber()));
        return BaseResult.success(evaluation);
    }

    @GetMapping("evaluationIdRedo")
    @ApiOperation("重新认证")
    public BaseResult<String> evaluationIdRedo(
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("操作人") Integer userId,
            @RequestParam @ApiParam("是否废掉测评次数") Boolean isAbolish) {
        recordService.evaluationIdRedo(evaluationId, userId, isAbolish);
        return BaseResult.success();
    }

    @GetMapping("getEvaluation")
    @ApiOperation("获取测评信息")
    public PageResult<List<SsCustomerEvaluation>> getEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
        @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
        @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size, Sort.by(Sort.Direction.DESC,"createTime","id"));
        Page<SsCustomerEvaluation> recordList = customerEvaluationService.getEvaluation(userId,pageRequest);
        return PageResult.success(recordList.getContent(), Pagination.of(current, size, recordList.getTotalElements()));
    }

    @GetMapping("getIdEvaluation")
    @ApiOperation("获取认证信息")
    public PageResult<List<SsCustomerEvaluation>> getIdEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size, Sort.by(Sort.Direction.DESC,"createTime","id"));
        Page<SsCustomerEvaluation> recordList = customerEvaluationService.getIdEvaluation(userId,pageRequest);
        return PageResult.success(recordList.getContent(), Pagination.of(current, size, recordList.getTotalElements()));
    }

    @PostMapping("evaluationIdVerify")
    @ApiOperation("认证审核")
    public BaseResult<String> evaluationIdVerify(
            @RequestBody IdVerifyReq req){
        recordService.evaluationIdVerify(req);
        return BaseResult.success();
    }
}

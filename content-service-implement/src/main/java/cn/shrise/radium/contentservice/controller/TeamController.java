package cn.shrise.radium.contentservice.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.entity.SsArticle;
import cn.shrise.radium.contentservice.entity.SsShortVideo;
import cn.shrise.radium.contentservice.entity.SsStreamMessage;
import cn.shrise.radium.contentservice.properties.TeamProperties;
import cn.shrise.radium.contentservice.resp.PublicConfig;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import cn.shrise.radium.contentservice.service.article.ArticleService;
import cn.shrise.radium.contentservice.service.stream.StreamMessageService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.Objects;

import static cn.shrise.radium.contentservice.constant.ArticleStatusEnum.AS_Reviewing;
import static cn.shrise.radium.userservice.constant.ContentTeamModuleTypeConstant.*;

@RestController
@RequestMapping("teams")
@RequiredArgsConstructor
public class TeamController {

    private final TeamProperties teamProperties;
    private final ArticleService articleService;
    private final ShortVideoService shortVideoService;
    private final StreamMessageService streamMessageService;

    @GetMapping("public")
    @ApiOperation("获取内容主配置")
    public BaseResult<PublicConfig> getTeamPublicConfig() {
        PublicConfig publicConfig = teamProperties.getPublicConfig();
        return BaseResult.success(publicConfig);
    }

    @GetMapping("banner")
    @ApiOperation("获取观大盘配置")
    public BaseResult<String> getTeamBannerConfig() {
        String banner = teamProperties.getBanner();
        return BaseResult.success(banner);
    }

    @GetMapping("audit")
    @ApiOperation("内容主审核")
    public BaseResult<String> teamContentAudit(
            @RequestParam @ApiParam("ID") Long id,
            @RequestParam @ApiParam("模块类型") Integer moduleType,
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("是否审核通过") Boolean isAudit,
            @RequestParam(required = false) @ApiParam("审核理由") String auditRemark) {
        if (Objects.equals(moduleType, STREAM)) {
            SsStreamMessage streamMessage = streamMessageService.findOneById(id);
            if (ObjectUtil.isEmpty(streamMessage)) {
                throw new BusinessException("未找到对应观点");
            }
            if (!Objects.equals(streamMessage.getStatus(), AS_Reviewing.getValue())) {
                throw new BusinessException("不符合审核条件的状态");
            }
            streamMessageService.auditStreamMsg(isAudit, id, auditorId, auditRemark);
        }else if (Objects.equals(moduleType, VIDEO)) {
            SsShortVideo shortVideo = shortVideoService.findOneById(id);
            if (ObjectUtil.isEmpty(shortVideo)) {
                throw new BusinessException("未找到对应视频");
            }
            if (!Objects.equals(shortVideo.getStatus(), AS_Reviewing.getValue())) {
                throw new BusinessException("不符合审核条件的状态");
            }
            shortVideoService.auditVideo(isAudit, id, auditorId, auditRemark,  shortVideo.getPreReleaseTime());
        }else if (Objects.equals(moduleType, ARTICLE)) {
            SsArticle article = articleService.findOneById(id);
            if (ObjectUtil.isEmpty(article)) {
                throw new BusinessException("未找到对应文章");
            }
            if (!Objects.equals(article.getArticleStatus(), AS_Reviewing.getValue()) || ObjectUtil.isNotEmpty(article.getReleaseTime())) {
                throw new BusinessException("不符合审核条件的状态");
            }
            articleService.auditArticle(isAudit, id, auditorId, auditRemark, article.getPreReleaseTime());
        }
        return BaseResult.success();
    }

}

package cn.shrise.radium.contentservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.entity.QSsContractAnalystRecord;
import cn.shrise.radium.contentservice.entity.SsContractAnalystRecord;
import cn.shrise.radium.contentservice.repository.SsContractAnalystRecordRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class SsContractAnalystRecordDao {

    private final JPAQueryFactory queryFactory;
    private final QSsContractAnalystRecord qSsContractAnalystRecord = QSsContractAnalystRecord.ssContractAnalystRecord;
    private final SsContractAnalystRecordRepository ssContractAnalystRecordRepository;

    public SsContractAnalystRecord getContractAdvisor(Integer contractType) {
        return queryFactory.selectFrom(qSsContractAnalystRecord)
                .where(qSsContractAnalystRecord.contractType.eq(contractType))
                .orderBy(qSsContractAnalystRecord.gmtCreate.desc())
                .limit(1)
                .fetchFirst();
    }

    public void updateContractAdvisor(Integer operatorId, String content, Integer contractType) {
        SsContractAnalystRecord contractAdvisor = getContractAdvisor(contractType);
        if (contractAdvisor != null && ObjectUtil.equals(contractAdvisor.getContent(), content)) {
            throw new BusinessException("数据相同");
        }
        SsContractAnalystRecord record = SsContractAnalystRecord.builder()
                .operatorId(operatorId)
                .contractType(contractType)
                .content(content)
                .build();
        ssContractAnalystRecordRepository.save(record);
    }

    public List<SsContractAnalystRecord> getContractAdvisorOperateRecord(Integer contractType) {
        return queryFactory.selectFrom(qSsContractAnalystRecord)
                .where(qSsContractAnalystRecord.contractType.eq(contractType))
                .orderBy(qSsContractAnalystRecord.gmtCreate.desc())
                .fetch();
    }

    public String getContractAdvisorInfo(Integer contractType) {
        return queryFactory.select(qSsContractAnalystRecord.content)
                .from(qSsContractAnalystRecord)
                .where(qSsContractAnalystRecord.contractType.eq(contractType))
                .orderBy(qSsContractAnalystRecord.id.desc())
                .limit(1)
                .fetchFirst();
    }
}

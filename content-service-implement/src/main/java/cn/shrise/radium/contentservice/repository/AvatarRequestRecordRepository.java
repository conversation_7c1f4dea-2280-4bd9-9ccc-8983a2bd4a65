package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsAvatarRequestRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface AvatarRequestRecordRepository extends JpaRepository<SsAvatarRequestRecord, Long>, QuerydslPredicateExecutor<SsAvatarRequestRecord> {
}

package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.contentservice.constant.AnalystModifyMsgConstant;
import cn.shrise.radium.contentservice.entity.QSsAnalystAuditApply;
import cn.shrise.radium.contentservice.entity.SsAnalystAuditApply;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.entity.SsAnalystModifyRecord;
import cn.shrise.radium.contentservice.repository.AnalystAuditApplyRepository;
import cn.shrise.radium.contentservice.repository.AnalystRepository;
import cn.shrise.radium.contentservice.req.CreateAnalystAuditApplyReq;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

import static cn.shrise.radium.contentservice.constant.AnalystModifyConstant.CREATE;
import static cn.shrise.radium.contentservice.constant.AnalystModifyConstant.MODIFY;
import static cn.shrise.radium.contentservice.constant.AnalystModifyMsgConstant.AUDIT_CHANGE;
import static cn.shrise.radium.contentservice.constant.AuditStatusConstant.*;
import static cn.shrise.radium.contentservice.constant.AuditStatusEnum.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AnalystAuditApplyService {

    private final JPAQueryFactory queryFactory;

    private final AnalystAuditApplyRepository applyRepository;

    private final AnalystRepository analystRepository;

    private final AnalystModifyRecordService modifyRecordService;

    private final QSsAnalystAuditApply qSsAnalystAuditApply = QSsAnalystAuditApply.ssAnalystAuditApply;

    public PageResult<List<SsAnalystAuditApply>> getAnalystAuditApplyPage(Integer auditStatus, Integer analystId, Integer current, Integer size) {
        JPAQuery<SsAnalystAuditApply> query = queryFactory.selectFrom(qSsAnalystAuditApply);
        if (ObjectUtil.isNotEmpty(auditStatus)) {
            query.where(qSsAnalystAuditApply.auditStatus.eq(auditStatus));
        }
        if (ObjectUtil.isNotEmpty(analystId)) {
            query.where(qSsAnalystAuditApply.analystId.eq(analystId));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(qSsAnalystAuditApply.gmtCreate.desc(), qSsAnalystAuditApply.id.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional(rollbackFor = Exception.class)
    public void auditApply(Long applyId, Integer auditorId, Integer auditStatus) {
        //审核状态校验
        Instant now = Instant.now();
        String operateContent;
        if (ObjectUtil.equals(AUDIT_PASS, auditStatus)) {
            operateContent = String.format(AUDIT_CHANGE, AS_Passed.getMsg());
        } else if (ObjectUtil.equals(AUDIT_NOT_PASS, auditStatus)) {
            operateContent = String.format(AUDIT_CHANGE, AS_Rejected.getMsg());
        } else {
            throw new BusinessException("审核状态错误");
        }
        SsAnalystAuditApply ssAnalystAuditApply = applyRepository.findById(applyId).orElseThrow(RecordNotExistedException::new);
        ssAnalystAuditApply.setAuditorId(auditorId);
        ssAnalystAuditApply.setAuditTime(now);
        ssAnalystAuditApply.setAuditStatus(auditStatus);
        applyRepository.save(ssAnalystAuditApply);
        SsAnalystInfo ssAnalystInfo = analystRepository.findById(ssAnalystAuditApply.getAnalystId()).orElseThrow(RecordNotExistedException::new);
        if (ObjectUtil.equals(AUDIT_EXECUTING, ssAnalystInfo.getAuditStatus())) {
            //修改老师状态
            ssAnalystInfo.setAuditId(auditorId);
            ssAnalystInfo.setAuditStatus(auditStatus);
            ssAnalystInfo.setAuditTime(now);
            ssAnalystInfo.setIsEnabled(ObjectUtil.equals(AUDIT_PASS, auditStatus));
            analystRepository.save(ssAnalystInfo);
        } else {
            if (ObjectUtil.equals(AUDIT_PASS, auditStatus)) {
                //修改老师信息
                SsAnalystInfo analystInfo = JSON.parseObject(ssAnalystAuditApply.getAnalystInfo(), SsAnalystInfo.class);
                analystInfo.setAuditId(auditorId);
                analystInfo.setAuditStatus(auditStatus);
                analystInfo.setAuditTime(now);
                analystInfo.setIsEnabled(true);
                analystRepository.save(analystInfo);
            }
        }
        //审核记录
        SsAnalystModifyRecord modifyRecord = SsAnalystModifyRecord.builder()
                .analystId(ssAnalystAuditApply.getAnalystId())
                .operateId(auditorId)
                .operateContent(operateContent)
                .build();
        modifyRecordService.createOne(modifyRecord);
    }

    public void createAnalystAuditApply(CreateAnalystAuditApplyReq req) {
        String operateContent;
        if (ObjectUtil.equals(CREATE, req.getOperateType())) {
            operateContent = String.format(AnalystModifyMsgConstant.CREATE, AS_Executing.getMsg());
        } else if (ObjectUtil.equals(MODIFY, req.getOperateType())) {
            operateContent = String.format(AnalystModifyMsgConstant.MODIFY, AS_Executing.getMsg());
        } else {
            throw new BusinessException("操作记录类型错误");
        }
        SsAnalystAuditApply analystAuditApply = SsAnalystAuditApply.builder()
                .analystId(req.getAnalystId())
                .operateId(req.getOperateId())
                .operateType(req.getOperateType())
                .analystInfo(req.getAnalystInfo())
                .auditStatus(AUDIT_EXECUTING)
                .build();
        applyRepository.save(analystAuditApply);
        SsAnalystModifyRecord modifyRecord = SsAnalystModifyRecord.builder()
                .analystId(req.getAnalystId())
                .operateId(req.getOperateId())
                .operateContent(operateContent)
                .build();
        modifyRecordService.createOne(modifyRecord);
    }
}

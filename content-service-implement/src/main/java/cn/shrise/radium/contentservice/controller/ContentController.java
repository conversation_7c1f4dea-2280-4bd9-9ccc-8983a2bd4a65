package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.entity.SsStockPool;
import cn.shrise.radium.contentservice.service.ContentService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("content")
@RequiredArgsConstructor
public class ContentController {

    private final ContentService contentService;

    @GetMapping("analysts")
    @ApiOperation("获取投顾老师列表")
    public BaseResult<List<SsAnalystInfo>> getAnalystInfoList(
            @RequestParam(required = false) @ApiParam("Id列表") List<Integer> Ids,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("删除状态") Boolean isEnabled) {
       return contentService.getAnalystInfoList(Ids, companyType, isEnabled);
    }

    @GetMapping("channel/analysts")
    @ApiOperation("获取投顾老师列表")
    public BaseResult<List<SsAnalystInfo>> getChannelAnalystInfoList(
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("删除状态") Boolean isEnabled) {
        return contentService.getAnalystInfoList(channelId, companyType, isEnabled);
    }

    @GetMapping("stock/pool")
    @ApiOperation("获取选股宝股票池列表")
    public BaseResult<List<SsStockPool>> getStockPoolList(
            @RequestParam @ApiParam("查看日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate dateTime,
            @RequestParam(required = false) @ApiParam("股票池类型(10-涨停池/20-强势股票池)") Integer type) {
        return contentService.getStockPoolList(dateTime, type);
    }
}

package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.QSsShortVideo;
import cn.shrise.radium.contentservice.entity.QSsShortVideoTag;
import cn.shrise.radium.contentservice.entity.SsShortVideoTag;
import cn.shrise.radium.contentservice.repository.SsShortVideoTagRepository;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class ShortVideoTagService {

    private final JPAQueryFactory queryFactory;
    private final QSsShortVideo qSsShortVideo = QSsShortVideo.ssShortVideo;
    private final QSsShortVideoTag qSsShortVideoTag = QSsShortVideoTag.ssShortVideoTag;
    private final SsShortVideoTagRepository ssShortVideoTagRepository;
    private final JdbcTemplate jdbcTemplate;

    public SsShortVideoTag getShortVideoTag(Long videoId) {
        JPAQuery<SsShortVideoTag> query = queryFactory.select(qSsShortVideoTag)
                .from(qSsShortVideoTag)
                .where(qSsShortVideoTag.videoId.eq(videoId));
        return query.fetchFirst();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateShortVideoTag(SsShortVideoTag shortVideoTag) {

        JPAUpdateClause updateClause = queryFactory.update(qSsShortVideoTag);
        if (ObjectUtil.isNotEmpty(shortVideoTag.getId())) {
            updateClause.where(qSsShortVideoTag.id.eq(shortVideoTag.getId()));
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getVideoId())) {
            updateClause.where(qSsShortVideoTag.videoId.eq(shortVideoTag.getVideoId()));
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getJobId())) {
            updateClause.where(qSsShortVideoTag.jobId.eq(shortVideoTag.getJobId()));
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getMessage())) {
            updateClause.set(qSsShortVideoTag.message, shortVideoTag.getMessage());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getTags())) {
            updateClause.set(qSsShortVideoTag.tags, shortVideoTag.getTags());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getTagInfo())) {
            updateClause.set(qSsShortVideoTag.tagInfo, shortVideoTag.getTagInfo());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getStatus())) {
            updateClause.set(qSsShortVideoTag.status, shortVideoTag.getStatus());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getCode())) {
            updateClause.set(qSsShortVideoTag.code, shortVideoTag.getCode());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getMessage())) {
            updateClause.set(qSsShortVideoTag.message, shortVideoTag.getMessage());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getEnabled())) {
            updateClause.set(qSsShortVideoTag.enabled, shortVideoTag.getEnabled());
        }
        updateClause.execute();
    }

    @Transactional
    public void createOrUpdateShortVideoTag(SsShortVideoTag shortVideoTag) {
        String sql = SqlUtil.onDuplicateKeyUpdateSql(shortVideoTag, true);
        jdbcTemplate.execute(sql);
    }

    public SsShortVideoTag createShortVideoTag(SsShortVideoTag shortVideoTag) {
        return ssShortVideoTagRepository.save(shortVideoTag);
    }
}

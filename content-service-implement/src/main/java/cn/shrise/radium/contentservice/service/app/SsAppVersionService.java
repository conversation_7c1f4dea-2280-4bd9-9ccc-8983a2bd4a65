package cn.shrise.radium.contentservice.service.app;

import akka.persistence.Update;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.entity.QSsAppVersion;
import cn.shrise.radium.contentservice.entity.SsAppVersion;
import cn.shrise.radium.contentservice.repository.SsAppVersionRepository;
import cn.shrise.radium.contentservice.req.AppVersionReq;
import cn.shrise.radium.contentservice.req.UpdateVersionReq;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SsAppVersionService {

    private final JPAQueryFactory queryFactory;
    private final QSsAppVersion appVersion = QSsAppVersion.ssAppVersion;
    private final SsAppVersionRepository ssAppVersionRepository;

    public PageResult<List<SsAppVersion>> getAppVersionPage(Integer platform, Integer current, Integer size) {
        JPAQuery<SsAppVersion> query = queryFactory.selectFrom(appVersion).where(appVersion.platform.eq(platform));
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(appVersion.gmtCreate.desc(), appVersion.id.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional
    public void addAppVersion(AppVersionReq req) {
        SsAppVersion exist = queryFactory.selectFrom(appVersion).where(appVersion.platform.eq(req.getPlatform())).where(appVersion.version.eq(req.getVersion())).fetchFirst();
        if (ObjectUtil.isNotEmpty(exist)) {
            throw new BusinessException("版本号已存在");
        }
        SsAppVersion ssAppVersion = new SsAppVersion();
        BeanUtils.copyProperties(req, ssAppVersion);
        ssAppVersionRepository.save(ssAppVersion);
    }

    @Transactional
    public void updateAppVersion(UpdateVersionReq req) {
        queryFactory.update(appVersion)
                .set(appVersion.noticeVersion, req.getNoticeVersion())
                .set(appVersion.updateType, req.getUpdateType())
                .set(appVersion.updateContent, req.getUpdateContent())
                .where(appVersion.id.eq(req.getId()))
                .execute();
    }

    public SsAppVersion getAppVersionLast(Integer platform) {
        return queryFactory.selectFrom(appVersion)
                .where(appVersion.platform.eq(platform))
                .orderBy(appVersion.gmtCreate.desc(), appVersion.id.desc())
                .fetchFirst();
    }
}

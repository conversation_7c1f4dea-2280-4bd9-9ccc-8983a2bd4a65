package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.entity.SsLiveRoomVideoInfo;
import cn.shrise.radium.contentservice.service.LiveRoomVideoService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("live-room")
@RequiredArgsConstructor
public class LiveRoomVideoController {

    private final LiveRoomVideoService liveRoomVideoService;

    @GetMapping("video-list")
    @ApiOperation("通过number获取视频列表")
    public PageResult<List<SsLiveRoomVideoInfo>> getLiveRoomVideoList(
            @RequestParam @ApiParam("编号") String number,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size) {
        return liveRoomVideoService.getLiveRoomVideoList(number, current, size);
    }

    @GetMapping("video-info")
    @ApiOperation("通过id获取视频详情")
    public BaseResult<SsLiveRoomVideoInfo> getLiveRoomVideoInfo(@RequestParam @ApiParam Long id) {
        SsLiveRoomVideoInfo liveRoomVideoInfo = liveRoomVideoService.getLiveRoomVideoInfo(id);
        return BaseResult.success(liveRoomVideoInfo);
    }
}

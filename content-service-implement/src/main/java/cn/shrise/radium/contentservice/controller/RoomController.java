package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.dto.SsRoomLotteryDetailDto;
import cn.shrise.radium.contentservice.entity.SsLiveRoom;
import cn.shrise.radium.contentservice.entity.SsRoomLottery;
import cn.shrise.radium.contentservice.entity.SsRoomPrize;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.req.CreateParticipateLotteryReq;
import cn.shrise.radium.contentservice.req.CreateRoomLotteryReq;
import cn.shrise.radium.contentservice.req.CreateRoomPrizeReq;
import cn.shrise.radium.contentservice.req.UpdateRoomLotteryReq;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.contentservice.service.*;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@RestController
@RequestMapping("room")
@RequiredArgsConstructor
public class RoomController {

    private final RoomPrizeService roomPrizeService;
    private final RoomLotteryService roomLotteryService;
    private final LiveRoomService liveRoomService;
    private final RoomLotteryDetailService roomLotteryDetailService;
    private final RoomLotteryOperateRecordService roomLotteryOperateRecordService;

    @ApiOperation("获取直播间奖品列表")
    @GetMapping("prize")
    public PageResult<List<SsRoomPrize>> getRoomPrizeList(
            @RequestParam(required = false) @ApiParam("奖品ID") Long id,
            @RequestParam(required = false) @ApiParam("启用禁用") Boolean isEnabled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size);
        final Page<SsRoomPrize> roomPrizeRespList = roomPrizeService.getRoomPrizeList(id, isEnabled, pageRequest);
        return PageResult.success(roomPrizeRespList.getContent(), Pagination.of(current, size, roomPrizeRespList.getTotalElements()));
    }

    @ApiOperation("新增奖品")
    @PostMapping("prize")
    public BaseResult<SsRoomPrize> createRoomPrize(
            @RequestBody @Valid CreateRoomPrizeReq req
    ) {
        final SsRoomPrize roomPrize = roomPrizeService.createRoomPrize(req);
        return BaseResult.success(roomPrize);
    }

    @ApiOperation("修改奖品状态")
    @PutMapping("prize")
    public BaseResult<SsRoomPrize> updateRoomPrize(
            @RequestParam(required = false) @ApiParam("奖品ID") Long id,
            @RequestParam(required = false) @ApiParam("启用禁用") Boolean isEnabled) {
        SsRoomPrize ssRoomPrize = roomPrizeService.updateRoomPrize(id, isEnabled);
        return BaseResult.success(ssRoomPrize);
    }

    @ApiOperation("获取我的奖品列表")
    @GetMapping("prize/my")
    public PageResult<List<MyLotteryPrizeResp>> findMyPrizeList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return roomLotteryDetailService.findMyPrizeList(userId, current, size);
    }

    @ApiOperation("获取单个中奖明细")
    @GetMapping("prize/detail")
    public BaseResult<MyLotteryPrizeResp> findOnePrize(
            @RequestParam @ApiParam("抽奖明细id") Long detailId) {
        return BaseResult.success(roomLotteryDetailService.findOnePrize(detailId));
    }

    @ApiOperation("获取中奖列表")
    @GetMapping("lottery/winner")
    public PageResult<List<LotteryWinnerResp>> getLotteryWinnerList(
            @RequestParam @ApiParam("抽奖活动id") Long lotteryId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return roomLotteryDetailService.getLotteryWinnerList(lotteryId, current, size);
    }

    @ApiOperation("参与抽奖活动")
    @PostMapping("lottery/participate")
    public BaseResult<Void> participateLottery(
            @RequestBody @Validated CreateParticipateLotteryReq req) {
        roomLotteryDetailService.createOne(req);
        return BaseResult.successful();
    }

    @ApiOperation("获取抽奖活动列表")
    @GetMapping("lottery")
    public PageResult<List<RoomLotteryResp>> getRoomLottery(
            @RequestParam(required = false) @ApiParam("直播室ID") List<Long> roomList,
            @RequestParam(required = false) @ApiParam("场次Id") Long sceneId,
            @RequestParam(required = false) @ApiParam("活动状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("销售ID") Integer salesId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<RoomLotteryResp> roomLotteryList = roomLotteryService.getRoomLottery(roomList, sceneId, status, startTime, endTime, salesId, pageRequest);
        return PageResult.success(roomLotteryList.getContent(), Pagination.of(current, size, roomLotteryList.getTotalElements()));
    }

    @ApiOperation("获取直播室名称")
    @GetMapping("name")
    public BaseResult<List<LiveRoomNameResp>> getLiveRoomName(
            @RequestParam(required = false) @ApiParam("直播室ID") Collection<Long> roomIds,
            @RequestParam(required = false) @ApiParam("是否直播") Boolean isLive,
            @RequestParam(required = false) @ApiParam("启用禁用") Boolean isDeleted,
            @RequestParam(required = false) @ApiParam("直播室类型") Integer roomType,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("销售ID") Integer salesId) {
        List<LiveRoomNameResp> liveRoomName = liveRoomService.getLiveRoomName(roomIds, isLive, isDeleted, companyType, roomType, salesId);
        return BaseResult.success(liveRoomName);
    }

    @PostMapping("lottery")
    @ApiOperation("创建抽奖活动")
    public BaseResult<String> createRoomLottery(
            @RequestBody @Valid CreateRoomLotteryReq req
    ) {
        ContentErrorCode roomLottery = roomLotteryService.createRoomLottery(req);
        return BaseResult.create(roomLottery.getCode(), roomLottery.getMsg());
    }

    @GetMapping("lottery/valid")
    @ApiOperation("获取有效抽奖活动")
    public BaseResult<List<RoomLotteryResp>> getValidLotteryList(
            @RequestParam @ApiParam("场次id") Long sceneId) {
        List<RoomLotteryResp> validLotteryList = roomLotteryService.getValidLotteryList(sceneId);
        return BaseResult.success(validLotteryList);
    }

    @GetMapping("lottery/detail")
    @ApiOperation("获取抽奖活动详情")
    public BaseResult<LotteryDetailResp> getRoomLotteryDetail(
            @RequestParam @ApiParam("抽奖活动ID") Long lotteryId,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId) {
        LotteryDetailResp detail = roomLotteryDetailService.getLotteryDetail(lotteryId, userId);
        return BaseResult.success(detail);
    }

    @PutMapping("lottery/status")
    @ApiOperation("修改活动状态")
    public BaseResult<String> updateRoomLotteryStatus(
            @RequestParam(required = true) @ApiParam("活动ID") Long lotteryId,
            @RequestParam(required = true) @ApiParam("活动状态") RoomLotteryType status,
            @RequestParam(required = true) @ApiParam("用户ID") Integer userId) {
        ContentErrorCode contentErrorCode = roomLotteryService.updateRoomLotteryStatus(lotteryId, status, userId);
        return BaseResult.create(contentErrorCode.getCode(), contentErrorCode.getMsg());
    }

    @PutMapping("lottery")
    @ApiOperation("编辑抽奖活动")
    public BaseResult<String> updateRoomLottery(
            @RequestBody @Valid UpdateRoomLotteryReq req
    ) {
        ContentErrorCode contentErrorCode = roomLotteryService.updateRoomLottery(req);
        return BaseResult.create(contentErrorCode.getCode(), contentErrorCode.getMsg());
    }

    @GetMapping("lottery/{lotteryId}")
    @ApiOperation("获取抽奖活动")
    public BaseResult<SsRoomLottery> findOneRoomLottery(
            @PathVariable Long lotteryId) {
        SsRoomLottery lottery = roomLotteryService.findOne(lotteryId);
        return BaseResult.success(lottery);
    }

    @GetMapping("lottery/detailList")
    @ApiOperation("获取抽奖详情列表")
    public PageResult<List<SsRoomLotteryDetailDto>> getRoomLotteryDetailList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("直播室ID") Long roomId,
            @RequestParam(required = false) @ApiParam("场次ID") Long sceneId,
            @RequestParam(required = false) @ApiParam("活动ID") Long lotteryId,
            @RequestParam(required = false) @ApiParam("奖品ID") Long prizeId,
            @RequestParam(required = false) @ApiParam("可见的后台用户ID") Integer relationServerId,
            @RequestParam(required = false) @ApiParam("销售ID") Integer salesId,
            @RequestParam(required = false) @ApiParam("客户ID") List<Integer> customerList,
            @RequestParam(required = false) @ApiParam("是否中奖") Boolean isWin,
            @RequestParam(required = false) @ApiParam("是否已处理") Boolean isHandled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size
    ) {
        PageRequest pageRequest;
        if (size == null) {
            pageRequest = PageRequest.of(current - 1, Integer.MAX_VALUE);
        } else {
            pageRequest = PageRequest.of(current - 1, size);
        }
        Page<SsRoomLotteryDetailDto> detailList = roomLotteryDetailService.getRoomLotteryDetailList(
                pageRequest, companyType, relationServerId, salesId, roomId, sceneId, lotteryId, prizeId, customerList, isWin, isHandled);
        return PageResult.success(detailList.getContent(), Pagination.of(current, size, detailList.getTotalElements()));
    }

    @PostMapping("lottery/detail/{detailId}/markHandled")
    @ApiOperation("标记抽奖详情为已处理")
    public BaseResult<String> markRoomLotteryDetailHandled(
            @PathVariable @ApiParam("抽奖详情ID") Long detailId,
            @RequestParam @ApiParam("收件人电话（掩码)") String mobile,
            @RequestParam @ApiParam("收件人电话id") Long mobileId,
            @RequestParam @ApiParam("收件人地区") String region,
            @RequestParam @ApiParam("收件人地址") String address,
            @RequestParam @ApiParam("收件人姓名") String name) {
        roomLotteryDetailService.markHandled(detailId, mobile, mobileId, region, address, name);
        return BaseResult.success();
    }

    @GetMapping("lottery/operate/record")
    @ApiOperation("获取活动操作日志")
    public BaseResult<List<RoomLotteryOperateResp>> getRoomLotteryOperateRecord(
            @RequestParam(required = true) @ApiParam("活动ID") Long lotteryId) {
        List<RoomLotteryOperateResp> roomLotteryOperateRecord = roomLotteryOperateRecordService.getRoomLotteryOperateRecord(lotteryId);
        return BaseResult.success(roomLotteryOperateRecord);
    }

    @GetMapping("{roomId}")
    @ApiOperation("获取直播室详情")
    public BaseResult<SsLiveRoom> getLiveRoomInfo(@PathVariable Long roomId) {
        SsLiveRoom room = liveRoomService.findOne(roomId).orElseThrow(() -> new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED));
        return BaseResult.success(room);
    }

    @GetMapping("all")
    @ApiOperation("获取所有直播室")
    public BaseResult<List<SsLiveRoom>> getAllLiveRoom(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否过滤删除") Boolean filterDeleted) {
        List<SsLiveRoom> rooms = liveRoomService.findAll(companyType, filterDeleted);
        return BaseResult.success(rooms);
    }

    @GetMapping("service/introduction")
    @ApiOperation("获取直播室服务简介")
    public BaseResult<ServiceIntroduction> getLiveRoomServiceIntroduction(Integer serviceId) {
        ServiceIntroduction introduction = liveRoomService.getLiveRoomServiceIntroduction(serviceId)
                .orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(introduction);
    }

    @PostMapping("service/introduction/number")
    @ApiOperation("获取直播室服务简介列表")
    public BaseResult<List<ServiceIntroduction>> getLiveRoomServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req,
            @RequestParam(required = false, defaultValue = "false") Boolean sorted) {
        List<ServiceIntroduction> introductions = liveRoomService.getLiveRoomServiceIntroductionList(companyType, req.getValues(), sorted);
        return BaseResult.success(introductions);
    }

    @PostMapping("service/introduction")
    @ApiOperation("获取直播室服务简介列表")
    public BaseResult<List<ServiceIntroduction>> getLiveRoomServiceIntroductionList(@RequestBody @Valid BatchReq<Integer> req) {
        List<ServiceIntroduction> introductions = liveRoomService.getLiveRoomServiceIntroductionList(req.getValues());
        return BaseResult.success(introductions);
    }
}

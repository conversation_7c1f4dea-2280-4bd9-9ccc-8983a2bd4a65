package cn.shrise.radium.contentservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.entity.QSsCustomerComplaint;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.Instant;

@Repository
@RequiredArgsConstructor
public class SsCustomerComplaintDao {

    private final JPAQueryFactory queryFactory;
    private final QSsCustomerComplaint qSsCustomerComplaint = QSsCustomerComplaint.ssCustomerComplaint;

    public Integer getUserComplaintCountMonthly(Integer userId, Integer wxId, Instant startTime) {
        NumberTemplate<Integer> countTemp = Expressions.numberTemplate(Integer.class, "COUNT(*)");
        BooleanBuilder builder = new BooleanBuilder();
        if (ObjectUtil.isNotEmpty(userId)) {
            builder.or(qSsCustomerComplaint.userId.eq(userId));
        }
        if (ObjectUtil.isNotEmpty(wxId)) {
            builder.or(qSsCustomerComplaint.wxId.eq(wxId));
        }
        return queryFactory.select(countTemp).from(qSsCustomerComplaint)
                .where(builder)
                .where(qSsCustomerComplaint.gmtCreate.after(startTime))
                .fetchOne();
    }

}

package cn.shrise.radium.contentservice.consumer;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.resp.AiRecUserResp;
import cn.shrise.radium.contentservice.service.airec.AiRecService;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = ContentServiceConst.CONTENT_TOPIC, consumerGroup = ContentServiceConst.MqGroupType.GID_AI_REC_USER,
        selectorType = ExpressionType.TAG, selectorExpression = ContentServiceConst.MqTagType.AI_REC_USER)
public class AiRecUserConsumer implements MessageListener {

    private final AiRecService aiRecService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        AiRecUserResp resp = JSON.parseObject(new String(message.getBody()), AiRecUserResp.class);
        if (ObjectUtils.isNotEmpty(resp)) {
            log.info("GID_aiRecUser, resp: {}", resp);
            aiRecService.processUser(resp);
        }
        return Action.CommitMessage;
    }
}

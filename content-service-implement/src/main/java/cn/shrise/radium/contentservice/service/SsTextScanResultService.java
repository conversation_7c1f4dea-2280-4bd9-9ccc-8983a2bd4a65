package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.contentservice.entity.QSsTextScanResult;
import cn.shrise.radium.contentservice.entity.SsTextScanResult;
import cn.shrise.radium.contentservice.repository.SsTextScanResultRepository;
import cn.shrise.radium.contentservice.req.CreateTextScanResultReq;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SsTextScanResultService {

    private final SsTextScanResultRepository ssTextScanResultRepository;
    private final JPAQueryFactory queryFactory;
    private final QSsTextScanResult qSsTextScanResult = QSsTextScanResult.ssTextScanResult;

    @Transactional
    public SsTextScanResult createTextScanResult(CreateTextScanResultReq createTextScanResultReq) {
        SsTextScanResult entity = SsTextScanResult.builder()
                .companyType(createTextScanResultReq.getCompanyType())
                .dataId(createTextScanResultReq.getDataId())
                .content(createTextScanResultReq.getContent())
                .taskId(createTextScanResultReq.getTaskId())
                .filteredContent(createTextScanResultReq.getFilteredContent())
                .results(createTextScanResultReq.getResults())
                .error(createTextScanResultReq.getError())
                .labels(createTextScanResultReq.getLabels())
                .passed(createTextScanResultReq.getPassed())
                .build();
        return ssTextScanResultRepository.save(entity);
    }

    public List<SsTextScanResult> getTextScanResultList(Collection<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        final Set<Long> idSet = ids.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return queryFactory.selectFrom(qSsTextScanResult)
                .where(qSsTextScanResult.id.in(idSet))
                .fetch();
    }

}

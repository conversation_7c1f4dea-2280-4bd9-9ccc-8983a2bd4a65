package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsTextScanResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SsTextScanResultRepository extends JpaRepository<SsTextScanResult, Long>, QuerydslPredicateExecutor<SsTextScanResult> {

    Optional<SsTextScanResult> findByCompanyTypeAndDataId(Integer companyType, String dataId);
}

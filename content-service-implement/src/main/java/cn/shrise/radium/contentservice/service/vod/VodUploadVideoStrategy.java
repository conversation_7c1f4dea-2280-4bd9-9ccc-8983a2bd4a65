package cn.shrise.radium.contentservice.service.vod;

import cn.shrise.radium.contentservice.constant.VideoTranscodeConstant;
import cn.shrise.radium.contentservice.entity.SsVideoTranscode;
import cn.shrise.radium.contentservice.resp.VodCallbackResp;
import cn.shrise.radium.contentservice.service.VideoTranscodeService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static cn.shrise.radium.contentservice.constant.VodResultEventConstant.StreamTranscodeComplete;
import static cn.shrise.radium.contentservice.constant.VodResultEventConstant.TranscodeComplete;

/**
 * 上传回调策略
 */
@Service
@RequiredArgsConstructor
public class VodUploadVideoStrategy implements VodVideoStrategy {

    private final VideoTranscodeService videoTranscodeService;

    @Override
    public void handle(Integer companyType, VodCallbackResp callbackResp) {
        String status = callbackResp.getStatus();
        String eventType = callbackResp.getEventType();
        String videoId = callbackResp.getVideoId();

        if (Objects.equals(status, "success")) {
            if (Objects.equals(eventType, TranscodeComplete)) {
                //视频转码完成
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(VideoTranscodeConstant.TranscodeComplete)
                        .event(JSON.toJSONString(callbackResp))
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            } else if (Objects.equals(eventType, StreamTranscodeComplete)) {
                //单个完成
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(VideoTranscodeConstant.StreamTranscodeComplete)
                        .event(JSON.toJSONString(callbackResp))
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            }
        }
    }
}

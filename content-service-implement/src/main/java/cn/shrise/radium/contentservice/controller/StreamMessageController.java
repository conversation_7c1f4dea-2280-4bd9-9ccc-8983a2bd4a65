package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsStreamMessage;
import cn.shrise.radium.contentservice.entity.SsStreamMessageComment;
import cn.shrise.radium.contentservice.entity.SsStreamOperateRecord;
import cn.shrise.radium.contentservice.req.CreateTeamStreamMessageReq;
import cn.shrise.radium.contentservice.req.StreamMessageReq;
import cn.shrise.radium.contentservice.req.UpdateStreamMessageLikeReq;
import cn.shrise.radium.contentservice.resp.StreamMessageLikeStatistics;
import cn.shrise.radium.contentservice.resp.StreamMessageResp;
import cn.shrise.radium.contentservice.resp.StreamMessageStatistics;
import cn.shrise.radium.contentservice.service.stream.StreamMessageService;
import cn.shrise.radium.contentservice.service.stream.StreamOperateRecordService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("stream/message")
@RequiredArgsConstructor
public class StreamMessageController {

    private final StreamMessageService streamMessageService;
    private final StreamOperateRecordService streamOperateRecordService;

    @GetMapping("list")
    @ApiOperation("解盘列表")
    public PageResult<List<StreamMessageResp>> getStreamMessagePage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户") Integer userId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Pageable pageable = PageRequest.of(current - 1, size);
        Page<StreamMessageResp> streamMessagePage = streamMessageService.getStreamMessagePage(userId, companyType, startTime, endTime, channelId, pageable);
        return PageResult.success(streamMessagePage);
    }

    @GetMapping("audit/list")
    @ApiOperation("解盘审核列表")
    public PageResult<List<StreamMessageResp>> getStreamMessageAuditList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("是否审核") Boolean isAudit,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size) {
        return streamMessageService.getStreamMessageAuditList(companyType, isAudit, current, size);
    }

    @PostMapping("audit")
    @ApiOperation("解盘消息审核")
    public BaseResult<Void> auditStreamMessage(
            @RequestParam @ApiParam("审核人id") Integer userId,
            @RequestParam @ApiParam("消息id") Long messageId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("审核原因") String refuseRemark) {
        streamMessageService.auditStreamMessage(userId, messageId, auditStatus, refuseRemark);
        return BaseResult.successful();
    }

    @GetMapping("analyst")
    @ApiOperation("前端获取对应老师的解盘列表")
    public BaseResult<List<SsStreamMessage>> getStreamMessageList(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam List<Integer> analystIds,
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(required = false) @ApiParam("开始时间") Instant openTime,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size) {
        PageRequest pageRequest = PageRequest.of(current - 1, size);
        List<SsStreamMessage> messageList = streamMessageService.getStreamMessageList(companyType, analystIds, channelId, openTime, pageRequest);
        return BaseResult.success(messageList);
    }

    @GetMapping("team")
    @ApiOperation("按内容主获取解盘列表")
    public PageResult<List<SsStreamMessage>> getTeamStreamMessageList(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size) {
        return streamMessageService.getTeamStreamMessageList(companyType, teamIds, status, startTime, endTime, current, size);
    }

    @GetMapping("{id}")
    @ApiOperation("根据解盘消息id获取解盘消息")
    public BaseResult<SsStreamMessage> getStreamMessage(@PathVariable Long id) {
        SsStreamMessage streamMessage = streamMessageService.getStreamMessage(id).orElse(null);
        return BaseResult.success(streamMessage);
    }

    @PostMapping
    @ApiOperation("创建解盘消息")
    public BaseResult<SsStreamMessage> createMessage(@RequestBody StreamMessageReq req) {
        SsStreamMessage ssStreamMessage = streamMessageService.sendMessage(req);
        return BaseResult.success(ssStreamMessage);
    }

    @DeleteMapping("{id}")
    @ApiOperation("删除消息")
    public BaseResult<Void> deleteMessage(@PathVariable Long id) {
        streamMessageService.deleteMessage(id);
        return BaseResult.successful();
    }

    @GetMapping("comment/list")
    @ApiOperation("评论列表")
    public PageResult<List<SsStreamMessageComment>> getCommentPage(
            @RequestParam @ApiParam("消息id") Long messageId,
            @RequestParam(required = false) @ApiParam("是否精选") Boolean isChoice,
            @RequestParam(required = false) @ApiParam("是否已审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Pageable pageable = PageRequest.of(current - 1, size);
        Page<SsStreamMessageComment> commentPage = streamMessageService.getCommentPage(messageId, isChoice, audit, auditStatus, startTime, endTime, pageable);
        return PageResult.success(commentPage);
    }

    @PutMapping("{messageId}/comment")
    @ApiOperation("发送评论")
    public BaseResult<Boolean> sendComment(
            @PathVariable Long messageId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer customerId,
            @RequestParam @ApiParam("评论内容") String content) {
        Boolean sendComment = streamMessageService.sendComment(messageId, customerId, content, companyType);
        return BaseResult.success(sendComment);
    }

    @DeleteMapping("comment/{id}")
    @ApiOperation("删除评论")
    public BaseResult<Void> deleteMessageComment(@PathVariable Long id) {
        streamMessageService.deleteMessageComment(id);
        return BaseResult.successful();
    }

    @PutMapping("comment/{id}")
    @ApiOperation("修改精选状态")
    public BaseResult<Void> updateChoice(
            @PathVariable Long id,
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("是否精选") Boolean isChoice) {
        streamMessageService.updateChoice(id, auditorId, isChoice);
        return BaseResult.successful();
    }

    @PostMapping("comment/statistics")
    @ApiOperation("统计解盘评论数量")
    public BaseResult<List<StreamMessageStatistics>> getStreamMessageCommentStatistics(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false) Integer auditStatus,
            @RequestParam(required = false) Boolean isChose) {
        List<StreamMessageStatistics> statistics = streamMessageService.getStreamMessageCommentStatistics(req.getValues(), auditStatus, isChose);
        return BaseResult.success(statistics);
    }

    @PostMapping("comment/batch")
    @ApiOperation("批量获取解盘精选评论")
    public BaseResult<List<SsStreamMessageComment>> getStreamMessageCommentList(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false, defaultValue = "1") Integer auditStatus,
            @RequestParam(required = false, defaultValue = "true") Boolean isChose,
            @RequestParam(required = false, defaultValue = "3") Integer perSize) {
        List<SsStreamMessageComment> commentList = streamMessageService.getStreamMessageCommentList(req.getValues(), auditStatus, isChose, perSize);
        return BaseResult.success(commentList);
    }

    @PutMapping("answer/{id}")
    @ApiOperation("回复评论")
    public BaseResult<Void> updateAnswer(
            @PathVariable Long id,
            @RequestParam @ApiParam("回复内容") String content,
            @RequestParam @ApiParam("回复人") Integer answerId) {
        streamMessageService.updateAnswer(id, content, answerId);
        return BaseResult.successful();
    }

    @PostMapping("like/statistics")
    @ApiOperation("统计解盘点赞数量")
    public BaseResult<List<StreamMessageStatistics>> getStreamMessageLikeStatistics(@RequestBody @Valid BatchReq<Long> req) {
        List<StreamMessageStatistics> statistics = streamMessageService.getStreamMessageLikeStatistics(req.getValues());
        return BaseResult.success(statistics);
    }

    @PostMapping("users/like/statistics")
    @ApiOperation("统计用户解盘是否点赞")
    public BaseResult<List<StreamMessageLikeStatistics>> getUserStreamMessageLikeStatistics(@RequestParam(required = false) Integer userId, @RequestBody @Valid BatchReq<Long> req) {
        List<StreamMessageLikeStatistics> statistics = streamMessageService.getUserStreamMessageLikeStatistics(userId, req.getValues());
        return BaseResult.success(statistics);
    }

    @PutMapping("like")
    @ApiOperation("修改解盘点赞状态")
    public BaseResult<String> updateStreamMessageLike(@RequestBody @Valid UpdateStreamMessageLikeReq req) {
        streamMessageService.updateStreamMessageLike(req);
        return BaseResult.success();
    }

    @GetMapping("comment/page")
    @ApiOperation("前端评论列表")
    public PageResult<List<SsStreamMessageComment>> getCommentPage(
            @RequestParam @ApiParam("消息id") Long messageId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Pageable pageable = PageRequest.of(current - 1, size);
        Page<SsStreamMessageComment> commentPage = streamMessageService.getCommentPage(messageId, pageable);
        return PageResult.success(commentPage);
    }

    @GetMapping("team/list")
    @ApiOperation("内容主解盘列表")
    public PageResult<List<SsStreamMessage>> getTeamStreamMessagePage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("状态") List<Integer> statusList,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Pageable pageable = PageRequest.of(current - 1, size);
        Page<SsStreamMessage> streamMessagePage = streamMessageService.getTeamStreamMessagePage(teamIds, companyType, auditStatus, startTime, endTime, statusList, pageable);
        return PageResult.success(streamMessagePage);
    }

    @GetMapping("team/latest")
    @ApiOperation("获取内容主最新解盘")
    public BaseResult<List<SsStreamMessage>> getLatestTeamStreamMessageList(
            @RequestParam Integer companyType,
            @RequestParam Long teamId,
            @RequestParam(required = false) @ApiParam("状态") Integer status,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("数量") Long size
    ) {
        List<SsStreamMessage> messageList = streamMessageService.getLatestTeamStreamMessageList(companyType, teamId, status, size);
        return BaseResult.success(messageList);
    }

    @PostMapping("team/create")
    @ApiOperation("创建内容主解盘消息")
    public BaseResult<SsStreamMessage> createTeamStreamMessage(@RequestBody CreateTeamStreamMessageReq req) {
        SsStreamMessage ssStreamMessage = streamMessageService.createTeamStreamMessage(req);
        return BaseResult.success(ssStreamMessage);
    }

    @PutMapping("audit/comment/{id}")
    @ApiOperation("审核评论")
    public BaseResult<Void> auditComment(
            @PathVariable Long id,
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus) {
        streamMessageService.auditComment(id, auditorId, auditStatus);
        return BaseResult.successful();
    }

    @DeleteMapping("team/{id}")
    @ApiOperation("删除内容主解盘")
    public BaseResult<Void> deleteTeamMessage(
            @PathVariable Long id,
            @RequestParam @ApiParam("用户id") Integer userId
    ) {
        streamMessageService.deleteTeamMessage(id, userId);
        return BaseResult.successful();
    }

    @GetMapping("operate/record")
    @ApiOperation("获取观点操作记录")
    public BaseResult<List<SsStreamOperateRecord>> getStreamOperateRecord(
            @RequestParam @ApiParam("解盘id") Long streamId
    ) {
        List<SsStreamOperateRecord> record = streamOperateRecordService.getOperateRecord(streamId);
        return BaseResult.success(record);
    }
}

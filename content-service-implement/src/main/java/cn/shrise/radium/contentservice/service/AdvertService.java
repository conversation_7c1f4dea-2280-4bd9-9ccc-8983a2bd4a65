package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordExistedException;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.repository.AdvertCategoryRepository;
import cn.shrise.radium.contentservice.repository.AdvertRepository;
import cn.shrise.radium.contentservice.req.*;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static cn.shrise.radium.contentservice.constant.AdvertTypeConstant.PC;
import static com.querydsl.core.types.dsl.Expressions.nullExpression;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AdvertService {

    private final JPAQueryFactory queryFactory;

    private final QSsAdvert qSsAdvert = QSsAdvert.ssAdvert;
    private final QSsAdvertLabel qSsAdvertLabel = QSsAdvertLabel.ssAdvertLabel;

    private final AdvertRepository advertRepository;

    private final QSsAdvertCategory qSsAdvertCategory = QSsAdvertCategory.ssAdvertCategory;

    private final AdvertCategoryRepository advertCategoryRepository;

    public PageResult<List<SsAdvert>> getAdvertList(Integer companyType, Boolean enabled, String label, Long categoryId, Integer current, Integer size) {
        JPAQuery<SsAdvert> query = queryFactory.selectFrom(qSsAdvert)
                .where(qSsAdvert.type.eq(PC))
                .where(qSsAdvert.companyType.eq(companyType))
                .where(qSsAdvert.deleted.eq(false));
        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(qSsAdvert.enabled.eq(enabled));
        }

        if (ObjectUtil.isNotEmpty(label)) {
            query.where(qSsAdvert.label.eq(label));
        }

        if (ObjectUtil.isNotEmpty(categoryId)) {
            query.where(qSsAdvert.categoryId.eq(categoryId));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        if (ObjectUtil.isNotEmpty(categoryId)) {
            query.orderBy(qSsAdvert.sort.asc(), qSsAdvert.id.asc());
        } else {
            query.orderBy(qSsAdvert.createTime.desc(), qSsAdvert.id.desc());
        }

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public void createAdvert(CreateAdvertReq req) {
        if (ObjectUtil.isNotEmpty(req.getCategoryId())){
            SsAdvertCategory ssAdvertCategory = queryFactory.selectFrom(qSsAdvertCategory)
                    .where(qSsAdvertCategory.id.eq(req.getCategoryId()))
                    .where(qSsAdvertCategory.enabled.eq(true))
                    .fetchOne();
            if (ObjectUtil.isEmpty(ssAdvertCategory)){
                throw new BusinessException("产品类型状态已变更，请刷新重试");
            }
        }
        SsAdvert advert = queryFactory.selectFrom(qSsAdvert)
                .where(qSsAdvert.categoryId.eq(req.getCategoryId()))
                .where(qSsAdvert.sort.eq(req.getSort()))
                //.where(qSsAdvert.deleted.eq(false))
                .fetchOne();
        if (ObjectUtil.isNotEmpty(advert)) {
            throw new BusinessException("前端排序数值需唯一");
        }
        SsAdvert ssAdvert = SsAdvert.builder()
                .name(req.getTitle())
                .companyType(req.getCompanyType())
                .creatorId(req.getUserId())
                .imageUrl(req.getImageUrl())
                .linkUrl(req.getLinkUrl())
                .brief(req.getBrief())
                .label(req.getLabel())
                .enabled(true)
                .deleted(false)
                .enabledTime(Instant.now())
                .type(PC)
                .remark(req.getRemark())
                .categoryId(req.getCategoryId())
                .sort(req.getSort())
                .build();
        advertRepository.save(ssAdvert);
    }

    public void editAdvert(EditAdvertReq req) {
        SsAdvert advert = queryFactory.selectFrom(qSsAdvert)
                .where(qSsAdvert.categoryId.eq(req.getCategoryId()))
                .where(qSsAdvert.sort.eq(req.getSort()))
                .where(qSsAdvert.id.ne(req.getId()))
                //.where(qSsAdvert.deleted.eq(false))
                .fetchOne();
        if (advert != null) {
            throw new BusinessException("前端排序数值需唯一");
        }
        SsAdvert ssAdvert = advertRepository.findById(req.getId()).orElseThrow(RecordExistedException::new);
        ssAdvert.setName(req.getTitle());
        ssAdvert.setImageUrl(req.getImageUrl());
        ssAdvert.setBrief(req.getBrief());
        ssAdvert.setLabel(req.getLabel());
        ssAdvert.setLinkUrl(req.getLinkUrl());
        ssAdvert.setRemark(req.getRemark());
        ssAdvert.setCategoryId(req.getCategoryId());
        ssAdvert.setSort(req.getSort());
        advertRepository.save(ssAdvert);
    }

    public void enabledAdvert(Integer id, Boolean enabled) {
        SsAdvert ssAdvert = advertRepository.findById(id).orElseThrow(RecordExistedException::new);
        ssAdvert.setEnabled(enabled);
        if (enabled) {
            ssAdvert.setEnabledTime(Instant.now());
        }
        advertRepository.save(ssAdvert);
    }

    public List<String> getAdvertLabelList(Integer companyType, Boolean enabled, Long categoryId) {
        JPAQuery<String> query = queryFactory.selectDistinct(qSsAdvert.label)
                .from(qSsAdvert)
                .where(qSsAdvert.type.eq(PC))
                .where(qSsAdvert.companyType.eq(companyType))
                .where(qSsAdvert.deleted.eq(false));

        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(qSsAdvert.enabled.eq(enabled));
        }
        if (ObjectUtil.isNotEmpty(categoryId)) {
            query.where(qSsAdvert.categoryId.eq(categoryId));
        }
        query.orderBy(qSsAdvert.createTime.desc(), qSsAdvert.id.desc());

        return query.fetch();
    }

    public List<SsAdvert> getAdvertList(Integer companyType, List<String> labels, Boolean enabled) {

        JPAQuery<SsAdvert> query = queryFactory.selectFrom(qSsAdvert)
                .where(qSsAdvert.companyType.eq(companyType))
                .where(qSsAdvert.deleted.eq(false))
                .where(qSsAdvert.label.in(labels));

        if (ObjectUtils.isNotEmpty(enabled)) {
            query.where(qSsAdvert.enabled.eq(enabled));
        }

        query.orderBy(qSsAdvert.id.desc());

        return query.fetch();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletedAdvert(Integer id) {
        queryFactory.update(qSsAdvert)
                .where(qSsAdvert.id.eq(id))
                .set(qSsAdvert.deleted, true)
                .set(qSsAdvert.sort, nullExpression())
                .execute();
    }

    public PageResult<List<SsAdvertCategory>> getAdvertCategoryList(Boolean enable, Boolean isAsc, Integer current, Integer size) {
        JPAQuery<SsAdvertCategory> query = queryFactory.selectFrom(qSsAdvertCategory);
        if (ObjectUtil.isNotEmpty(enable)) {
            query.where(qSsAdvertCategory.enabled.eq(enable));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        if (ObjectUtil.equals(isAsc, true)) {
            query.orderBy(qSsAdvertCategory.gmtCreate.asc(), qSsAdvertCategory.id.asc());
        } else {
            query.orderBy(qSsAdvertCategory.gmtCreate.desc(), qSsAdvertCategory.id.asc());
        }

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public void createCategory(SsAdvertCategoryCreateReq req) {
        SsAdvertCategory build = SsAdvertCategory.builder()
                .name(req.getCategoryName())
                .creatorId(req.getCreatorId())
                .enabled(true)
                .build();
        advertCategoryRepository.save(build);
    }

    @Transactional
    public void updateCategory(SsAdvertCategoryUpdateReq req) {
        queryFactory.update(qSsAdvertCategory)
                .set(qSsAdvertCategory.name, req.getCategoryName())
                .where(qSsAdvertCategory.id.eq(req.getId()))
                .execute();
    }

    @Transactional
    public void enabledAdvertCategory(SsAdvertCategoryEnableReq req) {
        queryFactory.update(qSsAdvertCategory)
                .set(qSsAdvertCategory.enabled, req.getEnable())
                .where(qSsAdvertCategory.id.eq(req.getId()))
                .execute();
    }

    public List<SsAdvertCategory> getAdvertCategoryList(Collection<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return queryFactory.selectFrom(qSsAdvertCategory)
                .where(qSsAdvertCategory.id.in(ids))
                .fetch();
    }
}

package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.dao.SsLiveRoomVideoInfoDao;
import cn.shrise.radium.contentservice.dao.SsLiveRoomVideoTypeDao;
import cn.shrise.radium.contentservice.entity.SsLiveRoomVideoInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class LiveRoomVideoService {

    private final SsLiveRoomVideoTypeDao ssLiveRoomVideoTypeDao;
    private final SsLiveRoomVideoInfoDao ssLiveRoomVideoInfoDao;

    public PageResult<List<SsLiveRoomVideoInfo>> getLiveRoomVideoList(String number, Integer current, Integer size) {
        return ssLiveRoomVideoTypeDao.getLiveRoomVideoTypeByNumber(number, current, size);
    }

    public SsLiveRoomVideoInfo getLiveRoomVideoInfo(Long id) {
        return ssLiveRoomVideoInfoDao.getById(id);
    }
}

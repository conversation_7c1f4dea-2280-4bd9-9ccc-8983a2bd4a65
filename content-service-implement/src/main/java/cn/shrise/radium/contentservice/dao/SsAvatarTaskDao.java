package cn.shrise.radium.contentservice.dao;

import cn.shrise.radium.contentservice.entity.QSsAvatarTask;
import cn.shrise.radium.contentservice.entity.SsAvatarTask;
import cn.shrise.radium.contentservice.repository.AvatarTaskRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class SsAvatarTaskDao {

    private final JPAQueryFactory queryFactory;
    private final QSsAvatarTask qSsAvatarTask = QSsAvatarTask.ssAvatarTask;
    private final AvatarTaskRepository avatarTaskRepository;

    public List<SsAvatarTask> getAvatarTaskByUuid(List<String> uuidList) {
        return queryFactory.selectFrom(qSsAvatarTask)
                .where(qSsAvatarTask.taskUuid.in(uuidList))
                .fetch();
    }

    public void saveAvatarTask(SsAvatarTask task) {
        avatarTaskRepository.save(task);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAvatarTask(String taskUuid, Integer status) {
        queryFactory.update(qSsAvatarTask)
                .where(qSsAvatarTask.taskUuid.eq(taskUuid))
                .set(qSsAvatarTask.status,status)
                .execute();
    }
}

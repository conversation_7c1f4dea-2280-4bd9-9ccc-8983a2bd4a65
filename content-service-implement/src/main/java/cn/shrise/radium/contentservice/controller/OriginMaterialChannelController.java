package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialChannel;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialChannelDepartmentRelation;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialChannelUserRelation;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.req.CreateOriginMaterialChannelReq;
import cn.shrise.radium.contentservice.req.EditOriginMaterialChannelReq;
import cn.shrise.radium.contentservice.req.OriginMaterialManagerReq;
import cn.shrise.radium.contentservice.service.originmaterial.OriginMaterialChannelService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: zhangjianwu
 * @Date: 2024/5/6 14:19
 * @Desc:
 **/
@RestController
@RequestMapping("origin-material-channel")
@RequiredArgsConstructor
public class OriginMaterialChannelController {

    private final OriginMaterialChannelService originMaterialChannelService;

    @GetMapping("list")
    @ApiOperation("获取好评素材频道列表")
    PageResult<List<SsOriginMaterialChannel>> getOriginMaterialChannelList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return originMaterialChannelService.getOriginMaterialChannelList(companyType, current, size);
    }

    @PostMapping("create")
    @ApiOperation("创建好评素材频道")
    BaseResult<ContentErrorCode> createOriginMaterialChannel(@RequestBody @Valid CreateOriginMaterialChannelReq req) {
        return originMaterialChannelService.create(req);
    }

    @PutMapping("update")
    @ApiOperation("编辑好评素材频道")
    BaseResult<ContentErrorCode> updateOriginMaterialChannel(@RequestBody @Valid EditOriginMaterialChannelReq req) {
        return originMaterialChannelService.update(req);
    }

    @PostMapping("set-visible")
    @ApiOperation("设置好评素材频道可见范围")
    BaseResult<ContentErrorCode> setOriginMaterialVisible(@RequestBody @Valid OriginMaterialManagerReq req) {
        originMaterialChannelService.setOriginMaterialVisible(req);
        return BaseResult.success(ContentErrorCode.SUCCESS);
    }

    @GetMapping("relation/user")
    @ApiOperation("获取好评素材频道处理人列表")
    BaseResult<List<SsOriginMaterialChannelUserRelation>> getOriginMaterialUserRelations(
            @RequestParam @ApiParam("好评素材频道id") Long channelId
    ){
        List<SsOriginMaterialChannelUserRelation> originMaterialUserRelations = originMaterialChannelService.getOriginMaterialUserRelations(null, channelId);
        return BaseResult.success(originMaterialUserRelations);
    }

    @GetMapping("relation/department")
    @ApiOperation("获取好评素材频道部门列表")
    BaseResult<List<SsOriginMaterialChannelDepartmentRelation>> getOriginMaterialDeptRelations(
            @RequestParam @ApiParam("好评素材频道id") Long channelId
    ){
        List<SsOriginMaterialChannelDepartmentRelation> originMaterialDeptRelations = originMaterialChannelService.getOriginMaterialDeptRelations(null, channelId);
        return BaseResult.success(originMaterialDeptRelations);
    }

    @PostMapping("batch")
    @ApiOperation("根据频道ID批量获取频道map")
    public BaseResult<Map<Long, SsOriginMaterialChannel>> batchGetOriginMaterialChannelMap(@RequestBody BatchReq<Long> req) {
        List<SsOriginMaterialChannel> channelList = originMaterialChannelService.getChannelList(req.getValues());
        final Map<Long, SsOriginMaterialChannel> channelMap = channelList.stream()
                .collect(Collectors.toMap(SsOriginMaterialChannel::getId, Function.identity()));
        return BaseResult.success(channelMap);
    }

    @GetMapping("visible/list")
    @ApiOperation("获取好评素材可见频道列表")
    public BaseResult<List<SsOriginMaterialChannel>> getOriginMaterialVisibleChannelList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("用户部门id列表") List<Integer> departmentIdList) {
        List<SsOriginMaterialChannel> visibleChannelList = originMaterialChannelService.getVisibleChannelList(userId, departmentIdList);
        return BaseResult.success(visibleChannelList);
    }

}

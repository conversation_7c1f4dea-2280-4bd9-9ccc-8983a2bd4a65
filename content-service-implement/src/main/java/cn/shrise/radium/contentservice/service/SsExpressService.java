package cn.shrise.radium.contentservice.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.QSsExpress;
import cn.shrise.radium.contentservice.entity.QSsExpressLike;
import cn.shrise.radium.contentservice.entity.SsExpress;
import cn.shrise.radium.contentservice.entity.SsExpressLike;
import cn.shrise.radium.contentservice.req.UpdateExpressLikeReq;
import cn.shrise.radium.contentservice.resp.ExpressLikeCount;
import cn.shrise.radium.contentservice.resp.ExpressResp;
import cn.shrise.radium.contentservice.resp.ExpressUserIsLike;
import cn.shrise.radium.contentservice.resp.SsExpressResp;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SsExpressService {

    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;
    private final QSsExpress express = QSsExpress.ssExpress;
    private final QSsExpressLike expressLike = QSsExpressLike.ssExpressLike;

    public PageResult<List<SsExpressResp>> getExpressPage(Integer userId, Integer current, Integer size) {
        JPAQuery<SsExpress> query = queryFactory.select(express).from(express).where(express.isEnabled.eq(true));

        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();

        query.orderBy(express.releaseTime.desc(), express.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        List<SsExpressResp> ssExpressResps = new ArrayList<>();
        Set<Long> idSets = new HashSet<>();
        Map<Long, Boolean> isLikeMap = new HashMap<>();
        Map<Long, Long> likeCountMap = new HashMap<>();
        for (SsExpress fetch : query.fetch()) {
            SsExpressResp ssExpressResp = new SsExpressResp();
            BeanUtil.copyProperties(fetch, ssExpressResp);
            idSets.add(fetch.getId());
            ssExpressResps.add(ssExpressResp);
        }

        List<ExpressLikeCount> expressLikeCount = getExpressLikeCount(idSets);
        if (ObjectUtil.isNotEmpty(expressLikeCount)) {
            likeCountMap = expressLikeCount.stream().collect(Collectors.toMap(ExpressLikeCount::getId, ExpressLikeCount::getCount));
        }

        List<ExpressUserIsLike> expressUserIsLike = getExpressUserIsLike(userId, idSets);
        if (ObjectUtil.isNotNull(expressUserIsLike)) {
            isLikeMap = expressUserIsLike.stream().collect(Collectors.toMap(ExpressUserIsLike::getId, ExpressUserIsLike::getIsLike));
        }

        for (SsExpressResp ssExpressResp : ssExpressResps) {
            ssExpressResp.setIsLike(isLikeMap.getOrDefault(ssExpressResp.getId(), false));
            ssExpressResp.setLiKeCount(likeCountMap.getOrDefault(ssExpressResp.getId(), 0L));
        }

        return PageResult.success(ssExpressResps, Pagination.of(current, size, total));
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveSsExpress(List<SsExpress> ssExpressList) {
        if (ObjectUtil.isNotEmpty(ssExpressList)) {
            String sql = SqlUtil.batchInsertSql(ssExpressList);
            jdbcTemplate.execute(sql);
        }
    }

    public List<ExpressLikeCount> getExpressLikeCount(Collection<Long> expressIds) {
        if (ObjectUtils.isEmpty(expressIds)) {
            return Collections.emptyList();
        }
        NumberExpression<Long> countExpression = expressLike.customerId.count().as("count");
        Set<Long> expressIdSet = new HashSet<>(expressIds);
        List<Tuple> tuples = queryFactory.select(Projections.tuple(expressLike.expressId,
                        countExpression))
                .from(expressLike)
                .where(expressLike.expressId.in(expressIdSet))
                .where(expressLike.enabled.eq(true))
                .groupBy(expressLike.expressId)
                .fetch();

        return tuples.stream().map(tuple -> ExpressLikeCount.builder()
                        .id(tuple.get(expressLike.expressId))
                        .count(tuple.get(countExpression))
                        .build())
                .collect(Collectors.toList());
    }

    public List<ExpressUserIsLike> getExpressUserIsLike(Integer userId, Collection<Long> expressIds) {

        if (ObjectUtils.isEmpty(expressIds)) {
            return Collections.emptyList();
        }

        Set<Long> expressIdSet = new HashSet<>(expressIds);
        JPAQuery<Long> query = queryFactory.select(expressLike.expressId)
                .from(expressLike)
                .where(expressLike.expressId.in(expressIdSet))
                .where(expressLike.enabled.eq(true));

        if (ObjectUtil.isNotNull(userId)) {
            query.where(expressLike.customerId.eq(userId));
        } else {
            return null;
        }

        List<Long> likedList = query.fetch();

        Set<Long> likedSet = new HashSet<>(likedList);

        return expressIdSet.stream().map(e -> {
            boolean isLike = likedSet.contains(e);
            return ExpressUserIsLike.builder()
                    .id(e)
                    .isLike(isLike)
                    .build();
        }).collect(Collectors.toList());
    }

    @Transactional
    public Boolean updateExpressLike(UpdateExpressLikeReq req) {
        SsExpressLike entity = SsExpressLike.builder()
                .customerId(req.getUserId())
                .expressId(req.getExpressId())
                .enabled(req.getLike())
                .build();

        String sql = SqlUtil.onDuplicateKeyUpdateSql(entity);
        int affected = jdbcTemplate.update(sql);
        return affected > 0;
    }

    public List<Long> getExpressSourceIdList(List<Long> ids) {
        return queryFactory.select(express.sourceId).from(express).where(express.sourceId.in(ids)).fetch();
    }
}

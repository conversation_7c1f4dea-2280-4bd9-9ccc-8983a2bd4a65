package cn.shrise.radium.contentservice.service.evaluation;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.entity.QSsEvaluationAnswer;
import cn.shrise.radium.contentservice.entity.QSsSurveyChoice;
import cn.shrise.radium.contentservice.entity.QSsSurveyTopic;
import cn.shrise.radium.contentservice.entity.SsEvaluationAnswer;
import cn.shrise.radium.contentservice.repository.EvaluationAnswerRepository;
import cn.shrise.radium.contentservice.resp.EvaluationChoiceResp;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class EvaluationAnswerService {

    private final JPAQueryFactory queryFactory;
    private final EvaluationAnswerRepository answerRepository;

    private final QSsEvaluationAnswer evaluationAnswer = QSsEvaluationAnswer.ssEvaluationAnswer;
    private final QSsSurveyChoice surveyChoice = QSsSurveyChoice.ssSurveyChoice;
    private final QSsSurveyTopic surveyTopic = QSsSurveyTopic.ssSurveyTopic;
    private static final Integer SURVEY_TYPE = 12;

    @Transactional
    public void findAndCreateList(Integer oldEvaluationId, Integer newEvaluationId, List<Integer> surveyTypeList) {
        JPAQuery<SsEvaluationAnswer> query = queryFactory.select(evaluationAnswer)
                .from(evaluationAnswer)
                .leftJoin(surveyChoice).on(evaluationAnswer.choiceId.eq(surveyChoice.id))
                .leftJoin(surveyTopic).on(surveyChoice.topicId.eq(surveyTopic.id))
                .where(evaluationAnswer.evaluationId.eq(oldEvaluationId));
        if (ObjectUtil.isNotEmpty(surveyTypeList)) {
            query = query.where(surveyTopic.surveyType.in(surveyTypeList));
        }
        List<SsEvaluationAnswer> answers = query.fetch();
        if (ObjectUtil.isNotEmpty(answers)) {
            List<SsEvaluationAnswer> newAnswers = new ArrayList<>();
            answers.forEach(e -> {
                newAnswers.add(SsEvaluationAnswer.builder()
                        .evaluationId(newEvaluationId)
                        .choiceId(e.getChoiceId())
                        .build());
            });
            answerRepository.saveAll(newAnswers);
        }
    }

    public List<Long> getChoiceList(Integer evaluationId) {
        return queryFactory.select(evaluationAnswer.choiceId)
                .from(evaluationAnswer)
                .where(evaluationAnswer.evaluationId.eq(evaluationId))
                .fetch();
    }

    public Map<Long, EvaluationChoiceResp> getChoiceList() {
        Map<Long, EvaluationChoiceResp> choiceMap = new HashMap<>(87);
        List<Tuple> tupleList = queryFactory.select(surveyTopic.id, surveyChoice.id)
                .from(surveyTopic)
                .leftJoin(surveyChoice)
                .on(surveyTopic.id.eq(surveyChoice.topicId))
                .where(surveyTopic.surveyType.eq(SURVEY_TYPE))
                .orderBy(surveyTopic.id.asc(), surveyChoice.id.asc())
                .fetch();
        Long topicId = tupleList.get(0).get(surveyTopic.id);
        int answerNumber = 0;
        int topicNumber = 1;
        for (int i = 0; i < tupleList.size(); i++) {
            EvaluationChoiceResp evaluationChoice;
            if (topicId.equals(tupleList.get(i).get(surveyTopic.id))) {
                evaluationChoice = EvaluationChoiceResp.builder()
                        .topicNumber("a" + topicNumber)
                        .answer(getChoice(++answerNumber))
                        .build();
            } else {
                topicId = tupleList.get(i).get(surveyTopic.id);
                topicNumber = topicNumber + 1;
                answerNumber = 0;
                evaluationChoice = EvaluationChoiceResp.builder()
                        .topicNumber("a" + topicNumber)
                        .answer(getChoice(++answerNumber))
                        .build();
            }
            choiceMap.put(tupleList.get(i).get(surveyChoice.id), evaluationChoice);
        }
        return choiceMap;
    }

    public String getChoice(int number) {
        switch (number) {
            case 1:
                return "A";
            case 2:
                return "B";
            case 3:
                return "C";
            case 4:
                return "D";
            case 5:
                return "E";
            case 6:
                return "F";
            case 7:
                return "G";
            default:
                return null;
        }
    }


}

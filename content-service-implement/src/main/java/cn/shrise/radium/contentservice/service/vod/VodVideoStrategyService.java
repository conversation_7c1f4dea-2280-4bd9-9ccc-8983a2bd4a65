package cn.shrise.radium.contentservice.service.vod;

import cn.shrise.radium.contentservice.enums.VodCategoryEnum;
import cn.shrise.radium.contentservice.resp.VodCallbackResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class VodVideoStrategyService {

    private final VodUploadShortVideoStrategy vodUploadShortVideoStrategy;
    private final VodUploadVideoStrategy vodUploadVideoStrategy;

    private final Map<String, VodVideoStrategy> vodVideoStrategyMap = new HashMap<>();

    @PostConstruct
    public void addStrategy() {
        vodVideoStrategyMap.put(VodCategoryEnum.UPLOAD.name().toLowerCase(), vodUploadVideoStrategy);
        vodVideoStrategyMap.put(VodCategoryEnum.SHORT.name().toLowerCase(), vodUploadShortVideoStrategy);
    }

    public void vodVideoStrategy(Integer companyType, String cateName, VodCallbackResp callbackResp) {
        VodVideoStrategy vodVideoStrategy = vodVideoStrategyMap.get(cateName);
        if (vodVideoStrategy != null) {
            vodVideoStrategy.handle(companyType, callbackResp);
        }
    }


}

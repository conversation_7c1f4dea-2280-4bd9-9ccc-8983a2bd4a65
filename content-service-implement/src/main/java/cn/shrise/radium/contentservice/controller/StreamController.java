package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.req.EditStreamChannelReq;
import cn.shrise.radium.contentservice.req.StreamChannelAnalystReq;
import cn.shrise.radium.contentservice.req.StreamServiceManagerReq;
import cn.shrise.radium.contentservice.resp.StockMessageResp;
import cn.shrise.radium.contentservice.service.LiveStreamService;
import cn.shrise.radium.contentservice.service.StreamService;
import cn.shrise.radium.contentservice.service.stream.StreamMessageService;
import cn.shrise.radium.orderservice.entity.ArticleSeriesAnalystRelation;
import cn.shrise.radium.orderservice.entity.ArticleSeriesOperatorRelation;
import cn.shrise.radium.orderservice.req.AnalystReq;
import cn.shrise.radium.orderservice.req.ManagerReq;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("stream")
@RequiredArgsConstructor
public class StreamController {

    private final LiveStreamService liveStreamService;
    private final StreamService streamService;
    private final StreamMessageService streamMessageService;

    @GetMapping("all")
    @ApiOperation("获取所有直播室")
    public BaseResult<List<LiveStreamChannel>> getAllLiveStream(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否过滤删除") Boolean filterDeleted) {
        List<LiveStreamChannel> all = liveStreamService.findAll(companyType, filterDeleted);
        return BaseResult.success(all);
    }

    @GetMapping("channel/{id}")
    @ApiOperation("获取直播流频道")
    public BaseResult<SsStreamChannel> getStreamChannel(
            @PathVariable @ApiParam("直播流Id") Long id) {
        final SsStreamChannel streamChannel = streamService.getStreamChannel(id).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(streamChannel);
    }

    @GetMapping("channel/getStreamChannels")
    @ApiOperation("获取直播流频道列表")
    public PageResult<List<SsStreamChannel>> getStreamChannels(
            @RequestParam(required = false) @ApiParam("直播流Id列表") List<Long> ids,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return streamService.getStreamChannels(ids, companyType, current, size);
    }

    @PostMapping("channel/creatStreamChannel")
    @ApiOperation("创建直播流频道列表")
    public BaseResult<ContentErrorCode> creatStreamChannel(
            @RequestParam Integer companyType,
            @RequestBody @Valid EditStreamChannelReq req) {
        return streamService.creatStreamChannel(companyType, req);
    }

    @PostMapping("introduction")
    @ApiOperation("获取直播流服务简介列表")
    public BaseResult<List<ServiceIntroduction>> getStreamChannelServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req) {
        List<ServiceIntroduction> introductions = streamService.getStreamChannelServiceIntroductionList(companyType, req.getValues());
        return BaseResult.success(introductions);
    }

    @PostMapping("channel/updateStreamChannel")
    @ApiOperation("更新直播流频道列表")
    public BaseResult<ContentErrorCode> updateStreamChannel(@RequestBody @Valid EditStreamChannelReq req) {
        return streamService.updateStreamChannel(req);
    }

    @PostMapping("channel/addStreamChannelAnalyst")
    @ApiOperation("为直播流添加投顾老师")
    public BaseResult addStreamChannelAnalyst(@Validated @RequestBody StreamChannelAnalystReq req) {
        return streamService.addAnalyst(req);
    }

    @PostMapping("channel/deleteStreamChannelAnalystRelation")
    @ApiOperation("删除直播流投顾老师关系")
    public BaseResult deleteStreamChannelAnalystRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids) {
        return streamService.deleteAnalystRelation(Ids);
    }

    @GetMapping("channel/getStreamChannelAnalystRelations")
    @ApiOperation("获取直播流对映老师关系")
    public BaseResult<List<SsStreamChannelAnalystRelation>> getStreamChannelAnalystRelations(
            @RequestParam @ApiParam("直播流频道ID") Long channelId) {
        return streamService.getAnalystRelations(channelId);
    }

    @GetMapping("channel/analysts")
    @ApiOperation("获取直播流频道下的老师")
    public BaseResult<List<SsAnalystInfo>> getStreamChannelAnalystList(
            @RequestParam @ApiParam("直播流频道ID") Long channelId) {
        final List<SsAnalystInfo> analystList = streamService.getStreamChannelAnalystList(channelId);
        return BaseResult.success(analystList);
    }

    @PostMapping("channel/addStreamChannelManager")
    @ApiOperation("为直播流配置处理人")
    public BaseResult addStreamChannelManager(@Validated @RequestBody StreamServiceManagerReq req) {
        return streamService.addManager(req);
    }

    @GetMapping("channel/getStreamChannelManagerRelation")
    @ApiOperation("获取直播流对映处理人关系")
    public BaseResult<List<SsStreamChannelOperatorRelation>> getStreamChannelManagerRelations(
            @RequestParam(required = false) @ApiParam("直播流频道ID") Long channelId,
            @RequestParam(required = false) @ApiParam("处理人ID") Integer operatorId) {
        return streamService.getManagerRelations(channelId, operatorId);
    }

    @PostMapping("channel/deleteStreamChannelManagerRelation")
    @ApiOperation("删除直播流处理人关系")
    public BaseResult deleteStreamChannelManagerRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids) {
        return streamService.deleteManagerRelation(Ids);
    }

    @GetMapping("channel/list/{userId}")
    @ApiOperation("获取用户相关频道列表")
    public BaseResult<List<SsStreamChannel>> getChannelList(
            @PathVariable Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType) {
        List<SsStreamChannel> channelList = streamService.getChannelList(userId, companyType);
        return BaseResult.success(channelList);
    }

    @GetMapping("stock/list")
    @ApiOperation("入选股票")
    public BaseResult<List<StockMessageResp>> getChosenStocks(
            @RequestParam @ApiParam("内容主编号") String number,
            @RequestParam @ApiParam("n天内入选股票") Integer days,
            @RequestParam @ApiParam Integer companyType
    ) {
        return streamMessageService.getChosenStocks(number, days, companyType);
    }
}

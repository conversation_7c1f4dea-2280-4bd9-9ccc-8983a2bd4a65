package cn.shrise.radium.contentservice.service.index;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.RecordExistedException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.contentservice.entity.QSsQuoteIndex;
import cn.shrise.radium.contentservice.entity.SsQuoteIndex;
import cn.shrise.radium.contentservice.repository.SsQuoteIndexRepository;
import cn.shrise.radium.contentservice.req.QuoteIndexInfoReq;
import cn.shrise.radium.contentservice.resp.index.QuoteIndexResp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: tangjiajun
 * @Date: 2024/12/23 15:12
 * @Desc:
 **/

@Service
@RequiredArgsConstructor
public class QuoteIndexService {

    private final JPAQueryFactory queryFactory;
    private final QSsQuoteIndex qSsQuoteIndex = QSsQuoteIndex.ssQuoteIndex;
    private final SsQuoteIndexRepository ssQuoteIndexRepository;

    public BaseResult<List<QuoteIndexResp>> getIndexList(Integer type, Integer position, Boolean enabled) {
        JPAQuery<SsQuoteIndex> query = queryFactory.selectFrom(qSsQuoteIndex);
        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(qSsQuoteIndex.enabled.eq(enabled));
        }
        query.where(qSsQuoteIndex.type.eq(type))
                .where(qSsQuoteIndex.position.eq(position));
        if (ObjectUtil.equals(enabled, true)) {
            query.orderBy(qSsQuoteIndex.sort.asc());
        } else {
            query.orderBy(qSsQuoteIndex.id.desc());
        }
        List<SsQuoteIndex> fetch = query.fetch();
        List<QuoteIndexResp> respList = new ArrayList<>();
        fetch.forEach(i -> {
            QuoteIndexResp resp = new QuoteIndexResp();
            BeanUtil.copyProperties(i, resp);
            resp.setCreateTime(i.getGmtCreate());
            respList.add(resp);
        });
        if (ObjectUtil.isEmpty(respList)) {
            return BaseResult.success(new ArrayList<>());
        }
        return BaseResult.success(respList);
    }

    @Transactional
    public BaseResult<Void> updateIndexSort(String param) {
        JSONObject jsonObject = JSON.parseObject(param);
        Map<Long, Integer> sortMap = jsonObject.entrySet().stream().collect(
                Collectors.toMap(
                        entry -> Long.valueOf(entry.getKey()),
                        entry -> Integer.valueOf(entry.getValue().toString())
                ));
        for (Map.Entry<Long, Integer> entry : sortMap.entrySet()) {
            queryFactory.update(qSsQuoteIndex)
                    .set(qSsQuoteIndex.sort, entry.getValue())
                    .where(qSsQuoteIndex.id.eq(entry.getKey())).execute();
        }
        return BaseResult.successful();
    }

    @Transactional
    public BaseResult<Void> createIndex(QuoteIndexInfoReq req) {
        String number = queryFactory.select(qSsQuoteIndex.number).from(qSsQuoteIndex).where(qSsQuoteIndex.number.eq(req.getNumber())).fetchOne();
        String name = queryFactory.select(qSsQuoteIndex.name).from(qSsQuoteIndex).where(qSsQuoteIndex.name.eq(req.getName())).where(qSsQuoteIndex.type.eq(req.getType())).fetchOne();
        String showName = queryFactory.select(qSsQuoteIndex.showName).from(qSsQuoteIndex).where(qSsQuoteIndex.showName.eq(req.getShowName())).where(qSsQuoteIndex.type.eq(req.getType())).fetchOne();

        if (ObjectUtil.isNotEmpty(number)) {
            throw new RecordExistedException("编号重复");
        }
        if (ObjectUtil.isNotEmpty(name)) {
            throw new RecordExistedException("名称重复");
        }
        if (ObjectUtil.isNotEmpty(showName)) {
            throw new RecordExistedException("展示名称重复");
        }

        SsQuoteIndex index = SsQuoteIndex.builder().isPay(req.getIsPay())
                .enabled(true)
                .position(req.getPosition())
                .type(req.getType())
                .name(req.getName())
                .showName(req.getShowName())
                .number(req.getNumber())
                .sort(req.getSort())
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now()).build();
        ssQuoteIndexRepository.save(index);
        return BaseResult.successful();
    }

    @Transactional
    public BaseResult<Void> editIndex(QuoteIndexInfoReq req) {
        QuoteIndexResp resp = queryFactory.select(Projections.bean(QuoteIndexResp.class, qSsQuoteIndex.id, qSsQuoteIndex.name, qSsQuoteIndex.showName, qSsQuoteIndex.number))
                .from(qSsQuoteIndex).where(qSsQuoteIndex.id.eq(req.getId())).fetchOne();
        SsQuoteIndex numberIndex = queryFactory.select(qSsQuoteIndex).from(qSsQuoteIndex).where(qSsQuoteIndex.number.eq(req.getNumber())).fetchOne();
        SsQuoteIndex nameIndex = queryFactory.select(qSsQuoteIndex).from(qSsQuoteIndex).where(qSsQuoteIndex.name.eq(req.getName())).where(qSsQuoteIndex.type.eq(req.getType())).fetchOne();
        SsQuoteIndex showNameIndex = queryFactory.select(qSsQuoteIndex).from(qSsQuoteIndex).where(qSsQuoteIndex.showName.eq(req.getShowName())).where(qSsQuoteIndex.type.eq(req.getType())).fetchOne();

        if (resp == null) {
            throw new RecordNotExistedException("未找到对应的指标记录，ID：" + req.getId());
        }
        if (numberIndex != null && !ObjectUtil.equals(resp.getId(), numberIndex.getId()) && ObjectUtil.equals(req.getNumber(), numberIndex.getNumber())) {
            throw new RecordExistedException("编号重复");
        }
        if (nameIndex != null && !ObjectUtil.equals(resp.getId(), nameIndex.getId()) && ObjectUtil.equals(req.getName(), nameIndex.getName())) {
            throw new RecordExistedException("名称重复");
        }
        if (showNameIndex != null && !ObjectUtil.equals(resp.getId(), showNameIndex.getId()) && ObjectUtil.equals(req.getShowName(), showNameIndex.getShowName())) {
            throw new RecordExistedException("展示名称重复");
        }
        JPAUpdateClause updateClause = queryFactory.update(qSsQuoteIndex)
                .set(qSsQuoteIndex.name, req.getName())
                .set(qSsQuoteIndex.showName, req.getShowName())
                .set(qSsQuoteIndex.type, req.getType())
                .set(qSsQuoteIndex.position, req.getPosition())
                .set(qSsQuoteIndex.isPay, req.getIsPay());
        if (numberIndex == null) {
            updateClause.set(qSsQuoteIndex.number, req.getNumber());
        }
        updateClause.where(qSsQuoteIndex.id.eq(req.getId()))
                .execute();
        return BaseResult.successful();
    }

    public BaseResult<QuoteIndexResp> getTypeAndPosition(Long id) {
        QuoteIndexResp resp = queryFactory.select(Projections.bean(QuoteIndexResp.class, qSsQuoteIndex.type, qSsQuoteIndex.position))
                .from(qSsQuoteIndex).where(qSsQuoteIndex.id.eq(id)).fetchOne();
        return BaseResult.success(resp);
    }

    public BaseResult<Integer> getMaxSort(Integer type, Integer position) {
        Integer sort = queryFactory.select(qSsQuoteIndex.sort).from(qSsQuoteIndex)
                .where(qSsQuoteIndex.type.eq(type))
                .where(qSsQuoteIndex.position.eq(position))
                .where(qSsQuoteIndex.enabled.eq(true))
                .orderBy(qSsQuoteIndex.sort.desc()).fetchFirst();
        if (sort == null) {
            sort = 0;
        }
        return BaseResult.success(sort);
    }

    @Transactional
    public BaseResult<Void> setEnabled(Long id, Boolean enabled, Integer sort) {
        if (ObjectUtil.equals(enabled, true)) {
            queryFactory.update(qSsQuoteIndex).set(qSsQuoteIndex.sort, sort).where(qSsQuoteIndex.id.eq(id)).execute();
        }
        queryFactory.update(qSsQuoteIndex).set(qSsQuoteIndex.enabled, enabled).where(qSsQuoteIndex.id.eq(id)).execute();
        return BaseResult.successful();
    }

    public List<SsQuoteIndex> getIndexListByFilter(Integer type, Integer position, Boolean enabled) {
        JPAQuery<SsQuoteIndex> query = queryFactory.selectFrom(qSsQuoteIndex);
        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(qSsQuoteIndex.enabled.eq(enabled));
        }
        if (ObjectUtil.isNotEmpty(type)) {
            query.where(qSsQuoteIndex.type.eq(type));
        }
        if (ObjectUtil.isNotEmpty(position)) {
            query.where(qSsQuoteIndex.position.eq(position));
        }
        if (ObjectUtil.equals(enabled, true)) {
            query.orderBy(qSsQuoteIndex.sort.asc());
        } else {
            query.orderBy(qSsQuoteIndex.id.desc());
        }
        return query.fetch();
    }
}


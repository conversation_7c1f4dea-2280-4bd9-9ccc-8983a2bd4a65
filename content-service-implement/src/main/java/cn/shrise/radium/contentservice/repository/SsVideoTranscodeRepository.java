package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsVideoTranscode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SsVideoTranscodeRepository extends JpaRepository<SsVideoTranscode, Long>, QuerydslPredicateExecutor<SsVideoTranscode> {

    Optional<SsVideoTranscode> findByVideoId(String videoId);
}

package cn.shrise.radium.contentservice.job;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsNew;
import cn.shrise.radium.contentservice.properties.NewsConfigProperty;
import cn.shrise.radium.contentservice.req.GetExpressReq;
import cn.shrise.radium.contentservice.req.GetExpressTokenReq;
import cn.shrise.radium.contentservice.req.GetNewsDetailReq;
import cn.shrise.radium.contentservice.resp.PostExpressResp;
import cn.shrise.radium.contentservice.service.NewsService;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.oss.OSS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class PullYrNewsJob extends JavaProcessor {

    //友睿api文档
    //http://quote.youruitech.com/yourui/openapi/docscopy.html
    private final NewsConfigProperty newsConfigProperty;
    private final RestTemplate restTemplate;
    private final NewsService newsService;
    private final OSS ossClient;

    @Override
    public ProcessResult process(JobContext context) throws Exception {

        GetExpressTokenReq getExpressTokenReq = GetExpressTokenReq.builder()
                .appkey(newsConfigProperty.getNewsExpressConfig().getAppKey())
                .appsecret(newsConfigProperty.getNewsExpressConfig().getAppSecret())
                .devicetype(newsConfigProperty.getNewsExpressConfig().getDeviceType())
                .build();
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.add(HttpHeaders.ORIGIN, newsConfigProperty.getNewsExpressConfig().getOrigin());
        //获取友睿token
        String yrToken = getYrToken(getExpressTokenReq, headers);
        if (ObjectUtil.isNotNull(yrToken)) {
            //同步7*24新闻
            syncYrNews(yrToken, headers);
            //处理同步失败新闻
//            List<SsNew> uploadFailYrNews = newsService.getUploadFailYrNews();
//            uploadYrNewsPic(uploadFailYrNews, headers);
        } else {
            return new ProcessResult(false, "获取友睿token失败");
        }
        return new ProcessResult(true, "运行成功");
    }

    private String getYrToken(GetExpressTokenReq getExpressTokenReq, HttpHeaders headers) {
        HttpEntity<GetExpressTokenReq> getExpressTokenReqHttpEntity = new HttpEntity<>(getExpressTokenReq, headers);
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(newsConfigProperty.getNewsExpressConfig().getTokenPath(), getExpressTokenReqHttpEntity, String.class);
        JSONObject jsonObject = JSONUtil.parseObj(stringResponseEntity.getBody());
        Integer code = jsonObject.getInt("code");
        String token = jsonObject.getStr("token");
        if (ObjectUtil.equals(code, 0)) {
            return token;
        } else {
            log.info("获取友睿新闻token失败,{}", jsonObject.getStr("msg"));
            return null;
        }
    }

    private void uploadYrNewsPic(List<SsNew> uploadFailYrNews, HttpHeaders headers) {
        if (ObjectUtil.isNotEmpty(uploadFailYrNews)) {
            for (SsNew ssNew : uploadFailYrNews) {
                GetNewsDetailReq getNewsDetailReq = GetNewsDetailReq.builder().id(Integer.parseInt(ssNew.getSourceId())).infoType(1).build();
                HttpEntity<GetNewsDetailReq> getNewDetailReqHttpEntity = new HttpEntity<>(getNewsDetailReq, headers);
                ResponseEntity<String> newsDetailResponseEntity = restTemplate.postForEntity(newsConfigProperty.getNewsExpressConfig().getDetailPath(), getNewDetailReqHttpEntity, String.class);
                if (JSONUtil.parseObj(newsDetailResponseEntity.getBody()).getInt("code") == 200) {
                    String detailStr = JSONUtil.parseObj(newsDetailResponseEntity.getBody()).getStr("data");
                    JSONObject detail = JSONUtil.parseObj(detailStr);
                    Document doc = Jsoup.parse(detail.getStr("content"));
                    Elements p = doc.getElementsByTag("p");

                    try {
                        for (Element e : p) {
                            if (ObjectUtil.isNotEmpty(e.childNodes())) {
                                for (Node node : e.childNodes()) {
                                    if (ObjectUtil.isNotEmpty(e.getElementsByTag("img"))) {
                                        String src = node.attributes().get("src").trim();
                                        node.attributes().remove("src");
                                        if (ObjectUtil.isNotEmpty(src)) {
                                            if (!src.startsWith("https:")) {
                                                src = "https:" + src;
                                            }
                                            node.attr("src", newsConfigProperty.getConfig().getOssPath() + updateFromUrl(src));
                                        }
                                    }
                                }
                            }
                        }
                        ssNew.setContentHtml(doc.toString());
                        ssNew.setIsEnabled(true);
                    } catch (IOException e) {
                        log.info("上传图片失败,sourceId:{},exception:{}", getNewsDetailReq.getId(), e);
                        ssNew.setIsEnabled(false);
                    }
                } else {
                    log.info("获取新闻详情失败," + getNewsDetailReq.getId());
                }
                newsService.updateNewsBySourceId(ssNew);
            }
        }
    }

    public void syncYrNews(String token, HttpHeaders headers) {
        headers.add(HttpHeaders.AUTHORIZATION, token);
        int pageIndex = 1;
        String pattern = "yyyy-MM-dd HH:mm:ss";
        while (true) {
            GetExpressReq getNewReq = GetExpressReq.builder().page(pageIndex).infoType(5).build();
            HttpEntity<GetExpressReq> getNewReqHttpEntity = new HttpEntity<>(getNewReq, headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(newsConfigProperty.getNewsExpressConfig().getNewsPath(), getNewReqHttpEntity, String.class);
            String data = JSONUtil.parseObj(responseEntity.getBody()).getStr("data");
            PostExpressResp expressResp = JSON.parseObject(data, PostExpressResp.class);
            List<SsNew> ssNews = new ArrayList<>();
            for (Object record : expressResp.getRecords()) {
                JSONObject entries = JSONUtil.parseObj(record);
                String sourceId = entries.getStr("id");
                String title = entries.getStr("title");
                String releaseTime = entries.getStr("publ_date");
                String codeType = entries.getStr("codeType");
                String newsSourceAddress = entries.getStr("newsSourceAddress");
                String media = entries.getStr("media");
                String stockCode = entries.getStr("stockCode");
                String pxChangeRate = entries.getStr("pxChangeRate");
                String stockName = entries.getStr("stockName");
                String companyCode = entries.getStr("companycode");
                SsNew.SsNewBuilder ssNewBuilder = SsNew.builder().sourceId(sourceId).title(title).publishTime(DateUtils.formatterStringToInstant(releaseTime, pattern))
                        .codeType(codeType).link(newsSourceAddress).source(media).stockCode(stockCode).pxChangeRate(pxChangeRate)
                        .stockName(stockName).companyCode(companyCode).channelId(50L).isEnabled(true);
                ssNews.add(ssNewBuilder.build());
            }

            List<String> sourceIds = ssNews.stream().map(SsNew::getSourceId).collect(Collectors.toList());
            List<String> newsList = newsService.getNewSourceIds(sourceIds);
            if (sourceIds.size() <= newsList.size()) {
                log.info("7×24无最新数据");
                break;
            }
            ssNews.removeIf(news -> newsList.contains(news.getSourceId()));

            //获取新数据详情
            for (SsNew ssNew : ssNews) {
                GetNewsDetailReq getNewsDetailReq = GetNewsDetailReq.builder().id(Integer.parseInt(ssNew.getSourceId())).infoType(1).build();
                HttpEntity<GetNewsDetailReq> getNewDetailReqHttpEntity = new HttpEntity<>(getNewsDetailReq, headers);
                ResponseEntity<String> newsDetailResponseEntity = restTemplate.postForEntity(newsConfigProperty.getNewsExpressConfig().getDetailPath(), getNewDetailReqHttpEntity, String.class);
                if (JSONUtil.parseObj(newsDetailResponseEntity.getBody()).getInt("code") == 200) {
                    String detailStr = JSONUtil.parseObj(newsDetailResponseEntity.getBody()).getStr("data");
                    JSONObject detail = JSONUtil.parseObj(detailStr);
                    String anAbstract = detail.getStr("abstract");
                    String content = detail.getStr("content");
                    ssNew.setHtml(content);
                    ssNew.setBrief(anAbstract);
                    Document doc = Jsoup.parse(content);
                    Elements img = doc.getElementsByTag("img");

                    for (Element item : img) {
                        String src = item.attributes().get("src").trim();
                        String srcTemp = item.attributes().get("src").trim();
                        item.attributes().remove("src");
                        if (ObjectUtil.isNotEmpty(src)) {
                            if (src.startsWith("//comment.")) {
                                src = "https:" + src;
                            } else if (src.startsWith("//e.thsi.cn")) {
                                src = "https:" + src;
                            }
                            if (!src.startsWith("https:")) {
                                continue;
                            }
                            try {
                                item.attr("src", newsConfigProperty.getConfig().getOssPath() + updateFromUrl(src));
                            } catch (IOException e) {
                                log.info("上传图片失败,sourceId:{},exception:{}", getNewsDetailReq.getId(), e);
                                item.attr("src", srcTemp);
                            }
                        }
                    }
                    ssNew.setContentHtml(doc.toString());
                } else {
                    log.info("获取新闻详情失败," + getNewsDetailReq.getId());
                }
            }
            newsService.batchSaveSsNew(ssNews);
            pageIndex++;
        }
    }

    public String updateFromUrl(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(3 * 1000);
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        InputStream inputStream = conn.getInputStream();
        byte[] getData = readInputStream(inputStream);
        String id = IdUtil.nanoId(8);
        ossClient.putObject("gs-file-src", "news/" + id + ".jpg", new ByteArrayInputStream(getData));
        return "news/" + id + ".jpg";
    }

    public byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }
}

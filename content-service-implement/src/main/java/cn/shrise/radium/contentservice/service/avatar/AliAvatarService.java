package cn.shrise.radium.contentservice.service.avatar;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.constant.AvatarVideoContentEnum;
import cn.shrise.radium.contentservice.constant.AvatarVideoTaskStatusConstant;
import cn.shrise.radium.contentservice.dao.SsAvatarTaskDao;
import cn.shrise.radium.contentservice.entity.SsAvatarRequestRecord;
import cn.shrise.radium.contentservice.entity.SsAvatarTask;
import cn.shrise.radium.contentservice.properties.AvatarConfigProperty;
import cn.shrise.radium.contentservice.repository.AvatarRequestRecordRepository;
import cn.shrise.radium.contentservice.req.CreateAvatarVideoReq;
import cn.shrise.radium.contentservice.resp.AvatarInfoResp;
import cn.shrise.radium.contentservice.resp.AvatarVideoInfoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.aliyun.avatar20220130.Client;
import com.aliyun.avatar20220130.models.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AliAvatarService {
    private final Client avatarClient;
    private final AvatarRequestRecordRepository avatarRequestRecordRepository;
    private final SsAvatarTaskDao ssAvatarTaskDao;
    private final UserClient userClient;
    private final AvatarConfigProperty avatarConfigProperty;
    // 竖屏背景url
    private static String backUrlPortrait = "https://nuwa-live-image.oss-cn-beijing.aliyuncs.com/yun-vh/image/video-system-bg/%E7%A7%91%E6%8A%8011_1080_1920.png";
    // 横屏背景url
    private static String backUrlLandscape = "https://nuwa-live-image.oss-cn-beijing.aliyuncs.com/yun-vh/image/video-system-bg/%E7%A7%91%E6%8A%801.png";
    public void createAvatarVideo(CreateAvatarVideoReq req) {
        String taskUuid;
        if (AvatarVideoContentEnum.TEXT.getValue().equals(req.getContentType())) {
            taskUuid = createTextAvatarVideo(req);
        } else if (AvatarVideoContentEnum.AUDIO.getValue().equals(req.getContentType())) {
            taskUuid = createAudioAvatarVideo(req);
        } else {
            throw new RuntimeException("暂不支持该类型");
        }
        if (ObjectUtil.isNotEmpty(taskUuid)) {
            SsAvatarTask task = SsAvatarTask.builder()
                    .taskUuid(taskUuid)
                    .creatorId(req.getCreatorId())
                    .status(AvatarVideoTaskStatusConstant.WAITING)
                    .build();
            ssAvatarTaskDao.saveAvatarTask(task);
        }
    }

    public void saveRequestRecord(String apiType, String requestBody, String responseBody, Boolean isSuccess) {
        SsAvatarRequestRecord record = SsAvatarRequestRecord.builder()
                .apiType(apiType)
                .requestBody(requestBody)
                .responseResult(responseBody)
                .isSuccess(isSuccess)
                .build();
        avatarRequestRecordRepository.save(record);

    }

    // 根据文本生成视频
    public String createTextAvatarVideo(CreateAvatarVideoReq req) {

        SubmitTextTo2DAvatarVideoTaskRequest avatarVideoTaskRequest = new SubmitTextTo2DAvatarVideoTaskRequest();
        SubmitTextTo2DAvatarVideoTaskRequest.SubmitTextTo2DAvatarVideoTaskRequestAvatarInfo avatarInfo = new SubmitTextTo2DAvatarVideoTaskRequest.SubmitTextTo2DAvatarVideoTaskRequestAvatarInfo();
        avatarInfo.setCode(req.getAvatarCode());
        avatarVideoTaskRequest.setAvatarInfo(avatarInfo);
        SubmitTextTo2DAvatarVideoTaskRequest.SubmitTextTo2DAvatarVideoTaskRequestVideoInfo videoInfo = new SubmitTextTo2DAvatarVideoTaskRequest.SubmitTextTo2DAvatarVideoTaskRequestVideoInfo();
        videoInfo.setSubtitleEmbedded(req.getSubtitleEmbedded());
        videoInfo.setResolution(req.getResolution());
        if (req.getResolution()==3) {
            videoInfo.setBackgroundImageUrl(backUrlPortrait);
        }else {
            videoInfo.setBackgroundImageUrl(backUrlLandscape);
        }
        avatarVideoTaskRequest.setVideoInfo(videoInfo);
        avatarVideoTaskRequest.setText(req.getText());
        avatarVideoTaskRequest.setTitle(req.getTitle());
        avatarVideoTaskRequest.setCallback(true);
        avatarVideoTaskRequest.setTenantId(Long.valueOf(avatarConfigProperty.getTenantId()));
        SubmitTextTo2DAvatarVideoTaskRequest.SubmitTextTo2DAvatarVideoTaskRequestApp requestApp = new SubmitTextTo2DAvatarVideoTaskRequest.SubmitTextTo2DAvatarVideoTaskRequestApp();
        requestApp.setAppId(avatarConfigProperty.getAppId());
        avatarVideoTaskRequest.setApp(requestApp);
        String requestBody = JSONObject.toJSONString(avatarVideoTaskRequest);
        SubmitTextTo2DAvatarVideoTaskResponse videoTaskResponse = null;
        boolean isSuccess = false;
        try {
            videoTaskResponse = avatarClient.submitTextTo2DAvatarVideoTask(avatarVideoTaskRequest);
            isSuccess = true;
        } catch (Exception e) {
            log.error("submitTextTo2DAvatarVideoTask error:{}", ExceptionUtil.getStackTrace(e));
        }
        saveRequestRecord("SubmitTextTo2DAvatarVideoTask", requestBody, JSONObject.toJSONString(videoTaskResponse), isSuccess);
        String taskUuid;
        if (videoTaskResponse != null && videoTaskResponse.getBody() != null) {
            if (Objects.equals(videoTaskResponse.getBody().getSuccess(), true)) {
                taskUuid = videoTaskResponse.getBody().getData().getTaskUuid();
            } else {
                log.error("文本生成视频失败code:{},msg:{}", videoTaskResponse.getBody().getCode(), videoTaskResponse.getBody().getMessage());
                throw new BusinessException(videoTaskResponse.getBody().getMessage());
            }
        } else {
            throw new BusinessException("文本生成视频接口调用失败");
        }
        return taskUuid;
    }

    // 根据音频生成视频
    public String createAudioAvatarVideo(CreateAvatarVideoReq req) {

        SubmitAudioTo2DAvatarVideoTaskRequest avatarVideoTaskRequest = new SubmitAudioTo2DAvatarVideoTaskRequest();
        SubmitAudioTo2DAvatarVideoTaskRequest.SubmitAudioTo2DAvatarVideoTaskRequestAvatarInfo avatarInfo = new SubmitAudioTo2DAvatarVideoTaskRequest.SubmitAudioTo2DAvatarVideoTaskRequestAvatarInfo();
        avatarInfo.setCode(req.getAvatarCode());
        avatarVideoTaskRequest.setAvatarInfo(avatarInfo);
        SubmitAudioTo2DAvatarVideoTaskRequest.SubmitAudioTo2DAvatarVideoTaskRequestVideoInfo videoInfo = new SubmitAudioTo2DAvatarVideoTaskRequest.SubmitAudioTo2DAvatarVideoTaskRequestVideoInfo();
        videoInfo.setResolution(req.getResolution());
        if (req.getResolution()==3) {
            videoInfo.setBackgroundImageUrl(backUrlPortrait);
        }else {
            videoInfo.setBackgroundImageUrl(backUrlLandscape);
        }
        avatarVideoTaskRequest.setVideoInfo(videoInfo);
        avatarVideoTaskRequest.setUrl("https://tj-file-oss.cf69.com/" + req.getAudioUrl());
        avatarVideoTaskRequest.setTitle(req.getTitle());
        avatarVideoTaskRequest.setCallback(true);
        avatarVideoTaskRequest.setTenantId(Long.valueOf(avatarConfigProperty.getTenantId()));
        SubmitAudioTo2DAvatarVideoTaskRequest.SubmitAudioTo2DAvatarVideoTaskRequestApp requestApp = new SubmitAudioTo2DAvatarVideoTaskRequest.SubmitAudioTo2DAvatarVideoTaskRequestApp();
        requestApp.setAppId(avatarConfigProperty.getAppId());
        avatarVideoTaskRequest.setApp(requestApp);
        String requestBody = JSONObject.toJSONString(avatarVideoTaskRequest);
        boolean isSuccess = false;
        SubmitAudioTo2DAvatarVideoTaskResponse videoTaskResponse = null;
        try {
            videoTaskResponse = avatarClient.submitAudioTo2DAvatarVideoTask(avatarVideoTaskRequest);
            isSuccess = true;
        } catch (Exception e) {
            log.error("submitAudioTo2DAvatarVideoTask error:{}", ExceptionUtil.getStackTrace(e));
        }
        saveRequestRecord("SubmitAudioTo2DAvatarVideoTask", requestBody, JSONObject.toJSONString(videoTaskResponse), isSuccess);
        String taskUuid;
        if (videoTaskResponse != null && videoTaskResponse.getBody() != null) {
            if (Objects.equals(videoTaskResponse.getBody().getSuccess(), true)) {
                taskUuid = videoTaskResponse.getBody().getData().getTaskUuid();
            } else {
                log.error("文本生成视频失败code:{},msg:{}", videoTaskResponse.getBody().getCode(), videoTaskResponse.getBody().getMessage());
                throw new BusinessException(videoTaskResponse.getBody().getMessage());
            }
        } else {
            throw new BusinessException("文本生成视频接口调用失败");
        }
        return taskUuid;
    }


    public List<AvatarInfoResp> avatarList(String modelType) {
        int current = 1;
        int size = 1000;
        QueryAvatarListRequest queryAvatarListRequest = new QueryAvatarListRequest();
        queryAvatarListRequest.setModelType(modelType);
        queryAvatarListRequest.setTenantId(Long.valueOf(avatarConfigProperty.getTenantId()));
        List<QueryAvatarListResponseBody.QueryAvatarListResponseBodyDataList> list = new ArrayList<>();
        while (true) {
            queryAvatarListRequest.setPageNo(current);
            queryAvatarListRequest.setPageSize(size);
            String requestBody = JSONObject.toJSONString(queryAvatarListRequest);
            boolean isSuccess = false;
            QueryAvatarListResponse response = null;
            try {
                response = avatarClient.queryAvatarList(queryAvatarListRequest);
                isSuccess = true;
            } catch (Exception e) {
                log.error("queryAvatarList error:{}", ExceptionUtil.getStackTrace(e));
            }
            saveRequestRecord("QueryAvatarList", requestBody, JSONObject.toJSONString(response), isSuccess);
            if (response == null || response.getBody() == null
                    || Objects.equals(response.getBody().getSuccess(), false) || response.getBody().getData() == null) {
                break;
            }
            QueryAvatarListResponseBody.QueryAvatarListResponseBodyData data = response.getBody().getData();
            if (ObjectUtil.isEmpty(data.getList())) {
                break;
            }
            List<QueryAvatarListResponseBody.QueryAvatarListResponseBodyDataList> collect =
                    data.getList().stream()
                            .filter(o -> o != null && Objects.equals(o.getAvatarType(), "1"))
                            .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(collect)) {
                list.addAll(collect);
            }
            if ((long) current * size >= data.getTotalCount()) {
                break;
            }
            current++;
        }
        return list.stream()
                .map(o -> {
                    AvatarInfoResp avatarInfoResp = new AvatarInfoResp();
                    BeanUtils.copyProperties(o, avatarInfoResp);
                    if (o.getSupportedResolutions() != null) {
                        AvatarInfoResp.SupportedResolutionsDto supportedResolutionsDto = new AvatarInfoResp.SupportedResolutionsDto();
                        List<AvatarInfoResp.SupportedResolutionsDto.OfflineDto> offline = Optional.ofNullable(o.getSupportedResolutions().getOffline())
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(Objects::nonNull)
                                .map(obj -> {
                                    AvatarInfoResp.SupportedResolutionsDto.OfflineDto offlineDto = new AvatarInfoResp.SupportedResolutionsDto.OfflineDto();
                                    BeanUtils.copyProperties(obj, offlineDto);
                                    return offlineDto;
                                }).collect(Collectors.toList());
                        List<AvatarInfoResp.SupportedResolutionsDto.OfflineDto> online = Optional.ofNullable(o.getSupportedResolutions().getOnline())
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(Objects::nonNull)
                                .map(obj -> {
                                    AvatarInfoResp.SupportedResolutionsDto.OfflineDto onlineDto = new AvatarInfoResp.SupportedResolutionsDto.OfflineDto();
                                    BeanUtils.copyProperties(obj, onlineDto);
                                    return onlineDto;
                                }).collect(Collectors.toList());
                        supportedResolutionsDto.setOffline(offline);
                        supportedResolutionsDto.setOnline(online);
                        avatarInfoResp.setSupportedResolutions(supportedResolutionsDto);
                    }
                    return avatarInfoResp;
                }).sorted(Comparator.comparing(AvatarInfoResp::getCode, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    public PageResult<List<AvatarVideoInfoResp>> videoTaskList(Integer type, Integer status, Integer current, Integer size) {

        QueryVideoTaskInfoRequest queryVideoListRequest = new QueryVideoTaskInfoRequest();
        queryVideoListRequest.setType(type);
        queryVideoListRequest.setStatus(status);
        queryVideoListRequest.setPageNo(current);
        queryVideoListRequest.setPageSize(size);
        queryVideoListRequest.setTenantId(Long.valueOf(avatarConfigProperty.getTenantId()));
        QueryVideoTaskInfoRequest.QueryVideoTaskInfoRequestApp requestApp = new QueryVideoTaskInfoRequest.QueryVideoTaskInfoRequestApp();
        requestApp.setAppId(avatarConfigProperty.getAppId());
        queryVideoListRequest.setApp(requestApp);
        String requestBody = JSONObject.toJSONString(queryVideoListRequest);
        boolean isSuccess = false;
        QueryVideoTaskInfoResponse response = null;
        try {
            response = avatarClient.queryVideoTaskInfo(queryVideoListRequest);
            isSuccess = true;
        } catch (Exception e) {
            log.error("queryVideoTaskInfo error:{}", ExceptionUtil.getStackTrace(e));
        }
        saveRequestRecord("QueryVideoTaskInfo", requestBody, JSONObject.toJSONString(response), isSuccess);
        if (response != null && Objects.equals(200, response.getStatusCode()) && response.getBody() != null
                && Objects.equals(true, response.getBody().getSuccess()) && response.getBody().getData() != null) {
            QueryVideoTaskInfoResponseBody.QueryVideoTaskInfoResponseBodyData data = response.getBody().getData();
            List<AvatarVideoInfoResp> respList = null;
            if (ObjectUtil.isNotEmpty(data.getList())) {
                List<String> uuidList = data.getList()
                        .stream()
                        .filter(o -> o != null && StringUtils.isNotBlank(o.getTaskUuid()))
                        .map(QueryVideoTaskInfoResponseBody.QueryVideoTaskInfoResponseBodyDataList::getTaskUuid)
                        .collect(Collectors.toList());
                List<SsAvatarTask> avatarTaskList = ssAvatarTaskDao.getAvatarTaskByUuid(uuidList);
                Set<Integer> creatorIdSet = new HashSet<>();
                Map<String, SsAvatarTask> map = new HashMap<>();
                if (ObjectUtil.isNotEmpty(avatarTaskList)) {
                    for (SsAvatarTask ssAvatarTask : avatarTaskList) {
                        if (ssAvatarTask != null) {
                            creatorIdSet.add(ssAvatarTask.getCreatorId());
                            map.put(ssAvatarTask.getTaskUuid(), ssAvatarTask);
                        }
                    }
                }
                Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(creatorIdSet)).orElse(new HashMap<>());
                respList = data.getList()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(o -> {
                            AvatarVideoInfoResp infoResp = new AvatarVideoInfoResp();
                            SsAvatarTask avatarTask = map.getOrDefault(o.getTaskUuid(), new SsAvatarTask());
                            BeanUtils.copyProperties(o, infoResp);
                            if (o.getTaskResult() != null) {
                                AvatarVideoInfoResp.TaskResultDto taskResultDto = new AvatarVideoInfoResp.TaskResultDto();
                                BeanUtils.copyProperties(o.getTaskResult(), taskResultDto);
                                infoResp.setTaskResult(taskResultDto);
                            }
                            infoResp.setCreatorName(usersMap.getOrDefault(avatarTask.getCreatorId(), new UcUsers()).getRealName());
                            infoResp.setUpdateTime(avatarTask.getGmtModified());
                            infoResp.setCreateTime(avatarTask.getGmtCreate());
                            return infoResp;
                        }).sorted(Comparator.comparing(AvatarVideoInfoResp::getCreateTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
            }
            Pagination pagination = Pagination.of(data.pageNo, data.pageSize, data.getTotalCount());
            return PageResult.success(respList, pagination);
        } else {
            return PageResult.empty();
        }
    }

}

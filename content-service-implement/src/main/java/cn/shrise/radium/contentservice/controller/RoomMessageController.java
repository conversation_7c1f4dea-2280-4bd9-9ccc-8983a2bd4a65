package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.dto.SsRoomLotteryDetailDto;
import cn.shrise.radium.contentservice.entity.SsLiveRoom;
import cn.shrise.radium.contentservice.entity.SsLiveRoomMessage;
import cn.shrise.radium.contentservice.entity.SsRoomPrize;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.req.CreateRoomLotteryReq;
import cn.shrise.radium.contentservice.req.CreateRoomPrizeReq;
import cn.shrise.radium.contentservice.req.EditRoomMessageReq;
import cn.shrise.radium.contentservice.req.UpdateRoomLotteryReq;
import cn.shrise.radium.contentservice.resp.LiveRoomNameResp;
import cn.shrise.radium.contentservice.resp.RoomLotteryOperateResp;
import cn.shrise.radium.contentservice.resp.RoomLotteryResp;
import cn.shrise.radium.contentservice.service.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@RestController
@RequestMapping("room-message")
@RequiredArgsConstructor
public class RoomMessageController {

    private final RoomMessageService roomMessageService;

    @PostMapping
    @ApiOperation("新增直播室消息")
    public BaseResult<SsLiveRoomMessage> addLiveRoomMessage(
            @RequestBody @Valid EditRoomMessageReq req
    ) {
        SsLiveRoomMessage msg = SsLiveRoomMessage.builder()
                .roomId(req.getRoomId())
                .creatorId(req.getOperatorId())
                .messageType(req.getMessageType().getValue())
                .dealId(req.getDealId())
                .build();
        msg = roomMessageService.addMessage(msg);
        return BaseResult.success(msg);
    }
}

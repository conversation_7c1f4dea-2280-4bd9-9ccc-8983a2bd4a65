package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.contentservice.req.AdminAppVersionReq;
import cn.shrise.radium.contentservice.resp.AdminAppVersionInfoResp;
import cn.shrise.radium.contentservice.resp.AdminAppVersionResp;
import cn.shrise.radium.contentservice.service.AdminAppVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api
@RestController
@RequestMapping("admin-app-version")
@RequiredArgsConstructor
public class AdminAppVersionController {

    private final AdminAppVersionService adminAppVersionService;

    @GetMapping("list")
    @ApiOperation("版本管理列表")
    public BaseResult<List<AdminAppVersionResp>> adminAppVersionList(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type) {
        return adminAppVersionService.list(type);
    }

    @PostMapping("create")
    @ApiOperation("新增版本")
    public BaseResult<Void> addAdminAppVersion(@RequestBody AdminAppVersionReq req) {
        adminAppVersionService.save(req);
        return BaseResult.successful();
    }

    @PostMapping("update")
    @ApiOperation("编辑版本")
    public BaseResult<Void> updateAdminAppVersion(@RequestBody AdminAppVersionReq req) {
        adminAppVersionService.update(req);
        return BaseResult.successful();
    }

    @ApiOperation("版本上架/下架")
    @PostMapping("set-status")
    public BaseResult<Void> updateAdminAppVersionStatus(@RequestParam @ApiParam("false:下架，true：上架") Boolean enabled,
                                                        @RequestParam @ApiParam("id") Long id) {
        adminAppVersionService.setStatus(enabled, id);
        return BaseResult.successful();
    }

    @ApiOperation("检查更新版本")
    @GetMapping("check-update-version")
    public BaseResult<AdminAppVersionInfoResp> checkAdminAppUpdateVersion(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type,
                                                                          @RequestParam @ApiParam("版本号") String version) {
        return adminAppVersionService.checkVersion(type, version);
    }

    @ApiOperation("获取最新版本")
    @GetMapping("last")
    public BaseResult<AdminAppVersionResp> getAdminAppVersionLast(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type) {
        return adminAppVersionService.getLast(type);
    }

}

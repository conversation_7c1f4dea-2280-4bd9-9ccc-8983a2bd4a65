package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsVideoAiTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SsVideoAiTagRepository extends JpaRepository<SsVideoAiTag, Long>, QuerydslPredicateExecutor<SsVideoAiTag> {

    Optional<SsVideoAiTag> findByCompanyTypeAndSourceId(Integer companyType, String sourceId);
}

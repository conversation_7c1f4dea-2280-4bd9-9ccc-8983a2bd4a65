package cn.shrise.radium.contentservice.service.evaluation;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.constant.CompanyTypeConstant;
import cn.shrise.radium.common.constant.SexEnum;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.*;
import cn.shrise.radium.contentservice.constant.BardRecordConstant;
import cn.shrise.radium.contentservice.constant.IdentityTypeEnum;
import cn.shrise.radium.contentservice.constant.JobTypeConstant;
import cn.shrise.radium.contentservice.constant.SurveyTopicTypeConstant;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.error.CsErrorCode;
import cn.shrise.radium.contentservice.repository.CustomerEvaluationRepository;
import cn.shrise.radium.contentservice.repository.EvaluationIdRecordRepository;
import cn.shrise.radium.contentservice.repository.EvaluationRecordRepository;
import cn.shrise.radium.contentservice.req.EvaluationUserInfoReq;
import cn.shrise.radium.contentservice.req.SaveEvaluationAnswerReq;
import cn.shrise.radium.contentservice.req.SubmitEvaluationReq;
import cn.shrise.radium.contentservice.req.UpdateCustomerEvaluationReq;
import cn.shrise.radium.contentservice.resp.EvaluationAnswerResp;
import cn.shrise.radium.contentservice.resp.EvaluationChoiceResp;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.LockUtils.getCustomerEvaluationLockKey;
import static cn.shrise.radium.contentservice.constant.EvaluationIdRecordTypeConstant.SUBMIT;
import static cn.shrise.radium.contentservice.constant.EvaluationRecordTypeConstant.FINISH;
import static cn.shrise.radium.contentservice.enums.ContentErrorCode.EVALUATION_SCORE_NULL;
import static cn.shrise.radium.contentservice.enums.ContentErrorCode.RECORD_NOT_EXISTED;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerEvaluationService {

    private final JPAQueryFactory queryFactory;
    private final CustomerEvaluationRepository evaluationRepository;

    private final EvaluationIdRecordRepository evaluationIdRecordRepository;

    private final QSsCustomerEvaluation customerEvaluation = QSsCustomerEvaluation.ssCustomerEvaluation;

    private final JdbcTemplate jdbcTemplate;

    private final EvaluationRecordRepository evaluationRecordRepository;

    private final EvaluationAnswerService evaluationAnswerService;

    private final RedissonClient redissonClient;

    private final static Integer EXPIRE_DAY = 540;

    private final static Integer C1 = 24;

    private final EvaluationUtil evaluationUtil;
    private final QSsEvaluationAnswer qSsEvaluationAnswer = QSsEvaluationAnswer.ssEvaluationAnswer;
    private final QSsSurveyTopic qSsSurveyTopic = QSsSurveyTopic.ssSurveyTopic;
    private final QSsSurveyChoice qSsSurveyChoice = QSsSurveyChoice.ssSurveyChoice;


    public List<String> findAllNumber(Integer companyType) {
        Instant date = DateUtils.getDayOfStart(Instant.now());
        return queryFactory.select(customerEvaluation.number)
                .from(customerEvaluation)
                .where(customerEvaluation.companyType.eq(companyType))
                .where(customerEvaluation.createTime.goe(date))
                .fetch();
    }

    public SsCustomerEvaluation findOneById(Integer id) {
        return queryFactory.select(customerEvaluation)
                .from(customerEvaluation)
                .where(customerEvaluation.id.eq(id))
                .fetchOne();
    }

    public SsCustomerEvaluation findOneByUserId(Integer userId) {
        return queryFactory.select(customerEvaluation)
                .from(customerEvaluation)
                .where(customerEvaluation.userId.eq(userId))
                .where(customerEvaluation.isDeprecated.eq(false))
                .fetchFirst();
    }

    @Transactional
    public void abandonOneById(Integer id) {
        queryFactory.update(customerEvaluation)
                .set(customerEvaluation.isDeprecated, true)
                .where(customerEvaluation.id.eq(id))
                .execute();
    }

    @Transactional
    public SsCustomerEvaluation findOrCreateOne(Integer companyType, String mobile, Boolean isDeprecated,
                                                Integer userId, List<String> existNumberList, String name,
                                                Integer antiSurveyScore, Instant antiSurveyTime) {
        JPAQuery<SsCustomerEvaluation> query = queryFactory.select(customerEvaluation)
                .from(customerEvaluation)
                .where(customerEvaluation.companyType.eq(companyType))
                .where(customerEvaluation.userId.eq(userId))
                .where(customerEvaluation.isDeprecated.eq(isDeprecated));
        SsCustomerEvaluation existOne = query.fetchOne();
        if (ObjectUtil.isNotNull(existOne)) {
            return existOne;
        }
        SsCustomerEvaluation evaluation = SsCustomerEvaluation.builder()
                .companyType(companyType)
                .number(generateEvaluationNumber(companyType, existNumberList))
                .userId(userId)
                .mobile(mobile)
                .name(name)
                .antiSurveyScore(antiSurveyScore)
                .antiSurveyTime(antiSurveyTime)
                .isDeprecated(false)
                .build();
        return evaluationRepository.save(evaluation);
    }

    public String generateEvaluationNumber(Integer companyType, List<String> exitList) {
        String prefixStr;
        if (Objects.equals(companyType, CompanyTypeConstant.CT_GS.getValue())) {
            prefixStr = "HCT" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        } else {
            prefixStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss"));
        }
        String randomStr = prefixStr + RandomUtil.randomNumbers(4);
        if (ObjectUtil.isNotEmpty(exitList) && exitList.contains(randomStr)) {
            return generateEvaluationNumber(companyType, exitList);
        }
        return randomStr;
    }

    public Optional<SsCustomerEvaluation> getUserEvaluation(Integer userId) {
        SsCustomerEvaluation customerEvaluation = queryFactory.selectFrom(this.customerEvaluation)
                .where(this.customerEvaluation.userId.eq(userId))
                .where(this.customerEvaluation.isDeprecated.eq(false))
                .fetchOne();
        return Optional.ofNullable(customerEvaluation);
    }

    public SsCustomerEvaluation findByIdentityNumber(Integer companyType, String identityNumber) {
        JPAQuery<SsCustomerEvaluation> query = queryFactory.selectFrom(this.customerEvaluation)
                .where(this.customerEvaluation.companyType.eq(companyType))
                .where(this.customerEvaluation.identityNumber.eq(identityNumber))
                .where(this.customerEvaluation.surveyScore.isNotNull())
                .where(this.customerEvaluation.isDeprecated.eq(false));
        return query.fetchFirst();
    }

    public Optional<SsCustomerEvaluation> findById(Integer id) {
        return evaluationRepository.findById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void submitEvaluation(SubmitEvaluationReq req) {
        SsCustomerEvaluation existedOne = evaluationRepository.findById(req.getEvaluationId()).orElseThrow(() -> new BusinessException(RECORD_NOT_EXISTED));
        if (evaluationUtil.getUserEvaluationKey(existedOne.getUserId()).getCount() >= 2) {
            throw new BusinessException("测评次数超限");
        }
        if (existedOne.getIsDeprecated()) {
            throw new BusinessException("该测评已经废弃");
        }

        if (!ObjectUtil.equals(existedOne.getUserId(), req.getUserId())) {
            log.info("提交用户测评 evaluationId{},userid{},{}", req.getEvaluationId(), existedOne.getUserId(), req.getUserId());
            throw new BusinessException("非当前授权用户操作, 无法提交");
        }
        Instant now = Instant.now();
        QSsEvaluationAnswer qSsEvaluationAnswer = QSsEvaluationAnswer.ssEvaluationAnswer;
        List<SsEvaluationAnswer> answerList = queryFactory.selectFrom(qSsEvaluationAnswer)
                .where(qSsEvaluationAnswer.evaluationId.eq(req.getEvaluationId()))
                .fetch();
        if (CollectionUtil.isNotEmpty(answerList)) {
            queryFactory.delete(qSsEvaluationAnswer)
                    .where(qSsEvaluationAnswer.evaluationId.eq(req.getEvaluationId()))
                    .execute();
        }
        List<SsEvaluationAnswer> evaluationAnswerList = new ArrayList<>();
        for (String choiceId : req.getChoiceId()) {
            SsEvaluationAnswer answer = SsEvaluationAnswer.builder()
                    .choiceId(Long.parseLong(choiceId))
                    .evaluationId(req.getEvaluationId())
                    .build();
            evaluationAnswerList.add(answer);
        }
        String sql = SqlUtil.batchInsertSql(evaluationAnswerList);
        if (sql != null) {
            jdbcTemplate.execute(sql);
        }
        JPAUpdateClause updateClause = queryFactory.update(customerEvaluation)
                .set(customerEvaluation.surveyScore, req.getScore())
                .set(customerEvaluation.surveyTime, now);

        if (req.getScore() > C1) {
            evaluationUtil.deleteUserEvaluationKey(req.getUserId());
            updateClause.set(customerEvaluation.expireTime, now.plus(EXPIRE_DAY, ChronoUnit.DAYS));
        } else {
            evaluationUtil.addUserEvaluationKey(req.getUserId());
        }

        updateClause.where(customerEvaluation.id.eq(req.getEvaluationId()))
                .execute();
        EvaluationRecord build = EvaluationRecord.builder()
                .evaluationId(req.getEvaluationId())
                .operateType(FINISH)
                .operatorId(req.getUserId())
                .gmtCreate(now)
                .gmtModified(now)
                .build();
        evaluationRecordRepository.save(build);
    }

    @Transactional(rollbackFor = Exception.class)
    public void finishEvaluation(Integer userId, Integer evaluationId, Integer surveyType) {
        checkEvaluation(userId, evaluationId);
        Instant now = Instant.now();
        Set<Long> topicIdSet = queryFactory.selectFrom(qSsSurveyTopic)
                .where(qSsSurveyTopic.surveyType.eq(surveyType))
                .where(qSsSurveyTopic.disabled.eq(false))
                .fetch().stream().map(SsSurveyTopic::getId).collect(Collectors.toSet());
        Set<Long> answerChoiceIdSet = queryFactory.selectFrom(qSsEvaluationAnswer)
                .where(qSsEvaluationAnswer.evaluationId.eq(evaluationId))
                .fetch().stream().map(SsEvaluationAnswer::getChoiceId).collect(Collectors.toSet());
        List<SsSurveyChoice> answerChoiceList = queryFactory.selectFrom(qSsSurveyChoice)
                .where(qSsSurveyChoice.id.in(answerChoiceIdSet)).fetch();
        Set<Long> answerTopicIdSet = answerChoiceList.stream().map(SsSurveyChoice::getTopicId).collect(Collectors.toSet());
        if (!topicIdSet.equals(answerTopicIdSet)) {
            throw new BusinessException(CsErrorCode.SURVEY_NOT_COMPLETE);
        }
        Integer score = answerChoiceList.stream().mapToInt(SsSurveyChoice::getScore).sum();
        JPAUpdateClause updateClause = queryFactory.update(customerEvaluation)
                .set(customerEvaluation.surveyScore, score)
                .set(customerEvaluation.surveyTime, now);

        if (score > C1) {
            evaluationUtil.deleteUserEvaluationKey(userId);
            updateClause.set(customerEvaluation.expireTime, now.plus(EXPIRE_DAY, ChronoUnit.DAYS));
        } else {
            evaluationUtil.addUserEvaluationKey(userId);
        }

        updateClause.where(customerEvaluation.id.eq(evaluationId))
                .execute();
        EvaluationRecord build = EvaluationRecord.builder()
                .evaluationId(evaluationId)
                .operateType(FINISH)
                .operatorId(userId)
                .gmtCreate(now)
                .gmtModified(now)
                .build();
        evaluationRecordRepository.save(build);
    }

    public void checkEvaluation(Integer userId, Integer evaluationId) {
        SsCustomerEvaluation existedOne = evaluationRepository.findById(evaluationId).orElseThrow(() -> new BusinessException(RECORD_NOT_EXISTED));
        if (evaluationUtil.getUserEvaluationKey(existedOne.getUserId()).getCount() >= 2) {
            throw new BusinessException("测评次数超限");
        }
        if (existedOne.getIsDeprecated()) {
            throw new BusinessException("该测评已经废弃");
        }

        if (!ObjectUtil.equals(existedOne.getUserId(), userId)) {
            log.info("提交用户测评 evaluationId{},userid{},{}", evaluationId, existedOne.getUserId(), userId);
            throw new BusinessException("非当前授权用户操作, 无法提交");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveEvaluationAnswer(SaveEvaluationAnswerReq req) {
        if (ObjectUtil.isNotEmpty(req.getChoiceIdList())) {
            checkTopicChoice(req.getSurveyType(), new HashSet<>(req.getChoiceIdList()));
        }
        queryFactory.delete(qSsEvaluationAnswer)
                .where(qSsEvaluationAnswer.evaluationId.eq(req.getEvaluationId()))
                .execute();
        if (ObjectUtil.isNotEmpty(req.getChoiceIdList())) {
            List<SsEvaluationAnswer> evaluationAnswerList = req.getChoiceIdList().stream().map(choiceId ->
                    SsEvaluationAnswer.builder()
                            .choiceId(choiceId)
                            .evaluationId(req.getEvaluationId())
                            .build()).collect(Collectors.toList());
            String sql = SqlUtil.batchInsertSql(evaluationAnswerList);
            if (sql != null) {
                jdbcTemplate.execute(sql);
            }
        }
    }

    public EvaluationAnswerResp getEvaluationAnswer(Integer evaluationId) {
        List<Long> choiceIdList = queryFactory.selectFrom(qSsEvaluationAnswer)
                .where(qSsEvaluationAnswer.evaluationId.eq(evaluationId))
                .fetch().stream().map(SsEvaluationAnswer::getChoiceId).collect(toList());
        List<EvaluationAnswerResp.TopicChoiceInfo> topicChoiceList = queryFactory.selectFrom(qSsSurveyChoice)
                .where(qSsSurveyChoice.id.in(choiceIdList))
                .fetch().stream().collect(groupingBy(SsSurveyChoice::getTopicId, mapping(SsSurveyChoice::getId, toList())))
                .entrySet().stream().map(entry ->
                        EvaluationAnswerResp.TopicChoiceInfo.builder()
                                .topicId(entry.getKey())
                                .choiceIdList(entry.getValue())
                                .build()).collect(toList());
        return EvaluationAnswerResp.builder().topicChoiceList(topicChoiceList).build();
    }

    public void checkTopicChoice(Integer surveyType, Set<Long> answerChoiceIdList) {
        List<SsSurveyTopic> topicList = queryFactory.selectFrom(qSsSurveyTopic)
                .where(qSsSurveyTopic.surveyType.eq(surveyType))
                .where(qSsSurveyTopic.disabled.eq(false))
                .fetch();
        Set<Long> choiceIdSet = queryFactory.selectFrom(qSsSurveyChoice)
                .where(qSsSurveyChoice.topicId.in(topicList.stream().map(SsSurveyTopic::getId).collect(Collectors.toSet())))
                .fetch().stream().map(SsSurveyChoice::getId).collect(toSet());
        Collection<Long> subtract = CollectionUtil.subtract(answerChoiceIdList, choiceIdSet);
        if (!subtract.isEmpty()) {
            throw new BusinessException(CsErrorCode.SURVEY_CHOICE_NOT_CONTAIN);
        }
        Map<Long, List<Long>> topicChoiceMap = queryFactory.selectFrom(qSsSurveyChoice)
                .where(qSsSurveyChoice.id.in(answerChoiceIdList))
                .fetch().stream()
                .collect(groupingBy(SsSurveyChoice::getTopicId, mapping(SsSurveyChoice::getId, toList())));
        for (SsSurveyTopic topic : topicList) {
            if (topicChoiceMap.containsKey(topic.getId())) {
                List<Long> choices = topicChoiceMap.get(topic.getId());
                if (ObjectUtil.equals(topic.getTopicType(), SurveyTopicTypeConstant.SINGLE_CHOICE) && choices.size() > 1) {
                    throw new BusinessException(CsErrorCode.SURVEY_SINGLE_CHOICE_BUT_MULTIPLE);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void submitEvaluationUserInfo(EvaluationUserInfoReq req) {
        SsCustomerEvaluation evaluation = findOneById(req.getEvaluationId());
        if (evaluation.getIsDeprecated()) {
            throw new BusinessException("该测评已经废弃");
        }

        if (!ObjectUtil.equals(evaluation.getUserId(), req.getUserId())) {
            log.info("提交用户基本信息evaluationId{},userid{},{}", req.getEvaluationId(), evaluation.getUserId(), req.getUserId());
            throw new BusinessException("非当前授权用户操作, 无法提交");
        }

        JPAUpdateClause updateClause = queryFactory.update(customerEvaluation)
                .set(customerEvaluation.job, req.getJob())
                .set(customerEvaluation.otherJob, req.getOtherJob())
                .set(customerEvaluation.workUnit, req.getWorkUnit())
                .set(customerEvaluation.education, req.getEducation())
                .set(customerEvaluation.badRecord, req.getBadRecord())
                .set(customerEvaluation.otherBadRecord, req.getOtherBadRecord())
                .set(customerEvaluation.address, req.getAddress())
                .set(customerEvaluation.isUserInfoSubmitted, true);
        updateClause.where(customerEvaluation.id.eq(req.getEvaluationId()))
                .execute();
    }

    public SsCustomerEvaluation findByUserid(Integer userId) {
        SsCustomerEvaluation ssCustomerEvaluation = queryFactory.selectFrom(customerEvaluation)
                .where(customerEvaluation.userId.eq(userId))
                .where(customerEvaluation.isDeprecated.eq(false))
                .fetchFirst();
        if (ssCustomerEvaluation != null) {
            ssCustomerEvaluation.setIdentityNumber(AESUtil.decrypt(ssCustomerEvaluation.getIdentityNumber()));
        }
        return ssCustomerEvaluation;
    }

    public Page<SsCustomerEvaluation> getIdEvaluation(Integer userId, Pageable pageable) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(customerEvaluation.userId.eq(userId)
                .and(customerEvaluation.identityNumber.isNotNull()));
        return evaluationRepository.findAll(builder, pageable);
    }

    public Page<SsCustomerEvaluation> getEvaluation(Integer userId, Pageable pageable) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(customerEvaluation.userId.eq(userId)
                .and(customerEvaluation.identityNumber.isNotNull())
                .and(customerEvaluation.surveyScore.isNotNull()));
        return evaluationRepository.findAll(builder, pageable);
    }

    public SsCustomerEvaluation createCustomerEvaluation(Integer userId, Integer companyType, String mobile) {

        final String lockKey = getCustomerEvaluationLockKey(userId);

        RLock lock = redissonClient.getLock(lockKey);
        try {
            //按orderId锁，10s后自动释放锁
            boolean tryLock = lock.tryLock(5 * 100, 10 * 1000, TimeUnit.MILLISECONDS);
            if (tryLock) {
                JPAQuery<SsCustomerEvaluation> query = queryFactory.select(customerEvaluation)
                        .from(customerEvaluation)
                        .where(customerEvaluation.companyType.eq(companyType))
                        .where(customerEvaluation.userId.eq(userId))
                        .where(customerEvaluation.isDeprecated.eq(false));
                SsCustomerEvaluation existOne = query.fetchOne();
                if (ObjectUtil.isNotNull(existOne)) {
                    //释放锁
                    lock.unlock();
                    return existOne;
                }
                List<String> numberList = findAllNumber(companyType);
                SsCustomerEvaluation evaluation = SsCustomerEvaluation.builder()
                        .companyType(companyType)
                        .number(generateEvaluationNumber(companyType, numberList))
                        .userId(userId)
                        .mobile(mobile)
                        .isDeprecated(false)
                        .build();
                SsCustomerEvaluation save = evaluationRepository.save(evaluation);
                save.setIdentityNumber(AESUtil.decrypt(save.getIdentityNumber()));
                return save;
            } else {
                throw new BusinessException("获取锁失败");
            }
        } catch (InterruptedException e) {
            throw new BusinessException("获取锁失败");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCustomerEvaluation(UpdateCustomerEvaluationReq req) {
        SsCustomerEvaluation ssCustomerEvaluation = evaluationRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(RECORD_NOT_EXISTED));
        JPAUpdateClause updateClause = queryFactory.update(customerEvaluation)
                .set(customerEvaluation.identityType, req.getIdentityType())
                .set(customerEvaluation.identityNumber, AESUtil.encrypt(req.getIdentityNumber()))
                .set(customerEvaluation.sex, req.getSex())
                .set(customerEvaluation.idVerifyType, req.getIdVerifyType())
                .set(customerEvaluation.name, req.getName())
                .set(customerEvaluation.idVerifyStatus, req.getIdVerifyStatus());
        if (req.getMobile() != null) {
            updateClause.set(customerEvaluation.mobile, req.getMobile());
        }
        if (ObjectUtil.isNotEmpty(req.getIdentityNumber()) && req.getIdentityNumber().length() >= 15 && req.getIdentityNumber().length() <= 18) {
            updateClause.set(customerEvaluation.birthday, IdcardUtil.getBirthDate(req.getIdentityNumber()).toLocalDateTime().toLocalDate());
            updateClause.set(customerEvaluation.region, IdcardUtil.getProvinceByIdCard(req.getIdentityNumber()));
            updateClause.set(customerEvaluation.sex, IdcardUtil.getGenderByIdCard(req.getIdentityNumber()));
        }
        updateClause.where(customerEvaluation.id.eq(req.getId()))
                .execute()
        ;
        EvaluationIdRecord build = EvaluationIdRecord.builder()
                .evaluationId(req.getId())
                .operateType(SUBMIT)
                .operatorId(ssCustomerEvaluation.getUserId())
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now())
                .build();
        evaluationIdRecordRepository.save(build);
    }

    public Map<String, Object> getEvaluationData(Integer evaluationId) {

        SsCustomerEvaluation evaluation = evaluationRepository.findById(evaluationId).orElseThrow(() -> new BusinessException(RECORD_NOT_EXISTED));
        Map<String, Object> dataMap = new HashMap<>(40);
        if (evaluation.getSurveyScore() == null) {
            throw new BusinessException(EVALUATION_SCORE_NULL);
        }
        String idNumber = ObjectUtil.isNotEmpty(AESUtil.decrypt(evaluation.getIdentityNumber())) ? AESUtil.decrypt(evaluation.getIdentityNumber()) : evaluation.getIdentityNumber();
        //基本信息
        dataMap.put("name1", evaluation.getName());
        dataMap.put("sex", SexEnum.getSex(evaluation.getSex()));
        if (ObjectUtil.isNotEmpty(evaluation.getJob()) && evaluation.getJob().equals(JobTypeConstant.OTHER)) {
            dataMap.put("otherJob", evaluation.getOtherJob());
        }
        dataMap.put("job" + evaluation.getJob(), "Yes");
        dataMap.put("workUnit", evaluation.getWorkUnit());
        dataMap.put("education" + evaluation.getEducation(), "Yes");
        if (ObjectUtil.isNotEmpty(evaluation.getBadRecord()) && evaluation.getBadRecord().equals(BardRecordConstant.OTHER)) {
            dataMap.put("otherBadRecord", evaluation.getOtherBadRecord());
        }
        dataMap.put("badRecord" + evaluation.getBadRecord(), "Yes");
        dataMap.put("identityType", IdentityTypeEnum.getIdentityType(evaluation.getIdentityType()));
        dataMap.put("identityNumber", DesensitizedUtil.idCardNum(AESUtil.decrypt(evaluation.getIdentityNumber()), 4, 4));
        if (ObjectUtil.isNotEmpty(evaluation.getMobile())) {
            dataMap.put("mobile", evaluation.getMobile());
        }
        dataMap.put("address", evaluation.getAddress());
        dataMap.put("name2", evaluation.getName());
        String surveyTime = DateUtils.formatterInstantToString(evaluation.getSurveyTime(), DateUtils.DEFAULT_PATTERN_DATE_CHINESE);
        dataMap.put("date1", surveyTime);

        //选项信息
        List<Long> choiceList = evaluationAnswerService.getChoiceList(evaluationId);
        Map<Long, EvaluationChoiceResp> choiceMap = evaluationAnswerService.getChoiceList();
        for (Long choiceId : choiceList) {
            EvaluationChoiceResp choiceResp = choiceMap.get(choiceId);
            if (dataMap.containsKey(choiceResp.getTopicNumber())) {
                String answer = String.valueOf(dataMap.get(choiceResp.getTopicNumber()));
                dataMap.put(choiceResp.getTopicNumber(), answer + choiceResp.getAnswer());
            } else {
                dataMap.put(choiceResp.getTopicNumber(), choiceResp.getAnswer());
            }
        }
        dataMap.put("date2", surveyTime);
        dataMap.put("name3", evaluation.getName());
        if (ObjectUtil.isNotEmpty(evaluation.getSurveyScore()) && evaluation.getSurveyScore() > 24) {
            dataMap.put("name4", evaluation.getName());
            dataMap.put("date3", surveyTime);
            dataMap.put("risk", evaluationUtil.formatEvaluationScore(evaluation.getSurveyScore()));
            dataMap.put("risk1", evaluationUtil.formatEvaluationScore(evaluation.getSurveyScore()));
            dataMap.put("risk2", evaluationUtil.formatEvaluationScore(evaluation.getSurveyScore()));
            dataMap.put("name5", evaluation.getName());
            dataMap.put("date4", surveyTime);
            dataMap.put("name6", evaluation.getName());
            dataMap.put("date5", surveyTime);
            dataMap.put("name7", evaluation.getName());
            dataMap.put("date6", surveyTime);
        }

        return dataMap;
    }

    public SsCustomerEvaluation findByNumber(String number) {
        return queryFactory.selectFrom(customerEvaluation)
                .where(customerEvaluation.number.eq(number))
                .fetchOne();
    }

    public List<SsCustomerEvaluation> getCustomerEvaluationList(Collection<Integer> userIdList) {
        if (ObjectUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        final Set<Integer> idSet = userIdList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return queryFactory.selectFrom(customerEvaluation)
                .where(customerEvaluation.userId.in(idSet))
                .where(customerEvaluation.isDeprecated.eq(false))
                .fetch();
    }

    public BaseResult<SsCustomerEvaluation> getUserLastEvaluation(Integer userId) {
        SsCustomerEvaluation fetchFirst = queryFactory.selectFrom(customerEvaluation)
                .where(customerEvaluation.userId.eq(userId))
                .orderBy(customerEvaluation.id.desc())
                .fetchFirst();
        return BaseResult.success(fetchFirst);
    }
}

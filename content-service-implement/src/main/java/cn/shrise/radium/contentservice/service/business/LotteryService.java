package cn.shrise.radium.contentservice.service.business;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.entity.SsRoomLottery;
import cn.shrise.radium.contentservice.entity.SsRoomLotteryDetail;
import cn.shrise.radium.contentservice.entity.SsRoomLotteryOperateRecord;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.enums.RoomLotteryOperateRecord;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.repository.RoomLotteryDetailRepository;
import cn.shrise.radium.contentservice.repository.RoomLotteryOperateRecordRepository;
import cn.shrise.radium.contentservice.repository.RoomLotteryRepository;
import cn.shrise.radium.contentservice.service.RoomLotteryDetailService;
import cn.shrise.radium.imservice.ImClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.shrise.radium.contentservice.enums.RoomLotteryTypeEnum.SPECIAL_LOTTERY;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LotteryService {

    private final RoomLotteryRepository lotteryRepository;
    private final RoomLotteryDetailRepository lotteryDetailRepository;
    private final RoomLotteryDetailService lotteryDetailService;
    private final ImClient imClient;
    private final RoomLotteryRepository roomLotteryRepository;
    private final StringRedisTemplate stringRedisTemplate;
    private final RoomLotteryOperateRecordRepository roomLotteryOperateRecordRepository;

    public void handleDrawLottery(Long lotteryId){
        SsRoomLottery lotteryInfo = lotteryRepository.findById(lotteryId).orElseThrow(() -> new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED));
        if (lotteryInfo.getStatus() == RoomLotteryType.OPEN_2.getValue()){
            if (ObjectUtil.equals(lotteryInfo.getType(), SPECIAL_LOTTERY.getValue())) {
                lotteryInfo.setStatus(RoomLotteryType.COMPLETED_4.getValue());
                roomLotteryRepository.save(lotteryInfo);
                String roomLotteryKey = "room_lottery:" + lotteryInfo.getSceneId().toString();
                stringRedisTemplate.delete(roomLotteryKey);
                SsRoomLotteryOperateRecord ssRoomLotteryOperateRecord = new SsRoomLotteryOperateRecord();
                ssRoomLotteryOperateRecord.setOperatorId(null);
                ssRoomLotteryOperateRecord.setLotteryId(lotteryId);
                ssRoomLotteryOperateRecord.setOperateType(RoomLotteryOperateRecord.FINISH_LOTTERY.getValue());
                roomLotteryOperateRecordRepository.save(ssRoomLotteryOperateRecord);
                imClient.lotteryPush(lotteryInfo.getRoomId(), lotteryInfo.getSceneId(), lotteryId, RoomLotteryType.COMPLETED_4.getValue());
                log.info("活动结束lotteryId:{}", lotteryId);
            } else {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                List<SsRoomLotteryDetail> detailList = lotteryDetailRepository.findAllByLotteryId(lotteryId);
                // 实际中奖人数
                int luckyCount = Math.min(detailList.size(), lotteryInfo.getMaxCount());
                Set<Integer> luckyUser = RandomUtil.randomEleSet(detailList.stream().map(SsRoomLotteryDetail::getUserId).collect(Collectors.toSet()), luckyCount);
                lotteryDetailService.drawLottery(lotteryId, luckyUser, lotteryInfo.getPrizeId());
                imClient.lotteryPush(lotteryInfo.getRoomId(), lotteryInfo.getSceneId(), lotteryId, RoomLotteryType.DRAW_3.getValue());
                log.info("活动{}({})准备开奖，参与人数{}, 最大中奖数{}, 中奖数{}, ", lotteryInfo.getId(),
                        lotteryInfo.getName(), detailList.size(), lotteryInfo.getMaxCount(), luckyCount);
            }
        } else {
            log.error("活动{}({})开奖失败，当前状态 {}({})", lotteryInfo.getId(),
                    lotteryInfo.getName(), lotteryInfo.getStatus(), RoomLotteryType.findMsgByValue(lotteryInfo.getStatus()));
        }

    }
}

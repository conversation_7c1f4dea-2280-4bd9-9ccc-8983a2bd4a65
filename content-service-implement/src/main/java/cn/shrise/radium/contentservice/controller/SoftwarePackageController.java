package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.contentservice.req.SoftwarePackageReq;
import cn.shrise.radium.contentservice.resp.SoftwarePackageResp;
import cn.shrise.radium.contentservice.service.SoftwarePackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * @Author: tang<PERSON><PERSON>un
 * @Date: 2024/6/24 13:42
 * @Desc:
 **/
@Api
@RestController
@RequestMapping("software-package")
@RequiredArgsConstructor
public class SoftwarePackageController {

    private final SoftwarePackageService softwarePackageService;
    @GetMapping("list")
    @ApiOperation("软件包管理")
    public PageResult<List<SoftwarePackageResp>> packageList(
            @RequestParam @ApiParam("产品类型：10-国诚智投,20-决策家PC,30-决策家Android") Integer packageType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Sort.Direction direction = Sort.Direction.DESC;
        Page<SoftwarePackageResp> page = softwarePackageService.packageList(packageType, PageRequest.of(current - 1, size, Sort.by(direction, "gmtCreate")));
        Pagination pagination = Pagination.of(current, page.getSize(), page.getTotalElements());
        return PageResult.success(page.getContent(), pagination);
    }

    @PostMapping("add")
    @ApiOperation("新增软件包")
    public BaseResult<Void> addPackage(@RequestBody @Valid SoftwarePackageReq softwarePackageReq) {
        softwarePackageService.addPackage(softwarePackageReq);
        return BaseResult.successful();
    }

    @PostMapping("update-enable")
    @ApiOperation("更新软件包状态")
    public BaseResult<Void> updatePackageEnable(
            @RequestParam Long id,
            @RequestParam @ApiParam("上架/下架") Boolean enabled) {
        return softwarePackageService.updatePackageEnable(id,enabled);
    }

    @GetMapping("info")
    @ApiOperation("软件包详情")
    public BaseResult<SoftwarePackageResp> packageInfo(@RequestParam @ApiParam("id") Long id){
        SoftwarePackageResp resp= softwarePackageService.packageInfo(id);
        return BaseResult.success(resp);
    }

    @GetMapping("latest")
    @ApiOperation("获取最新安装包")
    public BaseResult<SoftwarePackageResp> LatestPackage(@RequestParam @ApiParam("软件包类型") Integer packageType){
        SoftwarePackageResp resp= softwarePackageService.LatestPackage(packageType);
        return BaseResult.success(resp);
    }
}

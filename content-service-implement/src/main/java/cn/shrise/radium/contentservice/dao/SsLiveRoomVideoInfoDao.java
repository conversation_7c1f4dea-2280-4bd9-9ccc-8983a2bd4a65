package cn.shrise.radium.contentservice.dao;

import cn.shrise.radium.contentservice.entity.QSsLiveRoomVideoInfo;
import cn.shrise.radium.contentservice.entity.SsLiveRoomVideoInfo;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class SsLiveRoomVideoInfoDao {

    private final JPAQueryFactory queryFactory;
    private final QSsLiveRoomVideoInfo qSsLiveRoomVideoInfo = QSsLiveRoomVideoInfo.ssLiveRoomVideoInfo;

    public SsLiveRoomVideoInfo getById(Long id) {
        return queryFactory.selectFrom(qSsLiveRoomVideoInfo)
                .where(qSsLiveRoomVideoInfo.pkId.eq(id).and(qSsLiveRoomVideoInfo.isDelete.eq(0)))
                .fetchOne();
    }
}

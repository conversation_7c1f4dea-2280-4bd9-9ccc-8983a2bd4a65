package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.repository.SsStreamChannelRepository;
import cn.shrise.radium.contentservice.req.EditStreamChannelReq;
import cn.shrise.radium.contentservice.req.StreamChannelAnalystReq;
import cn.shrise.radium.contentservice.req.StreamServiceManagerReq;
import cn.shrise.radium.contentservice.resp.ChannelAnalystInfo;
import cn.shrise.radium.contentservice.resp.StockMessageResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcContentTeam;
import cn.shrise.radium.userservice.resp.ContentTeamResp;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.STREAM;

@Slf4j
@Service
@RequiredArgsConstructor
public class StreamService {

    private final JdbcTemplate jdbcTemplate;
    private final JPAQueryFactory queryFactory;
    private final QSsStreamChannel qSsStreamChannel = QSsStreamChannel.ssStreamChannel;
    private final QSsStreamChannelAnalystRelation analystRelation = QSsStreamChannelAnalystRelation.ssStreamChannelAnalystRelation;
    private final QSsStreamChannelOperatorRelation operatorRelation = QSsStreamChannelOperatorRelation.ssStreamChannelOperatorRelation;
    private final SsStreamChannelRepository streamChannelRepository;
    private final QSsAnalystInfo qSsAnalystInfo = QSsAnalystInfo.ssAnalystInfo;
    private final AnalystInfoService analystInfoService;
    private final UserClient userClient;

    public Optional<SsStreamChannel> getStreamChannel(Long id) {
        return streamChannelRepository.findById(id);
    }

    /**
     * 获取直播流频道列表
     *
     * @param companyType
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<SsStreamChannel>> getStreamChannels(List<Long> ids, Integer companyType, Integer current, Integer size) {
        JPAQuery<SsStreamChannel> query = queryFactory.select(qSsStreamChannel)
                .from(qSsStreamChannel)
                .where(qSsStreamChannel.enabled.eq(true));
        if (ObjectUtil.isNotEmpty(ids)){
            query.where(qSsStreamChannel.id.in(ids));
        }
        if (ObjectUtil.isNotEmpty(companyType)) {
            query.where(qSsStreamChannel.companyType.eq(companyType));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsStreamChannel.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    /**
     * 创建直播流频道
     *
     * @param companyType
     * @param req
     * @return
     */
    @Transactional
    public BaseResult<ContentErrorCode> creatStreamChannel(Integer companyType, EditStreamChannelReq req) {

        SsStreamChannel fetchOne = queryFactory.select(qSsStreamChannel)
                .from(qSsStreamChannel)
                .where(qSsStreamChannel.number.eq(req.getNumber())).fetchOne();
        if (ObjectUtil.isNotEmpty(fetchOne)) {
            return BaseResult.of(ContentErrorCode.NUMBER_NAME_EXIST);
        }
        SsStreamChannel channel = SsStreamChannel.builder()
                .number(req.getNumber())
                .name(req.getName())
                .showName(req.getShowName())
                .bannerUrl(req.getBannerUrl())
                .appCoverUrl(req.getAppCoverUrl())
                .h5CoverUrl(req.getH5CoverUrl())
                .introduce(req.getIntroduce())
                .enabled(true)
                .companyType(companyType)
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now())
                .build();
        streamChannelRepository.save(channel);
        log.info("creat StreamChannel success, StreamChannel: {}", channel);
        return BaseResult.success(ContentErrorCode.SUCCESS);
    }

    public List<ServiceIntroduction> getStreamChannelServiceIntroductionList(Integer companyType, Collection<String> serviceNumbers) {
        if (ObjectUtils.isEmpty(serviceNumbers)) {
            return Collections.emptyList();
        }

        List<SsStreamChannel> streamChannelList = queryFactory.selectFrom(qSsStreamChannel)
                .where(qSsStreamChannel.number.in(serviceNumbers))
                .where(qSsStreamChannel.companyType.eq(companyType))
                .fetch();

        List<Long> channelIds = streamChannelList.stream().map(SsStreamChannel::getId).collect(Collectors.toList());
        List<ChannelAnalystInfo> analystList = getStreamChannelAnalystList(channelIds);
        Map<Long, List<ChannelAnalystInfo>> analystMap = analystList.stream()
                .collect(Collectors.groupingBy(ChannelAnalystInfo::getChannelId));

        return streamChannelList.stream().map(e -> {
                    Long id = e.getId();
                    List<ChannelAnalystInfo> analystInfos = analystMap.getOrDefault(id, Collections.emptyList());
                    List<String> analysts = analystInfos.stream().map(a -> a.getAnalystInfo().getName())
                            .collect(Collectors.toList());
                    Map<String, Object> properties = new HashMap<>();
                    properties.put("bannerUrl", e.getBannerUrl());
                    properties.put("introduce", e.getIntroduce());
                    properties.put("title", e.getShowName());
                    properties.put("analysts", analysts);
                    return ServiceIntroduction.builder()
                            .serviceId(e.getId())
                            .number(e.getNumber())
                            .serviceName(e.getName())
                            .name(e.getShowName())
                            .type(STREAM)
                            .properties(properties)
                            .build();
                })
                .collect(Collectors.toList());
    }

    /**
     * 更新直播流频道
     * @param req
     * @return
     */
    @Transactional
    public BaseResult<ContentErrorCode> updateStreamChannel(EditStreamChannelReq req) {
        if (ObjectUtil.isEmpty(req.getChannelId())){
            return BaseResult.of(ContentErrorCode.ID_NOT_NULL);
        }
        JPAUpdateClause updateClause = queryFactory.update(qSsStreamChannel)
                .where(qSsStreamChannel.id.eq(req.getChannelId()));
        if (ObjectUtil.isNotEmpty(req.getName())){
            updateClause.set(qSsStreamChannel.name, req.getName());
        }
        if (ObjectUtil.isNotEmpty(req.getShowName())){
            updateClause.set(qSsStreamChannel.showName, req.getShowName());
        }
        if (ObjectUtil.isNotEmpty(req.getBannerUrl())){
            updateClause.set(qSsStreamChannel.bannerUrl, req.getBannerUrl());
        }
        if (ObjectUtil.isNotEmpty(req.getAppCoverUrl())) {
            updateClause.set(qSsStreamChannel.appCoverUrl, req.getAppCoverUrl());
        }
        if (ObjectUtil.isNotEmpty(req.getH5CoverUrl())) {
            updateClause.set(qSsStreamChannel.h5CoverUrl, req.getH5CoverUrl());
        }
        if (ObjectUtil.isNotEmpty(req.getIntroduce())){
            updateClause.set(qSsStreamChannel.introduce, req.getIntroduce());
        }
        updateClause.set(qSsStreamChannel.gmtModified, Instant.now()).execute();
        log.info("update StreamChannel success, StreamChannel: {}",req);
        return BaseResult.success(ContentErrorCode.SUCCESS);
    }

    /**
     * 为直播流频道配置老师
     * @param req
     * @return
     */
    @Transactional
    public BaseResult addAnalyst(StreamChannelAnalystReq req) {
        final Long channelId = req.getChannelId();
        List<SsStreamChannelAnalystRelation> relations = req.getAnalystId().stream().map(analystId -> {
            return SsStreamChannelAnalystRelation.builder()
                    .analystId(analystId)
                    .channelId(channelId)
                    .gmtCreate(Instant.now())
                    .gmtModified(Instant.now())
                    .enabled(true)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建老师关系成功,SsStreamChannelAnalystRelation {}", relations);
        return BaseResult.success();
    }

    /**
     * 删除直播流老师
     * @param ids 直播流Id
     * @return
     */
    @Transactional
    public BaseResult deleteAnalystRelation(List<Long> ids) {
        long execute = queryFactory.delete(analystRelation)
                .where(analystRelation.id.in(ids)).execute();
        log.info("删除投顾老师关系成功,delete {}", execute);
        return BaseResult.success();
    }

    /**
     * 获取直播流老师关系
     * @param channelId
     * @return
     */
    public BaseResult<List<SsStreamChannelAnalystRelation>> getAnalystRelations(Long channelId) {
        JPAQuery<SsStreamChannelAnalystRelation> query = queryFactory.select(analystRelation)
                .from(analystRelation)
                .where(analystRelation.channelId.eq(channelId));
        return BaseResult.success(query.fetch());
    }

    public List<SsAnalystInfo> getStreamChannelAnalystList(Long channelId) {
        return queryFactory.selectFrom(qSsAnalystInfo)
                .leftJoin(analystRelation)
                .on(analystRelation.analystId.eq(qSsAnalystInfo.id))
                .where(analystRelation.channelId.eq(channelId))
                .where(analystRelation.enabled.eq(true))
                .fetch();
    }

    public List<ChannelAnalystInfo> getStreamChannelAnalystList(Collection<Long> channelIds) {
        return queryFactory.select(Projections.bean(ChannelAnalystInfo.class,
                        analystRelation.channelId,
                        qSsAnalystInfo.as("analystInfo")))
                .from(qSsAnalystInfo)
                .leftJoin(analystRelation)
                .on(analystRelation.analystId.eq(qSsAnalystInfo.id))
                .where(analystRelation.channelId.in(channelIds))
                .where(analystRelation.enabled.eq(true))
                .fetch();
    }

    /**
     * 为直播流添加处理人
     * @param req
     * @return
     */
    @Transactional
    public BaseResult addManager(StreamServiceManagerReq req) {
        final Long channelId = req.getChannelId();
        List<SsStreamChannelOperatorRelation> relations = req.getOperatorId().stream().map(operatorId -> {
            return SsStreamChannelOperatorRelation.builder()
                    .operatorId(operatorId)
                    .channelId(channelId)
                    .gmtCreate(Instant.now())
                    .gmtModified(Instant.now())
                    .enabled(true)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建处理人关系成功,SsStreamChannelOperatorRelation {}", relations);
        return BaseResult.success();
    }

    /**
     * 获取直播流对映处理人关系
     * @param channelId     直播流Id
     * @param operatorId    处理人Id
     * @return
     */
    public BaseResult<List<SsStreamChannelOperatorRelation>> getManagerRelations(Long channelId, Integer operatorId) {
        JPAQuery<SsStreamChannelOperatorRelation> query = queryFactory.select(operatorRelation)
                .from(operatorRelation);
        if (ObjectUtil.isNotEmpty(channelId)){
            query.where(operatorRelation.channelId.eq(channelId));
        }
        if (ObjectUtil.isNotEmpty(operatorId)){
            query.where(operatorRelation.operatorId.eq(operatorId));
        }
        return BaseResult.success(query.fetch());
    }

    /**
     * 删除直播流处理人关系
     * @param ids   需要删除的关系id
     * @return
     */
    @Transactional
    public BaseResult deleteManagerRelation(List<Long> ids) {
        long execute = queryFactory.delete(operatorRelation)
                .where(operatorRelation.id.in(ids)).execute();
        log.info("删除投顾老师关系成功,delete {}", execute);
        return BaseResult.success();
    }

    public List<SsStreamChannel> getChannelList(Integer userId, Integer companyType){
        return queryFactory.select(qSsStreamChannel)
                .from(qSsStreamChannel)
                .leftJoin(operatorRelation)
                .on(operatorRelation.channelId.eq(qSsStreamChannel.id))
                .where(qSsStreamChannel.enabled.eq(true))
                .where(operatorRelation.operatorId.eq(userId))
                .where(qSsStreamChannel.companyType.eq(companyType))
                .fetch();
    }
}

package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.BaseConfig;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.entity.SsTextScanResult;
import cn.shrise.radium.contentservice.properties.GreenLabelProperties;
import cn.shrise.radium.contentservice.req.GreenTextScanReq;
import cn.shrise.radium.contentservice.req.TextChatAuditReq;
import cn.shrise.radium.contentservice.req.TextModerationReq;
import cn.shrise.radium.contentservice.resp.GreenTextScanResp;
import cn.shrise.radium.contentservice.resp.ModerationLabelItem;
import cn.shrise.radium.contentservice.resp.TextChatAuditResp;
import cn.shrise.radium.contentservice.resp.TextModerationResp;
import cn.shrise.radium.contentservice.service.SsTextScanResultService;
import cn.shrise.radium.contentservice.service.green.GreenService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("green")
@RequiredArgsConstructor
public class GreenController {

    private final GreenService greenService;

    private final SsTextScanResultService textScanResultService;
    private final GreenLabelProperties greenLabelProperties;

    @PostMapping("text/scan")
    @ApiOperation("文本内容安全检测(阿里云检测服务1.0)")
    public BaseResult<GreenTextScanResp> textScan(@RequestBody GreenTextScanReq greenTextScanReq) {
        GreenTextScanResp textScanResp = greenService.textScan(greenTextScanReq);
        return BaseResult.success(textScanResp);
    }

    @PostMapping("text/moderation")
    @ApiOperation("文本内容安全检测(阿里云文本审核增强版)")
    public BaseResult<TextModerationResp> textModeration(@RequestBody TextModerationReq textModerationReq) {
        TextModerationResp textModerationResp = greenService.textModeration(textModerationReq);
        return BaseResult.success(textModerationResp);
    }

    @PostMapping("batch")
    @ApiOperation("内容安全根据id批量查询map")
    public BaseResult<Map<Long, SsTextScanResult>> batchGetResultMap(@RequestBody BatchReq<Long> req) {
        List<SsTextScanResult> resultList = textScanResultService.getTextScanResultList(req.getValues());
        final Map<Long, SsTextScanResult> resultMap = resultList.stream()
                .collect(Collectors.toMap(SsTextScanResult::getId, Function.identity()));
        return BaseResult.success(resultMap);
    }

    @GetMapping("labels")
    @ApiOperation("获取内容安全审核结果标签")
    public BaseResult<List<ModerationLabelItem>> getModerationLabels() {
        List<GreenLabelProperties.Label> labels = greenLabelProperties.getLabels();
        List<ModerationLabelItem> items = labels.stream()
                .map(e -> new ModerationLabelItem(e.getName(), e.getLabel()))
                .collect(Collectors.toList());
        return BaseResult.success(items);
    }

    @PostMapping("text/chat/audit")
    @ApiOperation("文本内容安全检测(企微聊天记录专用)")
    public BaseResult<TextChatAuditResp> textChatAudit(@RequestBody TextChatAuditReq req) {
        TextChatAuditResp resp = greenService.textChatAudit(req);
        return BaseResult.success(resp);
    }
}

package cn.shrise.radium.contentservice.consumer;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.contentservice.constant.AvatarVideoTaskStatusConstant;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.dao.SsAvatarTaskDao;
import cn.shrise.radium.contentservice.properties.AvatarConfigProperty;
import cn.shrise.radium.contentservice.resp.AvatarVideoTaskResp;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Locale;

@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = ContentServiceConst.CONTENT_TOPIC, consumerGroup = ContentServiceConst.MqGroupType.GID_AVATAR_VIDEO_TASK_NOTIFY,
        selectorType = ExpressionType.TAG, selectorExpression = ContentServiceConst.MqTagType.AVATAR_VIDEO_TASK_NOTIFY)
class AvatarVideoTaskNotifyConsumer implements MessageListener {

    private final AvatarConfigProperty avatarConfigProperty;
    private final SsAvatarTaskDao ssAvatarTaskDao;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        AvatarVideoTaskResp resp = JSON.parseObject(new String(message.getBody()), AvatarVideoTaskResp.class);
        if (ObjectUtils.isNotEmpty(resp)) {
            log.info("AvatarVideoTaskNotify resp: {}", resp);
            String tenantId = avatarConfigProperty.getTenantId();
            String authKey = avatarConfigProperty.getAuthKey();
            String checkStr = tenantId + "|" + resp.getTimestamp() + "|" + authKey;
            String checkSign = DigestUtils.md5DigestAsHex(checkStr.getBytes(StandardCharsets.UTF_8)).toLowerCase(Locale.ROOT);
            if (checkSign.equals(resp.getSign())) {
                if (resp.getEType().equals("VIDEO_START")) {
                    // 视频合成任务开始执行
                    ssAvatarTaskDao.updateAvatarTask(resp.getUuid(), AvatarVideoTaskStatusConstant.PROCESSING);
                } else if (resp.getEType().equals("VIDEO_END")) {
                    //视频合成任务执行成功
                    Integer status = resp.getSuccess() ? AvatarVideoTaskStatusConstant.SUCCESS : AvatarVideoTaskStatusConstant.FAIL;
                    ssAvatarTaskDao.updateAvatarTask(resp.getUuid(), status);
                } else {
                    log.info("AvatarVideoTaskNotify 类型错误 eType:{}", resp.getEType());
                }
            } else {
                log.info("AvatarVideoTaskNotify 签名错误 sign: {} checkSign: {}", resp.getSign(), checkSign);
            }
        }
        return Action.CommitMessage;
    }
}

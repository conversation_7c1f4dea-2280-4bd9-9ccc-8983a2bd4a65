package cn.shrise.radium.contentservice.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.JsonUtils;
import cn.shrise.radium.contentservice.dao.SsCustomerComplaintDao;
import cn.shrise.radium.contentservice.entity.QSsCustomerComplaint;
import cn.shrise.radium.contentservice.entity.SsCustomerComplaint;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.repository.SsCustomerComplaintRepository;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CustomerComplaintService {

    private final JPAQueryFactory queryFactory;
    private final QSsCustomerComplaint qSsCustomerComplaint = QSsCustomerComplaint.ssCustomerComplaint;
    private final SsCustomerComplaintRepository ssCustomerComplaintRepository;
    private final SsCustomerComplaintDao ssCustomerComplaintDao;

    public PageResult<List<SsCustomerComplaint>> getCustomerComplaintList(Boolean isHandle, String source, Integer current, Integer size) {

        JPAQuery<SsCustomerComplaint> query = queryFactory.select(qSsCustomerComplaint)
                .from(qSsCustomerComplaint)
                .where(qSsCustomerComplaint.isHandle.eq(isHandle));
        if (ObjectUtil.isNotEmpty(source)) {
            query.where(qSsCustomerComplaint.source.eq(source));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsCustomerComplaint.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(Long id, Integer wxId, Integer userId, String content, List<String> images, Integer auditId, String source, String handleResult) {
        if (id == null) {
            Integer count = ssCustomerComplaintDao.getUserComplaintCountMonthly(userId, wxId, DateUtil.beginOfMonth(new Date()).toInstant());
            if (count >= 4) {
                throw new BusinessException(ContentErrorCode.COMPLAINT_OVER_LIMIT);
            }
            SsCustomerComplaint build = SsCustomerComplaint.builder()
                    .wxId(wxId)
                    .userId(userId)
                    .content(content)
                    .isHandle(false)
                    .source(source)
                    .build();
            if (ObjectUtil.isNotEmpty(images)) {
                String imageList = JsonUtils.toJson(images);
                build.setImageUrls(imageList);
            }
            ssCustomerComplaintRepository.save(build);
        } else {
            queryFactory.update(qSsCustomerComplaint)
                    .where(qSsCustomerComplaint.id.eq(id))
                    .set(qSsCustomerComplaint.isHandle, true)
                    .set(qSsCustomerComplaint.auditId, auditId)
                    .set(qSsCustomerComplaint.handleResult, handleResult)
                    .execute();
        }
    }

    public BaseResult<SsCustomerComplaint> getCustomerComplaintInfo(Long id) {
        JPAQuery<SsCustomerComplaint> query = queryFactory.select(qSsCustomerComplaint)
                .from(qSsCustomerComplaint)
                .where(qSsCustomerComplaint.id.eq(id));
        return BaseResult.success(query.fetchOne());
    }
}

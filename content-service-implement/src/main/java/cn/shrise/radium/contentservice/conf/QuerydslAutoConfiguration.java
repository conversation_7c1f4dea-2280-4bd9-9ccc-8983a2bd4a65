package cn.shrise.radium.contentservice.conf;

import com.blazebit.persistence.Criteria;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;

/**
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class QuerydslAutoConfiguration {

    @Bean(name = "queryFactory")
    @Autowired
    public JPAQueryFactory jpaQueryPrimary(@Qualifier("entityManagerFactoryPrimary") EntityManager entityManager) {
        return new JPAQueryFactory(entityManager);
    }


    @Bean(name = "jpaQueryTertiary")
    @Autowired
    public JPAQueryFactory jpaQueryTertiary(@Qualifier("entityManagerFactoryTertiary") EntityManager entityManager) {
        return new JPAQueryFactory(entityManager);
    }

    @Bean(name = "jpaQuerySecondary")
    @Autowired
    public JPAQueryFactory jpaQuerySecondary(@Qualifier("entityManagerFactorySecondary") EntityManager entityManager) {
        return new JPAQueryFactory(entityManager);
    }

    @Bean
    public CriteriaBuilderFactory criteriaBuilderFactory(
            EntityManagerFactory entityManagerFactory) {
        return Criteria.getDefault().createCriteriaBuilderFactory(entityManagerFactory);
    }
}

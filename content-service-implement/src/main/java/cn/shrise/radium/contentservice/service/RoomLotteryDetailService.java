package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.contentservice.dto.RoomLotteryDetailDto;
import cn.shrise.radium.contentservice.dto.SsRoomLotteryDetailDto;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.enums.RoomLotteryOperateRecord;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.repository.RoomLotteryDetailRepository;
import cn.shrise.radium.contentservice.repository.RoomLotteryOperateRecordRepository;
import cn.shrise.radium.contentservice.req.CreateParticipateLotteryReq;
import cn.shrise.radium.contentservice.resp.LotteryDetailResp;
import cn.shrise.radium.contentservice.resp.LotteryWinnerResp;
import cn.shrise.radium.contentservice.resp.MyLotteryPrizeResp;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.RocketMQ5Constant.*;
import static cn.shrise.radium.contentservice.enums.RoomLotteryTypeEnum.SPECIAL_LOTTERY;

@Service
@RequiredArgsConstructor
public class RoomLotteryDetailService {

    private final JPAQueryFactory queryFactory;
    private final QSsRoomLotteryDetail detail = QSsRoomLotteryDetail.ssRoomLotteryDetail;
    private final QSsRoomLottery lottery = QSsRoomLottery.ssRoomLottery;
    private final QSsRoomPrize prize = QSsRoomPrize.ssRoomPrize;
    private final QSsLiveRoom room = QSsLiveRoom.ssLiveRoom;
    private final RoomLotteryOperateRecordRepository roomLotteryOperateRecordRepository;
    private final RoomPrizeService roomPrizeService;
    private final RoomLotteryService roomLotteryService;
    private final RoomLotteryDetailRepository roomLotteryDetailRepository;
    private final RocketMqUtils rocketMqUtils;

    public Page<SsRoomLotteryDetailDto> getRoomLotteryDetailList(Pageable pageable, Integer companyType, Integer relationServerId, Integer salesId, Long roomId, Long sceneId, Long lotteryId, Long prizeId, List<Integer> customerList, Boolean isWin, Boolean isHandled) {
        JPAQuery<Tuple> query = queryFactory.select(detail, lottery, prize).from(detail)
                .leftJoin(lottery).on(lottery.id.eq(detail.lotteryId))
                .leftJoin(prize).on(prize.id.eq(detail.prizeId))
                .where(detail.companyType.eq(companyType));
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        JPAQuery<Long> queryCount = queryFactory.select(countTemp).from(detail)
                .leftJoin(lottery).on(lottery.id.eq(detail.lotteryId))
                .where(detail.companyType.eq(companyType));

        if (!ObjectUtils.isEmpty(lotteryId)) {
            query.where(detail.lotteryId.eq(lotteryId));
            queryCount.where(detail.lotteryId.eq(lotteryId));
        }
        if (!ObjectUtils.isEmpty(prizeId)) {
            query.where(detail.prizeId.eq(prizeId));
            queryCount.where(detail.prizeId.eq(prizeId));
        }
        if (!ObjectUtils.isEmpty(customerList)) {
            query.where(detail.userId.in(customerList));
            queryCount.where(detail.userId.in(customerList));
        }
        if (!ObjectUtils.isEmpty(salesId)) {
            query.where(detail.salesId.eq(salesId));
            queryCount.where(detail.salesId.eq(salesId));
        }
        if (!ObjectUtils.isEmpty(roomId)) {
            query.where(lottery.roomId.eq(roomId));
            queryCount.where(lottery.roomId.eq(roomId));
        }
        if (!ObjectUtils.isEmpty(sceneId)) {
            query.where(lottery.sceneId.eq(sceneId));
            queryCount.where(lottery.sceneId.eq(sceneId));
        }
        if (isWin != null) {
            query.where(detail.isWin.eq(isWin));
            queryCount.where(detail.isWin.eq(isWin));
        }
        if (isHandled != null) {
            query.where(detail.isHandled.eq(isHandled));
            queryCount.where(detail.isHandled.eq(isHandled));
        }

        List<Tuple> res = query.orderBy(detail.id.desc()).offset(pageable.getOffset())
                .limit(pageable.getPageSize()).fetch();
        Long count = queryCount.fetchOne();
        List<SsRoomLotteryDetailDto> details = res.stream().map(item ->
                SsRoomLotteryDetailDto.builder()
                        .detail(item.get(detail))
                        .lottery(item.get(lottery))
                        .prize(item.get(prize))
                        .build()
        ).collect(Collectors.toList());

        return new PageImpl<>(details, pageable, count);
    }

    @Transactional(rollbackFor = Exception.class)
    public void drawLottery(@NonNull Long lotteryId, @NonNull Collection<Integer> userIdList, @NonNull Long prizeId) {
        // 修改 活动 状态为已开奖
        queryFactory.update(lottery).set(lottery.status, RoomLotteryType.DRAW_3.getValue()).where(lottery.id.eq(lotteryId)).execute();
        // 修改中奖信息
        queryFactory.update(detail)
                .set(detail.isWin, true).set(detail.prizeId, prizeId)
                .where(detail.lotteryId.eq(lotteryId), detail.userId.in(userIdList)).execute();
        // 存记录
        SsRoomLotteryOperateRecord record = SsRoomLotteryOperateRecord.builder()
                .lotteryId(lotteryId)
                .operateType(RoomLotteryOperateRecord.SYSTEM_EDIT_LOTTERY.getValue())
                .build();
        roomLotteryOperateRecordRepository.save(record);
    }

    @Transactional(rollbackFor = Exception.class)
    public void markHandled(Long detailId, String mobile, Long mobileId, String region, String address, String name) {
        queryFactory.update(detail)
                .set(detail.isHandled, true)
                .set(detail.mobile, mobile)
                .set(detail.mobileId, mobileId)
                .set(detail.region, region)
                .set(detail.address, address)
                .set(detail.name, name)
                .where(detail.id.eq(detailId))
                .where(detail.isHandled.eq(false)).execute();
    }

    public LotteryDetailResp getLotteryDetail(Long lotteryId, Integer userId) {
        SsRoomLottery roomLottery = queryFactory.selectFrom(lottery).where(lottery.id.eq(lotteryId)).fetchOne();
        SsRoomPrize roomPrize = null;
        if (ObjectUtil.isNotNull(roomLottery.getPrizeId())) {
            roomPrize = roomPrizeService.findRoomPrize(roomLottery.getPrizeId());
        }

        SsRoomLotteryDetail lotteryDetail;
        if (userId == null) {
            lotteryDetail = null;
        } else {
            lotteryDetail = queryFactory.selectFrom(detail)
                    .where(detail.lotteryId.eq(lotteryId))
                    .where(detail.userId.eq(userId)).fetchOne();
        }
        if (ObjectUtil.equals(roomLottery.getType(), SPECIAL_LOTTERY.getValue())
                && ObjectUtil.isNotNull(lotteryDetail) && ObjectUtil.isNotEmpty(lotteryDetail.getPrizeId())) {
            roomPrize = roomPrizeService.findRoomPrize(lotteryDetail.getPrizeId());
        }
        return LotteryDetailResp.of(roomLottery, roomPrize, lotteryDetail);
    }

    public PageResult<List<MyLotteryPrizeResp>> findMyPrizeList(Integer userId, Integer current, Integer size) {
        JPAQuery<MyLotteryPrizeResp> query = queryFactory.select(
                        Projections.bean(MyLotteryPrizeResp.class,
                                detail.id.as("detailId"),
                                detail.userId,
                                detail.joinTime,
                                detail.isHandled,
                                detail.mobile,
                                detail.region,
                                detail.address,
                                detail.name,
                                prize.name.as("prizeName"),
                                prize.imageUrl.as("prizeImageUrl")
                        ))
                .from(detail)
                .leftJoin(prize)
                .on(detail.prizeId.eq(prize.id))
                .where(detail.userId.eq(userId))
                .where(detail.isWin.eq(true));

        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();

        query.orderBy(detail.joinTime.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        List<MyLotteryPrizeResp> records = query.fetch();
        return PageResult.success(records, Pagination.of(current, size, total));
    }

    public MyLotteryPrizeResp findOnePrize(Long detailId) {
        return queryFactory.select(
                        Projections.bean(MyLotteryPrizeResp.class,
                                detail.id,
                                detail.userId,
                                detail.joinTime,
                                detail.isHandled,
                                detail.mobile,
                                detail.region,
                                detail.address,
                                detail.name,
                                prize.name.as("prizeName"),
                                prize.imageUrl.as("prizeImageUrl")
                        ))
                .from(detail)
                .leftJoin(prize)
                .on(detail.prizeId.eq(prize.id))
                .where(detail.id.eq(detailId))
                .fetchOne();
    }

    @Transactional
    public void createOne(CreateParticipateLotteryReq req) {
        SsRoomLottery lottery = roomLotteryService.findOne(req.getLotteryId());
        if (ObjectUtil.isNull(lottery)) {
            throw new BusinessException(ContentErrorCode.LOTTERY_NOT_EXISTED);
        }
        if (!ObjectUtil.equals(lottery.getStatus(), RoomLotteryType.OPEN_2.getValue())) {
            throw new BusinessException(ContentErrorCode.LOTTERY_NOT_OPEN);
        }
        if (lottery.getStartTime().plus(lottery.getDuration(), ChronoUnit.MINUTES).isBefore(Instant.now())) {
            throw new BusinessException(ContentErrorCode.LOTTERY_CLOSED);
        }
        SsRoomLotteryDetail lotteryDetail = queryFactory.selectFrom(detail)
                .where(detail.lotteryId.eq(req.getLotteryId()))
                .where(detail.userId.eq(req.getUserId()))
                .fetchOne();
        if (ObjectUtil.isNotNull(lotteryDetail)) {
            throw new BusinessException(ContentErrorCode.LOTTERY_IS_JOINED);
        }
        SsRoomLotteryDetail.SsRoomLotteryDetailBuilder builder = SsRoomLotteryDetail.builder()
                .companyType(req.getCompanyType()).lotteryId(req.getLotteryId())
                .userId(req.getUserId()).salesId(req.getSalesId()).joinTime(Instant.now())
                .isHandled(false).isWin(false)
                .prizeId(lottery.getPrizeId());
        //.build();
        if (ObjectUtil.equals(lottery.getType(), SPECIAL_LOTTERY.getValue())) {
            builder.prizeId(null);
        }
        SsRoomLotteryDetail roomLotteryDetail = roomLotteryDetailRepository.save(builder.build());
        /*if (ObjectUtil.equals(lottery.getType(), SPECIAL_LOTTERY.getValue())) {
            RoomLotteryDetailDto build = RoomLotteryDetailDto.builder()
                    .lottery(lottery)
                    .detail(roomLotteryDetail)
                    .build();
            rocketMqUtils.convertAndSendOrderly(TOPIC_CONTENT_ORDERLY, TAG_CONTENT_LOTTERY_JOIN, build);
        }*/
    }

    public PageResult<List<LotteryWinnerResp>> getLotteryWinnerList(Long lotteryId, Integer current, Integer size) {
        JPAQuery<LotteryWinnerResp> query = queryFactory.select(
                        Projections.bean(LotteryWinnerResp.class,
                                detail.lotteryId,
                                detail.userId
                        ))
                .from(detail)
                .where(detail.lotteryId.eq(lotteryId))
                .where(detail.isWin.eq(true));

        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();

        query.orderBy(detail.joinTime.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }
}

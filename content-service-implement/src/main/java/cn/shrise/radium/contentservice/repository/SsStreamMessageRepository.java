package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsStreamMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface SsStreamMessageRepository extends JpaRepository<SsStreamMessage, Long>,
        QuerydslPredicateExecutor<SsStreamMessage> {
}

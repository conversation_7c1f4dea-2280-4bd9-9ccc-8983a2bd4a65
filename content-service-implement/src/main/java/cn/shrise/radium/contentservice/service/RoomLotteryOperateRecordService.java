package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.contentservice.entity.QSsRoomLottery;
import cn.shrise.radium.contentservice.entity.QSsRoomLotteryOperateRecord;
import cn.shrise.radium.contentservice.resp.RoomLotteryOperateResp;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RoomLotteryOperateRecordService {

    private final JPAQueryFactory queryFactory;

    public List<RoomLotteryOperateResp> getRoomLotteryOperateRecord(Long lotteryId) {
        QSsRoomLotteryOperateRecord ssRoomLotteryOperateRecord = QSsRoomLotteryOperateRecord.ssRoomLotteryOperateRecord;
        QSsRoomLottery ssRoomLottery = QSsRoomLottery.ssRoomLottery;
        List<Tuple> fetch = queryFactory.select(ssRoomLotteryOperateRecord.lotteryId, ssRoomLotteryOperateRecord.operatorId, ssRoomLotteryOperateRecord.operateType, ssRoomLottery.name, ssRoomLotteryOperateRecord.gmtCreate, ssRoomLottery.status, ssRoomLottery.number, ssRoomLottery.type)
                .from(ssRoomLotteryOperateRecord)
                .leftJoin(ssRoomLottery)
                .on(ssRoomLottery.id.eq(ssRoomLotteryOperateRecord.lotteryId))
                .where(ssRoomLotteryOperateRecord.lotteryId.eq(lotteryId))
                .orderBy(ssRoomLotteryOperateRecord.gmtCreate.asc())
                .fetch();
        List<RoomLotteryOperateResp> roomLotteryOperateRespList = new ArrayList<>();
        fetch.forEach(e -> {
            RoomLotteryOperateResp roomLotteryOperateResp = new RoomLotteryOperateResp(
                    e.get(ssRoomLotteryOperateRecord.lotteryId),
                    e.get(ssRoomLotteryOperateRecord.operatorId),
                    e.get(ssRoomLotteryOperateRecord.operateType),
                    e.get(ssRoomLottery.name),
                    e.get(ssRoomLotteryOperateRecord.gmtCreate),
                    e.get(ssRoomLottery.status),
                    e.get(ssRoomLottery.number),
                    e.get(ssRoomLottery.type)
            );
            roomLotteryOperateRespList.add(roomLotteryOperateResp);
        });
        return roomLotteryOperateRespList;
    }
}

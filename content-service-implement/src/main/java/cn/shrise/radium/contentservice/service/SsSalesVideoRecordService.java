package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.entity.QSsSalesVideoRecord;
import cn.shrise.radium.contentservice.entity.SsSalesVideoRecord;
import cn.shrise.radium.contentservice.repository.SsSalesVideoRecordRepository;
import cn.shrise.radium.contentservice.req.CreateSsSalesVideoRecordReq;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;


@Service
@RequiredArgsConstructor
public class SsSalesVideoRecordService {
    private final JPAQueryFactory queryFactory;
    private final QSsSalesVideoRecord qSsSalesVideoRecord=QSsSalesVideoRecord.ssSalesVideoRecord;
    private final SsSalesVideoRecordRepository videoRepository;

    public Page<SsSalesVideoRecord> getSalesVideoRecord(Integer companyType,
                                                        Integer creatorId,
                                                        String title,
                                                        Integer current,
                                                        Integer size){

        JPAQuery<SsSalesVideoRecord> query = queryFactory.selectFrom(qSsSalesVideoRecord).where(qSsSalesVideoRecord.videoType.eq("video_channel"));
        BooleanBuilder booleanBuilder=new BooleanBuilder();
        if(ObjectUtils.isNotEmpty(companyType)){
            booleanBuilder.and(qSsSalesVideoRecord.companyType.eq(companyType));
        }
        if(ObjectUtils.isNotEmpty(creatorId)){
            booleanBuilder.and(qSsSalesVideoRecord.creatorId.eq(creatorId));
        }
        if(ObjectUtils.isNotEmpty(title)){
            booleanBuilder.and(qSsSalesVideoRecord.title.like("%"+title+"%"));
        }
        NumberTemplate<Long> count= Expressions.numberTemplate(Long.class,"COUNT(*)");
        Long total=query.clone().where(booleanBuilder).select(count).fetchOne();
        if(ObjectUtils.allNotNull(current,size)){
            query.offset((long) (current-1)*size).limit(size);
        }
        query.where(booleanBuilder).orderBy(qSsSalesVideoRecord.gmtCreate.desc());
        return new PageImpl<>(query.fetch(), PageRequest.of(current-1,size),total==null?0L:total);
    }

    public Page<SsSalesVideoRecord> getSalesVideoByFilter(Integer companyType, String videoType,
                                                          List<Integer> creatorIdList,
                                                          String title, Boolean enabled, Instant startTime,
                                                          Instant endTime, Integer current, Integer size) {
        JPAQuery<SsSalesVideoRecord> query = queryFactory.selectFrom(qSsSalesVideoRecord)
                .where(qSsSalesVideoRecord.companyType.eq(companyType));
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (ObjectUtil.isNotNull(videoType)) {
            booleanBuilder.and(qSsSalesVideoRecord.videoType.eq(videoType));
        }
        if (ObjectUtil.isNotNull(creatorIdList)) {
            booleanBuilder.and(qSsSalesVideoRecord.creatorId.in(creatorIdList));
        }
        if (ObjectUtil.isNotNull(title)) {
            booleanBuilder.and(qSsSalesVideoRecord.title.like("%" + title + "%"));
        }
        if (ObjectUtil.isNotNull(enabled)) {
            booleanBuilder.and(qSsSalesVideoRecord.enabled.eq(enabled));
        }
        if (ObjectUtil.isNotNull(startTime)) {
            booleanBuilder.and(qSsSalesVideoRecord.gmtCreate.goe(startTime));
        }
        if (ObjectUtil.isNotNull(endTime)) {
            booleanBuilder.and(qSsSalesVideoRecord.gmtCreate.lt(endTime));
        }
        NumberTemplate<Long> count= Expressions.numberTemplate(Long.class,"COUNT(*)");
        Long total = query.clone().select(count).where(booleanBuilder).fetchOne();
        if(ObjectUtil.isAllNotEmpty(current, size)){
            query.offset((long) (current - 1) * size).limit(size);
        }
        query.where(booleanBuilder).orderBy(qSsSalesVideoRecord.id.desc());
        return new PageImpl<>(query.fetch(), PageRequest.of(current - 1, size), total==null?0L:total);
    }

    @Transactional
    public void createOne(CreateSsSalesVideoRecordReq req) {
        SsSalesVideoRecord record = new SsSalesVideoRecord();
        BeanUtils.copyProperties(req, record);
        record.setEnabled(false);
        videoRepository.save(record);
    }

    @Transactional
    public void enableOne(String videoId) {
        queryFactory.update(qSsSalesVideoRecord)
                .where(qSsSalesVideoRecord.videoId.eq(videoId))
                .set(qSsSalesVideoRecord.enabled, true)
                .execute();
    }
}

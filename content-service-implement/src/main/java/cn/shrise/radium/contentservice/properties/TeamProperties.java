package cn.shrise.radium.contentservice.properties;

import cn.shrise.radium.contentservice.resp.PublicConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "team")
public class TeamProperties {

    /**
     * 首页新闻tab中短视频/文章/解盘配置
     */
    public PublicConfig publicConfig;

    /**
     * app首页观大盘配置 内容主number
     */
    public String banner;
}

package cn.shrise.radium.contentservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "room")
public class RoomLotteryProperty {

    private Map<Long, List<String>> lotteries;
    private List<Prize> prizes;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Prize {
        private long id;
        private int count;
    }

}

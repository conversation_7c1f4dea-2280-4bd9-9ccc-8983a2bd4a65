package cn.shrise.radium.contentservice.controller.article;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsArticleComment;
import cn.shrise.radium.contentservice.req.CreateArticleCommentReq;
import cn.shrise.radium.contentservice.resp.CountStatistics;
import cn.shrise.radium.contentservice.service.article.ArticleCommentService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("article/comment")
@RequiredArgsConstructor
public class ArticleCommentController {

    private final ArticleCommentService articleCommentService;

    @GetMapping("page")
    @ApiOperation("文章评论列表")
    public PageResult<List<SsArticleComment>> getArticleCommentPage(
            @RequestParam @ApiParam("文章id") Long articleId,
            @RequestParam @ApiParam("是否已审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("评论状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return articleCommentService.getArticleCommentPage(articleId, startTime, endTime, audit, status, current, size);
    }

    @GetMapping()
    @ApiOperation("获取文章评论列表")
    public PageResult<List<SsArticleComment>> getArticleCommentList(
            @RequestParam @ApiParam("文章id") Long articleId,
            @RequestParam(required = false) @ApiParam("评论状态") Integer auditFlag,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size) {
        PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<SsArticleComment> page = articleCommentService.getArticleCommentList(articleId, auditFlag, pageRequest);
        return PageResult.success(page);
    }

    @PostMapping("statistics")
    @ApiOperation("统计文章评论数量")
    public BaseResult<List<CountStatistics>> getArticleCommentStatistics(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false) @ApiParam("枚举AuditStatusConstant") Integer auditFlag) {
        List<CountStatistics> statistics = articleCommentService.getArticleCommentStatistics(req.getValues(), auditFlag);
        return BaseResult.success(statistics);
    }

    @PostMapping("audit")
    @ApiOperation("审核评论")
    public BaseResult<Void> auditArticleComment(
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("评论id") Integer commentId,
            @RequestParam @ApiParam("审核状态") Integer status) {
        articleCommentService.auditArticleComment(commentId, status, auditorId);
        return BaseResult.successful();
    }

    @PostMapping
    @ApiOperation("发布评论")
    public BaseResult<SsArticleComment> createArticleComment(@RequestBody @Valid CreateArticleCommentReq req) {
        SsArticleComment articleComment = articleCommentService.createArticleComment(req);
        return BaseResult.success(articleComment);
    }
}

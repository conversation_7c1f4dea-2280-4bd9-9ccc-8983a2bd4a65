package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsOriginMaterialChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/6 15:19
 * @Desc:
 **/
@Repository
public interface SsOriginMaterialChannelRepository extends JpaRepository<SsOriginMaterialChannel, Long>, QuerydslPredicateExecutor<SsOriginMaterialChannel> {

    SsOriginMaterialChannel findByNumber(String number);
}

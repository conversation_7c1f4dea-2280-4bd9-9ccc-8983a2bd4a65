package cn.shrise.radium.contentservice.controller.article;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.contentservice.constant.ArticleStatusEnum;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.req.CreateArticleReq;
import cn.shrise.radium.contentservice.req.EditArticleReq;
import cn.shrise.radium.contentservice.req.UpdateArticleReq;
import cn.shrise.radium.contentservice.resp.ArticleInfoResp;
import cn.shrise.radium.contentservice.resp.CountStatistics;
import cn.shrise.radium.contentservice.resp.LikeStatistics;
import cn.shrise.radium.contentservice.service.article.ArticleLikeService;
import cn.shrise.radium.contentservice.service.article.ArticleOperateRecordService;
import cn.shrise.radium.contentservice.service.article.ArticleService;
import cn.shrise.radium.contentservice.service.article.ArticleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

@Api
@RestController
@RequestMapping("article")
@RequiredArgsConstructor
public class ArticleController {

    private final ArticleTypeService articleTypeService;
    private final ArticleService articleService;
    private final ArticleLikeService articleLikeService;
    private final CommonProperties commonProperties;
    private final ArticleOperateRecordService articleOperateRecordService;

    @PostMapping("list")
    @ApiOperation("免费文章列表")
    public PageResult<List<ArticleInfoResp>> getArticleList(
            @RequestParam @ApiParam("栏目编号逗号隔开") String typeNumbers,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size
    ) {
        Set<String> typeNumberSet = Arrays.stream(typeNumbers.split(",")).collect(Collectors.toSet());
        List<SsArticleType> resArticleTypes = articleTypeService.findAllByNumbers(companyType, typeNumberSet);
        List<Integer> typeIdList = resArticleTypes.stream().map(SsArticleType::getId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(typeIdList)) {
            return PageResult.success(null, Pagination.of(current, size, 0L));
        }
        PageResult<List<SsArticle>> result = articleService.findAllByFilter(typeIdList, Collections.singletonList(ArticleStatusEnum.AS_Released.getValue()), current, size);
        List<Long> articleIds = result.getData().stream().map(SsArticle::getId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(articleIds)) {
            return PageResult.success(null, Pagination.of(current, size, 0L));
        }
        Map<Long, Long> articleLikeMap = articleLikeService.getLikeCount(articleIds);

        List<Long> likeIds = new ArrayList<>();
        if (!ObjectUtil.isAllEmpty(userId, wxId)) {
            List<SsArticleLike> articleLikes = articleLikeService.findExistedByUser(articleIds, userId, wxId);
            likeIds = articleLikes.stream().map(SsArticleLike::getArticleId).collect(Collectors.toList());
        }
        List<Long> finalLikeIds = likeIds;
        List<ArticleInfoResp> respList = result.getData().stream().map(e -> {
            ArticleInfoResp resp = new ArticleInfoResp();
            BeanUtil.copyProperties(e, resp);
            resp.setContent(null);
            resp.setLikeCount(articleLikeMap.getOrDefault(e.getId(), 0L));
            resp.setIsLike(finalLikeIds.contains(e.getId()));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }

    @GetMapping("info")
    @ApiOperation("获取文章详情")
    public BaseResult<ArticleInfoResp> getArticleInfo(
            @RequestParam @ApiParam("文章编号") String number,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId
    ) {
        SsArticle article = articleService.viewArticleInfo(number);
        if (ObjectUtils.isEmpty(article)) {
            throw new BusinessException("文章不存在");
        }
        ArticleInfoResp resp = new ArticleInfoResp();
        List<Long> likeIds = new ArrayList<>();
        Map<Long, Long> articleLikeMap = articleLikeService.getLikeCount(Collections.singletonList(article.getId()));
        if (!ObjectUtil.isAllEmpty(userId, wxId)) {
            List<SsArticleLike> articleLikes = articleLikeService.findExistedByUser(Collections.singletonList(article.getId()), userId, wxId);
            likeIds = articleLikes.stream().map(SsArticleLike::getArticleId).collect(Collectors.toList());
        }
        BeanUtil.copyProperties(article, resp);
        resp.setLikeCount(articleLikeMap.getOrDefault(article.getId(), 0L));
        resp.setIsLike(likeIds.contains(article.getId()));
        String articleUrl = ObjectUtil.isNotEmpty(article.getWxUrlNumber()) ? StrUtil.format("{}/s/{}", commonProperties.getWebApiUrl(), article.getWxUrlNumber()) : null;
        resp.setArticleUrl(articleUrl);
        SsArticleTypeRelation typeIdByArticleId = articleService.findTypeIdByArticleId(resp.getId());
        if (ObjectUtils.isNotEmpty(typeIdByArticleId)) {
            resp.setLastArticleInfoResp(articleService.findLastArticleByTypeId(resp.getId(), resp.getCreateTime(), typeIdByArticleId.getTypeId(), Collections.singletonList(ArticleStatusEnum.AS_Released.getValue())));
            resp.setNextArticleInfoResp(articleService.findNextArticleByTypeId(resp.getId(), resp.getCreateTime(), typeIdByArticleId.getTypeId(), Collections.singletonList(ArticleStatusEnum.AS_Released.getValue())));
        }
        return BaseResult.success(resp);
    }

    @PostMapping("like")
    @ApiOperation("文章点赞（取消）")
    public BaseResult<String> articleLike(
            @RequestParam @ApiParam("文章编号") String number,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId
    ) {
        SsArticle article = articleService.findOneByNumber(number);
        if (ObjectUtils.isEmpty(article)) {
            throw new BusinessException("文章不存在");
        }
        articleLikeService.processArticleLike(article.getId(), userId, wxId);
        return BaseResult.success();
    }

    @GetMapping("team/page")
    @ApiOperation("内容主文章列表")
    public PageResult<List<SsArticle>> getContentTeamArticlePage(
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("文章状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("状态") List<Integer> statusList,
            @RequestParam(required = false) @ApiParam("排序字段") String field,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return articleService.getContentTeamArticlePage(teamIds, companyType, status, startTime, endTime, statusList, field, isAsc, current, size);
    }

    @PostMapping("create")
    @ApiOperation("发布文章")
    public BaseResult<Void> createArticle(
            @RequestBody CreateArticleReq req) {
        SsArticle article = articleService.createArticle(req);
        return BaseResult.successful();
    }

    @PostMapping("update")
    @ApiOperation("编辑文章")
    public BaseResult<Void> updateArticle(
            @RequestBody UpdateArticleReq req) {
        articleService.updateArticle(req);
        return BaseResult.successful();
    }

    @PostMapping("edit")
    @ApiOperation("编辑文章")
    public BaseResult<Void> editArticle(
            @RequestBody EditArticleReq req) {
        articleService.editArticle(req);
        return BaseResult.successful();
    }

    @GetMapping("team")
    @ApiOperation("获取内容主文章列表")
    public PageResult<List<SsArticle>> getTeamArticleList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("内容主id列表") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("文章状态") Integer articleStatus,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("根据创建时间排序") Boolean articleOrderByCreate
    ) {
        PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<SsArticle> page = articleService.getTeamArticleList(companyType, teamIds, articleStatus, pageRequest, articleOrderByCreate);
        return PageResult.success(page);
    }

    @GetMapping("team/next")
    @ApiOperation("获取内容主当前文章下一篇文章")
    public BaseResult<SsArticle> getTeamNextArticle(
            @RequestParam Long teamId,
            @RequestParam Long articleId,
            @RequestParam(required = false) @ApiParam("文章状态") List<Integer> articleStatuses
    ) {
        SsArticle ssArticle = articleService.getTeamNextArticle(teamId, articleId, articleStatuses).orElse(null);
        return BaseResult.success(ssArticle);
    }

    @GetMapping("team/previous")
    @ApiOperation("获取内容主当前文章上一篇文章")
    public BaseResult<SsArticle> getTeamPreviousArticle(
            @RequestParam Long teamId,
            @RequestParam Long articleId,
            @RequestParam(required = false) @ApiParam("文章状态") List<Integer> articleStatuses
    ) {
        SsArticle ssArticle = articleService.getTeamPreviousArticle(teamId, articleId, articleStatuses).orElse(null);
        return BaseResult.success(ssArticle);
    }

    @GetMapping("team/latest")
    @ApiOperation("获取内容主最新文章")
    public BaseResult<List<SsArticle>> getLatestTeamArticleList(
            @RequestParam Integer companyType,
            @RequestParam Long teamId,
            @RequestParam(required = false) @ApiParam("文章状态") Integer articleStatus,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("文章数量") Long size
    ) {
        List<SsArticle> articles = articleService.getLatestTeamArticleList(companyType, teamId, articleStatus, size);
        return BaseResult.success(articles);
    }

    @PostMapping("like/count/statistics")
    @ApiOperation("获取文章点赞统计")
    public BaseResult<List<CountStatistics>> getArticleLikeCountStatistics(@RequestBody BatchReq<Long> req) {
        List<CountStatistics> statistics = articleLikeService.getArticleLikeCountStatistics(req.getValues());
        return BaseResult.success(statistics);
    }

    @PostMapping("like/operation/statistics")
    @ApiOperation("获取文章点赞操作统计")
    public BaseResult<List<LikeStatistics>> getArticleLikeOperationStatistics(
            @RequestBody BatchReq<Long> req,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Integer wxId) {
        List<LikeStatistics> statistics = articleLikeService.getArticleLikeOperationStatistics(req.getValues(), userId, wxId);
        return BaseResult.success(statistics);
    }

    @GetMapping("info/byId")
    @ApiOperation("获取文章详情")
    public BaseResult<SsArticle> getArticleInfoById(
            @RequestParam @ApiParam("文章id") Long id) {
        SsArticle article = articleService.findOneById(id);
        return BaseResult.success(article);
    }

    @GetMapping("operate/record")
    @ApiOperation("获取文章操作记录")
    public BaseResult<List<SsArticleOperateRecord>> getOperateRecord(
            @RequestParam @ApiParam("文章id") Long articleId
    ) {
        List<SsArticleOperateRecord> record = articleOperateRecordService.getOperateRecord(articleId);
        return BaseResult.success(record);
    }


}

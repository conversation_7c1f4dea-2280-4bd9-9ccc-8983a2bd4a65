package cn.shrise.radium.contentservice.dao.adblake;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.contentservice.adb.entity.QSsMaterialDepartment;
import cn.shrise.radium.contentservice.constant.AuditStatusConstant;
import cn.shrise.radium.contentservice.constant.MaterialCategoryConstant;
import cn.shrise.radium.contentservice.entity.SsMaterial;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class SsMaterialContentDao {

    @Autowired
    @Qualifier("tertiaryJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Resource(name = "jpaQueryTertiary")
    private final JPAQueryFactory jpaQueryTertiary;
    private final QSsMaterialDepartment qSsMaterialDepartment = QSsMaterialDepartment.ssMaterialDepartment;

    public PageResult<List<SsMaterial>> searchContentList(Boolean isAuditPage, List<Integer> creatorIds, Integer category, String searchContent, Integer current, Integer size) {
        String condition1 = ObjectUtil.isNotEmpty(creatorIds)? "AND creator_id IN (:creatorIds) ": "";
        String condition2 = Objects.equals(isAuditPage, true)? "AND audit_status NOT IN (:auditStatus) ": "";
        String condition3 = ObjectUtil.isNotEmpty(category)? "AND category = :category ": "";
        String sql = "SELECT * " +
                "FROM article_db.ss_material " +
                "WHERE id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId) "
                + condition1 + condition2 + condition3 +
                "ORDER BY id DESC " +
                "LIMIT :offset, :size";

        String countSql = "SELECT COUNT(*) " +
                "FROM article_db.ss_material " +
                "WHERE id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId) "
                + condition1 + condition2 + condition3;

        Map<String, Object> args = new HashMap<>();
        args.put("creatorIds", creatorIds);
        args.put("category", category);
        args.put("auditStatus", Arrays.asList(AuditStatusConstant.PRE_AUDIT_NOT_PASS, AuditStatusConstant.AUDIT_DELETE));
        args.put("searchContent", StrUtil.format("\"{}\"", searchContent));
        args.put("materialId", NumberUtil.isLong(searchContent)?searchContent: "无");
        args.put("offset", (current - 1) * size);
        args.put("size", size);
        NamedParameterJdbcTemplate givenParamJdbcTemp = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<SsMaterial> respList = givenParamJdbcTemp.query(sql, args, (rs, rowNum) -> SsMaterial.builder()
                .id(rs.getLong("id"))
                .gmtCreate(rs.getTimestamp("gmt_create").toInstant())
                .gmtModified(rs.getTimestamp("gmt_modified").toInstant())
                .companyType(rs.getInt("company_type"))
                .category(rs.getInt("category"))
                .contentType(Objects.equals(rs.getInt("content_type"), 0)?null: rs.getInt("content_type"))
                .title(rs.getString("title"))
                .content(rs.getString("content"))
                .url(rs.getString("url"))
                .creatorId(Objects.equals(rs.getInt("creator_id"), 0)?null: rs.getInt("creator_id"))
                .auditStatus(Objects.equals(rs.getInt("audit_status"), 0)?null: rs.getInt("audit_status"))
                .auditorId(Objects.equals(rs.getInt("auditor_id"), 0)?null: rs.getInt("auditor_id"))
                .auditRemark(rs.getString("audit_remark"))
                .auditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("audit_time"))?rs.getTimestamp("audit_time").toInstant(): null)
                .visibleType(Objects.equals(rs.getInt("visible_type"), 0)?null: rs.getInt("visible_type"))
                .imageList(rs.getString("image_list"))
                .preAuditorId(Objects.equals(rs.getInt("pre_auditor_id"), 0)?null: rs.getInt("pre_auditor_id"))
                .preAuditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("pre_audit_time"))?rs.getTimestamp("pre_audit_time").toInstant(): null)
                .contentMd5(rs.getString("content_md5"))
                .originMaterialId(rs.getLong("origin_material_id"))
                .build());
        Long total = givenParamJdbcTemp.queryForList(countSql, args, Long.class).get(0);
        return PageResult.success(respList, Pagination.of(current, size, total));
    }

    public PageResult<List<SsMaterial>> searchContentList(Integer companyType, Integer creatorId, Set<Integer> departmentList, String searchContent, Integer current, Integer size) {
        List<Long> materialIds = jpaQueryTertiary.select(qSsMaterialDepartment.materialId).from(qSsMaterialDepartment)
                .where(qSsMaterialDepartment.departmentId.in(departmentList)).fetch();

        String condition = "(visible_type = 10 or (visible_type = 20 and creator_id = :creatorId) or (visible_type = 30 and id in (:materialIds))) ";
        String sql = "SELECT * " +
                "FROM article_db.ss_material " +
                "WHERE company_type = :companyType AND audit_status IN (:auditStatus) AND category = :category AND " + condition +
                "AND id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId) " +
                "ORDER BY id DESC " +
                "LIMIT :offset, :size";

        String countSql = "SELECT COUNT(*) " +
                "FROM article_db.ss_material " +
                "WHERE company_type = :companyType AND audit_status IN (:auditStatus) AND category = :category AND " + condition +
                "AND id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId)";

        Map<String, Object> args = new HashMap<>();
        args.put("companyType", companyType);
        args.put("creatorId", creatorId);
        args.put("materialIds", materialIds);
        args.put("category", MaterialCategoryConstant.MARKETING_SERVICE);
        args.put("auditStatus", Arrays.asList(AuditStatusConstant.AUDIT_PASS, AuditStatusConstant.PRE_AUDIT_PASS));
        args.put("searchContent", StrUtil.format("\"{}\"", searchContent));
        args.put("materialId", NumberUtil.isLong(searchContent)?searchContent: "无");
        args.put("offset", (current - 1) * size);
        args.put("size", size);
        NamedParameterJdbcTemplate givenParamJdbcTemp = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<SsMaterial> respList = givenParamJdbcTemp.query(sql, args, (rs, rowNum) -> SsMaterial.builder()
                .id(rs.getLong("id"))
                .gmtCreate(rs.getTimestamp("gmt_create").toInstant())
                .gmtModified(rs.getTimestamp("gmt_modified").toInstant())
                .companyType(rs.getInt("company_type"))
                .category(rs.getInt("category"))
                .contentType(Objects.equals(rs.getInt("content_type"), 0)?null: rs.getInt("content_type"))
                .title(rs.getString("title"))
                .content(rs.getString("content"))
                .url(rs.getString("url"))
                .creatorId(Objects.equals(rs.getInt("creator_id"), 0)?null: rs.getInt("creator_id"))
                .auditStatus(Objects.equals(rs.getInt("audit_status"), 0)?null: rs.getInt("audit_status"))
                .auditorId(Objects.equals(rs.getInt("auditor_id"), 0)?null: rs.getInt("auditor_id"))
                .auditRemark(rs.getString("audit_remark"))
                .auditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("audit_time"))?rs.getTimestamp("audit_time").toInstant(): null)
                .visibleType(Objects.equals(rs.getInt("visible_type"), 0)?null: rs.getInt("visible_type"))
                .imageList(rs.getString("image_list"))
                .preAuditorId(Objects.equals(rs.getInt("pre_auditor_id"), 0)?null: rs.getInt("pre_auditor_id"))
                .preAuditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("pre_audit_time"))?rs.getTimestamp("pre_audit_time").toInstant(): null)
                .contentMd5(rs.getString("content_md5"))
                .build());
        Long total = givenParamJdbcTemp.queryForList(countSql, args, Long.class).get(0);
        return PageResult.success(respList, Pagination.of(current, size, total));
    }

    public PageResult<List<SsMaterial>> searchPromotionContentList(Integer companyType, String searchContent, Integer current, Integer size) {
        String sql = "SELECT * " +
                "FROM article_db.ss_material " +
                "WHERE company_type = :companyType AND audit_status IN (:auditStatus) AND category = :category AND " +
                "id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId) " +
                "ORDER BY id DESC " +
                "LIMIT :offset, :size";

        String countSql = "SELECT COUNT(*) " +
                "FROM article_db.ss_material " +
                "WHERE company_type = :companyType AND audit_status IN (:auditStatus) AND category = :category AND " +
                "id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId) ";

        Map<String, Object> args = new HashMap<>();
        args.put("companyType", companyType);
        args.put("auditStatus", Arrays.asList(AuditStatusConstant.AUDIT_PASS, AuditStatusConstant.PRE_AUDIT_PASS));
        args.put("category", MaterialCategoryConstant.PROMOTION_CUSTOMER);
        args.put("searchContent", StrUtil.format("\"{}\"", searchContent));
        args.put("materialId", NumberUtil.isLong(searchContent)?searchContent: "无");
        args.put("offset", (current - 1) * size);
        args.put("size", size);
        NamedParameterJdbcTemplate givenParamJdbcTemp = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<SsMaterial> respList = givenParamJdbcTemp.query(sql, args, (rs, rowNum) -> SsMaterial.builder()
                .id(rs.getLong("id"))
                .gmtCreate(rs.getTimestamp("gmt_create").toInstant())
                .gmtModified(rs.getTimestamp("gmt_modified").toInstant())
                .companyType(rs.getInt("company_type"))
                .category(rs.getInt("category"))
                .contentType(Objects.equals(rs.getInt("content_type"), 0)?null: rs.getInt("content_type"))
                .title(rs.getString("title"))
                .content(rs.getString("content"))
                .url(rs.getString("url"))
                .creatorId(Objects.equals(rs.getInt("creator_id"), 0)?null: rs.getInt("creator_id"))
                .auditStatus(Objects.equals(rs.getInt("audit_status"), 0)?null: rs.getInt("audit_status"))
                .auditorId(Objects.equals(rs.getInt("auditor_id"), 0)?null: rs.getInt("auditor_id"))
                .auditRemark(rs.getString("audit_remark"))
                .auditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("audit_time"))?rs.getTimestamp("audit_time").toInstant(): null)
                .visibleType(Objects.equals(rs.getInt("visible_type"), 0)?null: rs.getInt("visible_type"))
                .imageList(rs.getString("image_list"))
                .preAuditorId(Objects.equals(rs.getInt("pre_auditor_id"), 0)?null: rs.getInt("pre_auditor_id"))
                .preAuditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("pre_audit_time"))?rs.getTimestamp("pre_audit_time").toInstant(): null)
                .contentMd5(rs.getString("content_md5"))
                .build());
        Long total = givenParamJdbcTemp.queryForList(countSql, args, Long.class).get(0);
        return PageResult.success(respList, Pagination.of(current, size, total));
    }

    public PageResult<List<SsMaterial>> searchBrandContentList(Integer companyType, String searchContent, Integer current, Integer size) {
        String sql = "SELECT * " +
                "FROM article_db.ss_material " +
                "WHERE company_type = :companyType AND audit_status IN (:auditStatus) AND category = :category AND " +
                "id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId) " +
                "ORDER BY id DESC " +
                "LIMIT :offset, :size";

        String countSql = "SELECT COUNT(*) " +
                "FROM article_db.ss_material " +
                "WHERE company_type = :companyType AND audit_status IN (:auditStatus) AND category = :category AND " +
                "id IN (SELECT DISTINCT(material_id) FROM article_db.ss_material_content WHERE MATCH(content) AGAINST (:searchContent) or material_id = :materialId) ";

        Map<String, Object> args = new HashMap<>();
        args.put("companyType", companyType);
        args.put("auditStatus", Arrays.asList(AuditStatusConstant.AUDIT_PASS, AuditStatusConstant.PRE_AUDIT_PASS));
        args.put("category", MaterialCategoryConstant.BRAND_TEXT);
        args.put("searchContent", StrUtil.format("\"{}\"", searchContent));
        args.put("materialId", NumberUtil.isLong(searchContent)?searchContent: "无");
        args.put("offset", (current - 1) * size);
        args.put("size", size);
        NamedParameterJdbcTemplate givenParamJdbcTemp = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<SsMaterial> respList = givenParamJdbcTemp.query(sql, args, (rs, rowNum) -> SsMaterial.builder()
                .id(rs.getLong("id"))
                .gmtCreate(rs.getTimestamp("gmt_create").toInstant())
                .gmtModified(rs.getTimestamp("gmt_modified").toInstant())
                .companyType(rs.getInt("company_type"))
                .category(rs.getInt("category"))
                .contentType(Objects.equals(rs.getInt("content_type"), 0)?null: rs.getInt("content_type"))
                .title(rs.getString("title"))
                .content(rs.getString("content"))
                .url(rs.getString("url"))
                .creatorId(Objects.equals(rs.getInt("creator_id"), 0)?null: rs.getInt("creator_id"))
                .auditStatus(Objects.equals(rs.getInt("audit_status"), 0)?null: rs.getInt("audit_status"))
                .auditorId(Objects.equals(rs.getInt("auditor_id"), 0)?null: rs.getInt("auditor_id"))
                .auditRemark(rs.getString("audit_remark"))
                .auditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("audit_time"))?rs.getTimestamp("audit_time").toInstant(): null)
                .visibleType(Objects.equals(rs.getInt("visible_type"), 0)?null: rs.getInt("visible_type"))
                .imageList(rs.getString("image_list"))
                .preAuditorId(Objects.equals(rs.getInt("pre_auditor_id"), 0)?null: rs.getInt("pre_auditor_id"))
                .preAuditTime(ObjectUtil.isNotEmpty(rs.getTimestamp("pre_audit_time"))?rs.getTimestamp("pre_audit_time").toInstant(): null)
                .contentMd5(rs.getString("content_md5"))
                .build());
        Long total = givenParamJdbcTemp.queryForList(countSql, args, Long.class).get(0);
        return PageResult.success(respList, Pagination.of(current, size, total));
    }
}

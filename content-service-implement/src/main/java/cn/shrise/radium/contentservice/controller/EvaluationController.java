package cn.shrise.radium.contentservice.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.entity.SsCustomerEvaluation;
import cn.shrise.radium.contentservice.entity.SsSurveyChoice;
import cn.shrise.radium.contentservice.entity.SsSurveyTopic;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.req.EvaluationUserInfoReq;
import cn.shrise.radium.contentservice.req.SaveEvaluationAnswerReq;
import cn.shrise.radium.contentservice.req.SubmitEvaluationReq;
import cn.shrise.radium.contentservice.req.UpdateCustomerEvaluationReq;
import cn.shrise.radium.contentservice.resp.EvaluationAnswerResp;
import cn.shrise.radium.contentservice.service.evaluation.ChoiceService;
import cn.shrise.radium.contentservice.service.evaluation.CustomerEvaluationService;
import cn.shrise.radium.contentservice.service.evaluation.EvaluationAnswerService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.shrise.radium.contentservice.constant.SurveyTypeConst.AJ_ANTI_MONEY_EVALUATION;
import static cn.shrise.radium.contentservice.constant.SurveyTypeConst.AJ_FEEDBACK_EVALUATION;

@RestController
@RequestMapping("evaluation")
@RequiredArgsConstructor
public class EvaluationController {

    private final CustomerEvaluationService customerEvaluationService;
    private final EvaluationAnswerService evaluationAnswerService;
    private final ChoiceService choiceService;

    @ApiOperation("重新测评")
    @PostMapping("redo")
    public BaseResult<String> evaluationRedo(
            @RequestParam @ApiParam("EvaluationId") Integer evaluationId,
            @RequestParam @ApiParam("CompanyType") Integer companyType) {
        List<String> numberList = customerEvaluationService.findAllNumber(companyType);
        SsCustomerEvaluation ev = customerEvaluationService.findOneById(evaluationId);
        if (ObjectUtil.isNull(ev)) {
            return BaseResult.create(ContentErrorCode.FAILURE);
        }
        customerEvaluationService.abandonOneById(evaluationId);
        SsCustomerEvaluation newEv = customerEvaluationService.findOrCreateOne(companyType, ev.getMobile(),
                false, ev.getUserId(), numberList, ev.getName(), ev.getAntiSurveyScore(),
                ev.getAntiSurveyTime());
        evaluationAnswerService.findAndCreateList(evaluationId, newEv.getId(),
                Arrays.asList(AJ_ANTI_MONEY_EVALUATION.getValue(), AJ_FEEDBACK_EVALUATION.getValue()));
        return BaseResult.create(ContentErrorCode.SUCCESS);
    }

    @GetMapping("users")
    @ApiOperation("获取用户的评测")
    public BaseResult<SsCustomerEvaluation> getUserEvaluation(@RequestParam Integer userId) {
        SsCustomerEvaluation evaluation = customerEvaluationService.getUserEvaluation(userId)
                .orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(evaluation);
    }

    @GetMapping("user_choice")
    @ApiOperation("获取用户的评测选项")
    public BaseResult<SsSurveyChoice> getUserChoice(
            @RequestParam @ApiParam("评测id") Integer evaluationId,
            @RequestParam @ApiParam("选项") String content) {
        SsSurveyChoice choice = choiceService.findUserChoice(evaluationId, content);
        return BaseResult.success(choice);
    }

    @GetMapping("identity_number")
    @ApiOperation("通过身份证号获取客户评估")
    public BaseResult<SsCustomerEvaluation> findByIdentityNumber(
            @RequestParam Integer companyType,
            @RequestParam String identityNumber) {
        SsCustomerEvaluation evaluation = customerEvaluationService.findByIdentityNumber(companyType, identityNumber);
        return BaseResult.success(evaluation);
    }

    @GetMapping("topic")
    @ApiOperation("获取题目列表")
    public BaseResult<List<SsSurveyTopic>> getTopicList(
            @RequestParam @ApiParam("测评类型") Integer surveyType) {
        List<SsSurveyTopic> surveyList = choiceService.getTopicList(surveyType);
        return BaseResult.success(surveyList);
    }

    @GetMapping("choice")
    @ApiOperation("根据题目获取选项信息")
    public BaseResult<List<SsSurveyChoice>> getChoiceList(
            @RequestParam @ApiParam("题目id") Long topicId) {
        List<SsSurveyChoice> surveyList = choiceService.getChoiceList(topicId);
        return BaseResult.success(surveyList);
    }

    @GetMapping("choice/options")
    @ApiOperation("获取评测题目选项")
    public BaseResult<List<SsSurveyChoice>> getEvaluationChoiceList(
            @RequestParam @ApiParam("评测id") Integer evaluationId,
            @RequestParam @ApiParam("题目类型") Integer surveyType,
            @RequestParam @ApiParam("第几题") Integer order) {
        List<SsSurveyChoice> surveyList = choiceService.getEvaluationChoiceList(evaluationId, surveyType, order);
        return BaseResult.success(surveyList);
    }

    @GetMapping("getCustomerEvaluation")
    @ApiOperation("通过id获取客户评估")
    public BaseResult<SsCustomerEvaluation> findById(
            @RequestParam Integer id) {
        SsCustomerEvaluation evaluation = customerEvaluationService.findById(id)
                .orElse(null);
        return BaseResult.success(evaluation);
    }

    @PostMapping("submitEvaluation")
    @ApiOperation("提交客户评估")
    public BaseResult<Boolean> submitEvaluation(@RequestBody SubmitEvaluationReq req) {
        customerEvaluationService.submitEvaluation(req);
        return BaseResult.success(true);
    }

    @PostMapping("finish")
    @ApiOperation("提交客户评估")
    public BaseResult<Void> finishEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("测评类型") Integer surveyType) {
        customerEvaluationService.finishEvaluation(userId, evaluationId, surveyType);
        return BaseResult.successful();
    }

    @PostMapping("answer/save")
    @ApiOperation("保存测评选项")
    public BaseResult<Void> saveEvaluationAnswer(
            @RequestBody SaveEvaluationAnswerReq req) {
        customerEvaluationService.saveEvaluationAnswer(req);
        return BaseResult.successful();
    }

    @GetMapping("answer/get")
    @ApiOperation("获取测评选项")
    public BaseResult<EvaluationAnswerResp> getEvaluationAnswer(
            @RequestParam @ApiParam("测评id") Integer evaluationId) {
        EvaluationAnswerResp resp = customerEvaluationService.getEvaluationAnswer(evaluationId);
        return BaseResult.success(resp);
    }

    @GetMapping("getCustomerEvaluation/{userId}")
    @ApiOperation("通过userid获取客户评估")
    public BaseResult<SsCustomerEvaluation> getCustomerEvaluation(
            @PathVariable Integer userId) {
        SsCustomerEvaluation evaluation = customerEvaluationService.findByUserid(userId);
        return BaseResult.success(evaluation);
    }

    @GetMapping("createCustomerEvaluation")
    @ApiOperation("为用户创建评测")
    public BaseResult<SsCustomerEvaluation> createCustomerEvaluation(
            @RequestParam Integer userId,
            @RequestParam Integer companyType,
            @RequestParam String mobile) {
        SsCustomerEvaluation evaluation = customerEvaluationService.createCustomerEvaluation(userId, companyType, mobile);
        return BaseResult.success(evaluation);
    }

    @PostMapping("updateCustomerEvaluation")
    @ApiOperation("更新客户评估")
    public BaseResult<Boolean> updateCustomerEvaluation(
            @RequestBody @ApiParam("更新的属性") UpdateCustomerEvaluationReq req) {
        customerEvaluationService.updateCustomerEvaluation(req);
        return BaseResult.success(true);
    }

    @GetMapping("evaluationData")
    @ApiOperation("获取测评pdf信息")
    public BaseResult<Map<String, Object>> evaluationDate(
            @RequestParam @ApiParam("测评id") Integer evaluationId) {
        Map<String, Object> dataMap = customerEvaluationService.getEvaluationData(evaluationId);
        return BaseResult.success(dataMap);
    }

    @PostMapping("submitEvaluationUserInfo")
    @ApiOperation("提交客户评估基本信息")
    public BaseResult<Boolean> submitEvaluationUserInfo(@RequestBody @Valid EvaluationUserInfoReq req) {
        customerEvaluationService.submitEvaluationUserInfo(req);
        return BaseResult.success(true);
    }

    @GetMapping("findByNumber")
    @ApiOperation("通过number获取客户评估")
    public BaseResult<SsCustomerEvaluation> findByNumber(
            @RequestParam String number) {
        SsCustomerEvaluation evaluation = customerEvaluationService.findByNumber(number);
        return BaseResult.success(evaluation);
    }

    @PostMapping("batch/user")
    @ApiOperation("根据userid批量获取客户评估")
    public BaseResult<List<SsCustomerEvaluation>> getCustomerEvaluationList(
            @RequestBody BatchReq<Integer> req) {
        List<SsCustomerEvaluation> evaluation = customerEvaluationService.getCustomerEvaluationList(req.getValues());
        return BaseResult.success(evaluation);
    }

    @GetMapping("getUserLast")
    @ApiOperation("获取用户最后一次的评测")
    public BaseResult<SsCustomerEvaluation> getUserLastEvaluation(@RequestParam Integer userId) {
        return customerEvaluationService.getUserLastEvaluation(userId);
    }
}

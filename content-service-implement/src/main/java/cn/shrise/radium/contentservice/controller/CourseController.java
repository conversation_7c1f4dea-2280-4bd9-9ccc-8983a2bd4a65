package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.contentservice.constant.VodBehaviorConstant;
import cn.shrise.radium.contentservice.service.vod.AliVodService;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.req.createVideoCourseReq;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("course")
@RequiredArgsConstructor
public class CourseController {

    private final AliVodService aliVodService;
    private final OrderClient orderClient;


    @PostMapping("video/create")
    @ApiOperation("创建课程视频")
    public BaseResult<CreateUploadVideoResponse> createVideoCourse(
            @RequestParam @ApiParam("系列id") Integer seriesId,
            @RequestParam @ApiParam("目录id") Long catalogId,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam @ApiParam("视频时长") Double videoDuration,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL
    ) {
        CreateUploadVideoResponse uploadVideo = aliVodService.createUploadVideo(title, fileName, cateId, templateGroupId,
                description, coverURL, "vod/sales/video/upload", VodBehaviorConstant.RsVideoCourse);
        createVideoCourseReq req = createVideoCourseReq.builder()
                .name(title)
                .seriesId(seriesId)
                .catalogId(catalogId)
                .videoId(uploadVideo.getVideoId())
                .videoDuration(videoDuration)
                .enabled(true)
                .build();
        orderClient.createVideoCourse(req);
        return BaseResult.success(uploadVideo);
    }


}

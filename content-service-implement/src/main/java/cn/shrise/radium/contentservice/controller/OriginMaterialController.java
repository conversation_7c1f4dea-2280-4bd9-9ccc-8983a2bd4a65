package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsOriginMaterial;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialOperateRecord;
import cn.shrise.radium.contentservice.req.CreateOriginMaterialReq;
import cn.shrise.radium.contentservice.req.OriginMaterialAuditReq;
import cn.shrise.radium.contentservice.service.originmaterial.OriginMaterialOperateRecordService;
import cn.shrise.radium.contentservice.service.originmaterial.OriginMaterialService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zhangjianwu
 * @Date: 2024/4/22 10:41
 * @Desc:
 **/
@RestController
@RequestMapping("origin-material")
@RequiredArgsConstructor
public class OriginMaterialController {

    private final OriginMaterialService originMaterialService;
    private final OriginMaterialOperateRecordService originMaterialOperateRecordService;

    @PostMapping("create")
    @ApiOperation("创建好评素材")
    public BaseResult<Void> createOriginMaterial(
            @RequestBody @Valid CreateOriginMaterialReq req) {
        originMaterialService.createOne(req);
        return BaseResult.successful();
    }

    @GetMapping()
    @ApiOperation("获取好评素材")
    BaseResult<SsOriginMaterial> getOriginMaterial(@RequestParam @ApiParam("好评素材频道id") Long materialId) {
        SsOriginMaterial originMaterial = originMaterialService.getOriginMaterial(materialId);
        return BaseResult.success(originMaterial);
    }

    @PostMapping("delete")
    @ApiOperation("删除好评素材")
    public BaseResult<Void> deleteOriginMaterial(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("好评素材id") Long materialId
    ) {
        originMaterialService.deleteOriginMaterial(userId, materialId);
        return BaseResult.successful();
    }

    @GetMapping("operate/record")
    @ApiOperation("获取好评素材操作记录")
    public BaseResult<List<SsOriginMaterialOperateRecord>> getOriginMaterialOperateRecord(
            @RequestParam @ApiParam("好评素材id") Long materialId
    ) {
        List<SsOriginMaterialOperateRecord> operateRecord = originMaterialOperateRecordService.getOriginMaterialOperateRecord(materialId);
        return BaseResult.success(operateRecord);
    }

    @GetMapping("list")
    @ApiOperation("获取好评素材列表")
    public PageResult<List<SsOriginMaterial>> getOriginMaterialList(
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("频道id") List<Long> channelIds,
            @RequestParam(required = false) @ApiParam("分类") Integer category,
            @RequestParam(required = false) @ApiParam("状态") Boolean status,
            @RequestParam(required = false) @ApiParam("审核状态列表") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<SsOriginMaterial> page = originMaterialService.getOriginMaterialList(startTime, endTime, userId, channelIds, category, status, auditStatusList, searchContent, pageRequest);
        Pagination pagination = Pagination.of(current, page.getSize(), page.getTotalElements());
        return PageResult.success(page.getContent(), pagination);
    }


    @GetMapping("getOriginMaterialByIdList")
    @ApiOperation("根据素材id批量获取好评素材")
    BaseResult<List<SsOriginMaterial>> getOriginMaterialByIdList(@RequestParam @ApiParam("好评素材id") List<Long> originMaterialIds) {
        return BaseResult.success(originMaterialService.getOriginMaterialByIdList(originMaterialIds));
    }

    @PostMapping("audit")
    @ApiOperation("好评素材审核")
    BaseResult<Void> auditOriginMaterial(@RequestBody OriginMaterialAuditReq req) {
        originMaterialService.auditOriginMaterial(req);
        return BaseResult.successful();
    }


}


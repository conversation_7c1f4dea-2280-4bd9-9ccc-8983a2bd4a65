package cn.shrise.radium.contentservice.service.article;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.constant.AuditStatusConstant;
import cn.shrise.radium.contentservice.entity.QSsArticle;
import cn.shrise.radium.contentservice.entity.QSsArticleComment;
import cn.shrise.radium.contentservice.entity.QSsArticleTypeRelation;
import cn.shrise.radium.contentservice.entity.SsArticleComment;
import cn.shrise.radium.contentservice.repository.ArticleCommentRepository;
import cn.shrise.radium.contentservice.repository.ArticleRepository;
import cn.shrise.radium.contentservice.req.CreateArticleCommentReq;
import cn.shrise.radium.contentservice.req.TextModerationReq;
import cn.shrise.radium.contentservice.resp.CountStatistics;
import cn.shrise.radium.contentservice.resp.TextModerationResp;
import cn.shrise.radium.contentservice.service.green.GreenService;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.PagedList;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.contentservice.constant.AuditStatusConstant.*;

@Service
@RequiredArgsConstructor
public class ArticleCommentService {

    private final JPAQueryFactory queryFactory;
    private final QSsArticleComment qSsArticleComment = QSsArticleComment.ssArticleComment;
    private final EntityManager entityManager;
    private final CriteriaBuilderFactory criteriaBuilderFactory;
    private final ArticleCommentRepository articleCommentRepository;
    private final GreenService greenService;

    public PageResult<List<SsArticleComment>> getArticleCommentPage(Long articleId, LocalDate startTime, LocalDate endTime, Boolean audit, Integer status, Integer current, Integer size) {
        JPAQuery<SsArticleComment> query = queryFactory.select(qSsArticleComment)
                .from(qSsArticleComment)
                .where(qSsArticleComment.articleId.eq(articleId));
        if (ObjectUtil.isAllNotEmpty(startTime, endTime)) {
            query.where(qSsArticleComment.commentTime.between(DateUtils.getDayOfStart(startTime), DateUtils.getDayOfEnd(endTime)));
        }

        if (audit) {
            if (ObjectUtil.isNotEmpty(status)) {
                query.where(qSsArticleComment.auditFlag.eq(status));
            } else {
                query.where(qSsArticleComment.auditFlag.in(Arrays.asList(AUDIT_NOT_PASS, AUDIT_PASS)));
            }
        } else {
            query.where(qSsArticleComment.auditFlag.eq(AUDIT_EXECUTING));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(qSsArticleComment.commentTime.desc(), qSsArticleComment.id.desc());
        query.offset((long) (current - 1) * size).limit(size);
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }


    public List<CountStatistics> getArticleCommentStatistics(Collection<Long> articleIds, Integer auditFlag) {
        if (ObjectUtils.isEmpty(articleIds)) {
            return Collections.emptyList();
        }

        NumberExpression<Long> countExpression = qSsArticleComment.userId.count().as("count");
        Set<Long> messageIdSet = new HashSet<>(articleIds);
        final JPAQuery<Tuple> query = queryFactory.select(Projections.tuple(qSsArticleComment.articleId,
                        countExpression))
                .from(qSsArticleComment)
                .where(qSsArticleComment.articleId.in(messageIdSet))
                .where(qSsArticleComment.enabled.eq(true))
                .groupBy(qSsArticleComment.articleId);

        if (ObjectUtils.isNotEmpty(auditFlag)) {
            query.where(qSsArticleComment.auditFlag.eq(auditFlag));
        }

        List<Tuple> tuples = query.fetch();

        return tuples.stream().map(tuple -> CountStatistics.builder()
                        .id(tuple.get(qSsArticleComment.articleId))
                        .count(tuple.get(countExpression))
                        .build())
                .collect(Collectors.toList());
    }

    public Page<SsArticleComment> getArticleCommentList(Long articleId, Integer auditFlag, Pageable pageable) {
        BlazeJPAQuery<SsArticleComment> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<SsArticleComment> query = blazeJpaQuery.select(qSsArticleComment)
                .from(qSsArticleComment)
                .where(qSsArticleComment.enabled.eq(true))
                .where(qSsArticleComment.articleId.eq(articleId));

        if (ObjectUtils.isNotEmpty(auditFlag)) {
            query.where(qSsArticleComment.auditFlag.eq(auditFlag));
        }

        PagedList<SsArticleComment> pagedList = query.orderBy(qSsArticleComment.commentTime.desc(), qSsArticleComment.id.desc())
                .fetchPage((int) pageable.getOffset(), pageable.getPageSize());

        return new PageImpl<>(pagedList, pageable, pagedList.getTotalSize());
    }

    public void auditArticleComment(Integer commentId, Integer auditFlag, Integer auditorId) {
        Optional<SsArticleComment> commentOptional = articleCommentRepository.findById(commentId);
        if (commentOptional.isPresent()) {
            SsArticleComment comment = commentOptional.get();
            comment.setAuditFlag(auditFlag);
            comment.setAuditorId(auditorId);
            articleCommentRepository.save(comment);
            return;
        }
        throw new RecordNotExistedException("评论不存在");
    }

    public SsArticleComment createArticleComment(CreateArticleCommentReq req) {
        String content = req.getContent();
        Boolean audit = req.getAudit();

        SsArticleComment.SsArticleCommentBuilder builder = SsArticleComment.builder()
                .articleId(req.getId())
                .commentMessage(content)
                .commentTime(Instant.now())
                .wxId(req.getWxId())
                .userId(req.getUserId())
                .enabled(true)
                .auditFlag(AUDIT_EXECUTING);

        //是否开启机审
        if (audit) {
            TextModerationReq textModerationReq = TextModerationReq.builder()
                    .companyType(req.getCompanyType())
                    .service("comment_detection")
                    .dataId(UUID.randomUUID().toString())
                    .content(content)
                    .build();
            TextModerationResp textModerationResp = greenService.textModeration(textModerationReq);
            boolean passed = textModerationResp.isPassed();
            Long resultId = textModerationResp.getResultId();
            Integer auditFlag = passed ? AUDIT_EXECUTING: AUDIT_NOT_PASS;
            builder.resultId(resultId)
                    .auditFlag(auditFlag);
        }

        return articleCommentRepository.save(builder.build());
    }
}

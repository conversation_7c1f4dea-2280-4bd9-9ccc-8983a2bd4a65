package cn.shrise.radium.contentservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "radium.web-oss-config")
@EnableConfigurationProperties
public class WebOssConfigProperty {

    private String regionId;
    private String accesskeyId;
    private String secret;
    private String roleArn;
    private String roleSessionName;
    private String videoEndPoint;
    private String fileEndPoint;
}

package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.common.util.StringRedisUtil;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.enums.RoomLotteryOperateRecord;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.properties.RoomLotteryProperty;
import cn.shrise.radium.contentservice.repository.RoomLotteryOperateRecordRepository;
import cn.shrise.radium.contentservice.repository.RoomLotteryRepository;
import cn.shrise.radium.contentservice.req.CreateRoomLotteryReq;
import cn.shrise.radium.contentservice.req.UpdateRoomLotteryReq;
import cn.shrise.radium.contentservice.resp.RoomLotteryResp;
import cn.shrise.radium.imservice.ImClient;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.RocketMQ5Constant.TAG_CONTENT_LOTTERY_JOIN;
import static cn.shrise.radium.common.constant.RocketMQ5Constant.TOPIC_COMMON_DELAY;
import static cn.shrise.radium.contentservice.enums.RoomLotteryTypeEnum.ORDINARY_LOTTERY;
import static cn.shrise.radium.contentservice.enums.RoomLotteryTypeEnum.SPECIAL_LOTTERY;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoomLotteryService {

    private final JPAQueryFactory queryFactory;
    private final RoomLotteryRepository roomLotteryRepository;
    private final StringRedisUtil stringRedisUtil;
    private final RoomLotteryOperateRecordRepository roomLotteryOperateRecordRepository;
    private final RocketMqUtils rocketMqUtils;
    private final StringRedisTemplate stringRedisTemplate;
    private final ImClient imClient;
    private final QSsRoomLottery qSsRoomLottery = QSsRoomLottery.ssRoomLottery;
    private final RoomLotteryProperty roomLotteryProperty;
    public static final String LOTTERY_PRIZE_COUNT_KEY = "lottery:prize_count:%d:%s";

    public Page<RoomLotteryResp> getRoomLottery(List<Long> roomList, Long sceneId, Integer status, LocalDate startTime, LocalDate endTime, Integer salesId, Pageable pageable) {
        QSsRoomLottery ssRoomLottery = QSsRoomLottery.ssRoomLottery;
        QSsRoomPrize ssRoomPrize = QSsRoomPrize.ssRoomPrize;
        JPAQuery<Tuple> query = queryFactory.select(ssRoomLottery.id, ssRoomLottery.number, ssRoomLottery.name, ssRoomPrize.name, ssRoomLottery.maxCount, ssRoomLottery.status, ssRoomLottery.startTime, ssRoomLottery.duration, ssRoomLottery.roomId, ssRoomLottery.gmtCreate, ssRoomLottery.type)
                .from(ssRoomLottery)
                .leftJoin(ssRoomPrize).on(ssRoomPrize.id.eq(ssRoomLottery.prizeId));
        if (!ObjectUtils.isEmpty(roomList)) {
            query.where(ssRoomLottery.roomId.in(roomList));
        }
        if (!ObjectUtils.isEmpty(sceneId)) {
            query.where(ssRoomLottery.sceneId.eq(sceneId));
        }
        if (!ObjectUtils.isEmpty(status)) {
            query.where(ssRoomLottery.status.eq(status));
        }
        if (!ObjectUtils.isEmpty(startTime)) {
            query.where(ssRoomLottery.gmtCreate.goe(startTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (!ObjectUtils.isEmpty(endTime)) {
            query.where(ssRoomLottery.gmtCreate.lt(endTime.atStartOfDay().plusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
        }
        final long total = query.fetchCount();
        List<Tuple> fetch = query.orderBy(ssRoomLottery.id.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(ssRoomLottery.id.desc())
                .fetch();
        List<RoomLotteryResp> roomLotteryResps = new ArrayList<>();
        if (!ObjectUtils.isEmpty(fetch)) {
            fetch.forEach(e -> {
                RoomLotteryResp roomLotteryResp = RoomLotteryResp.builder()
                        .id(e.get(ssRoomLottery.id))
                        .number(e.get(ssRoomLottery.number))
                        .name(e.get(ssRoomLottery.name))
                        .prizeName(e.get(ssRoomPrize.name))
                        .maxCount(e.get(ssRoomLottery.maxCount))
                        .status(e.get(ssRoomLottery.status))
                        .startTime(e.get(ssRoomLottery.startTime))
                        .duration(e.get(ssRoomLottery.duration))
                        .roomId(e.get(ssRoomLottery.roomId))
                        .createTime(e.get(ssRoomLottery.gmtCreate))
                        .type(e.get(ssRoomLottery.type))
                        .build();
                roomLotteryResps.add(roomLotteryResp);
            });
        }
        return new PageImpl<>(roomLotteryResps, pageable, total);
    }

    public List<RoomLotteryResp> getValidLotteryList(Long sceneId) {
        return queryFactory.selectFrom(qSsRoomLottery)
                .where(qSsRoomLottery.sceneId.eq(sceneId))
                .where(qSsRoomLottery.status.in(Arrays.asList(
                        RoomLotteryType.INACTIVATED_1.getValue(),
                        RoomLotteryType.OPEN_2.getValue(),
                        RoomLotteryType.DRAW_3.getValue())))
                .fetch().stream().map(l -> RoomLotteryResp.builder()
                        .id(l.getId())
                        .number(l.getNumber())
                        .name(l.getName())
                        .maxCount(l.getMaxCount())
                        .status(l.getStatus())
                        .startTime(l.getStartTime())
                        .duration(l.getDuration())
                        .roomId(l.getRoomId())
                        .sceneId(l.getSceneId())
                        .createTime(l.getGmtCreate())
                        .type(l.getType())
                        .build())
                .collect(Collectors.toList());
    }

    public ContentErrorCode createRoomLottery(CreateRoomLotteryReq req) {
        if (req.getNumber().length() > 32) {
            throw new BusinessException("活动编号超出32字符长度限制");
        }
        if (req.getName().length() > 128) {
            throw new BusinessException("活动名称超出128字符长度限制");
        }
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        if (ObjectUtil.equal(req.getType(), ORDINARY_LOTTERY.getValue())) {
            if (req.getMaxCount() <= 0 || !pattern.matcher(req.getMaxCount().toString()).matches()) {
                return ContentErrorCode.PARAM_INVALID;
            }
        }
        //查询活动编号是否重复
        QSsRoomLottery ssLottery = QSsRoomLottery.ssRoomLottery;
        List<String> fetch = queryFactory.select(ssLottery.number)
                .from(ssLottery)
                .where(ssLottery.roomId.eq(req.getRoomId()))
                .fetch();
        if (fetch.contains(req.getNumber())) {
            return ContentErrorCode.NUMBER_EXISTED;
        }
        SsRoomLottery ssRoomLottery = new SsRoomLottery();
        BeanUtils.copyProperties(req, ssRoomLottery);
        ssRoomLottery.setStatus(RoomLotteryType.INACTIVATED_1.getValue());
        SsRoomLottery save = roomLotteryRepository.save(ssRoomLottery);
        createRoomLotteryOperateRecord(req.getUserId(), save.getId(), RoomLotteryOperateRecord.CREATE_LOTTERY.getValue());
        return ContentErrorCode.SUCCESS;
    }

    public ContentErrorCode updateRoomLotteryStatus(Long lotteryId, RoomLotteryType status, Integer operatorId) {
        // 查询活动是否存在
        SsRoomLottery lottery = roomLotteryRepository.findById(lotteryId).orElse(null);
        if (lottery == null) {
            return ContentErrorCode.RECORD_NOT_EXISTED;
        }

        String roomLotteryKey = "room_lottery:" + lottery.getSceneId().toString();
        String roomLockKey = "room_lottery_lock:" + lottery.getSceneId().toString();
        try {
            if (stringRedisUtil.getLock(roomLockKey, "OpenLottery", 30, TimeUnit.SECONDS)) {
                // 检查活动状态是否正确（是否可操作修改）
                boolean isStatusCorrect = false;
                RoomLotteryOperateRecord recordType = RoomLotteryOperateRecord.EDIT_LOTTERY;
                switch (status) {
                    case INACTIVATED_1:
                        break;
                    case OPEN_2:
                        // 未激活并且REDIS不存在未完成的活动
                        isStatusCorrect = lottery.getStatus() == RoomLotteryType.INACTIVATED_1.getValue() && Boolean.FALSE.equals(stringRedisTemplate.hasKey(roomLotteryKey));
                        recordType = RoomLotteryOperateRecord.START_LOTTERY;
                        break;
                    case DRAW_3:
                        isStatusCorrect = lottery.getStatus() == RoomLotteryType.OPEN_2.getValue();
                        break;
                    case COMPLETED_4:
                        isStatusCorrect = lottery.getStatus() == RoomLotteryType.DRAW_3.getValue();
                        recordType = RoomLotteryOperateRecord.FINISH_LOTTERY;
                        break;
                    case CANCELLED_5:
                        // 未激活并且非REDIS存在的活动
                        isStatusCorrect = lottery.getStatus() == RoomLotteryType.INACTIVATED_1.getValue() && !Objects.equals(stringRedisTemplate.opsForValue().get(roomLotteryKey), lotteryId.toString());
                        recordType = RoomLotteryOperateRecord.CANCEL_LOTTERY;
                        break;
                }
                if (!isStatusCorrect) {
                    return ContentErrorCode.RECORD_UNCHANGEABLE;
                }

                //更新状态处理逻辑
                if (status == RoomLotteryType.OPEN_2) {
                    //发送开奖延时消息
                    rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, ContentServiceConst.MqTagType.DRAW_LOTTERY, lottery.getDuration() * 60 * 1000, lotteryId);
                    lottery.setStartTime(Instant.now());
                    if (Objects.equals(SPECIAL_LOTTERY.getValue(), lottery.getType())) {
                        loadPrizesToRedis(lottery.getNumber());
                        rocketMqUtils.convertAndSend(TOPIC_COMMON_DELAY, TAG_CONTENT_LOTTERY_JOIN, 3 * 1000, lottery);
                    }
                    imClient.lotteryPush(lottery.getRoomId(), lottery.getSceneId(), lotteryId, RoomLotteryType.OPEN_2.getValue());
                }
                lottery.setStatus(status.getValue());
                roomLotteryRepository.save(lottery);
                createRoomLotteryOperateRecord(operatorId, lotteryId, recordType.getValue());

                // 更新REDIS信息
                if (status == RoomLotteryType.OPEN_2) {
                    //REDIS置激活的活动
                    stringRedisTemplate.opsForValue().set(roomLotteryKey, lotteryId.toString());
                } else if (status == RoomLotteryType.COMPLETED_4 || status == RoomLotteryType.CANCELLED_5) {
                    if (status == RoomLotteryType.COMPLETED_4) {
                        imClient.lotteryPush(lottery.getRoomId(), lottery.getSceneId(), lotteryId, RoomLotteryType.COMPLETED_4.getValue());
                        //REDIS清理活动
                        stringRedisTemplate.delete(roomLotteryKey);
                    }
                }

                return ContentErrorCode.SUCCESS;
            }
        } catch (Exception e) {
            throw e;
        } finally {
            stringRedisUtil.unlock(roomLockKey);
        }
        return ContentErrorCode.RECORD_UNCHANGEABLE;
    }

    private void loadPrizesToRedis(String number) {
        // 从配置加载奖品到redis
        List<RoomLotteryProperty.Prize> prizes = roomLotteryProperty.getPrizes();
        if (ObjectUtil.isEmpty(prizes)) {
            throw new BusinessException("奖品配置错误");
        }
        roomLotteryProperty.getLotteries().forEach((k, v) -> {
            if (v.contains(number)) {
                String format = String.format(LOTTERY_PRIZE_COUNT_KEY, k, number);
                Boolean hasKey = stringRedisTemplate.hasKey(format);
                if (!ObjectUtil.equals(hasKey, true)) {
                    log.info("奖品配置加载到redis:{}", format);
                    prizes.forEach(p -> {
                        stringRedisTemplate.opsForHash().put(format, String.valueOf(p.getId()), String.valueOf(p.getCount()));
                    });
                } else {
                    log.info("奖品配置已存在redis:{}", format);
                }
            }
        });
    }


    public ContentErrorCode updateRoomLottery(UpdateRoomLotteryReq req) {
        Optional<SsRoomLottery> roomLottery = roomLotteryRepository.findById(req.getId());
        SsRoomLottery lottery = roomLottery.orElseThrow(() -> new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED));
        if (lottery.getStatus().equals(RoomLotteryType.INACTIVATED_1.getValue())) {
            if (ObjectUtil.equals(lottery.getName(), req.getName()) && ObjectUtil.equals(lottery.getPrizeId(), req.getPrizeId())
                    && ObjectUtil.equals(lottery.getMaxCount(), req.getMaxCount()) && ObjectUtil.equals(lottery.getDuration(), req.getDuration())) {
                return ContentErrorCode.SUCCESS;
            }
            lottery.setName(req.getName());
            lottery.setPrizeId(req.getPrizeId());
            Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
            if (ObjectUtil.isNotEmpty(req.getMaxCount()) && (req.getMaxCount() <= 0 || !pattern.matcher(req.getMaxCount().toString()).matches())) {
                return ContentErrorCode.PARAM_INVALID;
            }
            lottery.setMaxCount(req.getMaxCount());
            lottery.setDuration(req.getDuration());
            SsRoomLottery save = roomLotteryRepository.save(lottery);
            createRoomLotteryOperateRecord(req.getUserId(), save.getId(), RoomLotteryOperateRecord.EDIT_LOTTERY.getValue());
            return ContentErrorCode.SUCCESS;
        } else {
            return ContentErrorCode.FAILURE;
        }
    }

    public void createRoomLotteryOperateRecord(Integer operatorId, Long lotteryId, Integer operateType) {
        SsRoomLotteryOperateRecord ssRoomLotteryOperateRecord = new SsRoomLotteryOperateRecord();
        ssRoomLotteryOperateRecord.setOperatorId(operatorId);
        ssRoomLotteryOperateRecord.setLotteryId(lotteryId);
        ssRoomLotteryOperateRecord.setOperateType(operateType);
        roomLotteryOperateRecordRepository.save(ssRoomLotteryOperateRecord);
    }

    public SsRoomLottery findOne(Long lotteryId) {
        return roomLotteryRepository.findById(lotteryId).orElse(null);
    }
}

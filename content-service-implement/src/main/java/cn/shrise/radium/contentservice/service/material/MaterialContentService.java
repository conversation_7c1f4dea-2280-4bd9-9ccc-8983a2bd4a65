package cn.shrise.radium.contentservice.service.material;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.QSsMaterialContent;
import cn.shrise.radium.contentservice.entity.SsMaterialContent;
import cn.shrise.radium.contentservice.req.CreateMaterialReq;
import cn.shrise.radium.contentservice.req.UpdateMaterialReq;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor

public class MaterialContentService {

    private final JdbcTemplate jdbcTemplate;
    private final JPAQueryFactory queryFactory;
    private final QSsMaterialContent qSsMaterialContent = QSsMaterialContent.ssMaterialContent;

    public void createList(Long materialId, List<CreateMaterialReq.MaterialContent> materialContents) {
        if (ObjectUtil.isNotEmpty(materialContents)) {
            if (materialContents.size() < 6) {
                List<SsMaterialContent> ssMaterialContents = new ArrayList<>();
                for (CreateMaterialReq.MaterialContent materialContent : materialContents) {
                    SsMaterialContent ssMaterialContent = new SsMaterialContent();
                    BeanUtil.copyProperties(materialContent, ssMaterialContent);
                    ssMaterialContent.setMaterialId(materialId);
                    ssMaterialContent.setEnabled(true);
                    ssMaterialContents.add(ssMaterialContent);
                }
                String sql = SqlUtil.batchInsertSql(ssMaterialContents);
                jdbcTemplate.execute(sql);
            } else {
                throw new BusinessException("素材内容超出限制");
            }
        } else {
            throw new BusinessException("素材内容不能为空");
        }
    }

    public void updateList(Long materialId, List<UpdateMaterialReq.MaterialContent> materialContents) {
        if (ObjectUtil.isNotEmpty(materialContents)) {
            List<UpdateMaterialReq.MaterialContent> contents = materialContents.stream().filter(UpdateMaterialReq.MaterialContent::getEnabled).collect(Collectors.toList());
            if (contents.size() < 4) {
                List<SsMaterialContent> ssMaterialContents = new ArrayList<>();
                for (UpdateMaterialReq.MaterialContent materialContent : materialContents) {
                    SsMaterialContent ssMaterialContent = new SsMaterialContent();
                    BeanUtil.copyProperties(materialContent, ssMaterialContent);
                    ssMaterialContent.setMaterialId(materialId);
                    ssMaterialContent.setEnabled(true);
                    ssMaterialContents.add(ssMaterialContent);
                }
                String sql = SqlUtil.onDuplicateKeyUpdateSql(ssMaterialContents);
                jdbcTemplate.execute(sql);
            } else {
                throw new BusinessException("素材内容超出限制");
            }
        } else {
            throw new BusinessException("素材内容不能为空");
        }
    }

    public List<SsMaterialContent> batchGetMaterialContent(Collection<Long> materialIdList, Boolean enabled) {

        JPAQuery<SsMaterialContent> query = queryFactory.selectFrom(qSsMaterialContent)
                .where(qSsMaterialContent.materialId.in(materialIdList));
        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(qSsMaterialContent.enabled.eq(enabled));
        }
        return query.fetch();
    }

    public SsMaterialContent getMaterialContentById(Long materialContentId) {
        return queryFactory.selectFrom(qSsMaterialContent)
                .where(qSsMaterialContent.id.eq(materialContentId))
                .where(qSsMaterialContent.enabled.eq(true))
                .fetchFirst();
    }

    public List<SsMaterialContent> getContentByMaterialId(Long materialId) {
        return queryFactory.selectFrom(qSsMaterialContent)
                .where(qSsMaterialContent.materialId.eq(materialId))
                .where(qSsMaterialContent.enabled.eq(true))
                .fetch();
    }
}

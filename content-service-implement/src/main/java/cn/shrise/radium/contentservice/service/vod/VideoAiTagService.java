package cn.shrise.radium.contentservice.service.vod;

import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.constant.ShortVideoTagStatusEnum;
import cn.shrise.radium.contentservice.entity.QSsVideoAiTag;
import cn.shrise.radium.contentservice.entity.SsShortVideoTag;
import cn.shrise.radium.contentservice.entity.SsVideoAiTag;
import cn.shrise.radium.contentservice.repository.SsVideoAiTagRepository;
import cn.shrise.radium.contentservice.service.ShortVideoTagService;
import com.aliyuncs.vod.model.v20170321.SubmitAIJobResponse;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class VideoAiTagService {

    private final JPAQueryFactory queryFactory;
    private final SsVideoAiTagRepository ssVideoAiTagRepository;
    private final AliVodService aliVodService;
    private final QSsVideoAiTag qSsVideoAiTag = QSsVideoAiTag.ssVideoAiTag;
    private final ShortVideoTagService shortVideoTagService;
    private final JdbcTemplate jdbcTemplate;


    @Transactional
    public void submitAiTagTask(Integer companyType, String mediaId, Long videoId) {
        SsVideoAiTag newAiTag;
        Optional<SsVideoAiTag> optional = getVideoAiTag(companyType, mediaId);
        SsVideoAiTag ssVideoAiTag = optional.orElse(null);
        boolean hasSubmit = ssVideoAiTag != null && !Objects.equals(ssVideoAiTag.getStatus(), ShortVideoTagStatusEnum.TAG_STATUS_FAIL.getValue());

        if (!hasSubmit) {
            SubmitAIJobResponse aiVideoTagResponse = aliVodService.submitAIJob(mediaId, "AIVideoTag");
            String jobId = aiVideoTagResponse.getAIJobList().get(0).getJobId();
            newAiTag = SsVideoAiTag.builder()
                    .companyType(companyType)
                    .sourceId(mediaId)
                    .jobId(jobId)
                    .status(ShortVideoTagStatusEnum.TAG_STATUS_GETTING.getValue())
                    .message(ShortVideoTagStatusEnum.TAG_STATUS_GETTING.getMsg())
                    .enabled(true)
                    .build();
            createOrUpdateVideoAiTag(newAiTag);
        } else {
            newAiTag = ssVideoAiTag;
        }

        //兼容原有tag表
        copyToShortVideoTag(newAiTag, videoId);
    }

    public SsVideoAiTag saveVideoAiTag(SsVideoAiTag entity) {
        return ssVideoAiTagRepository.save(entity);
    }

    @Transactional
    public void createOrUpdateVideoAiTag(SsVideoAiTag entity) {
        String sql = SqlUtil.onDuplicateKeyUpdateSql(entity, true);
        jdbcTemplate.execute(sql);
    }

    public Optional<SsVideoAiTag> getVideoAiTag(Integer companyType, String sourceId) {
        return ssVideoAiTagRepository.findByCompanyTypeAndSourceId(companyType, sourceId);
    }

    /**
     * copy result from aiTag
     * @param aiTag
     * @param videoId
     */
    public void copyToShortVideoTag(SsVideoAiTag aiTag, Long videoId) {
        SsShortVideoTag shortVideoTag = shortVideoTagService.getShortVideoTag(videoId);
        SsShortVideoTag updateBody = SsShortVideoTag.builder()
                .companyType(aiTag.getCompanyType())
                .videoId(videoId)
                .tags(aiTag.getTags())
                .tagInfo(aiTag.getTagInfo())
                .status(aiTag.getStatus())
                .jobId(aiTag.getJobId())
                .code(aiTag.getCode())
                .message(aiTag.getMessage())
                .enabled(aiTag.getEnabled())
                .build();
        if (shortVideoTag == null) {
            shortVideoTagService.createShortVideoTag(updateBody);
        } else {
            shortVideoTagService.updateShortVideoTag(updateBody);
        }
    }
}

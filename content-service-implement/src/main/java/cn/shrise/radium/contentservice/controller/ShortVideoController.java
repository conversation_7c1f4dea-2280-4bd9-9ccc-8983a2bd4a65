package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.entity.SsShortVideo;
import cn.shrise.radium.contentservice.entity.SsShortVideoComment;
import cn.shrise.radium.contentservice.entity.SsShortVideoTag;
import cn.shrise.radium.contentservice.entity.SsVideoOperateRecord;
import cn.shrise.radium.contentservice.req.CreateTeamShortVideoReq;
import cn.shrise.radium.contentservice.req.UpdateTeamShortVideoCommentReq;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import cn.shrise.radium.contentservice.service.VideoOperateRecordService;
import cn.shrise.radium.contentservice.service.airec.AiRecService;
import cn.shrise.radium.contentservice.service.vod.AliVodService;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.SubmitAIJobResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

@RestController
@RequestMapping("video")
@RequiredArgsConstructor
public class ShortVideoController {

    private final AliVodService aliVodService;
    private final ShortVideoService shortVideoService;
    private final AiRecService aiRecService;
    private final RocketMqUtils rocketMqUtils;
    private final VideoOperateRecordService videoOperateRecordService;

    @GetMapping("getVideoList")
    @ApiOperation("内容管理-短视频")
    public PageResult<List<SsShortVideoResp>> getShortVideoList(
            @RequestParam(required = false) @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size
    ) {
        Map<Long, AiRecResultResp> recResultMap = null;
        if (ObjectUtils.isEmpty(analystId) && ObjectUtils.isNotEmpty(customerId)) {
            // 获取推荐结果
            String filterRuleString = aiRecService.getFilterRule(teamIds);
            List<AiRecResultResp> recResults = aiRecService.getAiRecResults(customerId, size, filterRuleString);
            if (ObjectUtils.isEmpty(recResults)) {
                return PageResult.success(new ArrayList<>(), Pagination.of(current, size, 0L));
            }
            recResultMap = recResults.stream().collect(Collectors.toMap(e -> Long.valueOf(e.getItemId()), e -> e));
        }
        return shortVideoService.getShortVideoList(analystId, recResultMap, customerId, current, size);
    }

    @GetMapping("getTeamVideoList")
    @ApiOperation("内容主短视频")
    public PageResult<List<SsShortVideoResp>> getTeamShortVideoList(
            @RequestParam @ApiParam("内容主可见范围") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("内容主id") Long teamId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size
    ) {
        return shortVideoService.getTeamShortVideoList(teamIds, teamId, customerId, current, size);
    }

    @PostMapping("delete")
    @ApiOperation("删除短视频")
    public BaseResult<String> deleteShortVideo(
            @RequestParam @ApiParam("视频id") Long id,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId
    ) {
        AiRecShortVideoResp resp = AiRecShortVideoResp.builder().id(id).enabled(false).build();
        rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, ContentServiceConst.MqTagType.AI_REC_SHORT_VIDEO, resp);
        return shortVideoService.deleteShortVideo(id, userId);
    }

    @GetMapping("getCommentList")
    @ApiOperation("获取短视频评论列表")
    public PageResult<List<SsShortVideoComment>> getShortVideoCommentList(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("是否精选") Boolean isChose,
            @RequestParam(required = false) @ApiParam("发送开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("发送结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return shortVideoService.getShortVideoCommentList(videoId, customerId, isChose, startTime, endTime, current, size);
    }

    @PostMapping("choseComment")
    @ApiOperation("精选短视频评论")
    public BaseResult<String> choseShortVideoComment(
            @RequestParam @ApiParam("评论id") Long commentId,
            @RequestParam @ApiParam("是否精选") Boolean isChose) {
        return shortVideoService.choseShortVideoComment(commentId, isChose);
    }

    @PostMapping("deleteComment")
    @ApiOperation("删除短视频评论")
    public BaseResult<String> deleteShortVideoComment(@RequestParam @ApiParam("评论id") Long commentId) {
        return shortVideoService.deleteShortVideoComment(commentId);
    }

    @PostMapping("comment")
    @ApiParam("短视频发表评论")
    public BaseResult<String> shortVideoComment(
            @RequestParam @ApiParam("视频ID") Long videoId,
            @RequestParam @ApiParam("评论人ID") Integer customerId,
            @RequestParam @ApiParam("评论内容") String content
    ) {
        shortVideoService.shortVideoComment(videoId, customerId, content);
        return BaseResult.success();
    }

    @PostMapping("create")
    @ApiOperation("创建视频")
    public BaseResult<CreateUploadVideoResponse> createVideo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("老师ID") Integer analystId,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam @ApiParam("文件标题") String fileTitle,
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestParam @ApiParam("分类id") Long cateId,
            @RequestParam(required = false) @ApiParam("转码模组id") String templateGroupId,
            @RequestParam(required = false) @ApiParam("描述") String description,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL
    ) {
        CreateUploadVideoResponse uploadVideo = aliVodService.createUploadVideo(fileTitle, fileName, cateId,
                templateGroupId, description, coverURL, "vod/sales/video/short/upload", null);
        SsShortVideo.SsShortVideoBuilder builder = SsShortVideo.builder()
                .analystId(analystId)
                .title(title)
                .companyType(companyType)
                .sourceId(uploadVideo.getVideoId())
                .bannerUrl(coverURL)
                .enabled(true);
        shortVideoService.createOne(builder.build());
        return BaseResult.success(uploadVideo);
    }

    @PostMapping("team")
    @ApiOperation("内容主上传视频")
    public BaseResult<CreateTeamShortVideoResp> createTeamShortVideo(
            @RequestBody @Valid CreateTeamShortVideoReq createTeamShortVideoReq
    ) {
        CreateTeamShortVideoResp resp = shortVideoService.createTeamShortVideo(createTeamShortVideoReq);
        return BaseResult.success(resp);
    }

    @PostMapping("update")
    @ApiOperation("编辑视频")
    public BaseResult<String> updateVideo(
            @RequestParam @ApiParam("视频ID") Long id,
            @RequestParam @ApiParam("标题") String title,
            @RequestParam(required = false) @ApiParam("封面url") String coverURL
    ) {
        shortVideoService.updateOne(id, title, null, coverURL, null);
        return BaseResult.success();
    }

    @PostMapping("like/or/unlike")
    @ApiOperation("视频点赞/取消点赞")
    public BaseResult<String> videoLikeOrUnlike(
            @RequestParam @ApiParam("视频ID") Long videoId,
            @RequestParam @ApiParam("用户ID") Integer customerId,
            @RequestParam @ApiParam("点赞/取消点赞") Boolean enabled
    ) {
        shortVideoService.videoLikeOrUnlike(videoId, customerId, enabled);
        return BaseResult.success();
    }

    @PostMapping("tag/create")
    @ApiOperation("创建短视频标签数据")
    public BaseResult<String> createShortVideoTag(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("mediaId") String mediaId,
            @RequestParam(required = false) @ApiParam("短视频Id") Long videoId) {
        SubmitAIJobResponse aiVideoTag = aliVodService.submitAIJob(mediaId, "AIVideoTag");
        shortVideoService.createShortVideoTag(companyType, mediaId, videoId, aiVideoTag.getAIJobList().get(0).getJobId());
        return BaseResult.success();
    }

    @GetMapping("tag/getTagByVideoIds")
    @ApiOperation("根据短视频id获取相应标签数据")
    public BaseResult<List<SsShortVideoTag>> getTagByShortVideoIds(@RequestParam @ApiParam("短视频Id") List<Long> videoIds) {
        return shortVideoService.getTagByShortVideoIds(videoIds);
    }

    @GetMapping("tag/reacquire")
    @ApiOperation("重新获取短视频标签数据")
    public BaseResult<String> reacquireShortVideoTag(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("mediaId") String mediaId,
            @RequestParam @ApiParam("短视频Id") Long videoId) {
        SubmitAIJobResponse aiVideoTag = aliVodService.submitAIJob(mediaId, "AIVideoTag");
        shortVideoService.updateShortVideoTagByVideoId(companyType, mediaId, videoId, aiVideoTag.getAIJobList().get(0).getJobId());
        return BaseResult.success();
    }

    @GetMapping("team/commentList")
    @ApiOperation("获取内容主视频评论列表")
    public PageResult<List<SsShortVideoComment>> getTeamVideoCommentList(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("评论开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("评论结束时间") Instant endTime,
            @RequestParam(required = false) @ApiParam("是否精选") Boolean isChose,
            @RequestParam(required = false) @ApiParam("是否审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size) {
        return shortVideoService.getTeamVideoCommentList(videoId, customerId, startTime, endTime, isChose, audit, auditStatus, current, size);
    }

    @PostMapping("team/comment")
    @ApiOperation("内容主视频发表评论")
    public BaseResult<String> teamVideoComment(
            @RequestParam @ApiParam("视频ID") Long videoId,
            @RequestParam @ApiParam("评论人ID") Integer customerId,
            @RequestParam @ApiParam("评论内容") String content,
            @RequestParam @ApiParam("公司类型") Integer companyType) {
        shortVideoService.teamVideoComment(videoId, customerId, content, companyType);
        return BaseResult.success();
    }

    @PutMapping("team/comment")
    @ApiOperation("内容主视频评论审核")
    public BaseResult<Void> updateTeamVideoComment(@RequestBody @Valid UpdateTeamShortVideoCommentReq updateTeamShortVideoCommentReq) {
        shortVideoService.updateTeamVideoComment(updateTeamShortVideoCommentReq);
        return BaseResult.successful();
    }

    @GetMapping("team/info")
    @ApiOperation("获取内容主视频信息")
    public BaseResult<TeamShortVideoResp> getTeamVideoInfo(
            @RequestParam @ApiParam("视频id") Long videoId,
            @RequestParam(required = false) @ApiParam("用户id") Integer customerId) {
        TeamShortVideoResp resp = shortVideoService.teamVideoInfo(videoId, customerId);
        return BaseResult.success(resp);
    }

    @GetMapping("team/list")
    @ApiOperation("视频列表")
    public PageResult<List<SsShortVideo>> getTeamVideoList(
            @RequestParam(required = false) @ApiParam("内容主ids") List<Long> teamIds,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("视频状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("排序字段") String field,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("状态") List<Integer> statusList,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return shortVideoService.getTeamVideoList(teamIds, companyType, status, startTime, endTime, field, isAsc, statusList, current, size);
    }

    @GetMapping("team/latest")
    @ApiOperation("获取内容主最新视频")
    public BaseResult<List<SsShortVideo>> getLatestTeamVideoList(
            @RequestParam Integer companyType,
            @RequestParam Long teamId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("数量") Long size
    ) {
        List<SsShortVideo> videoList = shortVideoService.getLatestTeamVideoList(companyType, teamId, size);
        return BaseResult.success(videoList);
    }

    @PostMapping("like/count/statistics")
    @ApiOperation("获取短视频点赞统计")
    public BaseResult<List<CountStatistics>> getShortVideoLikeCountStatistics(@RequestBody BatchReq<Long> req) {
        List<CountStatistics> statistics = shortVideoService.getShortVideoLikeCountStatistics(req.getValues());
        return BaseResult.success(statistics);
    }

    @PostMapping("like/operation/statistics")
    @ApiOperation("获取短视频点赞操作统计")
    public BaseResult<List<LikeStatistics>> getShortVideoLikeOperationStatistics(
            @RequestBody BatchReq<Long> req,
            @RequestParam(required = false) Integer userId) {
        List<LikeStatistics> statistics = shortVideoService.getShortVideoLikeOperationStatistics(req.getValues(), userId);
        return BaseResult.success(statistics);
    }

    @PostMapping("comment/statistics")
    @ApiOperation("获取短视频评论统计")
    public BaseResult<List<CountStatistics>> getShortVideoCommentCountStatistics(
            @RequestBody @Valid BatchReq<Long> req,
            @RequestParam(required = false) Integer auditStatus,
            @RequestParam(required = false) Boolean isChose) {
        List<CountStatistics> statistics = shortVideoService.getShortVideoCommentCountStatistics(req.getValues(), auditStatus, isChose);
        return BaseResult.success(statistics);
    }

    @GetMapping("operate/record")
    @ApiOperation("获取短视频操作记录")
    public BaseResult<List<SsVideoOperateRecord>> getVideoOperateRecord(
            @RequestParam @ApiParam("短视频id") Long videoId
    ) {
        List<SsVideoOperateRecord> record = videoOperateRecordService.getOperateRecord(videoId);
        return BaseResult.success(record);
    }
}

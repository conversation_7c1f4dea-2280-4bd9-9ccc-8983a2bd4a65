package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.common.util.StringUtil;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.NewsChannelTypeEnum;
import cn.shrise.radium.contentservice.properties.NewsConfigProperty;
import cn.shrise.radium.contentservice.req.UpdateNewsLikeReq;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.imservice.lindorm.entity.ImChatDialogLindorm;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jsoup.select.Elements;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新闻服务
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class NewsService {

    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;
    private final QSsNew qSsNew = QSsNew.ssNew;
    private final QSsNewsChannel qSsNewsChannel = QSsNewsChannel.ssNewsChannel;
    private final NewsConfigProperty newsConfigProperty;
    private final QSsNewsLike newsLike = QSsNewsLike.ssNewsLike;

    public BaseResult<List<NewsChannelResp>> getNewsChannels() {

        List<NewsConfigProperty.NewsChannel> channelList = newsConfigProperty.getChannel();

        List<String> channelNumbers = channelList.stream().map(NewsConfigProperty.NewsChannel::getNumber).collect(Collectors.toList());

        List<SsNewsChannel> channels = filterNewsChannel(null, channelNumbers);
        Map<String, Long> channelIdMap = channels.stream().collect(Collectors.toMap(SsNewsChannel::getChannelNumber, SsNewsChannel::getId));

        List<NewsChannelResp> resps = new ArrayList<>();
        for (NewsConfigProperty.NewsChannel newsChannel : channelList) {
            NewsChannelResp resp = new NewsChannelResp();
            resp.setChannelNumber(newsChannel.getNumber());
            resp.setName(newsChannel.getName());
            resp.setChannelId(channelIdMap.get(newsChannel.getNumber()));
            resps.add(resp);
        }

        return BaseResult.success(resps);
    }

    public List<SsNewsChannel> filterNewsChannel(List<Long> ids, Collection<String> channelNumbers) {

        JPAQuery<SsNewsChannel> query = queryFactory.select(qSsNewsChannel)
                .from(qSsNewsChannel)
                .where(qSsNewsChannel.isEnabled.eq(true));

        if (ObjectUtil.isNotEmpty(ids)) {
            query.where(qSsNewsChannel.id.in(ids));
        }

        if (ObjectUtil.isNotEmpty(channelNumbers)) {
            query.where(qSsNewsChannel.channelNumber.in(channelNumbers));
        }

        return query.fetch();
    }

    public PageResult<List<SsNew>> getNewsList(Collection<String> channelNumber, Integer current, Integer size) {

        List<SsNewsChannel> ssNewsChannels = filterNewsChannel(null, channelNumber);

        if (ObjectUtil.isEmpty(ssNewsChannels)) {
            return PageResult.empty(current, size);
        }

        List<Long> channelIds = ssNewsChannels.stream().map(SsNewsChannel::getId).collect(Collectors.toList());

        JPAQuery<SsNew> countQuery = queryFactory.select(qSsNew)
                .from(qSsNew)
                .where(qSsNew.isEnabled.eq(true))
                .where(qSsNew.channelId.in(channelIds));

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = countQuery.clone().select(countTemp).fetchOne();

        String sql = "select * from article_db.ss_news use index(idx_channel_enabled_publish) " +
                "where is_enabled is true and channel_id in (:channel_ids) " +
                "order by publish_Time desc, id desc " +
                "limit :current,:size";
        Map<String, Object> args = new HashMap<>();
        args.put("channel_ids", channelIds);
        args.put("current", (current - 1) * size);
        args.put("size", size);
        NamedParameterJdbcTemplate givenParamJdbcTemp = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<SsNew> result = givenParamJdbcTemp.query(sql, args, (rs, rowNum) -> SsNew.builder()
                .id(rs.getLong("id"))
                .gmtCreate(rs.getTimestamp("gmt_create").toInstant())
                .gmtModified(rs.getTimestamp("gmt_modified").toInstant())
                .publishTime(rs.getTimestamp("publish_time").toInstant())
                .channelId(rs.getLong("channel_id"))
                .title(rs.getString("title"))
                .brief(rs.getString("brief"))
                .sourceId(rs.getString("source_id"))
                .content(rs.getString("content"))
                .contentHtml(rs.getString("content_html"))
                .html(rs.getString("html"))
                .source(rs.getString("source"))
                .link(rs.getString("link"))
                .allList(rs.getString("all_list"))
                .imageUrls(rs.getString("image_urls"))
                .isEnabled(rs.getBoolean("is_enabled"))
                .titleImageUrl(rs.getString("title_image_url"))
                .viewCount(rs.getInt("view_count"))
                .codeType(rs.getString("code_type"))
                .stockCode(rs.getString("stock_code"))
                .stockName(rs.getString("stock_name"))
                .companyCode(rs.getString("company_code"))
                .pxChangeRate(rs.getString("px_change_rate"))
                .stockCode(rs.getString("stock_code"))
                .build());

        return PageResult.success(result, Pagination.of(current, size, total));
    }

    public PageResult<List<SsNew>> getNewsListByLabelList(Collection<String> channelNumber, Collection<String> labelList, Integer current, Integer size) {

        List<SsNewsChannel> ssNewsChannels = filterNewsChannel(null, channelNumber);

        if (ObjectUtil.isEmpty(ssNewsChannels)) {
            return PageResult.empty(current, size);
        }

        List<Long> channelIds = ssNewsChannels.stream().map(SsNewsChannel::getId).collect(Collectors.toList());

        JPAQuery<SsNew> query = queryFactory.select(qSsNew)
                .from(qSsNew)
                .where(qSsNew.isEnabled.eq(true))
                .where(qSsNew.companyCode.in(labelList));

        if (ObjectUtil.isEmpty(channelIds)) {
            return PageResult.empty(current, size);
        }

        query.where(qSsNew.channelId.in(channelIds));

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsNew.publishTime.desc(), qSsNew.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public PageResult<List<SsNew>> getNewsListByLabel(Collection<String> channelNumber, String label, Integer current, Integer size) {

        List<SsNewsChannel> ssNewsChannels = filterNewsChannel(null, channelNumber);

        List<Long> channelIds = ssNewsChannels.stream().map(SsNewsChannel::getId).collect(Collectors.toList());

        JPAQuery<SsNew> query = queryFactory.select(qSsNew)
                .from(qSsNew)
                .where(qSsNew.isEnabled.eq(true))
                .where(qSsNew.companyCode.eq(label));

        if (ObjectUtil.isEmpty(channelIds)) {
            return PageResult.empty(current, size);
        }

        query.where(qSsNew.channelId.in(channelIds));

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsNew.publishTime.desc(), qSsNew.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public SsNew findLastNews(Long id, Instant publishTime, Long channelId) {
        return queryFactory.select(qSsNew)
                .from(qSsNew)
                .where(qSsNew.isEnabled.eq(true))
                .where(qSsNew.channelId.eq(channelId))
                .where(qSsNew.publishTime.goe(publishTime))
                .where(qSsNew.id.gt(id))
                .orderBy(qSsNew.publishTime.asc(), qSsNew.id.asc())
                .fetchFirst();
    }

    public SsNew findNextNews(Long id, Instant publishTime, Long channelId) {
        return queryFactory.select(qSsNew)
                .from(qSsNew)
                .where(qSsNew.isEnabled.eq(true))
                .where(qSsNew.channelId.eq(channelId))
                .where(qSsNew.publishTime.loe(publishTime))
                .where(qSsNew.id.lt(id))
                .orderBy(qSsNew.publishTime.desc(), qSsNew.id.desc())
                .fetchFirst();
    }

    public BaseResult<SsNew> getNewsById(Long id) {
        JPAQuery<SsNew> query = queryFactory.select(qSsNew)
                .from(qSsNew)
                .where(qSsNew.id.eq(id))
                .where(qSsNew.isEnabled.eq(true));
        return BaseResult.success(query.fetchOne());
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveSsNew(List<SsNew> ssNewList) {
        if (ObjectUtil.isNotEmpty(ssNewList)) {
            String sql = SqlUtil.batchInsertSql(ssNewList);
            jdbcTemplate.execute(sql);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveSsNewsChannel(List<SsNewsChannel> ssNewsChannelList) {
        if (ObjectUtil.isNotEmpty(ssNewsChannelList)) {
            String sql = SqlUtil.batchInsertSql(ssNewsChannelList);
            jdbcTemplate.execute(sql);
        }
    }

    public Map<String, SsNewsChannel> getSsNewsChannelsMap() {
        List<SsNewsChannel> fetch = queryFactory.select(qSsNewsChannel).from(qSsNewsChannel).fetch();
        return fetch.stream().collect(Collectors.toMap(SsNewsChannel::getChannelNumber, r -> r));
    }

    public List<String> getNewSourceIds(List<String> idList) {
        return queryFactory.select(qSsNew.sourceId).from(qSsNew).where(qSsNew.sourceId.in(idList)).fetch();
    }

    public List<SsNew> getUploadFailNews() {
        List<Long> channelIds = queryFactory.select(qSsNewsChannel.id).from(qSsNewsChannel).where(qSsNewsChannel.type.eq(NewsChannelTypeEnum.NCT_AliYy.getValue())).fetch();
        return queryFactory.select(qSsNew).from(qSsNew).where(qSsNew.channelId.in(channelIds)).where(qSsNew.isEnabled.eq(false)).fetch();
    }

    public List<SsNew> getUploadFailYrNews() {
        List<Long> channelIds = queryFactory.select(qSsNewsChannel.id).from(qSsNewsChannel).where(qSsNewsChannel.type.eq(NewsChannelTypeEnum.NCT_Yr.getValue())).fetch();
        return queryFactory.select(qSsNew).from(qSsNew).where(qSsNew.channelId.in(channelIds)).where(qSsNew.isEnabled.eq(false)).fetch();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateNewsBySourceId(String sourceId, Elements p, String imageUrls, String titleImageUrl) {
        queryFactory.update(qSsNew)
                .set(qSsNew.contentHtml, p.toString())
                .set(qSsNew.imageUrls, imageUrls)
                .set(qSsNew.titleImageUrl, titleImageUrl)
                .set(qSsNew.isEnabled, true)
                .where(qSsNew.sourceId.eq(sourceId)).execute();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateNewsBySourceId(SsNew ssNew) {
        queryFactory.update(qSsNew)
                .set(qSsNew.contentHtml, ssNew.getContentHtml())
                .set(qSsNew.isEnabled, ssNew.getIsEnabled())
                .where(qSsNew.sourceId.eq(ssNew.getSourceId())).execute();
    }

    @Transactional
    public Boolean updateNewsLike(UpdateNewsLikeReq req) {
        SsNewsLike entity = SsNewsLike.builder()
                .customerId(req.getUserId())
                .newsId(req.getNewsId())
                .enabled(req.getLike())
                .build();

        String sql = SqlUtil.onDuplicateKeyUpdateSql(entity);
        int affected = jdbcTemplate.update(sql);
        return affected > 0;
    }

    public List<NewsLikeCount> getNewsLikeCount(Collection<Long> newsIds) {
        if (ObjectUtils.isEmpty(newsIds)) {
            return Collections.emptyList();
        }
        NumberExpression<Long> countExpression = newsLike.customerId.count().as("count");
        Set<Long> newsIdSet = new HashSet<>(newsIds);
        List<Tuple> tuples = queryFactory.select(Projections.tuple(newsLike.newsId,
                        countExpression))
                .from(newsLike)
                .where(newsLike.newsId.in(newsIdSet))
                .where(newsLike.enabled.eq(true))
                .groupBy(newsLike.newsId)
                .fetch();

        return tuples.stream().map(tuple -> NewsLikeCount.builder()
                        .id(tuple.get(newsLike.newsId))
                        .count(tuple.get(countExpression))
                        .build())
                .collect(Collectors.toList());
    }

    public List<NewsUserIsLike> getNewsUserIsLike(Integer userId, Collection<Long> newsIds) {

        if (ObjectUtils.isEmpty(newsIds)) {
            return Collections.emptyList();
        }

        Set<Long> newsIdSet = new HashSet<>(newsIds);
        JPAQuery<Long> query = queryFactory.select(newsLike.newsId)
                .from(newsLike)
                .where(newsLike.newsId.in(newsIdSet))
                .where(newsLike.enabled.eq(true));

        if (ObjectUtil.isNotNull(userId)) {
            query.where(newsLike.customerId.eq(userId));
        } else {
            return null;
        }

        List<Long> likedList = query.fetch();

        Set<Long> likedSet = new HashSet<>(likedList);

        return newsIdSet.stream().map(e -> {
            boolean isLike = likedSet.contains(e);
            return NewsUserIsLike.builder()
                    .id(e)
                    .isLike(isLike)
                    .build();
        }).collect(Collectors.toList());
    }

    @Transactional
    public void addViewCount(Long newsId) {
        Integer viewCount = queryFactory.select(qSsNew.viewCount).from(qSsNew).where(qSsNew.id.eq(newsId)).fetchOne();
        if (ObjectUtil.isNull(viewCount)) {
            viewCount = 1;
        } else {
            viewCount += 1;
        }
        queryFactory.update(qSsNew)
                .set(qSsNew.viewCount, viewCount)
                .where(qSsNew.id.eq(newsId))
                .execute();
    }
}

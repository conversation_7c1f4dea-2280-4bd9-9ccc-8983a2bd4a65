package cn.shrise.radium.contentservice.consumer;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.constant.VodBehaviorConstant;
import cn.shrise.radium.contentservice.constant.VodResultEventConstant;
import cn.shrise.radium.contentservice.entity.SsVideoTranscode;
import cn.shrise.radium.contentservice.resp.VodCallbackResp;
import cn.shrise.radium.contentservice.service.SsSalesVideoRecordService;
import cn.shrise.radium.contentservice.service.VideoTranscodeService;
import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.core.utils.Objects;
import com.aliyun.openservices.ons.api.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import static cn.shrise.radium.contentservice.constant.VideoTranscodeConstant.StreamTranscodeComplete;
import static cn.shrise.radium.contentservice.constant.VideoTranscodeConstant.TranscodeComplete;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = ContentServiceConst.CONTENT_TOPIC, consumerGroup = ContentServiceConst.MqGroupType.GID_SsSalesVideoRecord,
        selectorType = ExpressionType.TAG, selectorExpression = VodBehaviorConstant.SsSalesVideoRecord)
public class SsSalesVideoRecordConsumer implements MessageListener {

    private final SsSalesVideoRecordService salesVideoRecordService;
    private final VideoTranscodeService videoTranscodeService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        VodCallbackResp resp = JSON.parseObject(new String(message.getBody()), VodCallbackResp.class);
        if (ObjectUtils.isNotEmpty(resp)) {
            String eventType = resp.getEventType();
            log.info("eventType: {}", eventType);
            String videoId = resp.getVideoId();
            if (Objects.equals(resp.getEventType(), VodResultEventConstant.FileUploadComplete)) {
                salesVideoRecordService.enableOne(resp.getVideoId());
            } else if (Objects.equals(eventType, VodResultEventConstant.TranscodeComplete)) {
                //转码完成
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(TranscodeComplete)
                        .event(new String(message.getBody()))
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            } else if (Objects.equals(eventType, VodResultEventConstant.StreamTranscodeComplete)) {
                //单个完成
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(StreamTranscodeComplete)
                        .event(new String(message.getBody()))
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            }
        }
        return Action.CommitMessage;
    }
}

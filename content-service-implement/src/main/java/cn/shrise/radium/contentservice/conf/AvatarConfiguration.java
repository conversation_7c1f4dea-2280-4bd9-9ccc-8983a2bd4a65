package cn.shrise.radium.contentservice.conf;

import cn.shrise.radium.common.properties.AccessKeyOtherProperties;
import cn.shrise.radium.contentservice.properties.AvatarConfigProperty;
import com.aliyun.avatar20220130.Client;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 数字人相关配置
 *
 * <AUTHOR>
 */

@Configuration
@Component
@RequiredArgsConstructor
@Slf4j
@EnableConfigurationProperties({AccessKeyOtherProperties.class, AvatarConfigProperty.class})
public class AvatarConfiguration {

    private final AccessKeyOtherProperties accessKeyOtherProperties;
    private final AvatarConfigProperty avatarConfigProperty;


    @Bean
    @ConditionalOnMissingBean
    public Client avatarClient() throws Exception {

        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKeyOtherProperties.getAccessKeyId())
                .setAccessKeySecret(accessKeyOtherProperties.getAccessKeySecret());
        config.endpoint = avatarConfigProperty.getEndpoint();
        return new com.aliyun.avatar20220130.Client(config);
    }

}

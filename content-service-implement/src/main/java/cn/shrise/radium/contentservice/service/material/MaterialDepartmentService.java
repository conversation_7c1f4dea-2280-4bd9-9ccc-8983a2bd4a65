package cn.shrise.radium.contentservice.service.material;

import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.QSsMaterialDepartment;
import cn.shrise.radium.contentservice.entity.SsMaterialDepartment;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MaterialDepartmentService {
    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;
    private final QSsMaterialDepartment qSsMaterialDepartment = QSsMaterialDepartment.ssMaterialDepartment;

    @Transactional
    public void createList(Long materialId, List<Integer> departmentList) {
        List<SsMaterialDepartment> infoList = departmentList.stream()
                .map(t -> SsMaterialDepartment.builder().materialId(materialId).departmentId(t).build()).collect(Collectors.toList());
        String sql = SqlUtil.batchInsertSql(infoList);
        jdbcTemplate.execute(sql);
    }

    @Transactional
    public void deleteByMaterial(Long materialId) {
        queryFactory.delete(qSsMaterialDepartment).where(qSsMaterialDepartment.materialId.eq(materialId)).execute();
    }

    public List<SsMaterialDepartment> getDepartmentByMaterial(List<Long> materialList) {
        return queryFactory.selectFrom(qSsMaterialDepartment)
                .where(qSsMaterialDepartment.materialId.in(materialList))
                .fetch();
    }
}

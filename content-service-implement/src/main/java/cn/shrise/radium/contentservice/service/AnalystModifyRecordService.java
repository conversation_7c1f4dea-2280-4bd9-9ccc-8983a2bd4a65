package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.contentservice.entity.QSsAnalystModifyRecord;
import cn.shrise.radium.contentservice.entity.SsAnalystModifyRecord;
import cn.shrise.radium.contentservice.repository.AnalystModifyRecordRepository;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AnalystModifyRecordService {

    private final JPAQueryFactory queryFactory;
    private final QSsAnalystModifyRecord qSsAnalystModifyRecord = QSsAnalystModifyRecord.ssAnalystModifyRecord;
    private final AnalystModifyRecordRepository analystModifyRecordRepository;

    public PageResult<List<SsAnalystModifyRecord>> getAnalystModifyRecordPage(Integer analystId, Integer current, Integer size) {
        JPAQuery<SsAnalystModifyRecord> query = queryFactory.selectFrom(qSsAnalystModifyRecord)
                .where(qSsAnalystModifyRecord.analystId.eq(analystId));
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(qSsAnalystModifyRecord.gmtCreate.asc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public void createOne(SsAnalystModifyRecord info) {
        analystModifyRecordRepository.save(info);
    }
}

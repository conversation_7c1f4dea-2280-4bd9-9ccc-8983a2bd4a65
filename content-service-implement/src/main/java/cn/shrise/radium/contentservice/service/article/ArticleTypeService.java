package cn.shrise.radium.contentservice.service.article;

import cn.shrise.radium.contentservice.entity.QSsArticleType;
import cn.shrise.radium.contentservice.entity.SsArticleType;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleTypeService {

    private final JPAQueryFactory queryFactory;
    private final QSsArticleType qSsArticleType = QSsArticleType.ssArticleType;

    public List<SsArticleType> findAllByNumbers(Integer companyType, Set<String> numbers) {
        return queryFactory.selectFrom(qSsArticleType)
                .where(qSsArticleType.companyType.eq(companyType))
                .where(qSsArticleType.number.in(numbers))
                .fetch();
    }
}

package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordExistedException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.contentservice.entity.QSsSoftwarePackage;
import cn.shrise.radium.contentservice.entity.SsSoftwarePackage;
import cn.shrise.radium.contentservice.repository.SoftwarePackageRepository;
import cn.shrise.radium.contentservice.req.SoftwarePackageReq;
import cn.shrise.radium.contentservice.resp.SoftwarePackageResp;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cn.shrise.radium.contentservice.constant.PackageType.JCE_ANDROID;

/**
 * @Author: tangjiajun
 * @Date: 2024/6/24 11:22
 * @Desc:
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class SoftwarePackageService {
    private final JPAQueryFactory queryFactory;
    private final QSsSoftwarePackage ssSoftwarePackage = QSsSoftwarePackage.ssSoftwarePackage;
    private final SoftwarePackageRepository softwarePackageRepository;

    public Page<SoftwarePackageResp> packageList(Integer packageType, PageRequest of) {
        BooleanExpression booleanExpression = ssSoftwarePackage.packageType.eq(packageType);
        Page<SsSoftwarePackage> softwarePackages = softwarePackageRepository.findAll(booleanExpression, of);
        List<SsSoftwarePackage> packageList = softwarePackages.getContent();
        List<SoftwarePackageResp> respList = new ArrayList<>();
        for (SsSoftwarePackage pack : packageList) {
            SoftwarePackageResp packageResp = new SoftwarePackageResp();
            BeanUtils.copyProperties(pack, packageResp);
            respList.add(packageResp);
        }
        return new PageImpl<>(respList, of, softwarePackages.getTotalElements());
    }

    public void addPackage(SoftwarePackageReq softwarePackageReq) {
        SsSoftwarePackage softwarePackage = softwarePackageRepository.findByPackageTypeAndVersion(softwarePackageReq.getPackageType(), softwarePackageReq.getVersion());
        if (softwarePackage != null) {
            throw new RecordExistedException("该类型软件包版本号已存在");
        }
        List<String> nameList = queryFactory.select(ssSoftwarePackage.name)
                .from(ssSoftwarePackage)
                .where(ssSoftwarePackage.name.eq(softwarePackageReq.getName())
                        .and(ssSoftwarePackage.packageType.eq(softwarePackageReq.getPackageType())))
                .fetch();
        if (!nameList.isEmpty() && softwarePackageReq.getPackageType() != JCE_ANDROID.getCode()) {
            throw new RecordExistedException("软件包名重复");
        }
        softwarePackage = SsSoftwarePackage.builder()
                .packageType(softwarePackageReq.getPackageType())
                .content(softwarePackageReq.getContent())
                .url(softwarePackageReq.getUrl())
                .version(softwarePackageReq.getVersion())
                .enabled(false)
                .name(softwarePackageReq.getName())
                .build();
        softwarePackageRepository.save(softwarePackage);
    }

    @Transactional
    public BaseResult<Void> updatePackageEnable(Long id, Boolean enabled) {
        Optional<SsSoftwarePackage> softwarePackage = softwarePackageRepository.findById(id);
        if (!softwarePackage.isPresent()) {
            throw new RecordNotExistedException("软件包不存在");
        }
        softwarePackage.get().setEnabled(enabled);
        softwarePackageRepository.save(softwarePackage.get());
        return BaseResult.successful();
    }

    public SoftwarePackageResp packageInfo(Long id) {
        Optional<SsSoftwarePackage> softwarePackage = softwarePackageRepository.findById(id);
        if (!softwarePackage.isPresent()) {
            throw new RecordNotExistedException("软件包不存在");
        }
        return queryFactory.select(Projections.bean(
                        SoftwarePackageResp.class,
                        ssSoftwarePackage.id,
                        ssSoftwarePackage.content,
                        ssSoftwarePackage.packageType,
                        ssSoftwarePackage.url,
                        ssSoftwarePackage.enabled,
                        ssSoftwarePackage.version,
                        ssSoftwarePackage.gmtCreate,
                        ssSoftwarePackage.name))
                .from(ssSoftwarePackage)
                .where(ssSoftwarePackage.id.eq(id))
                .fetchOne();
    }

    public SoftwarePackageResp LatestPackage(Integer packageType) {
        List<SsSoftwarePackage> softwarePackage = queryFactory.selectFrom(ssSoftwarePackage)
                .where(ssSoftwarePackage.packageType.eq(packageType))
                .fetch();
        if (softwarePackage.isEmpty()) {
            throw new RecordNotExistedException("未传入软件包类型参数或该参数不正确");
        }
        List<String> versionList = queryFactory.select(ssSoftwarePackage.version)
                .from(ssSoftwarePackage)
                .where(ssSoftwarePackage.packageType.eq(packageType).and(ssSoftwarePackage.enabled.eq(true)))
                .fetch();
        if (versionList.isEmpty()) {
            throw new RecordNotExistedException("该类型暂时没有软件包上架");
        }
        //版本号排序
        versionList.sort((v1, v2) -> compareVersions(v2, v1));
        String latestPackageVersion = versionList.get(0);
        SoftwarePackageResp latestPackage = queryFactory.select(Projections.bean(
                        SoftwarePackageResp.class,
                        ssSoftwarePackage.id,
                        ssSoftwarePackage.content,
                        ssSoftwarePackage.packageType,
                        ssSoftwarePackage.url,
                        ssSoftwarePackage.enabled,
                        ssSoftwarePackage.version,
                        ssSoftwarePackage.gmtCreate,
                        ssSoftwarePackage.name))
                .from(ssSoftwarePackage)
                .where(ssSoftwarePackage.version.eq(latestPackageVersion).and(ssSoftwarePackage.packageType.eq(packageType)))
                .fetchOne();
        return latestPackage;
    }

    private int compareVersions(String v1, String v2) {
        String[] arr1 = v1.split("\\.");
        String[] arr2 = v2.split("\\.");

        int minLength = Math.min(arr1.length, arr2.length);

        for (int i = 0; i < minLength; i++) {
            long num1 = Long.parseLong(arr1[i]);
            long num2 = Long.parseLong(arr2[i]);
            if (num1 != num2) {
                return Long.compare(num1, num2);
            }
        }
        return Long.compare(arr2.length, arr1.length);
    }
}

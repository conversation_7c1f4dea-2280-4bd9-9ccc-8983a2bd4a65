package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsRoomLottery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface RoomLotteryRepository extends JpaRepository<SsRoomLottery, Long>, QuerydslPredicateExecutor<SsRoomLottery> {
    SsRoomLottery findAllById(Long id);

    List<SsRoomLottery> findAllByRoomId(Long roomId);

    List<String> findNumberByRoomId(Long roomId);
}

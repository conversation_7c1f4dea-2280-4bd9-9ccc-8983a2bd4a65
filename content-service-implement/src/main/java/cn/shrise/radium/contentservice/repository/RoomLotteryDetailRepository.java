package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsRoomLotteryDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Repository
public interface RoomLotteryDetailRepository  extends JpaRepository<SsRoomLotteryDetail, Long>, QuerydslPredicateExecutor<SsRoomLotteryDetail> {
    /**
     * 根据lotteryId 找出所有参与本次抽奖活动的用户
     * @param lotteryId
     * @return
     */
    List<SsRoomLotteryDetail> findAllByLotteryId(Long lotteryId);
}

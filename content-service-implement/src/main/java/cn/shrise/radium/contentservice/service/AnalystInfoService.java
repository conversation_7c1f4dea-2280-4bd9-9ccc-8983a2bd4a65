package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.constant.AnalystModifyConstant;
import cn.shrise.radium.contentservice.constant.AnalystModifyMsgConstant;
import cn.shrise.radium.contentservice.constant.AuditStatusConstant;
import cn.shrise.radium.contentservice.entity.QSsAnalystInfo;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.entity.SsAnalystModifyRecord;
import cn.shrise.radium.contentservice.repository.AnalystRepository;
import cn.shrise.radium.contentservice.req.CreateAnalystAuditApplyReq;
import cn.shrise.radium.contentservice.req.EditAnalystInfoReq;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;

import static cn.shrise.radium.contentservice.enums.ContentErrorCode.CERTIFICATENO_EXISTED;
import static cn.shrise.radium.contentservice.enums.ContentErrorCode.NAME_EXISTED;

@Service
@RequiredArgsConstructor
public class AnalystInfoService {
    private final AnalystRepository analystRepository;
    private final JPAQueryFactory queryFactory;
    private final QSsAnalystInfo qSsAnalystInfo = QSsAnalystInfo.ssAnalystInfo;
    private final AnalystModifyRecordService analystModifyRecordService;
    private final AnalystAuditApplyService analystAuditApplyService;

    public Optional<SsAnalystInfo> findOneById(@NonNull Integer id) {
        return analystRepository.findById(id);
    }

    public List<SsAnalystInfo> getAnalystInfoList(Collection<Integer> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        Set<Integer> idSet = new HashSet<>(ids);
        return queryFactory.selectFrom(qSsAnalystInfo)
                .where(qSsAnalystInfo.id.in(idSet))
                .orderBy(qSsAnalystInfo.id.desc())
                .fetch();
    }

    public SsAnalystInfo getAnalystInfo(Integer id) {
        return queryFactory.selectFrom(qSsAnalystInfo)
                .where(qSsAnalystInfo.id.eq(id))
                .fetchOne();
    }

    public PageResult<List<SsAnalystInfo>> getAnalystInfoListPage(Integer companyType, Boolean enabled, Boolean gwShow,
                                                                  Integer auditStatus, Integer current, Integer size) {

        JPAQuery<SsAnalystInfo> query = queryFactory.select(qSsAnalystInfo)
                .from(qSsAnalystInfo);

        if (ObjectUtils.isNotEmpty(companyType)) {
            query.where(qSsAnalystInfo.companyType.eq(companyType));
        }
        if (ObjectUtils.isNotEmpty(enabled)) {
            query.where(qSsAnalystInfo.isEnabled.eq(enabled));
        }
        if (ObjectUtils.isNotEmpty(gwShow)) {
            query.where(qSsAnalystInfo.gwShow.eq(gwShow));
        }
        if (ObjectUtils.isNotEmpty(auditStatus)) {
            query.where(qSsAnalystInfo.auditStatus.eq(auditStatus));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsAnalystInfo.createTime.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    /**
     * 创建老师
     */
    @Transactional
    public BaseResult<String> createAnalystInfo(Integer companyType, Integer userId, EditAnalystInfoReq req) {

        // 检查参数长度限制
        checkAnalystInfoLengthLimit(req);
        // 检查信息唯一性限制
        checkAnalystInfo(companyType, null, req.getName(), req.getCertificateNo());

        SsAnalystInfo info = SsAnalystInfo.builder()
                .name(req.getName())
                .gender(req.getGender())
                .birthday(req.getBirthday())
                .certificateNo(req.getCertificateNo())
                .introduce(req.getIntroduce())
                .avatarUrl(req.getAvatarUrl())
                .titleImageUrl(req.getTitleImageUrl())
                .title(req.getTitle())
                .smAccount(req.getSmAccount())
                .companyType(companyType)
                .isEnabled(true)
                .gwShow(req.getGwShow())
                .gwAvatarUrl(req.getGwAvatarUrl())
                .gwRanking(req.getGwRanking())
                .createTime(Instant.now())
                .updateTime(Instant.now())
                .auditStatus(AuditStatusConstant.AUDIT_EXECUTING)
                .build();

        SsAnalystInfo save = analystRepository.save(info);

        req.setId(save.getId());
        CreateAnalystAuditApplyReq applyReq = CreateAnalystAuditApplyReq.builder()
                .analystId(req.getId())
                .operateId(userId)
                .operateType(AnalystModifyConstant.CREATE)
                .analystInfo(JSON.toJSONString(req))
                .build();
        analystAuditApplyService.createAnalystAuditApply(applyReq);

        return BaseResult.success();
    }

    private void checkAnalystInfoLengthLimit(EditAnalystInfoReq req) {

        if (ObjectUtils.isNotEmpty(req.getName()) && req.getName().length() > 10) {
            throw new BusinessException("老师姓名长度超出10字符限制");
        }
        if (ObjectUtils.isNotEmpty(req.getCertificateNo()) && req.getCertificateNo().length() > 30) {
            throw new BusinessException("资格证号编号长度超出30字符限制");
        }
        if (ObjectUtils.isEmpty(req.getIntroduce())) {
            throw new BusinessException("老师介绍不能为空");
        }
        if (ObjectUtils.isNotEmpty(req.getTitle()) && req.getTitle().length() > 10) {
            throw new BusinessException("title长度超出10字符限制");
        }
        if (ObjectUtils.isNotEmpty(req.getSmAccount()) && req.getSmAccount().length() > 10) {
            throw new BusinessException("自媒体账号长度超出10字符限制");
        }
    }

    /**
     * 检查老师信息唯一性
     */
    public void checkAnalystInfo(Integer companyType, Integer id, String name, String certificateNo) {

        if (ObjectUtils.isNotEmpty(name)) {
            SsAnalystInfo analystInfo = queryFactory.select(qSsAnalystInfo)
                    .from(qSsAnalystInfo)
                    .where(qSsAnalystInfo.name.eq(name))
                    .where(qSsAnalystInfo.auditStatus.ne(AuditStatusConstant.AUDIT_NOT_PASS))
                    .where(qSsAnalystInfo.companyType.eq(companyType))
                    .fetchOne();
            if (ObjectUtils.isNotEmpty(analystInfo)) {
                if (ObjectUtils.isEmpty(id) || !analystInfo.getId().equals(id)) {
                    throw new BusinessException(NAME_EXISTED);
                }
            }
        }
        if (ObjectUtils.isNotEmpty(certificateNo)) {
            SsAnalystInfo info = queryFactory.select(qSsAnalystInfo)
                    .from(qSsAnalystInfo)
                    .where(qSsAnalystInfo.certificateNo.eq(certificateNo))
                    .where(qSsAnalystInfo.auditStatus.ne(AuditStatusConstant.AUDIT_NOT_PASS))
                    .fetchOne();
            if (ObjectUtils.isNotEmpty(info)) {
                if (ObjectUtils.isEmpty(id) || !info.getId().equals(id)) {
                    throw new BusinessException(CERTIFICATENO_EXISTED);
                }
            }
        }

    }

    /**
     * 更新老师信息
     *
     * @param req
     * @return
     */
    @Transactional
    public BaseResult<String> updateAnalystInfo(Integer companyType, Integer userId, EditAnalystInfoReq req) {

        // 检查参数长度限制
        checkAnalystInfoLengthLimit(req);
        // 检查信息唯一性
        checkAnalystInfo(companyType, req.getId(), req.getName(), req.getCertificateNo());

//        SsAnalystInfo analystInfo = getAnalystInfo(req.getId());
//        if (!ObjectUtil.equals(analystInfo.getAuditStatus(), AuditStatusConstant.AUDIT_PASS)) {
//            JPAUpdateClause where = queryFactory.update(qSsAnalystInfo)
//                    .set(qSsAnalystInfo.name, req.getName())
//                    .set(qSsAnalystInfo.gender, req.getGender())
//                    .set(qSsAnalystInfo.birthday, req.getBirthday())
//                    .set(qSsAnalystInfo.certificateNo, req.getCertificateNo())
//                    .set(qSsAnalystInfo.introduce, req.getIntroduce())
//                    .set(qSsAnalystInfo.avatarUrl, req.getAvatarUrl())
//                    .set(qSsAnalystInfo.titleImageUrl, req.getTitleImageUrl())
//                    .set(qSsAnalystInfo.title, req.getTitle())
//                    .set(qSsAnalystInfo.smAccount, req.getSmAccount())
//                    .set(qSsAnalystInfo.gwShow, req.getGwShow())
//                    .set(qSsAnalystInfo.gwAvatarUrl, req.getGwAvatarUrl())
//                    .set(qSsAnalystInfo.gwRanking, req.getGwRanking())
//                    .set(qSsAnalystInfo.auditStatus, AuditStatusConstant.AUDIT_EXECUTING)
//                    .set(qSsAnalystInfo.updateTime, Instant.now())
//                    .where(qSsAnalystInfo.id.eq(req.getId()));
//            where.execute();
//        }

        CreateAnalystAuditApplyReq applyReq = CreateAnalystAuditApplyReq.builder()
                .analystId(req.getId())
                .operateId(userId)
                .operateType(AnalystModifyConstant.MODIFY)
                .analystInfo(JSON.toJSONString(req))
                .build();
        analystAuditApplyService.createAnalystAuditApply(applyReq);
        return BaseResult.success();
    }

    /**
     * 启用/禁用老师
     */
    @Transactional
    public BaseResult<String> deleteAnalystInfo(Integer userId, Integer id, Boolean enabled) {
        queryFactory.update(qSsAnalystInfo)
                .where(qSsAnalystInfo.id.eq(id))
                .set(qSsAnalystInfo.isEnabled, enabled)
                .set(qSsAnalystInfo.updateTime, Instant.now())
                .execute();

        SsAnalystModifyRecord modifyRecord = SsAnalystModifyRecord.builder()
                .analystId(id)
                .operateId(userId)
                .operateContent(ObjectUtil.equals(enabled, true) ? AnalystModifyMsgConstant.ENABLE : AnalystModifyMsgConstant.DISABLE)
                .build();
        analystModifyRecordService.createOne(modifyRecord);
        return BaseResult.success();
    }

    public List<SsAnalystInfo> getGwAnalystInfoList() {
        return queryFactory.selectFrom(qSsAnalystInfo)
                .where(qSsAnalystInfo.isEnabled.eq(true))
                .where(qSsAnalystInfo.gwShow.eq(true))
                .where(qSsAnalystInfo.auditStatus.eq(AuditStatusConstant.AUDIT_PASS))
                .orderBy(qSsAnalystInfo.gwRanking.asc(), qSsAnalystInfo.id.asc())
                .fetch();
    }
}

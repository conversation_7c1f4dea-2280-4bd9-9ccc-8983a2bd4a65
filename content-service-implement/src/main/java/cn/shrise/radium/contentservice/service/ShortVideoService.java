package cn.shrise.radium.contentservice.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.JsonUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.common.util.UrlUtil;
import cn.shrise.radium.contentservice.constant.*;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.repository.SsShortVideoCommentRepository;
import cn.shrise.radium.contentservice.repository.SsShortVideoLikeRepository;
import cn.shrise.radium.contentservice.repository.SsShortVideoRepository;
import cn.shrise.radium.contentservice.repository.SsShortVideoTagRepository;
import cn.shrise.radium.contentservice.req.CreateTeamShortVideoReq;
import cn.shrise.radium.contentservice.req.TextModerationReq;
import cn.shrise.radium.contentservice.req.UpdateTeamShortVideoCommentReq;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.contentservice.service.green.GreenService;
import cn.shrise.radium.contentservice.service.vod.AliVodService;
import cn.shrise.radium.contentservice.service.vod.VideoAiTagService;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcContentTeam;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.vod.model.v20170321.SubmitAIJobResponse;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.contentservice.constant.VideoOperateRecordConstant.AUDIT_PASS_PUBLISHED;
import static cn.shrise.radium.contentservice.constant.VideoOperateRecordConstant.AUDIT_PASS_RELEASE;
import static cn.shrise.radium.contentservice.constant.ArticleStatusEnum.*;
import static cn.shrise.radium.contentservice.constant.AuditStatusConstant.*;
import static cn.shrise.radium.contentservice.constant.VodResultEventConstant.StreamTranscodeComplete;
import static cn.shrise.radium.contentservice.constant.VodResultEventConstant.TranscodeComplete;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShortVideoService {

    private final JPAQueryFactory queryFactory;
    private final AnalystInfoService analystInfoService;
    private final QSsShortVideo qSsShortVideo = QSsShortVideo.ssShortVideo;
    private final QSsShortVideoComment qSsShortVideoComment = QSsShortVideoComment.ssShortVideoComment;
    private final QSsShortVideoLike qSsShortVideoLike = QSsShortVideoLike.ssShortVideoLike;
    private final QSsShortVideoTag qSsShortVideoTag = QSsShortVideoTag.ssShortVideoTag;
    private final SsShortVideoRepository ssShortVideoRepository;
    private final SsShortVideoCommentRepository ssShortVideoCommentRepository;
    private final SsShortVideoLikeRepository ssShortVideoLikeRepository;
    private final SsShortVideoTagRepository ssShortVideoTagRepository;
    private final CommonProperties commonProperties;

    private final GreenService greenService;
    private final AliVodService aliVodService;
    private final VideoTranscodeService videoTranscodeService;
    private final UserClient userClient;
    private final VideoAiTagService videoAiTagService;
    private final VideoOperateRecordService videoOperateRecordService;
    private final RocketMqUtils rocketMqUtils;

    /**
     * 短视频列表页
     */
    public PageResult<List<SsShortVideoResp>> getShortVideoList(Integer analystId, Map<Long, AiRecResultResp> recResultMap, Integer customerId, Integer current, Integer size) {
        JPAQuery<SsShortVideo> query = queryFactory.select(qSsShortVideo).from(qSsShortVideo)
                .where(qSsShortVideo.enabled.eq(true));
        Long total;
        if (ObjectUtils.isNotEmpty(customerId)) {
            query.where(qSsShortVideo.videoDuration.isNotNull());
        }
        if (ObjectUtils.isEmpty(analystId)) {
            List<Long> pkIdList = ListUtil.toList(recResultMap.keySet());
            query.where(qSsShortVideo.id.in(pkIdList));
            NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
            total = query.clone().select(countTemp).fetchOne();
            query.limit(size);
        } else {
            query.where(qSsShortVideo.analystId.eq(analystId));
            NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
            total = query.clone().select(countTemp).fetchOne();
            if (ObjectUtil.isAllNotEmpty(current, size)) {
                query.offset((long) (current - 1) * size).limit(size);
            }
        }

        query.orderBy(qSsShortVideo.gmtCreate.desc());


        List<SsShortVideo> ssShortVideos = query.fetch();
        List<Long> videoIds = ssShortVideos.stream().map(SsShortVideo::getId).collect(Collectors.toList());
        Map<Long, Long> likeCount = getLikeCount(videoIds);
        Map<Long, Long> commentCount = getCommentCount(videoIds, customerId);
        List<SsShortVideoResp> ssShortVideoResps = new ArrayList<>();
        ssShortVideos.forEach(e -> {
            SsShortVideoResp ssShortVideoResp = new SsShortVideoResp();
            BeanUtil.copyProperties(e, ssShortVideoResp);
            if (likeCount.containsKey(ssShortVideoResp.getId())) {
                ssShortVideoResp.setLikeCount(likeCount.get(ssShortVideoResp.getId()));
            }
            if (commentCount.containsKey(ssShortVideoResp.getId())) {
                ssShortVideoResp.setCommentCount(commentCount.get(ssShortVideoResp.getId()));
            }
            if (ObjectUtil.isNotNull(recResultMap) && recResultMap.containsKey(ssShortVideoResp.getId())) {
                ssShortVideoResp.setTraceInfo(recResultMap.get(ssShortVideoResp.getId()).getTraceInfo());
                ssShortVideoResp.setTraceId(recResultMap.get(ssShortVideoResp.getId()).getTraceId());
            }
            ssShortVideoResps.add(ssShortVideoResp);
        });
        if (ObjectUtil.isNotNull(customerId)) {
            Map<Long, Boolean> isLike = isLike(videoIds, customerId);
            if (ObjectUtil.isNotEmpty(isLike)) {
                ssShortVideoResps.forEach(e -> {
                    if (isLike.containsKey(e.getId())) {
                        e.setIsLike(isLike.get(e.getId()));
                    }
                });
            }
        }

        return PageResult.success(ssShortVideoResps, Pagination.of(current, size, total));
    }

    /**
     * 视频点赞量
     */
    public Map<Long, Long> getLikeCount(Collection<Long> videoIds) {
        Map<Long, Long> map = new HashMap<>();
        List<Tuple> fetch = queryFactory.select(qSsShortVideoLike.videoId, qSsShortVideoLike.count()).from(qSsShortVideoLike)
                .where(qSsShortVideoLike.videoId.in(videoIds))
                .where(qSsShortVideoLike.enabled.eq(true))
                .groupBy(qSsShortVideoLike.videoId)
                .fetch();
        if (ObjectUtil.isNotEmpty(fetch)) {
            for (Tuple tuple : fetch) {
                map.put(tuple.get(qSsShortVideoLike.videoId), tuple.get(qSsShortVideoLike.count()));
            }
        }
        return map;
    }

    /**
     * 是否点赞
     */
    public Map<Long, Boolean> isLike(List<Long> videoIds, Integer customerId) {
        Map<Long, Boolean> map = new HashMap<>();
        List<Tuple> fetch = queryFactory.select(qSsShortVideoLike.videoId, qSsShortVideoLike.enabled).from(qSsShortVideoLike)
                .where(qSsShortVideoLike.videoId.in(videoIds))
                .where(qSsShortVideoLike.customerId.eq(customerId))
                .fetch();
        if (ObjectUtil.isNotEmpty(fetch)) {
            for (Tuple tuple : fetch) {
                map.put(tuple.get(qSsShortVideoLike.videoId), tuple.get(qSsShortVideoLike.enabled));
            }
        }
        return map;
    }

    /**
     * 评论数
     */
    public Map<Long, Long> getCommentCount(List<Long> videoIds, Integer customerId) {
        Map<Long, Long> map = new HashMap<>();
        JPAQuery<Tuple> tupleJPAQuery = queryFactory.select(qSsShortVideoComment.videoId, qSsShortVideoComment.count()).from(qSsShortVideoComment)
                .where(qSsShortVideoComment.videoId.in(videoIds))
                .where(qSsShortVideoComment.enabled.eq(true))
                .where(qSsShortVideoComment.auditStatus.eq(AUDIT_PASS))
                .groupBy(qSsShortVideoComment.videoId);
        if (ObjectUtil.isNotNull(customerId)) {
            tupleJPAQuery.where(qSsShortVideoComment.customerId.eq(customerId).or(qSsShortVideoComment.isChose.eq(true)));
        }
        List<Tuple> fetch = tupleJPAQuery.fetch();
        if (ObjectUtil.isNotEmpty(fetch)) {
            for (Tuple tuple : fetch) {
                map.put(tuple.get(qSsShortVideoComment.videoId), tuple.get(qSsShortVideoComment.count()));
            }
        }
        return map;
    }

    /**
     * 删除短视频
     */
    @Transactional
    public BaseResult<String> deleteShortVideo(Long id, Integer userId) {
        queryFactory.update(qSsShortVideo)
                .where(qSsShortVideo.id.eq(id))
                .set(qSsShortVideo.status, ArticleStatusEnum.AS_Deleted.getValue())
                .execute();
        UcUsers users = userClient.getUser(userId).getData();
        videoOperateRecordService.saveRecord(id, userId, VideoOperateRecordEnum.DELETE_VIDEO, users.getRealName(), VideoOperateRecordConstant.DELETE);
        return BaseResult.success();
    }

    /**
     * 短视频评论列表
     */
    public PageResult<List<SsShortVideoComment>> getShortVideoCommentList(Long videoId, Integer customerId, Boolean isChose, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {

        JPAQuery<SsShortVideoComment> query = queryFactory.select(qSsShortVideoComment)
                .from(qSsShortVideoComment)
                .where(qSsShortVideoComment.enabled.eq(true))
                .where(qSsShortVideoComment.videoId.eq(videoId));
        if (ObjectUtil.isNotEmpty(customerId)) {
            query.where(qSsShortVideoComment.customerId.eq(customerId).or(qSsShortVideoComment.isChose.eq(true)));
        }

        if (ObjectUtil.isNotEmpty(isChose)) {
            query.where(qSsShortVideoComment.isChose.eq(isChose));
        }

        if (ObjectUtil.isNotEmpty(startTime)) {
            query.where(qSsShortVideoComment.gmtCreate.goe(startTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (ObjectUtil.isNotEmpty(endTime)) {
            query.where(qSsShortVideoComment.gmtCreate.lt(endTime.atStartOfDay().plusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsShortVideoComment.commentTime.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    /**
     * 删除短视频评论
     */
    @Transactional
    public BaseResult<String> deleteShortVideoComment(Long commentId) {
        queryFactory.update(qSsShortVideoComment)
                .where(qSsShortVideoComment.id.eq(commentId))
                .set(qSsShortVideoComment.enabled, false)
                .execute();
        return BaseResult.success();
    }

    /**
     * 精选短视频评论
     */
    @Transactional
    public BaseResult<String> choseShortVideoComment(Long commentId, Boolean isChose) {
        queryFactory.update(qSsShortVideoComment)
                .where(qSsShortVideoComment.id.eq(commentId))
                .set(qSsShortVideoComment.isChose, isChose)
                .execute();
        return BaseResult.success();
    }

    /**
     * 短视频发表评论
     */
    @Transactional
    public void shortVideoComment(Long videoId, Integer customerId, String content) {
        SsShortVideoComment build = SsShortVideoComment.builder()
                .videoId(videoId)
                .customerId(customerId)
                .commentContent(content)
                .commentTime(Instant.now())
                .isChose(false)
                .enabled(true)
                .build();
        ssShortVideoCommentRepository.save(build);
    }

    @Transactional
    public SsShortVideo createOne(SsShortVideo req) {
        return ssShortVideoRepository.save(req);
    }

    /**
     * 音/视频上传完成
     */
    @Transactional
    public void enableOne(String videoId, Boolean enabled, String duration, String coverUrl) {
        JPAUpdateClause updateClause = queryFactory.update(qSsShortVideo)
                .where(qSsShortVideo.sourceId.eq(videoId));
        if (ObjectUtil.isNotNull(duration)) {
            updateClause.set(qSsShortVideo.videoDuration, duration);
        }
        if (ObjectUtil.isNotNull(coverUrl)) {
            updateClause.set(qSsShortVideo.bannerUrl, coverUrl);
        }
        if (ObjectUtil.isNotNull(enabled)) {
            updateClause.set(qSsShortVideo.enabled, enabled);
        }
        updateClause.execute();
    }

    @Transactional
    public void updateOne(Long id, String title, String sourceId, String coverURL, Boolean enable) {
        JPAUpdateClause updateClause = queryFactory.update(qSsShortVideo)
                .where(qSsShortVideo.id.eq(id));
        updateClause.set(qSsShortVideo.title, title);
        if (ObjectUtil.isNotNull(sourceId)) {
            updateClause.set(qSsShortVideo.sourceId, sourceId);
        }
        if (ObjectUtil.isNotNull(coverURL)) {
            updateClause.set(qSsShortVideo.bannerUrl, coverURL);
        }
        if (ObjectUtil.isNotNull(enable)) {
            updateClause.set(qSsShortVideo.enabled, enable);
        }
        updateClause.execute();
    }

    /**
     * 视频点赞/取消点赞
     */
    @Transactional
    public void videoLikeOrUnlike(Long videoId, Integer customerId, Boolean enabled) {
        SsShortVideoLike ssShortVideoLike = queryFactory.select(qSsShortVideoLike).from(qSsShortVideoLike).where(qSsShortVideoLike.videoId.eq(videoId)).where(qSsShortVideoLike.customerId.eq(customerId)).fetchOne();
        if (ObjectUtil.isNotEmpty(ssShortVideoLike)) {
            queryFactory.update(qSsShortVideoLike)
                    .where(qSsShortVideoLike.videoId.eq(videoId))
                    .where(qSsShortVideoLike.customerId.eq(customerId))
                    .set(qSsShortVideoLike.enabled, enabled)
                    .execute();
        } else {
            SsShortVideoLike build = SsShortVideoLike.builder().videoId(videoId).customerId(customerId).enabled(enabled).build();
            ssShortVideoLikeRepository.save(build);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createShortVideoTag(Integer companyType, String mediaId, Long videoId, String jobId) {
        if (ObjectUtil.isEmpty(videoId)) {
            SsShortVideo video = getShortVideoBySourceId(mediaId);
            videoId = video.getId();
            companyType = video.getCompanyType();
        }
        SsShortVideoTag videoTag = SsShortVideoTag.builder()
                .companyType(companyType)
                .videoId(videoId)
                .jobId(jobId)
                .status(ShortVideoTagStatusEnum.TAG_STATUS_GETTING.getValue())
                .message(ShortVideoTagStatusEnum.TAG_STATUS_GETTING.getMsg())
                .enabled(true)
                .build();
        ssShortVideoTagRepository.save(videoTag);
    }

    /**
     * 根据视频源Id查询
     *
     * @param sourceId
     * @return
     */
    public SsShortVideo getShortVideoBySourceId(String sourceId) {
        return queryFactory.select(qSsShortVideo)
                .from(qSsShortVideo)
                .where(qSsShortVideo.sourceId.eq(sourceId))
                .fetchOne();
    }

    /**
     * 根据短视频id获取相应标签数据
     */
    public BaseResult<List<SsShortVideoTag>> getTagByShortVideoIds(List<Long> videoIds) {
        JPAQuery<SsShortVideoTag> query = queryFactory.select(qSsShortVideoTag)
                .from(qSsShortVideoTag)
                .where(qSsShortVideoTag.videoId.in(videoIds))
                .where(qSsShortVideoTag.enabled.eq(true));
        return BaseResult.success(query.fetch());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateShortVideoTag(SsShortVideoTag shortVideoTag) {

        JPAUpdateClause updateClause = queryFactory.update(qSsShortVideoTag);
        if (ObjectUtil.isNotEmpty(shortVideoTag.getId())) {
            updateClause.where(qSsShortVideoTag.id.eq(shortVideoTag.getId()));
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getVideoId())) {
            updateClause.where(qSsShortVideoTag.videoId.eq(shortVideoTag.getVideoId()));
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getJobId())) {
            updateClause.where(qSsShortVideoTag.jobId.eq(shortVideoTag.getJobId()));
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getMessage())) {
            updateClause.set(qSsShortVideoTag.message, shortVideoTag.getMessage());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getTags())) {
            updateClause.set(qSsShortVideoTag.tags, shortVideoTag.getTags());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getTagInfo())) {
            updateClause.set(qSsShortVideoTag.tagInfo, shortVideoTag.getTagInfo());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getStatus())) {
            updateClause.set(qSsShortVideoTag.status, shortVideoTag.getStatus());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getCode())) {
            updateClause.set(qSsShortVideoTag.code, shortVideoTag.getCode());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getMessage())) {
            updateClause.set(qSsShortVideoTag.message, shortVideoTag.getMessage());
        }
        if (ObjectUtil.isNotEmpty(shortVideoTag.getEnabled())) {
            updateClause.set(qSsShortVideoTag.enabled, shortVideoTag.getEnabled());
        }
        updateClause.execute();
    }

    public AiRecShortVideoResp buildAiRecShortVideoResp(String mediaId, AIVideoTagResultResp videoTagResult) {
        AiRecShortVideoResp resp = new AiRecShortVideoResp();
        SsShortVideo video = getShortVideoBySourceId(mediaId);
        BeanUtil.copyProperties(video, resp);
        Integer analystId = resp.getAnalystId();
        List<SsAnalystInfo> analystList = analystInfoService.getAnalystInfoList(Collections.singletonList(analystId));
        resp.setAnalystName(analystList.get(0).getName());
        // 标签处理
        List<String> tags = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(videoTagResult.getCategory())) {
            tags.addAll(videoTagResult.getCategory().stream().map(AIVideoTagResultResp.AIVideoTagCategoryItem::getTag).collect(Collectors.toList()));
        }
        if (ObjectUtil.isNotEmpty(videoTagResult.getPerson())) {
            tags.addAll(videoTagResult.getPerson().stream().map(AIVideoTagResultResp.AIVideoTagResultItem::getTag).collect(Collectors.toList()));
        }
        String tagsJson = JsonUtils.toJson(tags.toArray());
        resp.setTags(tagsJson);
        return resp;
    }

    public AiRecShortVideoResp buildAiRecShortVideoResp(String mediaId, String tags) {
        AiRecShortVideoResp resp = new AiRecShortVideoResp();
        SsShortVideo video = getShortVideoBySourceId(mediaId);
        BeanUtil.copyProperties(video, resp);

        Integer analystId = video.getAnalystId();
        Long teamId = video.getTeamId();

        if (Objects.nonNull(analystId)) {
            List<SsAnalystInfo> analystList = analystInfoService.getAnalystInfoList(Collections.singletonList(analystId));
            resp.setAnalystName(analystList.get(0).getName());
        } else {
            UcContentTeam contentTeam = userClient.getContentTeam(teamId).orElse(null);
            String analystName = contentTeam != null ? contentTeam.getName() : null;
            resp.setAnalystName(analystName);
        }
        resp.setTags(tags);
        resp.setTeamId(teamId);
        return resp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateShortVideoTagByVideoId(Integer companyType, String mediaId, Long videoId, String jobId) {
        SsShortVideoTag shortVideoTag = queryFactory.select(qSsShortVideoTag)
                .from(qSsShortVideoTag)
                .where(qSsShortVideoTag.videoId.eq(videoId)).fetchOne();
        if (ObjectUtil.isNotEmpty(shortVideoTag)) {
            queryFactory.update(qSsShortVideoTag)
                    .where(qSsShortVideoTag.videoId.eq(videoId))
                    .set(qSsShortVideoTag.jobId, jobId)
                    .set(qSsShortVideoTag.status, ShortVideoTagStatusEnum.TAG_STATUS_GETTING.getValue())
                    .set(qSsShortVideoTag.message, ShortVideoTagStatusEnum.TAG_STATUS_GETTING.getMsg())
                    .execute();
        } else {
            createShortVideoTag(companyType, mediaId, videoId, jobId);
        }
    }

    /**
     * 内容主视频评论列表
     */
    public PageResult<List<SsShortVideoComment>> getTeamVideoCommentList(Long videoId, Integer customerId, Instant startTime, Instant endTime,
                                                                         Boolean isChose, Boolean audit, Integer auditStatus, Integer current, Integer size) {

        JPAQuery<SsShortVideoComment> query = queryFactory.select(qSsShortVideoComment)
                .from(qSsShortVideoComment)
                .where(qSsShortVideoComment.enabled.eq(true))
                .where(qSsShortVideoComment.videoId.eq(videoId));

        if (ObjectUtil.isNotEmpty(customerId)) {
            query.where(qSsShortVideoComment.auditStatus.eq(AUDIT_PASS));
        }

        if (ObjectUtil.isNotEmpty(isChose)) {
            query.where(qSsShortVideoComment.isChose.eq(isChose));
        }

        if (Objects.equals(audit, true)) {
            if (ObjectUtil.isNotEmpty(auditStatus)) {
                query.where(qSsShortVideoComment.auditStatus.eq(auditStatus));
            } else {
                query.where(qSsShortVideoComment.auditStatus.in(AUDIT_PASS, AUDIT_NOT_PASS));
            }
        } else if (Objects.equals(audit, false)) {
            query.where(qSsShortVideoComment.auditStatus.eq(AUDIT_EXECUTING));
        } else {
            query.where(qSsShortVideoComment.auditStatus.eq(AUDIT_PASS));
        }

        if (ObjectUtil.isNotEmpty(startTime)) {
            query.where(qSsShortVideoComment.commentTime.goe(startTime));
        }
        if (ObjectUtil.isNotEmpty(endTime)) {
            query.where(qSsShortVideoComment.commentTime.loe(endTime));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsShortVideoComment.commentTime.desc(), qSsShortVideoComment.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    /**
     * 内容主视频发表评论
     */
    @Transactional(rollbackFor = Exception.class)
    public void teamVideoComment(Long videoId, Integer customerId, String content, Integer companyType) {
        SsShortVideoComment build = SsShortVideoComment.builder()
                .videoId(videoId)
                .customerId(customerId)
                .commentContent(content)
                .commentTime(Instant.now())
                .auditTime(Instant.now())
                .enabled(true)
                .isChose(false)
                .build();
        TextModerationReq textModerationReq = TextModerationReq.builder()
                .companyType(companyType)
                .service("comment_detection")
                .content(content)
                .dataId(UUID.randomUUID().toString())
                .build();

        TextModerationResp resp = greenService.textModeration(textModerationReq);
        build.setAuditStatus(AUDIT_EXECUTING);
        if (!resp.isPassed()) {
            build.setAuditStatus(AUDIT_NOT_PASS);
        }
        build.setResultId(resp.getResultId());
        ssShortVideoCommentRepository.save(build);
    }

    @Transactional
    public void updateTeamVideoComment(UpdateTeamShortVideoCommentReq updateTeamShortVideoCommentReq) {
        JPAUpdateClause updateClause = queryFactory.update(qSsShortVideoComment)
                .where(qSsShortVideoComment.id.eq(updateTeamShortVideoCommentReq.getId()));
        if (ObjectUtil.isNotEmpty(updateTeamShortVideoCommentReq.getIsChose())) {
            updateClause = updateClause.set(qSsShortVideoComment.isChose, updateTeamShortVideoCommentReq.getIsChose());
        }
        if (ObjectUtil.isNotEmpty(updateTeamShortVideoCommentReq.getAuditStatus())) {
            updateClause = updateClause.set(qSsShortVideoComment.auditStatus, updateTeamShortVideoCommentReq.getAuditStatus());
        }
        if (ObjectUtil.isNotEmpty(updateTeamShortVideoCommentReq.getAuditorId())) {
            updateClause = updateClause.set(qSsShortVideoComment.auditorId, updateTeamShortVideoCommentReq.getAuditorId());
            updateClause = updateClause.set(qSsShortVideoComment.auditTime, Instant.now());
        }

        updateClause.execute();
    }

    /**
     * 内容主视频信息
     */
    public TeamShortVideoResp teamVideoInfo(Long videoId, Integer customerId) {
        Optional<SsShortVideo> videoOptional = ssShortVideoRepository.findById(videoId);
        if (videoOptional.isPresent()) {
            SsShortVideo ssShortVideo = videoOptional.get();
            TeamShortVideoResp resp = new TeamShortVideoResp();
            BeanUtil.copyProperties(ssShortVideo, resp);
            JPAQuery<Long> query = queryFactory.select(qSsShortVideoComment.id.count())
                    .from(qSsShortVideoComment)
                    .where(qSsShortVideoComment.enabled.eq(true))
                    .where(qSsShortVideoComment.videoId.eq(videoId))
                    .where((qSsShortVideoComment.auditStatus.eq(AUDIT_PASS)));
            resp.setCommentCount(query.fetchOne());
            Set<Integer> customerSet = new HashSet<>(queryFactory.select(qSsShortVideoLike.customerId)
                    .from(qSsShortVideoLike)
                    .where(qSsShortVideoLike.videoId.eq(videoId)
                            .and(qSsShortVideoLike.enabled.eq(true)))
                    .fetch());
            resp.setLikeCount((long) customerSet.size());
            if (ObjectUtil.isNotEmpty(customerId)) {
                resp.setIsLike(customerSet.contains(customerId));
            }
            return resp;
        }
        return null;
    }

    public PageResult<List<SsShortVideo>> getTeamVideoList(List<Long> teamIds, Integer companyType, Integer status, LocalDate startTime, LocalDate endTime, String field, Boolean isAsc, List<Integer> statusList, Integer current, Integer size) {
        JPAQuery<SsShortVideo> query = queryFactory.selectFrom(qSsShortVideo)
                .where(qSsShortVideo.companyType.eq(companyType));

        if (ObjectUtils.isNotEmpty(statusList)) {
            query.where(qSsShortVideo.status.in(statusList));
        }

        if (ObjectUtils.isNotEmpty(status)) {
            query.where(qSsShortVideo.status.eq(status));
        }

        if (ObjectUtil.isNotEmpty(teamIds)) {
            query.where(qSsShortVideo.teamId.in(teamIds));
        } else {
            query.where(qSsShortVideo.teamId.isNotNull());
        }

        if (ObjectUtil.isAllNotEmpty(startTime, endTime)) {
            query.where(qSsShortVideo.gmtCreate.between(DateUtils.getDayOfStart(startTime), DateUtils.getDayOfStart(endTime)));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        if (ObjectUtil.isAllNotEmpty(field, isAsc)) {
            if ("videoDuration".equals(field)) {
                NumberExpression<Double> doubleExpression = qSsShortVideo.videoDuration.castToNum(Double.class);
                if (isAsc) {
                    // 创建NumberExpression，指定排序规则为默认的自然排序
                    query.orderBy(doubleExpression.asc(), qSsShortVideo.id.asc());
                } else {
                    query.orderBy(doubleExpression.desc(), qSsShortVideo.id.desc());
                }
            } else if ("gmtCreate".equals(field)) {
                if (isAsc) {
                    query.orderBy(qSsShortVideo.gmtCreate.asc(), qSsShortVideo.id.asc());
                } else {
                    query.orderBy(qSsShortVideo.gmtCreate.desc(), qSsShortVideo.id.desc());
                }
            }
        } else {
            query.orderBy(qSsShortVideo.gmtCreate.desc(), qSsShortVideo.id.desc());
        }


        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    /**
     * 内容主短视频列表
     */
    public PageResult<List<SsShortVideoResp>> getTeamShortVideoList(List<Long> teamIds, Long teamId, Integer customerId, Integer current, Integer size) {

        JPAQuery<SsShortVideo> query = queryFactory.selectFrom(qSsShortVideo)
                .where(qSsShortVideo.enabled.eq(true));
        if (!teamIds.isEmpty()) {
            query.where(qSsShortVideo.teamId.in(teamIds));
        }
        if (ObjectUtil.isNotNull(teamId)) {
            query.where(qSsShortVideo.teamId.eq(teamId));
        } else {
            query.where(qSsShortVideo.teamId.isNotNull());
        }
        query.where(qSsShortVideo.status.eq(ArticleStatusEnum.AS_Released.getValue()));

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsShortVideo.releaseTime.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        List<SsShortVideo> records = query.fetch();
        List<Long> videoIds = records.stream().map(SsShortVideo::getId).collect(Collectors.toList());
        Map<Long, Long> likeCount = getLikeCount(videoIds);
        Map<Long, Long> commentCount = getCommentCount(videoIds, null);
        List<SsShortVideoResp> ssShortVideoResps = new ArrayList<>();
        records.forEach(e -> {
            SsShortVideoResp ssShortVideoResp = new SsShortVideoResp();
            BeanUtil.copyProperties(e, ssShortVideoResp);
            if (likeCount.containsKey(ssShortVideoResp.getId())) {
                ssShortVideoResp.setLikeCount(likeCount.get(ssShortVideoResp.getId()));
            }
            if (commentCount.containsKey(ssShortVideoResp.getId())) {
                ssShortVideoResp.setCommentCount(commentCount.get(ssShortVideoResp.getId()));
            }
            ssShortVideoResps.add(ssShortVideoResp);
        });
        if (ObjectUtil.isNotNull(customerId)) {
            Map<Long, Boolean> isLike = isLike(videoIds, customerId);
            if (ObjectUtil.isNotEmpty(isLike)) {
                ssShortVideoResps.forEach(e -> {
                    if (isLike.containsKey(e.getId())) {
                        e.setIsLike(isLike.get(e.getId()));
                    }
                });
            }
        }

        return PageResult.success(ssShortVideoResps, Pagination.of(current, size, total));
    }

    public List<SsShortVideo> getLatestTeamVideoList(Integer companyType, Long teamId, Long size) {
        JPAQuery<SsShortVideo> query = queryFactory.selectFrom(qSsShortVideo)
                .where(qSsShortVideo.companyType.eq(companyType))
                .where(qSsShortVideo.enabled.eq(true));
        query.where(qSsShortVideo.status.eq(ArticleStatusEnum.AS_Released.getValue()));
        if (ObjectUtils.isNotEmpty(teamId)) {
            query.where(qSsShortVideo.teamId.eq(teamId));
        }

        return query.orderBy(qSsShortVideo.gmtCreate.desc(), qSsShortVideo.id.desc())
                .limit(size)
                .fetch();

    }

    @Transactional
    public CreateTeamShortVideoResp createTeamShortVideo(CreateTeamShortVideoReq createTeamShortVideoReq) {
        String bannerUrl = createTeamShortVideoReq.getBannerUrl();
        Long teamId = createTeamShortVideoReq.getTeamId();
        Integer creatorId = createTeamShortVideoReq.getCreatorId();
        Integer companyType = createTeamShortVideoReq.getCompanyType();
        String videoId = createTeamShortVideoReq.getVideoId();
        String duration = createTeamShortVideoReq.getDuration();
        Instant preReleaseTime = createTeamShortVideoReq.getPreReleaseTime();
        String ossCoverUrl = UrlUtil.concat(commonProperties.getOssDomain(), bannerUrl);

        SsShortVideo shortVideo = SsShortVideo.builder()
                .teamId(teamId)
                .title(createTeamShortVideoReq.getTitle())
                .companyType(companyType)
                .sourceId(videoId)
                .bannerUrl(ossCoverUrl)
                .enabled(true)
                .creatorId(creatorId)
                .videoDuration(duration)
                .status(ArticleStatusEnum.AS_Reviewing.getValue())
                .preReleaseTime(preReleaseTime)
                .build();
        SsShortVideo entity = createOne(shortVideo);

        UcUsers users = userClient.getUser(creatorId).getData();
        videoOperateRecordService.saveRecord(entity.getId(), creatorId, VideoOperateRecordEnum.CREATE_VIDEO, users.getRealName(), null);

        //提交智能标签请求
        videoAiTagService.submitAiTagTask(companyType, videoId, entity.getId());

        return CreateTeamShortVideoResp.builder()
                .id(entity.getId())
                .teamId(teamId)
                .title(createTeamShortVideoReq.getTitle())
                .companyType(companyType)
                .bannerUrl(bannerUrl)
                .creatorId(creatorId)
                .sourceId(videoId)
                .build();
    }

    @Transactional
    public void shortVideoUploadResult(String body) {
        JSONObject res = JSONObject.parseObject(body);
        log.info("短视频上传结果,body:{}", body);
        if (Objects.equals(res.getString("Status"), "success")) {
            String eventType = res.getString("EventType");
            log.info("eventType: {}", eventType);
            if (Objects.equals(res.getString("EventType"), "FileUploadComplete")) {//视频上传完成事件
                this.enableOne(res.getString("VideoId"), true, null, null);
                // 提交智能标签请求
                SubmitAIJobResponse aiVideoTag = aliVodService.submitAIJob(res.getString("VideoId"), "AIVideoTag");
                this.createShortVideoTag(null, res.getString("VideoId"), null, aiVideoTag.getAIJobList().get(0).getJobId());
            } else if (Objects.equals(res.getString("EventType"), "VideoAnalysisComplete")) {//视频分析完成事件
                this.enableOne(res.getString("VideoId"), null, String.valueOf(res.getFloat("Duration")), null);
            } else if (Objects.equals(res.getString("EventType"), "SnapshotComplete")) {//视频截图完成事件
                //判断是否有封面
                SsShortVideo ssShortVideo = this.getShortVideoBySourceId(res.getString("VideoId"));
                if (ObjectUtil.isNull(ssShortVideo.getBannerUrl())) {
                    this.enableOne(res.getString("VideoId"), null, null, String.valueOf(res.getString("CoverUrl")));
                }
            } else if (Objects.equals(eventType, TranscodeComplete)) {
                //视频转码完成
                String videoId = res.getString("VideoId");
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(VideoTranscodeConstant.TranscodeComplete)
                        .event(body)
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            } else if (Objects.equals(eventType, StreamTranscodeComplete)) {
                //单个完成
                String videoId = res.getString("VideoId");
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(VideoTranscodeConstant.StreamTranscodeComplete)
                        .event(body)
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            }
        }
    }


    public List<SsShortVideo> getShortVideoList(String sourceId, Integer status) {
        JPAQuery<SsShortVideo> query = queryFactory.select(qSsShortVideo)
                .from(qSsShortVideo)
                .leftJoin(qSsShortVideoTag)
                .on(qSsShortVideo.id.eq(qSsShortVideoTag.videoId))
                .where(qSsShortVideo.sourceId.eq(sourceId));

        if (Objects.nonNull(status)) {
            query.where(qSsShortVideoTag.status.eq(status));
        }

        return query.fetch();
    }

    public List<SsShortVideoLike> getUserVideoLikeList(List<Long> videoIds, Integer userId) {
        if (ObjectUtils.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        return queryFactory.selectFrom(qSsShortVideoLike)
                .where(qSsShortVideoLike.videoId.in(videoIds))
                .where(qSsShortVideoLike.customerId.eq(userId))
                .where(qSsShortVideoLike.enabled.eq(true))
                .fetch();
    }


    public List<CountStatistics> getShortVideoLikeCountStatistics(Collection<Long> articleIds) {
        if (ObjectUtils.isEmpty(articleIds)) {
            return Collections.emptyList();
        }
        Map<Long, Long> map = getLikeCount(articleIds);
        return articleIds.stream()
                .map(id -> new CountStatistics(id, map.getOrDefault(id, 0L)))
                .collect(Collectors.toList());
    }

    public List<LikeStatistics> getShortVideoLikeOperationStatistics(Collection<Long> videoIds, Integer userId) {
        if (ObjectUtils.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        List<SsShortVideoLike> userVideoLikeList = getUserVideoLikeList(new ArrayList<>(videoIds), userId);
        Set<Long> likeMap = userVideoLikeList.stream().map(SsShortVideoLike::getVideoId).collect(Collectors.toSet());
        return videoIds.stream()
                .map(id -> new LikeStatistics(id, likeMap.contains(id)))
                .collect(Collectors.toList());
    }

    public List<CountStatistics> getShortVideoCommentCountStatistics(Collection<Long> videoIds, Integer auditStatus, Boolean isChose) {
        if (ObjectUtils.isEmpty(videoIds)) {
            return Collections.emptyList();
        }

        NumberExpression<Long> countExpression = qSsShortVideoComment.customerId.count().as("count");
        Set<Long> videoIdSet = new HashSet<>(videoIds);
        final JPAQuery<Tuple> query = queryFactory.select(Projections.tuple(qSsShortVideoComment.videoId, countExpression))
                .from(qSsShortVideoComment)
                .where(qSsShortVideoComment.videoId.in(videoIdSet))
                .where(qSsShortVideoComment.enabled.eq(true))
                .groupBy(qSsShortVideoComment.videoId);

        if (ObjectUtils.isNotEmpty(auditStatus)) {
            query.where(qSsShortVideoComment.auditStatus.eq(auditStatus));
        }

        if (ObjectUtils.isNotEmpty(isChose)) {
            query.where(qSsShortVideoComment.isChose.eq(isChose));
        }

        List<Tuple> tuples = query.fetch();

        return tuples.stream().map(tuple -> CountStatistics.builder()
                        .id(tuple.get(qSsShortVideoComment.videoId))
                        .count(tuple.get(countExpression))
                        .build())
                .collect(Collectors.toList());
    }

    @Transactional
    public void auditVideo(@NonNull Boolean isAudit, Long id, Integer auditorId, String auditRemark, Instant preReleaseTime) {
        if (isAudit) {
            Instant now = Instant.now();
            Instant releaseTime = checkPubTime(preReleaseTime, now);
            Integer status = ObjectUtil.isNotEmpty(releaseTime) ? AS_Released.getValue() : AS_Reviewed.getValue();
            String recordStatus = ObjectUtil.isNotEmpty(releaseTime) ? AUDIT_PASS_PUBLISHED : AUDIT_PASS_RELEASE;
            this.auditPassed(id, auditorId, status, releaseTime, recordStatus);
            if (ObjectUtil.isEmpty(releaseTime)) {
                // 发送延时消息
                rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, ContentServiceConst.MqTagType.SHORT_VIDEO_RELEASE,
                        preReleaseTime.toEpochMilli() - now.toEpochMilli(), id);
            }
        } else {
            this.auditRejected(id, auditorId, auditRemark);
        }
    }

    @Transactional
    public void auditPassed(Long id, Integer auditorId, Integer status, Instant releaseTime, String recordStatus) {
        JPAUpdateClause clause = queryFactory.update(qSsShortVideo)
                .set(qSsShortVideo.status, status)
                .set(qSsShortVideo.auditId, auditorId)
                .set(qSsShortVideo.auditTime, Instant.now())
                .where(qSsShortVideo.id.eq(id));
        if (ObjectUtil.isNotEmpty(releaseTime)) {
            clause.set(qSsShortVideo.releaseTime, releaseTime);
        }
        clause.execute();
        UcUsers users = userClient.getUser(auditorId).getData();
        videoOperateRecordService.saveRecord(id, auditorId, VideoOperateRecordEnum.AUDIT_VIDEO, users.getRealName(), recordStatus);
    }

    @Transactional
    public void auditRejected(Long id, Integer auditorId, String auditRemark) {
        queryFactory.update(qSsShortVideo)
                .set(qSsShortVideo.status, AS_Rejected.getValue())
                .set(qSsShortVideo.auditRemark, auditRemark)
                .set(qSsShortVideo.auditTime, Instant.now())
                .set(qSsShortVideo.auditId, auditorId)
                .where(qSsShortVideo.id.eq(id))
                .execute();
        UcUsers users = userClient.getUser(auditorId).getData();
        videoOperateRecordService.saveRecord(id, auditorId, VideoOperateRecordEnum.AUDIT_VIDEO, users.getRealName(), ArticleOperateRecordConstant.AUDIT_REFUSE);
    }

    public SsShortVideo findOneById(@NonNull Long id) {
        return queryFactory.selectFrom(qSsShortVideo).where(qSsShortVideo.id.eq(id)).fetchOne();
    }

    public Instant checkPubTime(Instant preReleaseTime, Instant now) {
        if (ObjectUtil.isNull(preReleaseTime)) {
            return now;
        }
        return preReleaseTime.isBefore(now)? now: null;
    }

    @Transactional
    public void releaseShortVideo(Long id) {
        SsShortVideo shortVideo = queryFactory.selectFrom(qSsShortVideo).where(qSsShortVideo.id.eq(id)).fetchOne();
        if (ObjectUtil.isNotNull(shortVideo)) {
            queryFactory.update(qSsShortVideo)
                    .where(qSsShortVideo.status.eq(AS_Reviewed.getValue()))
                    .where(qSsShortVideo.id.eq(id))
                    .set(qSsShortVideo.releaseTime, shortVideo.getPreReleaseTime())
                    .set(qSsShortVideo.status, AS_Released.getValue())
                    .execute();
            videoOperateRecordService.saveRecord(id, null, VideoOperateRecordEnum.EDIT_VIDEO, null, AUDIT_PASS_PUBLISHED);
        }
    }
}

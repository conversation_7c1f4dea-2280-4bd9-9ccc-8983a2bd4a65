package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsArticle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface ArticleRepository extends JpaRepository<SsArticle, Long>, QuerydslPredicateExecutor<SsArticle> {
}

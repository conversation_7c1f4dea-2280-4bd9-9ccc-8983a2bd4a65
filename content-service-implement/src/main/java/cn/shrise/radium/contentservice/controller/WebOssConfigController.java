package cn.shrise.radium.contentservice.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseError;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.properties.AccessKeyOssProperties;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.util.GsonUtils;
import cn.shrise.radium.common.util.StringRedisUtil;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.properties.WebOssConfigProperty;
import cn.shrise.radium.contentservice.resp.OssTokenResp;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.*;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("oss")
@Slf4j
public class WebOssConfigController {

    private final WebOssConfigProperty webOssConfigProperty;
    private final AccessKeyOssProperties accessKeyOssProperties;
    private final StringRedisUtil stringRedisUtil;
    private final CommonProperties commonProperties;

    @GetMapping("config")
    @ApiOperation("生成sts token")
    public BaseResult<AssumeRoleResponse> getOssConfigInfo() {
        AssumeRoleResponse response = null;
        String cacheKey = String.format(ContentServiceConst.WEB_OSS_CACHE_KEY, webOssConfigProperty.getRegionId(),
                accessKeyOssProperties.getAccessKeyId());
        if (ObjectUtil.isNotEmpty(stringRedisUtil.getValue(cacheKey))) {
            response = GsonUtils.fromJson(stringRedisUtil.getValue(cacheKey), AssumeRoleResponse.class);
            return BaseResult.success(response);
        }
        DefaultProfile profile = DefaultProfile.getProfile(webOssConfigProperty.getRegionId(),
                accessKeyOssProperties.getAccessKeyId(), accessKeyOssProperties.getAccessKeySecret());
        IAcsClient client = new DefaultAcsClient(profile);

        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setSysRegionId(webOssConfigProperty.getRegionId());
        request.setRoleArn(webOssConfigProperty.getRoleArn());
        request.setRoleSessionName(webOssConfigProperty.getRoleSessionName());
        //发起请求，并得到响应。
        try {
            response = client.getAcsResponse(request);
            // 存redis token默认有效期为1小时
            stringRedisUtil.getLock(cacheKey, GsonUtils.toJson(response), 55, TimeUnit.MINUTES);
            return BaseResult.success(response);
        } catch (ClientException e) {
            log.error("ErrInfo:" + GsonUtils.toJson(e));
            return BaseResult.create(BaseError.FAILURE_CODE, GsonUtils.toJson(e), response);
        }
    }

    @GetMapping("token/{isVideo}")
    @ApiOperation("oss上传临时凭证")
    public BaseResult<OssTokenResp> genOssToken(@PathVariable Boolean isVideo) {
        String host = isVideo ? webOssConfigProperty.getVideoEndPoint() : webOssConfigProperty.getFileEndPoint();
        DateTime expireTime = DateUtil.offsetHour(new Date(), 1);
        String expirationTime = DatePattern.UTC_FORMAT.format(expireTime);
        String policy = String.format("{\"expiration\": \"%s\",\"conditions\": [[\"starts-with\", \"$key\", \"\"]]}", expirationTime);
        String encodePolicy = new String(Base64.encodeBase64(policy.getBytes()));
        String signature = com.aliyun.oss.common.auth.ServiceSignature.create().computeSignature(accessKeyOssProperties.getAccessKeySecret(), encodePolicy);

        OssTokenResp resp = OssTokenResp.builder()
                .accessId(accessKeyOssProperties.getAccessKeyId())
                .expire(expireTime.getTime() / 1000)
                .host(host)
                .policy(encodePolicy)
                .signature(signature)
                .build();
        return BaseResult.success(resp);
    }

    @GetMapping("file-url")
    @ApiOperation("获取文件url")
    public BaseResult<URL> getFileUrl(
            @RequestParam @ApiParam("originUrl") String originUrl,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("bucket类型：10-robot，20-promotion") Integer type
    ) {
        String endpoint = accessKeyOssProperties.getEndpoint();

        DefaultCredentialProvider credentialProvider = new DefaultCredentialProvider(accessKeyOssProperties.getAccessKeyId(), accessKeyOssProperties.getAccessKeySecret(), null);

        OSS ossClient = new OSSClientBuilder().build(endpoint, credentialProvider);

        URL signedUrl = null;
        try {
            // 指定生成的签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
            Date expiration = new Date(new Date().getTime() + 3600 * 1000L);

            String bucketName = null;
            if (type == 10) {
                bucketName = commonProperties.getRobotBucket();
            } else if (type == 20) {
                bucketName = commonProperties.getPromotionBucket();
            }

            // 生成签名URL。
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, originUrl, HttpMethod.GET);
            // 设置过期时间。
            request.setExpiration(expiration);

            // 通过HTTP GET请求生成签名URL。
            signedUrl = ossClient.generatePresignedUrl(request);
        } catch (OSSException oe) {
            throw new RuntimeException("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason." + "Error Message:" + oe.getErrorMessage()
                    + "Error Code:" + oe.getErrorCode()
                    + "Request ID:" + oe.getRequestId()
                    + "Host ID:" + oe.getHostId());
        }
        return BaseResult.success(signedUrl);
    }

}

package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.entity.QSsRoomPrize;
import cn.shrise.radium.contentservice.entity.SsRoomPrize;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.repository.RoomPrizeRepository;
import cn.shrise.radium.contentservice.req.CreateRoomPrizeReq;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class RoomPrizeService {

    private final JPAQueryFactory queryFactory;
    private final RoomPrizeRepository roomPrizeRepository;

    public Page<SsRoomPrize> getRoomPrizeList(Long id, Boolean isEnabled, Pageable pageable) {
        QSsRoomPrize ssRoomPrize = QSsRoomPrize.ssRoomPrize;
        final JPAQuery<SsRoomPrize> expression = queryFactory.select(ssRoomPrize).from(ssRoomPrize);
        if (!ObjectUtils.isEmpty(id)) {
            expression.where(ssRoomPrize.id.eq(id));
        }
        if (!ObjectUtils.isEmpty(isEnabled)) {
            expression.where(ssRoomPrize.isEnabled.eq(isEnabled));
        }
        final long count = expression.fetchCount();
        final List<SsRoomPrize> fetch = expression.orderBy(ssRoomPrize.id.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();
        return new PageImpl<>(fetch, pageable, count);
    }

    public SsRoomPrize updateRoomPrize(Long id, Boolean isEnabled) {
        Optional<SsRoomPrize> roomPrize = roomPrizeRepository.findById(id);
        SsRoomPrize ssRoomPrize = roomPrize.orElseThrow(() -> new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED));
        ssRoomPrize.setIsEnabled(isEnabled);
        return roomPrizeRepository.save(ssRoomPrize);
    }

    public SsRoomPrize findRoomPrize(Long id) {
        Optional<SsRoomPrize> roomPrize = roomPrizeRepository.findById(id);
        return roomPrize.orElse(null);
    }

    public SsRoomPrize createRoomPrize(CreateRoomPrizeReq req) {
        if (!ObjectUtils.isEmpty(req.getName()) && !ObjectUtils.isEmpty(req.getImageUrl())) {
            SsRoomPrize ssRoomPrize = new SsRoomPrize();
            BeanUtils.copyProperties(req, ssRoomPrize);
            ssRoomPrize.setIsEnabled(true);
            return roomPrizeRepository.save(ssRoomPrize);
        } else {
            throw new BusinessException(ContentErrorCode.PARAM_INVALID);
        }
    }
}

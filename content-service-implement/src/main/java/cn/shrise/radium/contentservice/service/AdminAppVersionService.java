package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.util.StringUtil;
import cn.shrise.radium.contentservice.constant.AdminAppVersionUpdateTypeConstant;
import cn.shrise.radium.contentservice.entity.QSsAdminAppVersion;
import cn.shrise.radium.contentservice.entity.SsAdminAppVersion;
import cn.shrise.radium.contentservice.repository.AdminAppVersionRepository;
import cn.shrise.radium.contentservice.req.AdminAppVersionReq;
import cn.shrise.radium.contentservice.resp.AdminAppVersionInfoResp;
import cn.shrise.radium.contentservice.resp.AdminAppVersionResp;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminAppVersionService {

    private final JPAQueryFactory queryFactory;
    private final QSsAdminAppVersion adminAppVersion = QSsAdminAppVersion.ssAdminAppVersion;
    private final AdminAppVersionRepository adminAppVersionRepository;

    public BaseResult<List<AdminAppVersionResp>> list(Integer type) {

        List<AdminAppVersionResp> fetch = queryFactory.select(Projections.bean(AdminAppVersionResp.class,
                        adminAppVersion.id,
                        adminAppVersion.gmtCreate,
                        adminAppVersion.version,
                        adminAppVersion.updateType,
                        adminAppVersion.updateContent,
                        adminAppVersion.enabled,
                        adminAppVersion.publishTime,
                        adminAppVersion.fileUrl,
                        adminAppVersion.type))
                .from(adminAppVersion)
                .where(adminAppVersion.type.eq(type))
                .orderBy(adminAppVersion.gmtCreate.desc())
                .fetch();

        return BaseResult.success(fetch);
    }

    public SsAdminAppVersion findAdminAppByVersion(Integer type, String version) {
        return queryFactory.selectFrom(adminAppVersion)
                .where(adminAppVersion.type.eq(type).and(adminAppVersion.version.eq(version)))
                .fetchOne();
    }

    public void save(AdminAppVersionReq req) {
        String version = req.getVersion();
        StringUtil.checkVersion(version, 3);
        SsAdminAppVersion appVersion = findAdminAppByVersion(req.getType(), req.getVersion());
        if (ObjectUtil.isEmpty(appVersion)) {
            SsAdminAppVersion adminAppVersion = SsAdminAppVersion.builder()
                    .type(req.getType())
                    .version(req.getVersion())
                    .updateType(req.getUpdateType())
                    .updateContent(req.getUpdateContent())
                    .enabled(false)
                    .fileUrl(req.getFileUrl())
                    .build();
            adminAppVersionRepository.save(adminAppVersion);
        } else {
            throw new BusinessException("请勿输入重复版本号");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void update(AdminAppVersionReq req) {
        queryFactory.update(adminAppVersion)
                .where(adminAppVersion.type.eq(req.getType()).and(adminAppVersion.version.eq(req.getVersion())))
                .set(adminAppVersion.fileUrl, req.getFileUrl())
                .set(adminAppVersion.updateType, req.getUpdateType())
                .set(adminAppVersion.updateContent, req.getUpdateContent())
                .execute();
    }

    @Transactional(rollbackFor = Exception.class)
    public void setStatus(Boolean enabled, Long id) {
        JPAUpdateClause updateClause = queryFactory.update(adminAppVersion)
                .where(adminAppVersion.id.eq(id))
                .set(adminAppVersion.enabled, enabled);
        if (enabled) {
            updateClause.set(adminAppVersion.publishTime, Instant.now());
        }
        updateClause.execute();
    }

    public BaseResult<AdminAppVersionResp> getLast(Integer type) {

        AdminAppVersionResp resp = queryFactory.select(Projections.bean(AdminAppVersionResp.class,
                        adminAppVersion.id,
                        adminAppVersion.gmtCreate,
                        adminAppVersion.version,
                        adminAppVersion.updateType,
                        adminAppVersion.updateContent,
                        adminAppVersion.enabled,
                        adminAppVersion.publishTime,
                        adminAppVersion.fileUrl,
                        adminAppVersion.type))
                .from(adminAppVersion)
                .where(adminAppVersion.type.eq(type))
                .where(adminAppVersion.enabled.eq(true))
                .orderBy(adminAppVersion.gmtCreate.desc())
                .fetchFirst();

        return BaseResult.success(resp);
    }

    public BaseResult<AdminAppVersionInfoResp> checkVersion(Integer type, String version) {
        //查询已发布的所有版本
        List<AdminAppVersionInfoResp> enabledVersionResps = queryFactory.select(Projections.bean(AdminAppVersionInfoResp.class,
                        adminAppVersion.updateType,
                        adminAppVersion.version,
                        adminAppVersion.fileUrl,
                        adminAppVersion.updateContent))
                .from(adminAppVersion)
                .where(adminAppVersion.type.eq(type))
                .where(adminAppVersion.enabled.eq(true))
                .orderBy(adminAppVersion.gmtCreate.desc())
                .fetch();
        if (ObjectUtil.isEmpty(enabledVersionResps)) {
            throw new RecordNotExistedException("无发布版本");
        }
        enabledVersionResps.add(AdminAppVersionInfoResp.builder().version(version).build());

        Collections.sort(enabledVersionResps, Comparator.comparing(AdminAppVersionInfoResp::getVersion, (v1, v2) -> {
            return StringUtil.versionCompare(v1, v2);
        }));

        AdminAppVersionInfoResp maxVersionResp = enabledVersionResps.get(0);
        if (StringUtil.versionCompare(maxVersionResp.getVersion(), version) >= 0) { //若当前版本号大于等于后台配置最新版本号，直接返回
            AdminAppVersionInfoResp resp = new AdminAppVersionInfoResp();
            resp.setIsLatest(true);
            return BaseResult.success(resp);
        }

        AdminAppVersionInfoResp currentVersionResp = enabledVersionResps.stream().filter(i -> Objects.equals(i.getVersion(), version)).findFirst().orElse(new AdminAppVersionInfoResp());
        int currentIndex = enabledVersionResps.indexOf(currentVersionResp);
        List<AdminAppVersionInfoResp> filterList = enabledVersionResps.subList(0, currentIndex); //截取列表中最大版本号到当前版本号部分
        AdminAppVersionInfoResp maxResp = new AdminAppVersionInfoResp();
        BeanUtils.copyProperties(maxVersionResp, maxResp);
        maxResp.setIsLatest(false);
        if (ObjectUtil.equals(maxResp.getUpdateType(), AdminAppVersionUpdateTypeConstant.PROMPT)) {
            //若为提示更新，检查在最新版本与当前版本之间是否有发布的强制更新的版本
            boolean isContainForce = filterList.stream().anyMatch(i -> i.getUpdateType().equals(AdminAppVersionUpdateTypeConstant.COMPULSION));
            maxResp.setUpdateType(isContainForce ? AdminAppVersionUpdateTypeConstant.COMPULSION : AdminAppVersionUpdateTypeConstant.PROMPT);
        }
        return BaseResult.success(maxResp);
    }
}

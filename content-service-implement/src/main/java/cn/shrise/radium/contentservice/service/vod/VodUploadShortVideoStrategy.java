package cn.shrise.radium.contentservice.service.vod;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.constant.VideoTranscodeConstant;
import cn.shrise.radium.contentservice.constant.VodResultEventConstant;
import cn.shrise.radium.contentservice.entity.SsShortVideo;
import cn.shrise.radium.contentservice.entity.SsVideoTranscode;
import cn.shrise.radium.contentservice.resp.VodCallbackResp;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import cn.shrise.radium.contentservice.service.VideoTranscodeService;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.vod.model.v20170321.SubmitAIJobResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static cn.shrise.radium.contentservice.constant.VodResultEventConstant.StreamTranscodeComplete;
import static cn.shrise.radium.contentservice.constant.VodResultEventConstant.TranscodeComplete;

/**
 * 短视频上传回调策略
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VodUploadShortVideoStrategy implements VodVideoStrategy {

    private final ShortVideoService shortVideoService;
    private final AliVodService aliVodService;
    private final VideoTranscodeService videoTranscodeService;

    @Override
    public void handle(Integer companyType, VodCallbackResp callbackResp) {
        String status = callbackResp.getStatus();
        String eventType = callbackResp.getEventType();
        String videoId = callbackResp.getVideoId();

        if (Objects.equals(status, "success")) {
            log.info("eventType: {}", eventType);
            if (Objects.equals(eventType, VodResultEventConstant.FileUploadComplete)) {//视频上传完成事件
                shortVideoService.enableOne(videoId, true, null, null);
            } else if (Objects.equals(eventType, VodResultEventConstant.VideoAnalysisComplete)) {//视频分析完成事件
                Float duration = callbackResp.getDuration();
                shortVideoService.enableOne(videoId, null, String.valueOf(duration), null);
            } else if (Objects.equals(eventType, TranscodeComplete)) {
                //视频转码完成
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(VideoTranscodeConstant.TranscodeComplete)
                        .event(JSON.toJSONString(callbackResp))
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            } else if (Objects.equals(eventType, StreamTranscodeComplete)) {
                //单个完成
                SsVideoTranscode entity = SsVideoTranscode.builder()
                        .videoId(videoId)
                        .status(VideoTranscodeConstant.StreamTranscodeComplete)
                        .event(JSON.toJSONString(callbackResp))
                        .build();
                videoTranscodeService.createOrUpdateVideoTranscode(entity);
            }
        }
    }
}

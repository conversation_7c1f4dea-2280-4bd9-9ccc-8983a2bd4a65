package cn.shrise.radium.contentservice.job;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsNew;
import cn.shrise.radium.contentservice.entity.SsNewsChannel;
import cn.shrise.radium.contentservice.enums.NewsChannelTypeEnum;
import cn.shrise.radium.contentservice.properties.NewsConfigProperty;
import cn.shrise.radium.contentservice.service.NewsService;
import cn.shrise.radium.contentservice.util.HttpUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.oss.OSS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class PullTechnologyNewsJob extends JavaProcessor {

    //全国热门带正文新闻查询_易源数据api文档
    //https://market.aliyun.com/products/57126001/cmapi011150.html?spm=5176.730005.result.6.18f43524sa1Gzl&innerSource=search_%E6%96%B0%E9%97%BB#sku=yuncode515000003
    private final NewsService newsService;
    private final NewsConfigProperty newsConfigProperty;
    private final OSS ossClient;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String appCode = newsConfigProperty.getConfig().getAppCode();
        String host = newsConfigProperty.getConfig().getBaseUrl();
        String newsPath = newsConfigProperty.getConfig().getNewsPath();
        String channelPath = newsConfigProperty.getConfig().getChannelPath();
        //同步频道
        syncChannel(appCode, host, channelPath);
        //同步新闻
        syncNews(appCode, host, newsPath);
//        List<SsNew> uploadFailNews = newsService.getUploadFailNews();
        //重新上传新闻图片
//        uploadNewsPic(uploadFailNews);
        return new ProcessResult(true, "运行成功");
    }

    public void syncChannel(String appCode, String host, String path) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + appCode);
        Map<String, SsNewsChannel> ssNewsChannelsMap = newsService.getSsNewsChannelsMap();
        HttpResponse httpResponse = HttpUtils.doGet(host, path, "GET", headers, null);
        String str = JSON.parse(EntityUtils.toString(httpResponse.getEntity())).toString();
        JSONObject jsonObject = JSONUtil.parseObj(str);
        JSONObject showApiResBody = jsonObject.getJSONObject("showapi_res_body");
        JSONArray channelList = showApiResBody.getJSONArray("channelList");
        if (ObjectUtil.isNotEmpty(channelList)) {
            List<SsNewsChannel> ssNewsChannels = new ArrayList<>();
            for (Object channel : channelList) {
                JSONObject contentItem = JSONUtil.parseObj(channel);
                if (!ssNewsChannelsMap.containsKey(contentItem.getStr("channelId"))) {
                    SsNewsChannel build = SsNewsChannel.builder().channelNumber(contentItem.getStr("channelId")).name(contentItem.getStr("name")).type(NewsChannelTypeEnum.NCT_AliYy.getValue()).isEnabled(true).build();
                    ssNewsChannels.add(build);
                }
            }
            newsService.batchSaveSsNewsChannel(ssNewsChannels);
        }
    }

    public void syncNews(String appCode, String host, String path) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + appCode);
        Map<String, String> query = new HashMap<>();
        query.put("needContent", "1");
        query.put("needHtml", "1");
        query.put("maxResult", "100");
        int pageIndex = 1;
        Map<String, SsNewsChannel> ssNewsChannelsMap = newsService.getSsNewsChannelsMap();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        while (true) {
            query.put("page", Integer.toString(pageIndex));
            HttpResponse httpResponse = HttpUtils.doGet(host, path, "GET", headers, query);
            String str = JSON.parse(EntityUtils.toString(httpResponse.getEntity())).toString();
            JSONObject jsonObject = JSONUtil.parseObj(str);
            JSONObject showApiResBody = jsonObject.getJSONObject("showapi_res_body");
            JSONObject pageBean = showApiResBody.getJSONObject("pagebean");
            JSONArray contentList = pageBean.getJSONArray("contentlist");
            if (ObjectUtil.isNotEmpty(contentList)) {
                List<String> idList = contentList.stream().map(i -> {
                    JSONObject content = JSONUtil.parseObj(i);
                    return MapUtil.getStr(content, "id");
                }).collect(Collectors.toList());
                List<String> newsList = newsService.getNewSourceIds(idList);
                if (newsList.size() == idList.size()) {
                    log.info("无最新新闻");
                    break;
                }
                List<SsNew> ssNews = new ArrayList<>();
                contentList.forEach(item -> {
                    JSONObject content = JSONUtil.parseObj(item);
                    if (!newsList.contains(MapUtil.getStr(content, "id"))) {
                        List<String> imageUrls = new ArrayList<>();
                        String imgUrl = null;
                        SsNew.SsNewBuilder ssNewBuilder = SsNew.builder()
                                .publishTime(DateUtils.getLocalDateTimeParse(content.getStr("pubDate"), dateTimeFormatter).atZone(ZoneId.systemDefault()).toInstant())
                                .channelId(ssNewsChannelsMap.get(content.getStr("channelId")).getId())
                                .title(content.getStr("title"))
                                .brief(content.getStr("desc"))
                                .sourceId(content.getStr("id"))
                                .content(content.getStr("content"))
                                .html(content.getStr("html"))
                                .contentHtml(content.getStr("html"))
                                .source(content.getStr("source"))
                                .link(content.getStr("link"))
                                .allList(content.getStr("allList"))
                                .isEnabled(true)
                                .imageUrls(JSONUtil.toJsonStr(imageUrls))
                                .titleImageUrl(imgUrl);
                        if (ObjectUtil.isNotNull(content.getBool("havePic")) && content.getBool("havePic")) {
                            log.info("source_id：{}", content.getStr("id"));
                            JSONArray imageurls = content.getJSONArray("imageurls");
                            String img = content.getStr("img");
                            String html = content.getStr("html");
                            Document doc = Jsoup.parse(html);
                            Elements p = doc.getElementsByTag("p");
                            try {
                                int picCount = 0;
                                long start = System.currentTimeMillis();
                                for (Element e : p) {
                                    if (ObjectUtil.isNotEmpty(e.childNodes()) && ObjectUtil.isNotEmpty(e.childNodes().get(0).attributes().get("src"))) {
                                        String urlStr = e.childNodes().get(0).attributes().get("src").trim();
                                        e.childNodes().get(0).clearAttributes();
                                        e.childNodes().get(0).attr("src", newsConfigProperty.getConfig().getOssPath() + updateFromUrl(urlStr));
                                        picCount++;
                                    }
                                }
                                log.info("上传图片{}张，用时{}s", picCount, (System.currentTimeMillis() - start) / 1000);
                                imgUrl = updateFromUrl(img);
                                for (Object imageurl : imageurls) {
                                    JSONObject imageurlitem = JSONUtil.parseObj(imageurl);
                                    String url = imageurlitem.getStr("url");
                                    imageUrls.add(updateFromUrl(url));
                                }
                                ssNewBuilder.imageUrls(JSONUtil.toJsonStr(imageUrls));
                                ssNewBuilder.titleImageUrl(imgUrl);
                                ssNewBuilder.contentHtml(p.toString());
                            } catch (IOException e) {
                                e.printStackTrace();
                                ssNewBuilder.titleImageUrl(img);
                                for (Object imageurl : imageurls) {
                                    JSONObject imageurlitem = JSONUtil.parseObj(imageurl);
                                    String url = imageurlitem.getStr("url");
                                    imageUrls.add(url);
                                }
                                ssNewBuilder.imageUrls(JSONUtil.toJsonStr(imageUrls));
                                ssNewBuilder.isEnabled(false);
                                log.error("新闻图片上传OSS失败，sourceId:{}", content.getStr("id"));
                            }
                        }
                        ssNews.add(ssNewBuilder.build());
                    }
                });
                newsService.batchSaveSsNew(ssNews);
                pageIndex++;
            }
        }
    }

    public void uploadNewsPic(List<SsNew> uploadFailNews) throws IOException {
        for (SsNew uploadFailNew : uploadFailNews) {
            List<String> imageUrls = new ArrayList<>();
            String imgUrl = null;
            JSONArray imageurls = JSONUtil.parseArray(uploadFailNew.getImageUrls());
            String img = uploadFailNew.getTitleImageUrl();
            String contentHtml = uploadFailNew.getContentHtml();
            Document doc = Jsoup.parse(contentHtml);
            Elements p = doc.getElementsByTag("p");
            for (Element e : p) {
                if (ObjectUtil.isNotEmpty(e.childNodes().get(0).attributes().get("src"))) {
                    String urlStr = e.childNodes().get(0).attributes().get("src").trim();
                    e.childNodes().get(0).clearAttributes();
                    e.childNodes().get(0).attr("src", updateFromUrl(urlStr));
                }
            }
            imgUrl = updateFromUrl(img);
            for (Object imageurl : imageurls) {
                imageUrls.add(updateFromUrl(imageurl.toString()));
            }
            newsService.updateNewsBySourceId(uploadFailNew.getSourceId(), p, JSONUtil.toJsonStr(imageUrls), imgUrl);
        }
    }

    public String updateFromUrl(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(3 * 1000);
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        InputStream inputStream = conn.getInputStream();
        byte[] getData = readInputStream(inputStream);
        String id = IdUtil.nanoId(8);
        ossClient.putObject("gs-file-src", "news/" + id + ".jpg", new ByteArrayInputStream(getData));
        return "news/" + id + ".jpg";
    }

    public byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }
}

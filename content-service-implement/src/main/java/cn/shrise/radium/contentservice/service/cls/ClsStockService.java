package cn.shrise.radium.contentservice.service.cls;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.cls.entity.*;
import cn.shrise.radium.contentservice.resp.cls.*;
import cn.shrise.radium.contentservice.util.ClsUtils;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.StockUtils.isStockCode;
import static cn.shrise.radium.contentservice.enums.ClsSubjectEnum.*;

/**
 * @Author: tangjiajun
 * @Date: 2025/3/6 15:44
 * @Desc:
 **/
@Service
@RequiredArgsConstructor
public class ClsStockService {
    @Resource(name = "jpaQuerySecondary")
    private final JPAQueryFactory jpaQuerySecondary;
    private final QLianStockPool lianStockPool = QLianStockPool.lianStockPool;
    private final QLianUpRemark lianUpRemark = QLianUpRemark.lianUpRemark;
    private final QLianV1Plate lianV1Plate = QLianV1Plate.lianV1Plate;
    private final QLianV1Article lianV1Article = QLianV1Article.lianV1Article;
    private final QLianSubjectArticleAssoc lianSubjectArticleAssoc = QLianSubjectArticleAssoc.lianSubjectArticleAssoc;
    private final QLianSubjectCategoryAssoc lianSubjectCategoryAssoc = QLianSubjectCategoryAssoc.lianSubjectCategoryAssoc;
    private final QLianDepthColumnRecommend lianDepthColumnRecommend = QLianDepthColumnRecommend.lianDepthColumnRecommend;
    private final QLianSubject lianSubject = QLianSubject.lianSubject;
    private final QXkAnncDatum xkAnncDatum = QXkAnncDatum.xkAnncDatum;
    private final QXkNewsDatum xkNewsDatum = QXkNewsDatum.xkNewsDatum;
    private final QXkResearchSummary xkResearchSummary = QXkResearchSummary.xkResearchSummary;
    private final QClsStockInformationRelation clsStockInformationRelation = QClsStockInformationRelation.clsStockInformationRelation;


    public List<ClsStockPlateResp> clsStockPlateList(LocalDate localDate) {
        List<ClsStockPlateResp> respList = new ArrayList<>();
        List<ClsStockPlateResp> removeList = new ArrayList<>();
        List<String> endPlates = Arrays.asList("其他", "ST股");
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        String dateStr = DateUtils.DateToStr(date, "yyyy-MM-dd 00:00:00");
        Instant flagTime = DateUtils.formatterStringToInstant(dateStr, "yyyy-MM-dd HH:mm:ss");

        List<Tuple> fetch = jpaQuerySecondary.select(lianUpRemark.id, lianUpRemark.remark, lianUpRemark.isTop, lianStockPool.stockCode, lianStockPool.upTime,
                        lianStockPool.upReason, lianV1Plate.id, lianV1Plate.plateCode, lianV1Plate.name).from(lianUpRemark)
                .innerJoin(lianStockPool).on(lianUpRemark.plateId.eq(lianStockPool.plateId))
                .innerJoin(lianV1Plate).on(lianStockPool.plateId.eq(lianV1Plate.id))
                .where(lianUpRemark.cdate.eq(flagTime))
                .where(lianStockPool.cdate.eq(flagTime)).where(lianStockPool.pool.eq(1))
                .fetch();

        if (ObjectUtil.isNotEmpty(fetch)) {
            //获取板块信息
            Map<Integer, Tuple> plateMap = fetch.stream().collect(Collectors.toMap(
                    i -> i.get(lianV1Plate.id),
                    i -> i,
                    (oldVal, newVal) -> newVal)
            );

            //按照板块将股票列表分组
            Map<Integer, List<ClsStockPlateResp.StockListDto>> stockMap = fetch.stream()
                    .collect(Collectors.groupingBy(
                            i -> i.get(lianV1Plate.id), // 按照 plate.id 分组
                            Collectors.mapping(
                                    i -> {
                                        ClsStockPlateResp.StockListDto dto = new ClsStockPlateResp.StockListDto();
                                        dto.setStockCode(ClsUtils.formatStockCode(i.get(lianStockPool.stockCode)));
                                        dto.setUpTime(i.get(lianStockPool.upTime));
                                        dto.setUpReason(i.get(lianStockPool.upReason));
                                        return dto;
                                    },
                                    Collectors.toList()
                            )
                    ));

            plateMap.forEach((key, value) -> {
                ClsStockPlateResp resp = ClsStockPlateResp.builder()
                        .plateId(key)
                        .plateCode(value.get(lianV1Plate.plateCode))
                        .plateName(value.get(lianV1Plate.name))
                        .remark(value.get(lianUpRemark.remark))
                        .isTop(value.get(lianUpRemark.isTop))
                        .stockList(stockMap.get(key))
                        .build();
                respList.add(resp);
            });
            respList.sort(Comparator.comparing(ClsStockPlateResp::getIsTop)); //按isTop顺序排序
            respList.forEach(plate -> {
                if (endPlates.contains(plate.getPlateName())) {
                    removeList.add(plate);
                }
                List<ClsStockPlateResp.StockListDto> stockList = plate.getStockList().stream().filter(stockListDto -> {
                    //过滤北交所股票
                    return isStockCode(stockListDto.getStockCode());
                }).collect(Collectors.toList());
                plate.setStockList(stockList);
            });
            //若为"其他"或"ST"板块，强制排在末尾
            if (ObjectUtil.isNotEmpty(removeList)) {
                respList.removeAll(removeList);
                respList.addAll(removeList);
            }
        }
        return respList;
    }

    public List<ClsRecommendArticleResp> clsArticleRecommendList(Integer size) {
        List<LianDepthColumnRecommend> recommends = jpaQuerySecondary.selectFrom(lianDepthColumnRecommend)
                .leftJoin(lianV1Article).on(lianDepthColumnRecommend.articleId.eq(lianV1Article.id))
                .where(lianDepthColumnRecommend.columnId.eq(STOCK_MARKET.getCode())
                        .or(lianDepthColumnRecommend.columnId.eq(COMPANY.getCode()))
                        .and(lianDepthColumnRecommend.pid.eq(0)).and(lianV1Article.id.isNotNull()).and(lianV1Article.status.eq(1)))
                .orderBy(lianDepthColumnRecommend.sortScore.desc())
                .limit(size)
                .fetch();
        List<Integer> pids = recommends.stream().map(LianDepthColumnRecommend::getId).collect(Collectors.toList());
        List<LianDepthColumnRecommend> recommendList = jpaQuerySecondary.selectFrom(lianDepthColumnRecommend)
                .where(lianDepthColumnRecommend.pid.in(pids))
                .fetch();
        Map<Integer, List<LianDepthColumnRecommend>> map = recommendList.stream().collect(Collectors.groupingBy(LianDepthColumnRecommend::getPid));
        List<ClsRecommendArticleResp> resps = recommends.stream().map(i -> {
            ClsRecommendArticleResp articleResp = ClsRecommendArticleResp.builder()
                    .articleId(i.getArticleId())
                    .build();
            if (ObjectUtil.isNotEmpty(map.get(i.getId()))) {
                articleResp.setChildList(map.get(i.getId()).stream().map(j -> {
                    return ClsArticleInfoResp.builder()
                            .articleId(j.getArticleId())
                            .build();
                }).collect(Collectors.toList()));
            }
            return articleResp;
        }).collect(Collectors.toList());
        return resps;
    }

    public LocalDate getLatestDate() {
        Instant dateInstant = jpaQuerySecondary.select(lianUpRemark.cdate).from(lianUpRemark).orderBy(lianUpRemark.cdate.desc()).limit(1).fetchOne();
        if (ObjectUtil.isEmpty(dateInstant)) {
            return null;
        }
        return DateUtils.instantToLocalDate(dateInstant);
    }

    public BaseResult<List<LianV1Article>> batchArticleList(Collection<Integer> articleIds) {
        if (CollectionUtils.isEmpty(articleIds)) {
            return BaseResult.success(new ArrayList<>());
        }
        List<LianV1Article> articles = jpaQuerySecondary.selectFrom(lianV1Article)
                .where(lianV1Article.id.in(articleIds))
                .where(lianV1Article.status.eq(1))
                .fetch();
        return BaseResult.success(articles);
    }

    public BaseResult<LianV1Article> clsLastWarningArticle() {
        LianV1Article article = jpaQuerySecondary.select(lianV1Article)
                .from(lianV1Article)
                .innerJoin(lianSubjectArticleAssoc).on(lianV1Article.id.eq(lianSubjectArticleAssoc.articleId))
                .where(lianSubjectArticleAssoc.subjectId.eq(WARNING.getCode()))
                .where(lianV1Article.status.eq(1))
                .orderBy(lianSubjectArticleAssoc.createTime.desc())
                .fetchFirst();
        return BaseResult.success(article);
    }

    public List<ClsArticleInfoResp> clsArticleList(Integer type, LocalDateTime dateTime, Integer size) {

        JPAQuery<LianV1Article> query = jpaQuerySecondary.select(lianV1Article).distinct()
                .from(lianSubjectCategoryAssoc)
                .leftJoin(lianSubjectArticleAssoc).on(lianSubjectArticleAssoc.subjectId.eq(lianSubjectCategoryAssoc.subjectId))
                .leftJoin(lianV1Article).on(lianSubjectArticleAssoc.articleId.eq(lianV1Article.id))
                .where(lianV1Article.type.eq(type))
                .where(lianV1Article.status.eq(1))
                .where(lianSubjectCategoryAssoc.isDel.eq(false))
                .where(lianSubjectArticleAssoc.isDel.eq(false));
        if (type == 0) {
            query.where(lianSubjectCategoryAssoc.subjectCategoryId.in(STOCK_MARKET.getCode(), COMPANY.getCode()));
        }
        if (type == -1) {
            query.where(lianSubjectCategoryAssoc.subjectCategoryId.eq(ALERTS.getCode()));
        }
        if (ObjectUtil.isNotEmpty(dateTime)) {
            query.where(lianV1Article.ctime.loe(DateUtils.localDateTimeToInstant(dateTime).getEpochSecond()));
        }

        List<LianV1Article> articles = query.orderBy(lianV1Article.ctime.desc())
                .limit(size)
                .fetch();
        return articles.stream().map(ClsArticleInfoResp::of).collect(Collectors.toList());

    }

    public BaseResult<Map<Integer, List<LianSubject>>> batchArticleSubjectMap(Collection<Integer> articleIds) {
        if (ObjectUtil.isEmpty(articleIds)) {
            return BaseResult.success(new HashMap<>());
        }
        HashMap<Integer, List<LianSubject>> respMap = new HashMap<>();
        Map<Integer, List<LianSubjectArticleAssoc>> subjectArticleMap = jpaQuerySecondary.selectFrom(lianSubjectArticleAssoc)
                .where(lianSubjectArticleAssoc.articleId.in(articleIds))
                .where(lianSubjectArticleAssoc.isDel.eq(false))
                .fetch().stream().collect(Collectors.groupingBy(LianSubjectArticleAssoc::getArticleId));
        List<Integer> subjectIds = new ArrayList<>();
        subjectArticleMap.values().stream().forEach(i -> {
            subjectIds.addAll(i.stream().map(LianSubjectArticleAssoc::getSubjectId)
                    .filter(s -> s < 80000).collect(Collectors.toList()));
        });
        Map<Integer, LianSubject> subjectMap = jpaQuerySecondary.selectFrom(lianSubject)
                .where(lianSubject.id.in(subjectIds))
                .where(lianSubject.isDel.eq(0))
                .fetch().stream().filter(ObjectUtil::isNotEmpty).collect(Collectors.toMap(LianSubject::getId, subject -> subject));
        subjectArticleMap.keySet().forEach(articleId -> {
            List<LianSubject> subjects = subjectArticleMap.get(articleId).stream().map(assoc -> {
                return subjectMap.getOrDefault(assoc.getSubjectId(), new LianSubject());
            }).collect(Collectors.toList());
            List<LianSubject> respSubjects = subjects.stream().filter(subject -> ObjectUtil.isNotEmpty(subject.getId())).collect(Collectors.toList());
            respMap.put(articleId, respSubjects);
        });
        return BaseResult.success(respMap);
    }

    public BaseResult<ClsArticleInfoResp> clsArticleInfo(Integer articleId) {
        LianV1Article article = jpaQuerySecondary.selectFrom(this.lianV1Article)
                .where(this.lianV1Article.id.eq(articleId)).fetchFirst();
        return BaseResult.success(ClsArticleInfoResp.of(article));
    }

    public BaseResult<Integer> clsArticleExpressCount(LocalDateTime dateTime) {
        Long count = (long) jpaQuerySecondary.select(lianV1Article).distinct()
                .from(lianSubjectCategoryAssoc)
                .leftJoin(lianSubjectArticleAssoc).on(lianSubjectArticleAssoc.subjectId.eq(lianSubjectCategoryAssoc.subjectId))
                .leftJoin(lianV1Article).on(lianSubjectArticleAssoc.articleId.eq(lianV1Article.id))
                .where(lianSubjectCategoryAssoc.subjectCategoryId.eq(ALERTS.getCode()))
                .where(lianV1Article.type.eq(-1))
                .where(lianV1Article.status.eq(1))
                .where(lianSubjectCategoryAssoc.isDel.eq(false))
                .where(lianSubjectArticleAssoc.isDel.eq(false))
                .where(lianV1Article.ctime.gt(DateUtils.localDateTimeToInstant(dateTime).getEpochSecond())).fetch().size();

        return BaseResult.success(count.intValue());
    }

    public BaseResult<List<ClsArticleInfoResp>> clsSubjectArticleList(Integer subjectId, LocalDateTime dateTime, Integer size) {
        JPAQuery<LianV1Article> query = jpaQuerySecondary.select(lianV1Article)
                .from(lianSubjectArticleAssoc)
                .leftJoin(lianV1Article).on(lianSubjectArticleAssoc.articleId.eq(lianV1Article.id))
                .where(lianV1Article.status.eq(1))
                .where(lianSubjectArticleAssoc.subjectId.eq(subjectId))
                .where(lianSubjectArticleAssoc.isDel.eq(false));
        if (ObjectUtil.isNotEmpty(dateTime)) {
            query.where(lianV1Article.ctime.loe(DateUtils.localDateTimeToInstant(dateTime).getEpochSecond()));
        }
        List<ClsArticleInfoResp> resps = query.orderBy(lianV1Article.ctime.desc())
                .limit(size)
                .fetch().stream().map(ClsArticleInfoResp::of).collect(Collectors.toList());

        return BaseResult.success(resps);
    }


    public BaseResult<ClsStockNewsInfoResp> clsStockNewsInfo(Integer id) {
        XkNewsDatum newsDatum = jpaQuerySecondary.selectFrom(xkNewsDatum).where(xkNewsDatum.id.eq(id)).fetchOne();
        if (ObjectUtil.isEmpty(newsDatum)) {
            return BaseResult.success(null);
        }
        ClsStockNewsInfoResp resp = ClsStockNewsInfoResp.of(newsDatum);
        return BaseResult.success(resp);
    }

    public BaseResult<ClsStockResearchInfoResp> clsStockResearchInfo(Long id) {
        XkResearchSummary researchSummary = jpaQuerySecondary.selectFrom(xkResearchSummary).where(xkResearchSummary.id.eq(String.valueOf(id))).fetchOne();
        if (ObjectUtil.isEmpty(researchSummary)) {
            return BaseResult.success(null);
        }
        ClsStockResearchInfoResp resp = ClsStockResearchInfoResp.of(researchSummary);
        return BaseResult.success(resp);
    }

    public BaseResult<ClsStockAnnounceInfoResp> clsStockAnnounceInfo(Integer id) {
        XkAnncDatum anncDatum = jpaQuerySecondary.selectFrom(xkAnncDatum).where(xkAnncDatum.id.eq(id)).fetchOne();
        if (ObjectUtil.isEmpty(anncDatum)) {
            return BaseResult.success(null);
        }
        ClsStockAnnounceInfoResp resp = ClsStockAnnounceInfoResp.of(anncDatum);
        return BaseResult.success(resp);
    }

    public BaseResult<List<ClsStockNewsInfoResp>> clsStockNewsList(List<String> stockCodeList, Long cursor, Integer size) {
        if (ObjectUtil.isEmpty(stockCodeList)) {
            return BaseResult.success(new ArrayList<>());
        }
        JPAQuery<Long> query = jpaQuerySecondary.select(clsStockInformationRelation.relationId).distinct()
                .from(clsStockInformationRelation)
                .where(clsStockInformationRelation.stockCode.in(stockCodeList))
                .where(clsStockInformationRelation.type.eq(10));
        if (ObjectUtil.isNotEmpty(cursor)) {
            query.where(clsStockInformationRelation.relationId.lt(cursor));
        }
        int limitSize = 2 * size;
        List<Integer> relationIds = query.orderBy(clsStockInformationRelation.publishTime.desc(), clsStockInformationRelation.id.desc()).limit(limitSize).fetch().stream().map(Long::intValue).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(relationIds)) {
            return BaseResult.success(new ArrayList<>());
        }
        List<XkNewsDatum> datumList = jpaQuerySecondary.selectFrom(xkNewsDatum)
                .where(xkNewsDatum.id.in(relationIds))
                .where(xkNewsDatum.state.eq(0))
                .fetch();
        List<ClsStockNewsInfoResp> resps = datumList.stream().map(ClsStockNewsInfoResp::of).sorted(Comparator.comparing(ClsStockNewsInfoResp::getPublishTime, Comparator.reverseOrder())
                        .thenComparing(ClsStockNewsInfoResp::getId, Comparator.reverseOrder()))
                .limit(size)
                .collect(Collectors.toList());
        return BaseResult.success(resps);
    }

    public BaseResult<List<ClsStockResearchInfoResp>> clsStockResearchList(List<String> stockCodeList, Long cursor, Integer size) {
        if (ObjectUtil.isEmpty(stockCodeList)) {
            return BaseResult.success(new ArrayList<>());
        }
        JPAQuery<Long> query = jpaQuerySecondary.select(clsStockInformationRelation.relationId).distinct()
                .from(clsStockInformationRelation)
                .where(clsStockInformationRelation.stockCode.in(stockCodeList))
                .where(clsStockInformationRelation.type.eq(30));
        if (ObjectUtil.isNotEmpty(cursor)) {
            query.where(clsStockInformationRelation.relationId.lt(cursor));
        }
        int limitSize = 2 * size;
        List<String> relationIds = query.orderBy(clsStockInformationRelation.publishTime.desc(), clsStockInformationRelation.id.desc()).limit(limitSize).fetch().stream().map(Object::toString).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(relationIds)) {
            return BaseResult.success(new ArrayList<>());
        }
        List<XkResearchSummary> datumList = jpaQuerySecondary.selectFrom(xkResearchSummary)
                .where(xkResearchSummary.id.in(relationIds))
                .where(xkResearchSummary.state.eq("0"))
                .fetch();
        List<ClsStockResearchInfoResp> resps = datumList.stream().map(ClsStockResearchInfoResp::of).sorted(Comparator.comparing(ClsStockResearchInfoResp::getPublishTime, Comparator.reverseOrder())
                .thenComparing(ClsStockResearchInfoResp::getId, Comparator.reverseOrder())).limit(size).collect(Collectors.toList());
        return BaseResult.success(resps);
    }

    public BaseResult<List<ClsStockAnnounceInfoResp>> clsStockAnnounceList(List<String> stockCodeList, Long cursor, Integer size) {
        if (ObjectUtil.isEmpty(stockCodeList)) {
            return BaseResult.success(new ArrayList<>());
        }
        JPAQuery<Long> query = jpaQuerySecondary.select(clsStockInformationRelation.relationId).distinct()
                .from(clsStockInformationRelation)
                .where(clsStockInformationRelation.stockCode.in(stockCodeList))
                .where(clsStockInformationRelation.type.eq(20));
        if (ObjectUtil.isNotEmpty(cursor)) {
            query.where(clsStockInformationRelation.relationId.lt(cursor));
        }
        int limitSize = 2 * size;
        List<Integer> relationIds = query.orderBy(clsStockInformationRelation.publishTime.desc(), clsStockInformationRelation.id.desc()).limit(limitSize).fetch().stream().map(Long::intValue).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(relationIds)) {
            return BaseResult.success(new ArrayList<>());
        }
        List<XkAnncDatum> datumList = jpaQuerySecondary.selectFrom(xkAnncDatum)
                .where(xkAnncDatum.id.in(relationIds))
                .where(xkAnncDatum.state.eq(0))
                .orderBy(xkAnncDatum.publishTime.desc())
                .fetch();
        List<ClsStockAnnounceInfoResp> resps = datumList.stream().map(ClsStockAnnounceInfoResp::of).sorted(Comparator.comparing(ClsStockAnnounceInfoResp::getPublishTime, Comparator.reverseOrder())
                .thenComparing(ClsStockAnnounceInfoResp::getId, Comparator.reverseOrder())).limit(size).collect(Collectors.toList());
        return BaseResult.success(resps);
    }
}

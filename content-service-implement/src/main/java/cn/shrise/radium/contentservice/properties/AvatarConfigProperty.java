package cn.shrise.radium.contentservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "avatar.config")
@EnableConfigurationProperties
public class AvatarConfigProperty {

    private String tenantId;
    private String appId;
    private String authKey;
    private String endpoint;
}

package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.entity.SsPromotionResourceShareLink;
import cn.shrise.radium.contentservice.resp.PromotionResourceFolderResp;
import cn.shrise.radium.contentservice.resp.PromotionResourceWxLinkResp;
import cn.shrise.radium.contentservice.service.PromotionResourceService;
import com.aliyun.oss.model.ListObjectsV2Result;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

@RestController
@RequestMapping("promotion-resource")
@RequiredArgsConstructor
public class PromotionResourceController {

    private final PromotionResourceService promotionResourceService;

    @GetMapping("folder-list")
    @ApiOperation("推广资源文件夹列表")
    public BaseResult<ListObjectsV2Result> getPromotionResourceFolder(
            @RequestParam(required = false) @ApiParam("分页游标") String cursor,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        ListObjectsV2Result result = promotionResourceService.listObjectsV2Result(null, "/", size, cursor);
        return BaseResult.success(result);
    }

    @PostMapping("folder-create")
    @ApiOperation("新建文件夹")
    public BaseResult<Void> createPromotionResourceFolder(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam @ApiParam("用户id") Integer userId) {
        promotionResourceService.createPromotionResourceFolder(folderName, userId);
        return BaseResult.successful();
    }

    @PostMapping("folder-delete")
    @ApiOperation("删除文件夹")
    public BaseResult<Void> deletePromotionResourceFolder(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam @ApiParam("用户id") Integer userId) {
        promotionResourceService.deletePromotionResourceFolder(folderName, userId);
        return BaseResult.successful();
    }

    @GetMapping("file-list")
    @ApiOperation("推广资源文件列表")
    public BaseResult<ListObjectsV2Result> getPromotionResourceFile(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam(required = false) @ApiParam("分页token") String cursor,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        ListObjectsV2Result result = promotionResourceService.listObjectsV2Result(folderName, null, size, cursor);
        return BaseResult.success(result);
    }

    @PostMapping("file-delete")
    @ApiOperation("删除文件")
    public BaseResult<Void> deletePromotionResourceFile(
            @RequestParam @ApiParam("文件完整路径") String path,
            @RequestParam @ApiParam("用户id") Integer userId
    ) {
        promotionResourceService.deletePromotionResourceFile(path, userId);
        return BaseResult.successful();
    }

    @GetMapping("link-url")
    @ApiOperation("推广资源分享链接")
    public BaseResult<String> promotionResourceLinkUrl(
            @RequestParam @ApiParam("文件夹名称") String folderName,
            @RequestParam @ApiParam("天数类型（1-1天，3-3天，7-7天，999-永久）") Integer dayType,
            @RequestParam(required = false) @ApiParam("wxId") Integer wxId) {
        String linkUrl = promotionResourceService.promotionResourceLinkUrl(folderName, dayType, wxId);
        return BaseResult.success(linkUrl);
    }

    @GetMapping("folder-info")
    @ApiOperation("获取文件信息")
    public BaseResult<SsPromotionResourceShareLink> getPromotionResourceShareLink(
            @RequestParam @ApiParam("链接编号") String number
    ) {
        SsPromotionResourceShareLink result = promotionResourceService.getPromotionResourceShareLink(number);
        return BaseResult.success(result);
    }

    @GetMapping("wx-link-list")
    @ApiOperation("推广资源领取记录")
    public PageResult<List<PromotionResourceWxLinkResp>> promotionResourceWxLinkList(
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return promotionResourceService.promotionResourceWxLinkList(current, size);
    }

    @GetMapping("my-folder-list")
    @ApiOperation("获取我的文件夹列表")
    public BaseResult<List<PromotionResourceFolderResp>> getMyPromotionResourceFolder(@RequestParam Integer wxId) {
        return promotionResourceService.getMyPromotionResourceFolder(wxId);
    }

}

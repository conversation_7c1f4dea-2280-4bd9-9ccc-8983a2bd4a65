package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.constant.AuditStatusConstant;
import cn.shrise.radium.contentservice.constant.StockPoolTypeConstant;
import cn.shrise.radium.contentservice.entity.*;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

import static cn.shrise.radium.contentservice.constant.StockPoolTypeConstant.LIMITUPPOOL;

@Service
@RequiredArgsConstructor
public class ContentService {

    private final JPAQueryFactory queryFactory;
    private final QSsAnalystInfo analystInfo = QSsAnalystInfo.ssAnalystInfo;
    private final QSsStockPool qSsStockPool = QSsStockPool.ssStockPool;

    private final QSsStreamChannelAnalystRelation relation = QSsStreamChannelAnalystRelation.ssStreamChannelAnalystRelation;

    public BaseResult<List<SsAnalystInfo>> getAnalystInfoList(List<Integer> ids, Integer companyType, Boolean isEnabled) {

        JPAQuery<SsAnalystInfo> query = queryFactory.select(analystInfo)
                .from(analystInfo)
                .where(analystInfo.auditStatus.eq(AuditStatusConstant.AUDIT_PASS));
        if (ObjectUtil.isNotEmpty(ids)) {
            query.where(analystInfo.id.in(ids));
        }
        if (ObjectUtil.isNotEmpty(isEnabled)) {
            query.where(analystInfo.isEnabled.eq(isEnabled));
        }
        if (ObjectUtil.isNotEmpty(companyType)) {
            query.where(analystInfo.companyType.eq(companyType));
        }
        query.orderBy(analystInfo.createTime.desc());
        return BaseResult.success(query.fetch());
    }

    public BaseResult<List<SsAnalystInfo>> getAnalystInfoList(Long channelId, Integer companyType, Boolean isEnabled) {

        JPAQuery<SsAnalystInfo> query = queryFactory.select(analystInfo)
                .from(analystInfo)
                .where(analystInfo.auditStatus.eq(AuditStatusConstant.AUDIT_PASS));

        if (ObjectUtil.isNotEmpty(channelId)) {
            List<Integer> analystList = queryFactory.selectFrom(relation)
                    .where(relation.enabled.eq(true))
                    .where(relation.channelId.eq(channelId))
                    .fetch()
                    .stream()
                    .map(SsStreamChannelAnalystRelation::getAnalystId)
                    .collect(Collectors.toList());
            if (analystList.isEmpty()) {
                return BaseResult.success(null);
            }
            query.where(analystInfo.id.in(analystList));
        }

        if (ObjectUtil.isNotEmpty(isEnabled)) {
            query.where(analystInfo.isEnabled.eq(isEnabled));
        }

        if (ObjectUtil.isNotEmpty(companyType)) {
            query.where(analystInfo.companyType.eq(companyType));
        }
        query.orderBy(analystInfo.createTime.desc(), analystInfo.id.desc());
        return BaseResult.success(query.fetch());
    }

    public BaseResult<List<SsStockPool>> getStockPoolList(LocalDate dateTime, Integer type) {

        Instant dayOfStart = DateUtils.getDayOfStart(dateTime);
        Instant dayOfEnd = DateUtils.getDayOfEnd(dateTime);

        JPAQuery<SsStockPool> query = queryFactory.select(qSsStockPool)
                .from(qSsStockPool)
                .where(qSsStockPool.date.between(dayOfStart, dayOfEnd))
                .where(qSsStockPool.enabled.eq(true));
        if (ObjectUtil.isEmpty(type)){
            query.where(qSsStockPool.poolType.eq(LIMITUPPOOL));
        }else {
            query.where(qSsStockPool.poolType.eq(type));
        }
        query.orderBy(qSsStockPool.date.desc());
        return BaseResult.success(query.fetch());
    }
}

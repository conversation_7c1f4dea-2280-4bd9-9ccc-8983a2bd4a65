package cn.shrise.radium.contentservice.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@RefreshScope
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "green")
public class GreenLabelProperties {

    private List<Label> labels;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Label {
        private String name;
        private String label;
    }
}

package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.constant.LiveRoomMessageAuditStatusEnum;
import cn.shrise.radium.contentservice.entity.SsLiveRoomMessage;
import cn.shrise.radium.contentservice.repository.RoomMessageRepository;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
@RequiredArgsConstructor
public class RoomMessageService {

    private final RoomMessageRepository roomMessageRepository;

    public SsLiveRoomMessage addMessage(@NonNull SsLiveRoomMessage info){
        // 案例消息直接审核通过
        if (ObjectUtil.isNotEmpty(info.getDealId())){
            info.setAuditFlag(LiveRoomMessageAuditStatusEnum.AuditPass.getValue());
            info.setAuditTime(Instant.now());
        }
        info.setCreateTime(Instant.now());
        info.setUpdateTime(Instant.now());
        return roomMessageRepository.save(info);
    }

}

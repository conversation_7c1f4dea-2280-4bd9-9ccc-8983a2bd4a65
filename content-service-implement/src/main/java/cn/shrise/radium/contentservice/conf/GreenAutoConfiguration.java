package cn.shrise.radium.contentservice.conf;

import cn.shrise.radium.common.properties.AccessKeyOtherProperties;
import cn.shrise.radium.common.properties.AliyunProperties;
import cn.shrise.radium.contentservice.properties.GreenLabelProperties;
import com.aliyun.teaopenapi.models.Config;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(GreenLabelProperties.class)
public class GreenAutoConfiguration {

    private final AliyunProperties aliyunProperties;
    private final AccessKeyOtherProperties accessKeyOtherProperties;

    @Bean(name = "greenClient")
    @RefreshScope
    public com.aliyun.green20220302.Client greenClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyOtherProperties.getAccessKeyId())
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeyOtherProperties.getAccessKeySecret());
        // 访问的域名
        config.endpoint = aliyunProperties.getGreenCip().getEndpoint();
        return new com.aliyun.green20220302.Client(config);
    }
}

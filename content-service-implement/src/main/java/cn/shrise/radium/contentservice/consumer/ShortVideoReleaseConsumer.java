package cn.shrise.radium.contentservice.consumer;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = ContentServiceConst.CONTENT_TOPIC, consumerGroup = ContentServiceConst.MqGroupType.GID_SHORT_VIDEO_RELEASE,
        selectorType = ExpressionType.TAG, selectorExpression = ContentServiceConst.MqTagType.SHORT_VIDEO_RELEASE)
public class ShortVideoReleaseConsumer implements MessageListener {

    private final ShortVideoService shortVideoService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        Long id = JSON.parseObject(new String(message.getBody()), Long.class);
        shortVideoService.releaseShortVideo(id);
        return Action.CommitMessage;
    }
}

package cn.shrise.radium.contentservice.properties;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "news")
public class NewsConfigProperty {

    // 配置
    private NewsConfig config;

    //新闻快讯配置
    private NewsExpressConfig newsExpressConfig;

    // 新闻频道
    private List<NewsChannel> channel;


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewsConfig {
        private String appCode;
        private String appKey;
        private String appSecret;
        private String baseUrl;
        private String channelPath;
        private String newsPath;
        private String ossPath;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewsChannel {
        private String number;
        private String name;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewsExpressConfig {
        private String appKey;
        private String appSecret;
        private Integer deviceType;
        private String tokenPath;
        private String newsPath;
        private String detailPath;
        private String origin;
    }

}

package cn.shrise.radium.contentservice.service.airec;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.common.conf.AiRecAutoConfiguration;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.airec.AIRecItem;
import cn.shrise.radium.contentservice.airec.AIRecReq;
import cn.shrise.radium.contentservice.airec.AIRecUser;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.resp.AiRecResultResp;
import cn.shrise.radium.contentservice.resp.AiRecShortVideoResp;
import cn.shrise.radium.contentservice.resp.AiRecUserResp;
import cn.shrise.radium.contentservice.resp.airec.FilterResp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.airec.model.v20181012.PushDocumentRequest;
import com.aliyuncs.airec.model.v20181012.PushDocumentResponse;
import com.aliyuncs.airec.model.v20181012.RecommendRequest;
import com.aliyuncs.airec.model.v20181012.RecommendResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.FormatType;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiRecService {

    private final AiRecAutoConfiguration aiRecAutoConfiguration;
    private final PushDocumentRequest pushDocumentRequest;
    private final RecommendRequest recommendRequest;

    private static final String TABLE_NAME_ITEM = "item";
    private static final String TABLE_NAME_USER = "user";
    private static final String ITEM_TYPE = "shortvideo";
    private static final String ITEM_SCENE = "01";
    private static final String ITEM_WEIGHT = "1";

    public void processShortVideo(AiRecShortVideoResp resp) {
        pushDocumentRequest.setTableName(TABLE_NAME_ITEM);
        String content = JSON.toJSONString(buildItemData(Collections.singletonList(resp)));
        pushDocumentRequest.setHttpContent(content.getBytes(), "UTF-8", FormatType.JSON);
        DefaultAcsClient acsClient = aiRecAutoConfiguration.getDefaultAcsClient();
        try {
            PushDocumentResponse response = acsClient.getAcsResponse(pushDocumentRequest);
        } catch (ClientException e) {
            log.error("processShortVideo--errors:{}", e.getErrMsg());
            throw new BusinessException(ContentErrorCode.AI_ADD_ITEM_ERROR);
        }
    }

    public List<AIRecReq<Object>> buildItemData(List<AiRecShortVideoResp> shortVideoList) {
        List<AIRecReq<Object>> reqList = Lists.newArrayList();
        for (AiRecShortVideoResp resp : shortVideoList) {
            AIRecReq<Object> aiRecReq;
            log.info("buildItemData, pkId:{}, status:{}", resp.getId(), resp.getEnabled());
            if (resp.getEnabled()) {
                // 新增操作
                aiRecReq = addRecommendItem(resp);
            } else {
                // 更新操作
                aiRecReq = updateRecommendItem(resp);
            }
            reqList.add(aiRecReq);
        }
        return reqList;
    }

    public AIRecReq<Object> addRecommendItem(AiRecShortVideoResp resp) {
        Instant expireTime = resp.getGmtCreate().plusMillis(TimeUnit.DAYS.toMillis(15));
        String duration = ObjectUtils.isNotEmpty(resp.getVideoDuration()) ? resp.getVideoDuration().split("\\.")[0] : "0";
        AIRecItem item = AIRecItem.builder()
                .itemId(resp.getId().toString())
                .itemType(ITEM_TYPE)
                .status(String.valueOf(resp.getEnabled() ? 1 : 0))
                .sceneId(ITEM_SCENE)
                .duration(duration)
                .pubTime(String.valueOf(resp.getGmtCreate().getEpochSecond()))
                .expireTime(String.valueOf(expireTime.getEpochSecond()))
                .lastModifyTime(String.valueOf(resp.getGmtModified().getEpochSecond()))
                .title(resp.getTitle())
                .weight(ITEM_WEIGHT)
                .author(resp.getAnalystName())
                .tags(resp.getTags())
                .categoryLevel("1")
                .categoryPath(resp.getTeamId().toString())
                .build();
        return AIRecReq.builder().cmd("add").fields(item).build();
    }

    public AIRecReq<Object> updateRecommendItem(AiRecShortVideoResp resp) {
        AIRecItem item = AIRecItem.builder()
                .itemId(resp.getId().toString())
                .itemType(ITEM_TYPE)
                .status(String.valueOf(resp.getEnabled() ? 1 : 0))
                .build();
        return AIRecReq.builder().cmd("update").fields(item).build();
    }

    public void processUser(AiRecUserResp resp) {
        pushDocumentRequest.setTableName(TABLE_NAME_USER);
        String content = JSON.toJSONString(buildUserData(Collections.singletonList(resp)));
        pushDocumentRequest.setHttpContent(content.getBytes(), "UTF-8", FormatType.JSON);
        DefaultAcsClient acsClient = aiRecAutoConfiguration.getDefaultAcsClient();
        try {
            PushDocumentResponse response = acsClient.getAcsResponse(pushDocumentRequest);
        } catch (ClientException e) {
            log.error("processUser--errors:{}", e.getErrMsg());
            throw new BusinessException(ContentErrorCode.AI_ADD_USER_ERROR);
        }
    }

    public List<AIRecReq<Object>> buildUserData(List<AiRecUserResp> userRespList) {
        List<AIRecReq<Object>> reqList = Lists.newArrayList();
        for (AiRecUserResp resp : userRespList) {
            log.info("buildUserData, userId:{}, userType:{}", resp.getId(), resp.getUserIdType());
            reqList.add(addRecommendUser(resp));
        }
        return reqList;
    }

    public AIRecReq<Object> addRecommendUser(AiRecUserResp resp) {
        AIRecUser user = AIRecUser.builder()
                .userId(resp.getId().toString())
                .userIdType(resp.getUserIdType())
                .build();
        if (ObjectUtils.isNotEmpty(resp.getCreateTime())) {
            user.setRegisterTime(String.valueOf(resp.getCreateTime().getEpochSecond()));
        }
        return AIRecReq.builder().cmd("add").fields(user).build();
    }

    // 添加过滤规则（按内容主过滤）
    public String getFilterRule(List<Long> teamIds) {
        JSONObject rootRule = new JSONObject();
        rootRule.put("join", "or");
        List<FilterResp> filterRespList = teamIds.stream().map(i -> FilterResp.builder()
                .cond("category_match")
                .field("category_path")
                .value(i.toString())
                .build())
                .collect(Collectors.toList());
        rootRule.put("filters", filterRespList);
        String filterRuleString = JSON.toJSONString(rootRule);
        filterRuleString = new String(Base64.getEncoder().encode(filterRuleString.getBytes()));
        filterRuleString = filterRuleString.replaceAll("\\+", "-");
        filterRuleString = filterRuleString.replaceAll("/", "_");
        filterRuleString = filterRuleString.replaceAll("=", ".");
        return filterRuleString;
    }

    // 获取推荐结果
    public List<AiRecResultResp> getAiRecResults(@NonNull Integer userId, @NonNull Integer returnCount, String filterRuleString) {
        recommendRequest.setUserId(userId.toString());
        recommendRequest.setReturnCount(returnCount);
        recommendRequest.setSceneId(ITEM_SCENE);
        if (ObjectUtils.isNotEmpty(filterRuleString)) {
            recommendRequest.putQueryParameter("filter", filterRuleString);
        }
        DefaultAcsClient acsClient = aiRecAutoConfiguration.getDefaultAcsClient();
        try {
            RecommendResponse response = acsClient.getAcsResponse(recommendRequest);
            log.info("getAiRecResults, requestId:{}, itemIds:{}", response.getRequestId(), response.getResult().stream().map(RecommendResponse.ResultItem::getItemId).collect(Collectors.toList()));
            return response.getResult().stream().map(e -> {
                AiRecResultResp resp = new AiRecResultResp();
                BeanUtil.copyProperties(e, resp);
                return resp;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getAiRecResults--errors:{}", e.getMessage());
            throw new BusinessException(ContentErrorCode.AI_GET_RESULTS_ERROR);
        }
    }
}

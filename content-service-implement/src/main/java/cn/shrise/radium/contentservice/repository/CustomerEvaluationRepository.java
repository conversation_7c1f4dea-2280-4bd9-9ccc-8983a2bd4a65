package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsCustomerEvaluation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerEvaluationRepository extends JpaRepository<SsCustomerEvaluation, Integer>,
        QuerydslPredicateExecutor<SsCustomerEvaluation> {

    List<SsCustomerEvaluation> findByUserIdAndIsDeprecated(Integer userId, Boolean isDeprecated);
}

package cn.shrise.radium.contentservice.service.stream;

import cn.shrise.radium.contentservice.constant.StreamOperateRecordEnum;
import cn.shrise.radium.contentservice.constant.VideoOperateRecordEnum;
import cn.shrise.radium.contentservice.entity.QSsStreamOperateRecord;
import cn.shrise.radium.contentservice.entity.QSsVideoOperateRecord;
import cn.shrise.radium.contentservice.entity.SsStreamOperateRecord;
import cn.shrise.radium.contentservice.entity.SsVideoOperateRecord;
import cn.shrise.radium.contentservice.repository.SsStreamOperateRecordRepository;
import cn.shrise.radium.contentservice.repository.SsVideoOperateRecordRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class StreamOperateRecordService {

    private final SsStreamOperateRecordRepository ssStreamOperateRecordRepository;
    private final JPAQueryFactory queryFactory;
    private final QSsStreamOperateRecord qSsStreamOperateRecord = QSsStreamOperateRecord.ssStreamOperateRecord;

    public void saveRecord(Long streamId, Integer operatorId, StreamOperateRecordEnum operateRecordEnum, String realName, String status) {
        SsStreamOperateRecord build = SsStreamOperateRecord.builder()
                .streamId(streamId)
                .operatorId(operatorId)
                .content(StreamOperateRecordEnum.getRecord(operateRecordEnum.getValue(), realName, status))
                .build();
        ssStreamOperateRecordRepository.save(build);
    }

    public List<SsStreamOperateRecord> getOperateRecord(Long streamId) {
        return queryFactory.selectFrom(qSsStreamOperateRecord).where(qSsStreamOperateRecord.streamId.eq(streamId)).orderBy(qSsStreamOperateRecord.gmtCreate.asc()).fetch();
    }
}

package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.contentservice.entity.SsSalesVideoRecord;
import cn.shrise.radium.contentservice.service.SsSalesVideoRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;


@Api
@RestController
@RequestMapping("video")
@RequiredArgsConstructor
public class SsSalesVideoRecordController {
    private final SsSalesVideoRecordService service;

    @GetMapping("list")
    @ApiOperation("我的视频列表")
    public PageResult<List<SsSalesVideoRecord>> getRecord(@RequestParam(required = false) Integer companyType,
                                                          @RequestParam(required = false) Integer creatorId,
                                                          @RequestParam(required = false) String title,
                                                          @RequestParam(defaultValue = "1") Integer current,
                                                          @RequestParam(defaultValue = "10") Integer size) {
        Page<SsSalesVideoRecord> page = service.getSalesVideoRecord(companyType, creatorId, title, current, size);
        return PageResult.success(page.getContent(), Pagination.of(current,size,page.getTotalElements()));

    }

    @GetMapping("list/filter")
    @ApiOperation("获取视频列表")
    public PageResult<List<SsSalesVideoRecord>> getSalesVideoByFilter(
            @RequestParam Integer companyType,
            @RequestParam(required = false) String videoType,
            @RequestParam(required = false) List<Integer> creatorIdList,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Instant startTime,
            @RequestParam(required = false) Instant endTime,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        Page<SsSalesVideoRecord> page = service.getSalesVideoByFilter(companyType, videoType, creatorIdList, title,
                enabled, startTime, endTime, current, size);
        return PageResult.success(page.getContent(), Pagination.of(current, size, page.getTotalElements()));
    }
}

package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.req.CreateAvatarVideoReq;
import cn.shrise.radium.contentservice.resp.AvatarInfoResp;
import cn.shrise.radium.contentservice.resp.AvatarVideoInfoResp;
import cn.shrise.radium.contentservice.service.avatar.AliAvatarService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("avatar")
@RequiredArgsConstructor
public class AliAvatarController {

    private final AliAvatarService aliAvatarService;

    @GetMapping("list")
    @ApiOperation("数字人列表")
    public BaseResult<List<AvatarInfoResp>> getAvatarList(@RequestParam @ApiParam("模型类型 2d 只查询2d人物  3d只查询3d人物") String modelType) {
        List<AvatarInfoResp> avatarInfoResps = aliAvatarService.avatarList(modelType);
        return BaseResult.success(avatarInfoResps);
    }

    @GetMapping("video/list")
    @ApiOperation("数字人视频列表")
    public PageResult<List<AvatarVideoInfoResp>> getAvatarVideoList(
            @RequestParam(required = false) @ApiParam("类型 1:3D  3:2D") Integer type,
            @RequestParam(required = false) @ApiParam("状态 1: 等待执行 2: 执行中 3: 成功 4：失败 不传默认查询全部状态") Integer status,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return aliAvatarService.videoTaskList(type, status, current, size);
    }

    @PostMapping("video/create")
    @ApiOperation("创建数字人视频")
    public BaseResult<Void> createAvatarVideo(@RequestBody @Valid CreateAvatarVideoReq req) {
        aliAvatarService.createAvatarVideo(req);
        return BaseResult.successful();
    }
}

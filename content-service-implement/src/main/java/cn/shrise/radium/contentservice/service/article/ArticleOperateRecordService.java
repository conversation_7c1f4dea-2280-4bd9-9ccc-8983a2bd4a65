package cn.shrise.radium.contentservice.service.article;

import cn.shrise.radium.contentservice.constant.ArticleOperateRecordEnum;
import cn.shrise.radium.contentservice.entity.QSsArticleOperateRecord;
import cn.shrise.radium.contentservice.entity.SsArticleOperateRecord;
import cn.shrise.radium.contentservice.repository.SsArticleOperateRecordRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleOperateRecordService {

    private final SsArticleOperateRecordRepository ssArticleOperateRecordRepository;
    private final JPAQueryFactory queryFactory;
    private final QSsArticleOperateRecord qSsArticleOperateRecord = QSsArticleOperateRecord.ssArticleOperateRecord;

    @Transactional(rollbackFor = Exception.class)
    public void saveRecord(Long articleId, Integer operatorId, ArticleOperateRecordEnum operateRecordEnum, String realName, String status) {
        SsArticleOperateRecord build = SsArticleOperateRecord.builder()
                .articleId(articleId)
                .operatorId(operatorId)
                .content(ArticleOperateRecordEnum.getRecord(operateRecordEnum.getValue(), realName, status))
                .build();
        ssArticleOperateRecordRepository.save(build);
    }

    public List<SsArticleOperateRecord> getOperateRecord(Long articleId) {
        return queryFactory.selectFrom(qSsArticleOperateRecord).where(qSsArticleOperateRecord.articleId.eq(articleId)).orderBy(qSsArticleOperateRecord.gmtCreate.asc()).fetch();
    }
}

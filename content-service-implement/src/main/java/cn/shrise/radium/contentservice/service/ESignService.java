package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.error.CsErrorCode;
import cn.shrise.radium.contentservice.repository.ESignRepository;
import cn.shrise.radium.contentservice.repository.ESignTemplateRecordRepository;
import cn.shrise.radium.contentservice.repository.ESignTemplateRepository;
import cn.shrise.radium.contentservice.req.UpdateSignInfoReq;
import cn.shrise.radium.contentservice.resp.TemplateResp;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ESignService {
    private final QSsEsignInfo eSignInfo = QSsEsignInfo.ssEsignInfo;
    private final ESignRepository eSignRepository;
    private final ESignTemplateRepository eSignTemplateRepository;
    private final JPAQueryFactory queryFactory;
    private final QSsEsignTemplateRecord eSignTemplateRecord = QSsEsignTemplateRecord.ssEsignTemplateRecord;
    private final QSsEsignTemplate eSignTemplate = QSsEsignTemplate.ssEsignTemplate;
    private final ESignTemplateRecordRepository eSignTemplateRecordRepository;

    public ContentErrorCode abandonSign(String accountUserId) {
        SsEsignInfo ssEsignInfo = queryFactory.select(eSignInfo).from(eSignInfo).where(eSignInfo.accountUserId.eq(accountUserId)).fetchOne();
        if (ObjectUtil.isEmpty(ssEsignInfo)) {
            return ContentErrorCode.RECORD_EXISTED;
        }
        // 废掉签字
        ssEsignInfo.setIsDeprecated(true);
        SsEsignInfo eSignInfo = eSignRepository.save(ssEsignInfo);
        if (ObjectUtil.isEmpty(eSignInfo)) {
            return ContentErrorCode.FAILURE;
        }
        return ContentErrorCode.SUCCESS;
    }

    @Transactional
    public void abandonSign(Integer signId) {
        QSsEsignInfo eSignInfo = QSsEsignInfo.ssEsignInfo;
        queryFactory.update(eSignInfo).set(eSignInfo.isDeprecated, true).where(eSignInfo.id.eq(signId)).execute();
    }

    public Optional<SsEsignInfo> getEsignInfo(Integer companyType, String flowId) {
        QSsEsignInfo eSignInfo = QSsEsignInfo.ssEsignInfo;
        SsEsignInfo entity = queryFactory.selectFrom(eSignInfo)
                .where(eSignInfo.companyType.eq(companyType))
                .where(eSignInfo.flowId.eq(flowId))
                .fetchOne();
        return Optional.ofNullable(entity);
    }

    public SsEsignInfo findOneById(Integer signId) {
        return queryFactory.selectFrom(this.eSignInfo)
                .where(this.eSignInfo.id.eq(signId))
                .fetchOne();
    }

    public List<SsEsignInfo> getBatchSignInfo(Collection<Integer> signIds) {
        return queryFactory.selectFrom(this.eSignInfo)
                .where(this.eSignInfo.id.in(signIds))
                .fetch();
    }

    @Transactional
    public SsEsignInfo createOne(Integer company, String userId, String accountId, Integer signType, Long payCompanyId, String appId) {
        SsEsignInfo signInfo = SsEsignInfo.builder()
                .companyType(company)
                .accountUserId(userId)
                .accountId(accountId)
                .signType(signType)
                .isFinish(false)
                .isDeprecated(false)
                .payCompanyId(payCompanyId)
                .appId(appId)
                .build();
        return eSignRepository.save(signInfo);
    }

    @Transactional
    public void updateById(Integer id, UpdateSignInfoReq info) {
        JPAUpdateClause update = queryFactory.update(this.eSignInfo)
                .where(this.eSignInfo.id.eq(id));
        if (ObjectUtil.isNotNull(info.getFileId())) {
            update = update.set(this.eSignInfo.fileId, info.getFileId());
        }
        if (ObjectUtil.isNotNull(info.getFlowId())) {
            update = update.set(this.eSignInfo.flowId, info.getFlowId());
        }
        if (ObjectUtil.isNotNull(info.getIsAddDocs())) {
            update = update.set(this.eSignInfo.isAddDocs, info.getIsAddDocs());
        }
        if (ObjectUtil.isNotNull(info.getIsCompanySign())) {
            update = update.set(this.eSignInfo.isCompanySign, info.getIsCompanySign());
        }
        if (ObjectUtil.isNotNull(info.getIsConfig())) {
            update = update.set(this.eSignInfo.isConfig, info.getIsConfig());
        }
        if (ObjectUtil.isNotNull(info.getIsStart())) {
            update = update.set(this.eSignInfo.isStart, info.getIsStart());
        }
        if (ObjectUtil.isNotNull(info.getSignUrl())) {
            update = update.set(this.eSignInfo.signUrl, info.getSignUrl());
        }
        if (ObjectUtil.isNotNull(info.getSignUrlTime())) {
            update = update.set(this.eSignInfo.signUrlTime, info.getSignUrlTime());
        }
        update.execute();
    }

    public List<SsEsignTemplateRecord> getESignTemplateOperateRecordList(Integer signType, String appId) {
        return queryFactory.select(eSignTemplateRecord).from(eSignTemplateRecord)
                .where(eSignTemplateRecord.signType.eq(signType))
                .where(eSignTemplateRecord.appId.eq(appId))
                .orderBy(eSignTemplateRecord.gmtCreate.desc())
                .fetch();
    }

    public PageResult<List<TemplateResp>> getTemplateList(String appId, Boolean enabled, Integer current, Integer size) {
        JPAQuery<TemplateResp> query = queryFactory
                .select(Projections.bean(TemplateResp.class,
                        eSignTemplate.templateName.as("templateName"),
                        eSignTemplate.templateId.as("templateId"),
                        eSignTemplate.gmtCreate.as("gmtCreate"),
                        eSignTemplate.appId.as("appId"),
                        eSignTemplate.enabled.as("enabled")
                ))
                .from(eSignTemplate);
        if (ObjectUtil.isNotEmpty(appId)) {
            query.where(eSignTemplate.appId.in(appId));
        }
        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(eSignTemplate.enabled.eq(enabled));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(eSignTemplate.id.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public List<SsEsignTemplate> getTemplateInfoList() {
        return queryFactory.select(eSignTemplate)
                .from(eSignTemplate)
                .fetch();
    }

    @Transactional
    public void templateIdUpdate(Integer signType, String appId, String templateId, Integer operatorId) {
        SsEsignTemplate template = getTemplateById(templateId);
        if (signType.equals(template.getSignType()) && appId.equals(template.getAppId())) {
            return;
        }
        queryFactory.update(eSignTemplate)
                .setNull(eSignTemplate.signType)
                .where(eSignTemplate.signType.eq(signType))
                .where(eSignTemplate.appId.eq(appId))
                .execute();
        queryFactory.update(eSignTemplate)
                .set(eSignTemplate.signType, signType)
                .where(eSignTemplate.templateId.eq(templateId))
                .execute();
        SsEsignTemplateRecord.SsEsignTemplateRecordBuilder builder = SsEsignTemplateRecord.builder()
                .signType(signType)
                .appId(appId)
                .content("修改模板为：" + template.getTemplateName())
                .operatorId(operatorId);
        eSignTemplateRecordRepository.save(builder.build());
    }

    public String getTemplateAppId(String templateId) {
        return queryFactory.select(eSignTemplate.appId)
                .from(eSignTemplate)
                .where(eSignTemplate.templateId.eq(templateId))
                .fetchOne();
    }

    @Transactional
    public void setTemplateEnabled(String templateId, Boolean enabled) {
        SsEsignTemplate ssEsignTemplate = queryFactory.selectFrom(eSignTemplate)
                .where(eSignTemplate.templateId.eq(templateId))
                .fetchOne();
        if (ssEsignTemplate == null) {
            throw new RecordNotExistedException();
        }
        if (ObjectUtil.equals(ssEsignTemplate.getEnabled(), enabled)) {
            throw new BusinessException("数据相同");
        }
        ssEsignTemplate.setEnabled(enabled);
        eSignTemplateRepository.save(ssEsignTemplate);
    }

    public String getTemplateId(Integer signType, String appId) {
        return queryFactory.select(eSignTemplate.templateId)
                .from(eSignTemplate)
                .where(eSignTemplate.signType.eq(signType))
                .where(eSignTemplate.appId.eq(appId))
                .fetchFirst();
    }

    public SsEsignTemplate createTemplate(String appId, String templateId, String templateName) {
        SsEsignTemplate ssEsignTemplate = SsEsignTemplate.builder()
                .templateId(templateId)
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now())
                .templateName(templateName)
                .appId(appId)
                .enabled(true)
                .build();
        return eSignTemplateRepository.save(ssEsignTemplate);
    }

    public SsEsignTemplate getTemplateById(String templateId) {
        return queryFactory.selectFrom(eSignTemplate)
                .where(eSignTemplate.templateId.eq(templateId))
                .fetchOne();
    }
}

package cn.shrise.radium.contentservice.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.constant.AuditStatusConstant;
import cn.shrise.radium.contentservice.constant.MaterialCategoryConstant;
import cn.shrise.radium.contentservice.dao.adblake.SsMaterialContentDao;
import cn.shrise.radium.contentservice.entity.SsMaterial;
import cn.shrise.radium.contentservice.entity.SsMaterialContent;
import cn.shrise.radium.contentservice.entity.SsMaterialDepartment;
import cn.shrise.radium.contentservice.entity.SsMaterialOperateRecord;
import cn.shrise.radium.contentservice.req.*;
import cn.shrise.radium.contentservice.resp.SameMaterialContentResp;
import cn.shrise.radium.contentservice.service.material.MaterialContentService;
import cn.shrise.radium.contentservice.service.material.MaterialDepartmentService;
import cn.shrise.radium.contentservice.service.material.MaterialService;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.req.MaterialAuditAgentMsgReq;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.DateUtils.localDateTimeToInstant;

@RestController
@RequestMapping("material")
@RequiredArgsConstructor
public class MaterialController {

    private final MaterialService materialService;
    private final MaterialDepartmentService materialDepartmentService;
    private final MaterialContentService materialContentService;
    private final WorkwxClient workwxClient;
    private final List<Integer> allowAuditStatus = Arrays.asList(AuditStatusConstant.AUDIT_EXECUTING, AuditStatusConstant.PRE_AUDIT_PASS, AuditStatusConstant.AUDIT_PASS, AuditStatusConstant.AUDIT_NOT_PASS);
    private final SsMaterialContentDao ssMaterialContentDao;


    @PostMapping("create")
    @ApiOperation("上传素材")
    public BaseResult<Void> createMaterial(@RequestBody @Valid CreateMaterialReq req) {
        materialService.createOne(req);

        return BaseResult.successful();
    }

    @PostMapping("update")
    @ApiOperation("修改素材内容")
    public BaseResult<Void> updateMaterial(@RequestBody @Valid UpdateMaterialReq req) {
        materialService.updateOne(req);
        return BaseResult.successful();
    }

    @PostMapping("delete")
    @ApiOperation("删除素材")
    public BaseResult<Void> deleteMaterial(
            @RequestParam @ApiParam("素材id") Long materialId,
            @RequestParam @ApiParam("操作人id") Integer operatorId
    ) {
        materialService.deleteMaterialById(materialId, operatorId);
        return BaseResult.successful();
    }

    @PostMapping("content/batch")
    @ApiOperation("根据素材id批量获取素材内容")
    public BaseResult<Map<Long, List<SsMaterialContent>>> batchGetMaterialContent(
            @RequestParam(required = false) @ApiParam("是否可用") Boolean enabled,
            @RequestBody BatchReq<Long> req) {
        List<SsMaterialContent> list = materialContentService.batchGetMaterialContent(req.getValues(), enabled);
        Map<Long, List<SsMaterialContent>> resp = null;
        if (ObjectUtil.isNotEmpty(list)) {
            resp = list.stream().sorted(Comparator.comparing(SsMaterialContent::getId, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(SsMaterialContent::getMaterialId, LinkedHashMap::new, Collectors.toList()));
        }
        return BaseResult.success(resp);
    }

    @PostMapping("visible")
    @ApiOperation("修改可见范围")
    public BaseResult<Void> updateMaterialVisibleConfig(@RequestBody @Valid UpdateMaterialVisibleConfigReq req) {
        materialService.updateVisibleConfig(req);
        return BaseResult.successful();
    }

    @PostMapping("list/creator")
    @ApiOperation("获取素材列表")
    public PageResult<List<SsMaterial>> getCreatorMaterialListPage(
            @RequestBody @Validated MaterialListReq req) {
        if (ObjectUtil.isNotEmpty(req.getSearchContent())) {
            return ssMaterialContentDao.searchContentList(req.getIsAuditPage(), req.getCreatorList(), req.getCategory(), req.getSearchContent(), req.getCurrent(), req.getSize());
        }else {
            return materialService.getMaterialListPage(req.getCompanyType(), req.getCreatorList(), req.getCategory(), req.getContentType(), req.getAuditStatusList(),
                    localDateTimeToInstant(req.getCreateStartTime()), localDateTimeToInstant(req.getCreateEndTime()), req.getIsAuditPage(), req.getCurrent(), req.getSize());
        }
    }

    @GetMapping("list/marketing")
    @ApiOperation("获取营销素材列表")
    public PageResult<List<SsMaterial>> getMarketingMaterialListPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("部门id") Set<Integer> departmentList,
            @RequestParam(required = false) @ApiParam("素材类型") Integer contentType,
            @RequestParam(required = false) @ApiParam("创建开始时间") Instant createStartTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") Instant createEndTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        if (ObjectUtil.isNotEmpty(searchContent)) {
            return ssMaterialContentDao.searchContentList(companyType, userId, departmentList, searchContent, current, size);
        }else {
            return materialService.getMaterialListPage(companyType, userId, departmentList, contentType,
                    createStartTime, createEndTime, current, size);
        }
    }

    @GetMapping("list/promotion")
    @ApiOperation("获取推广素材列表")
    public PageResult<List<SsMaterial>> getPromotionMaterialListPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("素材类型") Integer contentType,
            @RequestParam(required = false) @ApiParam("创建开始时间") Instant createStartTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") Instant createEndTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        if (ObjectUtil.isNotEmpty(searchContent)) {
            return ssMaterialContentDao.searchPromotionContentList(companyType, searchContent, current, size);
        }else {
            return materialService.getMaterialListPage(companyType, contentType, MaterialCategoryConstant.PROMOTION_CUSTOMER, createStartTime, createEndTime, current, size);
        }
    }

    @GetMapping("list/brand")
    @ApiOperation("获取品宣素材列表")
    public PageResult<List<SsMaterial>> getBrandMaterialListPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("素材类型") Integer contentType,
            @RequestParam(required = false) @ApiParam("创建开始时间") Instant createStartTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") Instant createEndTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        if (ObjectUtil.isNotEmpty(searchContent)) {
            return ssMaterialContentDao.searchBrandContentList(companyType, searchContent, current, size);
        }else {
            return materialService.getMaterialListPage(companyType, contentType, MaterialCategoryConstant.BRAND_TEXT, createStartTime, createEndTime, current, size);
        }
    }

    @GetMapping("department")
    @ApiOperation("获取推广素材可见部门")
    public BaseResult<List<SsMaterialDepartment>> getDepartmentByMaterial(
            @RequestParam @ApiParam("素材ids") List<Long> materialList) {
        List<SsMaterialDepartment> records = materialDepartmentService.getDepartmentByMaterial(materialList);
        return BaseResult.success(records);
    }

    @PostMapping("audit")
    @ApiOperation("素材审核")
    public BaseResult<Void> auditMaterial(@RequestBody @Valid MaterialAuditReq req) {
        SsMaterial material = materialService.getMaterialById(req.getMaterialId());
        if (allowAuditStatus.contains(material.getAuditStatus()) && !ObjectUtil.equals(material.getAuditStatus(), req.getAuditStatus())) {
            materialService.auditMaterial(req);
        } else {
            throw new BusinessException("状态已更新，请刷新重试");
        }
        MaterialAuditAgentMsgReq agentMsgReq = MaterialAuditAgentMsgReq.builder()
                .id(req.getMaterialId())
                .auditStatus(req.getAuditStatus())
                .companyType(material.getCompanyType())
                .creatorId(material.getCreatorId())
                .visibleType(material.getVisibleType())
                .build();
        if (ObjectUtil.equals(material.getAuditStatus(), AuditStatusConstant.AUDIT_EXECUTING) || (ObjectUtil.equals(material.getAuditStatus(), AuditStatusConstant.PRE_AUDIT_PASS) && ObjectUtil.equals(req.getAuditStatus(), AuditStatusConstant.AUDIT_NOT_PASS))) {
            workwxClient.sendMaterialCreatorAgentMsg(agentMsgReq);
            if (ObjectUtil.equals(req.getAuditStatus(), AuditStatusConstant.AUDIT_PASS)) {
                workwxClient.sendMaterialVisibleAgentMsg(agentMsgReq);
            }
        }
        return BaseResult.successful();
    }

    @PostMapping("pre/audit")
    @ApiOperation("素材预审核")
    public BaseResult<Void> preAuditMaterial(@RequestBody @Valid MaterialAuditReq req) {
        SsMaterial material = materialService.getMaterialById(req.getMaterialId());
        MaterialAuditAgentMsgReq agentMsgReq = MaterialAuditAgentMsgReq.builder()
                .id(req.getMaterialId())
                .auditStatus(req.getAuditStatus())
                .companyType(material.getCompanyType())
                .creatorId(material.getCreatorId())
                .visibleType(material.getVisibleType())
                .build();
        if (ObjectUtil.equals(material.getAuditStatus(), AuditStatusConstant.AUDIT_EXECUTING)) {
            materialService.preAuditMaterial(req);
            workwxClient.sendMaterialCreatorAgentMsg(agentMsgReq);
            if (ObjectUtil.equals(req.getAuditStatus(), AuditStatusConstant.PRE_AUDIT_PASS)) {
                workwxClient.sendMaterialVisibleAgentMsg(agentMsgReq);
            }
        } else {
            throw new BusinessException("状态已更新，请刷新重试");
        }
        return BaseResult.successful();
    }

    @GetMapping("getMaterial/{materialId}")
    @ApiOperation("获取素材")
    public BaseResult<SsMaterial> getMaterial(
            @PathVariable @ApiParam("素材id") Long materialId) {
        SsMaterial material = materialService.getMaterialById(materialId);
        return BaseResult.success(material);
    }

    @GetMapping("get-material-content")
    @ApiOperation("根据materialId获取素材内容")
    public BaseResult<List<SsMaterialContent>> getContentByMaterialId(
            @RequestParam @ApiParam("素材id") Long materialId) {
        List<SsMaterialContent> ssMaterialContents = materialContentService.getContentByMaterialId(materialId);
        return BaseResult.success(ssMaterialContents);
    }

    @GetMapping("content/{materialContentId}")
    @ApiOperation("根据id获取素材内容")
    public BaseResult<SsMaterialContent> getMaterialContentById(
            @PathVariable @ApiParam("素材内容id") Long materialContentId
    ) {
        SsMaterialContent materialContent = materialContentService.getMaterialContentById(materialContentId);
        return BaseResult.success(materialContent);
    }

    @GetMapping("operate/record")
    @ApiOperation("素材获取操作记录")
    public BaseResult<List<SsMaterialOperateRecord>> getMaterialOperateRecord(@RequestParam @ApiParam("素材id") Long materialId) {
        List<SsMaterialOperateRecord> record = materialService.getMaterialOperateRecord(materialId);
        return BaseResult.success(record);
    }

    @GetMapping("same-content")
    @ApiOperation("获取相同内容的素材")
    public PageResult<List<SameMaterialContentResp>> getSameMaterialContent(
            @RequestParam @ApiParam("素材id") Long materialId,
            @RequestParam @ApiParam("素材内容md5") String contentMd5,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        Instant endTime = DateUtils.getDayOfStart(Instant.now()).plus(1, ChronoUnit.DAYS);
        Instant startTime = endTime.minus(31, ChronoUnit.DAYS);
        return materialService.getContentListByMd5(materialId, contentMd5, startTime, endTime, current, size);
    }

    @GetMapping("getMaterialByIdList")
    @ApiOperation("根据素材id批量获取素材")
    BaseResult<List<SsMaterial>> getMaterialByIdList(@RequestParam @ApiParam("营销素材id") List<Long> materialIds) {
        return BaseResult.success(materialService.getMaterialByIdList(materialIds));
    }

    @GetMapping("getMaterialIdsMapByOriginMaterialIds")
    @ApiOperation("获取引用该好评素材的营销素材id列表")
    BaseResult<Map<Long, List<Long>>> getMaterialIdsMapByOriginMaterialIds(@RequestParam @ApiParam("好评素材列表") List<Long> originMaterialIds){
        return BaseResult.success(materialService.getMaterialIdsMapByOriginMaterialIds(originMaterialIds));
    }

    @PostMapping("relate-origin-material")
    @ApiOperation("关联好评素材")
    BaseResult<Void> relateOriginMaterial(@RequestBody RelateOriginMaterialReq req) {
        materialService.relateOriginMaterial(req);
        return BaseResult.successful();
    }

}

package cn.shrise.radium.contentservice.consumer;

import cn.shrise.radium.common.annotation.OnsOrderConsumer;
import cn.shrise.radium.contentservice.resp.VodCallbackResp;
import cn.shrise.radium.contentservice.service.vod.AliVodService;
import cn.shrise.radium.contentservice.service.vod.VodVideoStrategyService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.order.ConsumeOrderContext;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.aliyun.openservices.ons.api.order.OrderAction;
import com.aliyuncs.vod.model.v20170321.GetCategoriesResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import static cn.shrise.radium.common.constant.RocketMQConstant.GROUP_VOD;
import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_VOD;

@Slf4j
@Service
@OnsOrderConsumer(
        topic = TOPIC_VOD,
        consumerGroup = GROUP_VOD
)
@RequiredArgsConstructor
public class VodVideoConsumer implements MessageOrderListener {

    private final VodVideoStrategyService vodVideoStrategyService;
    private final AliVodService aliVodService;

    @Override
    public OrderAction consume(Message message, ConsumeOrderContext consumeOrderContext) {
        VodCallbackResp resp = JSON.parseObject(new String(message.getBody()), VodCallbackResp.class);
        if (ObjectUtils.isEmpty(resp)) {
            return OrderAction.Success;
        }

        String extend = resp.getExtend();
        if (ObjectUtils.isNotEmpty(extend)) {
            JSONObject jsonObject = JSON.parseObject(extend);
            Long cateId = jsonObject.getLong("cateId");
            Integer companyType = jsonObject.getInteger("companyType");

            String cateName = null;
            if (cateId != null) {
                GetCategoriesResponse category = aliVodService.getCategory(cateId, 1L, 100L);
                cateName = category.getCategory1().getCateName();
            }

            vodVideoStrategyService.vodVideoStrategy(companyType, cateName, resp);
        }
        return OrderAction.Success;
    }

}

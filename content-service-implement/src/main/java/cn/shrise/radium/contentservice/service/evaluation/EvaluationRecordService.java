package cn.shrise.radium.contentservice.service.evaluation;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.EvaluationUtil;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.repository.CustomerEvaluationRepository;
import cn.shrise.radium.contentservice.repository.EvaluationIdRecordRepository;
import cn.shrise.radium.contentservice.repository.EvaluationRecordRepository;
import cn.shrise.radium.contentservice.req.IdVerifyReq;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

import static cn.shrise.radium.contentservice.constant.EvaluationIdRecordTypeConstant.ARTIFICIAL;
import static cn.shrise.radium.contentservice.constant.EvaluationIdRecordTypeConstant.ID_REDO;
import static cn.shrise.radium.contentservice.constant.EvaluationRecordTypeConstant.REDO;
import static cn.shrise.radium.contentservice.enums.ContentErrorCode.RECORD_NOT_EXISTED;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EvaluationRecordService {

    private final JPAQueryFactory queryFactory;

    final QEvaluationRecord evaluationRecord = QEvaluationRecord.evaluationRecord;

    final QEvaluationIdRecord evaluationIdRecord = QEvaluationIdRecord.evaluationIdRecord;

    final QSsCustomerEvaluation customerEvaluation = QSsCustomerEvaluation.ssCustomerEvaluation;

    private final CustomerEvaluationRepository customerEvaluationRepository;

    private final EvaluationRecordRepository evaluationRecordRepository;

    private final EvaluationIdRecordRepository evaluationIdRecordRepository;

    private final CustomerEvaluationService customerEvaluationService;

    private final EvaluationUtil evaluationUtil;

    public PageResult<List<EvaluationRecord>> getByUser(Integer userId, Integer current, Integer size) {

        JPAQuery<EvaluationRecord> query = queryFactory.selectFrom(evaluationRecord)
                .leftJoin(customerEvaluation)
                .on(evaluationRecord.evaluationId.eq(customerEvaluation.id))
                .where(customerEvaluation.userId.eq(userId));

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        List<EvaluationRecord> fetch = query
                .orderBy(evaluationRecord.gmtCreate.desc(), evaluationRecord.id.desc())
                .fetch();
        return PageResult.success(fetch, Pagination.of(current, size, total));
    }

    public PageResult<List<EvaluationIdRecord>> getEvaluationIdRecord(Integer userId, Integer current, Integer size) {
        JPAQuery<EvaluationIdRecord> query = queryFactory.selectFrom(evaluationIdRecord)
                .leftJoin(customerEvaluation)
                .on(evaluationIdRecord.evaluationId.eq(customerEvaluation.id))
                .where(customerEvaluation.userId.eq(userId));

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        List<EvaluationIdRecord> fetch = query
                .orderBy(evaluationIdRecord.gmtCreate.desc(), evaluationIdRecord.id.desc())
                .fetch();
        return PageResult.success(fetch, Pagination.of(current, size, total));
    }

    @Transactional(rollbackFor = Exception.class)
    public SsCustomerEvaluation evaluationRedo(Integer evaluationId, Integer userId) {
        SsCustomerEvaluation existedOne = customerEvaluationRepository.findById(evaluationId).orElseThrow(() -> new BusinessException(RECORD_NOT_EXISTED));
        if (existedOne.getIsDeprecated()) {
            throw new BusinessException("该测评已经废弃");
        }
        customerEvaluationService.abandonOneById(evaluationId);
        List<String> numberList = customerEvaluationService.findAllNumber(existedOne.getCompanyType());
        SsCustomerEvaluation copyEntity = SsCustomerEvaluation.builder()
                .number(customerEvaluationService.generateEvaluationNumber(existedOne.getCompanyType(), numberList))
                .mobile(existedOne.getMobile())
                .companyType(existedOne.getCompanyType())
                .isDeprecated(false)
                .userId(existedOne.getUserId())
                .identityType(existedOne.getIdentityType())
                .identityNumber(existedOne.getIdentityNumber())
                .sex(existedOne.getSex())
                .idVerifyType(existedOne.getIdVerifyType())
                .idVerifyStatus(existedOne.getIdVerifyStatus())
                .name(existedOne.getName())
                .build();
        String identityNumber = AESUtil.decrypt(copyEntity.getIdentityNumber());
        if (ObjectUtil.isNotEmpty(identityNumber) && identityNumber.length() >= 15 && identityNumber.length() <= 18) {
            DateTime birthDate = IdcardUtil.getBirthDate(identityNumber);
            copyEntity.setBirthday(birthDate.toLocalDateTime().toLocalDate());
            copyEntity.setRegion(IdcardUtil.getProvinceByIdCard(identityNumber));
        }
        SsCustomerEvaluation newOne = customerEvaluationRepository.save(copyEntity);
        EvaluationRecord build = EvaluationRecord.builder()
                .evaluationId(evaluationId)
                .operateType(REDO)
                .operatorId(userId)
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now())
                .build();
        evaluationRecordRepository.save(build);
        return newOne;
    }

    @Transactional(rollbackFor = Exception.class)
    public void evaluationIdRedo(Integer evaluationId, Integer userId, Boolean isAbolish) {

        queryFactory.update(customerEvaluation)
                .set(customerEvaluation.isDeprecated, true)
                .where(customerEvaluation.id.eq(evaluationId))
                .execute();

        SsCustomerEvaluation evaluation = customerEvaluationService.findById(evaluationId).orElseThrow(() -> new BusinessException(RECORD_NOT_EXISTED));
        if (isAbolish) {
            evaluationUtil.deleteUserEvaluationKey(evaluation.getUserId());
        }
        EvaluationIdRecord build = EvaluationIdRecord.builder()
                .evaluationId(evaluationId)
                .operateType(ID_REDO)
                .operatorId(userId)
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now())
                .build();
        evaluationIdRecordRepository.save(build);
    }

    @Transactional(rollbackFor = Exception.class)
    public void evaluationIdVerify(IdVerifyReq req) {
        JPAUpdateClause query = queryFactory.update(customerEvaluation)
                .set(customerEvaluation.idVerifyStatus, req.getIdVerifyStatus())
                .where(customerEvaluation.id.eq(req.getEvaluationId()));
        if (req.getUrlList() != null && req.getUrlList().isEmpty()) {
            String join = String.join(",", req.getUrlList());
            query.set(customerEvaluation.idImageList, join);
        }
        query.execute();
        EvaluationIdRecord build = EvaluationIdRecord.builder()
                .evaluationId(req.getEvaluationId())
                .operateType(ARTIFICIAL)
                .operatorId(req.getUserId())
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now())
                .build();
        evaluationIdRecordRepository.save(build);
    }

}

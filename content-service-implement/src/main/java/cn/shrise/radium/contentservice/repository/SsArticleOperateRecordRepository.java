package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsAdvert;
import cn.shrise.radium.contentservice.entity.SsArticleOperateRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface SsArticleOperateRecordRepository extends JpaRepository<SsArticleOperateRecord, Long>, QuerydslPredicateExecutor<SsArticleOperateRecord> {
}

package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.entity.SsCustomerComplaint;
import cn.shrise.radium.contentservice.service.CustomerComplaintService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("customer/complaint")
@RequiredArgsConstructor
public class CustomerComplaintController {

    private final CustomerComplaintService customerComplaintService;

    @ApiOperation("客户投诉与建议")
    @GetMapping("by/isHandle")
    public PageResult<List<SsCustomerComplaint>> getCustomerComplaintList(
            @RequestParam @ApiParam("是否处理") Boolean isHandle,
            @RequestParam(required = false) @ApiParam("来源") String source,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size
    ) {
        return customerComplaintService.getCustomerComplaintList(isHandle, source, current, size);
    }

    @ApiOperation("处理/客户投诉与建议")
    @PostMapping("createOrUpdate")
    public BaseResult<Void> createOrUpdate(
            @RequestParam(required = false) @ApiParam("id") Long id,
            @RequestParam(required = false) @ApiParam("wxId") Integer wxId,
            @RequestParam(required = false) @ApiParam("userId") Integer userId,
            @RequestParam(required = false) @ApiParam("内容") String content,
            @RequestParam(required = false) @ApiParam("图片列表") List<String> images,
            @RequestParam(required = false) @ApiParam("审核人id") Integer auditId,
            @RequestParam(required = false) @ApiParam("来源") String source,
            @RequestParam(required = false) @ApiParam("处理结果") String handleResult) {
        customerComplaintService.createOrUpdate(id, wxId, userId, content, images, auditId, source, handleResult);
        return BaseResult.successful();
    }

    @ApiOperation("获取客户投诉与建议详情")
    @GetMapping("info")
    public BaseResult<SsCustomerComplaint> getCustomerComplaintInfo(@RequestParam @ApiParam("id") Long id) {
        return customerComplaintService.getCustomerComplaintInfo(id);
    }
}

package cn.shrise.radium.contentservice.service.vod;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.AliyunProperties;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.properties.ServerProperties;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.common.util.UrlUtil;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.req.VodCreateUploadReq;
import cn.shrise.radium.contentservice.resp.VodCallbackResp;
import cn.shrise.radium.contentservice.resp.VodCreateUploadResp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.vod.model.v20170321.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.shrise.radium.common.constant.CompanyTypeConstant.CT_GCTS;

@Slf4j
@Service
@RequiredArgsConstructor
public class AliVodService {

    private final IAcsClient iAcsClient;
    private final RocketMqUtils rocketMqUtils;
    private final ServerProperties serverProperties;
    private final CommonProperties commonProperties;
    private final AliyunProperties aliyunProperties;

    public List<GetCategoriesResponse.Category> getCategories(Long cateId, Long pageNo, Long pageSize) {
        GetCategoriesRequest request = new GetCategoriesRequest();
        request.setCateId(cateId);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        try {
            GetCategoriesResponse response = iAcsClient.getAcsResponse(request);
            return response.getSubCategories();
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取分类及其子分类失败，msg: %s", e));
        }
    }

    public GetCategoriesResponse getCategory(Long cateId, Long pageNo, Long pageSize) {
        GetCategoriesRequest request = new GetCategoriesRequest();
        request.setCateId(cateId);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取分类失败，msg: %s", e));
        }
    }

    public List<GetCategoriesResponse.Category> getCompanyCateList(Integer companyType, Long parentCateId) {
        String companyCateName = String.format("%s%s",
                Objects.equals(serverProperties.getEnv(), "prod") ? "p" : "d", companyType);

        long careId = parentCateId == null? -1 : parentCateId;
        List<GetCategoriesResponse.Category> categories = this.getCategories(careId, 1L, 100L);

        if (careId == -1L) {
            GetCategoriesResponse.Category cate = categories.stream()
                    .filter(e -> Objects.equals(e.getCateName(), companyCateName)).findFirst()
                    .orElseThrow(() -> new BusinessException(String.format("未配置公司分类，公司类型：%s", companyType)));
            return this.getCategories(cate.getCateId(), 1L, 100L);
        }
        return categories;
    }

    public List<ListTranscodeTemplateGroupResponse.TranscodeTemplateGroup> getTranscodeTemplateGroupList() {
        ListTranscodeTemplateGroupRequest request = new ListTranscodeTemplateGroupRequest();
        try {
            ListTranscodeTemplateGroupResponse response = iAcsClient.getAcsResponse(request);
            return response.getTranscodeTemplateGroupList();
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取转码模组列表失败，msg: %s", e));
        }
    }

    public CreateUploadVideoResponse createUploadVideo(String title, String fileName, Long cateId, String templateGroupId,
                                                       String description, String coverURL, String callbackPath,
                                                       String behavior) {
        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        request.setTitle(title);
        request.setFileName(fileName);
        if (ObjectUtil.isNotNull(cateId)) {
            request.setCateId(cateId);
        }
        if (ObjectUtil.isNotNull(templateGroupId)) {
            request.setTemplateGroupId(templateGroupId);
        }
        if (ObjectUtil.isNotNull(description)) {
            request.setDescription(description);
        }
        if (ObjectUtil.isNotNull(coverURL)) {
            request.setCoverURL(coverURL);
        }
        if (ObjectUtil.isNotEmpty(callbackPath)) {
            JSONObject messageCallback = new JSONObject();
            String callbackUrl = commonProperties.getCallbackUrl();
            String url = callbackUrl.endsWith("/") ? callbackUrl.substring(0, callbackUrl.length() - 1) : callbackUrl;
            messageCallback.put("CallbackURL", StrUtil.format("{}/{}", url, callbackPath));
            messageCallback.put("CallbackType", "https");

            JSONObject userData = new JSONObject();
            userData.put("MessageCallback", messageCallback.toJSONString());

            if (ObjectUtil.isNotEmpty(behavior)) {
                JSONObject extend = new JSONObject();
                // 对不同数据库操作的行为
                extend.put("Behavior", behavior);
                userData.put("Extend", extend.toJSONString());
            }
            request.setUserData(userData.toJSONString());
        }
        try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取视频上传地址和凭证失败，msg: %s", e));
        }
    }

    public VodCreateUploadResp vodCreateUploadVideo(VodCreateUploadReq vodCreateUploadReq) {
        String title = vodCreateUploadReq.getTitle();
        String fileName = vodCreateUploadReq.getFileName();
        String templateGroupId = vodCreateUploadReq.getTemplateGroupId();
        String description = vodCreateUploadReq.getDescription();
        String coverUrl = vodCreateUploadReq.getCoverUrl();
        String ossCoverUrl = UrlUtil.concat(commonProperties.getOssDomain(), coverUrl);

        Integer companyType = vodCreateUploadReq.getCompanyType();
        AliyunProperties.VodProduct vodProduct = aliyunProperties.getVod();
        String callbackUrl = vodProduct.getCallbackUrl();
        Long cateId = vodCreateUploadReq.getCateId();

        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        request.setTitle(title);
        request.setFileName(fileName);
        if (ObjectUtil.isNotNull(cateId)) {
            request.setCateId(cateId);
        }
        if (ObjectUtil.isNotNull(templateGroupId)) {
            request.setTemplateGroupId(templateGroupId);
        }
        if (ObjectUtil.isNotNull(description)) {
            request.setDescription(description);
        }
        if (ObjectUtil.isNotNull(ossCoverUrl)) {
            request.setCoverURL(ossCoverUrl);
        }
        if (ObjectUtil.isNotEmpty(callbackUrl)) {
            JSONObject messageCallback = new JSONObject();
            messageCallback.put("CallbackURL", callbackUrl);
            messageCallback.put("CallbackType", "https");

            JSONObject userData = new JSONObject();
            userData.put("MessageCallback", messageCallback.toJSONString());
            JSONObject extend = new JSONObject();
            //传递cateId
            extend.put("cateId", cateId);
            extend.put("companyType", companyType);
            userData.put("Extend", extend.toJSONString());
            request.setUserData(userData.toJSONString());
        }
        try {
            CreateUploadVideoResponse acsResponse = iAcsClient.getAcsResponse(request);
            return VodCreateUploadResp.builder()
                    .videoId(acsResponse.getVideoId())
                    .uploadAuth(acsResponse.getUploadAuth())
                    .uploadAddress(acsResponse.getUploadAddress())
                    .build();
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取视频上传地址和凭证失败，msg: %s", e));
        }
    }

    public Optional<GetCategoriesResponse.Category> transformCategory(Integer companyType, String categoryPath) {
        List<GetCategoriesResponse.Category> cateList = getCompanyCateList(companyType, -1L);
        String[] split = categoryPath.split("_");

        if (split.length == 1) {
            return cateList.stream().filter(e -> Objects.equals(e.getCateName(), split[0])).findFirst();
        } else {
            String parentCategory = split[0];
            String subCategory = split[split.length - 1];
            Optional<GetCategoriesResponse.Category> optional = cateList.stream()
                    .filter(e -> Objects.equals(e.getCateName(), parentCategory)).findFirst();
            if (optional.isPresent()) {
                Long parentCateId = optional.get().getCateId();
                List<GetCategoriesResponse.Category> categories = getCategories(parentCateId, 1L, 100L);
                return categories.stream().filter(e -> Objects.equals(e.getCateName(), subCategory)).findFirst();
            } else {
                return Optional.empty();
            }
        }
    }

    public RefreshUploadVideoResponse refreshUploadVideo(String videoId) {
        RefreshUploadVideoRequest request = new RefreshUploadVideoRequest();
        request.setVideoId(videoId);
        try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            throw new BusinessException(String.format("刷新视频上传凭证失败，msg: %s", e));
        }
    }

    public UpdateVideoInfoResponse updateVideoInfo(String videoId, String title, String description, String coverURL) {
        UpdateVideoInfoRequest request = new UpdateVideoInfoRequest();
        request.setVideoId(videoId);
        if (ObjectUtil.isNotEmpty(title)) {
            request.setTitle(title);
        }
        if (ObjectUtil.isNotEmpty(description)) {
            request.setDescription(description);
        }
        if (ObjectUtil.isNotEmpty(coverURL)) {
            request.setCoverURL(coverURL);
        }try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            throw new BusinessException(String.format("更新视频信息失败，msg: %s", e));
        }
    }

    public GetVideoPlayAuthResponse getVideoPlayAuth(String videoId) {
        GetVideoInfoResponse videoInfo = this.getVideoInfo(videoId);
        if (Objects.equals(videoInfo.getVideo().getStatus(), "Normal")) {
            GetVideoPlayAuthRequest request = new GetVideoPlayAuthRequest();
            request.setVideoId(videoId);
            try {
                return iAcsClient.getAcsResponse(request);
            } catch (ClientException e) {
                throw new BusinessException(String.format("获取播放凭证失败，msg: %s", e));
            }
        } else {
            log.error("getVideoPlayAuth--videoId:{},result:{}", videoId, JSON.toJSONString(videoInfo.getVideo()));
            throw new BusinessException("视频状态非正常");
        }
    }

    public GetVideoInfoResponse getVideoInfo(String videoId) {
        GetVideoInfoRequest request = new GetVideoInfoRequest();
        request.setVideoId(videoId);
        try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取单个视频信息失败，msg: %s", e));
        }
    }

    public GetPlayInfoResponse getPlayInfo(String videoId) {
        GetPlayInfoRequest request = new GetPlayInfoRequest();
        request.setVideoId(videoId);
        try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取单个视频播放信息失败，msg: %s", e));
        }
    }

    public void videoUploadResult(String body) {
        VodCallbackResp callbackResp = JSON.parseObject(body, VodCallbackResp.class);
        if (Objects.equals(callbackResp.getStatus(), "success") && ObjectUtils.isNotEmpty(callbackResp.getExtend())) {
            // tag对应（VodBehaviorConstant）
            JSONObject jsonObject = JSONObject.parseObject(callbackResp.getExtend());
            String behavior = jsonObject.getString("Behavior");
            rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, behavior, callbackResp);
        }
    }

    /**
     * ali视频点播提交Ai作业接口
     *
     * @param mediaId 视频id
     * @param type    作业类型 AIMediaDNA：视频DNA。AIVideoTag：智能标签。
     */
    public SubmitAIJobResponse submitAIJob(String mediaId, String type) {

        SubmitAIJobRequest request = new SubmitAIJobRequest();
        // 设置视频ID
        request.setMediaId(mediaId);
        // 设置AI类型，请确保已开通该类型AI
        request.setTypes(type);

        // 设置回调地址
        JSONObject messageCallback = new JSONObject();

        String callbackUrl = commonProperties.getCallbackUrl();
        String url = callbackUrl.endsWith("/") ? callbackUrl.substring(0, callbackUrl.length() - 1) : callbackUrl;
        if (Objects.equals(type, "AIVideoTag")) {
            // 智能标签回调url
            messageCallback.put("CallbackURL", String.format("%s/vod/sales/video/short/tag", url));
        }
        messageCallback.put("CallbackType", "https");

        JSONObject userData = new JSONObject();
        userData.put("MessageCallback", messageCallback.toJSONString());
        JSONObject extend = new JSONObject();
        extend.put("companyType", CT_GCTS.getValue());
        userData.put("Extend", extend.toJSONString());
        request.setUserData(userData.toJSONString());

        log.info("submitAIJob mediaId: {}, type: {}", mediaId, type);
        try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            log.info("视频提交ai作业失败，mediaId: {}, type: {},msg: {}", mediaId, type, e);
            throw new BusinessException(String.format("视频提交ai作业失败，msg: %s", e));
        }
    }

    /**
     * ali视频点播提交Ai作业接口
     *
     * @param mediaId 视频id
     * @param type    作业类型 AIMediaDNA：视频DNA。AIVideoTag：智能标签。
     */
    public SubmitAIJobResponse submitAIJob(Integer companyType, String mediaId, String type) {

        SubmitAIJobRequest request = new SubmitAIJobRequest();
        // 设置视频ID
        request.setMediaId(mediaId);
        // 设置AI类型，请确保已开通该类型AI
        request.setTypes(type);

        // 设置回调地址
        JSONObject messageCallback = new JSONObject();

        String callbackUrl = commonProperties.getCallbackUrl();
        String url = callbackUrl.endsWith("/") ? callbackUrl.substring(0, callbackUrl.length() - 1) : callbackUrl;
        if (Objects.equals(type, "AIVideoTag")) {
            // 智能标签回调url
            messageCallback.put("CallbackURL", String.format("%s/vod/sales/video/short/tag", url));
        }
        messageCallback.put("CallbackType", "https");

        JSONObject userData = new JSONObject();
        userData.put("MessageCallback", messageCallback.toJSONString());
        JSONObject extend = new JSONObject();
        extend.put("companyType", companyType);
        userData.put("Extend", extend.toJSONString());
        request.setUserData(userData.toJSONString());

        log.info("submitAIJob mediaId: {}, type: {}", mediaId, type);
        try {
            return iAcsClient.getAcsResponse(request);
        } catch (ClientException e) {
            log.info("视频提交ai作业失败，mediaId: {}, type: {},msg: {}", mediaId, type, e);
            throw new BusinessException(String.format("视频提交ai作业失败，msg: %s", e));
        }
    }

    public void handleShortVideoTagResult(String body) {

        VodCallbackResp callbackResp = JSON.parseObject(body, VodCallbackResp.class);
        log.info("Ai作业提交回调结果,callbackResp:{}", callbackResp);
        if (Objects.equals(callbackResp.getEventType(), "AIVideoTagComplete")) {//视频智能标签事件完成
            rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, ContentServiceConst.MqTagType.SHORT_VIDEO_TAG, callbackResp);
        }
    }

    public PageResult<List<SearchMediaResponse.Media>> searchMedia(String title, Long cateId, Boolean isAsc, String status,
                                                                   Instant startTime, Instant endTime,
                                                                   Integer current, Integer size) {
        List<String> match = new ArrayList<>();
        if (startTime != null || endTime != null) {
            String startTimeStr = startTime != null ? startTime.toString() : "";
            String endTimeStr = endTime != null ? endTime.toString() : "";
            match.add(String.format("CreationTime = ('%s','%s')", startTimeStr, endTimeStr));
        }
        if (cateId != null && cateId != -1L) {
            match.add(String.format("CateId = %d", cateId));
        }
        if (title != null) {
            match.add(String.format("Title in ('%s')", title));
        }
        if (status != null) {
            match.add(String.format("Status in ('%s')", status));
        }

        SearchMediaRequest request = new SearchMediaRequest();
        request.setSearchType("video");
        request.setFields("VideoId,AudioId,CateId,CateName,StorageLocation,RegionId,Title,Tags,Description,Status," +
                "MediaSource,PreprocessStatus,Size,Duration,CreationTime,ModificationTime,CoverURL,Snapshots," +
                "DownloadSwitch,TranscodeMode,PlayInfoList");
        if (match.size() > 0) {
            request.setMatch(String.join(" and ", match));
        }
        request.setPageNo(current);
        request.setPageSize(size);
        request.setSortBy(String.format("CreationTime:%s", isAsc ? "Asc" : "Desc"));
        try {
            SearchMediaResponse response = iAcsClient.getAcsResponse(request);
            return PageResult.success(response.getMediaList(), Pagination.of(current, size, response.getTotal()));
        } catch (ClientException e) {
            throw new BusinessException(String.format("获取视频列表失败，msg: %s", e));
        }
    }

}

package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.contentservice.entity.QSsLiveRoom;
import cn.shrise.radium.contentservice.entity.QSsRoomSalesRelation;
import cn.shrise.radium.contentservice.entity.SsLiveRoom;
import cn.shrise.radium.contentservice.repository.LiveRoomRepository;
import cn.shrise.radium.contentservice.resp.LiveRoomNameResp;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.CST_PayRoom;
import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.ROOM;

@Service
@RequiredArgsConstructor
public class LiveRoomService {

    private final LiveRoomRepository liveRoomRepository;
    private final JPAQueryFactory queryFactory;

    private final QSsLiveRoom liveRoom = QSsLiveRoom.ssLiveRoom;

    public List<LiveRoomNameResp> getLiveRoomName(Collection<Long> roomIds, Boolean isLive, Boolean isDeleted, Integer companyType, Integer roomType, Integer salesId) {
        QSsLiveRoom ssLiveRoom = QSsLiveRoom.ssLiveRoom;
        QSsRoomSalesRelation ssRoomSalesRelation = QSsRoomSalesRelation.ssRoomSalesRelation;
        JPAQuery<Tuple> query = queryFactory.select(ssLiveRoom.id, ssLiveRoom.name)
                .from(ssLiveRoom)
                .leftJoin(ssRoomSalesRelation).on(ssRoomSalesRelation.roomId.eq(ssLiveRoom.id)).groupBy(ssLiveRoom.id);
        if (!ObjectUtils.isEmpty(salesId)) {
            query.where(ssRoomSalesRelation.salesId.eq(salesId));
        }
        if (!ObjectUtils.isEmpty(roomIds)) {
            query.where(ssLiveRoom.id.in(roomIds));
        }
        if (!ObjectUtils.isEmpty(isLive)) {
            query.where(ssLiveRoom.isLive.eq(isLive));
        }
        if (!ObjectUtils.isEmpty(isDeleted)) {
            query.where(ssLiveRoom.isDeleted.eq(isDeleted));
        }
        if (!ObjectUtils.isEmpty(companyType)) {
            query.where(ssLiveRoom.companyType.eq(companyType));
        }
        if (!ObjectUtils.isEmpty(roomType)) {
            query.where(ssLiveRoom.roomType.eq(roomType));
        }
        List<Tuple> fetch = query.orderBy().fetch();
        List<LiveRoomNameResp> liveRoomNameResps = new ArrayList<>();
        if (!ObjectUtils.isEmpty(fetch)) {
            fetch.forEach(e -> {
                LiveRoomNameResp liveRoomNameResp = new LiveRoomNameResp(e.get(ssLiveRoom.id), e.get(ssLiveRoom.id) + "-" + e.get(ssLiveRoom.name));
                liveRoomNameResps.add(liveRoomNameResp);
            });
        }
        return liveRoomNameResps;
    }

    public Optional<SsLiveRoom> findOne(@NonNull Long roomId){
        return liveRoomRepository.findById(roomId);
    };

    public List<SsLiveRoom> findAll(Integer companyType, Boolean filterDeleted) {
        JPAQuery<SsLiveRoom> query =
                queryFactory.select(liveRoom).from(liveRoom).where(liveRoom.companyType.eq(companyType));
        if (ObjectUtil.isNotNull(filterDeleted) && filterDeleted) {
            query = query.where(liveRoom.isDeleted.eq(false).or(liveRoom.isDeleted.isNull()));
        }
        return query.fetch();
    }

    public Optional<ServiceIntroduction> getLiveRoomServiceIntroduction(Integer serviceId) {
        SsLiveRoom room = queryFactory.selectFrom(liveRoom)
                .where(liveRoom.id.eq(serviceId.longValue()))
                .fetchOne();
        ServiceIntroduction introduction = transformToServiceIntroduction(room);
        return Optional.ofNullable(introduction);
    }

    public List<ServiceIntroduction> getLiveRoomServiceIntroductionList(Collection<Integer> serviceIds) {
        if (ObjectUtils.isEmpty(serviceIds)) {
            return Collections.emptyList();
        }

        List<Long> distinctIds = serviceIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList());

        List<SsLiveRoom> roomList = queryFactory.selectFrom(liveRoom)
                .where(liveRoom.id.in(distinctIds))
                .fetch();

        return transformToServiceIntroduction(roomList);
    }

    public List<ServiceIntroduction> getLiveRoomServiceIntroductionList(Integer companyType, Collection<String> serviceNumbers, boolean sorted) {
        if (ObjectUtils.isEmpty(serviceNumbers)) {
            return Collections.emptyList();
        }

        List<SsLiveRoom> roomList = queryFactory.selectFrom(liveRoom)
                .where(liveRoom.number.in(serviceNumbers))
                .where(liveRoom.companyType.eq(companyType))
                .fetch();
        List<ServiceIntroduction> introductionList = transformToServiceIntroduction(roomList);
        return sorted? sortServiceIntroduction(serviceNumbers, introductionList): introductionList;
    }

    private List<ServiceIntroduction> transformToServiceIntroduction(List<SsLiveRoom> roomList) {
        if (ObjectUtils.isEmpty(roomList)) {
            return Collections.emptyList();
        }

        return roomList.stream()
                .map(this::transformToServiceIntroduction)
                .collect(Collectors.toList());
    }

    private ServiceIntroduction transformToServiceIntroduction(SsLiveRoom room) {
        Long serviceId = room.getId();
        Map<String, Object> properties = new HashMap<>();
        properties.put("isLive", room.getIsLive());
        properties.put("roomType", room.getRoomType());
        return ServiceIntroduction.builder()
                .serviceId(serviceId)
                .serviceType(CST_PayRoom)
                .number(room.getNumber())
                .serviceName(room.getName())
                .name(room.getName())
                .type(ROOM)
                .category(room.getRoomType())
                .properties(properties)
                .build();
    }

    private List<ServiceIntroduction> sortServiceIntroduction(Collection<String> serviceNumbers, List<ServiceIntroduction> introductionList) {
        Map<String, ServiceIntroduction> introductionMap = introductionList.stream()
                .collect(Collectors.toMap(ServiceIntroduction::getNumber, Function.identity()));
        return serviceNumbers.stream()
                .map(introductionMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}

package cn.shrise.radium.contentservice.repository;


import cn.shrise.radium.contentservice.entity.SsSoftwarePackage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2024/6/26 10:14
 * @Desc:
 **/
public interface SoftwarePackageRepository extends JpaRepository<SsSoftwarePackage, Long>, QuerydslPredicateExecutor<SsSoftwarePackage> {

    SsSoftwarePackage findByPackageTypeAndVersion(Integer packageType, String version);


}

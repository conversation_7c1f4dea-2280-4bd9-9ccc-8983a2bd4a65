package cn.shrise.radium.contentservice.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.common.util.JsonUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.constant.ShortVideoTagStatusEnum;
import cn.shrise.radium.contentservice.entity.SsShortVideo;
import cn.shrise.radium.contentservice.entity.SsShortVideoTag;
import cn.shrise.radium.contentservice.entity.SsVideoAiTag;
import cn.shrise.radium.contentservice.resp.AIVideoTagResultResp;
import cn.shrise.radium.contentservice.resp.AiRecShortVideoResp;
import cn.shrise.radium.contentservice.resp.VodCallbackResp;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import cn.shrise.radium.contentservice.service.ShortVideoTagService;
import cn.shrise.radium.contentservice.service.vod.VideoAiTagService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.shrise.radium.contentservice.constant.ShortVideoTagStatusEnum.TAG_STATUS_GETTING;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(
        topic = ContentServiceConst.CONTENT_TOPIC,
        consumerGroup = ContentServiceConst.MqGroupType.GID_SHORT_VIDEO_TAG,
        selectorExpression = ContentServiceConst.MqTagType.SHORT_VIDEO_TAG
)
public class ShortVideoTagConsumer implements MessageListener {

    private final ShortVideoService shortVideoService;
    private final ShortVideoTagService shortVideoTagService;
    private final RocketMqUtils rocketMqUtils;
    private final VideoAiTagService videoAiTagService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        VodCallbackResp callbackResp = JSON.parseObject(new String(message.getBody()), VodCallbackResp.class);
        log.info("智能标签队列开始消费，jobId: {},MediaId: {}", callbackResp.getJobId(), callbackResp.getMediaId());
        String extend = callbackResp.getExtend();
        JSONObject jsonObject = JSON.parseObject(extend);
        Integer companyType = jsonObject.getInteger("companyType");

        String mediaId = callbackResp.getMediaId();
        SsVideoAiTag.SsVideoAiTagBuilder tagBuilder = SsVideoAiTag.builder()
                .companyType(companyType)
                .sourceId(mediaId)
                .jobId(callbackResp.getJobId())
                .code(callbackResp.getCode())
                .message(callbackResp.getMessage())
                .tagInfo(callbackResp.getData());
        if (Objects.equals(callbackResp.getStatus(), "success")) {
            // 成功
            AIVideoTagResultResp videoTagResultResp = JSON.parseObject(callbackResp.getData(), AIVideoTagResultResp.class);
            log.info("视频智能标签回调结果,videoTagResult: {}", videoTagResultResp);
            // 标签处理
            List<String> tags = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(videoTagResultResp.getCategory())) {
                tags.addAll(videoTagResultResp.getCategory().stream()
                        .map(AIVideoTagResultResp.AIVideoTagCategoryItem::getTag)
                        .collect(Collectors.toList()));
            }
            if (ObjectUtil.isNotEmpty(videoTagResultResp.getPerson())) {
                tags.addAll(videoTagResultResp.getPerson().stream()
                        .map(AIVideoTagResultResp.AIVideoTagResultItem::getTag)
                        .collect(Collectors.toList()));
            }
            String tagsJson = JsonUtils.toJson(tags.toArray());
            tagBuilder.tags(tagsJson)
                    .status(ShortVideoTagStatusEnum.TAG_STATUS_SUCCESS.getValue())
                    .message(ShortVideoTagStatusEnum.TAG_STATUS_SUCCESS.getMsg());
        } else {
            // 失败
            tagBuilder.status(ShortVideoTagStatusEnum.TAG_STATUS_FAIL.getValue());
        }

        SsVideoAiTag aiTag = tagBuilder.build();
        // 更新视频标签表
        videoAiTagService.createOrUpdateVideoAiTag(aiTag);
        // 更新原有表
        List<SsShortVideo> updateList = shortVideoService.getShortVideoList(mediaId, TAG_STATUS_GETTING.getValue());
        if (ObjectUtils.isNotEmpty(updateList)) {
            updateList.forEach(e -> {
                Long id = e.getId();
                videoAiTagService.copyToShortVideoTag(aiTag, id);
            });
        }

        if (Objects.equals(aiTag.getStatus(), ShortVideoTagStatusEnum.TAG_STATUS_SUCCESS.getValue())) {
            // 智能推荐
            AiRecShortVideoResp resp = shortVideoService.buildAiRecShortVideoResp(callbackResp.getMediaId(), aiTag.getTags());
            rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, ContentServiceConst.MqTagType.AI_REC_SHORT_VIDEO, resp);
        }
        return Action.CommitMessage;
    }
}

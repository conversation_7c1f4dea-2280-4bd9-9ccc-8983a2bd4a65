package cn.shrise.radium.contentservice.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.entity.SsVipNotice;
import cn.shrise.radium.contentservice.req.CreateOrUpdateVipNoticeReq;
import cn.shrise.radium.contentservice.service.VipNoticeService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("vip-notice")
@RequiredArgsConstructor
public class VipNoticeController {

    private final VipNoticeService vipNoticeService;

    @PostMapping("createOrUpdate")
    @ApiOperation("创建/编辑vip公告")
    public BaseResult<Void> createOrUpdateVipNotice(
            @RequestBody @Valid CreateOrUpdateVipNoticeReq req) {
        vipNoticeService.createOrUpdateOne(req);
        return BaseResult.successful();
    }

    @PostMapping("enable")
    @ApiOperation("启用/禁用vip公告")
    public BaseResult<Void> enableVipNotice(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("ID") Long id,
            @RequestParam @ApiParam("产品类型") Integer product,
            @RequestParam @ApiParam("启用/禁用") Boolean enable) {
        if (ObjectUtil.equals(enable, true)) {
            SsVipNotice enableOne = vipNoticeService.findEnableOne(companyType, product);
            if (ObjectUtil.isNotNull(enableOne)) {
                throw new BusinessException("仅能启用一个公告");
            }
        }
        vipNoticeService.setEnable(id, enable);
        return BaseResult.successful();
    }

    @GetMapping("list")
    @ApiOperation("获取vip公告列表")
    public PageResult<List<SsVipNotice>> getVipNoticeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("产品类型") Integer product,
            @RequestParam(required = false) @ApiParam("状态") Boolean enable,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return vipNoticeService.getVipNoticeList(companyType, product, enable, current, size);
    }

    @GetMapping()
    @ApiOperation("获取vip公告")
    public BaseResult<SsVipNotice> getVipNotice(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("产品类型") Integer product) {
        return BaseResult.success(vipNoticeService.findEnableOne(companyType, product));
    }
}

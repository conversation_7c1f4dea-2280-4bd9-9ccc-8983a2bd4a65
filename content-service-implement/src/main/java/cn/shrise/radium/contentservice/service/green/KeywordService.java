package cn.shrise.radium.contentservice.service.green;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.resp.KeywordLibResp;
import cn.shrise.radium.contentservice.resp.KeywordResp;
import cn.shrise.radium.contentservice.resp.KeywordsResultResp;
import com.alibaba.fastjson.JSON;
import com.aliyun.green20220926.Client;
import com.aliyun.green20220926.models.*;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 内容安全增强版 词库管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KeywordService {

    @Resource(name = "keywordClient")
    private final Client client;

    private static final int SUCCESS_CODE = 200;

    /**
     * 查询关键词库
     */
    public List<KeywordLibResp> getListKeywordLibs() {
        ListKeywordLibsRequest listKeywordLibsRequest = new ListKeywordLibsRequest();
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            runtime.setConnectTimeout(10000);
            runtime.setReadTimeout(3000);
            ListKeywordLibsResponse listKeywordLibsResponse = client.listKeywordLibsWithOptions(listKeywordLibsRequest, runtime);
            if (listKeywordLibsResponse.getStatusCode() == SUCCESS_CODE) {
                ListKeywordLibsResponseBody listKeywordLibsResponseBody = listKeywordLibsResponse.getBody();
                if (listKeywordLibsResponseBody.getSuccess()) {
                    List<KeywordLibResp> respList = new ArrayList<>();
                    List<ListKeywordLibsResponseBody.ListKeywordLibsResponseBodyData> dataList = listKeywordLibsResponseBody.getData();
                    for (ListKeywordLibsResponseBody.ListKeywordLibsResponseBodyData data : dataList) {
                        KeywordLibResp resp = KeywordLibResp.builder()
                                .libId(data.getLibId())
                                .libName(data.getLibName())
                                .gmtModified(data.getGmtModified())
                                .keywordCount(data.getKeywordCount())
                                .uid(data.getUid())
                                .build();
                        respList.add(resp);
                    }
                    return respList;
                } else {
                    System.out.println("responseBody not success, code:" + listKeywordLibsResponseBody.getCode());
                }
            } else {
                System.out.println("response not success, statusCode:" + listKeywordLibsResponse.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new BusinessException("查询词库失败");
    }

    /**
     * 清空词库
     *
     * @param libId 词库id
     */
    public Boolean deleteKeywordLib(String libId) {
        DeleteKeywordLibRequest deleteKeywordLibRequest = new DeleteKeywordLibRequest();
        //设置需要清空的词库ID
        deleteKeywordLibRequest.setLibId(libId);
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            runtime.setConnectTimeout(10000);
            runtime.setReadTimeout(3000);
            DeleteKeywordLibResponse deleteKeywordLibResponse = client.deleteKeywordLibWithOptions(deleteKeywordLibRequest, runtime);
            if (deleteKeywordLibResponse.getStatusCode() == SUCCESS_CODE) {
                DeleteKeywordLibResponseBody deleteKeywordLibResponseBody = deleteKeywordLibResponse.getBody();
                if (deleteKeywordLibResponseBody.getSuccess()) {
                    return deleteKeywordLibResponseBody.getData();
                } else {
                    System.out.println("deleteKeywordLib responseBody not success, code:" + deleteKeywordLibResponseBody.getCode());
                }
            } else {
                System.out.println("deleteKeywordLib response not success, statusCode:" + deleteKeywordLibResponse.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new BusinessException("清空词库失败");
    }

    /**
     * 词库添加关键词
     *
     * @param libId    词库id
     * @param keywords 关键词
     */
    public KeywordsResultResp addKeywordToLib(String libId, String keywords) {
        AddKeywordsToLibRequest addKeywordsToLibRequest = new AddKeywordsToLibRequest();
        //设置要添加关键词的词库ID
        addKeywordsToLibRequest.setLibId(libId);
        //设置要添加的关键词 多个关键词用 \n 分隔 最多1000个
        addKeywordsToLibRequest.setKeywords(keywords);
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            runtime.setConnectTimeout(10000);
            runtime.setReadTimeout(3000);
            AddKeywordsToLibResponse addKeywordsToLibResponse = client.addKeywordsToLibWithOptions(addKeywordsToLibRequest, runtime);
            if (addKeywordsToLibResponse.getStatusCode() == SUCCESS_CODE) {
                AddKeywordsToLibResponseBody addKeywordsToLibResponseBody = addKeywordsToLibResponse.getBody();
                String jsonString = JSON.toJSONString(addKeywordsToLibResponseBody.getData().getKeywordsResult(), true);
                return JSON.parseObject(jsonString, KeywordsResultResp.class);
            } else {
                System.out.println("listKeywords response not success, statusCode:" + addKeywordsToLibResponse.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new BusinessException("添加关键词失败");
    }


    /**
     * 查询关键词
     *
     * @param libId   词库id
     * @param current 页码
     * @param size    页面数量
     * @param isAsc   是否升序
     * @return 关键词列表
     */
    public PageResult<List<KeywordResp>> getListKeywords(String libId, Integer current, Integer size, Boolean isAsc) {
        ListKeywordsRequest listKeywordsRequest = new ListKeywordsRequest();
        //设置查询词库的ID
        listKeywordsRequest.setLibId(libId);
        //设置分页查询每页数量  不得超过100
        listKeywordsRequest.setPageSize(size);
        listKeywordsRequest.setCurrentPage(current);
        //设置查询条件 查询含有指定字符的关键词
//        listKeywordsRequest.setWord(searchContent);
        //构造排序方式，目前只支持根据关键词创建时间排序  desc: 降序 asc: 升序
        Map<String, String> sort = new HashMap<>(1);
        if (isAsc) {
            sort.put("GmtCreate", "asc");
        } else {
            sort.put("GmtCreate", "desc");
        }
        //设置排序方式
        listKeywordsRequest.setSort(sort);
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            runtime.setConnectTimeout(10000);
            runtime.setReadTimeout(3000);
            ListKeywordsResponse listKeywordsResponse = client.listKeywordsWithOptions(listKeywordsRequest, runtime);
            if (listKeywordsResponse.getStatusCode() == SUCCESS_CODE) {
                ListKeywordsResponseBody listKeywordsResponseBody = listKeywordsResponse.getBody();
                if (listKeywordsResponseBody.getSuccess()) {
                    ListKeywordsResponseBody.ListKeywordsResponseBodyData data = listKeywordsResponseBody.getData();
                    List<KeywordResp> respList = new ArrayList<>();
                    for (ListKeywordsResponseBody.ListKeywordsResponseBodyDataItems items : data.getItems()) {
                        KeywordResp resp = KeywordResp.builder()
                                .id(items.getId())
                                .gmtCreate(items.getGmtCreate())
                                .gmtModified(items.getGmtModified())
                                .keywordLibId(items.getKeywordLibId())
                                .keywordMd5Id(items.getKeywordMd5Id())
                                .word(items.getWord())
                                .build();
                        respList.add(resp);
                    }
                    return PageResult.success(respList, data.getCurrentPage(), data.getPageSize(), (long) data.getTotalCount());
                } else {
                    System.out.println("listKeywords responseBody not success, code:" + listKeywordsResponseBody.getCode());
                }
            } else {
                System.out.println("listKeywords response not success, statusCode:" + listKeywordsResponse.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new BusinessException("获取关键词失败");
    }


    /**
     * 删除关键词
     *
     * @param libId       词库id
     * @param keywordList 关键词id列表
     */
    public Boolean deleteKeyword(String libId, List<String> keywordList) {
        DeleteKeywordRequest deleteKeywordRequest = new DeleteKeywordRequest();
        //设置要删除词库ID
        deleteKeywordRequest.setLibId(libId);
        //设置要删除的关键词ID
        String keywords = String.join(",", keywordList);
        deleteKeywordRequest.setKeywordIds(String.format("[%s]", keywords));
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            runtime.setConnectTimeout(10000);
            runtime.setReadTimeout(3000);
            DeleteKeywordResponse deleteKeywordResponse = client.deleteKeywordWithOptions(deleteKeywordRequest, runtime);
            if (deleteKeywordResponse.getStatusCode() == SUCCESS_CODE) {
                DeleteKeywordResponseBody deleteKeywordResponseBody = deleteKeywordResponse.getBody();
                if (deleteKeywordResponseBody.getSuccess()) {
                    return deleteKeywordResponseBody.getData();
                } else {
                    System.out.println("deleteKeyword responseBody not success, code:" + deleteKeywordResponseBody.getCode());
                }
            } else {
                System.out.println("deleteKeyword response not success, statusCode:" + deleteKeywordResponse.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new BusinessException("删除关键词失败");
    }
}

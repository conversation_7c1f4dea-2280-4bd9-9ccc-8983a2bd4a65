package cn.shrise.radium.contentservice.controller.cls;

/**
 * @Author: tan<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/6 15:42
 * @Desc:
 **/

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.cls.entity.*;
import cn.shrise.radium.contentservice.resp.cls.*;
import cn.shrise.radium.contentservice.service.cls.ClsStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Api
@RestController
@RequestMapping("cls")
@RequiredArgsConstructor
public class ClsController {

    private final ClsStockService clsStockService;

    @ApiOperation("财联社大涨股列表")
    @GetMapping("stock/plate-list")
    public BaseResult<List<ClsStockPlateResp>> clsStockPlateList(
            @RequestParam(required = false) @ApiParam("日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate date) {
        List<ClsStockPlateResp> respList = clsStockService.clsStockPlateList(date);
        return BaseResult.success(respList);
    }

    @ApiOperation("财联社热点文章列表")
    @GetMapping("article/recommend-list")
    public BaseResult<List<ClsRecommendArticleResp>> clsArticleRecommendList(@RequestParam @ApiParam("条数") Integer size) {
        List<ClsRecommendArticleResp> respList = clsStockService.clsArticleRecommendList(size);
        return BaseResult.success(respList);
    }

    @ApiOperation("根据文章id批量获取文章列表")
    @PostMapping("article/batch")
    public BaseResult<List<LianV1Article>> batchArticleList(@RequestBody BatchReq<Integer> req) {
        return clsStockService.batchArticleList(req.getValues());
    }

    @ApiOperation("财联社最新预警文章")
    @GetMapping("article/last-warning")
    public BaseResult<LianV1Article> clsLastWarningArticle() {
        return clsStockService.clsLastWarningArticle();
    }

    @ApiOperation("财联社文章列表")
    @GetMapping("article/list")
    public BaseResult<List<ClsArticleInfoResp>> clsArticleList(
            @RequestParam(required = false) @ApiParam("文章类型(0:文章 -1:快讯)") Integer type,
            @RequestParam(required = false) @ApiParam("截止时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime dateTime,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size) {
        List<ClsArticleInfoResp> respList = clsStockService.clsArticleList(type, dateTime, size);
        return BaseResult.success(respList);
    }

    @ApiOperation("根据文章id批量获取话题")
    @PostMapping("article/subject-batch-by-article")
    public BaseResult<Map<Integer, List<LianSubject>>> batchArticleSubjectMap(@RequestBody BatchReq<Integer> req) {
        return clsStockService.batchArticleSubjectMap(req.getValues());
    }

    @ApiOperation("财联社文章详情")
    @GetMapping("article/info")
    public BaseResult<ClsArticleInfoResp> clsArticleInfo(@RequestParam @ApiParam("文章id") Integer articleId) {
        return clsStockService.clsArticleInfo(articleId);
    }

    @ApiOperation("财联社快讯最新数量")
    @GetMapping("article/express-count")
    public BaseResult<Integer> clsArticleExpressCount(
            @RequestParam @ApiParam("当前快讯最后时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime dateTime) {
        return clsStockService.clsArticleExpressCount(dateTime);
    }

    @ApiOperation("财联社话题文章列表")
    @GetMapping("article/subject-list")
    public BaseResult<List<ClsArticleInfoResp>> clsSubjectArticleList(
            @RequestParam @ApiParam("话题id") Integer subjectId,
            @RequestParam(required = false) @ApiParam("截止时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime dateTime,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return clsStockService.clsSubjectArticleList(subjectId, dateTime, size);
    }

    @ApiOperation("股票新闻详情")
    @GetMapping("stock/news-info")
    public BaseResult<ClsStockNewsInfoResp> clsStockNewsInfo(@RequestParam @ApiParam("新闻id") Integer id) {
        return clsStockService.clsStockNewsInfo(id);
    }

    @ApiOperation("股票研报详情")
    @GetMapping("stock/research-info")
    public BaseResult<ClsStockResearchInfoResp> clsStockResearchInfo(@RequestParam @ApiParam("研报id") Long id) {
        return clsStockService.clsStockResearchInfo(id);
    }

    @ApiOperation("股票公告详情")
    @GetMapping("stock/announce-info")
    public BaseResult<ClsStockAnnounceInfoResp> clsStockAnnounceInfo(@RequestParam @ApiParam("公告id") Integer id) {
        return clsStockService.clsStockAnnounceInfo(id);
    }

    @ApiOperation("获取最近涨停板块日期")
    @GetMapping("latest-date")
    public BaseResult<LocalDate> getLatestDate() {
        LocalDate latestDate = clsStockService.getLatestDate();
        return BaseResult.success(latestDate);
    }

    @ApiOperation("股票新闻列表")
    @GetMapping("stock/news-list")
    public BaseResult<List<ClsStockNewsInfoResp>> clsStockNewsList(
            @RequestParam @ApiParam("股票代码") List<String> stockCodeList,
            @RequestParam(required = false) @ApiParam("上一页最小的id") Long cursor,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return clsStockService.clsStockNewsList(stockCodeList, cursor, size);
    }

    @ApiOperation("股票研报列表")
    @GetMapping("stock/research-list")
    public BaseResult<List<ClsStockResearchInfoResp>> clsStockResearchList(
            @RequestParam @ApiParam("股票代码") List<String> stockCodeList,
            @RequestParam(required = false) @ApiParam("上一页最小的id") Long cursor,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return clsStockService.clsStockResearchList(stockCodeList, cursor, size);
    }

    @ApiOperation("股票公告列表")
    @GetMapping("stock/announce-list")
    public BaseResult<List<ClsStockAnnounceInfoResp>> clsStockAnnounceList(
            @RequestParam @ApiParam("股票代码") List<String> stockCodeList,
            @RequestParam(required = false) @ApiParam("上一页最小的id") Long cursor,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return clsStockService.clsStockAnnounceList(stockCodeList, cursor, size);
    }

}

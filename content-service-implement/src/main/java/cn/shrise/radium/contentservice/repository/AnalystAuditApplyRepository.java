package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsAnalystAuditApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface AnalystAuditApplyRepository extends JpaRepository<SsAnalystAuditApply, Long>, QuerydslPredicateExecutor<SsAnalystAuditApply> {
}

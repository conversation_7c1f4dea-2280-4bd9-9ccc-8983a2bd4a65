package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsQuoteIndex;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * @Author: tan<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/23 16:46
 * @Desc:
 **/
public interface SsQuoteIndexRepository extends JpaRepository<SsQuoteIndex, Long>, QuerydslPredicateExecutor<SsQuoteIndex> {
}

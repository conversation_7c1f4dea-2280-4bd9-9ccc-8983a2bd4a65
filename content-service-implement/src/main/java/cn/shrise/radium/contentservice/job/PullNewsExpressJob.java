package cn.shrise.radium.contentservice.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsExpress;
import cn.shrise.radium.contentservice.properties.NewsConfigProperty;
import cn.shrise.radium.contentservice.req.GetExpressReq;
import cn.shrise.radium.contentservice.req.GetExpressTokenReq;
import cn.shrise.radium.contentservice.resp.PostExpressResp;
import cn.shrise.radium.contentservice.service.SsExpressService;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class PullNewsExpressJob extends JavaProcessor {

    //友睿api文档
    //http://quote.youruitech.com/yourui/openapi/docscopy.html
    private final NewsConfigProperty newsConfigProperty;
    private final RestTemplate restTemplate;
    private final SsExpressService expressService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        GetExpressTokenReq getExpressTokenReq = GetExpressTokenReq.builder()
                .appkey(newsConfigProperty.getNewsExpressConfig().getAppKey())
                .appsecret(newsConfigProperty.getNewsExpressConfig().getAppSecret())
                .devicetype(newsConfigProperty.getNewsExpressConfig().getDeviceType())
                .build();
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.add(HttpHeaders.ORIGIN, newsConfigProperty.getNewsExpressConfig().getOrigin());
        HttpEntity<GetExpressTokenReq> getExpressTokenReqHttpEntity = new HttpEntity<>(getExpressTokenReq, headers);
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(newsConfigProperty.getNewsExpressConfig().getTokenPath(), getExpressTokenReqHttpEntity, String.class);
        JSONObject jsonObject = JSONUtil.parseObj(stringResponseEntity.getBody());
        Integer code = jsonObject.getInt("code");
        if (ObjectUtil.equals(code, 0)) {
            String token = jsonObject.getStr("token");
            headers.add(HttpHeaders.AUTHORIZATION, token);
            int pageIndex = 1;
            String pattern = "yyyy-MM-dd HH:mm:ss";
            while (true) {
                GetExpressReq getExpressReq = GetExpressReq.builder().page(pageIndex).infoType(4).build();
                HttpEntity<GetExpressReq> getExpressReqHttpEntity = new HttpEntity<>(getExpressReq, headers);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(newsConfigProperty.getNewsExpressConfig().getNewsPath(), getExpressReqHttpEntity, String.class);
                String data = JSONUtil.parseObj(responseEntity.getBody()).getStr("data");
                PostExpressResp expressResp = JSON.parseObject(data, PostExpressResp.class);
                List<SsExpress> ssExpresses = new ArrayList<>();
                List<Long> sourceIds = new ArrayList<>();
                for (Object record : expressResp.getRecords()) {
                    JSONObject entries = JSONUtil.parseObj(record);
                    Long sourceId = entries.getLong("id");
                    String title = entries.getStr("information_title");
                    String releaseTime = entries.getStr("release_time");
                    String content = entries.getStr("abstract");
                    String category = entries.getStr("one_column");
                    String subCategory = entries.getStr("two_column");
                    sourceIds.add(entries.getLong("id"));
                    SsExpress ssExpress = SsExpress.builder().sourceId(sourceId).title(title)
                            .releaseTime(DateUtils.formatterStringToInstant(releaseTime, pattern))
                            .content(content).category(category).subCategory(subCategory).isEnabled(true).build();
                    ssExpresses.add(ssExpress);
                }
                List<Long> expressSourceIdList = expressService.getExpressSourceIdList(sourceIds);
                if (sourceIds.size() <= expressSourceIdList.size()) {
                    log.info("快讯无最新数据");
                    break;
                }
                ssExpresses.removeIf(ssExpress -> expressSourceIdList.contains(ssExpress.getSourceId()));
                expressService.batchSaveSsExpress(ssExpresses);
                pageIndex++;
            }
        } else {
            log.info("获取友睿新闻token失败,{}", jsonObject.getStr("msg"));
            return new ProcessResult(false, jsonObject.getStr("msg"));
        }
        return new ProcessResult(true, "运行成功");
    }
}

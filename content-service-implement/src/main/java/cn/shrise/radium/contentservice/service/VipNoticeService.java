package cn.shrise.radium.contentservice.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.contentservice.entity.QSsVipNotice;
import cn.shrise.radium.contentservice.entity.SsVipNotice;
import cn.shrise.radium.contentservice.repository.SsVipNoticeRepository;
import cn.shrise.radium.contentservice.req.CreateOrUpdateVipNoticeReq;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class VipNoticeService {

    private final JPAQueryFactory queryFactory;
    private final SsVipNoticeRepository vipNoticeRepository;
    private final QSsVipNotice qSsVipNotice = QSsVipNotice.ssVipNotice;

    @Transactional
    public void createOrUpdateOne(CreateOrUpdateVipNoticeReq req) {
        if (ObjectUtil.isNotNull(req.getId())) {
            queryFactory.update(qSsVipNotice)
                    .set(qSsVipNotice.frequency, req.getFrequency())
                    .set(qSsVipNotice.title, req.getTitle())
                    .set(qSsVipNotice.content, req.getContent())
                    .set(qSsVipNotice.linkUrl, req.getLinkUrl())
                    .where(qSsVipNotice.id.eq(req.getId()))
                    .execute();
        } else {
            SsVipNotice vipNotice = new SsVipNotice();
            BeanUtil.copyProperties(req, vipNotice);
            vipNotice.setEnable(false);
            vipNoticeRepository.save(vipNotice);
        }
    }

    public SsVipNotice findEnableOne(Integer companyType, Integer product) {
        return queryFactory.selectFrom(qSsVipNotice)
                .where(qSsVipNotice.companyType.eq(companyType))
                .where(qSsVipNotice.product.eq(product))
                .where(qSsVipNotice.enable.eq(true))
                .fetchFirst();
    }

    @Transactional
    public void setEnable(Long id, Boolean enable) {
        queryFactory.update(qSsVipNotice)
                .set(qSsVipNotice.enable, enable)
                .where(qSsVipNotice.id.eq(id))
                .execute();
    }

    public PageResult<List<SsVipNotice>> getVipNoticeList(Integer companyType, Integer product, Boolean enable,
                                                           Integer current, Integer size) {
        JPAQuery<SsVipNotice> query = queryFactory.select(qSsVipNotice)
                .from(qSsVipNotice)
                .where(qSsVipNotice.companyType.eq(companyType));
        if (ObjectUtil.isNotNull(product)){
            query.where(qSsVipNotice.product.eq(product));
        }
        if (ObjectUtil.isNotNull(enable)) {
            query.where(qSsVipNotice.enable.eq(enable));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsVipNotice.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }
}

package cn.shrise.radium.contentservice.service.originmaterial;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.repository.SsOriginMaterialChannelRepository;
import cn.shrise.radium.contentservice.req.CreateOriginMaterialChannelReq;
import cn.shrise.radium.contentservice.req.EditOriginMaterialChannelReq;
import cn.shrise.radium.contentservice.req.OriginMaterialManagerReq;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhangjianwu
 * @Date: 2024/5/6 14:22
 * @Desc:
 **/

@Service
@RequiredArgsConstructor
@Slf4j
public class OriginMaterialChannelService {

    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;
    private final SsOriginMaterialChannelRepository originMaterialChannelRepository;
    private final QSsOriginMaterialChannel qSsOriginMaterialChannel = QSsOriginMaterialChannel.ssOriginMaterialChannel;
    private final QSsOriginMaterialChannelUserRelation qSsOriginMaterialChannelUserRelation = QSsOriginMaterialChannelUserRelation.ssOriginMaterialChannelUserRelation;
    private final QSsOriginMaterialChannelDepartmentRelation qSsOriginMaterialChannelDepartmentRelation = QSsOriginMaterialChannelDepartmentRelation.ssOriginMaterialChannelDepartmentRelation;

    public PageResult<List<SsOriginMaterialChannel>> getOriginMaterialChannelList(Integer companyType, Integer current, Integer size) {
        JPAQuery<SsOriginMaterialChannel> query = queryFactory.selectFrom(qSsOriginMaterialChannel)
                .where(qSsOriginMaterialChannel.companyType.eq(companyType)
                        .and(qSsOriginMaterialChannel.isEnabled.eq(true)));
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(qSsOriginMaterialChannel.gmtCreate.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public BaseResult<ContentErrorCode> create(CreateOriginMaterialChannelReq req) {
        SsOriginMaterialChannel exist = originMaterialChannelRepository.findByNumber(req.getNumber());
        if (ObjectUtil.isNotEmpty(exist)) {
            return BaseResult.of(ContentErrorCode.NUMBER_NAME_EXIST);
        }
        SsOriginMaterialChannel originMaterialChannel = BeanUtil.copyProperties(req, SsOriginMaterialChannel.class);
        originMaterialChannel.setIsEnabled(true);
        originMaterialChannelRepository.save(originMaterialChannel);
        return BaseResult.success(ContentErrorCode.SUCCESS);
    }

    @Transactional
    public BaseResult<ContentErrorCode> update(EditOriginMaterialChannelReq req) {
        SsOriginMaterialChannel ssOriginMaterialChannel = queryFactory.selectFrom(qSsOriginMaterialChannel)
                .where(qSsOriginMaterialChannel.id.eq(req.getId())).fetchOne();
        if (ObjectUtil.isNull(ssOriginMaterialChannel)) {
            return BaseResult.of(ContentErrorCode.RECORD_NOT_EXISTED);
        }
        queryFactory.update(qSsOriginMaterialChannel)
                .set(qSsOriginMaterialChannel.name, req.getName())
                .where(qSsOriginMaterialChannel.id.eq(req.getId()))
                .execute();
        return BaseResult.success(ContentErrorCode.SUCCESS);
    }

    public List<SsOriginMaterialChannel> getVisibleChannelList(Integer userId, List<Integer> departmentIdList) {
        List<Long> userChannelIdList = queryFactory.select(qSsOriginMaterialChannelUserRelation.channelId)
                .from(qSsOriginMaterialChannelUserRelation)
                .where(qSsOriginMaterialChannelUserRelation.userId.eq(userId))
                .fetch();
        List<Long> deptChannelIdList = queryFactory.select(qSsOriginMaterialChannelDepartmentRelation.channelId)
                .from(qSsOriginMaterialChannelDepartmentRelation)
                .where(qSsOriginMaterialChannelDepartmentRelation.departmentId.in(departmentIdList))
                .fetch();
        Set<Long> channelIdSet = new HashSet<>(userChannelIdList);
        channelIdSet.addAll(deptChannelIdList);
        return queryFactory.select(qSsOriginMaterialChannel).from(qSsOriginMaterialChannel)
                .where(qSsOriginMaterialChannel.id.in(channelIdSet))
                .where(qSsOriginMaterialChannel.isEnabled.eq(true))
                .fetch();
    }

    public List<SsOriginMaterialChannel> getChannelList(Collection<Long> channelIds) {
        if (ObjectUtils.isEmpty(channelIds)) {
            return Collections.emptyList();
        }
        final Set<Long> idSet = channelIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return queryFactory.select(qSsOriginMaterialChannel).from(qSsOriginMaterialChannel)
                .where(qSsOriginMaterialChannel.id.in(idSet))
                .fetch();
    }

    @Transactional
    public void setOriginMaterialVisible(OriginMaterialManagerReq req) {
        setUser(req);
        setDepartment(req);
    }

    @Transactional
    public void setDepartment(OriginMaterialManagerReq req) {
        List<Integer> updateDeptIds = req.getDepartmentIds();
        List<SsOriginMaterialChannelDepartmentRelation> existDeptRelations = getOriginMaterialDeptRelations(null, req.getChannelId());
        List<Long> discardDeptRelationIds = existDeptRelations.stream()
                .filter(deptRelation -> !updateDeptIds.contains(deptRelation.getDepartmentId()))
                .map(SsOriginMaterialChannelDepartmentRelation::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(discardDeptRelationIds)) {
            long execute = queryFactory.delete(qSsOriginMaterialChannelDepartmentRelation)
                    .where(qSsOriginMaterialChannelDepartmentRelation.id.in(discardDeptRelationIds)).execute();
            log.info("删除频道部门关系成功,delete {}", execute);
        }
        if (ObjectUtil.isEmpty(updateDeptIds)) {
            return;
        }
        List<SsOriginMaterialChannelDepartmentRelation> relations = updateDeptIds.stream()
                .map(deptId -> SsOriginMaterialChannelDepartmentRelation.builder()
                        .channelId(req.getChannelId())
                        .departmentId(deptId)
                        .build())
                .collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建部门关系成功,SsOriginMaterialChannelDepartmentRelations {}", relations);
        addDepartmentRecord(req);
    }

    @Transactional
    public void addDepartmentRecord(OriginMaterialManagerReq req) {
        if (CollUtil.isEmpty(req.getDepartmentIds())) {
            return;
        }
        final Long channelId = req.getChannelId();
        final Integer operatorId = req.getOperatorId();
        List<SsOriginMaterialChannelManagerRecord> records = req.getDepartmentIds().stream().map(departmentId -> {
            return SsOriginMaterialChannelManagerRecord.builder()
                    .channelId(channelId)
                    .departmentId(departmentId)
                    .operatorId(operatorId)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(records);
        jdbcTemplate.execute(sql);
        log.info("添加处理人关系记录成功,SsOriginMaterialChannelManagerRecords {}", records);
    }

    public List<SsOriginMaterialChannelDepartmentRelation> getOriginMaterialDeptRelations(List<Integer> deptIds, Long channelId) {
        JPAQuery<SsOriginMaterialChannelDepartmentRelation> query = queryFactory.selectFrom(qSsOriginMaterialChannelDepartmentRelation);
        if (CollUtil.isNotEmpty(deptIds)) {
            query.where(qSsOriginMaterialChannelDepartmentRelation.departmentId.in(deptIds));
        }
        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(qSsOriginMaterialChannelDepartmentRelation.channelId.eq(channelId));
        }
        return query.fetch();
    }

    @Transactional
    public void setUser(OriginMaterialManagerReq req) {
        List<Integer> updateUserIds = req.getUserIds();
        List<SsOriginMaterialChannelUserRelation> exitsManagerRelations = getOriginMaterialUserRelations(null, req.getChannelId());
        List<Long> discardUserRelationIds = exitsManagerRelations.stream()
                .filter(userRelation -> !updateUserIds.contains(userRelation.getUserId()))
                .map(SsOriginMaterialChannelUserRelation::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(discardUserRelationIds)) {
            long execute = queryFactory.delete(qSsOriginMaterialChannelUserRelation)
                    .where(qSsOriginMaterialChannelUserRelation.id.in(discardUserRelationIds)).execute();
            log.info("删除频道处理人关系成功,delete {}", execute);
        }
        if (ObjectUtil.isEmpty(updateUserIds)) {
            return;
        }
        List<SsOriginMaterialChannelUserRelation> relations = updateUserIds.stream()
                .map(userId -> SsOriginMaterialChannelUserRelation.builder()
                        .channelId(req.getChannelId())
                        .userId(userId)
                        .build())
                .collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建处理人关系成功,SsOriginMaterialChannelUserRelation {}", relations);
        addUserRecord(req);
    }

    @Transactional
    public void addUserRecord(OriginMaterialManagerReq req) {
        if (CollUtil.isEmpty(req.getUserIds())) {
            return;
        }
        final Long channelId = req.getChannelId();
        final Integer operatorId = req.getOperatorId();
        List<SsOriginMaterialChannelManagerRecord> records = req.getUserIds().stream().map(userId -> {
            return SsOriginMaterialChannelManagerRecord.builder()
                    .channelId(channelId)
                    .userId(userId)
                    .operatorId(operatorId)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(records);
        jdbcTemplate.execute(sql);
        log.info("添加处理人关系记录成功,SsOriginMaterialChannelManagerRecords {}", records);
    }

    public List<SsOriginMaterialChannelUserRelation> getOriginMaterialUserRelations(Integer userId, Long channelId) {
        JPAQuery<SsOriginMaterialChannelUserRelation> query = queryFactory.selectFrom(qSsOriginMaterialChannelUserRelation);
        if (ObjectUtil.isNotEmpty(userId)) {
            query.where(qSsOriginMaterialChannelUserRelation.userId.eq(userId));
        }
        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(qSsOriginMaterialChannelUserRelation.channelId.eq(channelId));
        }
        return query.fetch();
    }

}

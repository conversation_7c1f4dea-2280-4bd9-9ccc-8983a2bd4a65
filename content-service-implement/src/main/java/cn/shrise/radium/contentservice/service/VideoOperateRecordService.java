package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.contentservice.constant.VideoOperateRecordEnum;
import cn.shrise.radium.contentservice.entity.QSsVideoOperateRecord;
import cn.shrise.radium.contentservice.entity.SsVideoOperateRecord;
import cn.shrise.radium.contentservice.repository.SsVideoOperateRecordRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class VideoOperateRecordService {

    private final SsVideoOperateRecordRepository ssVideoOperateRecordRepository;
    private final JPAQueryFactory queryFactory;
    private final QSsVideoOperateRecord qSsVideoOperateRecord = QSsVideoOperateRecord.ssVideoOperateRecord;

    public void saveRecord(Long videoId, Integer operatorId, VideoOperateRecordEnum operateRecordEnum, String realName, String status) {
        SsVideoOperateRecord build = SsVideoOperateRecord.builder()
                .videoId(videoId)
                .operatorId(operatorId)
                .content(VideoOperateRecordEnum.getRecord(operateRecordEnum.getValue(), realName, status))
                .build();
        ssVideoOperateRecordRepository.save(build);
    }

    public List<SsVideoOperateRecord> getOperateRecord(Long videoId) {
        return queryFactory.selectFrom(qSsVideoOperateRecord).where(qSsVideoOperateRecord.videoId.eq(videoId)).orderBy(qSsVideoOperateRecord.gmtCreate.asc()).fetch();
    }
}

package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.entity.SsNew;
import cn.shrise.radium.contentservice.req.UpdateExpressLikeReq;
import cn.shrise.radium.contentservice.req.UpdateNewsLikeReq;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.contentservice.service.NewsService;
import cn.shrise.radium.contentservice.service.SsExpressService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.util.Collection;
import java.util.List;

/**
 * 新闻
 */

@RestController
@RequestMapping("news")
@RequiredArgsConstructor
public class NewsController {

    private final NewsService newsService;
    private final SsExpressService expressService;

    @GetMapping("channel/getNewsChannels")
    @ApiOperation("获取新闻频道列表")
    public BaseResult<List<NewsChannelResp>> getNewsChannels() {
        return newsService.getNewsChannels();
    }

    @GetMapping("getNewsList")
    @ApiOperation("获取指定频道新闻")
    public PageResult<List<SsNew>> getNewsList(
            @RequestParam @ApiParam("频道id") Collection<String> channelNumber,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return newsService.getNewsList(channelNumber, current, size);
    }

    @GetMapping("getNewsList/labelList")
    @ApiOperation("获取指定频道新闻")
    public PageResult<List<SsNew>> getNewsListByLabelList(
            @RequestParam @ApiParam("频道id") Collection<String> channelNumber,
            @RequestParam @ApiParam("股票代码") Collection<String> labelList,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return newsService.getNewsListByLabelList(channelNumber, labelList, current, size);
    }

    @GetMapping("getNewsList/label")
    @ApiOperation("获取指定频道新闻")
    public PageResult<List<SsNew>> getNewsListByLabel(
            @RequestParam @ApiParam("频道id") Collection<String> channelNumber,
            @RequestParam @ApiParam("股票代码") String label,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return newsService.getNewsListByLabel(channelNumber, label, current, size);
    }

    @GetMapping("getNewsById")
    @ApiOperation("获取指定新闻信息")
    public BaseResult<SsNew> getNewsById(
            @RequestParam @ApiParam("新闻id") Long id) {
        return newsService.getNewsById(id);
    }

    @GetMapping("getLastNews")
    @ApiOperation("获取上一条指定新闻信息")
    public BaseResult<SsNew> getLastNews(
            @RequestParam @ApiParam("新闻id") Long id,
            @RequestParam @ApiParam("新闻发布时间") Instant publishTime,
            @RequestParam @ApiParam("新闻频道ID") Long channelId) {
        SsNew lastNews = newsService.findLastNews(id, publishTime, channelId);
        return BaseResult.success(lastNews);
    }

    @GetMapping("getNextNews")
    @ApiOperation("获取下一条指定新闻信息")
    public BaseResult<SsNew> getNextNews(
            @RequestParam @ApiParam("新闻id") Long id,
            @RequestParam @ApiParam("新闻发布时间") Instant publishTime,
            @RequestParam @ApiParam("新闻频道ID") Long channelId) {
        SsNew nextNews = newsService.findNextNews(id, publishTime, channelId);
        return BaseResult.success(nextNews);
    }

    @GetMapping("express")
    @ApiOperation("获取新闻快讯")
    public PageResult<List<SsExpressResp>> getExpressResp(
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size
    ) {
        return expressService.getExpressPage(userId, current, size);
    }

    @PutMapping("express/like")
    @ApiOperation("修改快讯点赞状态")
    public BaseResult<String> updateExpressLike(@RequestBody @Valid UpdateExpressLikeReq req) {
        expressService.updateExpressLike(req);
        return BaseResult.success();
    }

    @PutMapping("like")
    @ApiOperation("修改新闻点赞状态")
    public BaseResult<String> updateNewsLike(@RequestBody @Valid UpdateNewsLikeReq req) {
        newsService.updateNewsLike(req);
        return BaseResult.success();
    }

    @GetMapping("likeCount")
    @ApiOperation("获取新闻点赞量")
    public List<NewsLikeCount> getNewsLikeCount(@RequestBody @Valid Collection<Long> newsIds) {
        return newsService.getNewsLikeCount(newsIds);
    }

    @GetMapping("isLike")
    @ApiOperation("获取新闻是否点赞")
    public List<NewsUserIsLike> getNewsUserIsLike(@RequestParam(required = false) Integer userId, @RequestBody @Valid Collection<Long> newsIds) {
        return newsService.getNewsUserIsLike(userId, newsIds);
    }

    @PostMapping("addViewCount")
    @ApiOperation("新闻浏览量+1")
    public BaseResult<String> addViewCount(
            @RequestParam @ApiParam("新闻浏览量+1") Long newsId
    ) {
        newsService.addViewCount(newsId);
        return BaseResult.success();
    }
}

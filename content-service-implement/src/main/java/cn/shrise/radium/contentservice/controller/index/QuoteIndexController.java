package cn.shrise.radium.contentservice.controller.index;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.contentservice.entity.SsQuoteIndex;
import cn.shrise.radium.contentservice.req.QuoteIndexInfoReq;
import cn.shrise.radium.contentservice.resp.index.QuoteIndexResp;
import cn.shrise.radium.contentservice.service.index.QuoteIndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: tangjiajun
 * @Date: 2024/12/23 15:05
 * @Desc:
 **/

@Api
@RestController
@RequestMapping("quote-index")
@RequiredArgsConstructor
public class QuoteIndexController {

    private final QuoteIndexService quoteIndexService;

    @GetMapping("list")
    @ApiOperation("获取指标信息列表")
    public BaseResult<List<QuoteIndexResp>> getIndexList(
            @RequestParam @ApiParam("类型: 10-分时线图， 20-k线蜡烛图") Integer type,
            @RequestParam @ApiParam("位置: 1-主图， 2-副图") Integer position,
            @RequestParam(required = false) @ApiParam("上/下架") Boolean enabled) {
        return quoteIndexService.getIndexList(type, position, enabled);
    }

    @PostMapping("create")
    @ApiOperation("创建指标")
    public BaseResult<Void> createIndex(@RequestBody QuoteIndexInfoReq req) {
        return quoteIndexService.createIndex(req);
    }

    @PostMapping("edit")
    @ApiOperation("编辑指标")
    public BaseResult<Void> editIndex(@RequestBody QuoteIndexInfoReq req) {
        return quoteIndexService.editIndex(req);
    }

    @PostMapping("set-enabled")
    @ApiOperation("设置上下架状态")
    public BaseResult<Void> setEnabled(@RequestParam Long id,
                                       @RequestParam @ApiParam("上/下架") Boolean enabled,
                                       @RequestParam(required = false) @ApiParam("排序值") Integer sort) {
        return quoteIndexService.setEnabled(id, enabled, sort);
    }

    @PostMapping("update-sort")
    @ApiOperation("更新指标排序")
    public BaseResult<Void> updateIndexSort(@RequestParam String param) {
        return quoteIndexService.updateIndexSort(param);
    }

    @PostMapping("type-and-position")
    @ApiOperation("根据id获取当前类型和位置")
    public BaseResult<QuoteIndexResp> getTypeAndPosition(@RequestParam Long id){
        return quoteIndexService.getTypeAndPosition(id);
    }

    @PostMapping("max-sort")
    @ApiOperation("获取当前类型和位置下最大排序值")
    public BaseResult<Integer> getMaxSort(@RequestParam Integer type, @RequestParam Integer position){
        return quoteIndexService.getMaxSort(type, position);
    }

    @GetMapping("filter-list")
    @ApiOperation("获取指标信息列表")
    public BaseResult<List<SsQuoteIndex>> getIndexListByFilter(
            @RequestParam(required = false) @ApiParam("类型: 10-分时线图， 20-k线蜡烛图") Integer type,
            @RequestParam(required = false) @ApiParam("位置: 1-主图， 2-副图") Integer position,
            @RequestParam(required = false) @ApiParam("上/下架") Boolean enabled) {
        List<SsQuoteIndex> resultList = quoteIndexService.getIndexListByFilter(type, position, enabled);
        return BaseResult.success(resultList);
    }

}

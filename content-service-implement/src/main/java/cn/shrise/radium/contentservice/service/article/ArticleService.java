package cn.shrise.radium.contentservice.service.article;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.common.util.UrlUtil;
import cn.shrise.radium.contentservice.constant.ArticleOperateRecordConstant;
import cn.shrise.radium.contentservice.constant.ArticleOperateRecordEnum;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.entity.QSsArticle;
import cn.shrise.radium.contentservice.entity.QSsArticleTypeRelation;
import cn.shrise.radium.contentservice.entity.SsArticle;
import cn.shrise.radium.contentservice.entity.SsArticleTypeRelation;
import cn.shrise.radium.contentservice.repository.ArticleRepository;
import cn.shrise.radium.contentservice.req.CreateArticleReq;
import cn.shrise.radium.contentservice.req.EditArticleReq;
import cn.shrise.radium.contentservice.req.UpdateArticleReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.PagedList;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static cn.shrise.radium.common.util.DateUtils.localDateTimeToInstant;
import static cn.shrise.radium.contentservice.constant.ArticleOperateRecordConstant.*;
import static cn.shrise.radium.contentservice.constant.ArticleStatusEnum.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleService {

    private final JPAQueryFactory queryFactory;
    private final QSsArticle qSsArticle = QSsArticle.ssArticle;
    private final QSsArticleTypeRelation qSsArticleTypeRelation = QSsArticleTypeRelation.ssArticleTypeRelation;
    private final ArticleRepository articleRepository;
    private final EntityManager entityManager;
    private final CriteriaBuilderFactory criteriaBuilderFactory;
    private final UserClient userClient;
    private final ArticleOperateRecordService articleOperateRecordService;
    private final RocketMqUtils rocketMqUtils;

    public PageResult<List<SsArticle>> findAllByFilter(@NonNull List<Integer> typeIdList, List<Integer> articleStatuses, Integer current, Integer size) {
        JPAQuery<SsArticle> query = queryFactory.select(qSsArticle)
                .from(qSsArticleTypeRelation)
                .leftJoin(qSsArticle)
                .on(qSsArticleTypeRelation.id.eq(qSsArticle.id))
                .where(qSsArticleTypeRelation.typeId.in(typeIdList));

        if (ObjectUtil.isNotEmpty(articleStatuses)) {
            query.where(qSsArticle.articleStatus.in(articleStatuses));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qSsArticle.createTime.desc(), qSsArticleTypeRelation.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public SsArticleTypeRelation findTypeIdByArticleId(Long id) {
        return queryFactory.select(qSsArticleTypeRelation).from(qSsArticleTypeRelation).where(qSsArticleTypeRelation.id.eq(id)).fetchOne();
    }

    public SsArticle findLastArticleByTypeId(Long id, Instant createTime, Integer typeId, List<Integer> articleStatuses) {
        return queryFactory.select(qSsArticle)
                .from(qSsArticleTypeRelation)
                .leftJoin(qSsArticle)
                .on(qSsArticleTypeRelation.id.eq(qSsArticle.id))
                .where(qSsArticleTypeRelation.typeId.eq(typeId))
                .where(qSsArticle.articleStatus.in(articleStatuses))
                .where(qSsArticle.createTime.goe(createTime))
                .where(qSsArticle.id.gt(id))
                .orderBy(qSsArticle.createTime.asc(), qSsArticleTypeRelation.id.asc())
                .fetchFirst();
    }

    public SsArticle findNextArticleByTypeId(Long id, Instant createTime, Integer typeId, List<Integer> articleStatuses) {
        return queryFactory.select(qSsArticle)
                .from(qSsArticleTypeRelation)
                .leftJoin(qSsArticle)
                .on(qSsArticleTypeRelation.id.eq(qSsArticle.id))
                .where(qSsArticleTypeRelation.typeId.eq(typeId))
                .where(qSsArticle.articleStatus.in(articleStatuses))
                .where(qSsArticle.createTime.loe(createTime))
                .where(qSsArticle.id.lt(id))
                .orderBy(qSsArticle.createTime.desc(), qSsArticleTypeRelation.id.desc())
                .fetchFirst();
    }

    public Optional<SsArticle> getTeamNextArticle(Long teamId, Long articleId, List<Integer> articleStatuses) {
        JPAQuery<SsArticle> query = queryFactory.selectFrom(qSsArticle)
                .where(qSsArticle.teamId.eq(teamId))
                .where(qSsArticle.id.lt(articleId));

        if (ObjectUtils.isNotEmpty(articleStatuses)) {
            query.where(qSsArticle.articleStatus.in(articleStatuses));
        }

        SsArticle ssArticle = query
                .orderBy(qSsArticle.createTime.desc(), qSsArticle.id.desc())
                .limit(1)
                .fetchFirst();
        return Optional.ofNullable(ssArticle);
    }

    public Optional<SsArticle> getTeamPreviousArticle(Long teamId, Long articleId, List<Integer> articleStatuses) {
        JPAQuery<SsArticle> query = queryFactory.selectFrom(qSsArticle)
                .where(qSsArticle.teamId.eq(teamId))
                .where(qSsArticle.id.gt(articleId));

        if (ObjectUtils.isNotEmpty(articleStatuses)) {
            query.where(qSsArticle.articleStatus.in(articleStatuses));
        }

        SsArticle ssArticle = query
                .orderBy(qSsArticle.createTime.asc(), qSsArticle.id.asc())
                .limit(1)
                .fetchFirst();
        return Optional.ofNullable(ssArticle);
    }

    public SsArticle findOneByNumber(@NonNull String number) {
        return queryFactory.selectFrom(qSsArticle).where(qSsArticle.number.eq(number)).fetchOne();
    }

    @Transactional
    public SsArticle viewArticleInfo(@NonNull String number) {
        SsArticle article = queryFactory.selectFrom(qSsArticle).where(qSsArticle.number.eq(number)).fetchOne();
        if (article != null) {
            Integer viewCount = ObjectUtil.isNotEmpty(article.getViewCount()) ? article.getViewCount() + 1 : 1;
            queryFactory.update(qSsArticle)
                    .where(qSsArticle.id.eq(article.getId()))
                    .set(qSsArticle.viewCount, viewCount)
                    .execute();
            article.setViewCount(viewCount);
            return article;
        }
        return null;
    }

    @Transactional
    public void releaseArticle(Long articleId) {
        SsArticle article = queryFactory.selectFrom(qSsArticle).where(qSsArticle.id.eq(articleId)).fetchOne();
        if (ObjectUtil.isNotNull(article)) {
            queryFactory.update(qSsArticle)
                    .where(qSsArticle.articleStatus.eq(AS_Reviewed.getValue()))
                    .where(qSsArticle.id.eq(articleId))
                    .set(qSsArticle.releaseTime, article.getPreReleaseTime())
                    .set(qSsArticle.articleStatus, AS_Released.getValue())
                    .execute();
            articleOperateRecordService.saveRecord(article.getId(), null, ArticleOperateRecordEnum.EDIT_ARTICLE, null, AUDIT_PASS_PUBLISHED);
        }
    }

    public PageResult<List<SsArticle>> getContentTeamArticlePage(List<Long> teamIds, Integer companyType, Integer articleStatus, LocalDate startTime, LocalDate endTime, List<Integer> statusList, String field, Boolean isAsc, Integer current, Integer size) {
        JPAQuery<SsArticle> query = queryFactory.select(qSsArticle)
                .from(qSsArticle)
                .where(qSsArticle.companyType.eq(companyType));

        if (ObjectUtil.isNotEmpty(teamIds)) {
            query.where(qSsArticle.teamId.in(teamIds));
        } else {
            query.where(qSsArticle.teamId.isNotNull());
        }

        if (ObjectUtil.isNotEmpty(articleStatus)) {
            query.where(qSsArticle.articleStatus.eq(articleStatus));
        }

        if (ObjectUtil.isNotEmpty(statusList)) {
            query.where(qSsArticle.articleStatus.in(statusList));
        }

        if (ObjectUtil.isAllNotEmpty(startTime, endTime)) {
            query.where(qSsArticle.createTime.between(DateUtils.getDayOfStart(startTime), DateUtils.getDayOfEnd(endTime)));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        if (ObjectUtil.isAllNotEmpty(field, isAsc)) {
            if ("viewCount".equals(field)) {
                if (isAsc) {
                    query.orderBy(qSsArticle.viewCount.asc(), qSsArticle.id.asc());
                } else {
                    query.orderBy(qSsArticle.viewCount.desc(), qSsArticle.id.desc());
                }
            } else if ("createTime".equals(field)) {
                if (isAsc) {
                    query.orderBy(qSsArticle.createTime.asc(), qSsArticle.id.asc());
                } else {
                    query.orderBy(qSsArticle.createTime.desc(), qSsArticle.id.desc());
                }
            }
        } else {
            query.orderBy(qSsArticle.id.desc());
        }
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional
    public SsArticle createArticle(CreateArticleReq req) {
        Instant now = Instant.now();
        SsArticle article = SsArticle.builder()
                .teamId(req.getTeamId())
                .title(req.getTitle())
                .titleImageUrl(req.getImageUrl())
                .brief(req.getBrief())
                .content(req.getContent())
                .creatorId(req.getCreatorId())
                .auditorId(req.getAuditorId())
                .auditTime(now)
                .articleStatus(AS_Reviewing.getValue())
                .preReleaseTime(localDateTimeToInstant(req.getReleaseTime()))
                .createTime(now)
                .updateTime(now)
                .companyType(req.getCompanyType())
                .build();
        article = articleRepository.save(article);
        String number = UrlUtil.getShortURL(String.valueOf(article.getId()));
        article.setNumber(number);
        UcUsers users = userClient.getUser(req.getCreatorId()).getData();
        articleOperateRecordService.saveRecord(article.getId(), req.getCreatorId(), ArticleOperateRecordEnum.CREATE_ARTICLE, users.getRealName(), null);
        return articleRepository.save(article);
    }

    @Transactional
    public void updateArticle(UpdateArticleReq req) {
        JPAUpdateClause clause = queryFactory.update(qSsArticle)
                .set(qSsArticle.title, req.getTitle())
                .set(qSsArticle.titleImageUrl, req.getImageUrl())
                .set(qSsArticle.brief, req.getBrief())
                .set(qSsArticle.content, req.getContent())
                .set(qSsArticle.updateTime, Instant.now())
                .where(qSsArticle.id.eq(req.getId()));
        if (ObjectUtil.isNotEmpty(req.getAuditorId())){
            clause.set(qSsArticle.auditorId, req.getAuditorId())
                    .set(qSsArticle.auditTime,Instant.now());
        }
        clause.execute();
//        UcUsers users = userClient.getUser(req.getOperateId()).getData();
//        articleOperateRecordService.saveRecord(req.getId(), req.getOperateId(), ArticleOperateRecordEnum.EDIT_ARTICLE, users.getRealName());
    }

    @Transactional
    public void auditArticle(@NonNull Boolean isAudit, Long id, Integer auditorId, String auditRemark, Instant preReleaseTime) {
        if (isAudit) {
            Instant now = Instant.now();
            Instant releaseTime = checkPubTime(preReleaseTime, now);
            Integer status =  ObjectUtil.isNotEmpty(releaseTime) ? AS_Released.getValue() : AS_Reviewed.getValue();
            String recordStatus = ObjectUtil.isNotEmpty(releaseTime) ? AUDIT_PASS_PUBLISHED: AUDIT_PASS_RELEASE;
            this.auditPassed(id, status, auditorId, releaseTime, recordStatus);
            if (ObjectUtil.isEmpty(releaseTime)) {
                // 发送延时消息
                rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, ContentServiceConst.MqTagType.ARTICLE_RELEASE,
                        preReleaseTime.toEpochMilli() - now.toEpochMilli(), id);
            }
        }else {
            this.auditRejected(id, auditorId, auditRemark);
        }
    }

    public Instant checkPubTime(Instant preReleaseTime, Instant now) {
        if (ObjectUtil.isNull(preReleaseTime)) {
            return now;
        }
        return preReleaseTime.isBefore(now)? now: null;
    }

    @Transactional
    public void auditPassed(Long id, Integer status, Integer auditorId, Instant releaseTime, String recordStatus) {
        JPAUpdateClause clause = queryFactory.update(qSsArticle)
                .set(qSsArticle.articleStatus, status)
                .set(qSsArticle.updateTime, Instant.now())
                .set(qSsArticle.auditorId, auditorId)
                .set(qSsArticle.auditTime, Instant.now())
                .where(qSsArticle.id.eq(id));
        if (ObjectUtil.isNotEmpty(releaseTime)) {
            clause.set(qSsArticle.releaseTime, releaseTime);
        }
        clause.execute();
        UcUsers users = userClient.getUser(auditorId).getData();
        articleOperateRecordService.saveRecord(id, auditorId, ArticleOperateRecordEnum.AUDIT_ARTICLE, users.getRealName(), recordStatus);
    }

    @Transactional
    public void auditRejected(Long id, Integer auditorId, String auditRemark) {
        queryFactory.update(qSsArticle)
                .set(qSsArticle.articleStatus, AS_Rejected.getValue())
                .set(qSsArticle.updateTime, Instant.now())
                .set(qSsArticle.auditRemark, auditRemark)
                .set(qSsArticle.auditTime, Instant.now())
                .set(qSsArticle.auditorId, auditorId)
                .where(qSsArticle.id.eq(id))
                .execute();
        UcUsers users = userClient.getUser(auditorId).getData();
        articleOperateRecordService.saveRecord(id, auditorId, ArticleOperateRecordEnum.AUDIT_ARTICLE, users.getRealName(), AUDIT_REFUSE);
    }

    @Transactional
    public void editArticle(EditArticleReq req) {
        Optional<SsArticle> articleOptional = articleRepository.findById(req.getArticleId());
        if (articleOptional.isPresent()) {
            SsArticle article = articleOptional.get();
            if (ObjectUtil.isNotEmpty(req.getEnabled())) {
                article.setArticleStatus(AS_Deleted.getValue());
                article.setUpdateTime(Instant.now());
            }
            articleRepository.save(article);
            UcUsers users = userClient.getUser(req.getOperateId()).getData();
            articleOperateRecordService.saveRecord(req.getArticleId(), req.getOperateId(), ArticleOperateRecordEnum.DELETE_ARTICLE, users.getRealName(),ArticleOperateRecordConstant.DELETE);
            return;
        }
        throw new RecordNotExistedException("文章不存在");
    }

    public Page<SsArticle> getTeamArticleList(Integer companyType, Collection<Long> teamIds, Integer articleStatus, Pageable pageable, Boolean articleOrderByCreate) {
        BlazeJPAQuery<SsArticle> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<SsArticle> query = blazeJpaQuery.select(qSsArticle)
                .from(qSsArticle)
                .where(qSsArticle.companyType.eq(companyType))
                .where(qSsArticle.teamId.in(teamIds));

        if (ObjectUtils.isNotEmpty(articleStatus)) {
            query = query.where(qSsArticle.articleStatus.eq(articleStatus));
        }

        query = query.offset(pageable.getOffset())
                .limit(pageable.getPageSize());
        if (articleOrderByCreate) {
            query = query.orderBy(qSsArticle.createTime.desc(), qSsArticle.id.desc());
        } else {
            query = query.orderBy(qSsArticle.releaseTime.desc(), qSsArticle.id.desc());
        }
        PagedList<SsArticle> articleList = query.fetchPage((int) pageable.getOffset(), pageable.getPageSize());
        return new PageImpl<>(articleList, pageable, articleList.getTotalSize());
    }

    public List<SsArticle> getLatestTeamArticleList(Integer companyType, Long teamId, Integer articleStatus, Long size) {
        BlazeJPAQuery<SsArticle> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<SsArticle> query = blazeJpaQuery.select(qSsArticle)
                .from(qSsArticle)
                .where(qSsArticle.companyType.eq(companyType))
                .where(qSsArticle.teamId.eq(teamId));

        if (ObjectUtils.isNotEmpty(articleStatus)) {
            query = query.where(qSsArticle.articleStatus.eq(articleStatus));
        }

        return query.limit(size)
                .orderBy(qSsArticle.releaseTime.desc(), qSsArticle.id.desc())
                .fetch();
    }

    public SsArticle findOneById(@NonNull Long id) {
        return queryFactory.selectFrom(qSsArticle).where(qSsArticle.id.eq(id)).fetchOne();
    }
}

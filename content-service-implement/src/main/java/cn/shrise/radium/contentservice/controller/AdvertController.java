package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.entity.SsAdvert;
import cn.shrise.radium.contentservice.entity.SsAdvertCategory;
import cn.shrise.radium.contentservice.req.*;
import cn.shrise.radium.contentservice.service.AdvertService;
import cn.shrise.radium.userservice.entity.UcUsers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("advert")
@RequiredArgsConstructor
public class AdvertController {

    private final AdvertService advertService;

    @GetMapping("list")
    @ApiOperation("广告位列表")
    public PageResult<List<SsAdvert>> getAdvertList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("启用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("标签") String label,
            @RequestParam(required = false) @ApiParam("分类ID") Long categoryId,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return advertService.getAdvertList(companyType, enabled, label, categoryId, current, size);
    }

    @PostMapping("create")
    @ApiOperation("创建广告")
    public BaseResult<Void> createAdvert(
            @RequestBody @Valid CreateAdvertReq req) {
        advertService.createAdvert(req);
        return BaseResult.successful();
    }

    @PutMapping("edit")
    @ApiOperation("编辑广告")
    public BaseResult<Void> editAdvert(@RequestBody @Valid EditAdvertReq req) {
        advertService.editAdvert(req);
        return BaseResult.successful();
    }

    @PutMapping("enabled/{id}")
    @ApiOperation("启用禁用")
    public BaseResult<Void> enabledAdvert(
            @PathVariable Integer id,
            @RequestParam @ApiParam("是否启用") Boolean enabled) {
        advertService.enabledAdvert(id, enabled);
        return BaseResult.successful();
    }

    @DeleteMapping("deleted/{id}")
    @ApiOperation("删除广告")
    public BaseResult<Void> deletedAdvert(@PathVariable Integer id) {
        advertService.deletedAdvert(id);
        return BaseResult.successful();
    }

    @GetMapping("label/list")
    @ApiOperation("广告位标签列表")
    public BaseResult<List<String>> getAdvertLabelList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("启用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("分类ID") Long categoryId) {
        List<String> labelList = advertService.getAdvertLabelList(companyType, enabled, categoryId);
        return BaseResult.success(labelList);
    }

    @GetMapping
    @ApiOperation("按标签获取广告位列表")
    public BaseResult<List<SsAdvert>> getAdvertList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("标签列表") List<String> labels,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled) {
        List<SsAdvert> advertList = advertService.getAdvertList(companyType, labels, enabled);
        return BaseResult.success(advertList);
    }

    @GetMapping("category/list")
    @ApiOperation("广告分类列表")
    public PageResult<List<SsAdvertCategory>> getAdvertCategoryList(
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enable,
            @RequestParam(required = false) @ApiParam("是否按创建时间顺序排序") Boolean isAsc,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return advertService.getAdvertCategoryList(enable, isAsc, current, size);
    }

    @PostMapping("category/create")
    @ApiOperation("新建分类")
    public BaseResult<Void> createCategory(
            @RequestBody @Valid SsAdvertCategoryCreateReq req) {
        advertService.createCategory(req);
        return BaseResult.successful();
    }

    @PostMapping("category/update")
    @ApiOperation("修改分类")
    public BaseResult<Void> updateCategory(
            @RequestBody @Valid SsAdvertCategoryUpdateReq req) {
        advertService.updateCategory(req);
        return BaseResult.successful();
    }

    @PostMapping("category/enable")
    @ApiOperation("启用/禁用营销位分类")
    public BaseResult<Void> getAdvertCategoryEnable(
            @RequestBody @Valid SsAdvertCategoryEnableReq req) {
        advertService.enabledAdvertCategory(req);
        return BaseResult.successful();
    }

    @PostMapping("category/batch")
    @ApiOperation("根据分类ID批量获取分类信息")
    public BaseResult<Map<Long, SsAdvertCategory>> batchGetAdvertCategoryMap(@RequestBody BatchReq<Long> req) {
        List<SsAdvertCategory> categoryList = advertService.getAdvertCategoryList(req.getValues());
        Map<Long, SsAdvertCategory> categoryMap = categoryList.stream().collect(Collectors.toMap(SsAdvertCategory::getId, Function.identity()));
        return BaseResult.success(categoryMap);
    }

}

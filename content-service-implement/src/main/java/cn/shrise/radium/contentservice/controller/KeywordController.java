package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.req.DeleteKeywordReq;
import cn.shrise.radium.contentservice.req.KeywordReq;
import cn.shrise.radium.contentservice.resp.KeywordLibResp;
import cn.shrise.radium.contentservice.resp.KeywordResp;
import cn.shrise.radium.contentservice.resp.KeywordsResultResp;
import cn.shrise.radium.contentservice.service.green.KeywordService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("keyword")
@RequiredArgsConstructor
public class KeywordController {

    private final KeywordService keywordService;

    @GetMapping("libs/list")
    @ApiOperation("获取词库")
    public BaseResult<List<KeywordLibResp>> getListKeywordLibs() {
        List<KeywordLibResp> keywordLibs = keywordService.getListKeywordLibs();
        return BaseResult.success(keywordLibs);
    }

    @DeleteMapping("libs/{libId}")
    @ApiOperation("清空词库")
    public BaseResult<Boolean> deleteKeywordLib(@PathVariable String libId) {
        Boolean isSuccess = keywordService.deleteKeywordLib(libId);
        return BaseResult.success(isSuccess);
    }

    @PostMapping("add/keywords")
    @ApiOperation("词库添加关键词")
    public BaseResult<KeywordsResultResp> addKeywordToLib(
            @RequestBody @Valid KeywordReq req) {
        KeywordsResultResp resp = keywordService.addKeywordToLib(req.getLibId(), req.getKeywords());
        return BaseResult.success(resp);
    }

    @GetMapping("list")
    @ApiOperation("获取关键词列表")
    public PageResult<List<KeywordResp>> getListKeywords(
            @RequestParam @ApiParam("词库id") String libId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size,
            @RequestParam @ApiParam("是否升序") Boolean isAsc) {
        return keywordService.getListKeywords(libId, current, size, isAsc);
    }

    @DeleteMapping
    @ApiOperation("删除关键词")
    public BaseResult<Boolean> deleteKeyword(
            @RequestBody @Valid DeleteKeywordReq req) {
        Boolean isSuccess = keywordService.deleteKeyword(req.getLibId(), req.getKeywordList());
        return BaseResult.success(isSuccess);
    }
}

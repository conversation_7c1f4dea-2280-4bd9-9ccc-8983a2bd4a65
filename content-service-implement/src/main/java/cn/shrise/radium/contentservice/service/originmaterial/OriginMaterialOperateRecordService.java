package cn.shrise.radium.contentservice.service.originmaterial;

import cn.shrise.radium.contentservice.entity.QSsOriginMaterialOperateRecord;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialOperateRecord;
import cn.shrise.radium.contentservice.repository.SsOriginMaterialOperateRecordRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class OriginMaterialOperateRecordService {

    private final JPAQueryFactory queryFactory;
    private final SsOriginMaterialOperateRecordRepository ssOriginMaterialOperateRecordRepository;
    private final QSsOriginMaterialOperateRecord originMaterialOperateRecord = QSsOriginMaterialOperateRecord.ssOriginMaterialOperateRecord;

    public void saveOne(SsOriginMaterialOperateRecord entity) {
        ssOriginMaterialOperateRecordRepository.save(entity);
    }

    public List<SsOriginMaterialOperateRecord> getOriginMaterialOperateRecord(Long materialId) {
        return queryFactory.select(originMaterialOperateRecord)
                .from(originMaterialOperateRecord)
                .where(originMaterialOperateRecord.materialId.eq(materialId))
                .orderBy(originMaterialOperateRecord.gmtCreate.asc())
                .fetch();
    }
}

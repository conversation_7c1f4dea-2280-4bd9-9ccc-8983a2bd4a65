package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.entity.SsVideoTranscode;
import cn.shrise.radium.contentservice.repository.SsVideoTranscodeRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class VideoTranscodeService {

    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;
    private final SsVideoTranscodeRepository videoTranscodeRepository;

    @Transactional
    public void createOrUpdateVideoTranscode(SsVideoTranscode entity) {
        String sql = SqlUtil.onDuplicateKeyUpdateSql(entity, true);
        jdbcTemplate.execute(sql);
    }

    public Optional<SsVideoTranscode> getVideoTranscode(String videoId) {
        return videoTranscodeRepository.findByVideoId(videoId);
    }
}

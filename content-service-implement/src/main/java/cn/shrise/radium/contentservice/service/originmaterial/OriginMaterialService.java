package cn.shrise.radium.contentservice.service.originmaterial;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.constant.OriginMaterialAuditEnum;
import cn.shrise.radium.contentservice.constant.OriginMaterialOperateRecordConstant;
import cn.shrise.radium.contentservice.entity.QSsOriginMaterial;
import cn.shrise.radium.contentservice.entity.SsOriginMaterial;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialOperateRecord;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.repository.SsOriginMaterialRepository;
import cn.shrise.radium.contentservice.req.CreateOriginMaterialReq;
import cn.shrise.radium.contentservice.req.OriginMaterialAuditReq;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class OriginMaterialService {


    private final JPAQueryFactory queryFactory;
    private final SsOriginMaterialRepository originMaterialRepository;
    private final QSsOriginMaterial originMaterial = QSsOriginMaterial.ssOriginMaterial;
    private final OriginMaterialOperateRecordService originMaterialOperateRecordService;

    @Transactional
    public void deleteOriginMaterial(Integer userId, Long materialId) {
        SsOriginMaterial exist = queryFactory.selectFrom(originMaterial)
                .where(originMaterial.id.eq(materialId))
                .fetchOne();
        if (exist == null) {
            throw new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED);
        }
        if (!exist.getEnabled()) {
            throw new BusinessException("状态已更新,请刷新重试");
        }
        queryFactory.update(originMaterial)
                .set(originMaterial.enabled, false)
                .where(originMaterial.id.eq(materialId))
                .execute();
        SsOriginMaterialOperateRecord build = SsOriginMaterialOperateRecord.builder()
                .materialId(materialId)
                .operatorId(userId)
                .content(OriginMaterialOperateRecordConstant.DELETE)
                .build();
        originMaterialOperateRecordService.saveOne(build);
    }

    public void createOne(CreateOriginMaterialReq req) {
        SsOriginMaterial material = new SsOriginMaterial();
        BeanUtil.copyProperties(req, material, CopyOptions.create().setIgnoreProperties("imageList", "fileList"));
        material.setFileList(JSON.toJSONString(req.getFileList()));
        material.setFileType(req.getFileType());
        material.setProviderId(req.getProviderId());
        material.setCustomerId(DesensitizeUtil.maskToId(req.getUserCode()));
        material.setAuditStatus(OriginMaterialAuditEnum.ORIGIN_AUDIT_NOT_EXE.getStatus());
        SsOriginMaterial one = originMaterialRepository.save(material);

        SsOriginMaterialOperateRecord build = SsOriginMaterialOperateRecord.builder()
                .materialId(one.getId())
                .operatorId(req.getCreatorId())
                .content(OriginMaterialOperateRecordConstant.UPLOAD_ORIGIN_MATERIAL)
                .build();
        originMaterialOperateRecordService.saveOne(build);
    }

    public Page<SsOriginMaterial> getOriginMaterialList(LocalDateTime startTime, LocalDateTime endTime, Integer userId, List<Long> channelIds, Integer category, Boolean status, List<Integer> auditStatusList, String searchContent, PageRequest pageRequest) {
        JPAQuery<SsOriginMaterial> query = queryFactory.select(originMaterial).from(originMaterial);
        if (ObjectUtil.isNotNull(startTime)) {
            query.where(originMaterial.gmtCreate.goe(startTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (ObjectUtil.isNotNull(endTime)) {
            query.where(originMaterial.gmtCreate.loe(endTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (ObjectUtil.isNotNull(userId)) {
            query.where(originMaterial.creatorId.eq(userId));
        }
        if (ObjectUtil.isNotEmpty(channelIds)) {
            query.where(originMaterial.channelId.in(channelIds));
        }
        if (ObjectUtil.isNotNull(category)) {
            query.where(originMaterial.category.eq(category));
        }
        if (ObjectUtil.isNotNull(status)) {
            query.where(originMaterial.enabled.eq(status));
        }
        if (ObjectUtil.isNotEmpty(searchContent)) {
            if (NumberUtil.isLong(searchContent)) {
                query.where(originMaterial.id.eq(Long.valueOf(searchContent)));
            } else if (DesensitizeUtil.isValidUserCode(searchContent)) {
                int customerId = DesensitizeUtil.maskToId(searchContent);
                query.where(originMaterial.customerId.eq(customerId));
            } else {
                query.where(originMaterial.description.like("%" + searchContent + "%"));
            }
        }
        if(ObjectUtil.isNotEmpty(auditStatusList)){
            query.where(originMaterial.auditStatus.in(auditStatusList));
        }
        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();

        List<SsOriginMaterial> fetch = query.orderBy(originMaterial.gmtCreate.desc())
                .offset(pageRequest.getOffset())
                .limit(pageRequest.getPageSize())
                .fetch();
        return new PageImpl<>(fetch, pageRequest, total);
    }

    public SsOriginMaterial getOriginMaterial(Long materialId) {
        Optional<SsOriginMaterial> ssOriginMaterialOptional = originMaterialRepository.findById(materialId);
        if (ssOriginMaterialOptional.isPresent()) {
            return ssOriginMaterialOptional.get();
        }
        return null;
    }

    public List<SsOriginMaterial> getOriginMaterialByIdList(List<Long> originMaterialIds) {
        if (ObjectUtil.isEmpty(originMaterialIds)) {
            return Collections.emptyList();
        }
        return queryFactory.selectFrom(originMaterial)
                .where(originMaterial.id.in(originMaterialIds))
                .fetch();
    }

    @Transactional
    public void auditOriginMaterial(OriginMaterialAuditReq req) {
        SsOriginMaterial ssOriginMaterial = queryFactory.selectFrom(originMaterial)
                .where(originMaterial.id.eq(req.getOriginMaterialId()))
                .fetchOne();
        if (originMaterial == null) {
            throw new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED);
        }
        if (!ObjectUtil.equals(ssOriginMaterial.getAuditStatus(), req.getAuditStatus())) {
            queryFactory.update(originMaterial)
                    .set(originMaterial.auditStatus, req.getAuditStatus())
                    .set(originMaterial.auditorId, req.getAuditorId())
                    .set(originMaterial.auditTime, Instant.now())
                    .where(originMaterial.id.eq(req.getOriginMaterialId()))
                    .execute();
            String operateContent = ObjectUtil.isEmpty(OriginMaterialAuditEnum.getDesc(req.getAuditStatus())) ? null :
                    "修改状态为{" + OriginMaterialAuditEnum.getDesc(req.getAuditStatus()) + "}";
            SsOriginMaterialOperateRecord operateRecord = SsOriginMaterialOperateRecord.builder()
                    .materialId(req.getOriginMaterialId())
                    .operatorId(req.getAuditorId())
                    .content(operateContent)
                    .build();
            originMaterialOperateRecordService.saveOne(operateRecord);
        }
    }

}

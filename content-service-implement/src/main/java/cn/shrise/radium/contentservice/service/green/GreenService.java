package cn.shrise.radium.contentservice.service.green;

import cn.shrise.radium.common.properties.AliyunProperties;
import cn.shrise.radium.common.util.UrlUtil;
import cn.shrise.radium.contentservice.entity.SsTextScanResult;
import cn.shrise.radium.contentservice.req.*;
import cn.shrise.radium.contentservice.resp.GreenTextScanResp;
import cn.shrise.radium.contentservice.resp.TextChatAuditResp;
import cn.shrise.radium.contentservice.resp.TextModerationResp;
import cn.shrise.radium.contentservice.service.SsTextScanResultService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.TextModerationRequest;
import com.aliyun.green20220302.models.TextModerationResponse;
import com.aliyun.green20220302.models.TextModerationResponseBody;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.green.model.v20180509.TextScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 内容安全服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GreenService {

    private final IAcsClient acsClient;
    private final AliyunProperties aliyunProperties;
    private final SsTextScanResultService ssTextScanResultService;

    @Resource(name = "greenClient")
    private final Client client;

    public GreenTextScanResp textScan(GreenTextScanReq greenTextScanReq) {
        String bizType = greenTextScanReq.getBizType();
        List<String> scenes = greenTextScanReq.getScenes();
        Integer companyType = greenTextScanReq.getCompanyType();

        HttpResponse httpResponse;
        try {
            httpResponse = requestTextScan(bizType, scenes, greenTextScanReq.getTasks());
        } catch (ClientException e) {
            return createErrorTextScanResult(greenTextScanReq, JSON.toJSONString(e));
        }

        if (!httpResponse.isSuccess()) {
            return createErrorTextScanResult(greenTextScanReq, JSON.toJSONString(httpResponse));
        }

        JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getHttpContent(), StandardCharsets.UTF_8));
        if (200 != scrResponse.getInteger("code")) {
            return createErrorTextScanResult(greenTextScanReq, JSON.toJSONString(scrResponse));
        }

        List<GreenTextScanResp.ScanData> scanDataList = new ArrayList<>();

        JSONArray taskResults = scrResponse.getJSONArray("data");
        for (Object taskResult : taskResults) {
            JSONObject jsonObject = (JSONObject) taskResult;
            Integer code = jsonObject.getInteger("code");
            String dataId = jsonObject.getString("dataId");
            String content = jsonObject.getString("content");
            String filteredContent = jsonObject.getString("filteredContent");
            String taskId = jsonObject.getString("taskId");

            if (!Objects.equals(code, 200)) {
                GreenTextScanResp.ScanData scanData = createErrorTextScanResult(companyType, dataId, taskId, content, jsonObject.toJSONString());
                scanDataList.add(scanData);
                continue;
            }
            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
            GreenTextScanResp.ScanData scanData = createTextScanResult(companyType, dataId, taskId, content, filteredContent, sceneResults);
            scanDataList.add(scanData);
        }

        return GreenTextScanResp.builder()
                .data(scanDataList)
                .build();
    }

    public TextChatAuditResp textChatAudit(TextChatAuditReq req) {
        TextChatAuditResp resp = new TextChatAuditResp();
        String service = req.getService();
        String content = req.getContent();
        TextModerationResponse response = new TextModerationResponse();
        try {
            response = requestTextModeration(service, content);
        } catch (Exception e) {
            log.error("textChatAudit error {} {}", UrlUtil.md5ByHex(content), ExceptionUtils.getStackTrace(e));
        }
        if (response.getStatusCode() == 200) {
            TextModerationResponseBody result = response.getBody();
            Integer code = result.getCode();
            if (code != null && code == 200) {
                TextModerationResponseBody.TextModerationResponseBodyData data = result.getData();
                String labels = data.getLabels();
                String reason = data.getReason();
                if (ObjectUtils.isNotEmpty(labels)) {
                    resp.setLabels(labels);
                    if (ObjectUtils.isNotEmpty(reason)) {
                        JSONObject jsonObject = JSON.parseObject(reason);
                        String riskTips = jsonObject.getString("riskTips");
                        String riskWords = jsonObject.getString("riskWords");
                        String customizedLibs = jsonObject.getString("customizedLibs");
                        String customizedWords = jsonObject.getString("customizedWords");
                        resp.setRiskTips(riskTips);
                        resp.setRiskWords(riskWords);
                        resp.setCustomizedLibs(customizedLibs);
                        resp.setCustomizedWords(customizedWords);
                        resp.setRequestId(result.getRequestId());
                    }
                }
            }
        }
        return resp;
    }

    public TextModerationResp textModeration(TextModerationReq textModerationReq) {
        String service = textModerationReq.getService();
        String content = textModerationReq.getContent();
        String dataId = textModerationReq.getDataId();
        Integer companyType = textModerationReq.getCompanyType();

        TextModerationResponse response;
        try {
            response = requestTextModeration(service, content);
        } catch (Exception e) {
            return createErrorTextScanResult(companyType, dataId, content, JSON.toJSONString(e));
        }

        if (response.getStatusCode() == 200) {
            TextModerationResponseBody result = response.getBody();
            Integer code = result.getCode();
            if (code != null && code == 200) {
                TextModerationResponseBody.TextModerationResponseBodyData data = result.getData();
                String labels = data.getLabels();
                String reason = data.getReason();
                boolean passed = ObjectUtils.isEmpty(labels);
                String filteredContent = content;
                if (!passed && ObjectUtils.isNotEmpty(reason)) {
                    JSONObject jsonObject = JSON.parseObject(reason);
                    String riskWords = jsonObject.getString("riskWords");
                    String[] keyword = ObjectUtils.isNotEmpty(riskWords) ? riskWords.split(",") : new String[0];
                    for (String k : keyword) {
                        int length = k.length();
                        String placeholder = String.join("", Collections.nCopies(length, "*"));
                        filteredContent = filteredContent.replaceAll(k, placeholder);
                    }
                }

                String labelsJson = ObjectUtils.isNotEmpty(labels) ? JSON.toJSONString(labels.split(",")) : null;

                CreateTextScanResultReq createTextScanResultReq = CreateTextScanResultReq.builder()
                        .companyType(textModerationReq.getCompanyType())
                        .content(textModerationReq.getContent())
                        .results(JSON.toJSONString(result))
                        .filteredContent(filteredContent)
                        .dataId(dataId)
                        .labels(labelsJson)
                        .passed(passed)
                        .build();
                SsTextScanResult ssTextScanResult = ssTextScanResultService.createTextScanResult(createTextScanResultReq);

                return TextModerationResp.builder()
                        .labels(labels)
                        .reason(reason)
                        .content(content)
                        .filteredContent(filteredContent)
                        .resultId(ssTextScanResult.getId())
                        .passed(ssTextScanResult.getPassed())
                        .dataId(dataId)
                        .build();

            } else {
                return createErrorTextScanResult(companyType, dataId, content, JSON.toJSONString(response));
            }
        } else {
            return createErrorTextScanResult(companyType, dataId, content, JSON.toJSONString(response));
        }
    }

    public TextModerationResponse requestTextModeration(String service, String content) throws Exception {
        //检测参数构造
        JSONObject serviceParameters = new JSONObject();
        serviceParameters.put("content", content);

        TextModerationRequest textModerationRequest = new TextModerationRequest();
        /*
        文本检测服务 serviceCode:
        nickname_detection：用户昵称
        chat_detection：聊天互动
        comment_detection：动态评论
        pgc_detection：教学物料PGC
        */
        textModerationRequest.setService(service);
        textModerationRequest.setServiceParameters(serviceParameters.toJSONString());

        //调用方法获取检测结果
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.setConnectTimeout(10000);
        runtime.setReadTimeout(3000);
        return client.textModerationWithOptions(textModerationRequest, runtime);
    }

    public HttpResponse requestTextScan(String bizType, List<String> scenes, List<TextScanTask> textScanTasks) throws ClientException {
        AliyunProperties.GreenProduct greenProductConfig = aliyunProperties.getGreen();
        TextScanRequest textScanRequest = new TextScanRequest();
        textScanRequest.setSysEndpoint(greenProductConfig.getEndpoint());
        textScanRequest.setSysConnectTimeout(3000);

        JSONObject data = new JSONObject();
        data.put("bizType", bizType);
        data.put("scenes", scenes);

        List<Map<String, Object>> tasks = new ArrayList<>();
        textScanTasks.forEach(task -> {
            Map<String, Object> taskParam = new LinkedHashMap<>();
            String dataId = ObjectUtils.isNotEmpty(task.getDataId()) ? task.getDataId() : UUID.randomUUID().toString();
            taskParam.put("dataId", dataId);
            taskParam.put("content", task.getContent());
            tasks.add(taskParam);
        });

        data.put("tasks", tasks);
        textScanRequest.setHttpContent(data.toJSONString().getBytes(StandardCharsets.UTF_8), "UTF-8", FormatType.JSON);

        return acsClient.doAction(textScanRequest);
    }

    public GreenTextScanResp createErrorTextScanResult(GreenTextScanReq greenTextScanReq, String error) {
        List<GreenTextScanResp.ScanData> scanDataList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(greenTextScanReq.getTasks())) {
            Integer companyType = greenTextScanReq.getCompanyType();
            greenTextScanReq.getTasks().forEach(task -> {
                CreateTextScanResultReq createTextScanResultReq = CreateTextScanResultReq.builder()
                        .companyType(companyType)
                        .dataId(task.getDataId())
                        .content(task.getContent())
                        .error(error)
                        .passed(false)
                        .build();
                SsTextScanResult textScanResult = ssTextScanResultService.createTextScanResult(createTextScanResultReq);

                GreenTextScanResp.ScanData build = GreenTextScanResp.ScanData.builder()
                        .dataId(task.getDataId())
                        .content(task.getContent())
                        .filteredContent(null)
                        .passed(textScanResult.getPassed())
                        .resultId(textScanResult.getId())
                        .build();
                scanDataList.add(build);
            });
        }
        return GreenTextScanResp.builder().data(scanDataList).build();
    }

    private GreenTextScanResp.ScanData createErrorTextScanResult(Integer companyType, String dataId, String taskId, String content, String error) {
        CreateTextScanResultReq createTextScanResultReq = CreateTextScanResultReq.builder()
                .companyType(companyType)
                .dataId(dataId)
                .taskId(taskId)
                .content(content)
                .error(error)
                .passed(false)
                .build();
        SsTextScanResult textScanResult = ssTextScanResultService.createTextScanResult(createTextScanResultReq);

        return GreenTextScanResp.ScanData.builder()
                .dataId(dataId)
                .taskId(taskId)
                .content(content)
                .passed(textScanResult.getPassed())
                .resultId(textScanResult.getId())
                .build();
    }

    private TextModerationResp createErrorTextScanResult(Integer companyType, String dataId, String content, String error) {
        CreateTextScanResultReq createTextScanResultReq = CreateTextScanResultReq.builder()
                .companyType(companyType)
                .dataId(dataId)
                .content(content)
                .error(error)
                .passed(false)
                .build();
        SsTextScanResult textScanResult = ssTextScanResultService.createTextScanResult(createTextScanResultReq);

        return TextModerationResp.builder()
                .dataId(dataId)
                .content(content)
                .passed(textScanResult.getPassed())
                .resultId(textScanResult.getId())
                .build();
    }

    private GreenTextScanResp.ScanData createTextScanResult(Integer companyType, String dataId, String taskId, String content, String filteredContent, JSONArray sceneResults) {
        Set<String> labelSet = new HashSet<>();
        for (Object sceneResult : sceneResults) {
            String label = ((JSONObject) sceneResult).getString("label");
            if (ObjectUtils.isNotEmpty(label)) {
                labelSet.add(label);
            }
        }
        String labelJson = ObjectUtils.isNotEmpty(labelSet) ? JSON.toJSONString(labelSet) : null;
        boolean passed = isPassed(sceneResults);
        CreateTextScanResultReq createTextScanResultReq = CreateTextScanResultReq.builder()
                .companyType(companyType)
                .dataId(dataId)
                .taskId(taskId)
                .content(content)
                .filteredContent(filteredContent)
                .results(sceneResults.toJSONString())
                .labels(labelJson)
                .passed(passed)
                .build();
        SsTextScanResult textScanResult = ssTextScanResultService.createTextScanResult(createTextScanResultReq);

        return GreenTextScanResp.ScanData.builder()
                .dataId(dataId)
                .taskId(taskId)
                .content(content)
                .filteredContent(filteredContent)
                .passed(textScanResult.getPassed())
                .resultId(textScanResult.getId())
                .build();
    }

    private boolean isPassed(JSONArray sceneResults) {
        for (Object sceneResult : sceneResults) {
            String suggestion = ((JSONObject) sceneResult).getString("suggestion");
            if (Objects.equals(suggestion, "block")) {
                return false;
            }
        }
        return true;
    }
}

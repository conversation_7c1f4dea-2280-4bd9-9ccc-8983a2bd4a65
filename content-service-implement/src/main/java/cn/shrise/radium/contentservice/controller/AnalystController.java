package cn.shrise.radium.contentservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.dao.SsContractAnalystRecordDao;
import cn.shrise.radium.contentservice.entity.SsAnalystAuditApply;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.entity.SsAnalystModifyRecord;
import cn.shrise.radium.contentservice.entity.SsContractAnalystRecord;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.properties.ContractProperties;
import cn.shrise.radium.contentservice.req.EditAnalystInfoReq;
import cn.shrise.radium.contentservice.service.AnalystAuditApplyService;
import cn.shrise.radium.contentservice.service.AnalystInfoService;
import cn.shrise.radium.contentservice.service.AnalystModifyRecordService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@RestController
@RequestMapping("analyst")
@RequiredArgsConstructor
public class AnalystController {

    private final AnalystInfoService analystService;

    private final AnalystModifyRecordService modifyRecordService;

    private final AnalystAuditApplyService auditApplyService;

    private final SsContractAnalystRecordDao contractAnalystRecordDao;

    private final ContractProperties contractProperties;

    @GetMapping("{id}")
    @ApiOperation("获取投顾老师详情")
    public BaseResult<SsAnalystInfo> getAnalystInfo(@PathVariable Integer id) {
        SsAnalystInfo info = analystService.findOneById(id).orElseThrow(() -> new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED));
        return BaseResult.success(info);
    }

    @PostMapping("batch")
    @ApiOperation("批量获取投顾老师详情")
    public BaseResult<List<SsAnalystInfo>> getAnalystInfoList(@RequestBody @Valid BatchReq<Integer> req) {
        List<SsAnalystInfo> analystList = analystService.getAnalystInfoList(req.getValues());
        return BaseResult.success(analystList);
    }

    @GetMapping("list")
    @ApiOperation("获取投顾老师列表")
    public PageResult<List<SsAnalystInfo>> getAnalystInfoListPage(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("是否官网展示") Boolean gwShow,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return analystService.getAnalystInfoListPage(companyType, enabled, gwShow, auditStatus, current, size);
    }

    @PostMapping("create")
    @ApiOperation("创建投顾老师")
    public BaseResult<String> createAnalystInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestBody @Valid EditAnalystInfoReq req) {
        return analystService.createAnalystInfo(companyType, userId, req);
    }

    @PostMapping("update")
    @ApiOperation("编辑投顾老师信息")
    public BaseResult<String> updateAnalystInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestBody @Valid EditAnalystInfoReq req) {
        return analystService.updateAnalystInfo(companyType, userId, req);
    }

    @PostMapping("delete")
    @ApiOperation("启用/禁用老师")
    public BaseResult<String> deleteAnalystInfo(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("老师id") Integer id,
            @RequestParam @ApiParam("启用/禁用") Boolean enabled
    ) {
        return analystService.deleteAnalystInfo(userId, id, enabled);
    }

    @GetMapping("gw/list")
    @ApiOperation("官网获取投顾老师列表")
    public BaseResult<List<SsAnalystInfo>> getGwAnalystInfoList() {
        List<SsAnalystInfo> gwAnalystInfoList = analystService.getGwAnalystInfoList();
        return BaseResult.success(gwAnalystInfoList);
    }

    @GetMapping("audit/list")
    @ApiOperation("老师审核管理列表")
    public PageResult<List<SsAnalystAuditApply>> getAuditRecordPage(
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return auditApplyService.getAnalystAuditApplyPage(auditStatus, analystId, current, size);
    }

    @GetMapping("modify/list")
    @ApiOperation("老师修改记录列表")
    public PageResult<List<SsAnalystModifyRecord>> getAnalystModifyRecordPage(
            @RequestParam @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return modifyRecordService.getAnalystModifyRecordPage(analystId, current, size);
    }

    @PostMapping("audit/apply")
    @ApiOperation("审核老师申请")
    public BaseResult<Void> auditAnalystApply(
            @RequestParam @ApiParam("申请id") Long applyId,
            @RequestParam @ApiParam("审核人id") Integer auditorId,
            @RequestParam @ApiParam("审核状态(通过:1 不通过：2)") Integer auditStatus) {
        auditApplyService.auditApply(applyId, auditorId, auditStatus);
        return BaseResult.successful();
    }

    @GetMapping("contract-advisor/detail")
    @ApiOperation("合同投顾老师")
    public BaseResult<SsContractAnalystRecord> getContractAdvisor(
            @RequestParam @ApiParam("合同类型") Integer contractType
    ) {
        SsContractAnalystRecord contractAdvisor = contractAnalystRecordDao.getContractAdvisor(contractType);
        return BaseResult.success(contractAdvisor);
    }

    @PostMapping("contract-advisor/update")
    @ApiOperation("更新合同投顾老师")
    public BaseResult<Void> updateContractAdvisor(@RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
                                                  @RequestParam @ApiParam("合同投顾老师") String content,
                                                  @RequestParam @ApiParam("合同类型") Integer contractType) {
        contractAnalystRecordDao.updateContractAdvisor(operatorId, content, contractType);
        return BaseResult.successful();
    }

    @GetMapping("contract-advisor/operate-record")
    @ApiOperation("合同投顾老师操作记录")
    public BaseResult<List<SsContractAnalystRecord>> getContractAdvisorOperateRecord(
            @RequestParam @ApiParam("合同类型") Integer contractType
    ) {
        List<SsContractAnalystRecord> list = contractAnalystRecordDao.getContractAdvisorOperateRecord(contractType);
        return BaseResult.success(list);
    }

    @GetMapping("contract-advisor/info")
    @ApiOperation("获取合同投顾老师")
    public BaseResult<String> getContractAdvisorInfo(@RequestParam @ApiParam("sku类型") Integer productLevel) {
        Integer contractType = contractProperties.getContactType(productLevel);
        if (contractType == null) {
            return BaseResult.fail("未找到对应的合同类型");
        }
        String content = contractAnalystRecordDao.getContractAdvisorInfo(contractType);
        return BaseResult.success(content);
    }
}

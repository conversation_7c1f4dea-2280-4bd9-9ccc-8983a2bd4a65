package cn.shrise.radium.contentservice.repository;

import cn.shrise.radium.contentservice.entity.SsLiveRoom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface LiveRoomRepository extends JpaRepository<SsLiveRoom, Long>, QuerydslPredicateExecutor<SsLiveRoom> {
}

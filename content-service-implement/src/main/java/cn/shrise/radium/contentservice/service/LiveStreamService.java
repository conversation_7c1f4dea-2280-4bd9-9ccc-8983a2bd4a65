package cn.shrise.radium.contentservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.entity.LiveStreamChannel;
import cn.shrise.radium.contentservice.entity.QLiveStreamChannel;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class LiveStreamService {

    private final JPAQueryFactory queryFactory;

    final QLiveStreamChannel liveStreamChannel = QLiveStreamChannel.liveStreamChannel;

    public List<LiveStreamChannel> findAll(Integer companyType, Boolean filterDeleted) {
        JPAQuery<LiveStreamChannel> query =
                queryFactory.selectFrom(liveStreamChannel).where(liveStreamChannel.companyType.eq(companyType));
        if (ObjectUtil.isNotNull(filterDeleted) && filterDeleted) {
            query = query.where(liveStreamChannel.isDeleted.eq(false).or(liveStreamChannel.isDeleted.isNull()));
        }
        return query.fetch();
    }

}

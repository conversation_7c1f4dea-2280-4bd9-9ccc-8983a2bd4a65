package cn.shrise.radium.contentservice.properties;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "video.config")
@EnableConfigurationProperties
public class VideoConfigProperty {

    private String regionId;
    private String accessKeyId;
    private String accessKeySecret;
    private String roleArn;
    private String roleSessionName;

}

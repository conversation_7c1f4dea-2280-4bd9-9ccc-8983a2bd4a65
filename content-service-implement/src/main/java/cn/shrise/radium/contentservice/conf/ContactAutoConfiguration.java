package cn.shrise.radium.contentservice.conf;

import cn.shrise.radium.contentservice.properties.ContractProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/6/16, 星期一
 **/

@Configuration
@EnableConfigurationProperties(ContractProperties.class)
public class ContactAutoConfiguration {
}

package cn.shrise.radium.contentservice.service.stream;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.constant.*;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.repository.SsStreamMessageCommentRepository;
import cn.shrise.radium.contentservice.repository.SsStreamMessageRepository;
import cn.shrise.radium.contentservice.req.CreateTeamStreamMessageReq;
import cn.shrise.radium.contentservice.req.StreamMessageReq;
import cn.shrise.radium.contentservice.req.TextModerationReq;
import cn.shrise.radium.contentservice.req.UpdateStreamMessageLikeReq;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.contentservice.service.StreamService;
import cn.shrise.radium.contentservice.service.green.GreenService;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.ContentTeamResp;
import com.alibaba.fastjson.JSON;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.PagedList;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.DateUtils.dateToInstant;
import static cn.shrise.radium.contentservice.constant.ArticleOperateRecordConstant.AUDIT_REFUSE;
import static cn.shrise.radium.contentservice.constant.ArticleStatusEnum.AS_Rejected;
import static cn.shrise.radium.contentservice.constant.ArticleStatusEnum.AS_Released;
import static cn.shrise.radium.contentservice.constant.AuditStatusConstant.*;
import static cn.shrise.radium.contentservice.constant.LiveRoomMessageAuditStatusEnum.*;
import static cn.shrise.radium.contentservice.constant.StreamMessageTypeConstant.SMT_STOCK;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class StreamMessageService {

    private final JPAQueryFactory queryFactory;

    private final SsStreamMessageRepository ssStreamMessageRepository;

    private final QSsStreamMessage ssStreamMessage = QSsStreamMessage.ssStreamMessage;

    private final QSsStreamMessageComment ssStreamMessageComment = QSsStreamMessageComment.ssStreamMessageComment;

    private final QSsStreamMessageLike ssStreamMessageLike = QSsStreamMessageLike.ssStreamMessageLike;

    private final QSsStreamChannel ssStreamChannel = QSsStreamChannel.ssStreamChannel;

    private final EntityManager entityManager;

    private final CriteriaBuilderFactory criteriaBuilderFactory;
    private final JdbcTemplate jdbcTemplate;
    private final DataSource dataSource;

    private final StreamService streamService;

    private final SsStreamMessageCommentRepository streamMessageCommentRepository;

    private final GreenService greenService;
    private final StreamOperateRecordService streamOperateRecordService;
    private final UserClient userClient;

    @Transactional(rollbackFor = Exception.class)
    public void deleteMessage(Long id) {
        queryFactory.update(ssStreamMessage)
                .set(ssStreamMessage.enabled, false)
                .where(ssStreamMessage.id.eq(id))
                .execute();
    }

    public Optional<SsStreamMessage> getStreamMessage(Long id) {
        return ssStreamMessageRepository.findById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteMessageComment(Long id) {
        queryFactory.update(ssStreamMessageComment)
                .set(ssStreamMessageComment.enabled, false)
                .where(ssStreamMessageComment.id.eq(id))
                .execute();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateChoice(Long id, Integer auditorId, Boolean isChoice) {
        JPAUpdateClause updateClause = queryFactory.update(ssStreamMessageComment)
                .set(ssStreamMessageComment.isChose, isChoice);
        if (isChoice) {
            updateClause.set(ssStreamMessageComment.auditStatus, AuditPass.getValue())
                    .set(ssStreamMessageComment.auditTime, Instant.now())
                    .set(ssStreamMessageComment.auditorId, auditorId);
        } else {
            updateClause.set(ssStreamMessageComment.auditStatus, AuditNotPass.getValue())
                    .set(ssStreamMessageComment.auditTime, Instant.now())
                    .set(ssStreamMessageComment.auditorId, auditorId);
        }
        updateClause.where(ssStreamMessageComment.id.eq(id))
                .execute();
    }

    public Page<StreamMessageResp> getStreamMessagePage(Integer userId, Integer companyType, LocalDate startTime, LocalDate endTime, Long channelId, Pageable pageable) {

        BlazeJPAQuery<SsStreamMessage> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<StreamMessageResp> query = blazeJpaQuery.select(Projections.bean(StreamMessageResp.class,
                        ssStreamMessage.as("streamMessage"),
                        ssStreamChannel.name.as("channelName"),
                        ssStreamMessageComment.id.countDistinct().as("commentCount"),
                        ssStreamMessageLike.id.countDistinct().as("likeCount")))
                .from(ssStreamMessage)
                .leftJoin(ssStreamChannel)
                .on(ssStreamMessage.channelId.eq(ssStreamChannel.id))
                .leftJoin(ssStreamMessageComment)
                .on(ssStreamMessage.id.eq(ssStreamMessageComment.messageId))
                .leftJoin(ssStreamMessageLike)
                .on(ssStreamMessage.id.eq(ssStreamMessageLike.messageId)
                        .and(ssStreamMessageLike.enabled.eq(true)))
                .where(ssStreamMessage.companyType.eq(companyType));

        if (Objects.nonNull(channelId)) {
            query.where(ssStreamMessage.channelId.eq(channelId));
        } else {
            List<Long> channelList = streamService.getChannelList(userId, companyType).stream().map(SsStreamChannel::getId).collect(Collectors.toList());
            if (channelList.isEmpty()) {
                return null;
            }
            query.where(ssStreamMessage.channelId.in(channelList));
        }

        if (Objects.nonNull(startTime)) {
            query.where(ssStreamMessage.gmtCreate.goe(startTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        }

        if (Objects.nonNull(endTime)) {
            query.where(ssStreamMessage.gmtCreate.lt(endTime.atStartOfDay().plusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
        }

        PagedList<StreamMessageResp> streamMessagePage = query.groupBy(ssStreamMessage.id)
                .orderBy(ssStreamMessage.gmtCreate.desc(), ssStreamMessage.id.desc())
                .fetchPage((int) pageable.getOffset(), pageable.getPageSize());

        List<StreamMessageResp> messageRespList = new ArrayList<>(streamMessagePage);

        return new PageImpl<>(messageRespList, pageable, streamMessagePage.getTotalSize());
    }

    public List<SsStreamMessage> getStreamMessageList(Integer companyType, Collection<Integer> analystIds, Long channelId, Instant openTime, Pageable pageable) {
        if (ObjectUtils.isEmpty(analystIds)) {
            return Collections.emptyList();
        }

        BlazeJPAQuery<SsStreamMessage> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<SsStreamMessage> query = blazeJpaQuery.select(ssStreamMessage)
                .from(ssStreamMessage)
                .where(ssStreamMessage.enabled.eq(true))
                .where(ssStreamMessage.analystId.in(analystIds))
                .where(ssStreamMessage.auditStatus.eq(StreamMessageAuditStatusConstant.AUDIT_PASS));

        if (ObjectUtil.isNotNull(companyType)) {
            query.where(ssStreamMessage.companyType.eq(companyType));
        }

        if (Objects.nonNull(channelId)) {
            query.where(ssStreamMessage.channelId.eq(channelId));
        }

        if (Objects.nonNull(openTime)) {
            query.where(ssStreamMessage.gmtCreate.goe(openTime));
        }

        return query.offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(ssStreamMessage.auditTime.desc(), ssStreamMessage.id.desc())
                .fetch();
    }

    public PageResult<List<SsStreamMessage>> getTeamStreamMessageList(Integer companyType, Collection<Long> teamIds, Integer status,
                                                          Instant startTime, Instant endTime, Integer current, Integer size) {
        JPAQuery<SsStreamMessage> query = queryFactory.select(ssStreamMessage)
                .from(ssStreamMessage)
                .where(ssStreamMessage.teamId.in(teamIds));

        if (ObjectUtil.isNotNull(companyType)) {
            query.where(ssStreamMessage.companyType.eq(companyType));
        }

        if (Objects.nonNull(startTime)) {
            query.where(ssStreamMessage.gmtCreate.goe(startTime));
        }

        if (Objects.nonNull(endTime)) {
            query.where(ssStreamMessage.gmtCreate.loe(endTime));
        }

        if (Objects.nonNull(status)) {
            query.where(ssStreamMessage.status.eq(status));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(ssStreamMessage.gmtCreate.desc(), ssStreamMessage.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public SsStreamMessage sendMessage(StreamMessageReq req) {
        String imageList = ObjectUtils.isNotEmpty(req.getImageList()) ? JSON.toJSONString(req.getImageList()) : null;
        SsStreamMessage streamMessage = SsStreamMessage.builder()
                .analystId(req.getAnalystId())
                .messageType(req.getMessageType())
                .channelId(req.getChannelId())
                .content(req.getContent())
                .imageUrl(req.getImageUrl())
                .imageList(imageList)
                .audioUrl(req.getAudioUrl())
                .audioDuration(req.getAudioDuration())
                .companyType(req.getCompanyType())
                .creatorId(req.getCreatorId())
                .enabled(true)
                .auditStatus(StreamMessageAuditStatusConstant.AUDIT_WAITING)
                .build();
        return ssStreamMessageRepository.save(streamMessage);
    }

    public Page<SsStreamMessageComment> getCommentPage(Long messageId, Boolean isChoice, Boolean audit, Integer auditStatus, LocalDate startTime, LocalDate endTime, Pageable pageable) {
        BlazeJPAQuery<SsStreamMessageComment> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<SsStreamMessageComment> query = blazeJpaQuery.select(ssStreamMessageComment)
                .from(ssStreamMessageComment)
                .where(ssStreamMessageComment.messageId.eq(messageId));

        if (Objects.nonNull(isChoice)) {
            query.where(ssStreamMessageComment.isChose.eq(isChoice));
        }

        if (Objects.nonNull(startTime)) {
            query.where(ssStreamMessageComment.gmtCreate.goe(startTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        }

        if (Objects.nonNull(endTime)) {
            query.where(ssStreamMessageComment.gmtCreate.lt(endTime.atStartOfDay().plusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
        }

        if (ObjectUtil.isNotEmpty(audit)) {
            if (audit) {
                if (ObjectUtil.isNotEmpty(auditStatus)) {
                    query.where(ssStreamMessageComment.auditStatus.eq(auditStatus));
                } else {
                    query.where(ssStreamMessageComment.auditStatus.in(Arrays.asList(AUDIT_NOT_PASS, AUDIT_PASS)));
                }
            } else {
                query.where(ssStreamMessageComment.auditStatus.eq(AUDIT_EXECUTING));
            }
        }

        PagedList<SsStreamMessageComment> pagedList = query.orderBy(ssStreamMessageComment.commentTime.desc(), ssStreamMessageComment.id.desc())
                .fetchPage((int) pageable.getOffset(), pageable.getPageSize());

        List<SsStreamMessageComment> messageComments = new ArrayList<>(pagedList);

        return new PageImpl<>(messageComments, pageable, pagedList.getTotalSize());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAnswer(Long id, String content, Integer answerId) {
        queryFactory.update(ssStreamMessageComment)
                .set(ssStreamMessageComment.answerContent, content)
                .set(ssStreamMessageComment.answerId, answerId)
                .set(ssStreamMessageComment.answerTime, Instant.now())
                .where(ssStreamMessageComment.id.eq(id))
                .execute();
    }

    public List<StreamMessageStatistics> getStreamMessageLikeStatistics(Collection<Long> messageIds) {
        if (ObjectUtils.isEmpty(messageIds)) {
            return Collections.emptyList();
        }

        NumberExpression<Long> countExpression = ssStreamMessageLike.customerId.count().as("count");
        Set<Long> messageIdSet = new HashSet<>(messageIds);
        List<Tuple> tuples = queryFactory.select(Projections.tuple(ssStreamMessageLike.messageId,
                        countExpression))
                .from(ssStreamMessageLike)
                .where(ssStreamMessageLike.messageId.in(messageIdSet))
                .where(ssStreamMessageLike.enabled.eq(true))
                .groupBy(ssStreamMessageLike.messageId)
                .fetch();

        return tuples.stream().map(tuple -> StreamMessageStatistics.builder()
                        .id(tuple.get(ssStreamMessageLike.messageId))
                        .count(tuple.get(countExpression))
                        .build())
                .collect(Collectors.toList());
    }

    public List<StreamMessageLikeStatistics> getUserStreamMessageLikeStatistics(Integer userId, Collection<Long> messageIds) {
        if (ObjectUtils.isEmpty(messageIds)) {
            return Collections.emptyList();
        }

        Set<Long> messageIdSet = new HashSet<>(messageIds);
        JPAQuery<Long> query = queryFactory.select(ssStreamMessageLike.messageId)
                .from(ssStreamMessageLike)
                .where(ssStreamMessageLike.customerId.eq(userId))
                .where(ssStreamMessageLike.messageId.in(messageIdSet))
                .where(ssStreamMessageLike.enabled.eq(true));
        if (ObjectUtil.isNotNull(userId)) {
            query.where(ssStreamMessageLike.customerId.eq(userId));
        }
        List<Long> likedList = query.fetch();

        Set<Long> likedSet = new HashSet<>(likedList);

        return messageIdSet.stream().map(e -> {
            boolean isLike = likedSet.contains(e);
            return StreamMessageLikeStatistics.builder()
                    .id(e)
                    .isLike(isLike)
                    .build();
        }).collect(Collectors.toList());
    }


    @Transactional
    public Boolean updateStreamMessageLike(UpdateStreamMessageLikeReq req) {
        SsStreamMessageLike entity = SsStreamMessageLike.builder()
                .customerId(req.getUserId())
                .messageId(req.getMessageId())
                .enabled(req.getLike())
                .build();

        String sql = SqlUtil.onDuplicateKeyUpdateSql(entity);
        int affected = jdbcTemplate.update(sql);
        return affected > 0;
    }

    public List<StreamMessageStatistics> getStreamMessageCommentStatistics(Collection<Long> messageIds, Integer auditStatus, Boolean isChose) {
        if (ObjectUtils.isEmpty(messageIds)) {
            return Collections.emptyList();
        }

        NumberExpression<Long> countExpression = ssStreamMessageComment.customerId.count().as("count");
        Set<Long> messageIdSet = new HashSet<>(messageIds);
        final JPAQuery<Tuple> query = queryFactory.select(Projections.tuple(ssStreamMessageComment.messageId,
                        countExpression))
                .from(ssStreamMessageComment)
                .where(ssStreamMessageComment.messageId.in(messageIdSet))
                .where(ssStreamMessageComment.enabled.eq(true))
                .groupBy(ssStreamMessageComment.messageId);

        if (ObjectUtils.isNotEmpty(auditStatus)) {
            query.where(ssStreamMessageComment.auditStatus.eq(auditStatus));
        }

        if (ObjectUtils.isNotEmpty(isChose)) {
            query.where(ssStreamMessageComment.isChose.eq(isChose));
        }

        List<Tuple> tuples = query.fetch();

        return tuples.stream().map(tuple -> StreamMessageStatistics.builder()
                        .id(tuple.get(ssStreamMessageComment.messageId))
                        .count(tuple.get(countExpression))
                        .build())
                .collect(Collectors.toList());
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean sendComment(Long messageId, Integer customerId, String commentContent, Integer companyType) {
        SsStreamMessageComment streamMessageComment = SsStreamMessageComment.builder()
                .messageId(messageId)
                .commentContent(commentContent)
                .commentTime(Instant.now())
                .customerId(customerId)
                .enabled(true)
                .isChose(false)
                .auditStatus(AuditExecuting.getValue())
                .build();
        TextModerationReq textModerationReq = TextModerationReq.builder()
                .companyType(companyType)
                .service("comment_detection")
                .content(commentContent)
                .dataId(UUID.randomUUID().toString())
                .build();
        TextModerationResp resp = greenService.textModeration(textModerationReq);
        streamMessageComment.setAuditStatus(AUDIT_EXECUTING);
        if (!resp.isPassed()) {
            streamMessageComment.setAuditStatus(AUDIT_NOT_PASS);
        }
        streamMessageComment.setCommentTime(Instant.now());
        streamMessageComment.setResultId(resp.getResultId());
        streamMessageCommentRepository.save(streamMessageComment);
        return true;
    }

    public Page<SsStreamMessageComment> getCommentPage(Long messageId, Pageable pageable) {

        BlazeJPAQuery<SsStreamMessageComment> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<SsStreamMessageComment> query = blazeJpaQuery.select(ssStreamMessageComment)
                .from(ssStreamMessageComment)
                .where(ssStreamMessageComment.enabled.eq(true))
                .where(ssStreamMessageComment.messageId.eq(messageId))
                .where(ssStreamMessageComment.auditStatus.eq(AuditPass.getValue()));

        PagedList<SsStreamMessageComment> pagedList = query.orderBy(ssStreamMessageComment.commentTime.desc(), ssStreamMessageComment.id.desc())
                .fetchPage((int) pageable.getOffset(), pageable.getPageSize());

        List<SsStreamMessageComment> messageComments = new ArrayList<>(pagedList);

        return new PageImpl<>(messageComments, pageable, pagedList.getTotalSize());
    }

    /**
     * mysql窗口函数解决topk
     * <a href="https://zhuanlan.zhihu.com/p/114431605">mysql窗口函数</a>
     */
    public List<SsStreamMessageComment> getStreamMessageCommentList(Collection<Long> messageIds, Integer auditStatus, Boolean isChose, Integer perSize) {
        if (ObjectUtils.isEmpty(messageIds)) return Collections.emptyList();
        if (perSize <= 0) return Collections.emptyList();

        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
        String sql = "select * from (select *, row_number() over (partition by m.message_id order by m.audit_time desc) as ranking from article_db.ss_stream_message_comment m where m.audit_status = :auditStatus and m.is_chose = :isChose and m.enabled = :enabled and m.message_id in (:messageIds)) as a where a.ranking <= :ranking";
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("auditStatus", auditStatus);
        parameters.addValue("isChose", isChose);
        parameters.addValue("enabled", true);
        parameters.addValue("messageIds", messageIds);
        parameters.addValue("ranking", perSize);

        return namedParameterJdbcTemplate.query(sql, parameters, new RowMapper<SsStreamMessageComment>() {
            @Nullable
            @Override
            public SsStreamMessageComment mapRow(ResultSet rs, int rowNum) throws SQLException {
                Date create = rs.getDate("gmt_create");
                Date modified = rs.getDate("gmt_modified");
//                Date commentTime = rs.getDate("comment_time");
                String commentTime = rs.getObject("comment_time").toString().replace("T", " ");
                Date auditTime = rs.getDate("audit_time");
                Date answerTime = rs.getDate("answer_time");

                return SsStreamMessageComment.builder()
                        .id(rs.getLong("id"))
                        .gmtCreate(dateToInstant(create))
                        .gmtModified(dateToInstant(modified))
                        .messageId(rs.getLong("message_id"))
                        .customerId(rs.getInt("customer_id"))
                        .commentContent(rs.getString("comment_content"))
                        .commentTime(DateUtils.formatterStringToInstant(commentTime, "yyyy-MM-dd HH:mm:ss"))
                        .auditorId(rs.getInt("auditor_id"))
                        .auditStatus(rs.getInt("audit_status"))
                        .auditTime(dateToInstant(auditTime))
                        .answerContent(rs.getString("answer_content"))
                        .answerId(rs.getInt("answer_id"))
                        .answerTime(dateToInstant(answerTime))
                        .isChose(rs.getBoolean("is_chose"))
                        .enabled(rs.getBoolean("enabled"))
                        .build();
            }
        });
    }

    public Page<SsStreamMessage> getTeamStreamMessagePage(List<Long> teamIds, Integer companyType, Integer auditStatus, LocalDate startTime, LocalDate endTime, List<Integer> statusList, Pageable pageable) {

        JPAQuery<SsStreamMessage> query = queryFactory.select(ssStreamMessage)
                .from(ssStreamMessage)
                .where(ssStreamMessage.companyType.eq(companyType));

        if (ObjectUtil.isNotEmpty(teamIds)) {
            query.where(ssStreamMessage.teamId.in(teamIds));
        } else {
            query.where(ssStreamMessage.teamId.isNotNull());
        }

        if (ObjectUtil.isNotEmpty(auditStatus)) {
            if (auditStatus.equals(AUDIT_PASS)) {
                query.where(ssStreamMessage.auditStatus.eq(auditStatus).and(ssStreamMessage.enabled.eq(true)));
            } else {
                query.where(ssStreamMessage.auditStatus.eq(auditStatus));
            }
        }

        if (Objects.nonNull(statusList)) {
            query.where(ssStreamMessage.status.in(statusList));
        }

        if (Objects.nonNull(startTime)) {
            query.where(ssStreamMessage.gmtCreate.goe(DateUtils.getDayOfStart(startTime)));
        }

        if (Objects.nonNull(endTime)) {
            query.where(ssStreamMessage.gmtCreate.lt(DateUtils.getDayOfEnd(endTime)));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        List<SsStreamMessage> messageRespList = query.orderBy(ssStreamMessage.gmtCreate.desc(), ssStreamMessage.id.desc())
                .offset(pageable.getOffset()).limit(pageable.getPageSize()).fetch();

        return new PageImpl<>(messageRespList, pageable, total);
    }

    public List<SsStreamMessage> getLatestTeamStreamMessageList(Integer companyType, Long teamId, Integer status, Long size) {

        BlazeJPAQuery<SsStreamMessage> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);
        BlazeJPAQuery<SsStreamMessage> query = blazeJpaQuery.select(ssStreamMessage)
                .from(ssStreamMessage)
                .where(ssStreamMessage.companyType.eq(companyType));

        if (ObjectUtil.isNotEmpty(teamId)) {
            query.where(ssStreamMessage.teamId.eq(teamId));
        }

        if (ObjectUtil.isNotEmpty(status)) {
            query.where(ssStreamMessage.status.eq(status));
        }

        return query.orderBy(ssStreamMessage.gmtCreate.desc(), ssStreamMessage.id.desc())
                .limit(size)
                .fetch();
    }

    @Transactional
    public SsStreamMessage createTeamStreamMessage(CreateTeamStreamMessageReq req) {
        String image = null;
        if (ObjectUtil.isNotEmpty(req.getImageList())) {
            image = JSON.toJSONString(req.getImageList());
        }
        SsStreamMessage streamMessage = SsStreamMessage.builder()
                .messageType(req.getMessageType())
                .teamId(req.getTeamId())
                .content(req.getContent())
                .imageList(image)
                .audioUrl(req.getAudioUrl())
                .audioDuration(req.getAudioDuration())
                .companyType(req.getCompanyType())
                .creatorId(req.getCreatorId())
                .auditStatus(AUDIT_EXECUTING)
                .status(ArticleStatusEnum.AS_Reviewing.getValue())
                .build();
        SsStreamMessage ssStreamMessage = ssStreamMessageRepository.save(streamMessage);
        UcUsers users = userClient.getUser(req.getCreatorId()).getData();
        streamOperateRecordService.saveRecord(ssStreamMessage.getId(), req.getCreatorId(), StreamOperateRecordEnum.CREATE_STREAM, users.getRealName(), null);
        return ssStreamMessage;
    }

    public void auditComment(Long id, Integer auditorId, Integer auditStatus) {
        Optional<SsStreamMessageComment> optional = streamMessageCommentRepository.findById(id);
        if (optional.isPresent()) {
            SsStreamMessageComment comment = optional.get();
            if (ObjectUtil.equals(auditStatus, AuditPass.getValue())) {
                comment.setAuditTime(Instant.now());
                comment.setAuditStatus(AuditPass.getValue());
                comment.setAuditorId(auditorId);
            } else if (ObjectUtil.equals(auditStatus, AuditNotPass.getValue())) {
                comment.setAuditTime(Instant.now());
                comment.setAuditStatus(AuditNotPass.getValue());
                comment.setAuditorId(auditorId);
            } else {
                throw new BusinessException("审核状态不支持");
            }
            streamMessageCommentRepository.save(comment);
            return;
        }
        throw new RecordNotExistedException("解盘评论不存在");
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteTeamMessage(Long id, Integer userId) {
        queryFactory.update(ssStreamMessage)
                .set(ssStreamMessage.status, ArticleStatusEnum.AS_Deleted.getValue())
                .where(ssStreamMessage.id.eq(id))
                .execute();
        UcUsers users = userClient.getUser(userId).getData();
        streamOperateRecordService.saveRecord(id, userId, StreamOperateRecordEnum.DELETE_STREAM, users.getRealName(), VideoOperateRecordConstant.DELETE);

    }

    @Transactional
    public void auditStreamMsg(@NonNull Boolean isAudit, Long id, Integer auditorId, String auditRemark) {
        if (isAudit) {
            this.auditPassed(id, auditorId);
        } else {
            this.auditRejected(id, auditorId, auditRemark);
        }
    }

    @Transactional
    public void auditPassed(Long id, Integer auditorId) {
        queryFactory.update(ssStreamMessage)
                .set(ssStreamMessage.status, AS_Released.getValue())
                .set(ssStreamMessage.auditorId, auditorId)
                .set(ssStreamMessage.auditTime, Instant.now())
                .where(ssStreamMessage.id.eq(id))
                .execute();
        UcUsers users = userClient.getUser(auditorId).getData();
        streamOperateRecordService.saveRecord(id, auditorId, StreamOperateRecordEnum.AUDIT_STREAM, users.getRealName(), ArticleOperateRecordConstant.AUDIT_PASS);
    }

    @Transactional
    public void auditRejected(Long id, Integer auditorId, String auditRemark) {
        queryFactory.update(ssStreamMessage)
                .set(ssStreamMessage.status, AS_Rejected.getValue())
                .set(ssStreamMessage.auditRemark, auditRemark)
                .set(ssStreamMessage.auditorId, auditorId)
                .set(ssStreamMessage.auditTime, Instant.now())
                .where(ssStreamMessage.id.eq(id))
                .execute();
        UcUsers users = userClient.getUser(auditorId).getData();
        streamOperateRecordService.saveRecord(id, auditorId, StreamOperateRecordEnum.AUDIT_STREAM, users.getRealName(), AUDIT_REFUSE);
    }

    public SsStreamMessage findOneById(@NonNull Long id) {
        return queryFactory.selectFrom(ssStreamMessage).where(ssStreamMessage.id.eq(id)).fetchOne();
    }

    public PageResult<List<StreamMessageResp>> getStreamMessageAuditList(Integer companyType, Boolean isAudit, Integer current, Integer size) {


        JPAQuery<StreamMessageResp> query = queryFactory.select(Projections.bean(StreamMessageResp.class,
                        ssStreamMessage.as("streamMessage"),
                        ssStreamChannel.name.as("channelName")))
                .from(ssStreamMessage)
                .leftJoin(ssStreamChannel)
                .on(ssStreamMessage.channelId.eq(ssStreamChannel.id))
                .where(ssStreamMessage.companyType.eq(companyType))
                .where(ssStreamMessage.enabled.eq(true));

        if (isAudit) {
            query.where(ssStreamMessage.auditStatus.in(StreamMessageAuditStatusConstant.AUDIT_PASS, StreamMessageAuditStatusConstant.AUDIT_NOT_PASS));
        } else {
            query.where(ssStreamMessage.auditStatus.eq(StreamMessageAuditStatusConstant.AUDIT_WAITING));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(ssStreamMessage.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional(rollbackFor = Exception.class)
    public void auditStreamMessage(Integer userId, Long messageId, Integer auditStatus, String refuseRemark) {

        JPAUpdateClause clause = queryFactory.update(ssStreamMessage)
                .where(ssStreamMessage.id.eq(messageId))
                .where(ssStreamMessage.auditStatus.eq(StreamMessageAuditStatusConstant.AUDIT_WAITING))
                .set(ssStreamMessage.auditStatus, auditStatus)
                .set(ssStreamMessage.auditorId, userId)
                .set(ssStreamMessage.auditTime, Instant.now());

        if (ObjectUtil.equals(auditStatus, StreamMessageAuditStatusConstant.AUDIT_NOT_PASS)) {
            if (ObjectUtil.isNotEmpty(refuseRemark)) {
                clause.set(ssStreamMessage.auditRemark, refuseRemark);
            } else {
                throw new BusinessException("拒绝原因不能为空");
            }
        }

        long execute = clause.execute();
        if (execute == 0) {
            throw new BusinessException("状态已变更，请刷新重试");
        }
    }

    public BaseResult<List<StockMessageResp>> getChosenStocks(String number, Integer days, Integer companyType) {
        ContentTeamResp resp = userClient.getContentTeam(companyType, number).getData();
        Optional<ContentTeamResp> contentTeamOptional = Optional.ofNullable(resp);
        if (!contentTeamOptional.isPresent()) {
            throw new BusinessException("内容主编号不存在");
        }

        LocalDateTime endTime = DateUtils.getTodayStartTime(1);
        LocalDateTime startTime = endTime.minusDays(days);


        JPAQuery<SsStreamMessage> query = queryFactory.select(ssStreamMessage)
                .from(ssStreamMessage)
                .where(ssStreamMessage.teamId.eq(contentTeamOptional.get().getId()))
                .where(ssStreamMessage.messageType.eq(SMT_STOCK))
                .where(ssStreamMessage.status.eq(AS_Released.getValue()))
                .where(ssStreamMessage.gmtCreate.goe(DateUtils.localDateTimeToInstant(startTime)).and(ssStreamMessage.gmtCreate.lt(DateUtils.localDateTimeToInstant(endTime))));

        // 同一天内去重，取最新的一条
        List<SsStreamMessage> messageList = query.fetch().stream()
                .collect(Collectors.toMap(
                        msg->DateUtils.instantToLocalDate(msg.getGmtCreate()),
                        msg-> msg,
                        (existing, replacement) -> existing.getGmtCreate().compareTo(replacement.getGmtCreate()) > 0 ? existing : replacement
                )).values()
                .stream()
                .sorted(Comparator.comparing(SsStreamMessage::getGmtCreate).reversed())
                .collect(Collectors.toList());

        List<StockMessageResp> resps = messageList.stream().map(msg->StockMessageResp.builder()
                .time(msg.getGmtCreate())
                .stockList(JSON.parseArray(msg.getContent(), String.class))
                .build())
                .collect(Collectors.toList());
        return BaseResult.success(resps);
    }
}

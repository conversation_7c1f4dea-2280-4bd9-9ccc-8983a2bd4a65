import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.shrise.radium.contentservice.enums.RiskLevelType;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.codec.binary.Base64;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Slf4j
public class CommonTest {

    @Test
    public void randomTest(){
        for (int i = 0; i < 20; i++) {
            Set<Integer> set = RandomUtil.randomEleSet(CollUtil.newArrayList(1, 2, 3, 4, 5, 6), 2);
            System.out.println(set);
        }
    }

    @Test
    public void timeTest() {
        System.out.println(DateUtil.formatDateTime(new Date()));
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(DatePattern.NORM_DATETIME_FORMAT.format(new Date()));
        System.out.println(DatePattern.UTC_FORMAT.format(new Date()));
        System.out.println(DatePattern.UTC_FORMAT.format(DateUtil.offsetHour(new Date(), 1)));
    }

    @Test
    public void genOssToken(){
        // Endpoint以杭州为例，其它Region请按实际情况填写。
        String endpoint = "http://oss-cn-shanghai.aliyuncs.com";
        // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录RAM控制台创建RAM账号。
        String accessKeyId = "LTAI5t8WtM1ADaVyiyB6PWE2";
        String accessKeySecret = "******************************";
        // 设置存储空间名称。
        String videoBucket = "gs-stream-input";
        String fileBucket = "gs-file-src";
        // 在URL中添加存储空间名称，添加后URL如下：http://yourBucketName.oss-cn-hangzhou.aliyuncs.com。
        String host = endpoint.replace("http://", "http://" + fileBucket+ ".");
        // 设置表单Map。
        Map<String, String> formFields = new LinkedHashMap<>();
        // 设置OSSAccessKeyId。
        formFields.put("OSSAccessKeyId", accessKeyId);
        DateTime expireTime = DateUtil.offsetHour(new Date(), 1);
        String expirationTime = DatePattern.UTC_FORMAT.format(expireTime);
        String policy = String.format("{\"expiration\": \"%s\",\"conditions\": [[\"starts-with\", \"$key\", \"\"]]}", expirationTime);
        String encodePolicy = new String(Base64.encodeBase64(policy.getBytes()));
        // 设置policy。
        formFields.put("policy", encodePolicy);
        // 生成签名。
        String signature = com.aliyun.oss.common.auth.ServiceSignature.create().computeSignature(accessKeySecret, encodePolicy);
        // 设置签名。
        formFields.put("Signature", signature);
        formFields.put("Host", host);
        formFields.put("Expire", String.valueOf(expireTime.getTime() / 1000));
        System.out.println(formFields);
    }

    @Test
    public void retryTest(){
        sendMsg(0);
        System.out.println(2);
        sendMsg(5);
    }

    private boolean sendMsg(int num){
        boolean sendSuccess = false;
        int retryCount = 0;
        while (!sendSuccess && retryCount < 3){
            try {
                sendSuccess = num < retryCount;
            }catch (Exception e) {
                System.out.printf("retryCount: {}, errorInfo: {}", retryCount, e.getMessage());
            }
            log.info("retryCount: {}, errorInfo: {}, res: {}", retryCount, num, sendSuccess);
            retryCount+=1;
        }
        return sendSuccess;
    }

    @Test
    public void getAge(){
        String code = "612459199605201321";
        String birthDate = code.subSequence(6, 14).toString();
        System.out.println(birthDate);
        System.out.println(DateUtil.ageOfNow(birthDate));
    }

    @Test
    public void getLevel(){
        System.out.println(RiskLevelType.getLevelByScore(99) + 99);
        System.out.println(RiskLevelType.getLevelByScore(83) + 83);
        System.out.println(RiskLevelType.getLevelByScore(59) + 59);
        System.out.println(RiskLevelType.getLevelByScore(54) + 54);
        System.out.println(RiskLevelType.getLevelByScore(39) + 39);
        System.out.println(RiskLevelType.getLevelByScore(13) + 13);
    }
}

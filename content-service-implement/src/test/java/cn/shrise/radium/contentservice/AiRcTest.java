package cn.shrise.radium.contentservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.contentservice.airec.AIRecBehavior;
import cn.shrise.radium.contentservice.airec.AIRecItem;
import cn.shrise.radium.contentservice.airec.AIRecReq;
import cn.shrise.radium.contentservice.airec.AIRecUser;
import cn.shrise.radium.contentservice.constant.ContentServiceConst;
import cn.shrise.radium.contentservice.resp.AiRecResultResp;
import cn.shrise.radium.contentservice.resp.AiRecShortVideoResp;
import cn.shrise.radium.contentservice.resp.AiRecUserResp;
import cn.shrise.radium.contentservice.resp.SsShortVideoResp;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import cn.shrise.radium.contentservice.service.airec.AiRecService;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.airec.model.v20181012.PushDocumentRequest;
import com.aliyuncs.airec.model.v20181012.PushDocumentResponse;
import com.aliyuncs.airec.model.v20181012.RecommendRequest;
import com.aliyuncs.airec.model.v20181012.RecommendResponse;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class AiRcTest {

    @Autowired
    RocketMqUtils rocketMqUtils;

    @Autowired
    ShortVideoService shortVideoService;

    @Autowired
    AiRecService aiRecService;

    @Test
    void testBuilderUser() {
        String regionId = "cn-hangzhou";
        String accessKeyId = "LTAI5tRpmoqAfxDpYY4LFB5K";
        String secret = "******************************";
        String endpoint = "airec.cn-hangzhou.aliyuncs.com";
        String instanceId = "airec-cn-7pp2v3z36006";
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, secret);
        DefaultProfile.addEndpoint(regionId, "Airec", endpoint);
        DefaultAcsClient client = new DefaultAcsClient(profile);

        PushDocumentRequest request = new PushDocumentRequest();
        request.setAcceptFormat(FormatType.JSON);
        //填入实例id
        request.setInstanceId(instanceId);
        //填入要上报的数据表名：user/item/behavior
//        request.setTableName("user");
//        request.setTableName("item");
        request.setTableName("behavior");

//        String content = JSON.toJSONString(buildRecommendUser());
//        String content = JSON.toJSONString(buildRecommendItem());
        String content = JSON.toJSONString(buildRecommendBehavior());
        request.setHttpContent(content.getBytes(), "UTF-8", FormatType.JSON);

        try {
            PushDocumentResponse response = client.getAcsResponse(request);
            System.out.println(response.getResult());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    void testGetPushResult() {
        String regionId = "cn-hangzhou";
        String accessKeyId = "LTAI5tRpmoqAfxDpYY4LFB5K";
        String secret = "******************************";
        String endpoint = "airec.cn-hangzhou.aliyuncs.com";
        String instanceId = "airec-cn-7pp2v3z36006";
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, secret);
        DefaultProfile.addEndpoint(regionId, "Airec", endpoint);
        DefaultAcsClient client = new DefaultAcsClient(profile);

        RecommendRequest request = new RecommendRequest();
        request.setUserId("127565");
        request.setReturnCount(1);
        request.setSceneId("1");
        request.setAcceptFormat(FormatType.JSON);
        request.setInstanceId(instanceId);
        try {
            RecommendResponse response = client.getAcsResponse(request);
            for (RecommendResponse.ResultItem item : response.getResult()) {
                System.out.println(item.getItemId());
                System.out.println(item.getItemType());
                System.out.println(item.getTraceId());
                System.out.println(item.getTraceInfo());
                System.out.println(item.getMatchInfo());
                System.out.println(item.getWeight());
                System.out.println(item.getPosition());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    List<AIRecReq> buildRecommendUser() {
        List<AIRecReq> reqList = Lists.newArrayList();
        AIRecUser user = AIRecUser.builder()
                .userId("127565")
                .userIdType("2")
                .thirdUserName("抄在山顶的齐天大圣")
                .thirdUserType("101")
                .phoneMd5("MTIzNDU2Nzg5MGFiY2RlZoWX5hk4LuNBB9I2Fp4qbkjqUsa4BvfcwYKyPvbep/Ek")
                .gender("0")
                .build();
        AIRecReq<Object> req = AIRecReq.builder().cmd("add").fields(user).build();
        reqList.add(req);
        return reqList;
    }

    List<AIRecReq> buildRecommendItem() {
        List<AIRecReq> reqList = Lists.newArrayList();
        AIRecItem item = AIRecItem.builder()
                .itemId("21")
                .itemType("shortvideo")
                .status("1")
                .sceneId("1")
                .duration("14")
                .pubTime("1663035947")
                .expireTime("1663899947")
                .build();
        AIRecReq<Object> req = AIRecReq.builder().cmd("add").fields(item).build();
        reqList.add(req);
        return reqList;
    }

    List<AIRecReq> buildRecommendBehavior() {
        List<AIRecReq> reqList = Lists.newArrayList();
        AIRecBehavior behavior = AIRecBehavior.builder()
                .itemId("21")
                .itemType("shortvideo")
                .bhvType("expose")
                .traceId("selfhold")
                .traceInfo("1")
                .sceneId("1")
                .bhvTime("1663035967")
                .bhvValue("1")
                .userId("127565")
                .platform("ios")
                .build();
        AIRecReq<Object> req = AIRecReq.builder().cmd("add").fields(behavior).build();
        reqList.add(req);
        return reqList;
    }


    @Test
    void aiRecShortVideoConsumer() {
        List<SsShortVideoResp> respList = shortVideoService.getShortVideoList(34, null, null, 1, 1).orElse(null);
        if (ObjectUtils.isNotEmpty(respList)) {
            AiRecShortVideoResp resp = new AiRecShortVideoResp();
            SsShortVideoResp ssShortVideoResp = respList.get(0);
            BeanUtil.copyProperties(ssShortVideoResp, resp);
            rocketMqUtils.send(ContentServiceConst.CONTENT_TOPIC, ContentServiceConst.MqTagType.AI_REC_SHORT_VIDEO, resp);
        }
    }

    @Test
    void testAirec() {
        List<Long> teamIds = Arrays.asList(9L, 25L, 26L, 27L, 28L, 29L, 30L, 31L, 32L);
//        List<Long> teamIds = Arrays.asList(9L);
        Integer analystId = null;
        Integer customerId = 128125;
        Map<Long, AiRecResultResp> recResultMap = null;
        if (ObjectUtils.isEmpty(analystId) && ObjectUtils.isNotEmpty(customerId)) {
            // 获取推荐结果
            String filterRuleString = aiRecService.getFilterRule(teamIds);
            List<AiRecResultResp> recResults = aiRecService.getAiRecResults(customerId, 20, filterRuleString);
            recResultMap = recResults.stream().collect(Collectors.toMap(e -> Long.valueOf(e.getItemId()), e -> e));
        }
        shortVideoService.getShortVideoList(analystId, recResultMap, customerId, 0, 20);
    }
}

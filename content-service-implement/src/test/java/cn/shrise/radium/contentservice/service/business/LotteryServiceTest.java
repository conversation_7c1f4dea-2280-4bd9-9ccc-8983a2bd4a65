package cn.shrise.radium.contentservice.service.business;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.entity.SsExpress;
import cn.shrise.radium.contentservice.entity.SsNew;
import cn.shrise.radium.contentservice.entity.SsNewsChannel;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.properties.NewsConfigProperty;
import cn.shrise.radium.contentservice.req.GetExpressReq;
import cn.shrise.radium.contentservice.req.GetExpressTokenReq;
import cn.shrise.radium.contentservice.req.GetNewsDetailReq;
import cn.shrise.radium.contentservice.resp.PostExpressResp;
import cn.shrise.radium.contentservice.service.NewsService;
import cn.shrise.radium.contentservice.service.RoomLotteryService;
import cn.shrise.radium.contentservice.service.SsExpressService;
import cn.shrise.radium.contentservice.util.HttpUtils;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class LotteryServiceTest {

    @Autowired
    LotteryService lotteryService;
    @Autowired
    RoomLotteryService roomLotteryService;
    @Autowired
    NewsService newsService;
    @Autowired
    OSS ossClient;
    @Autowired
    NewsConfigProperty newsConfigProperty;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    SsExpressService ssExpressService;

    @Test
    void handleDrawLottery() {
        lotteryService.handleDrawLottery(21L);
    }

    @Test
    void RoomLottery() {
        roomLotteryService.updateRoomLotteryStatus(50L, RoomLotteryType.OPEN_2, 240);
    }

    @Test
    void testBatchSave() {
        List<SsNew> ssNews = new ArrayList<>();
        SsNew ssNew1 = new SsNew();
        ssNew1.setPublishTime(Instant.now());
        ssNew1.setChannelId(1l);
        ssNew1.setTitle("公路沿线充电基础设施建设加快推进 将有效缓解“里程焦虑”");
        ssNew1.setBrief("　　本报记者 杜雨萌");
        ssNew1.setSourceId("62be505da65404729cc6ccdb66acf356");
        ssNew1.setContent("8月25日，交通运输部发布消息称，交通运输部、国家能源局、国家电网有限公司、中国南方电网有限责任公司近日联合印发《加快推进公路沿线充电基础设施建设行动方案》(下称《行动方案》)，提出力争到2022年底前，全国除高寒高海拔以外区域的高速公路服务区能够提供基本充电服务。一位接近系统内的人士告诉记者，从《行动方案》的内容来看，明确在今年年底前，要实现高速公路服务区大规模建设充电桩。这将大幅缓解电动汽车远行的“里程焦虑”，进而加快促进新能源汽车产业的发展。据交通运输部公路局相关负责人介绍，截至目前，全国6618个高速公路服务区中，已有3102个服务区建成了13374个充电桩，基本满足了当前电动汽车充电要求。但与我国新能源汽车迅猛发展相比，目前高速公路充电基础设施设置总量不够、覆盖面不足等问题还较为突出，普通国省干线和农村公路充电基础设施建设工作还未全面起步，无法有效满足电动汽车远程出行的需求。据相关部门统计，截至今年6月底，全国新能源汽车保有量已达到1001万辆。根据国家有关规划和预测，到2025年，全国新能源汽车新车保有量将超过2500万辆；到2030年将达到8000万辆。为此，《行动方案》提出了三个阶段的工作目标：第一阶段，力争到2022年底前，全国除高寒高海拔以外区域的高速公路服务区能够提供基本充电服务；第二阶段，到2023年底前，具备条件的普通国省干线公路服务区(站)能够提供基本充电服务；第三阶段，到2025年底前，高速公路和普通国省干线公路服务区(站)充电基础设施进一步加密优化，农村公路沿线有效覆盖，基本形成“固定设施为主体，移动设施为补充，重要节点全覆盖，运行维护服务好，群众出行有保障”的公路沿线充电基础设施网络，保障公众“回得了家、出得了城、下得了乡”，畅行无忧。对于高速公路服务区，《行动方案》还提出，每个服务区建设的充电基础设施或预留建设安装条件的车位原则上不低于小型客车停车位的10%。北京特亿阳光新能源总裁祁海珅在接受《证券日报》记者采访时表示，加快建设充电设施不仅可以带动诸多产业投资，还可以配合电网构建高效率、智慧电力调度运行体系，即电力系统通过充电桩给电动汽车充电，在电网用电高峰时，电动汽车也可以通过充电桩放电给用户使用或输电给电网进行反向交易，对电网系统削峰填谷十分有利。在《行动方案》明确的六项任务中，还特别提到要探索推进新技术新设备应用，包括支持电动汽车生产、大型运输等企业在服务区(站)建设布局换电站等。祁海珅称，加快推进充电、换电领域新型基础设施建设，一方面能促进新能源汽车发展；另一方面也能促进构建以新能源为主体的新型电力系统建设。尤其是发展“分布式光伏发电+储能调节电网+充电桩”一体式的“光储充”新型电力基础设施，既可以补足充电桩数量不足的短板，还可以促进新能源电动车产业的发展。前述负责人称，公路沿线充电基础设施的建设运营，将充分发挥市场在资源配置中的决定性作用，积极引导社会资本参与建设运营。通过采用合作经营等模式、签订长期合作协议、给予运营商合理的收入预期等方式，充分调动各方积极性，共同开展充电基础设施运营与维护，最大程度实现优势互补、合作共赢。(证券日报)");
        ssNew1.setContentHtml("<p>8月25日，交通运输部发布消息称，交通运输部、国家能源局、国家电网有限公司、中国南方电网有限责任公司近日联合印发《加快推进公路沿线充电基础设施建设行动方案》(下称《行动方案》)，提出力争到2022年底前，全国除高寒高海拔以外区域的高速公路服务区能够提供基本充电服务。</p><p>一位接近系统内的人士告诉记者，从《行动方案》的内容来看，明确在今年年底前，要实现高速公路服务区大规模建设充电桩。这将大幅缓解电动汽车远行的“里程焦虑”，进而加快促进新能源汽车产业的发展。</p><p>据交通运输部公路局相关负责人介绍，截至目前，全国6618个高速公路服务区中，已有3102个服务区建成了13374个充电桩，基本满足了当前电动汽车充电要求。但与我国新能源汽车迅猛发展相比，目前高速公路充电基础设施设置总量不够、覆盖面不足等问题还较为突出，普通国省干线和农村公路充电基础设施建设工作还未全面起步，无法有效满足电动汽车远程出行的需求。</p><p>据相关部门统计，截至今年6月底，全国新能源汽车保有量已达到1001万辆。根据国家有关规划和预测，到2025年，全国新能源汽车新车保有量将超过2500万辆；到2030年将达到8000万辆。</p><p>为此，《行动方案》提出了三个阶段的工作目标：第一阶段，力争到2022年底前，全国除高寒高海拔以外区域的高速公路服务区能够提供基本充电服务；第二阶段，到2023年底前，具备条件的普通国省干线公路服务区(站)能够提供基本充电服务；第三阶段，到2025年底前，高速公路和普通国省干线公路服务区(站)充电基础设施进一步加密优化，农村公路沿线有效覆盖，基本形成“固定设施为主体，移动设施为补充，重要节点全覆盖，运行维护服务好，群众出行有保障”的公路沿线充电基础设施网络，保障公众“回得了家、出得了城、下得了乡”，畅行无忧。</p><p>对于高速公路服务区，《行动方案》还提出，每个服务区建设的充电基础设施或预留建设安装条件的车位原则上不低于小型客车停车位的10%。</p><p>北京特亿阳光新能源总裁祁海珅在接受《证券日报》记者采访时表示，加快建设充电设施不仅可以带动诸多产业投资，还可以配合电网构建高效率、智慧电力调度运行体系，即电力系统通过充电桩给电动汽车充电，在电网用电高峰时，电动汽车也可以通过充电桩放电给用户使用或输电给电网进行反向交易，对电网系统削峰填谷十分有利。</p><p>在《行动方案》明确的六项任务中，还特别提到要探索推进新技术新设备应用，包括支持电动汽车生产、大型运输等企业在服务区(站)建设布局换电站等。</p><p>祁海珅称，加快推进充电、换电领域新型基础设施建设，一方面能促进新能源汽车发展；另一方面也能促进构建以新能源为主体的新型电力系统建设。尤其是发展“分布式光伏发电+储能调节电网+充电桩”一体式的“光储充”新型电力基础设施，既可以补足充电桩数量不足的短板，还可以促进新能源电动车产业的发展。</p><p>前述负责人称，公路沿线充电基础设施的建设运营，将充分发挥市场在资源配置中的决定性作用，积极引导社会资本参与建设运营。通过采用合作经营等模式、签订长期合作协议、给予运营商合理的收入预期等方式，充分调动各方积极性，共同开展充电基础设施运营与维护，最大程度实现优势互补、合作共赢。(证券日报)</p>");
        ssNew1.setSource("中国新闻网");
        ssNew1.setLink("https://www.chinanews.com.cn/cj/2022/08-26/9837229.shtml");
        ssNew1.setAllList("[\n" +
                "                        \"8月25日，交通运输部发布消息称，交通运输部、国家能源局、国家电网有限公司、中国南方电网有限责任公司近日联合印发《加快推进公路沿线充电基础设施建设行动方案》(下称《行动方案》)，提出力争到2022年底前，全国除高寒高海拔以外区域的高速公路服务区能够提供基本充电服务。\",\n" +
                "                        \"一位接近系统内的人士告诉记者，从《行动方案》的内容来看，明确在今年年底前，要实现高速公路服务区大规模建设充电桩。这将大幅缓解电动汽车远行的“里程焦虑”，进而加快促进新能源汽车产业的发展。\",\n" +
                "                        \"据交通运输部公路局相关负责人介绍，截至目前，全国6618个高速公路服务区中，已有3102个服务区建成了13374个充电桩，基本满足了当前电动汽车充电要求。但与我国新能源汽车迅猛发展相比，目前高速公路充电基础设施设置总量不够、覆盖面不足等问题还较为突出，普通国省干线和农村公路充电基础设施建设工作还未全面起步，无法有效满足电动汽车远程出行的需求。\",\n" +
                "                        \"据相关部门统计，截至今年6月底，全国新能源汽车保有量已达到1001万辆。根据国家有关规划和预测，到2025年，全国新能源汽车新车保有量将超过2500万辆；到2030年将达到8000万辆。\",\n" +
                "                        \"为此，《行动方案》提出了三个阶段的工作目标：第一阶段，力争到2022年底前，全国除高寒高海拔以外区域的高速公路服务区能够提供基本充电服务；第二阶段，到2023年底前，具备条件的普通国省干线公路服务区(站)能够提供基本充电服务；第三阶段，到2025年底前，高速公路和普通国省干线公路服务区(站)充电基础设施进一步加密优化，农村公路沿线有效覆盖，基本形成“固定设施为主体，移动设施为补充，重要节点全覆盖，运行维护服务好，群众出行有保障”的公路沿线充电基础设施网络，保障公众“回得了家、出得了城、下得了乡”，畅行无忧。\",\n" +
                "                        \"对于高速公路服务区，《行动方案》还提出，每个服务区建设的充电基础设施或预留建设安装条件的车位原则上不低于小型客车停车位的10%。\",\n" +
                "                        \"北京特亿阳光新能源总裁祁海珅在接受《证券日报》记者采访时表示，加快建设充电设施不仅可以带动诸多产业投资，还可以配合电网构建高效率、智慧电力调度运行体系，即电力系统通过充电桩给电动汽车充电，在电网用电高峰时，电动汽车也可以通过充电桩放电给用户使用或输电给电网进行反向交易，对电网系统削峰填谷十分有利。\",\n" +
                "                        \"在《行动方案》明确的六项任务中，还特别提到要探索推进新技术新设备应用，包括支持电动汽车生产、大型运输等企业在服务区(站)建设布局换电站等。\",\n" +
                "                        \"祁海珅称，加快推进充电、换电领域新型基础设施建设，一方面能促进新能源汽车发展；另一方面也能促进构建以新能源为主体的新型电力系统建设。尤其是发展“分布式光伏发电+储能调节电网+充电桩”一体式的“光储充”新型电力基础设施，既可以补足充电桩数量不足的短板，还可以促进新能源电动车产业的发展。\",\n" +
                "                        \"前述负责人称，公路沿线充电基础设施的建设运营，将充分发挥市场在资源配置中的决定性作用，积极引导社会资本参与建设运营。通过采用合作经营等模式、签订长期合作协议、给予运营商合理的收入预期等方式，充分调动各方积极性，共同开展充电基础设施运营与维护，最大程度实现优势互补、合作共赢。(证券日报)\"\n" +
                "                    ]");
        ssNew1.setIsEnabled(true);
        ssNews.add(ssNew1);
        newsService.batchSaveSsNew(ssNews);
    }

    @Test
    void initNewsTest() throws Exception {
        String host = "https://ali-news.showapi.com";
        String path = "/newsList";
        String method = "GET";
        String appcode = "69e7814c7a60475781238354eafd0fd4";
        String appKey = "24735769";
        String appSecret = "ba1a894057a5d21690d745a2456db87f";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, String> query = new HashMap<>();
        query.put("needContent", "1");
        query.put("needDesc", "1");
        query.put("needHtml", "1");
        query.put("maxResult", "100");
        Map<String, SsNewsChannel> ssNewsChannelsMap = newsService.getSsNewsChannelsMap();
        for (int i = 4; i <= 100; i++) {
            query.put("page", Integer.toString(i));
            HttpResponse httpResponse = HttpUtils.doGet(host, path, "GET", headers, query);
            String str = JSON.parse(EntityUtils.toString(httpResponse.getEntity())).toString();
            JSONObject jsonObject = JSONUtil.parseObj(str);
            JSONObject showApiResBody = jsonObject.getJSONObject("showapi_res_body");
            JSONObject pageBean = showApiResBody.getJSONObject("pagebean");
            JSONArray contentList = pageBean.getJSONArray("contentlist");
            if (ObjectUtil.isNotEmpty(contentList)) {
                List<String> idList = contentList.stream().map(item -> {
                    JSONObject content = JSONUtil.parseObj(item);
                    return MapUtil.getStr(content, "id");
                }).collect(Collectors.toList());
                List<String> newsList = newsService.getNewSourceIds(idList);
                System.out.println(idList);
                if (newsList.size() == idList.size()) {
                    System.out.println("终止页码" + i);
//                    break;
                }
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                List<SsNew> ssNewList = contentList.stream().map(contentItem -> {
                    SsNew ssNew = new SsNew();
                    JSONObject content = JSONUtil.parseObj(contentItem);
                    List<String> imageUrls = new ArrayList<>();
                    String imgUrl = "";
                    if (content.getBool("havePic")) {
                        JSONArray imageurls = content.getJSONArray("imageurls");
                        String img = content.getStr("img");
                        try {
                            imgUrl = updateFromUrl(img);
                        } catch (IOException e) {
                            e.printStackTrace();
                            ssNew.setSourceId(content.getStr("id"));
                            ssNew.setIsEnabled(false);
                        }
                        for (Object imageurl : imageurls) {
                            JSONObject imageurlitem = JSONUtil.parseObj(imageurl);
                            String url = imageurlitem.getStr("url");
                            try {
                                imageUrls.add(updateFromUrl(url));
                            } catch (IOException e) {
                                e.printStackTrace();
                                ssNew.setSourceId(content.getStr("id"));
                                ssNew.setIsEnabled(false);
                            }
                        }
                    }
                    if (ObjectUtil.isEmpty(imageUrls) && content.getBool("havePic")) {
                        return ssNew;
                    }
                    ssNew = SsNew.builder()
                            .publishTime(DateUtils.getLocalDateTimeParse(content.getStr("pubDate"), dateTimeFormatter).atZone(ZoneId.systemDefault()).toInstant())
                            .channelId(ssNewsChannelsMap.get(content.getStr("channelId")).getId())
                            .title(content.getStr("title"))
                            .brief(content.getStr("desc"))
                            .sourceId(content.getStr("id"))
                            .content(content.getStr("content"))
                            .contentHtml(content.getStr("html"))
                            .source(content.getStr("source"))
                            .link(content.getStr("link"))
                            .allList(content.getStr("allList"))
                            .isEnabled(true)
                            .imageUrls(JSONUtil.toJsonStr(imageUrls))
                            .titleImageUrl(imgUrl)
                            .build();
                    return ssNew;
                }).collect(Collectors.toList());
                newsService.batchSaveSsNew(ssNewList);
            }
        }
    }

    @Test
    public void newsList() throws Exception {
        String host = "https://ali-news.showapi.com";
        String path = "/newsList";
        String method = "GET";
        String appcode = "69e7814c7a60475781238354eafd0fd4";
        String appKey = "24735769";
        String appSecret = "ba1a894057a5d21690d745a2456db87f";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, String> query = new HashMap<>();
        query.put("needContent", "1");
        query.put("needHtml", "1");
        query.put("maxResult", "100");
        Integer pageIndex = 1;
        while (true) {
            query.put("page", Integer.toString(pageIndex));
            HttpResponse httpResponse = HttpUtils.doGet(host, path, "GET", headers, query);
            String str = JSON.parse(EntityUtils.toString(httpResponse.getEntity())).toString();
            JSONObject jsonObject = JSONUtil.parseObj(str);
            JSONObject showApiResBody = jsonObject.getJSONObject("showapi_res_body");
            JSONObject pageBean = showApiResBody.getJSONObject("pagebean");
            JSONArray contentList = pageBean.getJSONArray("contentlist");
            if (ObjectUtil.isNotEmpty(contentList)) {
                List<String> isList = contentList.stream().map(i -> {
                    JSONObject content = JSONUtil.parseObj(i);
                    return MapUtil.getStr(content, "id");
                }).collect(Collectors.toList());
                newsService.getNewSourceIds(isList);
                break;
            }
        }
    }

    @Test
    void testInterface() throws Exception {
        String host = "https://ali-news.showapi.com";
        String path = "/newsList";
        String method = "GET";
        String appcode = "69e7814c7a60475781238354eafd0fd4";
        String appKey = "24735769";
        String appSecret = "ba1a894057a5d21690d745a2456db87f";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, String> query = new HashMap<>();
        query.put("needContent", "1");
        query.put("needHtml", "1");
        query.put("maxResult", "100");
        HttpResponse httpResponse = HttpUtils.doGet(host, path, "GET", headers, query);
        String str = JSON.parse(EntityUtils.toString(httpResponse.getEntity())).toString();
        JSONObject jsonObject = JSONUtil.parseObj(str);
        JSONObject showApiResBody = jsonObject.getJSONObject("showapi_res_body");
        JSONObject pageBean = showApiResBody.getJSONObject("pagebean");
        JSONArray contentList = pageBean.getJSONArray("contentlist");
    }

    public String updateFromUrl(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(3 * 1000);
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        InputStream inputStream = conn.getInputStream();
        byte[] getData = readInputStream(inputStream);
        String id = IdUtil.nanoId(8);
        ossClient.putObject("gs-file-src", "news/" + id + ".jpg", new ByteArrayInputStream(getData));
        return "news/" + id + ".jpg";
    }

    public byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    @Test
    void testOss() throws IOException {
        updateFromUrl("https://nimg.ws.126.net/?url=http%3A%2F%2Fcms-bucket.ws.126.net%2F2022%2F0826%2F4060b1dfp00rh7lbt003ec000h600anc.png&thumbnail=660x2147483647&quality=80&type=jpg");
    }

    @Test
    void testChannel() throws Exception {
        String host = "https://ali-news.showapi.com";
        String path = "/channelList";
        String method = "GET";
        String appcode = "69e7814c7a60475781238354eafd0fd4";
        String appKey = "24735769";
        String appSecret = "ba1a894057a5d21690d745a2456db87f";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, String> query = new HashMap<>();
        HttpResponse httpResponse = HttpUtils.doGet(host, path, "GET", headers, null);
        String str = JSON.parse(EntityUtils.toString(httpResponse.getEntity())).toString();
        JSONObject jsonObject = JSONUtil.parseObj(str);
        JSONObject showApiResBody = jsonObject.getJSONObject("showapi_res_body");
        JSONArray channelList = showApiResBody.getJSONArray("channelList");
        List<SsNewsChannel> ssNewsChannels = new ArrayList<>();
        for (Object channel : channelList) {
            JSONObject channelItem = JSONUtil.parseObj(channel);
            String name = channelItem.getStr("name");
            String channelNumber = channelItem.getStr("channelId");
            SsNewsChannel build = SsNewsChannel.builder().channelNumber(channelNumber).name(name).isEnabled(true).build();
            ssNewsChannels.add(build);
        }
        newsService.batchSaveSsNewsChannel(ssNewsChannels);
    }

    @Test
    void testProperty() {
        String str = "<p>北京8月29日电(记者 卞立群)28日，中甲联赛召开赛风赛纪专题工作会议，要求各俱乐部切实做好赛风赛纪工作，坚守职业足球底线。中足联筹备组组长史强表示，对于联赛中依旧可能存在“假赌黑”等丑恶现象，中国足协、中足联筹备组将联同相关部门长期保持高压打击态势。</p><p><img src='https://i2.chinanews.com.cn/simg/cmshd/2022/08/29/534105a10f7e4b0aa6739502ef0cf39b.jpg' /></p><p>会议现场。中甲联赛供图。</p><p>据了解，在“2022中甲联赛赛风赛纪工作会议”上，中足联筹备组副组长刘军传达了《中国足协深入开展赛风赛纪自查自纠工作方案》，要求严守职业底线，净化足球发展环境，在全行业内全面开展自查自纠工作，并明确了相关工作时间节点。</p><p>会议中，各中甲俱乐部负责人进行了交流发言。各俱乐部汇报本俱乐部赛风赛纪工作中的主要问题、下一步治理和整改措施。各俱乐部负责人均表示，已充分认识到严查赛风赛纪工作的重要性，将在接下来的比赛中，采取针对性措施，进一步加大管理力度，强化主体责任，杜绝赛风赛纪及“假赌黑”问题。</p><p><img src='https://i2.chinanews.com.cn/simg/cmshd/2022/08/29/e104fbe3fad24a489a47a8ac6bab3dfa.jpg' /></p><p>中足联筹备组组长史强。中甲联赛供图</p><p>中足联筹备组组长史强表示，近年来，职业联赛赛风赛纪方面暴露出一些问题，严重危害职业联赛发展。严肃赛风赛纪将是下一阶段工作的重中之重。作为联赛主体的俱乐部负责人、领队、主教练等管理人员，必须对自我行为严格要求，深入自查自纠，切实整改，绝不能有法不责众的思想。在接下来的联赛中，裁判作为执法者，必须执法更加严格、大胆管理。</p><p>史强表示，对于联赛中依旧可能存在“假赌黑”等丑恶现象，中国足协、中足联筹备组将严厉打击、绝不容忍，并将联同相关部门，采取有效措施，长期保持高压打击态势。</p><p><img src='https://i2.chinanews.com.cn/simg/cmshd/2022/08/29/c6fe548e47c046deab11c4d42fa4830f.jpg' /></p><p>会议现场。中甲联赛供图</p><p>会议现场，各中甲俱乐部联合签署《2022中国足球协会甲级联赛赛风赛纪和反兴奋剂诚信宣言》。宣言表示：将深刻认识做好赛风赛纪和反兴奋剂工作的重要性，将要求和责任传导至每一名运动员和教练员；发扬中华体育精神，尊重对手、尊重裁判、尊重观众、遵守规则，胜不骄、败不馁，充分展示中国职业足球的良好形象；绝不参加任何形式的赌博活动，绝不利用赛事活动内幕信息进行交易或牟利，绝不通过任何不正当手段操控比赛或者消极比赛；坚决防止任何损害体育形象的事情发生。</p><p>本赛季中甲联赛分四阶段以双循环赛制进行，第三阶段比赛即将于8月29日至9月29日在江苏南京、辽宁大连和河北唐山三个赛区进行。8月28日上午，中甲联赛组委会召集18家俱乐部教练员、球员、工作人员、各赛区工作人员以及全体比赛监督、裁判员等，就严肃赛风赛纪问题举行了第三阶段赛前线上宣讲。</p><p>中足联筹备组竞赛负责人郭炳颜传达了《中国足球协会关于进一步加强各类足球赛事赛风赛纪工作的通知》、《中国足协关于进一步落实2022中国足球职业联赛赛风赛纪会议要求的通知》，同时就严肃中甲赛事的赛风赛纪工作提出了具体要求。(完)</p><p><img src='https://i2.chinanews.com.cn/simg/cmshd/2022/08/27/8532405e5db74de89420be96e1cbfd69.jpg' /></p><p><img src='https://i2.chinanews.com.cn/simg/cmshd/2022/08/27/e4f771995e00468a8b4a47387bc63d05.jpg' /></p>";
        Document doc = Jsoup.parse(str);
        Elements p = doc.getElementsByTag("p");
        for (Element e : p) {
            if (ObjectUtil.isNotEmpty(e.childNodes().get(0).attributes().get("src"))) {
                System.out.println(e.childNodes().get(0).attributes().get("src"));
            }
        }
        for (Element e : p) {
            if (ObjectUtil.isNotEmpty(e.childNodes().get(0).attributes().get("src"))) {
                e.childNodes().get(0).clearAttributes();
                e.childNodes().get(0).attr("src", "11111");
            }
        }
        for (Element e : p) {
            if (ObjectUtil.isNotEmpty(e.childNodes().get(0).attributes().get("src"))) {
                System.out.println(e.childNodes().get(0).attributes().get("src"));
            }
        }
        System.out.println(p.toString());
    }

    @Test
    void testDoPost() {
        String getTokenUrl = "http://quote.youruitech.com/touxing/token";
        GetExpressTokenReq build = GetExpressTokenReq.builder().appkey("YR182842253200001").appsecret("6356ba4b-c309-4416-b60e-38edca6e9b65").devicetype(1).build();
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.add(HttpHeaders.ORIGIN, "http://quote.youruitech.com");
        HttpEntity<GetExpressTokenReq> doPostReqHttpEntity = new HttpEntity<>(build, headers);
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(getTokenUrl, doPostReqHttpEntity, String.class);
        System.out.println(stringResponseEntity);
        JSONObject jsonObject = JSONUtil.parseObj(stringResponseEntity.getBody());
        Integer code = jsonObject.getInt("code");
        if (ObjectUtil.equals(code, 0)) {
            String token = jsonObject.getStr("token");
            String getNewUrl = "http://quote.youruitech.com/quote/information_page";
            headers.add(HttpHeaders.AUTHORIZATION, token);
            List<SsExpress> ssExpresses = new ArrayList<>();
            for (int i = 20; i < 25; i++) {
                GetExpressReq getNewReq = GetExpressReq.builder().page(i).infoType(4).build();
                HttpEntity<GetExpressReq> getNewReqHttpEntity = new HttpEntity<>(getNewReq, headers);
                ResponseEntity<String> stringResponseEntity1 = restTemplate.postForEntity(getNewUrl, getNewReqHttpEntity, String.class);
                String data = JSONUtil.parseObj(stringResponseEntity1.getBody()).getStr("data");
                PostExpressResp expressResp = JSON.parseObject(data, PostExpressResp.class);
                String pattern = "yyyy-MM-dd HH:mm:ss";
                for (Object record : expressResp.getRecords()) {
                    JSONObject entries = JSONUtil.parseObj(record);
                    Long sourceId = entries.getLong("id");
                    String title = entries.getStr("information_title");
                    String releaseTime = entries.getStr("release_time");
                    String content = entries.getStr("abstract");
                    String category = entries.getStr("one_column");
                    String subCategory = entries.getStr("two_column");
                    SsExpress ssExpress = SsExpress.builder().sourceId(sourceId).title(title)
                            .releaseTime(DateUtils.formatterStringToInstant(releaseTime, pattern))
                            .content(content).category(category).subCategory(subCategory).isEnabled(true).build();
                    ssExpresses.add(ssExpress);
                }
            }
            ssExpressService.batchSaveSsExpress(ssExpresses);
        }
    }

    @Test
    void getNews() {
        String getTokenUrl = "http://quote.youruitech.com/touxing/token";
        GetExpressTokenReq build = GetExpressTokenReq.builder().appkey("YR182842253200001").appsecret("6356ba4b-c309-4416-b60e-38edca6e9b65").devicetype(1).build();
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.add(HttpHeaders.ORIGIN, "http://quote.youruitech.com");
        HttpEntity<GetExpressTokenReq> doPostReqHttpEntity = new HttpEntity<>(build, headers);
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(getTokenUrl, doPostReqHttpEntity, String.class);
        System.out.println(stringResponseEntity);
        JSONObject jsonObject = JSONUtil.parseObj(stringResponseEntity.getBody());
        Integer code = jsonObject.getInt("code");
        if (ObjectUtil.equals(code, 0)) {
            String token = jsonObject.getStr("token");
            String getNewUrl = "http://quote.youruitech.com/quote/information_page";
            String getNewsDetailUrl = "http://quote.youruitech.com/quote/detail";
            headers.add(HttpHeaders.AUTHORIZATION, token);
            for (int i = 1; i < 20; i++) {
                GetExpressReq getNewReq = GetExpressReq.builder().page(i).infoType(5).build();
                HttpEntity<GetExpressReq> getNewReqHttpEntity = new HttpEntity<>(getNewReq, headers);
                ResponseEntity<String> stringResponseEntity1 = restTemplate.postForEntity(getNewUrl, getNewReqHttpEntity, String.class);
                String data = JSONUtil.parseObj(stringResponseEntity1.getBody()).getStr("data");
                PostExpressResp expressResp = JSON.parseObject(data, PostExpressResp.class);
                String pattern = "yyyy-MM-dd HH:mm:ss";
                List<SsNew> ssNews = new ArrayList<>();
                for (Object record : expressResp.getRecords()) {
                    JSONObject entries = JSONUtil.parseObj(record);
                    String sourceId = entries.getStr("id");
                    String title = entries.getStr("title");
                    String releaseTime = entries.getStr("publ_date");
                    String codeType = entries.getStr("codeType");
                    String newsSourceAddress = entries.getStr("newsSourceAddress");
                    String media = entries.getStr("media");
                    String stockCode = entries.getStr("stockCode");
                    String pxChangeRate = entries.getStr("pxChangeRate");
                    String stockName = entries.getStr("stockName");
                    String companyCode = entries.getStr("companycode");
                    SsNew.SsNewBuilder ssNewBuilder = SsNew.builder().sourceId(sourceId).title(title).publishTime(DateUtils.formatterStringToInstant(releaseTime, pattern))
                            .codeType(codeType).link(newsSourceAddress).source(media).stockCode(stockCode).pxChangeRate(pxChangeRate)
                            .stockName(stockName).companyCode(companyCode).channelId(50L).isEnabled(true);
                    GetNewsDetailReq getNewsDetailReq = GetNewsDetailReq.builder().id(entries.getInt("id")).infoType(1).build();
                    HttpEntity<GetNewsDetailReq> getNewDetailReqHttpEntity = new HttpEntity<>(getNewsDetailReq, headers);
                    ResponseEntity<String> stringResponseEntity2 = restTemplate.postForEntity(getNewsDetailUrl, getNewDetailReqHttpEntity, String.class);
                    if (JSONUtil.parseObj(stringResponseEntity2.getBody()).getInt("code") == 200) {
                        String detailStr = JSONUtil.parseObj(stringResponseEntity2.getBody()).getStr("data");
                        JSONObject detail = JSONUtil.parseObj(detailStr);
                        String anAbstract = detail.getStr("abstract");
                        String content = detail.getStr("content");
                        Document doc = Jsoup.parse(detail.getStr("content"));
                        Elements p = doc.getElementsByTag("p");

                        try {
                            for (Element e : p) {
                                if (ObjectUtil.isNotEmpty(e.childNodes())) {
                                    for (Node node : e.childNodes()) {
                                        if (ObjectUtil.isNotEmpty(e.getElementsByTag("img"))) {
                                            String src = node.attributes().get("src").trim();
                                            node.attributes().remove("src");
                                            if (ObjectUtil.isNotEmpty(src)) {
                                                if (!src.startsWith("https:")) {
                                                    src = "https:" + src;
                                                }
                                                if (!src.endsWith("/")) {
                                                    src = src + "/";
                                                }
                                                System.out.println(src);
                                                System.out.println(sourceId);
                                                node.attr("src", newsConfigProperty.getConfig().getOssPath() + updateFromUrl(src));
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (IOException e) {
                            ssNewBuilder.isEnabled(false);
                        }
                        SsNew ssNew = ssNewBuilder.html(content).contentHtml(p.toString()).brief(anAbstract)
                                .contentHtml(doc.toString()).build();
                        System.out.println(ssNew);
                        ssNews.add(ssNew);
                    } else {
                        System.out.println("获取新闻详情失败," + getNewsDetailReq.getId());
                    }
                }
                newsService.batchSaveSsNew(ssNews);
            }
        }
    }

    @Test
    void testUpdateFromUrl() throws IOException {
        String s = updateFromUrl("https://comment.10jqka.com.cn/sourcepic/12/1qTIr9au0Mctsay.png");
        System.out.println(s);
    }
}
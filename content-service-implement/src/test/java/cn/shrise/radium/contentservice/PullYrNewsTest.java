package cn.shrise.radium.contentservice;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.contentservice.properties.NewsConfigProperty;
import cn.shrise.radium.contentservice.req.GetNewsDetailReq;
import com.aliyun.oss.OSS;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@SpringBootTest
public class PullYrNewsTest {

    @Autowired
    NewsConfigProperty newsConfigProperty;
    @Autowired
    OSS ossClient;
    @Autowired
    RestTemplate restTemplate;
    @Test
    public void testDetail() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.AUTHORIZATION, "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJZUjE4Mjg0MjI1MzIwMDAwMSIsImV4cCI6MTY4OTgzNDAzMSwiaWF0IjoxNjg5MjI5MjMxfQ.4J3XFDyKcX5vBIGpIgRfPK1VFSyHVejrPl-4cT1dIjSLVW4pv15MD3aArCpBr0NPhBJ8xu6ovKPiy5I2ShEvRA");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.add(HttpHeaders.ORIGIN, newsConfigProperty.getNewsExpressConfig().getOrigin());
        GetNewsDetailReq getNewsDetailReq = GetNewsDetailReq.builder().id(Integer.parseInt("4471220")).infoType(1).build();
        HttpEntity<GetNewsDetailReq> getNewDetailReqHttpEntity = new HttpEntity<>(getNewsDetailReq, headers);
        ResponseEntity<String> newsDetailResponseEntity = restTemplate.postForEntity(newsConfigProperty.getNewsExpressConfig().getDetailPath(), getNewDetailReqHttpEntity, String.class);
        if (JSONUtil.parseObj(newsDetailResponseEntity.getBody()).getInt("code") == 200) {
            String detailStr = JSONUtil.parseObj(newsDetailResponseEntity.getBody()).getStr("data");
            JSONObject detail = JSONUtil.parseObj(detailStr);
            String anAbstract = detail.getStr("abstract");
            String content = detail.getStr("content");
            Document doc = Jsoup.parse(content);
            Elements img = doc.getElementsByTag("img");

            for (Element item : img) {
                String src = item.attributes().get("src").trim();
                String srcTemp = item.attributes().get("src").trim();
                item.attributes().remove("src");
                if (ObjectUtil.isNotEmpty(src)) {
                    if (src.startsWith("//comment.")) {
                        src = "https:" + src;
                    } else if (src.startsWith("//e.thsi.cn")) {
                        src = "https:" + src;
                    }
                    if (!src.startsWith("https:")) {
                        continue;
                    }
                    try {
                        item.attr("src", newsConfigProperty.getConfig().getOssPath() + updateFromUrl(src));
                    } catch (IOException e) {
                        item.attr("src", srcTemp);
                    }
                }
            }
        } else {
            System.out.println("获取新闻详情失败," + getNewsDetailReq.getId());
        }

    }

    @Test
    public void testYr() {
        Document doc = Jsoup.parse("<meta charset=\\\"gbk\\\">\\n\\t\\t\\t</p><p><p>　　2023年2月8日，凌玮科技成功登陆深交所创业板，成为国内纳米二氧化硅企业佼佼者。从初创起步到行业龙头，从技术升级到打破外企垄断，从引领国内到走向世界，凌玮科技十年如一日，用坚持与创新，为中国新制造增添亮丽一笔。</p><p>　　<strong>0-1首次突破</strong></p><p>　　<strong>从初创起步到行业龙头</strong></p><p>　　起步之初，凌玮科技以生产二氧化硅主要原材料固体水玻璃为主营业务。由于固体水玻璃技术门槛低，国内市场竞争加剧，公司以超前的战略眼光看待企业发展，决心投身二氧化硅行业，建立硅化工产业链，向科技含金量高的精细品种发展。起初，采用传统沉淀法工艺，生产出首个产品“鸿盛”消光粉，实现从0-1的首次突破。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/ca936b41b6cca6ef\\\"id=\\\"14f3f60b7a5723fee6f328b0676681d0\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技“鸿盛”产品</strong></p><p>　　然而转型的路并不好走。在上世纪90年代，国内二氧化硅行业还是一片有待开发的粗放之地。当时并没有产品的质量标准，没有检验方法，更没有生产标准和流程。仅根据“行家”的说法进行生产，出品的质量参差不齐。</p><p>　　起初公司生产出来的产品因分散性不好，遭到客户的拒绝，公司受挫严重。但是融入骨子里的不屈精神，让公司上下无暇顾它，继续寻找解决的办法。几经探索，终于领悟问题关键之门，调整了生产的工艺参数和方式，解决了第一代产品分散性的问题，成功打入市场。继后，逐步形成了“鸿盛A系列”产品品牌，在二氧化硅行业打下了扎实的基础。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/c26529bf5ae51fa0\\\"id=\\\"91c89d110339269415a6df99728ba577\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/e51c21fbdb3db13f\\\"id=\\\"03cfe4ef6b459c045ae27dfeea9b55f9\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技技术研发</strong></p><p>　　如今，经过十几年的发展，国内市场变化翻天覆地。曾经红极一时的、设厂较早、采用沉淀法工艺的生产企业已经江河日下，市场份额所剩无几，在逐渐黯然退场。而凌玮科技后来者居上，经过市场的淬炼，已经发展成为国内消光用二氧化硅领域龙头企业。</p><p>　　正如凌玮人所言：弱肉强食，不进则退，是市场永恒的法则。所以唯有居安思危，不断进攻才是最好的防守。</p><p>　　<strong>0-1二次突破</strong></p><p>　　<strong>从技术升级到打破外企垄断</strong></p><p>　　随着社会的发展，应用领域产品需求不断升级，是进还是退，再次摆在台上。公司敏锐意识到这一发展趋势，提出必须要进一步提升工艺技术，生产具有自主知识产权的气凝胶产品，才能开创更为广阔的发展空间。</p><p>　　当时在二氧化硅气凝胶产品领域上，内有年产千吨级的同行厂家先入为主，外有世界级企业大幅垄断，公司站在战略高度，坚持一步到位把目标定在5千吨年产的规模，对手锚定在国外企业。这是2006年，公司又一次向新一轮从0到1发起挑战。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/e61d5bced311ab4\\\"id=\\\"f9f55374d0956073a8a12ba9823f8590\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/1f37f80120cbd6b7\\\"id=\\\"825d79cd20d7daf5a22d29ab545728bb\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技技术研发</strong></p><p>　　为了实现这一目标，公司加大培养技术人才的力度，成立气凝胶项目建设组，完成整个工程设计，开启了标准化的自主创新研发之路。然而在这条路上，并不平坦。由于前期技术不成熟及产品批次之间的不稳定，2008年自主生产的SA250产品，因消光性、透明性、分散性与市场上成熟的产品质量差距甚远，不被客户接受，此前艰难打开的一点市场全部断送。这对公司全员触动很大，决心举全力把品质搞上去。</p><p>　　经过不断摸索，甚至远道求贤，借鉴各种技术，2011年10月具有自主产权产品TSA250终于诞生，在消光性及透明性方面，媲美当时国内同行头部产品。这意味着公司攻克了该系列产品的关键技术。之后，迎来花开时刻。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/944109cbb62d66fc\\\"id=\\\"7f115681a2b106fe19ac7cdf8950c2be\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技荣誉</strong></p><p>　　先后成功研发经济型消光剂、吸附剂、高档消光剂等产品，拓展了产品线，同时满足经济型及高档市场需求。2013年更牵头制定了《消光用二氧化硅》行业标准，从此改写该行业曾经荒蛮的局面，并成长为中国纳米二氧化硅行业的头部企业，打破了国外企业在国内市场的垄断，收获了这一阶段的荣光。</p><p>　　<strong>0-1三次突破</strong></p><p>　　<strong>从引领国内走向世界</strong></p><p>　　时代在不断发展，技术在加速更迭焕新，凌玮科技主动适应变化，牢牢抓住技术革新的主线，加大对自主研发的投入，为纳米二氧化硅产业链发展提供坚厚的技术支持。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/e0fcdfd22eb003ec\\\"id=\\\"ac92982cdd09d64a44f0c92532bba7d5\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/f725ed3f2f1334d6\\\"id=\\\"933fb5ca9a6ea161c1b145ea52bd12b4\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技生产现场</strong></p><p>　　在产品上，通过自主研发，以消光剂及吸附剂为发展初始点，依据应用领域不断向高端产品拓展，推出高端开口剂、离子交换型二氧化硅环保防锈颜料等高端产品，不仅应用于石化、塑料、涂料等常规领域，更应用于光伏、环保及新能源等代表未来趋势的重要领域。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/1f79f88e01942b5\\\"id=\\\"88d2420116b7107a5f8f1e15b14fefe7\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技湖南冷水江三A生产基地</strong></p><p>　　在生产上，以湖南冷水江三A材料为始发点，向全国拓展，相继投建广州总部大楼和研发中心、安徽生产基地，形成集研发、生产、销售和技术服务为一体的新材料产业化集团。设立了博士后科研流动站协作研发中心及其创新创业实践基地等，实现学研产协同发展。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/bdb3f077905c536\\\"id=\\\"794ffb47986c336c3296ad21ad3cceb0\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技安徽马鞍山生产基地</strong></p><p>　　从量变到质变，凌玮科技十年如一日，在二氧化硅行业的路上，拥抱市场变化，坚持自主创新，以实干为第一要义，从0到1发起挑战。从传统领域到新材料领域，从行业追随者到行业领跑者，从引领国内走向世界前沿，凌玮科技走过了一段行业更迭茁壮的历程。以亲历者的角色，创下中国新制造民族品牌在纳米二氧化硅征程中的崛起之路。</p><p><img style=\\\"display:none;\\\" src=\\\"//e.thsi.cn/img/28aeb6cffad60796\\\"id=\\\"5201c3048911723b1a69bf10c10ae695\\\"/ onerror=\\\"javascript: this.parentNode.removeChild(this);\\\" onload=\\\"javascript:this.parentNode.className='img-pWrap';this.parentNode.style.textAlign='center';\\\"></p><p>　　<strong>▲凌玮科技董事长胡颖妮女士</strong></p><p>　　正如董事长胡颖妮女士所言：过去的十几年，因为有客户的信任与支持，凌玮人的团结一心，成就了今天的凌玮。相信下一个十年，我们将会迈过更多的里程碑，持续为行业做出贡献，提升纳米二氧化硅在全球行业的影响力。</p><strong></strong></p>\\n\\t\\t  <p class=\\\"bottomSign politics-hide\\\"><span>");
        Elements img = doc.getElementsByTag("img");


        for (Element item : img) {
            String src = item.attributes().get("src").trim();
            String srcTemp = item.attributes().get("src").trim();
            item.attributes().remove("src");
            if (ObjectUtil.isNotEmpty(src)) {
                if (src.startsWith("\\\"")) {
                    src = srcTemp.substring(2, srcTemp.length() - 2);
                }
                if (!src.startsWith("https:")) {
                    src = "https:" + src;
                }
                if (!src.endsWith("/")) {
                    src = src + "/";
                }
                try {
                    item.attr("src", newsConfigProperty.getConfig().getOssPath() + updateFromUrl(src));
                } catch (IOException e) {
                    System.out.println(e);
                    item.attr("src", srcTemp);
                }
            }
        }
        System.out.println(doc);

    }

    public String updateFromUrl(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(3 * 1000);
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        InputStream inputStream = conn.getInputStream();
        byte[] getData = readInputStream(inputStream);
        String id = IdUtil.nanoId(8);
        ossClient.putObject("gs-file-src", "news/" + id + ".jpg", new ByteArrayInputStream(getData));
        return "news/" + id + ".jpg";
    }

    public byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }
}

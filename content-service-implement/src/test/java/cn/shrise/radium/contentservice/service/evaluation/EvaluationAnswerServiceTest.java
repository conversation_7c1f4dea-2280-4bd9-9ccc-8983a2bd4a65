package cn.shrise.radium.contentservice.service.evaluation;

import cn.shrise.radium.contentservice.resp.EvaluationChoiceResp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class EvaluationAnswerServiceTest {


    @Autowired
    private EvaluationAnswerService evaluationAnswerService;

    @Test
    void getChoiceList() {
        Map<Long, EvaluationChoiceResp> choiceList = evaluationAnswerService.getChoiceList();
        System.out.println(choiceList);
    }
}
package cn.shrise.radium.contentservice.service;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.entity.SsNew;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class NewsServiceTest {

    @Autowired
    NewsService newsService;

    @Test
    void getNewsList() {
        PageResult<List<SsNew>> newsList = newsService.getNewsList(Arrays.asList("yr7-24"), 1, 10);
        System.out.println(newsList);
    }
}
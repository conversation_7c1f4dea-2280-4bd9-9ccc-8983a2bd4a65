package cn.shrise.radium.contentservice;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.common.properties.AccessKeyOtherProperties;
import cn.shrise.radium.contentservice.resp.AvatarInfoResp;
import com.aliyun.avatar20220130.Client;
import com.aliyun.avatar20220130.models.QueryAvatarListRequest;
import com.aliyun.avatar20220130.models.QueryAvatarListResponse;
import com.aliyun.avatar20220130.models.QueryAvatarListResponseBody;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest
public class AvatarTest {

    @Autowired
    AccessKeyOtherProperties accessKeyOtherProperties;

    @Test
    public void test() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId("LTAI5t9r3ZXwXr3zgDBkwcCq")
                .setAccessKeySecret("******************************");
        config.endpoint = "avatar.cn-zhangjiakou.aliyuncs.com";
        Client client = new Client(config);
        QueryAvatarListRequest request = new QueryAvatarListRequest();
        request.setModelType("2d");
        request.setTenantId(20192L);
        request.setPageNo(1);
        request.setPageSize(10);
        // Asynchronously get the return value of the API request
        QueryAvatarListResponse avatarListResponse = client.queryAvatarList(request);
        // Synchronously get the return value of the API request
        QueryAvatarListResponseBody body = avatarListResponse.getBody();
        List<AvatarInfoResp> respList = body.getData().getList().stream().map(info -> {
            AvatarInfoResp avatarInfoResp = new AvatarInfoResp();
            BeanUtil.copyProperties(info, avatarInfoResp);
            return avatarInfoResp;
        }).collect(Collectors.toList());

        System.out.println(body);
    }
}

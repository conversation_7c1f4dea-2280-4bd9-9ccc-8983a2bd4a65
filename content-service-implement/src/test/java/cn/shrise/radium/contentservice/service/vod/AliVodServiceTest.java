package cn.shrise.radium.contentservice.service.vod;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class AliVodServiceTest {

    @Autowired
    private AliVodService aliVodService;

    @Test
    void transformCategory() {
//        Optional<GetCategoriesResponse.Category> category = aliVodService.transformCategory(45, VodCategoryPathEnum.upload_short.name());
//        System.out.println(category);
    }
}
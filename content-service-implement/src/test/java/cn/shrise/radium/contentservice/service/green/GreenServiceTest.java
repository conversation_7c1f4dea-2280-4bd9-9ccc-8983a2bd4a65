package cn.shrise.radium.contentservice.service.green;

import cn.shrise.radium.contentservice.req.GreenTextScanReq;
import cn.shrise.radium.contentservice.req.TextScanTask;
import com.alibaba.fastjson.JSON;
import com.aliyun.green20220302.models.TextModerationResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.HttpResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class GreenServiceTest {

    @Autowired
    private GreenService greenService;

    @Test
    void test() throws ClientException {
        TextScanTask task1 = TextScanTask.builder()
                .dataId(UUID.randomUUID().toString())
                .content("感觉你们好有趣啊，不像我，不仅连句话都搭不上，还要被当成破坏氛围的傻狗，我现实生活中自闭没朋友，哪怕是在网上也受尽冷眼，每次组织了半天的语言都如鲠在喉，最后还是默默删掉了看你们互动，你有说有笑的样子不知道为什么在我眼里这么刺眼，融入不了群体的我，躲在屏幕后面默默哭出来了，所以今天是肯德基疯狂星期四有好心人请我吃吗？")
                .build();
        TextScanTask task2 = TextScanTask.builder()
                .dataId(UUID.randomUUID().toString())
                .content("本校小额贷款，安全、快捷、方便、无抵押，随机随贷，当天放款，上门服务。")
                .build();

//        HttpResponse httpResponse = greenService.requestTextScan("", Collections.singletonList("antispam"), Arrays.asList(task1, task2));
//        System.out.println(httpResponse.getHttpContentString());
    }

    @Test
    void requestTextModeration() throws Exception {
//        TextModerationResponse textModerationResponse = greenService.requestTextModeration("comment_detection", "感觉你们好有趣啊，不像我，不仅连句话都搭不上，还要被当成破坏氛围的，我现实生活中自闭没朋友，哪怕是在网上也受尽冷眼，每次组织了半天的语言都如鲠在喉，最后还是默默删掉了看你们互动，你有说有笑的样子不知道为什么在我眼里这么刺眼，融入不了群体的我，躲在屏幕后面默默哭出来了，所以今天是肯德基疯狂星期四有好心人请我吃吗？");
//        System.out.println(JSON.toJSONString(textModerationResponse));
    }

}
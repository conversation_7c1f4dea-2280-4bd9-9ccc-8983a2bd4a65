package cn.shrise.radium.contentservice;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.util.JsonUtils;
import cn.shrise.radium.contentservice.entity.SsShortVideoTag;
import cn.shrise.radium.contentservice.resp.AIVideoTagResultResp;
import cn.shrise.radium.contentservice.service.vod.AliVodService;
import cn.shrise.radium.contentservice.service.ShortVideoService;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.vod.model.v20170321.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest
public class VodTest {

    @Autowired
    private AliVodService aliVodService;

    @Autowired
    private ShortVideoService shortVideoService;

    @Test
    public void test() throws ClientException {
        List<GetCategoriesResponse.Category> companyCateList = aliVodService.getCompanyCateList(45, -1L);
        log.info("success");
    }

    @Test
    public void getVideoInfo(){
        GetVideoInfoResponse videoInfo = aliVodService.getVideoInfo("8bcfe05226a8498b936cc5c923ddec2e");
        System.out.println(videoInfo);
    }

    @Test
    void stsAuth() {
        String regionId = "cn-shanghai";
        String accessKeyId = "LTAI5tRpmoqAfxDpYY4LFB5K";
        String accessKeySecret = "******************************";
        String roleArn = "acs:ram::1952650058542185:role/voderole";
        String roleSessionName = "voderole";
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        //用profile构造client
        DefaultAcsClient client = new DefaultAcsClient(profile);
        // 创建一个 AssumeRoleRequest 并设置请求参数
        final AssumeRoleRequest request = new AssumeRoleRequest();
        request.setSysEndpoint("sts.aliyuncs.com");
        request.setSysMethod(MethodType.POST);
        request.setRoleArn(roleArn);
        request.setRoleSessionName(roleSessionName);
//        request.setPolicy(policy);
        // 发起请求，并得到response
        try {
            AssumeRoleResponse response = client.getAcsResponse(request);
        } catch (ClientException e) {
            e.printStackTrace();
        }
    }

    /**
     * 提交智能标签请求
     */
    @Test
    void submitAITagJob() {
        String regionId = "cn-shanghai";
        String accessKeyId = "LTAI5tRpmoqAfxDpYY4LFB5K";
        String accessKeySecret = "******************************";
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        //用profile构造client
        DefaultAcsClient client = new DefaultAcsClient(profile);

        SubmitAIJobRequest request = new SubmitAIJobRequest();
        // 设置视频ID
        request.setMediaId("5af40c26a4ff4cc0942c9fbb4208f67e");
        // 设置AI类型，请确保已开通该类型AI
        request.setTypes("AIVideoTag");
        // 返回结果
        try {
            SubmitAIJobResponse acsResponse = client.getAcsResponse(request);
            System.out.println(acsResponse.getRequestId());
            acsResponse.getAIJobList().stream().forEach(e->{
                System.out.println(e.getCode());
                System.out.println(e.getMessage());
                System.out.println(e.getJobId());
                System.out.println(e.getCreationTime());
                System.out.println(e.getData());
                System.out.println(e.getMediaId());
                System.out.println(e.getStatus());
                System.out.println(e.getType());
            });
            System.out.println("-----");
        } catch (ClientException e) {
            e.printStackTrace();
        }
        System.out.println("1");
    }

    /**
     * 获取智能标签结果
     */
    @Test
    void getAIVideoTag() {
        String regionId = "cn-shanghai";
        String accessKeyId = "LTAI5tRpmoqAfxDpYY4LFB5K";
        String accessKeySecret = "******************************";
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        //用profile构造client
        DefaultAcsClient client = new DefaultAcsClient(profile);
        //获取结果的两种方式，根据mediaId和jobId
        GetAIVideoTagResultRequest tagResultRequest = new GetAIVideoTagResultRequest();
        tagResultRequest.setMediaId("5af40c26a4ff4cc0942c9fbb4208f67e");

        ListAIJobRequest listAIJobRequest = new ListAIJobRequest();
        listAIJobRequest.setJobIds("082491cdd9b04a53ae27ffed2b194d72");
        // 返回结果
        try {
            GetAIVideoTagResultResponse acsResponse = client.getAcsResponse(tagResultRequest);
            GetAIVideoTagResultResponse.VideoTagResult videoTagResult = acsResponse.getVideoTagResult();
            ListAIJobResponse clientAcsResponse = client.getAcsResponse(listAIJobRequest);
            System.out.println("-----");
        } catch (ClientException e) {
            e.printStackTrace();
        }
        System.out.println("1");
    }

    /**
     * 处理标签
     */
    @Test
    void processAIVideoTag() {
        String tagInfo = "{\"Category\":[{\"Score\":0.518,\"Category\":\"花卉绿植\",\"Tag\":\"花\"},{\"Score\":0.438,\"Category\":\"花卉绿植\",\"Tag\":\"赏花\"}],\"Keyword\":[{\"Times\":[\"5960-7160\"],\"Tag\":\"人\"},{\"Times\":[\"360-12760\"],\"Tag\":\"植物\"},{\"Times\":[\"360-1960\",\"5560-10360\",\"11960-12760\"],\"Tag\":\"草\"},{\"Times\":[\"360-1960\",\"4760-12760\"],\"Tag\":\"草地\"}]}";
        AIVideoTagResultResp videoTagResult = JSON.parseObject(tagInfo, AIVideoTagResultResp.class);
        // 标签处理
        List<String> tags = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(videoTagResult.getCategory())){
            tags.addAll(videoTagResult.getCategory().stream().map(AIVideoTagResultResp.AIVideoTagCategoryItem::getTag).collect(Collectors.toList()));
        }
        if (ObjectUtil.isNotEmpty(videoTagResult.getPerson())){
            tags.addAll(videoTagResult.getPerson().stream().map(AIVideoTagResultResp.AIVideoTagResultItem::getTag).collect(Collectors.toList()));
        }
        if (ObjectUtil.isNotEmpty(videoTagResult.getLocation())){
            tags.addAll(videoTagResult.getLocation().stream().map(AIVideoTagResultResp.AIVideoTagResultItem::getTag).collect(Collectors.toList()));
        }
        String tagsJson = JsonUtils.toJson(tags.toArray());
        SsShortVideoTag videoTag = SsShortVideoTag.builder()
                .id(3L)
                .tags(tagsJson)
                .build();
        shortVideoService.updateShortVideoTag(videoTag);
    }
}

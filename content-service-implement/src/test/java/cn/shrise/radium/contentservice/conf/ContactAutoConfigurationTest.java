package cn.shrise.radium.contentservice.conf;

import cn.hutool.json.JSONUtil;
import cn.shrise.radium.contentservice.properties.ContractProperties;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/6/16, 星期一
 **/
@SpringBootTest
class ContactAutoConfigurationTest {

    @Autowired
    private ContractProperties contractProperties;

    @Test
    void test() {
        System.out.println(JSONUtil.toJsonStr(contractProperties));
    }


}
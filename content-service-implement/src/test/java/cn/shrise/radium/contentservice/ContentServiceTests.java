package cn.shrise.radium.contentservice;

import cn.shrise.radium.common.util.UrlUtil;
import cn.shrise.radium.contentservice.dao.SsContractAnalystRecordDao;
import cn.shrise.radium.contentservice.properties.WebOssConfigProperty;
import cn.shrise.radium.contentservice.req.CreateMaterialReq;
import cn.shrise.radium.contentservice.service.evaluation.EvaluationRecordService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@SpringBootTest
class ContentServiceTests {
    @Autowired
    WebOssConfigProperty properties;

    @Autowired
    EvaluationRecordService recordService;

    @Autowired
    SsContractAnalystRecordDao contractAnalystRecordDao;

    @Test
    void contextLoads() {
    }

    @Test
    void propertiesTest() {
        System.out.println(properties);
    }

    @Test
    void testEvaluationRedo() {
        recordService.evaluationRedo(3143, 240);
    }

    @Test
    public void getContent(){
        String contractAdvisorInfo = contractAnalystRecordDao.getContractAdvisorInfo(10);
        System.out.println(contractAdvisorInfo);
    }

    @Test
    void testMd5() {
        List<CreateMaterialReq.MaterialContent> contentList = new ArrayList<>();
        contentList.add(CreateMaterialReq.MaterialContent.builder().content("11111111").contentType(10).build());
        contentList.add(CreateMaterialReq.MaterialContent.builder().content("22222222").contentType(10).build());
        contentList.add(CreateMaterialReq.MaterialContent.builder().content("aaaaaaaa").contentType(20).url("/qqq/www/eee.jpg").build());
        String allText = contentList.stream()
                .filter(i -> Objects.equals(i.getContentType(), 10))
                .map(CreateMaterialReq.MaterialContent::getContent)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(""));
        String contentMd5 = UrlUtil.md5ByHex(allText);
    }
}

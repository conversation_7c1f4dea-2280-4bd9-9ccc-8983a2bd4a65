package cn.shrise.radium.contentservice.service.evaluation;

import cn.shrise.radium.contentservice.entity.SsSurveyTopic;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class ChoiceServiceTest {

    @Autowired
    private ChoiceService choiceService;

    ChoiceServiceTest(ChoiceService choiceService) {
        this.choiceService = choiceService;
    }

    @Test
    void getSurveyList() {
//        List<SsSurveyTopic> surveyList = choiceService.getSurveyList(9);
//        System.out.printf("ttttt", surveyList);
    }
}
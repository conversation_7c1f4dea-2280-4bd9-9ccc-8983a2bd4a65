package cn.shrise.radium.common.constant;

public class RocketMQConstant {

    public static final String TOPIC_WORK_WX = "workWxNotify";
    public static final String TOPIC_SMS = "smsNotify";
    public static final String TOPIC_WX = "wxNotify";
    //客服消息回调 顺序消息
    public static final String TOPIC_KF = "kfNotify";
    public static final String TOPIC_DOUYIN = "dyNotify";
    //会话存档回调 分区顺序消息
    public static final String TOPIC_CHAT = "msgAudit";
    //支付回调
    public static final String TOPIC_PAY = "payNotify";
    //开发票
    public static final String TOPIC_RECEIPT_OBTAIN = "wxReceiptNotify";
    //统计服务
    public static final String TOPIC_STATISTICS = "statisticsNotify";
    //e签宝签字
    public static final String TOPIC_ESIGN = "esignNotify";
    //通用延时消息topic
    public static final String TOPIC_COMMON_DELAY = "commonNotifyDelay";

    //用户服务
    public static final String TOPIC_USER = "userNotify";
    //直播室服务
    public static final String TOPIC_IM = "imNotify";
    //gateway服务
    public static final String TOPIC_GATEWAY = "gatewayNotify";
    //积分
    public static final String TOPIC_REWARD = "rewardNotify";

    //抖店回调
    public static final String TOPIC_DD = "ddNotify";
    // 行情gateway
    public static final String TOPIC_QUOTE_GATEWAY = "quoteGatewayNotify";
    //行情文件
    public static final String TOPIC_QUOTE_FILE = "quoteFileNotify";
    // vod回调
    public static final String TOPIC_VOD = "vodNotify";
    // 推送
    public static final String PUSH_TOPIC = "pushTopic";
    // 抖音
    public static final String DY_CALLBACK_TOPIC = "dyCallbackTopic";
    // 微盟
    public static final String WEI_MOB_TOPIC = "weimobTopic";
    // 头条人群包
    public static final String TOPIC_TT_PACKAGE = "ttPackageTopic";
    // 百度
    public static final String BD_CALLBACK_TOPIC = "bdCallbackTopic";
    // rpa
    public static final String RPA_TOPIC = "rpaTopic";
    // 智能投顾
    public static final String TOPIC_ROBO_ADVISER_GATEWAY = "roboAdviserGatewayTopic";

    // 数据同步
    public static final String TOPIC_DATA_SYNC = "dataSyncTopic";
    // 数据同步
    public static final String TOPIC_DATA_SYNC_MQ5 = "topic_data_sync";

    //腾讯
    public static final String TX_CALLBACK_TOPIC = "txCallbackTopic";
    //快手
    public static final String KS_CALLBACK_TOPIC = "ksCallbackTopic";
    //点证相关
    public static final String DZ_TOPIC = "dzTopic";
    //订单回访问卷
    public static final String ORDER_FEEDBACK_TOPIC = "orderFeedbackTopic";

    // 策略
    public static final String TOPIC_STRATEGY_TOPIC = "strategyTopic";
    // 交易
    public static final String TRADE_TOPIC = "tradeTopic";
    // 华为
    public static final String HW_CALLBACK_TOPIC = "hwCallbackTopic";
    // 云控
    public static final String WORKWX_ROBOT_GATEWAY_TOPIC = "workwxRobotGatewayTopic";
    //营销助手
    public static final String TOPIC_WORKWX_ROBOT = "workwxRobotTopic";
    //营销相关
    public static final String TOPIC_MARKETING = "marketingTopic";
    //钉钉相关
    public static final String TOPIC_DING_TALK = "dingTalkNotify";
    //钉钉相关 mq5
    public static final String TOPIC_DING_TALK_MQ5 = "topic_ding_talk";

    //开口回传
    public static final String GROUP_CONTACT_REPLY_CONVERT = "GID_wwxContactReplyConvert";
    //企业微信会话消息解密
    public static final String GROUP_CHAT_DECRYPT = "GID_chatDecrypt";
    //外部联系人消息回复/开口回传
    public static final String GROUP_CHAT_REPLY_MSG = "GID_chatReplyMsg";
    //语音消息翻译
    public static final String GROUP_CHAT_VOICE_TRANSFER = "GID_fileTrans";
    //会话存档
    public static final String GROUP_MSG_AUDIT = "GID_msgAudit";
    //会话图片存储Oss回调
    public static final String GROUP_CHAT_IMAGE_OSS_CALLBACK = "GID_chat_image_oss_callback";
    //企业微信会话消息
    public static final String GROUP_CHAT_MSG = "GID_chatMsg";
    //开发票
    public static final String GROUP_WX_RECEIPT_NOTIFY = "GID_wxReceiptNotify";
    //加好友回传
    public static final String GROUP_WWX_ADD_CONTACT_CONVERT = "GID_wwxAddContactConvert";
    //成交回传
    public static final String GROUP_DEALT_ORDER_CONVERT = "GID_dealtOrderConvert";
    //支付成功回传
    public static final String GROUP_PAID_PROMOTION_ORDER_CONVERT = "GID_paidPromotionOrderConvert";
    //外部联系人删员工回传
    public static final String GROUP_WWX_DEL_CONTACT_CONVERT = "GID_wwxDelContactConvert";
    //h5支付回传
    public static final String GROUP_H5_ORDER_CONVERT = "GID_h5OrderConvert";
    //公众号内支付回传
    public static final String GROUP_WX_ACCOUNT_ORDER_CONVERT = "GID_wxAccountOrderConvert";
    //打标签、发站内信
    public static final String GROUP_WWX_ADD_CONTACT_TAGS = "GID_wwxAddContactTags";
    //创建好友关系扩展信息
    public static final String GROUP_WWX_ADD_FRIEND_EXT = "GID_wwxAddFriendExt";
    //企业微信客服消息回调 顺序消息
    public static final String GROUP_KF_MSG_OR_EVENT = "GID_kfMsgOrEvent";
    //企业微信客服消息欢迎语
    public static final String GROUP_KF_WELCOME = "GID_kfWelcome";
    //企业微信客服消息自动回复
    public static final String GROUP_KF_AUTO_REPLY = "GID_kfAutoReply";
    //企业微信客服消息自动回复发送
    public static final String GROUP_KF_AUTO_REPLY_SEND = "GID_kfAutoReplySend";
    //企业微信客服消息临时素材下载
    public static final String GROUP_KF_MEDIA_DOWNLOAD = "GID_wwxMediaDownload";
    //企业微信客服回传
    public static final String GROUP_KF_CONVERT = "GID_kfConvert";
    //文件导出
    public static final String GROUP_EXPORT_EXCEL = "GID_exportStatisticsExcel";
    //腾讯云短信
    public static final String GROUP_TENCENT_SMS = "GID_tencentSmsNotify";
    //文嘻短信
    public static final String GROUP_WENXI_SMS = "GID_wenxiSmsNotify";
    //移动短信
    public static final String GROUP_MAS_SMS = "GID_masSmsNotify";
    //用户行为追踪绑定
    public static final String GROUP_BIND_WORKWX_USER = "GID_bindWorkWxUser";
    //esign回调
    public static final String GROUP_ESIGN = "GID_esign";
    //esign人脸识别回调
    public static final String GROUP_ESIGN_FACE_AUTH = "GID_esign_face_auth";
    //esign签字
    public static final String GROUP_ESIGN_ORDER_PROCESS = "GID_esign_order_process";
    //退款单签字
    public static final String GROUP_ESIGN_REFUND_PROCESS = "GID_esign_refund_process";
    //微信支付订单回调
    public static final String GROUP_WECHAT_PAY_ORDER_NOTIFY = "GID_wechatPayOrderNotify";
    //微信支付查询unionId
    public static final String GROUP_WECHAT_H5_PAY_ORDER_UNION_BIND_NOTIFY = "GID_wechatH5PayOrderUnionBindNotify";
    //微信支付退款回调
    public static final String GROUP_WECHAT_PAY_REFUND_NOTIFY = "GID_wechatPayRefundNotify";
    //微信支付营销订单退款回调
    public static final String GROUP_WECHAT_PAY_PROMOTION_REFUND_NOTIFY = "GID_wechatPayPromotionRefundNotify";
    /**
     * 营销订单自动退款
     */
    public static final String GROUP_PAYMENT_PROMOTION_ORDER_REFUND = "GID_payment_promotion_order_refund";
    //营销订单退款回调
    public static final String GROUP_PAYMENT_PROMOTION_REFUND_NOTIFY = "GID_payment_promotion_refund_notify";
    //支付宝支付订单回调
    public static final String GROUP_ALIPAY_ORDER_NOTIFY = "GID_alipayOrderNotify";
    //通联银联支付订单回调
    public static final String GROUP_UNION_PAY_ORDER_NOTIFY = "GID_unionpayOrderNotify";
    //订单延时关闭
    public static final String GROUP_ORDER_CLOSE = "GID_orderClose";
    //延期券延时关闭
    public static final String GROUP_DELAY_COUPON_CLOSE = "GID_delayCouponClose";
    //执行退款计划
    public static final String GROUP_ORDER_REFUND_PLAN = "GID_orderRefundPlan";
    //执行退款主订单更新
    public static final String GROUP_ORDER_REFUND_COMPLETE = "GID_orderRefundComplete";
    //直播室消息处理
    public static final String GROUP_IM_MSG_HANDLE = "GID_imMsgHandle";
    //直播室消息推送
    public static final String GROUP_IM_MSG_PUSH = "GID_imMsgPush";
    //积分变动
    public static final String GROUP_REWARD_CHANGE = "GID_rewardChange";
    //行情文件
    public static final String GROUP_QUOTE_FILE = "GID_quoteFile";
    //同步企业微信员工信息
    public static final String GROUP_WORK_WX_SYNC_USER = "GID_syncUser";
    //同步企业微信组织架构
    public static final String GROUP_WORK_WX_SYNC_ORGANIZATION = "GID_syncOrganization";
    //发布头条人群包
    public static final String GROUP_PUBLISH_TT_PACKAGE = "GID_pubTtPackage";
    //上传头条人群包（排除包）
    public static final String GROUP_UPLOAD_EXCLUDE_PACKAGE = "GID_uploadExcludePackage";

    //发布头条人群包
    public static final String GROUP_PUBLISH_TT_AUDIENCE_PACKAGE = "GID_audience_pubTtPackage";

    //客户行为
    public static final String GROUP_USER_BIND_WORKWX_SALES = "GID_userBindWorkwxSales";

    public static final String GROUP_VOD = "GID_vodVideo";
    // 聊天室app推送
    public static final String GID_CHAT_PUSH_APP = "GID_chat_push_app";
    // app推送
    public static final String GID_PUSH_APP = "GID_push_app";
    // 语音推送
    public static final String GID_PUSH_CALL = "GID_push_call";
    // 语音推送分片处理
    public static final String GID_PUSH_CALL_PROCESS = "GID_push_call_process";
    // 头条广告行为
    public static final String GID_DY_CALLBACK = "GID_dy_callback";
    // 抖音直播获客源
    public static final String GID_DY_SOURCE = "GID_dy_source";
    // 快手广告行为
    public static final String GID_KS_CALLBACK = "GID_ks_callback";
    // 发放微盟积分
    public static final String GID_SEND_WEI_MOB_POINT = "GID_send_wei_mob_point";

    // 抖店回调处理
    public static final String GID_DD_CALLBACK_HANDLE = "GID_dd_callback_handle";

    // 抖店订单处理
    public static final String GID_DD_ORDER_HANDLE = "GID_dd_order_handle";
    // 查询抖店发短信结果
    public static final String GID_DD_SEND_RESULT = "GID_dd_send_result";

    //发票回调处理
    public static final String GID_INVOICE_CALLBACK_HANDLE = "GID_invoice_callback_handle";

    //发票超时
    public static final String GID_INVOICE_TIMEOUT = "GID_invoice_timeout";

    /**
     * 百度广告行为
     */
    public static final String GID_BD_CALLBACK = "GID_bd_callback";

    // 创建极速群发消息任务
    public static final String GROUP_FAST_GROUP_SEND_TASK = "GID_group_fast_group_send_task";

    // 智能投顾
    public static final String GID_ROBO_ADVISER_GATEWAY = "GID_roboAdviserGateway";

    /**
     * 订单策略处理
     */
    public static final String GROUP_ORDER_STRATEGY_HANDLE = "GID_order_strategy_handle";

    /**
     * 重试策略处理
     */
    public static final String GROUP_RETRY_STRATEGY_HANDLE = "GID_retry_strategy_handle";

    /**
     * 策略增值包处理
     */
    public static final String GROUP_STRATEGY_APPLY_HANDLE = "GID_strategy_apply_handle";

    /**
     * 策略回测
     */
    public static final String GROUP_STRATEGY_BACK_TEST = "GID_strategy_back_test";

    /**
     * 策略模板消息
     */
    public static final String GROUP_STRATEGY_TEMPLATE = "GID_strategy_template";

    /**
     * 策略交易执行
     */
    public static final String GROUP_STRATEGY_TRADE_APPLY = "GID_strategy_trade_apply";

    /**
     * 同步客户访问事件
     */
    public static final String GROUP_SYNC_CUSTOMER_TRACK_EVENT = "GID_sync_customer_track_event";

    /**
     * 同步员工访问事件
     */
    public static final String GROUP_SYNC_STAFF_TRACK_EVENT = "GID_sync_staff_track_event";

    public static final String GROUP_SYNC_PUSH_RECORD = "GID_sync_push_record";

    public static final String GROUP_SYNC_SINGLE_CHAT = "GID_sync_single_chat";
    /**
     * 同步直播间停留时长统计
     */
    public static final String GROUP_DATA_SYNC_ROOM_SCENE_ONLINE_STATE = "GID_data_sync_room_scene_online_state";

    public static final String GROUP_SYNC_UNION_CONVERT_RECORD = "GID_sync_union_convert_record";

    public static final String GROUP_TX_CALLBACK = "GID_tx_callback";
    public static final String GROUP_TX_CALL_SEAT = "GID_tx_call_seat";

    public static final String GROUP_TX_CALL_RECORD_CALLBACK = "GID_tx_call_record_callback";

    // 策略体验券签字处理
    public static final String GROUP_ESIGN_STRATEGY_COUPON_PROCESS = "GID_esign_strategy_coupon_process";

    //智投增值包申请签字
    public static final String GROUP_ESIGN_STRATEGY_APPLY_PROCESS = "GID_esign_strategy_apply_process";

    // 服务体验券签字
    public static final String GROUP_ESIGN_VIP_COUPON_PROCESS = "GID_esign_vip_coupon_process";
    /**
     * 同步APP行为日志
     */
    public static final String GROUP_SYNC_APP_ACTION_LOG = "GID_sync_app_action_log";
    /**
     * 同步APP行为日志
     */
    public static final String GROUP_PROCESS_DZ_SUBSCRIBE = "GID_process_dz_subscribe";
    /**
     * 企微聊天记录查询处理
     */
    public static final String GROUP_WWX_CHAT_QUERY_PROCESS = "GID_wwx_chat_query_process";
    /**
     * 订单回访问卷PDF
     */
    public static final String GROUP_ORDER_FEEDBACK_PDF = "GID_order_feedback_pdf";

    /**
     * 策略股票池通知
     */
    public static final String GROUP_STRATEGY_STOCK_NOTIFY = "GID_strategy_stock_notify";

    /**
     * 策略股票池app通知
     */
    public static final String GID_STRATEGY_STOCK_PUSH_APP = "GID_strategy_stock_push_app";

    /**
     * 股票池成交处理
     */
    public static final String GROUP_RECOMMEND_STOCK_TRADE_PROCESS = "GID_recommend_stock_trade_process";
    /**
     * 股票池收益处理
     */
    public static final String GROUP_RECOMMEND_STOCK_PROFIT_PROCESS = "GID_recommend_stock_profit_process";
    /**
     * 股票池最大收益处理
     */
    public static final String GROUP_RECOMMEND_STOCK_MAX_PROFIT_PROCESS = "GID_recommend_stock_max_profit_process";
    /**
     * 云控消息发送
     */
    public static final String GROUP_WORKWX_ROBOT_MSG_SEND = "GID_workwx_robot_msg_send";

    /**
     * 云控获取client信息
     */
    public static final String GROUP_WWX_ROBOT_CLIENT_MSG = "GID_wwx_robot_client_msg";

    // 华为广告行为
    public static final String GID_HW_CALLBACK = "GID_hw_callback";

    /**
     * 用户标签标记
     */
    public static final String GID_UNION_TAG_MARK = "GID_union_tag_mark";
    /**
     * 用户标签分发
     */
    public static final String GID_UNION_TAG_BATCH = "GID_union_tag_batch";
    /**
     * 用户标签完成
     */
    public static final String GID_UNION_TAG_BATCH_FINISH = "GID_union_tag_batch_finish";
    /**
     * 用户添加好友后标记用户标签
     */
    public static final String GID_ADD_CONTACT_UNION_TAG_MARK = "GID_add_contact_union_tag_mark";
    /**
     * 百度广告授权
     */
    public static final String GID_BAIDU_AD_AUTH = "GID_baidu_ad_auth";

    /**
     * 推广服务订阅
     */
    public static final String GID_PROMOTION_SERVICE_SUBSCRIPTION = "GID_promotionServiceSubscription";
    /**
     * 用户添加好友后标记时间和广告链路标签
     */
    public static final String GID_ADD_CONTACT_TIME_AD_CHANNEL_TAG_MARK = "GID_add_contact_time_ad_channel_tag_mark";
    /**
     * 钉钉通知消息
     */
    public static final String GID_DING_TALK_NOTICE = "GID_ding_talk_notice";
    /**
     * 惩处单&业绩单钉钉通知消息
     */
    public static final String GID_DING_TALK_PENALTY_NOTICE = "GID_ding_talk_penalty_notice";

    //开口回传
    public static final String TAG_CONTACT_REPLY_CONVERT = "ad_convert_reply";
    //企业微信会话消息解密
    public static final String TAG_CHAT_DECRYPT = "chat_decrypt";
    //外部联系人消息回复/开口回传
    public static final String TAG_CHAT_REPLY_MSG = "chat_reply_msg";
    //语音消息翻译
    public static final String TAG_CHAT_VOICE_TRANSFER = "chat_voice_transfer";
    //企业微信会话消息
    public static final String TAG_CHAT_MSG = "chat_msg";
    //会话消息素材Oss回调
    public static final String TAG_CHAT_IMAGE_OSS_CALLBACK = "chat_image_oss_callback";
    //文件导出
    public static final String TAG_EXPORT_EXCEL = "export_statistics_excel";
    //加好友回传
    public static final String TAG_ADD_CONTACT_ACTION = "add_contact_action";
    //成交回传
    public static final String TAG_DEALT_ORDER_ACTION = "dealt_order_action";
    //广告单支付成功回传
    public static final String TAG_PAID_PROMOTION_ORDER_ACTION = "paid_promotion_order_action";
    //外部联系人删员工回传
    public static final String TAG_CONVERT_DELETE = "ad_convert_delete";
    //企业微信客服消息欢迎语
    public static final String TAG_KF_WELCOME = "kf_welcome";
    //企业微信客服消息自动回复
    public static final String TAG_KF_AUTO_REPLY = "kf_auto_reply";
    //企业微信客服消息自动回复发送
    public static final String TAG_KF_AUTO_REPLY_SEND = "kf_auto_reply_send";
    //企业微信客服消息临时素材下载
    public static final String TAG_KF_MEDIA_DOWNLOAD = "media_download";
    //企业微信客服回传
    public static final String TAG_KF_CONVERT = "kf_convert";
    //腾讯云短信
    public static final String TAG_TENCENT_SMS = "tencent";
    //文嘻短信
    public static final String TAG_WENXI_SMS = "wenxi";
    //移动短信
    public static final String TAG_MAS_SMS = "MAS";
    //用户行为追踪绑定
    public static final String TAG_BIND_WORKWX_USER = "wx_track_event";
    //esign回调
    public static final String TAG_ESIGN = "esign";
    //esign人脸识别回调
    public static final String TAG_ESIGN_FACE_AUTH = "esign_face_auth";
    //esign签字
    public static final String TAG_ESIGN_ORDER_PROCSS = "esign_order_process";
    //退款单签字
    public static final String TAG_ESIGN_REFUND_PROCSS = "esign_refund_process";

    //微信支付订单回调
    public static final String TAG_WECHAT_PAY_ORDER_NOTIFY = "wechat_pay_order_notify";
    //微信支付订单查询unionId
    public static final String TAG_WECHAT_H5_PAY_ORDER_UNION_BIND_NOTIFY = "wechat_pay_h5_order_union_bind_notify";
    //微信支付退款回调
    public static final String TAG_WECHAT_PAY_REFUND_NOTIFY = "wechat_pay_refund_notify";
    //微信支付营销订单退款回调
    public static final String TAG_WECHAT_PAY_PROMOTION_REFUND_NOTIFY = "wechat_pay_promotion_refund_notify";
    //支付宝订单回调
    public static final String TAG_ALIPAY_ORDER_NOTIFY = "alipay_order_notify";
    //银联支付订单回调
    public static final String TAG_UNION_PAY_ORDER_NOTIFY = "union_pay_order_notify";
    //订单延时关闭
    public static final String TAG_ORDER_CLOSE = "order_close";
    //延期券延时关闭
    public static final String TAG_DELAY_COUPON_CLOSE = "delay_coupon_close";
    // 发布头条人群包
    public static final String TAG_PUBLISH_TT_PACKAGE = "publish_tt_package";
    // 上传头条人群包（排除包）
    public static final String TAG_TT_UPLOAD_EXCLUDE_PACKAGE = "tt_upload_exclude_package";

    // 发布头条人群包
    public static final String TAG_PUBLISH_TT_AUDIENCE_PACKAGE = "publish_tt_audience_package";

    /**
     * sku状态延时修改
     */
    public static final String SKU_STATUS_EDIT = "sku_status_edit";

    /**
     * sku促销活动状态延时修改
     */
    public static final String SKU_ACTIVITY_STATUS_EDIT = "sku_activity_status_edit";

    //执行退款计划
    public static final String TAG_ORDER_REFUND_PLAN = "order_refund_plan";

    //退款主订单状态更新
    public static final String TAG_ORDER_REFUND_COMPLETE = "order_refund_complete";

    /**
     * 上传用户头像
     */
    public static final String TAG_UPLOAD_USER_AVATAR = "upload_user_avatar";
    /**
     * 直播室消息处理
     */
    public static final String TAG_IM_MSG_HANDLE = "im_msg_handle";
    /**
     * 直播室消息推送
     */
    public static final String TAG_IM_MSG_PUSH = "im_msg_push";

    /**
     * 兴趣标签处理
     */
    public static final String TAG_INTEREST_TAG_HANDLE = "interest_tag_handle";
    /**
     * 积分变动
     */
    public static final String TAG_REWARD_CHANGE = "reward_change";

    /**
     * 同步企业微信员工信息
     */
    public static final String TAG_WORK_WX_SYNC_USER = "sync_user";

    /**
     * 同步企业微信组织架构
     */
    public static final String TAG_WORK_WX_SYNC_ORGANIZATION = "sync_organization";

    /**
     * 积分变动
     */
    public static final String TAG_SCENE_STATISTICS = "scene_statistics";

    /**
     * 用户基于企业微信好友绑定销售
     */
    public static final String TAG_USER_BIND_WORKWX_SALES = "user_bind_workwx_sales";

    public static final String TAG_CHAT_PUSH_APP = "tag_chat_push_app";
    // app推送
    public static final String TAG_PUSH_APP = "tag_push_app";
    // 语音推送
    public static final String TAG_PUSH_CALL = "tag_push_call";
    // 语音推送分片处理
    public static final String TAG_PUSH_CALL_PROCESS = "tag_push_call_process";

    //直播室预告开始推送
    public static final String TAG_IM_PREHEAT_PUSH = "tag_im_preheat_push";
    // 头条广告行为
    public static final String TAG_DY_CALLBACK_EXPOSE = "tag_dy_callback_expose";
    // 头条广告点击
    public static final String TAG_DY_CALLBACK_CLICK = "tag_dy_callback_click";
    // 头条视频播放
    public static final String TAG_DY_CALLBACK_VIDEO_PLAYING = "tag_dy_callback_video_playing";
    // 头条视频播完
    public static final String TAG_DY_CALLBACK_VIDEO_PLAYED = "tag_dy_callback_video_played";
    // 头条视频有效播放
    public static final String TAG_DY_CALLBACK_VIDEO_ENABLED = "tag_dy_callback_video_enabled";
    // 发放微盟积分
    public static final String TAG_SEND_WEI_MOB_POINT = "tag_send_wei_mob_point";
    //抖音直播获客源
    public static final String TAG_DY_SOURCE = "tag_dy_source";

    // 快手广告点击
    public static final String TAG_KS_CALLBACK_CLICK = "tag_ks_callback_click";

    /**
     * 抖店回调处理
     */
    public static final String TAG_DD_CALLBACK_HANDLE = "tag_dd_callback_handle";

    /**
     * 抖店订单处理
     */
    public static final String TAG_DD_ORDER_HANDLE = "tag_dd_order_handle";

    /**
     * 抖店发短信
     */
    public static final String TAG_DD_SEND_RESULT = "tag_dd_send_result";

    /**
     * 发票超时
     */
    public static final String TAG_INVOICE_TIMEOUT = "tag_invoice_timeout";


    /**
     * 发票回调处理
     */
    public static final String TAG_INVOICE_CALLBACK_HANDLE = "tag_invoice_call_handle";

    /**
     * 百度广告点击
     */
    public static final String TAG_BD_CALLBACK_CLICK = "tag_bd_callback_click";

    /**
     * rpa 发送急速群发消息
     */
    public static final String TAG_RPA_FAST_GROUP_SEND = "tag_rpa_fast_group_send";

    // 智能投顾
    public static final String TAG_ROBO_ADVISER_GATEWAY = "robo_adviser_gateway";


    /**
     * 支付订单策略处理
     */
    public static final String TAG_ORDER_STRATEGY_HANDLE = "orderStrategyHandle";

    /**
     * 重试策略处理
     */
    public static final String TAG_RETRY_STRATEGY_HANDLE = "retryStrategyHandle";

    /**
     * 策略增值包处理
     */
    public static final String TAG_STRATEGY_APPLY_HANDLE = "strategyApplyHandle";

    /**
     * 同步客户访问事件
     */
    public static final String TAG_SYNC_CUSTOMER_TRACK_EVENT = "tag_sync_customer_track_event";

    /**
     * 同步员工访问事件
     */
    public static final String TAG_SYNC_STAFF_TRACK_EVENT = "tag_sync_staff_track_event";

    /**
     * 策略回测更新
     */
    public static final String TAG_STRATEGY_BACK_TEST = "tag_strategy_back_test";
    /**
     * 策略模板消息
     */
    public static final String TAG_STRATEGY_TEMPLATE = "tag_strategy_template";
    /**
     * 策略交易执行
     */
    public static final String TAG_STRATEGY_TRADE_APPLY = "tag_strategy_trade_apply";

    /**
     * 同步推送记录
     */
    public static final String TAG_SYNC_PUSH_RECORD = "tag_sync_push_record";

    /**
     * 同步单聊
     */
    public static final String TAG_SYNC_SINGLE_CHAT = "tag_sync_single_chat";
    /**
     * 同步直播间停留时长统计
     */
    public static final String TAG_DATA_SYNC_ROOM_SCENE_ONLINE_STATE = "tag_data_sync_room_scene_online_state";

    public static final String TAG_SYNC_UNION_CONVERT_RECORD = "sync_union_convert_record";

    /**
     * 同步腾讯回调
     */
    public static final String TAG_SYNC_TX_CALLBACK = "tag_sync_tx_callback";

    /**
     * 同步腾讯回调
     */
    public static final String TAG_TX_CALL_SEAT = "tag_tx_call_seat";

    /**
     * 腾讯云联络 通话录音回调
     */
    public static final String TAG_TX_CALL_RECORD_CALLBACK = "tag_tx_call_record_callback";

    /*
     * 断连模板消息
     * */
    public static final String TAG_DISCONNECT_TEMPLATE = "tag_disconnect_template";

    /*
     * 策略运行微信通知消息
     * */
    public static final String TAG_STRATEGY_WX_NOTIFY_TEMPLATE = "tag_strategy_wx_notify_template";

    /*
     * 断连模板消息消费组
     * */
    public static final String GROUP_DISCONNECT_TEMPLATE = "GID_disconnect_template";

    /*
     * 策略运行微信通知消费组
     * */
    public static final String GROUP_STRATEGY_WX_NOTIFY_TEMPLATE = "GID_strategy_wx_notify_template";


    /*
     * 策略体验券签字处理
     * */
    public static final String TAG_ESIGN_STRATEGY_COUPON_PROCESS = "tag_esign_strategy_coupon_process";


    /*
     * 策略增值包签字处理
     * */
    public static final String TAG_ESIGN_STRATEGY_APPLY_PROCESS = "tag_esign_strategy_apply_process";


    /*
     * 服务体验券签字处理
     * */
    public static final String TAG_ESIGN_VIP_COUPON_PROCESS = "tag_esign_vip_coupon_process";

    /**
     * 同步APP行为日志
     */
    public static final String TAG_SYNC_APP_ACTION_LOG = "tag_sync_app_action_log";
    /**
     * 企微聊天记录查询处理
     */
    public static final String TAG_WWX_CHAT_QUERY_PROCESS = "tag_wwx_chat_query_process";

    /**
     * 处理点证行情订阅
     */
    public static final String TAG_PROCESS_DZ_SUBSCRIBE = "tag_process_dz_subscribe";

    /**
     * 订单回访问卷PDF
     */
    public static final String TAG_ORDER_FEEDBACK_PDF = "tag_order_feedback_pdf";

    /**
     * 策略股票池通知
     */
    public static final String TAG_STRATEGY_STOCK_NOTIFY = "tag_strategy_stock_notify";

    /**
     * 策略股票池app推送
     */
    public static final String TAG_STRATEGY_STOCK_PUSH_APP = "tag_strategy_stock_push_app";
    /*
     * 支付回调
     * */
    public static final String TOPIC_PAYMENT = "paymentNotifyTopic";
    /*
     * 微信支付回调
     * */
    public static final String TAG_PAYMENT_WECHAT_ORDER_NOTIFY = "payment_wechat_order_notify";
    /*
     * 微信支付回调
     * */
    public static final String GROUP_PAYMENT_WECHAT_ORDER_NOTIFY = "GID_payment_wechat_order_notify";
    /*
     * 微信退款回调
     * */
    public static final String TAG_PAYMENT_WECHAT_REFUND_NOTIFY = "payment_wechat_refund_notify";
    /**
     * 营销订单自动退款
     */
    public static final String TAG_PAYMENT_PROMOTION_ORDER_REFUND = "tag_payment_promotion_order_refund";
    /*
     * 微信营销订单退款回调
     * */
    public static final String TAG_PAYMENT_PROMOTION_REFUND_NOTIFY = "tag_payment_promotion_refund_notify";
    /*
     * 微信退款回调
     * */
    public static final String GROUP_PAYMENT_WECHAT_REFUND_NOTIFY = "GID_payment_wechat_refund_notify";
    /*
     * 支付宝支付订单回调
     * */
    public static final String GROUP_PAYMENT_ALIPAY_ORDER_NOTIFY = "GID_payment_alipay_order_notify";
    /*
     * 支付宝订单回调
     * */
    public static final String TAG_PAYMENT_ALIPAY_ORDER_NOTIFY = "payment_alipay_order_notify";
    /*
     * 通联银联支付订单回调
     * */
    public static final String GROUP_PAYMENT_UNION_PAY_ORDER_NOTIFY = "GID_payment_union_pay_order_notify";
    /*
     * 银联支付订单回调
     * */
    public static final String TAG_PAYMENT_UNION_PAY_ORDER_NOTIFY = "payment_union_pay_order_notify";
    /*
     * 拉卡拉聚合扫码-交易通知
     * */
    public static final String TAG_PAYMENT_LKL_TRADE_NOTIFY = "payment_lkl_trade_notify";
    /*
     * 拉卡拉聚合扫码-交易通知
     * */
    public static final String GROUP_PAYMENT_LKL_TRADE_NOTIFY = "GID_payment_lkl_trade_notify";
    /*
     * 通联退款回调
     * */
    public static final String TAG_PAYMENT_ALLIN_REFUND_NOTIFY = "payment_allin_refund_notify";
    /*
     * 通联退款回调
     * */
    public static final String GROUP_PAYMENT_ALLIN_REFUND_NOTIFY = "GID_payment_allin_refund_notify";
    /*
     * 通联订单回调
     * */
    public static final String TAG_PAYMENT_ALLIN_PAY_NOTIFY = "payment_allin_pay_notify";
    /*
     * 通联订单回调
     * */
    public static final String GROUP_PAYMENT_ALLIN_PAY_NOTIFY = "GID_payment_allin_pay_notify";
    /*
     * 连连支付回调
     * */
    public static final String TAG_PAYMENT_LLIAN_ORDER_NOTIFY = "payment_llian_order_notify";
    /*
     * 连连支付回调
     * */
    public static final String GROUP_PAYMENT_LLIAN_ORDER_NOTIFY = "GID_payment_llian_order_notify";
    /*
     * 连连支付退款回调
     * */
    public static final String TAG_PAYMENT_LLIAN_REFUND_NOTIFY = "payment_llian_refund_notify";
    /*
     * 连连支付退款回调
     * */
    public static final String GROUP_PAYMENT_LLIAN_REFUND_NOTIFY = "GID_payment_llian_refund_notify";
    /**
     * 退款计划（新版）
     */
    public static final String GROUP_PAYMENT_REFUND_PLAN = "GID_payment_refund_plan";
    /**
     * 退款计划（新版）
     */
    public static final String TAG_PAYMENT_REFUND_PLAN = "payment_refund_plan";
    /**
     * 关闭订单（新版）
     */
    public static final String GROUP_PAYMENT_CLOSE_ORDER = "GID_payment_close_order";
    /**
     * 关闭订单（新版）
     */
    public static final String TAG_PAYMENT_CLOSE_ORDER = "payment_close_order";
    /**
     * 股票池成交处理
     */
    public static final String TAG_RECOMMEND_STOCK_TRADE_PROCESS = "tag_recommend_stock_trade_process";
    /**
     * 股票池收益处理
     */
    public static final String TAG_RECOMMEND_STOCK_PROFIT_PROCESS = "tag_recommend_stock_profit_process";
    /**
     * 股票池最大收益处理
     */
    public static final String TAG_RECOMMEND_STOCK_MAX_PROFIT_PROCESS = "tag_recommend_stock_max_profit_process";
    /**
     * 云控获取client信息
     */
    public static final String TAG_WORKWX_ROBOT_MSG_SEND = "tag_workwx_robot_msg_send";
    /**
     * 云控消息发送
     */
    public static final String TAG_WWX_ROBOT_CLIENT_MSG = "tag_wwx_robot_client_msg";

    // 华为广告点击
    public static final String TAG_HW_CALLBACK_CLICK = "tag_hw_callback_click";

    /*
     * 支付投诉回调
     * */
    public static final String TOPIC_COMPLAINT = "complaintNotify";
    /*
     * 微信原生
     * */
    public static final String GROUP_WX_ORDER_COMPLAINT_NOTIFY = "GID_wx_order_complaint_notify";
    /*
     * 微信原生
     * */
    public static final String TAG_WX_ORDER_COMPLAINT_NOTIFY = "wx_order_complaint_notify";
    /*
     * allinwx
     * */
    public static final String TAG_ALLIN_WX_ORDER_COMPLAINT_NOTIFY = "allin_wx_order_complaint_notify";
    /*
     * allinwx
     * */
    public static final String GROUP_ALLIN_WX_ORDER_COMPLAINT_NOTIFY = "GID_allin_wx_order_complaint_notify";

    /**
     * 抖店卡券激活
     */
    public static final String GROUP_DD_COUPON_ACTIVATE = "GID_dd_coupon_activate";

    /**
     * 抖店卡券激活
     */
    public static final String TAG_DD_COUPON_ACTIVATE = "dd_coupon_activate";

    /**
     * 单聊消息异步更新
     */
    public static final String GROUP_SYNC_SINGLE_CHAT_UPDATE = "GID_sync_single_chat_update";

    /**
     * 单聊消息异步更新
     */
    public static final String TAG_SYNC_SINGLE_CHAT_UPDATE = "tag_sync_single_chat_update";

    /**
     * 单聊消息腾讯大模型知识引擎审核
     */
    public static final String GROUP_SYNC_SINGLE_CHAT_TX_AUDIT = "GID_sync_single_chat_tx_audit";

    /**
     * 单聊消息腾讯大模型知识引擎审核
     */
    public static final String TAG_SYNC_SINGLE_CHAT_TX_AUDIT = "tag_sync_single_chat_tx_audit";

    /**
     * 更新订单自动审核
     */
    public static final String GROUP_UPDATE_ORDER_AUTO_AUDIT = "GID_update_order_auto_audit";

    /**
     * 更新订单自动审核
     */
    public static final String TAG_UPDATE_ORDER_AUTO_AUDIT = "tag_update_order_auto_audit";

    /**
     * 云控预约发送
     */
    public static final String GROUP_WORKWX_ROBOT_MSG_DELAY_SEND = "GID_workwx_robot_msg_delay_send";

    /**
     * 云控预约发送
     */
    public static final String TAG_WORKWX_ROBOT_MSG_DELAY_SEND = "tag_workwx_robot_msg_delay_send";

    /**
     * 用户标签标记
     */
    public static final String TAG_UNION_TAG_MARK = "tag_union_tag_mark";
    /**
     * 用户标签分发
     */
    public static final String TAG_UNION_TAG_BATCH = "tag_union_tag_batch";
    /**
     * 用户标签完成
     */
    public static final String TAG_UNION_TAG_BATCH_FINISH = "tag_union_tag_batch_finish";
    /**
     * 用户添加好友后标记用户标签
     */
    public static final String TAG_ADD_CONTACT_UNION_TAG_MARK = "tag_add_contact_union_tag_mark";
    /**
     * 百度广告授权
     */
    public static final String TAG_BAIDU_AD_AUTH = "tag_baidu_ad_auth";

    /**
     * 推广服务订阅
     */
    public static final String TAG_PROMOTION_SERVICE_SUBSCRIPTION = "tag_promotion_service_subscription";

    /**
     * 用户添加好友后标记时间和广告链路标签
     */
    public static final String TAG_ADD_CONTACT_TIME_AD_CHANNEL_TAG_MARK = "tag_add_contact_time_ad_channel_tag_mark";

    /**
     * 钉钉通知消息
     */
    public static final String TAG_DING_TALK_NOTICE = "tag_ding_talk_notice";

    /**
     * 惩处单&业绩单钉钉通知消息
     */
    public static final String TAG_DING_TALK_PENALTY_NOTICE = "tag_ding_talk_penalty_notice";


    /*
    * 钉钉通知消息5.0
    * */
    public static final String TOPIC_DING_DING_TALK = "topic_ding_talk";
    public static final String GROUP_DING_TALK_DEPT = "GID_ding_talk_dept";
    public static final String TAG_DING_TALK_DEPT_CREATE = "tag_ding_talk_dept_create";
    public static final String TAG_DING_TALK_DEPT_MODIFY = "tag_ding_talk_dept_modify";
    public static final String TAG_DING_TALK_DEPT_REMOVE = "tag_ding_talk_dept_remove";

    public static final String GROUP_DING_TALK_USER = "GID_ding_talk_user";
    public static final String TAG_DING_TALK_USER_ADD = "tag_ding_talk_user_add";
    public static final String TAG_DING_TALK_USER_MODIFY = "tag_ding_talk_user_modify";
    public static final String TAG_DING_TALK_USER_LEAVE = "tag_ding_talk_user_leave";
    public static final String TAG_DING_TALK_USER_ACTIVE = "tag_ding_talk_user_active";
    public static final String TAG_DING_TALK_USER_ADMIN_ADD = "tag_ding_talk_user_admin_add";
    public static final String TAG_DING_TALK_USER_ADMIN_REMOVE = "tag_ding_talk_user_admin_remove";
}

package cn.shrise.radium.common.conf;

import cn.shrise.radium.common.properties.AccessKeyOssProperties;
import cn.shrise.radium.common.properties.AccessKeyOtherProperties;
import cn.shrise.radium.common.properties.AliyunProperties;
import com.alibaba.cloud.context.AliCloudAuthorizationMode;
import com.alibaba.cloud.spring.boot.context.env.AliCloudProperties;
import com.alibaba.cloud.spring.boot.oss.env.OssProperties;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.util.Assert;

@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties({AliyunProperties.class, AccessKeyOtherProperties.class, AccessKeyOssProperties.class})
public class AliyunAutoConfiguration {

    private final AliyunProperties aliyunProperties;
    private final AccessKeyOtherProperties accessKeyOtherProperties;
    private final AccessKeyOssProperties accessKeyOssProperties;

    /**
     * 构造阿里云Common Api客户端
     *
     * @return client
     */
    @Bean
    public IAcsClient acsClient() {
        DefaultProfile defaultProfile = DefaultProfile.getProfile(
                aliyunProperties.getRegion(),
                accessKeyOtherProperties.getAccessKeyId(),
                accessKeyOtherProperties.getAccessKeySecret());
        return new DefaultAcsClient(defaultProfile);
    }

    @Bean
    @Order(0)
    public OSS ossClient() {
        return new OSSClientBuilder().build(accessKeyOssProperties.getEndpoint(),
                accessKeyOssProperties.getAccessKeyId(), accessKeyOssProperties.getAccessKeySecret(),
                new ClientBuilderConfiguration());
    }

}

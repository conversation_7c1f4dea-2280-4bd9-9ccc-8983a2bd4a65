package cn.shrise.radium.common.conf;

import brave.Tracing;
import cn.shrise.radium.common.annotation.OnsBatchConsumer;
import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.common.annotation.OnsOrderConsumer;
import cn.shrise.radium.common.ons.OnsBatchMessageListenerDecorator;
import cn.shrise.radium.common.ons.OnsConsumerBeanDecorator;
import cn.shrise.radium.common.ons.OnsMessageListenerDecorator;
import cn.shrise.radium.common.ons.OnsOrderMessageListenerDecorator;
import cn.shrise.radium.common.properties.AccessKeyOtherProperties;
import cn.shrise.radium.common.properties.RocketMQBinderConfigurationProperties;
import com.aliyun.ons20190214.Client;
import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.PropertyValueConst;
import com.aliyun.openservices.ons.api.batch.BatchMessageListener;
import com.aliyun.openservices.ons.api.bean.*;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.EmbeddedValueResolverAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.util.StringValueResolver;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties({RocketMQBinderConfigurationProperties.class, AccessKeyOtherProperties.class})
public class OnsAutoConfiguration implements ApplicationContextAware, SmartInitializingSingleton, EmbeddedValueResolverAware {

    private final RocketMQBinderConfigurationProperties rocketMQBinderProperties;
    private final AccessKeyOtherProperties accessKeyOtherProperties;

    private ConfigurableApplicationContext applicationContext;
    private StringValueResolver resolver;

    private final AtomicLong counter = new AtomicLong(0);
    private final Tracing tracing;

    public Properties getProperties() {
        Properties properties = new Properties();
        String nameServer = rocketMQBinderProperties.getNameServer().get(0);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKeyOtherProperties.getAccessKeyId());
        properties.setProperty(PropertyKeyConst.SecretKey, accessKeyOtherProperties.getAccessKeySecret());
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        //设置发送超时时间，单位毫秒。
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, "5000");
        return properties;
    }

    /**
     * 构造ons版的生产者
     *
     * @return Producer
     */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean buildProducer() {
        ProducerBean producer = new ProducerBean();
        producer.setProperties(getProperties());
        return producer;
    }

    /**
     * 构造ons版的顺序消息生产者
     *
     * @return Producer
     */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public OrderProducerBean buildOrderProducer() {
        OrderProducerBean producer = new OrderProducerBean();
        producer.setProperties(getProperties());
        return producer;
    }

    /**
     * 构造ons版的事务消息生产者
     *
     * @return Producer
     */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public TransactionProducerBean buildTransactionProducer() {
        TransactionProducerBean producer = new TransactionProducerBean();
        producer.setProperties(getProperties());
        return producer;
    }

    /**
     * 构建onsClient
     */
    @Bean
    @ConditionalOnMissingBean
    public Client onsClient() {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKeyOtherProperties.getAccessKeyId())
                .setAccessKeySecret(accessKeyOtherProperties.getAccessKeySecret())
                .setRegionId(rocketMQBinderProperties.getRegion());
        try {
            return new Client(config);
        } catch (Exception e) {
            log.info("create onsClient fail", e);
            throw new RuntimeException(e);
        }
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = (ConfigurableApplicationContext) applicationContext;
    }

    @Override
    public void setEmbeddedValueResolver(StringValueResolver resolver) {
        this.resolver = resolver;
    }

    @Override
    public void afterSingletonsInstantiated() {
        String nameServer = rocketMQBinderProperties.getNameServer().get(0);
        log.info("RocketMQ NameServer: {}", nameServer);
        Map<String, Object> consumerBeans = this.applicationContext.getBeansWithAnnotation(OnsConsumer.class);
        Map<String, Object> orderConsumerBeans = this.applicationContext.getBeansWithAnnotation(OnsOrderConsumer.class);
        Map<String, Object> batchConsumerBeans = this.applicationContext.getBeansWithAnnotation(OnsBatchConsumer.class);

        if (ObjectUtils.isNotEmpty(consumerBeans)) {
            consumerBeans.forEach(this::registerConsumer);
        }
        if (ObjectUtils.isNotEmpty(orderConsumerBeans)) {
            orderConsumerBeans.forEach(this::registerOrderConsumer);
        }
        if (ObjectUtils.isNotEmpty(batchConsumerBeans)) {
            batchConsumerBeans.forEach(this::registerBatchConsumer);
        }
    }

    private void registerConsumer(String beanName, Object bean) {
        Class<?> clazz = AopProxyUtils.ultimateTargetClass(bean);

        if (!MessageListener.class.isAssignableFrom(bean.getClass())) {
            throw new IllegalStateException(clazz + " is not instance of " + MessageListener.class.getName());
        }

        OnsConsumer annotation = clazz.getAnnotation(OnsConsumer.class);

        String containerBeanName = String.format("%s_%s_%s", "Container", clazz.getName(), counter.incrementAndGet());
        GenericApplicationContext genericApplicationContext = (GenericApplicationContext) applicationContext;

        genericApplicationContext.registerBean(containerBeanName, Consumer.class,
                () -> createOnsConsumerBean(bean, annotation), bd -> bd.setDestroyMethodName("shutdown"));
        Consumer container = genericApplicationContext.getBean(containerBeanName, Consumer.class);
        if (!container.isStarted()) {
            try {
                container.start();
            } catch (Exception e) {
                log.error("Started container failed. {}", container, e);
                throw new RuntimeException(e);
            }
        }
    }

    private void registerOrderConsumer(String beanName, Object bean) {
        Class<?> clazz = AopProxyUtils.ultimateTargetClass(bean);

        if (!MessageOrderListener.class.isAssignableFrom(bean.getClass())) {
            throw new IllegalStateException(clazz + " is not instance of " + MessageOrderListener.class.getName());
        }

        OnsOrderConsumer annotation = clazz.getAnnotation(OnsOrderConsumer.class);

        String containerBeanName = String.format("%s_%s_%s", "Container", clazz.getName(), counter.incrementAndGet());
        GenericApplicationContext genericApplicationContext = (GenericApplicationContext) applicationContext;

        genericApplicationContext.registerBean(containerBeanName, OrderConsumerBean.class,
                () -> createOnsOrderConsumerBean(bean, annotation), bd -> bd.setDestroyMethodName("shutdown"));
        OrderConsumerBean container = genericApplicationContext.getBean(containerBeanName, OrderConsumerBean.class);
        if (!container.isStarted()) {
            try {
                container.start();
            } catch (Exception e) {
                log.error("Started container failed. {}", container, e);
                throw new RuntimeException(e);
            }
        }

        log.info("Register order listener topic: {}, group: {}, selectorExpression: {}",
                annotation.topic(), annotation.consumerGroup(), annotation.selectorExpression());
    }

    private void registerBatchConsumer(String beanName, Object bean) {
        Class<?> clazz = AopProxyUtils.ultimateTargetClass(bean);

        if (!BatchMessageListener.class.isAssignableFrom(bean.getClass())) {
            throw new IllegalStateException(clazz + " is not instance of " + BatchMessageListener.class.getName());
        }

        OnsBatchConsumer annotation = clazz.getAnnotation(OnsBatchConsumer.class);

        String containerBeanName = String.format("%s_%s_%s", "Container", clazz.getName(), counter.incrementAndGet());
        GenericApplicationContext genericApplicationContext = (GenericApplicationContext) applicationContext;

        genericApplicationContext.registerBean(containerBeanName, BatchConsumerBean.class,
                () -> createOnsBatchConsumerBean(bean, annotation), bd -> bd.setDestroyMethodName("shutdown"));
        BatchConsumerBean container = genericApplicationContext.getBean(containerBeanName, BatchConsumerBean.class);
        if (!container.isStarted()) {
            try {
                container.start();
            } catch (Exception e) {
                log.error("Started container failed. {}", container, e);
                throw new RuntimeException(e);
            }
        }

        log.info("Register batch listener topic: {}, group: {}, selectorExpression: {}",
                annotation.topic(), annotation.consumerGroup(), annotation.selectorExpression());
    }

    /**
     * 普通消息消费者
     *
     * @param bean
     * @param annotation
     * @return consumerBean
     */
    private Consumer createOnsConsumerBean(Object bean, OnsConsumer annotation) {

        Properties properties = getProperties();
        String topic = resolver.resolveStringValue(annotation.topic());
        boolean dynamicConsumerGroup = annotation.dynamicConsumerGroup();
        String consumerGroup = resolver.resolveStringValue(annotation.consumerGroup());
        String selectorExpression = resolver.resolveStringValue(annotation.selectorExpression());
        String consumeThreadNums = resolver.resolveStringValue(annotation.consumeThreadNums());


        properties.setProperty(PropertyKeyConst.GROUP_ID, consumerGroup);
        properties.setProperty(PropertyKeyConst.MessageModel, annotation.messageModel());
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, consumeThreadNums);
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, annotation.maxReconsumeTimes());
        properties.setProperty(PropertyKeyConst.ConsumeTimeout, annotation.consumeTimeout());


        ConsumerBean consumerBean = new ConsumerBean();
        consumerBean.setProperties(properties);

        //订阅关系
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>(1);
        Subscription subscription = new Subscription();
        subscription.setTopic(topic);
        subscription.setType(annotation.selectorType().name());
        subscription.setExpression(selectorExpression);

        //add tracing
        OnsMessageListenerDecorator decorator = new OnsMessageListenerDecorator(tracing, (MessageListener) bean);
        subscriptionTable.put(subscription, decorator);
        consumerBean.setSubscriptionTable(subscriptionTable);
        OnsConsumerBeanDecorator consumerBeanDecorator = new OnsConsumerBeanDecorator(consumerGroup, rocketMQBinderProperties.getInstanceId(), consumerBean, dynamicConsumerGroup, onsClient());
        consumerBeanDecorator.start();

        log.info("Register listener topic: {}, group: {}, selectorExpression: {}",
                topic, consumerGroup, selectorExpression);
        return consumerBeanDecorator;
    }

    /**
     * 顺序消息消费者
     *
     * @param bean
     * @param annotation
     * @return
     */
    private OrderConsumerBean createOnsOrderConsumerBean(Object bean, OnsOrderConsumer annotation) {
        OrderConsumerBean orderConsumerBean = new OrderConsumerBean();
        final Properties properties = getProperties();
        String topic = resolver.resolveStringValue(annotation.topic());
        String consumerGroup = resolver.resolveStringValue(annotation.consumerGroup());
        String selectorExpression = resolver.resolveStringValue(annotation.selectorExpression());
        String consumeThreadNums = resolver.resolveStringValue(annotation.consumeThreadNums());

        properties.setProperty(PropertyKeyConst.GROUP_ID, consumerGroup);
        //顺序消息只能集群消费
        properties.setProperty(PropertyKeyConst.MessageModel, PropertyValueConst.CLUSTERING);
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, consumeThreadNums);
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, annotation.maxReconsumeTimes());
        properties.setProperty(PropertyKeyConst.ConsumeTimeout, annotation.consumeTimeout());
        properties.setProperty(PropertyKeyConst.ENABLE_ORDERLY_CONSUME_ACCELERATOR, String.valueOf(annotation.enableOrderlyConsumeAccelerator()));
        orderConsumerBean.setProperties(properties);

        Map<Subscription, MessageOrderListener> subscriptionTable = new HashMap<>(1);
        Subscription subscription = new Subscription();
        subscription.setTopic(topic);
        subscription.setType(annotation.selectorType().name());
        subscription.setExpression(selectorExpression);

        //add tracing
        OnsOrderMessageListenerDecorator decorator = new OnsOrderMessageListenerDecorator(tracing, (MessageOrderListener) bean);
        subscriptionTable.put(subscription, decorator);
        orderConsumerBean.setSubscriptionTable(subscriptionTable);
        orderConsumerBean.start();
        return orderConsumerBean;
    }

    /**
     * 批量消息消费者
     *
     * @param bean
     * @param annotation
     * @return
     */
    private BatchConsumerBean createOnsBatchConsumerBean(Object bean, OnsBatchConsumer annotation) {
        BatchConsumerBean batchConsumerBean = new BatchConsumerBean();
        final Properties properties = getProperties();
        String topic = resolver.resolveStringValue(annotation.topic());
        String consumerGroup = resolver.resolveStringValue(annotation.consumerGroup());
        String selectorExpression = resolver.resolveStringValue(annotation.selectorExpression());
        String consumeThreadNums = resolver.resolveStringValue(annotation.consumeThreadNums());

        properties.setProperty(PropertyKeyConst.GROUP_ID, consumerGroup);
        properties.setProperty(PropertyKeyConst.MessageModel, annotation.messageModel());
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, consumeThreadNums);
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, annotation.maxReconsumeTimes());
        properties.setProperty(PropertyKeyConst.ConsumeTimeout, annotation.consumeTimeout());
        properties.setProperty(PropertyKeyConst.ConsumeMessageBatchMaxSize, annotation.consumeMessageBatchMaxSize());
        properties.setProperty(PropertyKeyConst.BatchConsumeMaxAwaitDurationInSeconds, annotation.batchConsumeMaxAwaitDurationInSeconds());
        batchConsumerBean.setProperties(properties);

        Map<Subscription, BatchMessageListener> subscriptionTable = new HashMap<>(1);
        Subscription subscription = new Subscription();
        subscription.setTopic(topic);
        subscription.setType(annotation.selectorType().name());
        subscription.setExpression(selectorExpression);

        //add tracing
        OnsBatchMessageListenerDecorator decorator = new OnsBatchMessageListenerDecorator(tracing, (BatchMessageListener) bean);
        subscriptionTable.put(subscription, decorator);
        batchConsumerBean.setSubscriptionTable(subscriptionTable);
        batchConsumerBean.start();
        return batchConsumerBean;
    }

}

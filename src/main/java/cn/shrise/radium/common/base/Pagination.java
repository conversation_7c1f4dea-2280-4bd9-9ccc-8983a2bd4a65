package cn.shrise.radium.common.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

@ApiModel
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Pagination {

    private static final int defaultCurrent = 1;
    private static final int defaultSize = 20;
    public static final String DEFAULT_CURRENT = defaultCurrent + "";
    public static final String DEFAULT_SIZE = defaultSize + "";

    @ApiModelProperty(required = true, value = "当前页码")
    private Integer current;

    @ApiModelProperty(required = true, value = "每页条数")
    private Integer size;

    @ApiModelProperty(required = true, value = "总数")
    private Long total;

    public static Pagination of(Integer current, Integer size, Long total) {
        return new Pagination(current, size, total);
    }

    public static Pagination of(Integer current, Integer size) {
        return new Pagination(current, size, 0L);
    }

    public static Pagination of(Page<?> page) {
        return new Pagination(page.getNumber() + 1, page.getSize(), page.getTotalElements());
    }

    public static Pagination empty() {
        return new Pagination(defaultCurrent, defaultSize, 0L);
    }
}

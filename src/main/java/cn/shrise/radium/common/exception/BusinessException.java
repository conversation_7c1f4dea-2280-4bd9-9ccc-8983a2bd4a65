package cn.shrise.radium.common.exception;

import cn.shrise.radium.common.base.BaseError;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;

/**
 * 自定义业务异常类
 */
public class BusinessException extends RuntimeException {
    /**
     * 自定义错误码
     */
    private Integer code;

    public BusinessException() {
        super("Failure");
        this.code = BaseError.FAILURE_CODE;
    }

    public BusinessException(Integer code, String msg) {
        super(msg);
        this.code = code;
    }

    public BusinessException(String msg) {
        super(msg);
        this.code = BaseError.FAILURE_CODE;
    }

    public BusinessException(BaseResult<?> result) {
        super(result.getMsg());
        this.code = result.getCode();
    }

    public BusinessException(PageResult<?> result) {
        super(result.getMsg());
        this.code = result.getCode();
    }

    public BusinessException(BaseError error) {
        super(error.getMsg());
        this.code = error.getCode();
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}

package cn.shrise.radium.common.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@RefreshScope
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "version-limit")
@EnableConfigurationProperties
public class VersionLimitProperties {

    private Map<Integer, LimitVersion> config;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LimitVersion {
        private Boolean forceOut;
        private String min;
        private String max;
        private String name;
    }
}

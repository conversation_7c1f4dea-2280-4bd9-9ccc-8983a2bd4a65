package cn.shrise.radium.webapi.req.payment;

import cn.shrise.radium.webapi.constant.TradeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePaymentSubOrderReq {

    @NotNull(message = "订单编号不能为空")
    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNumber;

    @NotNull(message = "商户id不能为空")
    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    @NotNull(message = "支付金额不能为空")
    @Min(value = 0, message = "支付金额不能小于0")
    @ApiModelProperty(value = "支付金额 单位分", required = true)
    private Integer amount;

    @ApiModelProperty(value = "交易类型")
    private TradeTypeEnum tradeType;

    @ApiModelProperty(value = "支付后跳转页面")
    private String redirectUrl;

    @ApiModelProperty(value = "银行代码（银联支付）")
    private String gateId;

    @ApiModelProperty(value = "W:通联微信支付; A:通联支付宝支付", hidden = true)
    private String bnkId;

    @ApiModelProperty(value = "通联支付模式（H:JSAPI支付; I:小程序支付; J:主扫支付; L:H5支付; M:APP支付）")
    private String payMode;

    @ApiModelProperty(value = "pay_mode 为 H.JSAPI 时使用")
    private String frontUrl;

    @ApiModelProperty(value = "拉卡拉钱包类型（微信：WECHAT 支付宝：ALIPAY 银联：UQRCODEPAY 翼支付: BESTPAY 苏宁易付宝: SUNING 拉卡拉支付账户：LKLACC 网联小钱包：NUCSPAY）")
    private String lklAccountType;

    @ApiModelProperty(value = "拉卡拉接入方式（41:NATIVE（（ALIPAY，云闪付支持）51:JSAPI（微信公众号支付，支付宝服务窗支付，银联JS支付，翼支付JS支付、拉卡拉钱包支付）71:微信小程序支付）")
    private String lklTransType;

    @ApiModelProperty(value = "是否是小程序支付")
    private Boolean isMini = false;

    @ApiModelProperty(value = "小程序类型")
    private Integer accountType;

    @ApiModelProperty(value = "openId")
    private String openId;
}

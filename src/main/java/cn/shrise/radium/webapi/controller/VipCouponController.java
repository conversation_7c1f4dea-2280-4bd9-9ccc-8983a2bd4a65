package cn.shrise.radium.webapi.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.req.VipCouponInfoReq;
import cn.shrise.radium.orderservice.req.VipCouponTemplateResp;
import cn.shrise.radium.orderservice.resp.VipCouponInfoResp;
import cn.shrise.radium.orderservice.resp.VipCouponTemplateInfoResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * @Author: tangjiajun
 * @Date: 2024/9/4 14:19
 * @Desc:
 **/
@RestController
@RequestMapping("vip-coupon")
@RequiredArgsConstructor
public class VipCouponController {

    private final OrderClient orderClient;

    @PostMapping("apply")
    @ApiOperation("领取服务体验券")
    public BaseResult<VipCouponInfoResp> applyVipCoupon(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer customerId,
            @RequestBody @Validated VipCouponInfoReq req){
        return orderClient.applyVipCoupon(companyType, customerId, req);
    }

    @GetMapping("template/info")
    @ApiOperation("获取服务体验券模板详情")
    public BaseResult<VipCouponTemplateInfoResp> vipCouponTemplateInfo(@RequestParam @ApiParam("模板id") Long templateId){
        return orderClient.vipCouponTemplateInfo(templateId);
    }
}

package cn.shrise.radium.webapi.controller.roboadviser;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.resp.StrategyPassagewayStatisticsResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyPassagewayTransactionOrderResp;
import cn.shrise.radium.roboadviserservice.resp.StrategySimulateResp;
import cn.shrise.radium.roboadviserservice.resp.StrategySimulationRecordResp;
import cn.shrise.radium.webapi.service.CommonService;
import cn.shrise.radium.webapi.service.roboadviser.StrategySimulateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@RestController
@RequestMapping("strategy-simulate")
@RequiredArgsConstructor
public class StrategySimulateController {

    private final CommonService commonService;
    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final StrategySimulateService strategySimulateService;

    @ApiOperation("开始模拟实盘交易")
    @PostMapping("create")
    public BaseResult<Void> createStrategySimulate(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("策略code") String strategyCode,
            @RequestParam @ApiParam("通道金额") Long amount,
            @RequestParam @ApiParam("策略期限(单位：天)") Integer term) {
        String customerId = commonService.getCustomerIdByUserId(userId);
        return roboAdviserServiceClient.createStrategySimulate(userId, customerId, strategyCode, amount, term);
    }

    @ApiOperation("使用体验券开始模拟实盘交易")
    @PostMapping("coupon/create")
    public BaseResult<Void> createStrategySimulateUseCoupon(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("体验券编号") String number) {
        String customerId = commonService.getCustomerIdByUserId(userId);
        return roboAdviserServiceClient.createStrategySimulateUseCoupon(userId, customerId, number);
    }

    @ApiOperation("获取模拟实盘交易信息")
    @GetMapping("get")
    public BaseResult<StrategySimulateResp> getStrategySimulate(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId) {
        String customerId = commonService.getCustomerIdByUserId(userId);
        return strategySimulateService.getStrategySimulate(customerId);
    }

    @ApiOperation("终止模拟实盘交易")
    @PostMapping("terminate")
    public BaseResult<Void> terminateStrategySimulate(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId) {
        String customerId = commonService.getCustomerIdByUserId(userId);
        return roboAdviserServiceClient.terminateStrategySimulate(userId, customerId);
    }

    @ApiOperation("获取模拟实盘通道信息")
    @GetMapping("passageways")
    public BaseResult<List<StrategyPassagewayStatisticsResp>> getStrategySimulatePassageways(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId) {
        String customerId = commonService.getCustomerIdByUserId(userId);
        return strategySimulateService.getStrategySimulatePassageways(customerId);
    }

    @ApiOperation("获取模拟实盘持仓")
    @GetMapping("position")
    public BaseResult<List<StrategyPassagewayTransactionOrderResp>> getStrategySimulateTransactionOrder(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("是否平仓") Boolean isSell) {
        String customerId = commonService.getCustomerIdByUserId(userId);
        return roboAdviserServiceClient.getStrategySimulateTransactionOrder(customerId, isSell);
    }

    @ApiOperation("获取用户模拟实盘体验记录列表")
    @GetMapping("record/list")
    public PageResult<List<StrategySimulationRecordResp>> getUserStrategySimulateRecordList(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return roboAdviserServiceClient.getUserStrategySimulateRecordList(userId, current, size);
    }
}

package cn.shrise.radium.webapi.controller.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.webapi.req.CustomerComplaintReq;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;
import static cn.shrise.radium.common.constant.HeaderConstant.WX_ID;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("customer/complaint")
@RequiredArgsConstructor
public class CustomerComplaintController {

    private final ContentClient contentClient;

    @ApiOperation("客户投诉与建议")
    @PostMapping
    public BaseResult<Void> customerComplaint(
            @RequestBody @Valid CustomerComplaintReq req,
            @RequestHeader(value = WX_ID, required = false) @ApiIgnore Integer wxId,
            @RequestHeader(value = USER_ID, required = false) @ApiIgnore Integer userId) {
        if (ObjectUtil.isEmpty(wxId) && ObjectUtil.isEmpty(userId)){
            throw new BusinessException("参数错误");
        }
        return contentClient.createOrUpdate(null, wxId, userId, req.getContent(), req.getImages(), null, req.getSource(), null);
    }
}

package cn.shrise.radium.webapi.controller.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.orderservice.constant.InvoiceIdTypeConstant;
import cn.shrise.radium.orderservice.entity.RsInvoice;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcVerifyInfo;
import cn.shrise.radium.webapi.constant.ErrorConstant;
import cn.shrise.radium.webapi.req.ApplyOrderInvoiceReq;
import cn.shrise.radium.webapi.resp.order.GetInvoiceResp;
import cn.shrise.radium.webapi.service.CommonService;
import cn.shrise.radium.webapi.service.order.InvoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;
import static cn.shrise.radium.webapi.constant.ErrorConstant.VERIFY_INFO_NOT_EXIST;

@Api
@RestController
@RequestMapping("invoice")
@RequiredArgsConstructor
public class InvoiceController {

    private final InvoiceService invoiceService;
    private final CommonService commonService;
    private final UserClient userClient;

    @GetMapping("{orderNumber}")
    @ApiOperation("获取发票")
    public BaseResult<List<GetInvoiceResp>> getInvoice(@PathVariable @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        List<GetInvoiceResp> resp = invoiceService.getInvoice(orderId).orElseThrow(() -> new BusinessException(ErrorConstant.INVOICE_NOT_EXIST));
        return BaseResult.success(resp);
    }

    @PostMapping
    @ApiOperation("开发票")
    public BaseResult<String> applyOrderInvoice(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid ApplyOrderInvoiceReq req
    ) {
        Integer orderId = commonService.getOrderIdByNumber(req.getOrderNumber());
        req.setOrderId(orderId);
        UcVerifyInfo verifyInfo = userClient.getVerifyInfo(userId).orElse(null);
        if (ObjectUtil.isEmpty(verifyInfo)) {
            throw new BusinessException(VERIFY_INFO_NOT_EXIST);
        }
        req.setTitle(verifyInfo.getName());
        ErrorConstant errorConstant = invoiceService.applyOrderInvoice(req);
        return BaseResult.create(errorConstant);
    }

    @PostMapping("batch")
    @ApiOperation("批量获取发票")
    public BaseResult<Map<Integer, RsInvoice>> batchGetInvoice(@RequestBody @ApiParam("多个订单number") @Valid BatchReq<String> req) {
        List<Integer> orderIds = commonService.getBatchOrderIdByNumber(new ArrayList<>(req.getValues()));
        return invoiceService.batchGetInvoice(InvoiceIdTypeConstant.ORDER_ID, BatchReq.create(orderIds));
    }

}

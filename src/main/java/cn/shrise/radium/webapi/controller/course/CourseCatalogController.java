package cn.shrise.radium.webapi.controller.course;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.resp.CourseCatalogResp;
import cn.shrise.radium.webapi.annotation.RequiredPermission;
import cn.shrise.radium.webapi.entity.Permissions;
import cn.shrise.radium.webapi.resp.course.CoursePermissionResp;
import cn.shrise.radium.webapi.service.VipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;
import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.COURSE;


/**
 * <AUTHOR>
 */
@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("course/catalog")
public class CourseCatalogController {

    private final OrderClient orderClient;
    private final VipService vipService;

    @GetMapping("list")
    @ApiOperation("查询目录列表")
    @RequiredPermission(type = COURSE)
    public BaseResult<List<CourseCatalogResp>> getCatalogList(
            @RequestParam Integer companyType,
            @RequestParam String seriesNumber,
            @ApiIgnore @RequestHeader(value = USER_ID, required = false) Integer userId,
            @RequestParam(required = false) Boolean enabled,
            @ApiIgnore Permissions permissions){
        CoursePermissionResp resp = vipService.handleUserCoursePermission(companyType, userId, seriesNumber, permissions);
        return orderClient.getCatalogList(resp.getId(), enabled, resp.getVisible());
    }

    @GetMapping("updateVisitCount")
    @ApiOperation("编辑浏览量")
    public BaseResult<String> updateVisitCount(
            @RequestParam Integer id) {
        return orderClient.updateVisitCount(id);
    }
}

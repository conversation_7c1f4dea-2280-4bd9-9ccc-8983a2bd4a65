package cn.shrise.radium.webapi.resp.im;

import cn.shrise.radium.imservice.resp.ChatStreamMessageResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class WebChatStreamMessageResp {

    @ApiModelProperty("解盘消息")
    private StreamMessageInfo streamMessageInfo;

    @ApiModelProperty("老师名称")
    private String analystName;

    @ApiModelProperty("老师头像")
    private String analystUrl;

    @ApiModelProperty("发送人名称")
    private String creatorName;

    @ApiModelProperty("发送人头像")
    private String creatorUrl;

    @ApiModelProperty("证书编号")
    private String certificateNo;

    @ApiModelProperty("老师标签")
    private String title;

    @ApiModelProperty("心情")
    private List<ChatStreamMessageResp.MoodInfo> moodList;

    @ApiModelProperty("消息类型")
    private Integer messageType;

    @ApiModelProperty("案例股票池消息")
    private StockCaseInfo stockInfo;

    @ApiModelProperty("是否喜欢")
    private Boolean isLike;

    @ApiModelProperty("喜欢类型")
    private Integer likeType;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    public static class StreamMessageInfo {
        private Long id;
        private Instant gmtCreate;
        private Instant gmtModified;
        private Long chatId;
        private Integer messageType;
        private String content;
        private String imageUrl;
        private String linkUrl;
        private String audioUrl;
        private Double audioDuration;

        @ApiModelProperty(value = "视频id")
        private String videoId;

        @ApiModelProperty(value = "封面url")
        private String coverUrl;
        private List<String> imageList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    public static class StockCaseInfo {
        private Long id;
        private String labelCode;
        private String category;
        private Integer dealType;
        private Double price;
        private Integer count;
        private Integer endCount;
        private String reason;
        private String risk;
        private String tips;
        private Double priceUp;
        private Double priceDown;
        private Double takeProfitUp;
        private Double takeProfitDown;
        private Double stopLossUp;
        private Double stopLossDown;
        private Instant createTime;
    }
}

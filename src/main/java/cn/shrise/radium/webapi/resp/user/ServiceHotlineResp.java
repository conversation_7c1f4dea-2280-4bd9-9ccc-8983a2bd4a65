package cn.shrise.radium.webapi.resp.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceHotlineResp {

    @ApiModelProperty("服务热线")
    private String hotline;

    @ApiModelProperty("投诉与建议")
    private String suggest;
}

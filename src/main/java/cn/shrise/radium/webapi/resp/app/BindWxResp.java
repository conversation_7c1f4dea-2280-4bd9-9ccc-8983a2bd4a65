package cn.shrise.radium.webapi.resp.app;

import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.entity.RefreshToken;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class BindWxResp {

    @ApiModelProperty("token")
    private AccessToken accessToken;

    @ApiModelProperty("refresh token")
    private RefreshToken refreshToken;

    @ApiModelProperty("是否绑定wx")
    private Boolean isBind;

    @ApiModelProperty("wx信息")
    private UcWxExt wxInfo;

}

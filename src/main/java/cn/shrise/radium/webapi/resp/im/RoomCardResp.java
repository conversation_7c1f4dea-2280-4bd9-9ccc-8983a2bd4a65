package cn.shrise.radium.webapi.resp.im;

import cn.shrise.radium.webapi.resp.app.SceneCardResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoomCardResp {

    @ApiModelProperty("直播室信息")
    private SceneCardResp roomInfo;

    @ApiModelProperty("场次信息")
    private SceneBaseResp sceneInfo;
}

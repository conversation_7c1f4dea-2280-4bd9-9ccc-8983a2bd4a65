package cn.shrise.radium.webapi.service.order;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.BrTypeConstant;
import cn.shrise.radium.orderservice.constant.InvoiceStatusConstant;
import cn.shrise.radium.orderservice.constant.OrderStatusEnum;
import cn.shrise.radium.orderservice.constant.PayTypeConstantEnum;
import cn.shrise.radium.orderservice.entity.CourseSubOrder;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.orderservice.entity.RsInvoice;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.orderservice.req.CreateInvoiceReq;
import cn.shrise.radium.webapi.constant.ErrorConstant;
import cn.shrise.radium.webapi.properties.InvoiceProperties;
import cn.shrise.radium.webapi.req.ApplyOrderInvoiceReq;
import cn.shrise.radium.webapi.resp.order.GetInvoiceResp;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_RECEIPT_OBTAIN;
import static cn.shrise.radium.common.util.LockUtils.getApplyInvoiceLockKey;
import static cn.shrise.radium.orderservice.constant.ProductLevelEnum.STRATEGY;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class InvoiceService {

    private final OrderClient orderClient;
    private final InvoiceProperties invoiceProperties;
    private final RocketMqUtils rocketMqUtils;
    private final RedissonClient redissonClient;

    //300004：建行深圳福田保税区支行、300005：总部支付宝-花呗、300006：工商银行深圳深港科创支行
    List<Integer> invoiceAvailableList = Arrays.asList(1002, 300004, 300005, 901, 1001, 300006);

    public Optional<List<GetInvoiceResp>> getInvoice(Integer orderId) {
        BaseResult<List<RsInvoice>> getInvoiceListResult = orderClient.getInvoiceByOrderId(orderId);
        if (getInvoiceListResult.isFail()) {
            throw new BusinessException(getInvoiceListResult);
        }

        final List<RsInvoice> data = getInvoiceListResult.getData();
        if (ObjectUtils.isEmpty(data)) {
            return Optional.empty();
        }
        List<GetInvoiceResp> resultList = data.stream().map(i -> {
            GetInvoiceResp resp = new GetInvoiceResp();
            BeanUtils.copyProperties(i, resp);
            resp.setAmount(i.getTotalAmount());
            return resp;
        }).collect(Collectors.toList());
        return Optional.of(resultList);
    }

    public ErrorConstant applyOrderInvoice(ApplyOrderInvoiceReq req) {

        final Integer orderId = req.getOrderId();
        final String lockKey = getApplyInvoiceLockKey(orderId);

        RLock lock = redissonClient.getLock(lockKey);
        try {
            //按orderId锁，10s后自动释放锁
            boolean tryLock = lock.tryLock(5 * 100, 10 * 1000, TimeUnit.MILLISECONDS);
            if (tryLock) {
                return applyInvoice(req);
            } else {
                throw new BusinessException("获取锁失败");
            }
        } catch (InterruptedException e) {
            throw new BusinessException("获取锁失败");
        }
    }

    private ErrorConstant applyInvoice(ApplyOrderInvoiceReq req) {
        final Integer orderId = req.getOrderId();
        final BaseResult<RsCourseOrder> orderResult = orderClient.getOrder(orderId);
        final BaseResult<RsSku> skuBaseResult = orderClient.getSku(orderResult.getData().getSkuId());

        if (orderResult.isFail()) {
            throw new BusinessException(orderResult);
        }

        final RsCourseOrder order = orderResult.getData();
        if (ObjectUtil.isNotEmpty(order.getInvoiceStatus())) {
            return ErrorConstant.INVOICE_EXIST;
        }

        Set<Integer> mchTypeSet = orderClient.getSubOrderList(req.getOrderId(), OrderStatusEnum.PASSED.getValue()).getData().stream().map(CourseSubOrder::getMchType).collect(Collectors.toSet());
        if (!Collections.disjoint(mchTypeSet, invoiceAvailableList)) {
            return ErrorConstant.INVOICE_NOT_SUPPORT;
        }

        Integer amount = order.getAmount();
        Integer taxRate = null;
        String projects = null;
        if (ObjectUtil.equals(skuBaseResult.getData().getProductLevel(), STRATEGY.getValue())) {
            taxRate = invoiceProperties.getZtConfig().getRate();
            projects = invoiceProperties.getZtConfig().getProjects();
        } else {
            taxRate = invoiceProperties.getDefaultConfig().getRate();
            projects = invoiceProperties.getDefaultConfig().getProjects();
        }

        final int totalAmount = (int) Math.round(amount / ((100 + taxRate) / 100.0));
        final int totalTax = amount - totalAmount;

        CreateInvoiceReq createInvoiceReq = CreateInvoiceReq.builder()
                .orderId(orderId)
                .serialNumber(IdUtil.nanoId(15))
                .title(req.getTitle())
                .projects(projects)
                .invoiceType(req.getInvoiceType())
                .titleType(req.getTitleType())
                .status(InvoiceStatusConstant.PROCESSING)
                .brType(BrTypeConstant.BLUE)
                .totalTax(totalTax)
                .totalAmount(totalAmount)
                .build();

        BaseResult<RsInvoice> createInvoiceResult = orderClient.createInvoice(createInvoiceReq);
        if (createInvoiceResult.isFail()) {
            throw new BusinessException(createInvoiceResult);
        }
        orderClient.updateInvoiceStatus(orderId, InvoiceStatusConstant.PROCESSING);
        RsInvoice invoice = createInvoiceResult.getData();
        // 发消息
        rocketMqUtils.send(TOPIC_RECEIPT_OBTAIN, 10000, invoice);
        return ErrorConstant.SUCCESS;
    }

    private List<Integer> genInvoiceAmountList(Integer total) {
        List<Integer> res = new ArrayList<>();
        int max = 999900;
        for (int i = 0; i < total / max; i++) {
            res.add(max);
        }
        if (total % max > 0) {
            res.add(total % max);
        }
        return res;
    }


    public BaseResult<Map<Integer, RsInvoice>> batchGetInvoice(Integer idType, BatchReq<Integer> req) {
        return orderClient.batchGetInvoice(idType, req);
    }

}

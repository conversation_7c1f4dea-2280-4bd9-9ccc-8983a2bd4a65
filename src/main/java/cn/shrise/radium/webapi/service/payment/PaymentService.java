package cn.shrise.radium.webapi.service.payment;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.OrderStatusEnum;
import cn.shrise.radium.orderservice.constant.PayTypeConstant;
import cn.shrise.radium.orderservice.constant.WechatPayTradeTypeEnum;
import cn.shrise.radium.orderservice.constant.payment.PaymentChannelTypeEnum;
import cn.shrise.radium.orderservice.constant.payment.PaymentPlatformEnum;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.orderservice.entity.RsMerchantInfo;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.orderservice.req.CreateOrderDeliveryReq;
import cn.shrise.radium.orderservice.req.CreateOrderReq;
import cn.shrise.radium.orderservice.req.payment.*;
import cn.shrise.radium.orderservice.resp.*;
import cn.shrise.radium.orderservice.resp.allinpay.CreateAllinPaySubOrderResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUserAddress;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.webapi.constant.TradeTypeEnum;
import cn.shrise.radium.webapi.entity.BasicOrderInfo;
import cn.shrise.radium.webapi.entity.SubOrderInfo;
import cn.shrise.radium.webapi.req.CreateMainOrderReq;
import cn.shrise.radium.webapi.req.CreatePayableSubOrderReq;
import cn.shrise.radium.webapi.req.CreatePromotionOrderReq;
import cn.shrise.radium.webapi.req.payment.CreatePaymentSubOrderReq;
import cn.shrise.radium.webapi.resp.order.CreateMainOrderResp;
import cn.shrise.radium.webapi.resp.order.CreatePayableSubOrderResp;
import cn.shrise.radium.webapi.service.roboadviser.StrategyOrderLinkService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentService {

    private final OrderClient orderClient;
    private final UserClient userClient;
    private final StrategyOrderLinkService strategyOrderLinkService;

    public CreateMainOrderResp createMainOrder(Integer companyType, Integer userId, Integer wxId, CreateMainOrderReq req) {
        Integer skuId = req.getSkuId();
        RsSku sku = orderClient.getSku(skuId).orElseThrow(RecordNotExistedException::new);
        // 获取支付主体
        Long payCompanyId = null;
        if (!ObjectUtil.equals(sku.getIsPromotion(), true)) {
            payCompanyId = userClient.findOrUpdatePayCompany(userId).orElseThrow(() -> new BusinessException("资源出错，请稍候重试！"));
        }

        String salesNumber = req.getSalesNumber();
        UcUsers salesInfo = this.getSalesInfo(companyType, salesNumber);
        Integer salesId = salesInfo != null ? salesInfo.getId() : null;
        if (ObjectUtil.isNotEmpty(req.getStrategyLinkNumber())) {
            strategyOrderLinkService.checkStrategyLink(userId, req.getStrategyLinkNumber());
        }
        cn.shrise.radium.orderservice.req.payment.CreateMainOrderReq createOrderReq = cn.shrise.radium.orderservice.req.payment.CreateMainOrderReq.builder()
                .companyType(companyType)
                .userId(userId)
                .wxId(wxId)
                .skuId(skuId)
                .couponId(req.getCouponId())
                .activityId(req.getActivityId())
                .salesId(salesId)
                .amount(req.getAmount())
                .payCompanyId(payCompanyId)
                .strategyLinkNumber(req.getStrategyLinkNumber())
                .build();
        BaseResult<RsCourseOrder> createOrderResult = orderClient.createMainOrder(createOrderReq);
        if (createOrderResult.isFail()) {
            throw new BusinessException(createOrderResult);
        }
        RsCourseOrder mainOrder = createOrderResult.getData();
        if (ObjectUtil.isNotEmpty(req.getStrategyLinkNumber())) {
            orderClient.updateStrategyLinkOrder(req.getStrategyLinkNumber(), mainOrder.getId());
        }

        return CreateMainOrderResp.builder()
                .orderId(mainOrder.getId())
                .orderNumber(mainOrder.getOrderNumber())
                .orderStatus(mainOrder.getOrderStatus())
                .amount(mainOrder.getAmount())
                .discountAmount(mainOrder.getDiscountAmount())
                .skuId(mainOrder.getSkuId())
                .skuPrice(mainOrder.getSkuPrice())
                .salesId(mainOrder.getSalesId())
                .couponId(mainOrder.getCouponId())
                .expireTime(mainOrder.getExpireTime())
                .wxId(mainOrder.getWxId())
                .userId(mainOrder.getUserId())
                .build();
    }

    public CreateMainOrderResp createMainOrderNoAuth(CreateMainOrderReq req) {
        CreateOrderReq createOrderReq = CreateOrderReq.builder()
                .skuId(req.getSkuId())
                .couponId(req.getCouponId())
                .salesNumber(req.getSalesNumber())
                .amount(req.getAmount())
                .isSplit(true)
                .build();
        BaseResult<RsCourseOrder> createOrderResult = orderClient.createOrderNoAuth(createOrderReq);
        if (createOrderResult.isFail()) {
            throw new BusinessException(createOrderResult);
        }
        RsCourseOrder mainOrder = createOrderResult.getData();

        return CreateMainOrderResp.builder()
                .orderId(mainOrder.getId())
                .orderNumber(mainOrder.getOrderNumber())
                .orderStatus(mainOrder.getOrderStatus())
                .amount(mainOrder.getAmount())
                .discountAmount(mainOrder.getDiscountAmount())
                .skuId(mainOrder.getSkuId())
                .skuPrice(mainOrder.getSkuPrice())
                .salesId(mainOrder.getSalesId())
                .couponId(mainOrder.getCouponId())
                .expireTime(mainOrder.getExpireTime())
                .wxId(mainOrder.getWxId())
                .userId(mainOrder.getUserId())
                .build();
    }

    public CreatePayableSubOrderResp createPromotionOrder(Integer companyType, Integer userId, Integer wxId, String openId,
                                                          String referer, String clientIp, CreatePromotionOrderReq req, Integer accountType) {
        CreateMainOrderReq mainReq = CreateMainOrderReq.builder()
                .amount(req.getAmount())
                .couponId(req.getCouponId())
                .salesNumber(req.getSalesNumber())
                .skuId(req.getSkuId())
                .build();
        CreateMainOrderResp mainResp;
        if (ObjectUtil.isAllEmpty(userId, wxId)) {
            // 无需授权
            mainResp = createMainOrderNoAuth(mainReq);
        } else {
            mainResp = createMainOrder(companyType, userId, wxId, mainReq);
        }
        if (req.getIsReal()) {
            UcUserAddress ucUserAddress = userClient.findUserAddress(userId).orElse(new UcUserAddress());
            CreateOrderDeliveryReq deliveryReq = CreateOrderDeliveryReq.builder()
                    .userId(userId)
                    .orderId(mainResp.getOrderId())
                    .address(ucUserAddress.getAddress())
                    .name(ucUserAddress.getName())
                    .mobile(ucUserAddress.getMobile())
                    .mobileId(ucUserAddress.getMobileId())
                    .region(ucUserAddress.getRegion())
                    .build();
            orderClient.createDelivery(deliveryReq);
        }
        Long merchantId = orderClient.getPromotionPaymentType().getData();
        if (ObjectUtil.isNull(merchantId)) {
            throw new BusinessException("没有配置可用的商户号");
        }
        CreatePaymentSubOrderReq subReq = CreatePaymentSubOrderReq.builder()
                .orderNumber(mainResp.getOrderNumber())
                .merchantId(merchantId)
                .amount(req.getAmount())
                .tradeType(req.getTradeType())
                .redirectUrl(req.getRedirectUrl())
                .isMini(req.getIsMini())
                .accountType(accountType)
                .openId(openId)
                .build();
        return createPayableSubOrder(subReq, clientIp, referer);
    }

    public CreatePayableSubOrderResp createPayableSubOrder(CreatePaymentSubOrderReq req, String clientIp, String referer) {
        FullOrder fullOrder = orderClient.getFullOrder(req.getOrderNumber()).orElseThrow(() -> new BusinessException("主订单不存在"));
        RsCourseOrder mainOrder = fullOrder.getOrder();
        if (ObjectUtil.equals(mainOrder.getOrderStatus(), OrderStatusEnum.FROZEN.getValue())) {
            throw new BusinessException("主订单已冻结，不可再付款");
        }
        RsSku sku = fullOrder.getSku();
        // 查询商户信息
        RsMerchantInfo merchantInfo = orderClient.getMerchantInfo(req.getMerchantId()).orElseThrow(() -> new BusinessException("无效的商户号类型"));
        log.info("创建子订单 sku: {}, merchantId: {}, payAmount: {}", sku.getShowName(), merchantInfo.getId(), req.getAmount() / 100.0f);
        if (Objects.equals(merchantInfo.getPlatform(), PaymentPlatformEnum.PP_Wx.getCode())) {
            if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_Origin.getCode())) {
                //微信原生支付
                return this.createWxPaymentSubOrder(mainOrder, sku, clientIp, referer, req);
            }else if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_Allin.getCode())) {
                //通联微信支付
                return this.createAllinWxPaySubOrder(mainOrder, sku, clientIp, req);
            }else if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_Lkl.getCode())) {
                //拉卡拉微信支付
                return this.createLklWxPaySubOrder(mainOrder, sku, clientIp, req);
            } else if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_LLian.getCode())) {
                // 连连微信支付
                return this.createLLianPaySubOrder(mainOrder, sku, clientIp, req, true);
            }
        }else if (Objects.equals(merchantInfo.getPlatform(), PaymentPlatformEnum.PP_Ali.getCode())) {
            if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_Origin.getCode())) {
                //支付宝原生支付
                return this.createAlipaySubOrder(mainOrder, sku, clientIp, req);
            }else if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_Lkl.getCode())) {
                //拉卡拉支付宝支付
                return this.createLklAlipaySubOrder(mainOrder, sku, clientIp, req);
            } else if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_LLian.getCode())) {
                // 连连支付宝支付
                return this.createLLianPaySubOrder(mainOrder, sku, clientIp, req, false);
            }
        }else if (Objects.equals(merchantInfo.getPlatform(), PaymentPlatformEnum.PP_Union.getCode())) {
            //银联支付
            return this.createUnionPaySubOrder(mainOrder, sku, req);
        } else if (Objects.equals(merchantInfo.getPlatform(), PaymentPlatformEnum.PP_Online_Transfer.getCode())) {
            //大额转账
            return this.createOnlineTransferSubOrder(mainOrder, clientIp, req);
        } else {
            throw new BusinessException("暂不支持支付方式");
        }
        return null;
    }

    private WechatPayTradeTypeEnum transformTradeTypeToWechatPayTradeType(TradeTypeEnum tradeType) {
        if (Objects.equals(tradeType, TradeTypeEnum.JSAPI)) {
            return WechatPayTradeTypeEnum.JSAPI;
        } else if (Objects.equals(tradeType, TradeTypeEnum.WEB)) {
            return WechatPayTradeTypeEnum.MWEB;
        } else {
            throw new BusinessException("不支持的交易类型");
        }
    }

    private UcUsers getSalesInfo(Integer companyType, String salesNumber) {
        if (ObjectUtils.isEmpty(salesNumber)) {
            return null;
        }
        return userClient.getUser(companyType, salesNumber).orElseThrow();
    }

    /**
     * 创建微信原生支付子订单
     */
    private CreatePayableSubOrderResp createWxPaymentSubOrder(RsCourseOrder mainOrder, RsSku sku, String clientIp,
                                                              String referer, CreatePaymentSubOrderReq req) {
        TradeTypeEnum tradeType = req.getTradeType();
        WechatPayTradeTypeEnum wechatPayTradeType = this.transformTradeTypeToWechatPayTradeType(tradeType);
        CreateWxPaySubOrderReq createWechatPaySubOrderReq = CreateWxPaySubOrderReq.builder()
                .companyType(mainOrder.getCompanyType())
                .orderId(mainOrder.getId())
                .skuId(sku.getId())
                .skuShowName(sku.getShowName())
                .merchantId(req.getMerchantId())
                .amount(req.getAmount())
                .tradeType(wechatPayTradeType)
                .creatorIp(clientIp)
                .userId(mainOrder.getUserId())
                .wxId(mainOrder.getWxId())
                .h5Url(referer)
                .redirectUrl(req.getRedirectUrl())
                .isMini(req.getIsMini())
                .accountType(req.getAccountType())
                .openId(req.getOpenId())
                .build();
        CreateWechatPaySubOrderResp resp = orderClient.createWxPaySubOrder(createWechatPaySubOrderReq).orElseThrow();
        RsCourseOrder order = resp.getOrder();
        WechatPayInfoResp payInfo = resp.getPayInfo();
        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .payInfo(JSON.toJSON(payInfo))
                .build();
    }

    /**
     * 创建通联微信支付子订单
     */
    private CreatePayableSubOrderResp createAllinWxPaySubOrder(RsCourseOrder mainOrder, RsSku sku, String clientIp,
                                                               CreatePaymentSubOrderReq req) {
        Integer orderId = mainOrder.getId();
        Integer companyType = mainOrder.getCompanyType();
        Integer userId = mainOrder.getUserId();
        Integer wxId = mainOrder.getWxId();

        CreateAllinWxPaySubOrderReq createWechatPaySubOrderReq = CreateAllinWxPaySubOrderReq.builder()
                .orderId(orderId)
                .companyType(companyType)
                .skuId(sku.getId())
                .skuShowName(sku.getShowName())
                .merchantId(req.getMerchantId())
                .userId(userId)
                .wxId(wxId)
                .bnkId(req.getBnkId())
                .payMode(req.getPayMode())
                .amount(req.getAmount())
                .creatorIp(clientIp)
                .redirectUrl(req.getRedirectUrl())
                .frontUrl(req.getFrontUrl())
                .build();
        CreateAllinPaySubOrderResp resp = orderClient.createAllinWxPaySubOrder(createWechatPaySubOrderReq).orElseThrow();
        RsCourseOrder order = resp.getOrder();
        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .payInfo(resp.getPayInfo())
                .build();
    }

    /**
     * 创建支付宝原生支付子订单
     */
    private CreatePayableSubOrderResp createAlipaySubOrder(RsCourseOrder mainOrder, RsSku sku, String clientIp,
                                                           CreatePaymentSubOrderReq req) {
        Integer orderId = mainOrder.getId();
        TradeTypeEnum tradeType = req.getTradeType();
        if (!Objects.equals(tradeType, TradeTypeEnum.WEB)) {
            throw new BusinessException("该订单仅支持支付宝H5支付");
        }

        CreateAlipaySubOrderReq createAlipaySubOrderReq = CreateAlipaySubOrderReq.builder()
                .merchantId(req.getMerchantId())
                .orderId(orderId)
                .skuShowName(sku.getShowName())
                .amount(req.getAmount())
                .creatorIp(clientIp)
                .redirectUrl(req.getRedirectUrl())
                .build();
        CreateAlipaySubOrderResp resp = orderClient.createAlipayOriginSubOrder(createAlipaySubOrderReq).orElseThrow();
        AlipayInfoResp payInfo = resp.getPayInfo();
        RsCourseOrder order = resp.getOrder();

        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .payInfo(JSON.toJSON(payInfo))
                .build();
    }

    /**
     * 创建银联支付子订单
     */
    private CreatePayableSubOrderResp createUnionPaySubOrder(RsCourseOrder mainOrder, RsSku sku, CreatePaymentSubOrderReq req) {
        Integer orderId = mainOrder.getId();
        TradeTypeEnum tradeType = req.getTradeType();

        if (!Objects.equals(tradeType, TradeTypeEnum.WEB)) {
            throw new BusinessException("该订单仅支持H5支付");
        }

        CreateUnionPaySubOrderReq createUnionPaySubOrderReq = CreateUnionPaySubOrderReq.builder()
                .merchantId(req.getMerchantId())
                .orderId(orderId)
                .skuShowName(sku.getShowName())
                .amount(req.getAmount())
                .gateId(req.getGateId())
                .build();
        CreateUnionPaySubOrderResp resp = orderClient.createUnionPaySubOrderNew(createUnionPaySubOrderReq).orElseThrow();
        UnionPayInfoResp payInfo = resp.getPayInfo();
        RsCourseOrder order = resp.getOrder();

        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .payInfo(payInfo)
                .build();
    }

    /**
     * 创建拉卡拉微信支付子订单
     */
    private CreatePayableSubOrderResp createLklWxPaySubOrder(RsCourseOrder mainOrder, RsSku sku, String clientIp,
                                                             CreatePaymentSubOrderReq req) {
        Integer orderId = mainOrder.getId();
        Integer companyType = mainOrder.getCompanyType();
        Integer userId = mainOrder.getUserId();
        Integer wxId = mainOrder.getWxId();

        CreateLklWxPaySubOrderReq createLklPaySubOrderReq = CreateLklWxPaySubOrderReq.builder()
                .merchantId(req.getMerchantId())
                .companyType(companyType)
                .skuId(sku.getId())
                .lklAccountType(req.getLklAccountType())
                .transType(req.getLklTransType())
                .orderId(orderId)
                .userId(userId)
                .wxId(wxId)
                .amount(req.getAmount())
                .creatorIp(clientIp)
                .build();
        CreateLklPaySubOrderResp resp = orderClient.createLklWechatPaySubOrder(createLklPaySubOrderReq).orElseThrow();
        RsCourseOrder order = resp.getOrder();
        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .payInfo(resp.getPayInfo())
                .build();
    }

    /**
     * 创建拉卡拉支付宝支付子订单
     */
    private CreatePayableSubOrderResp createLklAlipaySubOrder(RsCourseOrder mainOrder, RsSku sku, String clientIp,
                                                              CreatePaymentSubOrderReq req) {
        Integer orderId = mainOrder.getId();

        CreateLklAlipaySubOrderReq createLklPaySubOrderReq = CreateLklAlipaySubOrderReq.builder()
                .merchantId(req.getMerchantId())
                .lklAccountType(req.getLklAccountType())
                .transType(req.getLklTransType())
                .orderId(orderId)
                .skuId(sku.getId())
                .skuShowName(sku.getShowName())
                .amount(req.getAmount())
                .creatorIp(clientIp)
                .build();
        CreateLklPaySubOrderResp resp = orderClient.createLklAlipaySubOrder(createLklPaySubOrderReq).orElseThrow();
        RsCourseOrder order = resp.getOrder();
        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .payInfo(resp.getPayInfo())
                .build();
    }

    /**
     * 创建连连微信支付子订单
     */
    private CreatePayableSubOrderResp createLLianPaySubOrder(RsCourseOrder mainOrder, RsSku sku, String clientIp,
                                                             CreatePaymentSubOrderReq req, boolean isWeChat) {
        Integer orderId = mainOrder.getId();
        Integer companyType = mainOrder.getCompanyType();
        Integer userId = mainOrder.getUserId();
        Integer wxId = mainOrder.getWxId();

        CreateLLianPaySubOrderReq lLianWxPaySubOrderReq = CreateLLianPaySubOrderReq.builder()
                .merchantId(req.getMerchantId())
                .companyType(companyType)
                .skuId(sku.getId())
                .orderId(orderId)
                .tradeType(req.getTradeType().name())
                .userId(userId)
                .wxId(wxId)
                .amount(req.getAmount())
                .creatorIp(clientIp)
                .skuShowName(sku.getShowName())
                .build();

        CreateLLianPaySubOrderResp resp;
        if (isWeChat) {
            resp = orderClient.createLLianWechatPaySubOrder(lLianWxPaySubOrderReq).orElseThrow();
        } else {
            resp = orderClient.createLLianAliPaySubOrder(lLianWxPaySubOrderReq).orElseThrow();
        }
        RsCourseOrder order = resp.getOrder();
        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .payInfo(resp.getPayInfo())
                .build();
    }

    /**
     * 创建大额转账子订单
     */
    private CreatePayableSubOrderResp createOnlineTransferSubOrder(RsCourseOrder mainOrder, String clientIp, CreatePaymentSubOrderReq req) {
        Integer orderId = mainOrder.getId();

        CreateLklOnlineTransferSubOrderReq lklOnlineTransferSubOrderReq = CreateLklOnlineTransferSubOrderReq.builder()
                .merchantId(req.getMerchantId())
                .orderId(orderId)
                .amount(req.getAmount())
                .creatorIp(clientIp)
                .build();

        CreateLklOnlineTransferSubOrderResp resp = orderClient.createLklOnlineTransferSubOrder(lklOnlineTransferSubOrderReq).orElseThrow();;
        RsCourseOrder order = resp.getOrder();
        return CreatePayableSubOrderResp.builder()
                .orderInfo(BasicOrderInfo.of(order))
                .subOrderInfo(SubOrderInfo.of(resp.getSubOrder()))
                .build();
    }
}

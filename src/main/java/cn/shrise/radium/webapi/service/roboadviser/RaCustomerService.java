package cn.shrise.radium.webapi.service.roboadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.entity.RaCustomerTradeAuth;
import cn.shrise.radium.roboadviserservice.entity.RaCustomerTradeAuthRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.webapi.resp.roboadviser.MyTradeAuthRecordResp;
import cn.shrise.radium.webapi.resp.roboadviser.MyTradeAuthResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RaCustomerService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final UserClient userClient;

    public PageResult<List<MyTradeAuthRecordResp>> getMyTradeAuthRecordList(Integer userId, Boolean isAuth, Integer current, Integer size) {
        PageResult<List<RaCustomerTradeAuthRecord>> pageResult = roboAdviserServiceClient.getRaCustomerTradeAuthRecordList(userId, isAuth, current, size);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<RaCustomerTradeAuthRecord> result = pageResult.getData();
        Set<Integer> userSet = result.stream().map(RaCustomerTradeAuthRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMapResult = userClient.batchGetUserMap(BatchReq.create(userSet)).orElse(new HashMap<>());
        List<MyTradeAuthRecordResp> respList = result.stream().map(i -> {
            MyTradeAuthRecordResp resp = new MyTradeAuthRecordResp();
            BeanUtils.copyProperties(i, resp);
            resp.setOperatorName(userMapResult.getOrDefault(i.getOperatorId(), new UcUsers()).getRealName());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, pageResult.getPagination());
    }

    public BaseResult<MyTradeAuthResp> getMyTradeAuthInfo(Integer userId) {
        List<RaCustomerTradeAuthRecord> records = roboAdviserServiceClient.getRaCustomerTradeAuthRecordList(userId, null, 1, 1).orElse(null);
        if (ObjectUtil.isEmpty(records)) {
            return BaseResult.success(null);
        }
        MyTradeAuthResp resp = MyTradeAuthResp.builder()
                .authTime(records.get(0).getGmtCreate())
                .isAuth(records.get(0).getIsAuth())
                .build();

        return BaseResult.success(resp);
    }

}

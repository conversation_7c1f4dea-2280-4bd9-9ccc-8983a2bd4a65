package cn.shrise.radium.webapi.service.trade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.ArticleSeriesTypeConstant;
import cn.shrise.radium.orderservice.entity.RsArticleStockRelation;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.properties.vip.UserSubscriptionServiceItem;
import cn.shrise.radium.orderservice.resp.ChatRoomConfigResp;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.quoteservice.resp.StockResp;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.constant.StockChannelType;
import cn.shrise.radium.tradeservice.dto.StockCaseDealDto;
import cn.shrise.radium.tradeservice.dto.StockCaseDto;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.resp.StrategyChannelResp;
import cn.shrise.radium.tradeservice.resp.StrategyStockCaseResp;
import cn.shrise.radium.tradeservice.resp.SubStockCaseCountResp;
import cn.shrise.radium.webapi.constant.ErrorConstant;
import cn.shrise.radium.webapi.entity.PermissionInfo;
import cn.shrise.radium.webapi.entity.Permissions;
import cn.shrise.radium.webapi.resp.content.AnalystInfoResp;
import cn.shrise.radium.webapi.resp.trade.*;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.POOL_CASE;
import static cn.shrise.radium.tradeservice.constant.StockCaseAuditStatusEnum.PASS;
import static cn.shrise.radium.tradeservice.constant.StockChannelType.SCT_QUANTIZE;
import static cn.shrise.radium.webapi.constant.ErrorConstant.SERVICE_NOT_OPENED;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockCaseService {

    private final TradeClient tradeClient;

    private final ContentClient contentClient;

    private final OrderClient orderClient;
    private final QuoteClient quoteClient;

    public PageResult<List<CaseNewsResp>> getCaseNewsPage(Integer userId, Integer companyType, Integer seriesId, LocalDate startTime, Instant openTime, Integer current, Integer size) {

        List<TdCaseSubInfo> subInfos = tradeClient.filterSubByUserId(userId).orElse(null);
        List<Long> subCaseIdList = subInfos.stream().map(TdCaseSubInfo::getCaseId).collect(Collectors.toList());

        Instant startInstant = null;
        if (ObjectUtil.isNotEmpty(startTime)) {
            startInstant = startTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        }
        PageResult<List<StockCaseDealDto>> result = tradeClient.getStockCaseNewsList(companyType, seriesId, startInstant, openTime, current, size);

        if (result.getData() == null || result.getData().size() == 0) {
            return PageResult.success(null, result.getPagination());
        }

        List<Integer> analystIds = new ArrayList<>();
        result.getData().forEach(i -> {
            TdStockCase caseInfo = i.getCaseInfo();
            analystIds.add(caseInfo.getAnalystId());
        });

        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, companyType).getData();

        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<CaseNewsResp> recordRespList = result.getData().stream()
                .map(i -> CaseNewsResp.of(i.getCaseInfo(), i.getDealInfo(), i.getChannelInfo(),
                        analystInfoMap.get(i.getCaseInfo().getAnalystId()), subCaseIdList.contains(i.getCaseInfo().getId())))
                .collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public PageResult<List<CaseNewsResp>> getCaseListPage(Integer companyType, Integer userId, Long channelId, Integer analystId, Boolean filterOpen,
                                                          Boolean filterClose, Boolean filterTop, Boolean checkOpen,
                                                          Integer checkOpenType, Instant caseStartTime,
                                                          Instant caseEndTime, Instant dealStartTime, Instant dealEndTime,
                                                          Instant openTime, Integer current, Integer size) {
        List<TdCaseSubInfo> subInfos = tradeClient.filterSubByUserId(userId).orElse(null);
        List<Long> subCaseIdList = subInfos.stream().map(TdCaseSubInfo::getCaseId).collect(Collectors.toList());

        PageResult<List<StockCaseDealDto>> result = tradeClient.getStockCaseList(companyType, channelId, filterOpen,
                filterClose, filterTop, checkOpen, checkOpenType, analystId, caseStartTime, caseEndTime,
                dealStartTime, dealEndTime, openTime, current, size);
        List<Integer> analystIds = result.getData().stream().map(i -> i.getCaseInfo().getAnalystId()).collect(Collectors.toList());

        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, companyType).getData();
        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<CaseNewsResp> recordRespList = result.getData().stream()
                .map(i -> CaseNewsResp.of(i.getCaseInfo(), i.getDealInfo(), i.getChannelInfo(),
                        analystInfoMap.get(i.getCaseInfo().getAnalystId()), subCaseIdList.contains(i.getCaseInfo().getId())))
                .collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public PageResult<List<CaseNewsResp>> getCaseDealListPage(Integer userId, Integer companyType, Long channelId, Long caseId,
                                                              Integer current, Integer size) {
        List<TdCaseSubInfo> subInfos = tradeClient.filterSubByUserId(userId).orElse(null);
        List<Long> subCaseIdList = subInfos.stream().map(TdCaseSubInfo::getCaseId).collect(Collectors.toList());
        PageResult<List<StockCaseDealDto>> result = tradeClient.getStockCaseDealList(companyType, channelId, caseId,
                null, null, PASS.getCode(), null, null, null, null, current, size);
        List<Integer> analystIds = result.getData().stream().map(i -> i.getCaseInfo().getAnalystId()).collect(Collectors.toList());

        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, companyType).getData();
        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<CaseNewsResp> recordRespList = result.getData().stream()
                .map(i -> CaseNewsResp.of(i.getCaseInfo(), i.getDealInfo(), i.getChannelInfo(),
                        analystInfoMap.get(i.getCaseInfo().getAnalystId()), subCaseIdList.contains(i.getCaseInfo().getId())))
                .collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public PageResult<List<StockCaseNoticeResp>> getL1StockCaseNotice(Integer companyType, Integer seriesId, String channelNumber, Integer current, Integer size) {
        List<String> caseChannelNumbers = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(seriesId)) {
            caseChannelNumbers = tradeClient.getStockCaseChannelBySeriesId(seriesId).orElse(new ArrayList<>())
                    .stream()
                    .map(TdStockCaseChannel::getNumber)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            caseChannelNumbers.add(channelNumber);
        }
        PageResult<List<TdRecommendStockNotice>> result = tradeClient.getCaseChannelNoticeList(companyType, caseChannelNumbers, true, current, size);
        if (result.isFail() || !result.isPresent()) {
            return PageResult.empty();
        }
        List<TdRecommendStockNotice> stockNotices = result.getData();
        Set<String> channelNumberSet = stockNotices.stream().map(TdRecommendStockNotice::getChannelNumber).filter(Objects::nonNull).collect(Collectors.toSet());
        List<ServiceIntroduction> serviceIntroductions = tradeClient.getPoolCaseServiceIntroductionList(companyType, BatchReq.of(channelNumberSet)).orElse(new ArrayList<>());
        Map<String, String> channelMap = serviceIntroductions.stream().collect(Collectors.toMap(ServiceIntroduction::getNumber, ServiceIntroduction::getName));

        List<StockCaseNoticeResp> respList = stockNotices.stream().map(notice -> StockCaseNoticeResp.builder()
                .id(notice.getId())
                .channelNumber(notice.getChannelNumber())
                .channelName(channelMap.getOrDefault(notice.getChannelNumber(), null))
                .content(notice.getContent())
                .createTime(notice.getCreateTime())
                .enabled(notice.getEnabled())
                .build()).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }

    public PageResult<List<StockCaseDetailResp>> getStockCaseRankList(Integer companyType, Integer seriesId, String channelNumber, Integer ratioType, Integer current, Integer size) {
        List<Long> caseChannelIds = new ArrayList<>();
        Integer articleSeriesId = null;
        if (ObjectUtil.isNotEmpty(seriesId)) {
            caseChannelIds = tradeClient.getStockCaseChannelBySeriesId(seriesId).orElse(new ArrayList<>())
                    .stream()
                    .map(TdStockCaseChannel::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else if (ObjectUtil.isNotEmpty(channelNumber)) {
            TdStockCaseChannel channelResp = tradeClient.getStockCaseChannelByFilter(null, channelNumber, null).orElse(null);
            if (ObjectUtil.isEmpty(channelResp)) {
                return PageResult.success(null, Pagination.of(current, size));
            }
            articleSeriesId = channelResp.getSeriesId();
            caseChannelIds.add(channelResp.getId());
        } else {
            return PageResult.success(null, Pagination.of(current, size));
        }
        PageResult<List<StockCaseDto>> result = tradeClient.getStockCaseDetailList(companyType, null, caseChannelIds, null, 1, null, null,
                false, null, true, null, null, ratioType, Lists.newArrayList(PASS.getCode()), null, null, current, size);
        if (result.getData() == null || result.getData().size() == 0) {
            return PageResult.success(null, result.getPagination());
        }
        Set<String> codeList = result.getData().stream().map(i -> i.getCaseInfo().getLabelCode()).collect(Collectors.toSet());
        Map<String, Long> codeArticleMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(articleSeriesId)) {
            BaseResult<List<RsArticleStockRelation>> relationArticleListResp = orderClient.getStockRelationArticleList(companyType, ArticleSeriesTypeConstant.AST_L1, articleSeriesId, codeList);
            if (relationArticleListResp.isSuccess()) {
                relationArticleListResp.getData().forEach(i -> codeArticleMap.put(i.getCode(), i.getArticleId()));
            }
        }
        List<StockCaseDetailResp> recordRespList = result.getData().stream().map(i -> {
            TdStockCase caseInfo = i.getCaseInfo();
            TdStockCaseDeal dealInfo = i.getCreateDealInfo();
            TdCaseProfit caseProfit = ObjectUtil.isNotEmpty(i.getCaseProfit()) ? i.getCaseProfit() : new TdCaseProfit();
            StockCaseDetailResp r = new StockCaseDetailResp();
            BeanUtil.copyProperties(caseInfo, r);
            r.setAuditTime(caseInfo.getAuditTime());
            r.setReason(dealInfo.getReason());
            r.setChannelId(caseInfo.getChannelId());
            r.setPriceUp(dealInfo.getPriceUp());
            r.setPriceDown(dealInfo.getPriceDown());
            r.setLossPrice(dealInfo.getLossPrice());
            r.setTargetPrice(dealInfo.getTargetPrice());
            r.setHasRelatedArticle(codeArticleMap.containsKey(caseInfo.getLabelCode()));
            if (ObjectUtil.isNotEmpty(caseProfit)) {
                r.setMaxRatio(caseProfit.getMaxRatio());
            }
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public PageResult<List<StockCaseDetailResp>> getStockCaseListL1(Integer companyType, Integer seriesId, String channelNumber, Integer timeType, Instant openTime, LocalDateTime startTime, LocalDateTime endTime,
                                                                    Boolean isAsc, Boolean isClosed, Boolean isTopSort, Integer current, Integer size) {
        if (ObjectUtil.isNotEmpty(startTime) && DateUtils.localDateTimeToInstant(startTime).isBefore(DateUtils.getDayOfStart(openTime))) {
            return PageResult.empty();
        }
        if (ObjectUtil.isEmpty(isTopSort)) {
            isTopSort = true;
        }
        List<Long> caseChannelIds = new ArrayList<>();
        Integer articleSeriesId = seriesId;
        if (ObjectUtil.isNotEmpty(seriesId)) {
            caseChannelIds = tradeClient.getStockCaseChannelBySeriesId(seriesId).orElse(new ArrayList<>())
                    .stream()
                    .map(TdStockCaseChannel::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else if (ObjectUtil.isNotEmpty(channelNumber)) {
            TdStockCaseChannel channelResp = tradeClient.getStockCaseChannelByFilter(null, channelNumber, null).orElse(null);
            if (ObjectUtil.isEmpty(channelResp)) {
                return PageResult.success(null, Pagination.of(current, size));
            }
            articleSeriesId = channelResp.getSeriesId();
            caseChannelIds.add(channelResp.getId());
        } else {
            return PageResult.success(null, Pagination.of(current, size));
        }
        PageResult<List<StockCaseDto>> result = tradeClient.getStockCaseDetailList(companyType, null, caseChannelIds, null, timeType, startTime, endTime,
                isAsc, isClosed, null, true, null, null, Lists.newArrayList(PASS.getCode()), null, null, current, size);
        if (result.getData() == null || result.getData().size() == 0) {
            return PageResult.success(null, result.getPagination());
        }
        Set<String> codeList = result.getData().stream().map(i -> i.getCaseInfo().getLabelCode()).collect(Collectors.toSet());
        Set<Integer> analystIds = result.getData().stream().map(i -> i.getCaseInfo().getAnalystId()).collect(Collectors.toSet());
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(new ArrayList<>(analystIds), companyType).getData();
        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));
        Map<String, Long> codeArticleMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(articleSeriesId)) {
            BaseResult<List<RsArticleStockRelation>> relationArticleListResp = orderClient.getStockRelationArticleList(companyType, ArticleSeriesTypeConstant.AST_L1, articleSeriesId, codeList);
            if (relationArticleListResp.isSuccess()) {
                relationArticleListResp.getData().forEach(i -> codeArticleMap.put(i.getCode(), i.getArticleId()));
            }
        }

        List<StockCaseDetailResp> recordRespList = result.getData().stream().map(i -> {
            TdStockCase caseInfo = i.getCaseInfo();
            TdStockCaseDeal dealInfo = i.getCreateDealInfo();
            TdCaseProfit caseProfit = ObjectUtil.isNotEmpty(i.getCaseProfit()) ? i.getCaseProfit() : new TdCaseProfit();
            StockCaseDetailResp r = new StockCaseDetailResp();
            BeanUtil.copyProperties(caseInfo, r);
            r.setAuditTime(caseInfo.getAuditTime());
            r.setReason(dealInfo.getReason());
            r.setChannelId(caseInfo.getChannelId());
            r.setPriceUp(dealInfo.getPriceUp());
            r.setPriceDown(dealInfo.getPriceDown());
            r.setLossPrice(dealInfo.getLossPrice());
            r.setTargetPrice(dealInfo.getTargetPrice());
            r.setHasRelatedArticle(codeArticleMap.containsKey(caseInfo.getLabelCode()));
            if (ObjectUtil.isNotEmpty(caseProfit)) {
                r.setMaxRatio(caseProfit.getMaxRatio());
            }
            r.setAnalystId(analystInfoMap.getOrDefault(caseInfo.getAnalystId(), new SsAnalystInfo()).getId());
            r.setAnalystName(analystInfoMap.getOrDefault(caseInfo.getAnalystId(), new SsAnalystInfo()).getName());
            r.setCertificateNo(analystInfoMap.getOrDefault(caseInfo.getAnalystId(), new SsAnalystInfo()).getCertificateNo());
            r.setAnalystAvatarUrl(analystInfoMap.getOrDefault(caseInfo.getAnalystId(), new SsAnalystInfo()).getAvatarUrl());
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public BaseResult<StockCaseDetailResp> getStockPoolCaseDetail(Long caseId, Permissions permissions) {
        StockCaseDto stockCaseDto = tradeClient.getStockCaseDetailInfo(caseId).orElse(null);
        if (ObjectUtil.isEmpty(stockCaseDto)) {
            return BaseResult.success(null);
        }
        Set<String> channelNumbers = permissions.stream().map(PermissionInfo::getNumber).collect(Collectors.toSet());
        if (!channelNumbers.contains(stockCaseDto.getChannelInfo().getNumber())) {
            throw new BusinessException(SERVICE_NOT_OPENED);
        }
        TdStockCase caseInfo = stockCaseDto.getCaseInfo();
        TdStockCaseDeal dealInfo = stockCaseDto.getCreateDealInfo();
        TdCaseProfit caseProfit = ObjectUtil.isNotEmpty(stockCaseDto.getCaseProfit()) ? stockCaseDto.getCaseProfit() : new TdCaseProfit();
        SsAnalystInfo analystResult = contentClient.getAnalystInfo(caseInfo.getAnalystId()).orElse(new SsAnalystInfo());
        StockCaseDetailResp resp = new StockCaseDetailResp();
        BeanUtil.copyProperties(caseInfo, resp);
        resp.setAuditTime(caseInfo.getAuditTime());
        resp.setReason(dealInfo.getReason());
        resp.setChannelId(caseInfo.getChannelId());
        resp.setPriceUp(dealInfo.getPriceUp());
        resp.setPriceDown(dealInfo.getPriceDown());
        resp.setLossPrice(dealInfo.getLossPrice());
        resp.setTargetPrice(dealInfo.getTargetPrice());
        if (ObjectUtil.isNotEmpty(caseProfit)) {
            resp.setMaxRatio(caseProfit.getMaxRatio());
        }
        resp.setAnalystId(analystResult.getId());
        resp.setAnalystName(analystResult.getName());
        resp.setCertificateNo(analystResult.getCertificateNo());
        resp.setAnalystAvatarUrl(analystResult.getAvatarUrl());
        return BaseResult.success(resp);
    }

    public BaseResult<StockCaseDetailResp> getStockCaseDetail(Long caseId, Permissions permissions) {
        StockCaseDto stockCaseDto = tradeClient.getStockCaseDetailInfo(caseId).orElse(null);
        if (ObjectUtil.isEmpty(stockCaseDto)) {
            return BaseResult.success(null);
        }
        Set<Integer> seriesSet = permissions.stream().map(x -> x.getId().intValue()).collect(Collectors.toSet());
        if (!seriesSet.contains(stockCaseDto.getChannelInfo().getSeriesId())) {
            throw new BusinessException(SERVICE_NOT_OPENED);
        }
        TdStockCase caseInfo = stockCaseDto.getCaseInfo();
        TdStockCaseDeal dealInfo = stockCaseDto.getCreateDealInfo();
        TdCaseProfit caseProfit = ObjectUtil.isNotEmpty(stockCaseDto.getCaseProfit()) ? stockCaseDto.getCaseProfit() : new TdCaseProfit();
        SsAnalystInfo analystResult = contentClient.getAnalystInfo(caseInfo.getAnalystId()).orElse(new SsAnalystInfo());
        StockCaseDetailResp resp = new StockCaseDetailResp();
        BeanUtil.copyProperties(caseInfo, resp);
        resp.setAuditTime(caseInfo.getAuditTime());
        resp.setReason(dealInfo.getReason());
        resp.setChannelId(caseInfo.getChannelId());
        resp.setPriceUp(dealInfo.getPriceUp());
        resp.setPriceDown(dealInfo.getPriceDown());
        resp.setLossPrice(dealInfo.getLossPrice());
        resp.setTargetPrice(dealInfo.getTargetPrice());
        if (ObjectUtil.isNotEmpty(caseProfit)) {
            resp.setMaxRatio(caseProfit.getMaxRatio());
        }
        resp.setAnalystId(analystResult.getId());
        resp.setAnalystName(analystResult.getName());
        resp.setCertificateNo(analystResult.getCertificateNo());
        resp.setAnalystAvatarUrl(analystResult.getAvatarUrl());
        return BaseResult.success(resp);
    }

    public PageResult<List<StockCaseDetailResp>> getCaseDetailList(Integer seriesId, Integer companyType, Long channelId, String channelNumber, String searchText, Integer timeType, LocalDateTime startTime,
                                                                   LocalDateTime endTime, Boolean isAsc, Boolean isClosed, Integer current, Integer size) {

        Integer articleSeriesId = seriesId;
        List<Long> caseChannelIds = null;
        if (ObjectUtil.isAllEmpty(channelId, channelNumber)) {
            caseChannelIds = tradeClient.getStockCaseChannelBySeriesId(seriesId).orElse(new ArrayList<>())
                    .stream()
                    .map(TdStockCaseChannel::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            BaseResult<TdStockCaseChannel> channelResp = tradeClient.getStockCaseChannelByFilter(channelId, channelNumber, null);
            if (channelResp.isSuccess() && channelResp.getData() != null) {
                channelId = channelResp.getData().getId();
                articleSeriesId = channelResp.getData().getSeriesId();
                if (!Objects.equals(seriesId, articleSeriesId)) {
                    return PageResult.fail(ErrorConstant.PERMISSION_INVALID);
                }
            } else {
                return PageResult.success(null, Pagination.of(current, size));
            }
        }

        PageResult<List<StockCaseDto>> result = tradeClient.getStockCaseDetailList(companyType, channelId,
                caseChannelIds, searchText, timeType, startTime, endTime, isAsc, isClosed, null, true, null, null, Lists.newArrayList(PASS.getCode()), null, null, current, size);
        if (result.getData() == null || result.getData().size() == 0) {
            return PageResult.success(null, result.getPagination());
        }
        Set<String> codeList = result.getData().stream().map(i -> i.getCaseInfo().getLabelCode()).collect(Collectors.toSet());
        Map<String, Long> codeArticleMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(articleSeriesId)) {
            BaseResult<List<RsArticleStockRelation>> relationArticleListResp = orderClient.getStockRelationArticleList(companyType, ArticleSeriesTypeConstant.AST_L2, articleSeriesId, codeList);
            if (relationArticleListResp.isSuccess()) {
                relationArticleListResp.getData().forEach(i -> codeArticleMap.put(i.getCode(), i.getArticleId()));
            }
        }

        List<StockCaseDetailResp> recordRespList = result.getData().stream().map(i -> {
            TdStockCase caseInfo = i.getCaseInfo();
            TdStockCaseDeal dealInfo = i.getCreateDealInfo();
            TdCaseProfit caseProfit = ObjectUtil.isNotEmpty(i.getCaseProfit()) ? i.getCaseProfit() : new TdCaseProfit();
            StockCaseDetailResp r = new StockCaseDetailResp();
            BeanUtil.copyProperties(caseInfo, r);
            r.setChannelId(caseInfo.getChannelId());
            r.setPriceUp(dealInfo.getPriceUp());
            r.setPriceDown(dealInfo.getPriceDown());
            r.setLossPrice(dealInfo.getLossPrice());
            r.setTargetPrice(dealInfo.getTargetPrice());
            r.setHasRelatedArticle(codeArticleMap.containsKey(caseInfo.getLabelCode()));
            if (ObjectUtil.isNotEmpty(caseProfit)) {
                r.setMaxRatio(caseProfit.getMaxRatio());
            }
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public BaseResult<List<AnalystInfoResp>> getAnalystInfoByChannelId(Long channelId) {

        BaseResult<List<TdCaseChannelAnalystRelation>> analystRelationsBaseResult = tradeClient.getCaseChannelAnalystRelations(channelId);

        if (analystRelationsBaseResult.isFail()) {
            log.error("获取老师关系记录失败： {}", analystRelationsBaseResult);
            throw new BusinessException(analystRelationsBaseResult);
        }

        List<TdCaseChannelAnalystRelation> analystRelations = analystRelationsBaseResult.getData() == null ? Collections.emptyList() : analystRelationsBaseResult.getData();
        if (ObjectUtils.isEmpty(analystRelations)) {
            return BaseResult.success(new ArrayList<>());
        }
        List<Integer> analystIds = analystRelations.stream().map(TdCaseChannelAnalystRelation::getAnalystId).collect(Collectors.toList());

        BaseResult<List<SsAnalystInfo>> analystInfoBaseResult = contentClient.getAnalystInfoList(analystIds, null, null);
        if (analystInfoBaseResult.isFail()) {
            log.error("获取老师列表失败： {}", analystInfoBaseResult);
            throw new BusinessException(analystInfoBaseResult);
        }
        List<SsAnalystInfo> analystInfos = analystInfoBaseResult.getData() == null ? Collections.emptyList() : analystInfoBaseResult.getData();
        List<AnalystInfoResp> resps = analystInfos.stream().map(analyst -> {
            AnalystInfoResp resp = new AnalystInfoResp();
            BeanUtils.copyProperties(analyst, resp);
            return resp;
        }).collect(Collectors.toList());

        return BaseResult.success(resps);

    }

    public List<PcStockCaseChannelItem> getPCStockCaseChannelList(Integer companyType, Integer userId) {
        List<TdStockCaseChannel> channelList = tradeClient.getStockCaseChannelList(companyType, null, SCT_QUANTIZE, true)
                .orElse(Collections.emptyList());

        List<UserSubscriptionServiceItem> subscriptionItems = orderClient.getUserVipSubscriptionServiceList(companyType, userId, POOL_CASE)
                .orElse(Collections.emptyList());
        Set<String> subscriptionSet = subscriptionItems.stream().map(e -> e.getService().getNumber()).collect(Collectors.toSet());

        return channelList.stream()
                .filter(this::filterPcChannel)
                .map(channel -> {
                    boolean accessible = subscriptionSet.contains(channel.getNumber());
                    return PcStockCaseChannelItem.builder()
                            .name(channel.getName())
                            .number(channel.getNumber())
                            .accessible(accessible)
                            .build();
                }).collect(Collectors.toList());
    }

    /**
     * 暂时过滤pc版频道
     *
     * @param channel
     * @return
     */
    private boolean filterPcChannel(TdStockCaseChannel channel) {
        Set<String> pcChannel = new HashSet<>(Arrays.asList("cdzdgc", "zsdxgc", "zsgc"));
        return pcChannel.contains(channel.getNumber());
    }

    public PageResult<List<PcStockCaseDetailResp>> getPCStockCaseList(Integer companyType, Long channelId, LocalDate flagDate, Integer current, Integer size) {
        Instant gmtCreate = null;
        if (flagDate == null) {
            BaseResult<TdStockCase> baseResult = tradeClient.getLastStockCaseDetail(companyType, channelId, PASS.getCode());
            if (baseResult.isFail()) {
                throw new BusinessException(baseResult);
            }
            gmtCreate = baseResult.getData().getGmtCreate();
        } else {
            gmtCreate = DateUtils.localDateToInstant(flagDate);
        }
        LocalDateTime startTime = DateUtils.instantToLocalDateTime(DateUtils.getDayOfStart(gmtCreate));
        LocalDateTime endTime = DateUtils.getDayOfEnd(startTime);
        PageResult<List<StockCaseDto>> pageResult = tradeClient.getStockCaseDetailList(companyType, channelId, null, null, 1, startTime, endTime, false, null, null, true, null, null, Lists.newArrayList(PASS.getCode()), null, null, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }

        List<StockCaseDto> data = pageResult.getData();
        Pagination pagination = pageResult.getPagination();
        List<PcStockCaseDetailResp> collected = data.stream().map(e -> {
            TdStockCase caseInfo = e.getCaseInfo();
            TdStockCaseDeal dealInfo = e.getCreateDealInfo();
            //给到关注价格：两个价格相加除2，四舍五入，取到小数点后两位
            Double priceUp = dealInfo.getPriceUp() != null ? dealInfo.getPriceUp() : 0.0;
            Double priceDown = dealInfo.getPriceDown() != null ? dealInfo.getPriceDown() : 0.0;
            Double avgPrice = (priceUp + priceDown) / 2.0;
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            String formatPrice = decimalFormat.format(avgPrice);

            return PcStockCaseDetailResp.builder()
                    .id(caseInfo.getId())
                    .labelCode(caseInfo.getLabelCode())
                    .gmtCreate(caseInfo.getGmtCreate())
                    .score(caseInfo.getScore())
                    .avgPrice(formatPrice)
                    .build();
        }).collect(Collectors.toList());

        return PageResult.success(collected, pagination);
    }

    public BaseResult<List<PcStockCaseSignalResp>> getPCStockCaseSignalList(Integer companyType, Integer userId, String labelCode) {
        List<TdStockCaseChannel> channelList = tradeClient.getStockCaseChannelList(companyType, null, SCT_QUANTIZE, true)
                .orElse(Collections.emptyList());
        List<UserSubscriptionServiceItem> subscriptionItems = orderClient.getUserVipSubscriptionServiceList(companyType, userId, POOL_CASE)
                .orElse(Collections.emptyList());
        Set<String> subscriptionSet = subscriptionItems.stream().map(e -> e.getService().getNumber()).collect(Collectors.toSet());
        List<Long> channelIdList = channelList.stream().filter(this::filterPcChannel)
                .filter(channel -> subscriptionSet.contains(channel.getNumber()))
                .map(TdStockCaseChannel::getId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(channelIdList)) {
            return BaseResult.success(null);
        }

        BaseResult<List<TdStockCase>> baseResult = tradeClient.getStockCaseSignalList(companyType, labelCode, channelIdList, PASS.getCode());
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        Map<Long, TdStockCaseChannel> channelMap = channelList.stream().collect(Collectors.toMap(TdStockCaseChannel::getId, Function.identity()));
        List<TdStockCase> data = baseResult.getData();
        List<PcStockCaseSignalResp> collected = data.stream().map(e ->
                        PcStockCaseSignalResp.builder()
                                .id(e.getId())
                                .labelCode(labelCode)
                                .channelNumber(channelMap.get(e.getChannelId()).getNumber())
                                .gmtCreate(e.getGmtCreate())
                                .build())
                .collect(Collectors.toList());
        return BaseResult.success(collected);
    }

    public List<StockCaseChannelItem> getStockCaseChannelList(Integer companyType, Integer userId, Integer channelType) {
        List<TdStockCaseChannel> channelList = tradeClient.getStockCaseChannelList(companyType, null, channelType, true)
                .orElse(Collections.emptyList());

        List<UserSubscriptionServiceItem> subscriptionItems = orderClient.getUserVipSubscriptionServiceList(companyType, userId, POOL_CASE)
                .orElse(Collections.emptyList());
        Set<String> subscriptionSet = subscriptionItems.stream().map(e -> e.getService().getNumber()).collect(Collectors.toSet());
        return channelList.stream().map(channel -> {
            boolean accessible = subscriptionSet.contains(channel.getNumber());
            StockCaseChannelItem build = StockCaseChannelItem.builder()
                    .name(channel.getName())
                    .number(channel.getNumber())
                    .accessible(accessible)
                    .isFree(!accessible)
                    .build();
            return build;
        }).collect(Collectors.toList());
    }

    public BaseResult<List<StockCaseResp>> getStockCaseList(Integer companyType, Long channelId, LocalDateTime startTime, LocalDateTime endTime) {
        PageResult<List<StockCaseDto>> pageResult = null;
        if (ObjectUtil.isAllNotEmpty(startTime, endTime)) {
            // 指定时间
            pageResult = tradeClient.getStockCaseDetailList(companyType, channelId, null, null, 1, startTime, endTime, false, null, null, null, null, null, Lists.newArrayList(PASS.getCode()), null, null, null, null);
            if (pageResult.isFail()) {
                throw new BusinessException(pageResult);
            }
        } else {
            // 最后一天有数据
            BaseResult<TdStockCase> baseResult = tradeClient.getLastStockCaseDetail(companyType, channelId, PASS.getCode());
            if (baseResult.isFail()) {
                throw new BusinessException(baseResult);
            }
            if (ObjectUtil.isNotEmpty(baseResult.getData())) {
                Instant gmtCreate = baseResult.getData().getGmtCreate();
                startTime = DateUtils.instantToLocalDateTime(DateUtils.getDayOfStart(gmtCreate));
                endTime = DateUtils.getDayOfEnd(startTime);
                pageResult = tradeClient.getStockCaseDetailList(companyType, channelId, null, null, 1, startTime, endTime, false, null, null, null, null, null, Lists.newArrayList(PASS.getCode()), null, null, null, null);
                if (pageResult.isFail()) {
                    throw new BusinessException(pageResult);
                }
            }
        }

        if (ObjectUtil.isEmpty(pageResult) || ObjectUtil.isEmpty(pageResult.getData())) {
            return BaseResult.success(Collections.emptyList());
        }
        List<StockCaseDto> data = pageResult.getData();
        Instant date = DateUtils.localDateTimeToInstant(startTime);
        List<StockCaseResp> collected = data.stream().map(e -> {
            TdStockCase caseInfo = e.getCaseInfo();
            return StockCaseResp.builder()
                    .id(caseInfo.getId())
                    .labelCode(caseInfo.getLabelCode())
                    .gmtCreate(caseInfo.getGmtCreate())
                    .score(caseInfo.getScore())
                    .priceDown(e.getCreateDealInfo().getPriceDown())
                    .priceUp(e.getCreateDealInfo().getPriceUp())
                    .date(date)
                    .build();
        }).collect(Collectors.toList());

        return BaseResult.success(collected);
    }

    public BaseResult<List<CaseAnalystResp>> getStockCaseAnalystList(Integer companyType, Long channelId) {

        List<CaseAnalystResp> resps = new ArrayList<>();
        BaseResult<List<TdCaseChannelAnalystRelation>> result = tradeClient.getCaseChannelAnalystRelations(channelId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!ObjectUtil.isEmpty(result.getData())) {
            List<Integer> analystIds = result.getData().stream().map(TdCaseChannelAnalystRelation::getAnalystId).collect(Collectors.toList());
            List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, companyType).getData();
            Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));
            resps = analystIds.stream()
                    .map(i -> {
                        return CaseAnalystResp.of(analystInfoMap.get(i));
                    }).collect(Collectors.toList());
        }

        return BaseResult.success(resps);
    }

    public BaseResult<StockCaseInfoResp> getStockCaseInfo(Integer userId, Long caseId) {
        List<TdCaseSubInfo> subInfos = tradeClient.filterSubByUserId(userId).orElse(null);
        List<Long> subCaseIdList = subInfos.stream().map(TdCaseSubInfo::getCaseId).collect(Collectors.toList());

        BaseResult<StockCaseDto> result = tradeClient.getStockCaseDetailInfo(caseId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return BaseResult.success(null);
        }
        StockCaseDto caseDto = result.getData();
        String labelCode = caseDto.getCaseInfo().getLabelCode();
        BaseResult<List<StockResp>> baseResult = quoteClient.getStockInfoListByLabel(BatchReq.of(Arrays.asList(labelCode)));
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        StockCaseInfoResp resp = StockCaseInfoResp.builder()
                .id(caseDto.getCaseInfo().getId())
                .labelCode(labelCode)
                .gmtCreate(caseDto.getCaseInfo().getGmtCreate())
                .isClosed(caseDto.getCaseInfo().getIsClosed())
                .channelId(caseDto.getCaseInfo().getChannelId())
                .count(caseDto.getCaseInfo().getCount())
                .priceDown(caseDto.getCreateDealInfo().getPriceDown())
                .priceUp(caseDto.getCreateDealInfo().getPriceUp())
                .channelName(caseDto.getChannelInfo().getName())
                .isSub(subCaseIdList.contains(caseDto.getCaseInfo().getId()))
                .build();
        if (ObjectUtil.isNotEmpty(caseDto.getCaseProfit())) {
            resp.setMax180Ratio(caseDto.getCaseProfit().getMax180Ratio());
        }
        if (ObjectUtil.isNotEmpty(baseResult.getData())) {
            StockResp stockResp = baseResult.getData().stream().filter(e -> ObjectUtil.equal(e.getLabel(), labelCode)).findFirst().orElse(null);
            if (ObjectUtil.isAllNotEmpty(stockResp, stockResp.getImageUrl())) {
                resp.setImageUrl(stockResp.getImageUrl());
            }
        }
        return BaseResult.success(resp);
    }

    public BaseResult<StockCaseProfitResp> getStockCaseMaxRatio(Long caseId) {

        BaseResult<TdCaseProfit> result = tradeClient.findCaseProfitByCaseId(caseId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return BaseResult.success(null);
        }
        TdCaseProfit caseProfit = result.getData();
        StockCaseProfitResp resp = StockCaseProfitResp.builder()
                .id(caseProfit.getCaseId())
                .max180Ratio(caseProfit.getMax180Ratio())
                .build();
        return BaseResult.success(resp);

    }

    public PageResult<List<CaseNewsResp>> getMySubStockCase(Integer companyType, Integer userId, Instant openTime, Integer current, Integer size) {

        PageResult<List<StockCaseDealDto>> pageResult = tradeClient.getMySubStockCaseList(companyType, userId, openTime, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.success(Collections.emptyList(), pageResult.getPagination());
        }
        List<StockCaseDealDto> dealDtoList = pageResult.getData();
        List<Integer> analystIds = dealDtoList.stream().map(i -> i.getCaseInfo().getAnalystId()).collect(Collectors.toList());

        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, companyType).getData();
        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<CaseNewsResp> recordRespList = dealDtoList.stream()
                .map(i -> CaseNewsResp.of(i.getCaseInfo(), i.getDealInfo(), i.getChannelInfo(),
                        analystInfoMap.get(i.getCaseInfo().getAnalystId()), true))
                .collect(Collectors.toList());
        return PageResult.success(recordRespList, pageResult.getPagination());
    }

    public BaseResult<SubStockCaseCountResp> getCaseSubLastCount(Integer customerId, Integer companyType) {

        BaseResult<VipSubscription> result = orderClient.getUserVipSubscription(customerId, 2);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            throw new BusinessException(SERVICE_NOT_OPENED);
        }

        ChatRoomConfigResp chatRoomConfigResp = orderClient.getChatRoomConfig().orElseThrow(() -> new BusinessException("获取聊天室配置错误"));

        SubStockCaseCountResp resp = tradeClient.getMySubStockCaseCount(customerId, true).orElseThrow();
        resp.setSubLastCount(Math.max(chatRoomConfigResp.getStockCount() - resp.getSubCount(), 0));
        return BaseResult.success(resp);
    }

    public BaseResult<StrategyResp> getStrategyResp(Integer companyType, Permissions permissions) {
        List<String> numberList = permissions.stream()
                .filter(e -> Objects.equals(e.getPackageNumber(), "l3"))
                .map(PermissionInfo::getNumber)
                .collect(Collectors.toList());
        if (ObjectUtil.isEmpty(numberList)) {
            return BaseResult.success(null);
        }
        List<TdStockCaseChannel> stockCaseChannels = tradeClient.getStrategyListByType(companyType, StockChannelType.SCT_STRATEGY, BatchReq.of(numberList)).orElse(Collections.emptyList());
        TdStockCaseChannel tdStockCaseChannel = stockCaseChannels.stream().findFirst().orElse(null);
        if (ObjectUtil.isEmpty(tdStockCaseChannel)) {
            return BaseResult.success(null);
        }
        PermissionInfo permissionInfo = permissions.stream()
                .filter(e -> Objects.equals(e.getId(), tdStockCaseChannel.getId()))
                .findFirst()
                .orElse(null);

        Long id = permissionInfo.getId();
        Instant openTime = permissionInfo.getOpenTime();

        StrategyResp strategyResp = new StrategyResp();
        BaseResult<StrategyChannelResp> strategyInfo = tradeClient.getStrategyByChannelId(id);
        strategyResp.setStrategyChannelResp(strategyInfo.getData());
        BaseResult<List<StrategyStockCaseResp>> strategyStock = tradeClient.getStrategyStock(companyType, id, openTime);
        List<StrategyStockCaseResp> baseResult = strategyStock.getData();
        strategyResp.setStrategyStockCaseResp(baseResult);
        if (ObjectUtil.isAllNotEmpty(baseResult, strategyResp.getStrategyChannelResp())) {
            Instant lastAuditTime = baseResult.stream().map(StrategyStockCaseResp::getAuditTime)
                    .max(Comparator.comparing(Instant::toEpochMilli)).orElse(null);
            strategyResp.getStrategyChannelResp().setLastUpdateTime(lastAuditTime);
        }
        return BaseResult.success(strategyResp);
    }

    public BaseResult<List<StrategyStockDealResp>> getStrategyStockDealList(Integer companyType, Long channelId, Instant openTime, LocalDate endTime, Integer day) {

        Instant ofStart = null;
        if (ObjectUtil.isNotEmpty(endTime)) {
            ofStart = DateUtils.getDayOfStart(endTime);
        }
        BaseResult<List<TdStockCaseDeal>> result = tradeClient.getStrategyStockDealList(companyType, channelId, openTime, ofStart, day);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return BaseResult.success(null);
        }
        List<TdStockCaseDeal> dealList = result.getData();
        List<Long> caseIdList = dealList.stream().map(TdStockCaseDeal::getCaseId).distinct().collect(Collectors.toList());
        List<TdStockCase> tdStockCases = tradeClient.batchStockCase(caseIdList).orElseThrow();
        Map<Long, TdStockCase> stockCaseMap = tdStockCases.stream().collect(Collectors.toMap(TdStockCase::getId, Function.identity()));
        List<StrategyStockDealResp> resps = dealList.stream().map(deal -> {
            return StrategyStockDealResp.of(stockCaseMap.getOrDefault(deal.getCaseId(), new TdStockCase()), deal);
        }).collect(Collectors.toList());
        return BaseResult.success(resps);
    }

    public BaseResult<StockCaseDealResp> getLastStockCaseDeal(Integer companyType, Long channelId, Instant openTime, LocalDate deadline) {

        TdStockCaseDeal tdStockCaseDeal = tradeClient.getLastStockCaseDeal(companyType, channelId, openTime, DateUtils.getDayOfStart(deadline)).orElseThrow();
        if (ObjectUtil.isEmpty(tdStockCaseDeal)) {
            return BaseResult.success(null);
        }
        StockCaseDealResp resp = StockCaseDealResp.of(tdStockCaseDeal);
        return BaseResult.success(resp);
    }
}

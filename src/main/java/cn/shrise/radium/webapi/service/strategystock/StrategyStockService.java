package cn.shrise.radium.webapi.service.strategystock;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.strategystockservice.StrategyStockServiceClient;
import cn.shrise.radium.strategystockservice.entity.TdStrategyStockPool;
import cn.shrise.radium.strategystockservice.entity.TdStrategyStockPoolSubscription;
import cn.shrise.radium.strategystockservice.resp.StrategyStockPoolResp;
import cn.shrise.radium.webapi.resp.strategystock.TradeStatusResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StrategyStockService {

    private final StrategyStockServiceClient strategyStockServiceClient;

    public BaseResult<List<StrategyStockPoolResp>> strategyStockService(Integer userId) {
        List<TdStrategyStockPool> strategyStockPools = strategyStockServiceClient.getStrategyStockPoolList(true).orElseThrow();
        BaseResult<TdStrategyStockPoolSubscription> baseResult;
        if (ObjectUtil.isNotEmpty(userId)) {
            baseResult = strategyStockServiceClient.getSubStrategyInfo(userId);
            if (baseResult.isFail()) {
                throw new BusinessException(baseResult);
            }
        } else {
            baseResult = null;
        }

        if (ObjectUtil.isEmpty(strategyStockPools)) {
            return BaseResult.success(Collections.emptyList());
        }
        List<StrategyStockPoolResp> resps = strategyStockPools.stream().map(stockPool -> {
            StrategyStockPoolResp resp = StrategyStockPoolResp.builder()
                    .id(stockPool.getId())
                    .code(stockPool.getCode())
                    .name(stockPool.getName())
                    .period(stockPool.getPeriod())
                    .description(stockPool.getDescription())
                    .isSub(false)
                    .build();
            if (ObjectUtil.isNotEmpty(baseResult) && ObjectUtil.isNotEmpty(baseResult.getData())) {
                TdStrategyStockPoolSubscription subscription = baseResult.getData();
                if (ObjectUtil.isNotEmpty(subscription) && subscription.getPoolCode().equals(stockPool.getCode())) {
                    resp.setIsSub(true);
                }
            }
            return resp;
        }).collect(Collectors.toList());

        return BaseResult.success(resps);
    }

    public BaseResult<TradeStatusResp> currentTradeStatus() {



        LocalDate today = LocalDate.now();
        Boolean isTradeDay = DateUtils.checkTradeDays(today);
        LocalDateTime time = LocalDateTime.now();
        LocalDateTime start = LocalDateTimeUtil.beginOfDay(time).plusHours(9).plusMinutes(30);
        LocalDateTime end = LocalDateTimeUtil.beginOfDay(time).plusHours(15);
        if (isTradeDay && time.isAfter(start) && time.isBefore(end)) {
            // 在交易时间内
            TradeStatusResp resp = TradeStatusResp.builder()
                    .isTradeDay(isTradeDay)
                    .nextTradeDuration(0L)
                    .currentTradeDuration(DateUtils.localDateTimeToInstant(end).getEpochSecond() - Instant.now().getEpochSecond())
                    .build();
            return BaseResult.success(resp);
        }
        LocalDateTime nextTradeTime = DateUtils.getNextTradeTime(today, isTradeDay);
        if (ObjectUtil.isEmpty(nextTradeTime)) {
            throw new BusinessException("获取交易日时间失败");
        }
        TradeStatusResp resp = TradeStatusResp.builder()
                .isTradeDay(isTradeDay)
                .nextTradeDuration(DateUtils.localDateTimeToInstant(nextTradeTime).getEpochSecond() - Instant.now().getEpochSecond())
                .currentTradeDuration(0L)
                .build();
        return BaseResult.success(resp);
    }

    public BaseResult<StrategyStockPoolResp> getStrategyStockPoolInfo(String poolCode, Integer userId) {
        StrategyStockPoolResp resp = new StrategyStockPoolResp();
        TdStrategyStockPool strategyStockPool = strategyStockServiceClient.getStrategyStockPoolInfo(poolCode).orElseThrow();
        BaseResult<TdStrategyStockPoolSubscription> baseResult = strategyStockServiceClient.getSubStrategyInfo(userId);
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        if (ObjectUtil.isEmpty(strategyStockPool)) {
            return BaseResult.success(resp);
        }

        BeanUtil.copyProperties(strategyStockPool, resp);
        resp.setIsSub(false);
        if (ObjectUtil.isNotEmpty(baseResult.getData())) {
            TdStrategyStockPoolSubscription subscription = baseResult.getData();
            if (ObjectUtil.isNotEmpty(subscription) && subscription.getPoolCode().equals(poolCode)) {
                resp.setIsSub(true);
            }
        }
        return BaseResult.success(resp);
    }
}

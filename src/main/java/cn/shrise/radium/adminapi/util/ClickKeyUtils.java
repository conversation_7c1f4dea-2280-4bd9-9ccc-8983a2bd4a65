package cn.shrise.radium.adminapi.util;

import cn.shrise.radium.common.base.BaseResult;

import java.util.Random;

public class ClickKeyUtils {
    private static final Random random = new Random();

    public static String generateClickKey() {
        long timePart = System.currentTimeMillis() / 60000L;
        int randomPart = random.nextInt(256);
        return String.format("CK_%07X%02X", timePart, randomPart);
    }
}

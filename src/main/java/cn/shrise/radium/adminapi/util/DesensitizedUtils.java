package cn.shrise.radium.adminapi.util;

import org.apache.commons.lang3.ObjectUtils;

public class DesensitizedUtils {

    /**
     * 脱敏字段
     * @param field
     * @return
     */
    public static String desensitizedField(String field) {
        return "**********";
    }

    /**
     * 脱敏字段，保留前后相应位数
     * @param field
     * @param keep
     * @return
     */
    public static String desensitizedField(String field, int keep) {
        int length = keep * 2;
        int fieldLength = ObjectUtils.isEmpty(field) ? 0 : field.length();
        if (fieldLength <= length) return "**********";
        int pos = fieldLength - 1 - keep;
        String prefix = field.substring(0, keep);
        String middle = field.substring(keep, pos);
        String suffix = field.substring(pos, fieldLength - 1);
        return prefix + middle + suffix;
    }

    public static String desensitizedField(String field, boolean desensitized) {
        return desensitized ? desensitizedField(field) : field;
    }

    public static String desensitizedField(String field, int keep, boolean desensitized) {
        return desensitized ? desensitizedField(field, keep) : field;
    }
}

package cn.shrise.radium.adminapi.util;

import cn.shrise.radium.common.util.DateUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import static cn.shrise.radium.adminapi.constant.DateConstant.*;

/**
 * <AUTHOR>
 */
public class DateDetailUtils {

    public static List<LocalDateTime> getLocalDateTime(Instant date, Integer periodType) {
        LocalDateTime startTime = date.atZone(ZoneId.systemDefault()).withHour(0).withMinute(0).withSecond(0).withNano(0).toLocalDateTime();
        return getLocalDate(periodType, startTime);
    }

    private static List<LocalDateTime> getLocalDate(Integer periodType, LocalDateTime startTime) {
        LocalDateTime endDate = null;
        if (periodType.equals(DAY)) {
            endDate = startTime;
        } else if (periodType.equals(WEEK)) {
            startTime = DateUtils.getTodayFirstOfWeek(startTime);
            endDate = startTime.plusDays(6);
        } else if (periodType.equals(MONTH)) {
            startTime = DateUtils.getFirstDayFromMonth(startTime);
            endDate = startTime.plusMonths(1).plusDays(-1);
        }
        List<LocalDateTime> dateList = new ArrayList<>(1);
        dateList.add(endDate);
        return dateList;
    }

    private static List<LocalDateTime> getLocalDateTimes(Integer periodType, LocalDateTime startTime) {
        LocalDateTime endDate = null;
        if (periodType.equals(DAY)) {
            endDate = startTime.plusDays(1);
        } else if (periodType.equals(WEEK)) {
            startTime = DateUtils.getTodayFirstOfWeek(startTime);
            endDate = startTime.plusDays(7);
        } else if (periodType.equals(MONTH)) {
            startTime = DateUtils.getFirstDayFromMonth(startTime);
            endDate = startTime.plusMonths(1);
        } else if (periodType.equals(QUARTER)) {
            startTime = DateUtils.getFirstDayFromMonth(startTime.plusMonths(-2));
            endDate = startTime.plusMonths(3);
        }
        List<LocalDateTime> dateList = new ArrayList<>(2);
        dateList.add(startTime);
        dateList.add(endDate);
        return dateList;
    }

    public static List<LocalDateTime> getTaskTimeList(Instant startDate, Integer periodType) {
        LocalDateTime startTime = startDate.atZone(ZoneId.systemDefault()).minusHours(5).withHour(0).withMinute(0).withSecond(0).withNano(0).toLocalDateTime();
        return getLocalDateTimes(periodType, startTime);
    }

}

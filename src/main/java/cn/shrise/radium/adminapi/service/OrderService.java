package cn.shrise.radium.adminapi.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.adminapi.constant.OrderRefundStatus;
import cn.shrise.radium.adminapi.entity.SubOrderInfo;
import cn.shrise.radium.adminapi.entity.*;
import cn.shrise.radium.adminapi.resp.AssignBatchRecordResp;
import cn.shrise.radium.adminapi.resp.DepartmentNameOrderVolumeResp;
import cn.shrise.radium.adminapi.resp.GetWholeOrderInfo;
import cn.shrise.radium.adminapi.resp.OrderVolumeResp;
import cn.shrise.radium.adminapi.resp.order.CouponResp;
import cn.shrise.radium.adminapi.resp.order.OrderInfoResp;
import cn.shrise.radium.adminapi.resp.order.*;
import cn.shrise.radium.adminapi.resp.user.DeptInfoResp;
import cn.shrise.radium.adminapi.resp.user.UserInfoResp;
import cn.shrise.radium.adminapi.resp.workWx.DzAdChannelResp;
import cn.shrise.radium.adminapi.resp.workWx.FullRelationInfoResp;
import cn.shrise.radium.adminapi.resp.wx.WxExtResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.PdfUtils;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.*;
import cn.shrise.radium.orderservice.constant.payment.PaymentPlatformEnum;
import cn.shrise.radium.orderservice.dto.OrderExtInfoDto;
import cn.shrise.radium.orderservice.dto.OrderInfoDto;
import cn.shrise.radium.orderservice.dto.SubOrderDto;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import cn.shrise.radium.orderservice.properties.merchant.Merchant;
import cn.shrise.radium.orderservice.req.CreateCourseRefundCommentReq;
import cn.shrise.radium.orderservice.req.RefundMarkReq;
import cn.shrise.radium.orderservice.req.RetryRefundPlanReq;
import cn.shrise.radium.orderservice.req.UpdateSubOrderReq;
import cn.shrise.radium.orderservice.resp.*;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcRole;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import cn.shrise.radium.userservice.resp.FakeDataPropertyResp;
import cn.shrise.radium.userservice.resp.UserDepartmentResp;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WorkWxFullContactUserRelation;
import cn.shrise.radium.workwxservice.bean.WwxConfig;
import cn.shrise.radium.workwxservice.entity.*;
import cn.shrise.radium.workwxservice.resp.ResourceDetailResp;
import cn.shrise.radium.wxservice.WxClient;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.DesensitizeUtil.isValidUserCode;
import static cn.shrise.radium.common.util.DesensitizeUtil.maskToId;
import static cn.shrise.radium.orderservice.constant.payment.PaymentChannelTypeEnum.PCT_Origin;
import static cn.shrise.radium.orderservice.constant.payment.PaymentPlatformEnum.PP_Ali;
import static cn.shrise.radium.orderservice.constant.payment.PaymentPlatformEnum.PP_Wx;
import static java.util.Collections.emptyList;

@Service
@RequiredArgsConstructor
public class OrderService {
    private final OrderClient orderClient;
    private final UserClient userClient;
    private final WorkwxClient workwxClient;
    private final WxClient wxClient;
    private final Executor adminExecutor;
    private final CommonService commonService;

    private static final List<Integer> noFeedbackLevelList = Arrays.asList(SkuProductLevelConstant.L1A.getValue(),
            SkuProductLevelConstant.L11.getValue(), SkuProductLevelConstant.L1B.getValue(),
            SkuProductLevelConstant.L1C.getValue(), SkuProductLevelConstant.COUPON.getValue(),
            SkuProductLevelConstant.TJ_L1.getValue(), SkuProductLevelConstant.TJ_L2.getValue());


    public Optional<OrderInfo> getOrder(Integer orderId) {
        BaseResult<RsCourseOrder> result = orderClient.getOrder(orderId);
        if (result.isFail()) {
            return Optional.empty();
        }
        OrderInfo orderInfo = OrderInfo.of(result.getData());
        return Optional.of(orderInfo);
    }

    public Optional<OrderInfo> getOrder(String orderNumber) {
        BaseResult<RsCourseOrder> result = orderClient.getOrder(orderNumber);
        if (result.isFail()) {
            return Optional.empty();
        }
        OrderInfo orderInfo = OrderInfo.of(result.getData());
        return Optional.of(orderInfo);
    }

    public Optional<OrderDetail> getOrderDetail(Integer orderId) {
        BaseResult<FullOrder> result = orderClient.getFullOrder(orderId);
        if (result.isFail()) {
            return Optional.empty();
        }
        FullOrder fullOrder = result.getData();
        OrderDetail orderDetail = transformToOrderDetail(fullOrder);
        return Optional.of(orderDetail);
    }

    public Optional<OrderDetail> getOrderDetail(String orderNumber) {
        BaseResult<FullOrder> result = orderClient.getFullOrder(orderNumber);
        if (result.isFail()) {
            return Optional.empty();
        }
        FullOrder fullOrder = result.getData();
        OrderDetail orderDetail = transformToOrderDetail(fullOrder);
        return Optional.of(orderDetail);
    }

    public Optional<OrderDetail> getOrderDetail(Integer companyType, String orderNumber) {
        BaseResult<FullOrder> result = orderClient.getFullOrderByCompanyNumber(companyType, orderNumber);
        if (result.isFail()) {
            return Optional.empty();
        }
        FullOrder fullOrder = result.getData();
        OrderDetail orderDetail = transformToOrderDetail(fullOrder);
        return Optional.of(orderDetail);
    }

    public Integer getFeedbackStatus(Integer productLevel, Long feedbackId, RsOrderArtificialFeedback artificialFeedback) {
        Integer feedbackStatus = ArtificialFeedbackConstant.AFC_0;
        if (productLevel != null && noFeedbackLevelList.contains(productLevel)) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_2;
        } else if ((ObjectUtil.isNull(feedbackId) && ObjectUtil.isNull(artificialFeedback) && ObjectUtil.equal(productLevel, SkuProductLevelConstant.STRATEGY.getValue()))) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_10;
        } else if (ObjectUtil.isNull(feedbackId) && ObjectUtil.isNull(artificialFeedback) && ObjectUtil.notEqual(productLevel, SkuProductLevelConstant.STRATEGY.getValue())) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_0;
        } else if (ObjectUtil.isNotNull(feedbackId) && ObjectUtil.notEqual(productLevel, SkuProductLevelConstant.STRATEGY.getValue())) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_1;
        } else if (ObjectUtil.isNotEmpty(artificialFeedback) && ObjectUtil.isNull(artificialFeedback.getIsPass())) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_10;
        } else if (ObjectUtil.isNotEmpty(artificialFeedback) && ObjectUtil.equal(artificialFeedback.getIsPass(), true)) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_11;
        } else if (ObjectUtil.isNotEmpty(artificialFeedback) && ObjectUtil.equal(artificialFeedback.getIsPass(), false)) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_12;
        }
        /*Integer feedbackStatus = ArtificialFeedbackConstant.AFC_0;
        if (ObjectUtil.isNotEmpty(productLevel) && productLevel.equals(SkuProductLevelConstant.STRATEGY.getValue())) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_10;
        }
        if (ObjectUtil.isNotEmpty(productLevel) && noFeedbackLevelList.contains(productLevel)) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_2;
        } else if (ObjectUtil.isNotEmpty(feedbackId)) {
            feedbackStatus = ArtificialFeedbackConstant.AFC_1;
        } else if (ObjectUtil.isNotEmpty(artificialFeedback)) {
            feedbackStatus = ObjectUtil.isNull(artificialFeedback.getIsPass()) ? ArtificialFeedbackConstant.AFC_10 :
                    ObjectUtil.equals(artificialFeedback.getIsPass(), true) ? ArtificialFeedbackConstant.AFC_11 :
                            ArtificialFeedbackConstant.AFC_12;
        }*/
        return feedbackStatus;
    }

    private OrderDetail transformToOrderDetail(FullOrder fullOrder) {
        RsCourseOrder order = fullOrder.getOrder();
        RsSku sku = fullOrder.getSku();
        Integer orderId = order.getId();
        NpDzAdChannel dzInfo = new NpDzAdChannel();
        String stateRemark = null;
        RsCourseOrderExt orderExt = orderClient.getOrderExt(orderId);
        if (ObjectUtil.isNotEmpty(orderExt) && ObjectUtil.isNotEmpty(orderExt.getDzId())) {
            Map<Long, NpDzAdChannel> dzInfoMap = workwxClient.getNpDzAdChannelByIdSet(new HashSet<>(Arrays.asList(orderExt.getDzId()))).getData();
            dzInfo = dzInfoMap.getOrDefault(orderExt.getDzId(), null);
        }
        if (ObjectUtil.isNotEmpty(orderExt) && ObjectUtil.isNotEmpty(orderExt.getState())) {
            NpWorkWxContactWay contactWay = workwxClient.getContactWay(orderExt.getAccountType(), orderExt.getState()).getData();
            NpWwxCustomerAcquisitionChannel customerAcquisitionChannel = workwxClient.getCustomerAcquisitionChannelByState(orderExt.getState()).getData();
            if (ObjectUtil.isNotNull(contactWay)) {
                stateRemark = contactWay.getRemark();
            } else {
                stateRemark = customerAcquisitionChannel.getRemark();
            }
        }
        BaseResult<List<CourseSubOrder>> getSubOrderListResult = orderClient.getSubOrderList(orderId);
        List<CourseSubOrder> subOrders = getSubOrderListResult.isSuccess() ?
                getSubOrderListResult.getData() : Collections.emptyList();
        OrderDetail orderDetail = OrderDetail.of(order, sku);
        //人工回访信息
        RsOrderArtificialFeedback artificialFeedback = orderClient.getArtificialFeedback(orderId).orElse(null);

        Set<Integer> userIdSet = new HashSet<>(Arrays.asList(order.getUserId(), order.getSalesId()));
        if (ObjectUtil.isNotEmpty(artificialFeedback) && ObjectUtil.isNotEmpty(artificialFeedback.getCreatorId())) {
            userIdSet.add(artificialFeedback.getCreatorId());
        }
        if (ObjectUtil.isNotEmpty(artificialFeedback) && ObjectUtil.isNotEmpty(artificialFeedback.getDealId())) {
            userIdSet.add(artificialFeedback.getDealId());
        }
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userIdSet));
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess() ? getUserResult.getData() : Collections.emptyMap();
        Map<Integer, String> deptMap = userClient.getDeptListByUsers(Collections.singletonList(order.getSalesId())).getData();
        UcUsers customer = userMap.get(order.getUserId());
        UcUsers sales = userMap.get(order.getSalesId());

        final RsOrderFeedback orderFeedback = orderClient.getOrderFeedback(orderId).orElse(null);

        orderDetail.setUserInfo(UserInfo.of(customer));
        orderDetail.setSalesInfo(UserInfo.of(sales));
        orderDetail.setDeptName(deptMap.getOrDefault(order.getSalesId(), null));
        orderDetail.setOrderExt(orderExt);
        orderDetail.setDzInfo(dzInfo);
        orderDetail.setStateRemark(stateRemark);
        List<SubOrderInfo> subOrderInfoList = subOrders.stream()
                .map(SubOrderInfo::of)
                .collect(Collectors.toList());
        orderDetail.setSubOrderList(subOrderInfoList);

        int paidAmount = calculatePaidAmount(subOrderInfoList);
        orderDetail.setPaidAmount(paidAmount);
        Long feedbackId = orderFeedback == null ? null : orderFeedback.getId();
        Instant feedbackTime = orderFeedback == null ? null : orderFeedback.getGmtCreate();
        String feedbackUrl = orderFeedback == null ? null : orderFeedback.getFeedbackUrl();
        orderDetail.setFeedbackId(feedbackId);
        orderDetail.setFeedbackTime(feedbackTime);
        orderDetail.setFeedbackUrl(feedbackUrl);
        Integer feedbackStatus = getFeedbackStatus(sku.getProductLevel(), feedbackId, artificialFeedback);
        orderDetail.setFeedbackStatus(feedbackStatus);
        if (ObjectUtil.isNotEmpty(artificialFeedback)) {
            OrderDetail.ArtificialFeedbackInfo artificialFeedbackInfo = OrderDetail.ArtificialFeedbackInfo.builder()
                    .id(artificialFeedback.getId())
                    .creatorName(userMap.getOrDefault(artificialFeedback.getCreatorId(), new UcUsers()).getRealName())
                    .createTime(artificialFeedback.getGmtCreate())
                    .isPass(artificialFeedback.getIsPass())
                    .dealName(userMap.getOrDefault(artificialFeedback.getDealId(), new UcUsers()).getRealName())
                    .dealTime(artificialFeedback.getDealTime())
                    .remark(artificialFeedback.getRemark())
                    .videoUrl(artificialFeedback.getVideoUrl())
                    .build();
            orderDetail.setArtificialFeedbackInfo(artificialFeedbackInfo);
        }
        if (ObjectUtil.isNotNull(orderDetail.getPayCompanyId())) {
            RsPayCompany payCompany = orderClient.getPayCompanyById(orderDetail.getPayCompanyId()).getData();
            orderDetail.setPayCompany(payCompany.getName());
        }
        return orderDetail;
    }

    public Integer getOrderId(Integer companyType, String searchNumber) {
        BaseResult<Integer> orderId = orderClient.getOrderId(companyType, searchNumber);
        if (!orderId.isSuccess()) {
            throw new BusinessException(orderId);
        }
        return orderId.getData();
    }


    public GetWholeOrderInfo getWholeOrder(Integer pkId) {
        BaseResult<WholeSelectOrderInfo> wholeSelectOrderInfo = orderClient.getWholeOrder(pkId);
        if (wholeSelectOrderInfo.getData() == null) {
            return null;
        }
        GetWholeOrderInfo resp = new GetWholeOrderInfo();
        WholeOrderInfo wholeOrderInfo = wholeSelectOrderInfo.getData().getWholeOrderInfo();
        BeanUtils.copyProperties(wholeOrderInfo, resp);
        String userName = null;
        String salesName = null;
        String userCode = null;
        if (wholeOrderInfo.getUserId() != null) {
            BaseResult<UcUsers> user = userClient.getUser(wholeOrderInfo.getUserId());
            userName = user.getData().getNickName();
            userCode = DesensitizeUtil.idToMask(wholeOrderInfo.getUserId());
        }
        if (wholeOrderInfo.getSalesId() != null) {
            BaseResult<UcUsers> sales = userClient.getUser(wholeOrderInfo.getSalesId());
            salesName = sales.getData().getRealName();
        }
        resp.setUserName(userName);
        resp.setSalesName(salesName);
        resp.setUserCode(userCode);
        resp.setSubOrderInfoList(wholeSelectOrderInfo.getData().getSubOrderInfoList());
        resp.setRelevantRefundInfoList(wholeSelectOrderInfo.getData().getRelevantRefundInfoList());
        return resp;
    }

    public DepartmentNameOrderVolumeResp getUserOrderVolume(Integer deptId, LocalDateTime startTime, LocalDateTime endTime, Integer size, Integer category) {
        BaseResult<List<Integer>> userIdsByDepartmentId = userClient.getUserIdsByDepartmentId(deptId);
        if (userIdsByDepartmentId.isFail()) {
            throw new BusinessException(userIdsByDepartmentId);
        }
        List<Integer> userIds = userIdsByDepartmentId.getData();
        BaseResult<List<UserOrderVolumeResp>> userOrder = orderClient.getUserOrderVolume(userIds, startTime, endTime, size, category);
        if (userOrder.isFail()) {
            throw new BusinessException(userOrder);
        }
        List<UserOrderVolumeResp> userOrderVolumes = userOrder.getData();
        List<OrderVolumeResp> orderVolumeResps = new ArrayList<>();
        userOrderVolumes.forEach(e -> {
            OrderVolumeResp orderVolumeResp = new OrderVolumeResp();
            BeanUtils.copyProperties(e, orderVolumeResp);
            orderVolumeResps.add(orderVolumeResp);
        });
        List<Integer> userIdsTemp = new ArrayList<>();
        userOrderVolumes.forEach(e -> {
            userIdsTemp.add(e.getUserId());
        });
        BaseResult<List<UserDepartmentResp>> userDepartments = userClient.getUserDepartmentListByUserIds(userIdsTemp);
        if (userDepartments.isFail()) {
            throw new BusinessException(userDepartments);
        }
        List<UserDepartmentResp> userDepartmentList = userDepartments.getData();
        List<OrderVolumeResp> orderVolumeResp = orderVolumeResps.stream().map(orderVolume -> userDepartmentList.stream()
                        .filter(userDepartment -> orderVolume.getUserId().equals(userDepartment.getUserId()))
                        .findFirst()
                        .map(userDepartment -> {
                            orderVolume.setUserRealName(userDepartment.getUserRealName());
                            orderVolume.setDepartment(userDepartment.getDepartment());
                            return orderVolume;
                        }).orElse(null))
                .collect(Collectors.toList());
        List<Integer> integers = new ArrayList<>();
        integers.add(deptId);
        BaseResult<List<UcDepartment>> listBaseResult = userClient.batchGetDeptList(integers);
        if (listBaseResult.isFail()) {
            throw new BusinessException(listBaseResult);
        }
        DepartmentNameOrderVolumeResp departmentNameOrderVolumeResp = new DepartmentNameOrderVolumeResp(listBaseResult.getData().get(0).getName(), orderVolumeResp);
        return departmentNameOrderVolumeResp;
    }

    public PageResult<List<SalesOrderDetailResp>> getSalesOrderDetail(Integer salesId, LocalDate flagTime,
                                                                      Integer periodType, Integer orderLevel,
                                                                      Long dzId, Integer current, Integer size) {
        PageResult<List<SalesOrderDetailResp>> salesOrderDetail = orderClient.getSalesOrderDetail(salesId, flagTime, periodType, orderLevel, dzId, current, size);
        if (salesOrderDetail.isFail()) {
            throw new BusinessException(salesOrderDetail);
        }
        List<SalesOrderDetailResp> data = salesOrderDetail.getData();
        Set<Long> dzIdSet = data.stream().map(SalesOrderDetailResp::getDzId).collect(Collectors.toSet());
        List<NpDzAdChannel> npDzAdChannels = workwxClient.getNpDzAdChannelByIds(dzIdSet).getData();
        if (ObjectUtil.isNotEmpty(npDzAdChannels)) {
            Map<Long, NpDzAdChannel> dzAdChannelMap = npDzAdChannels.stream().collect(Collectors.toMap(NpDzAdChannel::getId, r -> r));
            data.forEach(e -> {
                if (dzAdChannelMap.containsKey(e.getDzId())) {
                    e.setDzNumber(dzAdChannelMap.get(e.getDzId()).getDzNumber());
                }
            });
        }
        data.stream().filter(Objects::nonNull).forEach(item -> item.setUserCode(DesensitizeUtil.idToMask(item.getUserId())));
        return PageResult.success(data, Pagination.of(current, size, salesOrderDetail.getPagination().getTotal()));
    }

    public PageResult<List<SalesOrderDetailResp>> getDeptOrderDetail(Integer deptId, LocalDate flagTime,
                                                                     Integer periodType, Integer orderLevel, Long dzId,
                                                                     Integer current, Integer size) {
        PageResult<List<SalesOrderDetailResp>> deptOrderDetail = orderClient.getDeptOrderDetail(deptId, flagTime, periodType, orderLevel, dzId, current, size);
        if (deptOrderDetail.isFail()) {
            throw new BusinessException(deptOrderDetail);
        }
        List<SalesOrderDetailResp> data = deptOrderDetail.getData();
        Set<Long> dzIdSet = data.stream().map(SalesOrderDetailResp::getDzId).collect(Collectors.toSet());
        List<NpDzAdChannel> npDzAdChannels = workwxClient.getNpDzAdChannelByIds(dzIdSet).getData();
        if (ObjectUtil.isNotEmpty(npDzAdChannels)) {
            Map<Long, NpDzAdChannel> dzAdChannelMap = npDzAdChannels.stream().collect(Collectors.toMap(NpDzAdChannel::getId, r -> r));
            data.forEach(e -> {
                if (dzAdChannelMap.containsKey(e.getDzId())) {
                    e.setDzNumber(dzAdChannelMap.get(e.getDzId()).getDzNumber());
                }
            });
        }
        data.stream().filter(Objects::nonNull).forEach(item -> item.setUserCode(DesensitizeUtil.idToMask(item.getUserId())));
        return PageResult.success(data, Pagination.of(current, size, deptOrderDetail.getPagination().getTotal()));
    }

    public PageResult<List<SalesRefundDetailResp>> getSalesRefundDetail(Integer salesId, LocalDate flagTime,
                                                                        Integer periodType, Integer orderLevel,
                                                                        Long dzId, Integer current, Integer size) {
        PageResult<List<SalesRefundDetailResp>> salesRefundDetail = orderClient.getSalesRefundDetail(salesId, flagTime, periodType, orderLevel, dzId, current, size);
        if (salesRefundDetail.isFail()) {
            throw new BusinessException(salesRefundDetail);
        }
        return PageResult.success(salesRefundDetail.getData(), Pagination.of(current, size, salesRefundDetail.getPagination().getTotal()));
    }

    public PageResult<List<SalesRefundDetailResp>> getSalesValidRefundDetail(Integer salesId, LocalDate flagTime, Integer periodType, Integer orderLevel, Long dzId, Integer current, Integer size) {
        PageResult<List<SalesRefundDetailResp>> salesRefundDetail = orderClient.getSalesValidRefundDetail(salesId, flagTime, periodType, orderLevel, dzId, current, size);
        if (salesRefundDetail.isFail()) {
            throw new BusinessException(salesRefundDetail);
        }
        return PageResult.success(salesRefundDetail.getData(), Pagination.of(current, size, salesRefundDetail.getPagination().getTotal()));
    }

    public PageResult<List<SalesRefundDetailResp>> getDeptRefundDetail(Integer deptId, LocalDate flagTime, Integer periodType, Integer orderLevel, Long dzId, Integer current, Integer size) {
        PageResult<List<SalesRefundDetailResp>> deptRefundDetail = orderClient.getDeptRefundDetail(deptId, flagTime, periodType, orderLevel, dzId, current, size);
        if (deptRefundDetail.isFail()) {
            throw new BusinessException(deptRefundDetail);
        }
        return PageResult.success(deptRefundDetail.getData(), Pagination.of(current, size, deptRefundDetail.getPagination().getTotal()));
    }

    public PageResult<List<SalesRefundDetailResp>> getDeptValidRefundDetail(Integer deptId, LocalDate flagTime, Integer periodType, Integer orderLevel, Long dzId, Integer current, Integer size) {
        PageResult<List<SalesRefundDetailResp>> deptRefundDetail = orderClient.getDeptValidRefundDetail(deptId, flagTime, periodType, orderLevel, dzId, current, size);
        if (deptRefundDetail.isFail()) {
            throw new BusinessException(deptRefundDetail);
        }
        return PageResult.success(deptRefundDetail.getData(), Pagination.of(current, size, deptRefundDetail.getPagination().getTotal()));
    }

    public PageResult<List<ResourceDetailResp>> getSalesResourceDetail(Integer salesId, LocalDate flagTime,
                                                                       Integer periodType, Long dzId, Integer current,
                                                                       Integer size) {
        PageResult<List<ResourceDetailResp>> salesResourceDetail = workwxClient.getSalesResourceDetail(salesId, flagTime, periodType, dzId, current, size);
        if (salesResourceDetail.isFail()) {
            throw new BusinessException(salesResourceDetail);
        }
        return PageResult.success(salesResourceDetail.getData(), Pagination.of(current, size, salesResourceDetail.getPagination().getTotal()));
    }

    public PageResult<List<ResourceDetailResp>> getDeptResourceDetail(Integer deptId, LocalDate flagTime, Integer periodType, Long dzId, Integer current, Integer size) {
        PageResult<List<ResourceDetailResp>> deptResourceDetail = workwxClient.getDeptResourceDetail(deptId, flagTime, periodType, dzId, current, size);
        if (deptResourceDetail.isFail()) {
            throw new BusinessException(deptResourceDetail);
        }
        return PageResult.success(deptResourceDetail.getData(), Pagination.of(current, size, deptResourceDetail.getPagination().getTotal()));
    }

    /*退款订单列表*/
    public PageResult<List<SalesRelateRefundResp>> getSalesRelateRefundList(Integer userId, List<Integer> creatorIds,
                                                                            Integer companyType, Integer refundStatus, String refundNumber, String orderNumber,
                                                                            LocalDate createStartTime, LocalDate createEndTime,
                                                                            LocalDate refundStartTime, LocalDate refundEndTime,
                                                                            Integer productLevel, String refundStatusSet,
                                                                            Integer auditorId, Integer readStatus, RefundSignStatusEnum refundSignStatus,
                                                                            Integer current, Integer size) {
        List<Integer> salesIds = new ArrayList<>();
        if (userId != null) {
            BaseResult<List<Integer>> userIdByUserId = userClient.getUserIdByUserId(userId);
            salesIds = userIdByUserId.getData();
        }
        PageResult<List<SalesRelateRefundResp>> salesRelateRefundList = orderClient.getSalesRelateRefundList(salesIds, creatorIds,
                companyType, refundStatus, refundNumber, orderNumber,
                createStartTime, createEndTime,
                refundStartTime, refundEndTime,
                productLevel, refundStatusSet,
                auditorId, readStatus, refundSignStatus,
                current, size);
        if (salesRelateRefundList.isFail()) {
            throw new BusinessException(salesRelateRefundList);
        }

        Collection<Integer> userIdList = new HashSet<>();
        salesRelateRefundList.getData().forEach(e -> {
            if (e.getUserId() != null) {
                userIdList.add(e.getUserId());
            }
            if (e.getHandler() != null) {
                userIdList.add(e.getHandler());
            }
            if (e.getCreatorId() != null) {
                userIdList.add(e.getCreatorId());
            }
        });
        BaseResult<Map<Integer, UcUsers>> batchGetUserMapResult = userClient.batchGetUserMap(BatchReq.create(userIdList));
        Map<Integer, UcUsers> usersMap = batchGetUserMapResult.isSuccess() ? batchGetUserMapResult.getData() : new HashMap<>();
        salesRelateRefundList.getData().forEach(s -> {
            if (s.getUserId() != null) {
                s.setUserNickName(usersMap.get(s.getUserId()).getNickName());
                s.setHeadImgUrl(usersMap.get(s.getUserId()).getAvatarUrl());
                s.setUserCode(DesensitizeUtil.idToMask(s.getUserId()));
            }
            if (s.getHandler() != null) {
                s.setHandlerName(usersMap.get(s.getHandler()).getRealName());
            }
            if (s.getCreatorId() != null) {
                s.setCreatorName(usersMap.get(s.getCreatorId()).getRealName());
            }
        });

        return PageResult.success(salesRelateRefundList.getData(), Pagination.of(current, size, salesRelateRefundList.getPagination().getTotal()));
    }

    /*关闭退款订单*/
    public RsRefundFlowRecord closeRefundOrder(Integer userId, Integer refundId) {
        BaseResult<RsRefundFlowRecord> rsRefundFlowRecordBaseResult = orderClient.closeRefundOrder(userId, refundId);
        if (rsRefundFlowRecordBaseResult.isFail()) {
            throw new BusinessException(rsRefundFlowRecordBaseResult);
        }
        return rsRefundFlowRecordBaseResult.getData();
    }

    public List<RefundProcessResp> getRefundProcessRespListByRefundId(Integer refundId) {
        BaseResult<List<RefundFlowRecordResp>> rsRefundFlowRecordListByRefundId = orderClient.getRsRefundFlowRecordListByRefundId(refundId);
        if (rsRefundFlowRecordListByRefundId.isFail()) {
            throw new BusinessException(rsRefundFlowRecordListByRefundId);
        }
        List<RefundFlowRecordResp> refundFlowRecordList = rsRefundFlowRecordListByRefundId.getData();
        List<RefundProcessResp> refundProcessList = new ArrayList<>();
        Set<Integer> userIds = new HashSet<>();
        refundFlowRecordList.forEach(e -> {
            RefundProcessResp refundProcessResp = new RefundProcessResp(
                    e.getId(),
                    e.getRefundId(),
                    e.getOperateType(),
                    e.getOperateId(),
                    e.getCreateTime()
            );
            refundProcessList.add(refundProcessResp);
            userIds.add(e.getOperateId());
        });
        BaseResult<List<UcUsers>> batchGetUserList = userClient.batchGetUserList(userIds);
        if (batchGetUserList.isFail()) {
            throw new BusinessException(batchGetUserList);
        }
        List<UcUsers> userList = batchGetUserList.getData();
        List<RefundProcessResp> refundProcessListResp = refundProcessList.stream().map(refundProces -> userList.stream()
                        .filter(user -> Objects.equals(refundProces.getOperateId(), user.getId()))
                        .findFirst()
                        .map(user -> {
                            refundProces.setOperator(user.getRealName());
                            return refundProces;
                        }).orElse(refundProces))
                .collect(Collectors.toList());
        return refundProcessListResp;
    }

    public OrderDetailResp getOrderDetailRespByRefundId(Integer refundId, Integer orderId) {
        BaseResult<OrderDetailResp> orderDetailRespByRefundId = orderClient.getOrderDetailRespByRefundId(refundId, orderId);
        if (orderDetailRespByRefundId.isFail()) {
            throw new BusinessException(orderDetailRespByRefundId);
        }
        OrderDetailResp orderDetailResp = orderDetailRespByRefundId.getData();
        if (ObjectUtil.isEmpty(orderDetailResp)) {
            return null;
        }
        String userName = null;
        String salesName = null;
        if (orderDetailResp.getUserId() != null) {
            BaseResult<UcUsers> user = userClient.getUser(orderDetailResp.getUserId());
            userName = user.getData().getNickName();
        }
        if (orderDetailResp.getSalesId() != null) {
            BaseResult<UcUsers> sales = userClient.getUser(orderDetailResp.getSalesId());
            salesName = sales.getData().getRealName();
        }
        orderDetailResp.setUserName(userName);
        orderDetailResp.setSalesName(salesName);
        BaseResult<Integer> refundAmountResult = orderClient.getRefundAmountByOrderId(orderDetailResp.getId());
        if (refundAmountResult.isFail()) {
            throw new BusinessException(refundAmountResult);
        }
        Integer refundAmount = refundAmountResult.getData();
        orderDetailResp.setRefundAmount(refundAmount == null ? 0 : NumberUtil.div(Long.valueOf(refundAmount), Long.valueOf(100), 2,
                RoundingMode.HALF_UP).doubleValue());
        return orderDetailResp;
    }

    public List<RefundCommentResp> getRefundCommentRespByRefundId(Integer refundId) {
        BaseResult<List<RsCourseRefundComment>> rsCourseRefundComment = orderClient.getRsCourseRefundCommentByRefundId(refundId);
        if (rsCourseRefundComment.isFail()) {
            throw new BusinessException(rsCourseRefundComment);
        }
        List<RsCourseRefundComment> rsCourseRefundCommentList = rsCourseRefundComment.getData();
        List<RefundCommentResp> refundCommentRespList = new ArrayList<>();
        Set<Integer> userIds = new HashSet<>();
        rsCourseRefundCommentList.forEach(e -> {
            RefundCommentResp refundCommentResp = new RefundCommentResp();
            BeanUtils.copyProperties(e, refundCommentResp);
            refundCommentRespList.add(refundCommentResp);
            userIds.add(e.getUserID());
        });
        BaseResult<List<UcUsers>> batchGetUserList = userClient.batchGetUserList(userIds);
        if (batchGetUserList.isFail()) {
            throw new BusinessException(batchGetUserList);
        }
        List<UcUsers> userList = batchGetUserList.getData();
        List<RefundCommentResp> refundCommentResp = refundCommentRespList.stream().map(refundComment -> userList.stream()
                        .filter(user -> refundComment.getUserID().equals(user.getId()))
                        .findFirst()
                        .map(user -> {
                            refundComment.setUserName(user.getRealName());
                            refundComment.setAvatarUrl(user.getAvatarUrl());
                            return refundComment;
                        }).orElse(refundComment))
                .collect(Collectors.toList());
        return refundCommentResp;
    }

    public BaseResult<String> createCourseRefundComment(CreateCourseRefundCommentReq req) {
        BaseResult<String> courseRefundComment = orderClient.createCourseRefundComment(req);
        if (courseRefundComment.isFail()) {
            throw new BusinessException(courseRefundComment);
        }
        return courseRefundComment;
    }

    public RefundOrderResp getRefundOrderRespByRefundId(Integer refundId) {
        BaseResult<RefundOrderResp> refundOrderResp = orderClient.getRefundOrderRespByRefundId(refundId);
        if (refundOrderResp.isFail()) {
            throw new BusinessException(refundOrderResp);
        }
        String orderNumber = refundOrderResp.orElse(new RefundOrderResp()).getOrderNumber();
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        List<RsCourseRefundOrderSub> refundOrderSubs = orderClient.findRefundOrderSubList(orderId, null, null, null, null).orElse(new ArrayList<RsCourseRefundOrderSub>());
        List<CourseSubOrder> subOrders = orderClient.getSubOrderList(orderId, null).orElse(new ArrayList<>());
        Set<Long> merchantIds = subOrders.stream().map(CourseSubOrder::getMerchantId).collect(Collectors.toSet());
        List<RsMerchantInfo> rsMerchantInfos = orderClient.getMerchantInfoList(merchantIds).orElse(new ArrayList<>());
        boolean isTransfer = rsMerchantInfos.stream()
                .anyMatch(e -> e.getPlatform() == PaymentPlatformEnum.PP_Transfer.getCode());
        RsRefundBankInfo refundBankInfo = orderClient.orderRefundBankInfo(orderId).orElse(new RsRefundBankInfo());
        boolean isExpired = false;

        for (RsCourseRefundOrderSub refundOrderSub : refundOrderSubs) {
            Instant refundCreateTime = refundOrderSub.getGmtCreate();
            Integer subOrderId = refundOrderSub.getSubOrderId();

            CourseSubOrder correspondingSubOrder = subOrders.stream()
                    .filter(subOrder -> subOrder.getId().equals(subOrderId))
                    .findFirst()
                    .orElse(null);

            if (correspondingSubOrder != null) {
                Instant payTime = correspondingSubOrder.getPayTime();
                long daysDifference = ChronoUnit.DAYS.between(payTime, refundCreateTime);
                if (daysDifference >= 365) {
                    isExpired = true;
                    break;
                }
            }
        }
        refundOrderResp.getData().setIsTransfer(isTransfer);
        refundOrderResp.getData().setIsExpired(isExpired);
        refundOrderResp.getData().setRefundBankInfo(refundBankInfo);
        return refundOrderResp.getData();
    }

    public BaseResult<List<RefundOrderResp>> getRefundOrderRecord(Integer orderId, Integer refundStatus) {
        BaseResult<List<RefundOrderResp>> refundOrderRecord = orderClient.getRefundOrderRecord(orderId, refundStatus);
        if (refundOrderRecord.isFail()) {
            throw new BusinessException(refundOrderRecord);
        }
        return refundOrderRecord;
    }

    public BaseResult<String> distributeRefundOrder(Integer refundId, Integer auditorId, Integer assignId) {
        BaseResult<String> stringBaseResult = orderClient.distributeRefundOrder(refundId, auditorId, assignId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<String> distributeRefundOrderAgain(Integer newAuditorId, Integer oldAuditorId, Integer assignId) {
        BaseResult<String> stringBaseResult = orderClient.distributeRefundOrderAgain(newAuditorId, oldAuditorId, assignId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<String> auditRefundOrder(Integer refundId, Integer auditorId, Boolean isCloseService) {
        BaseResult<String> stringBaseResult = orderClient.auditRefundOrder(refundId, auditorId, isCloseService);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<String> confirmRefundOrder(Integer refundId, Integer confirmId) {
        BaseResult<String> stringBaseResult = orderClient.confirmRefundOrder(refundId, confirmId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<String> startConfirmSignRefundOrder(Integer refundId) {
        BaseResult<String> stringBaseResult = orderClient.startConfirmSignRefundOrder(refundId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<List<RefundPlanResp>> getRefundPlan(Integer orderId, Integer refundId, Integer refundStatus) {
        BaseResult<List<RefundPlanResp>> refundPlan = orderClient.getRefundPlan(orderId, refundId, refundStatus);
        if (refundPlan.isFail()) {
            throw new BusinessException(refundPlan);
        }
        return refundPlan;
    }

    public BaseResult<String> signAgain(Integer refundOrderSignId, Integer signId, Integer refundId) {
        BaseResult<String> stringBaseResult = orderClient.signAgain(refundOrderSignId, signId, refundId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<String> auditSign(Integer refundId) {
        BaseResult<String> stringBaseResult = orderClient.auditSign(refundId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    /*发起退款接口*/
    public BaseResult<String> wxPayRefundService(Integer refundId, Integer operatorId) {
        BaseResult<String> stringBaseResult = orderClient.wxPayRefund(refundId, operatorId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<String> startSign(Integer refundId) {
        BaseResult<String> stringBaseResult = orderClient.startSign(refundId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        return stringBaseResult;
    }

    public BaseResult<List<OrderExtInfoDto>> getOrderExtInfoDto(Integer orderId, Integer refundAmount) {
        BaseResult<List<OrderExtInfoDto>> orderExtInfoDto = orderClient.getOrderExtInfoDto(orderId, refundAmount);
        if (orderExtInfoDto.isFail()) {
            throw new BusinessException(orderExtInfoDto);
        }
        return orderExtInfoDto;
    }

    public PageResult<List<SubOrderDtoResp>> getSubOrderByFilter(Integer companyType, Integer orderId,
                                                                 Integer orderStatus,
                                                                 List<Integer> salesIds, Instant startCreateTime,
                                                                 Instant endCreateTime, Instant startPayTime,
                                                                 Instant endPayTime, String searchContent,
                                                                 Integer current, Integer size) {
        PageResult<List<SubOrderDto>> records = orderClient.getSubOrderByFilter(companyType, orderId, orderStatus,
                salesIds, false, startCreateTime, endCreateTime, startPayTime, endPayTime, searchContent, current,
                size);
        if (records.isFail()) {
            throw new BusinessException(records);
        }
        List<SubOrderDto> subOrderInfoDtos = records.getData();
        List<Integer> markOperatorId = new ArrayList<>();
        subOrderInfoDtos.forEach(o -> {
            if (o.getSubOrderInfo().getMarkOperatorId() != null) {
                markOperatorId.add(o.getSubOrderInfo().getMarkOperatorId());
            }
        });
        Set<Integer> wxIdSet = subOrderInfoDtos.stream().map(e -> e.getOrderInfo().getWxId())
                .filter(Objects::nonNull).collect(Collectors.toSet());

        // 用户信息
        BaseResult<List<UcUsers>> markOperatorsResult = userClient.batchGetUserList(markOperatorId);
        List<cn.shrise.radium.wxservice.entity.UcWxExt> wxInfoList = wxClient.batchGetUserWxExtByUserOrWxId(null, new ArrayList<>(wxIdSet)).orElse(emptyList());
        Map<Integer, cn.shrise.radium.wxservice.entity.UcWxExt> wxInfoMap = wxInfoList.stream()
                .collect(Collectors.toMap(cn.shrise.radium.wxservice.entity.UcWxExt::getId, Function.identity()));
        if (markOperatorsResult.isFail()) {
            throw new BusinessException(markOperatorsResult);
        }
        List<UcUsers> markOperators = markOperatorsResult.getData();
        Map<Integer, UcUsers> markOperatorsMap = markOperators.stream().collect(Collectors.toMap(UcUsers::getId,
                x -> x));

        List<SubOrderDtoResp> res = new ArrayList<>();
        subOrderInfoDtos.forEach(o -> {
            SubOrderDtoResp info = new SubOrderDtoResp();
            OrderInfoResp orderInfoResp = new OrderInfoResp();
            SubOrderInfoResp subOrderInfoResp = new SubOrderInfoResp();
            BeanUtils.copyProperties(o.getOrderInfo(), orderInfoResp);
            orderInfoResp.setUserCode(DesensitizeUtil.idToMask(o.getOrderInfo().getUserId()));
            BeanUtils.copyProperties(o.getSubOrderInfo(), subOrderInfoResp);
            if (o.getTransferOrderFlowInfo() != null) {
                TransferOrderResp transferOrderResp = new TransferOrderResp();
                BeanUtils.copyProperties(o.getTransferOrderFlowInfo(), transferOrderResp);
                info.setTransferOrderInfo(transferOrderResp);
            }
            if (o.getMerchantInfo() != null) {
                MerchantInfoResp merchantInfoResp = new MerchantInfoResp();
                BeanUtils.copyProperties(o.getMerchantInfo(), merchantInfoResp);
                info.setMerchantInfo(merchantInfoResp);
            }
            info.setOrderInfo(orderInfoResp);
            info.setSubOrderInfo(subOrderInfoResp);
            if (markOperatorsMap.get(o.getSubOrderInfo().getMarkOperatorId()) != null) {
                UserInfoResp userInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(markOperatorsMap.get(o.getSubOrderInfo().getMarkOperatorId()), userInfoResp);
                info.setMarkOperatorInfo(userInfoResp);
            }
            Integer wxId = orderInfoResp.getWxId();
            WxInfo wxInfo = wxId != null ? WxInfo.of(wxInfoMap.get(wxId)) : null;
            info.setWxInfo(wxInfo);
            res.add(info);
        });

        return PageResult.success(res, Pagination.of(current, size, records.getPagination().getTotal()));
    }

    public BaseResult<List<SubOrderDtoResp>> getSubOrderByFilter(Integer orderId, Integer orderStatus) {
        BaseResult<List<SubOrderDto>> records = orderClient.findSubOrderList(orderId, orderStatus);
        if (records.isFail()) {
            throw new BusinessException(records);
        }
        List<SubOrderDto> subOrderInfoDtos = records.getData();
        List<Integer> markOperatorId = new ArrayList<>();
        subOrderInfoDtos.forEach(o -> {
            if (o.getSubOrderInfo().getMarkOperatorId() != null) {
                markOperatorId.add(o.getSubOrderInfo().getMarkOperatorId());
            }
        });
        // 用户信息
        BaseResult<List<UcUsers>> markOperatorsResult = userClient.batchGetUserList(markOperatorId);
        if (markOperatorsResult.isFail()) {
            throw new BusinessException(markOperatorsResult);
        }
        List<UcUsers> markOperators = markOperatorsResult.getData();
        Map<Integer, UcUsers> markOperatorsMap = markOperators.stream().collect(Collectors.toMap(UcUsers::getId,
                x -> x));

        List<SubOrderDtoResp> res = new ArrayList<>();
        subOrderInfoDtos.forEach(o -> {
            SubOrderDtoResp info = new SubOrderDtoResp();
            OrderInfoResp orderInfoResp = new OrderInfoResp();
            SubOrderInfoResp subOrderInfoResp = new SubOrderInfoResp();
            BeanUtils.copyProperties(o.getOrderInfo(), orderInfoResp);
            BeanUtils.copyProperties(o.getSubOrderInfo(), subOrderInfoResp);
            if (o.getTransferOrderFlowInfo() != null) {
                TransferOrderResp transferOrderResp = new TransferOrderResp();
                BeanUtils.copyProperties(o.getTransferOrderFlowInfo(), transferOrderResp);
            }
            info.setOrderInfo(orderInfoResp);
            info.setSubOrderInfo(subOrderInfoResp);
            if (markOperatorsMap.get(o.getSubOrderInfo().getMarkOperatorId()) != null) {
                UserInfoResp userInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(markOperatorsMap.get(o.getSubOrderInfo().getMarkOperatorId()), userInfoResp);
                info.setMarkOperatorInfo(userInfoResp);
            }

            res.add(info);
        });

        return BaseResult.success(res);
    }

    private int calculatePaidAmount(List<SubOrderInfo> subOrderList) {
        if (ObjectUtils.isEmpty(subOrderList)) {
            return 0;
        }
        return subOrderList.stream()
                .filter(subOrderInfo -> Objects.equals(subOrderInfo.getStatus(), OrderStatusConstant.PASSED.getValue()))
                .mapToInt(SubOrderInfo::getAmount)
                .sum();
    }

    public PageResult<List<OrderDtoResp>> getOrderByFilter(Integer companyType, Integer auditorId, List<Integer> salesIds,
                                                           LocalDateTime startPayTime, LocalDateTime endPayTime, Integer feedbackStatus,
                                                           Integer productLevel, List<Integer> auditStatus, Integer autoAuditStatus, String searchContent, Integer current, Integer size) {
        PageResult<List<OrderInfoDto>> records = orderClient.getOrderHgAuditManage(companyType, auditorId, salesIds,
                startPayTime, endPayTime, feedbackStatus, productLevel, auditStatus, autoAuditStatus, searchContent, current, size);
        if (records.isFail()) {
            throw new BusinessException(records);
        }
        List<OrderInfoDto> orderInfoDtos = records.getData();
        List<Integer> userIds = new ArrayList<>();
        orderInfoDtos.forEach(o -> {
            userIds.add(o.getOrderInfo().getUserId());
            userIds.add(o.getOrderInfo().getSalesId());
            if (ObjectUtil.isNotEmpty(o.getOrderFlow()) && ObjectUtil.isNotEmpty(o.getOrderFlow().getHgAuditorId())) {
                userIds.add(o.getOrderFlow().getHgAuditorId());
            }
        });
        // 用户信息
        BaseResult<Map<Integer, UcUsers>> userMapResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        if (userMapResult.isFail()) {
            throw new BusinessException(userMapResult);
        }
        Map<Integer, UcUsers> userMap = userMapResult.getData();

        List<OrderDtoResp> res = new ArrayList<>();
        orderInfoDtos.forEach(o -> {
            OrderDtoResp info = new OrderDtoResp();
            OrderInfoResp orderInfoResp = new OrderInfoResp();
            SkuInfoResp skuInfoResp = new SkuInfoResp();
            BeanUtils.copyProperties(o.getOrderInfo(), orderInfoResp);
            orderInfoResp.setUserCode(DesensitizeUtil.idToMask(o.getOrderInfo().getUserId()));
            orderInfoResp.setSubOrderAmountSum(o.getSubOrderAmountSum());
            Long feedbackId = ObjectUtil.isEmpty(o.getOrderFeedback()) ? null : o.getOrderFeedback().getId();
            Integer fStatus = getFeedbackStatus(o.getSkuInfo().getProductLevel(), feedbackId, o.getArtificialFeedback());
            orderInfoResp.setFeedBackStatus(fStatus);
            orderInfoResp.setFeedBackUrl(ObjectUtil.isEmpty(o.getOrderFeedback()) ? null : o.getOrderFeedback().getFeedbackUrl());
            info.setOrderInfo(orderInfoResp);
            BeanUtils.copyProperties(o.getSkuInfo(), skuInfoResp);
            info.setSkuInfo(skuInfoResp);
            if (o.getOrderSignInfo() != null) {
                OrderSignInfoResp orderSignInfoResp = new OrderSignInfoResp();
                BeanUtils.copyProperties(o.getOrderSignInfo(), orderSignInfoResp);
                info.setOrderSignInfo(orderSignInfoResp);
            }
            if (userMap.get(o.getOrderInfo().getUserId()) != null) {
                UserInfoResp userInfoResp = new UserInfoResp();
                UcUsers ucUsers = userMap.get(o.getOrderInfo().getUserId());
                BeanUtils.copyProperties(ucUsers, userInfoResp);
                userInfoResp.setUserCode(DesensitizeUtil.idToMask(ucUsers.getId()));
                info.setUserInfoResp(userInfoResp);
            }
            if (userMap.get(o.getOrderInfo().getSalesId()) != null) {
                UserInfoResp salesInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(userMap.get(o.getOrderInfo().getSalesId()), salesInfoResp);
                info.setSalesInfoResp(salesInfoResp);
            }
            if (ObjectUtil.isNotEmpty(o.getOrderFlow())) {
                if (userMap.get(o.getOrderFlow().getHgAuditorId()) != null) {
                    UserInfoResp auditorInfoResp = new UserInfoResp();
                    BeanUtils.copyProperties(userMap.get(o.getOrderFlow().getHgAuditorId()), auditorInfoResp);
                    info.setAuditorInfoResp(auditorInfoResp);
                }
                info.getOrderInfo().setAuditStatus(o.getOrderFlow().getAuditStatus());
                info.getOrderInfo().setAutoAuditStatus(o.getOrderFlow().getAutoAuditStatus());
                info.getOrderInfo().setReason(o.getOrderFlow().getReason());
            }

            res.add(info);
        });

        return PageResult.success(res, Pagination.of(current, size, records.getPagination().getTotal()));
    }

    public PageResult<List<OrderDtoResp>> getOrderByFilter(Integer companyType, Integer orderStatus,
                                                           List<Integer> salesIds,
                                                           Instant startCreateTime,
                                                           Instant endCreateTime, Instant startPayTime,
                                                           Instant endPayTime, String searchContent,
                                                           Integer userId, Integer productLevel, List<Integer> skuList, Boolean isMarkWxUser,
                                                           Integer current, Integer size) {
        PageResult<List<OrderInfoDto>> records = orderClient.getOrderByFilter(companyType, orderStatus, null,
                salesIds, false, startCreateTime, endCreateTime, startPayTime, endPayTime, searchContent, userId, productLevel, skuList, isMarkWxUser, current,
                size);
        if (records.isFail()) {
            throw new BusinessException(records);
        }

        List<OrderInfoDto> orderInfoDtos = records.getData();
        List<Integer> recordSalesIds = new ArrayList<>();
        List<Integer> userIds = new ArrayList<>();
        Set<Long> dzIds = new HashSet<>();
        orderInfoDtos.forEach(o -> {
            recordSalesIds.add(o.getOrderInfo().getSalesId());
            userIds.add(o.getOrderInfo().getUserId());
            userIds.add(o.getOrderInfo().getSalesId());
            if (ObjectUtil.isNotNull(o.getOrderExt()) && ObjectUtil.isNotNull(o.getOrderExt().getDzId())) {
                dzIds.add(o.getOrderExt().getDzId());
            }
        });
        // 用户信息
        BaseResult<Map<Integer, UcUsers>> userMapResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        if (userMapResult.isFail()) {
            throw new BusinessException(userMapResult);
        }
        Map<Integer, UcUsers> userMap = userMapResult.getData();
        // 用户微信信息
        BaseResult<List<UcWxExt>> wxResult = userClient.getUnionIdsByUserId(userIds);
        if (wxResult.isFail()) {
            throw new BusinessException(wxResult);
        }
        List<UcWxExt> wxs = wxResult.getData();
        Map<Integer, UcWxExt> wxMap = wxs.stream().collect(Collectors.toMap(UcWxExt::getUserId, x -> x, (o, e) -> o));
        // 销售部门
        BaseResult<Map<Integer, String>> deptInfoResult = userClient.getDeptListByUsers(recordSalesIds);
        if (deptInfoResult.isFail()) {
            throw new BusinessException(deptInfoResult);
        }
        Map<Integer, String> deptInfosMap = deptInfoResult.getData();
        Map<Integer, UcRole> roleMap = userClient.batchGetUserRoleMap(BatchReq.create(recordSalesIds)).getData();
        // dz渠道信息
        BaseResult<List<NpDzAdChannel>> dzChannelResult = workwxClient.getNpDzAdChannelByIds(dzIds);
        if (dzChannelResult.isFail()) {
            throw new BusinessException(dzChannelResult);
        }
        List<NpDzAdChannel> dzChannels = dzChannelResult.getData();
        Map<Long, NpDzAdChannel> dzChannelMap = dzChannels.stream().collect(Collectors.toMap(NpDzAdChannel::getId, x -> x));

        List<OrderDtoResp> res = new ArrayList<>();
        orderInfoDtos.forEach(o -> {
            List<OrderStepResp> steps = new ArrayList<>();
            OrderDtoResp info = new OrderDtoResp();
            OrderInfoResp orderInfoResp = new OrderInfoResp();
            SkuInfoResp skuInfoResp = new SkuInfoResp();
            BeanUtils.copyProperties(o.getOrderInfo(), orderInfoResp);
            orderInfoResp.setUserCode(DesensitizeUtil.idToMask(o.getOrderInfo().getUserId()));
            orderInfoResp.setSubOrderAmountSum(o.getSubOrderAmountSum());
            orderInfoResp.setCallId(o.getCallId());
            info.setOrderInfo(orderInfoResp);
            steps.add(new OrderStepResp("创建订单", orderInfoResp.getCreateTime()));
            if (orderInfoResp.getOrderStatus().equals(OrderStatusEnum.PASSED.getValue())) {
                steps.add(new OrderStepResp("开通", orderInfoResp.getPayTime()));
            } else if (orderInfoResp.getOrderStatus().equals(OrderStatusEnum.FROZEN.getValue())) {
                steps.add(new OrderStepResp("冻结订单", orderInfoResp.getFrozenTime()));
            } else if (orderInfoResp.getOrderStatus().equals(OrderStatusEnum.CLOSED.getValue())) {
                steps.add(new OrderStepResp("关闭订单", orderInfoResp.getCloseTime()));
            }
            BeanUtils.copyProperties(o.getSkuInfo(), skuInfoResp);
            info.setSkuInfo(skuInfoResp);
            if (o.getOrderSignInfo() != null) {
                OrderSignInfoResp orderSignInfoResp = new OrderSignInfoResp();
                BeanUtils.copyProperties(o.getOrderSignInfo(), orderSignInfoResp);
                info.setOrderSignInfo(orderSignInfoResp);
                if (ObjectUtil.isNotNull(orderSignInfoResp.getIsSigned()) && orderSignInfoResp.getIsSigned()) {
                    steps.add(new OrderStepResp("签字", orderSignInfoResp.getSignTime()));
                }
            }
            if (o.getCouponInfo() != null) {
                CouponResp couponResp = new CouponResp();
                BeanUtils.copyProperties(o.getCouponInfo(), couponResp);
                info.setCouponInfo(couponResp);
            }
            if (userMap.get(o.getOrderInfo().getUserId()) != null) {
                UserInfoResp userInfoResp = new UserInfoResp();
                UcUsers ucUsers = userMap.get(o.getOrderInfo().getUserId());
                BeanUtils.copyProperties(ucUsers, userInfoResp);
                userInfoResp.setUserCode(DesensitizeUtil.idToMask(ucUsers.getId()));
                info.setUserInfoResp(userInfoResp);
            }
            if (userMap.get(o.getOrderInfo().getSalesId()) != null) {
                UserInfoResp salesInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(userMap.get(o.getOrderInfo().getSalesId()), salesInfoResp);
                if (roleMap.containsKey(o.getOrderInfo().getSalesId())) {
                    salesInfoResp.setRoleName(roleMap.get(o.getOrderInfo().getSalesId()).getName());
                }
                info.setSalesInfoResp(salesInfoResp);
            }
            if (wxMap.get(o.getOrderInfo().getUserId()) != null) {
                WxExtResp wxExtResp = new WxExtResp();
                UcWxExt ucWxExt = wxMap.get(o.getOrderInfo().getUserId());
                BeanUtils.copyProperties(ucWxExt, wxExtResp);
                wxExtResp.setUserCode(DesensitizeUtil.idToMask(ucWxExt.getUserId()));
                info.setWxExtInfo(wxExtResp);
            }
            if (deptInfosMap.get(o.getOrderInfo().getSalesId()) != null) {
                DeptInfoResp deptInfoResp = new DeptInfoResp();
                deptInfoResp.setDeptInfo(deptInfosMap.get(o.getOrderInfo().getSalesId()));
                info.setDeptInfoResp(deptInfoResp);
            }
            if (o.getOrderExt() != null) {
                orderInfoResp.setFriendRecordId(o.getOrderExt().getFriendRecordId());
                if (o.getOrderExt().getDzId() != null && dzChannelMap.get(o.getOrderExt().getDzId()) != null) {
                    DzAdChannelResp dzAdChannelResp = new DzAdChannelResp();
                    BeanUtils.copyProperties(dzChannelMap.get(o.getOrderExt().getDzId()), dzAdChannelResp);
                    info.setDzInfo(dzAdChannelResp);
                }
                // 一档A标记2.0销售
                if (SkuProductLevelConstant.L1A.getValue().equals(o.getSkuInfo().getProductLevel())) {
                    if (ObjectUtil.isNotEmpty(o.getOrderExt().getMarkTime())) {
                        info.setIsMarkWxUser(ObjectUtil.isNotEmpty(o.getOrderExt().getMarkRelationId()));
                    }
                }
            }
            info.setSteps(steps);
            res.add(info);
        });

        return PageResult.success(res, Pagination.of(current, size, records.getPagination().getTotal()));
    }

    public PageResult<List<DeptVisibleOrderResp>> getOrderBySalesVisible(
            Integer companyType, Integer salesId, String searchContent, Boolean isRefund, LocalDate startPayTime, LocalDate endPayTime,
            Integer current, Integer size) {
        List<Integer> visibleSalesIds = userClient.findDeptChatVisibleSales(salesId).getData();
        //搜索内容可精确搜userCode或订单号
        Integer userId = null;
        String orderNumber = null;
        if (isValidUserCode(searchContent)) {
            userId = maskToId(searchContent);
        } else {
            orderNumber = searchContent;
        }
//        PageResult<List<RsCourseOrder>> records = orderClient.getOrderBySales(
//                companyType, BatchReq.create(visibleSalesIds), userId, orderNumber, isRefund, startPayTime, endPayTime, current, size);
        PageResult<List<RsCourseOrder>> records = orderClient.getCourseOrderSalesByFilter(
                companyType, BatchReq.create(visibleSalesIds), userId, orderNumber, isRefund, startPayTime, endPayTime, current, size);
        List<RsCourseOrder> orders = records.getData();
        List<Integer> salesIds = orders.stream().map(RsCourseOrder::getSalesId).filter(Objects::nonNull).collect(Collectors.toList());
        Set<Integer> orderIdSet = orders.stream().map(RsCourseOrder::getId).collect(Collectors.toSet());
        BatchReq<Integer> batchRefundReq = new BatchReq<>(orderIdSet);
        Map<Integer, UcUsers> salesMap = userClient.batchGetUserMap(BatchReq.create(salesIds)).getData();
        Map<Integer, String> deptMap = userClient.getDeptListByUsers(salesIds).getData();

        final BaseResult<List<RsCourseRefundOrder>> batchGetRefundResult = orderClient.batchGetRefund(batchRefundReq);
        final Set<Integer> refundSet = batchGetRefundResult.isSuccess() ?
                batchGetRefundResult.getData().stream()
                        .filter(e -> Objects.equals(e.getStatus(), OrderRefundStatus.PASSED))
                        .map(RsCourseRefundOrder::getOrderId)
                        .collect(Collectors.toSet())
                : Collections.emptySet();

        List<List<WorkWxFullContactUserRelation>> relations = orders.stream().map(order ->
                CompletableFuture.supplyAsync(() -> {
                    UcWxExt wxExtInfo = userClient.findByUserId(companyType, order.getUserId()).orElse(null);
                    if (wxExtInfo == null) return new ArrayList<WorkWxFullContactUserRelation>();
                    return workwxClient.getRelationByUnionSales(
                            companyType, order.getSalesId(), wxExtInfo.getUnionId()).orElse(new ArrayList<>());
                }, adminExecutor)
        ).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        List<DeptVisibleOrderResp> orderInfos = new ArrayList<>();
        for (int i = 0; i < orders.size(); i++) {
            RsCourseOrder order = orders.get(i);
            DeptVisibleOrderResp record = BeanUtil.copyProperties(order, DeptVisibleOrderResp.class);
            record.setSalesName(salesMap.get(order.getSalesId()).getRealName());
            record.setSalesNickName(salesMap.get(order.getSalesId()).getNickName());
            record.setIsRefund(refundSet.contains(order.getId()));
            record.setUserId(order.getUserId());
            record.setUserCode(DesensitizeUtil.idToMask(order.getUserId()));
            if (deptMap.containsKey(order.getSalesId())) {
                record.setDeptName(deptMap.get(order.getSalesId()));
            }
            List<WorkWxFullContactUserRelation> fullRelations = relations.get(i);
            if (ObjectUtil.isNotEmpty(fullRelations)) {
                List<FullRelationInfoResp> relationInfos = fullRelations.stream().map(fr -> FullRelationInfoResp.builder()
                        .id(fr.getRelation().getPkId())
                        .wxAccount(fr.getRelation().getWxAccount())
                        .externalUserId(fr.getRelation().getExternalUserId())
                        .accountType(fr.getRelation().getAccountType())
                        .contactName(fr.getContact().getName())
                        .userEnabled(fr.getRelation().getUserEnabled())
                        .contactEnabled(fr.getRelation().getContactEnabled())
                        .userName(fr.getWorkWxUser().getName())
                        .createTime(fr.getRelation().getCreateTime())
                        .build()).collect(Collectors.toList());
                record.setRelations(relationInfos);
            }

            orderInfos.add(record);
        }
        return PageResult.success(orderInfos, Pagination.of(current, size, records.getPagination().getTotal()));
    }

    public BaseResult<List<RsCourseOrderSign>> getAllOrderSignByOrderId(Integer orderId) {
        return orderClient.getAllOrderSignByOrderId(orderId);
    }

    public List<Option> getMerchantList(Integer companyType, Integer payType) {
        BaseResult<List<Merchant>> result = orderClient.getMerchantList(companyType, payType);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        return result.getData()
                .stream()
                .map(merchant -> Option.builder()
                        .name(merchant.getName())
                        .value(merchant.getMchType())
                        .build())
                .collect(Collectors.toList());
    }

    public BaseResult<ErrorConstant> updateAvoidSign(Integer orderId) {
        BaseResult<Integer> refundAmountResult = orderClient.getRefundAmountByOrderId(orderId);
        if (refundAmountResult.isFail()) {
            throw new BusinessException(refundAmountResult);
        }
        Integer refundAmount = refundAmountResult.getData();
        BaseResult<Integer> allPaidAmountResult = orderClient.getAllSubPaidAmount(orderId);
        if (allPaidAmountResult.isFail()) {
            throw new BusinessException(allPaidAmountResult);
        }
        Integer allPaidAmount = allPaidAmountResult.getData();

        if (refundAmount == null || !refundAmount.equals(allPaidAmount)) {
            throw new BusinessException(ErrorConstant.ORDER_NOT_REFUND);
        }
        BaseResult<RsCourseOrderSign> orderSignResult = orderClient.getOrderSignAuditInfo(orderId);
        if (orderSignResult.isFail()) {
            throw new BusinessException(orderSignResult);
        }
        RsCourseOrderSign orderSign = orderSignResult.getData();
        if (orderSign != null && orderSign.getIsSigned() != null && orderSign.getIsSigned()) {
            throw new BusinessException(ErrorConstant.ORDER_HAS_SIGNED);
        }
        BaseResult<OsErrorCode> res = orderClient.updateAvoidSign(orderId, true);
        if (res.isSuccess()) {
            return BaseResult.of(ErrorConstant.SUCCESS);
        } else {
            return BaseResult.of(ErrorConstant.FAILURE);
        }
    }

    public BaseResult<ErrorConstant> subOrderMarkRefund(Integer subOrderId, Integer userId) {
        BaseResult<CourseSubOrder> subOrderRes = orderClient.getSubOrder(subOrderId);
        if (subOrderRes.isFail()) {
            throw new BusinessException(subOrderRes);
        }
        CourseSubOrder subOrder = subOrderRes.getData();
        if (!subOrder.getOrderStatus().equals(OrderStatusEnum.PASSED.getValue())) {
            return BaseResult.of(ErrorConstant.CHECK_ORDER_ERROR9);
        }
        BaseResult<RsCourseOrder> orderRes = orderClient.getOrder(subOrder.getOrderId());
        if (orderRes.isFail()) {
            throw new BusinessException(orderRes);
        }
        RsCourseOrder order = orderRes.getData();
        if (order.getOrderStatus().equals(OrderStatusEnum.PASSED.getValue())) {
            return BaseResult.of(ErrorConstant.CHECK_ORDER_ERROR10);
        }
        // 查询退款状态
        Boolean isRefund = false;
        BaseResult<Boolean> result = null;
        RsMerchantInfo merchantInfo = orderClient.getMerchantInfo(subOrder.getMerchantId()).orElseThrow();
        if (Objects.equals(merchantInfo.getPlatform(), PP_Wx.getCode()) && Objects.equals(merchantInfo.getChannelType(), PCT_Origin.getCode())) {
            result = orderClient.checkWechatPayRefundStatus(subOrder.getMerchantId(), subOrder.getNumber());
        } else if (Objects.equals(merchantInfo.getPlatform(), PP_Ali.getCode()) && Objects.equals(merchantInfo.getChannelType(), PCT_Origin.getCode())) {
            result = orderClient.checkAlipayRefundStatus(subOrder.getMerchantId(), subOrder.getNumber());
        } else {
            isRefund = true;
        }
        if (result != null) {
            if (result.isSuccess()) {
                isRefund = result.getData();
            } else {
                throw new BusinessException(result);
            }
        }
        if (isRefund) {
            UpdateSubOrderReq build = UpdateSubOrderReq.builder()
                    .markTime(Instant.now())
                    .orderStatus(OrderStatusEnum.REFUND.getValue())
                    .markOperatorId(userId)
                    .build();
            BaseResult<Void> res = orderClient.updateSubOrder(subOrderId, build);
            if (res.isSuccess()) {
                return BaseResult.of(ErrorConstant.SUCCESS);
            } else {
                return BaseResult.of(ErrorConstant.FAILURE);
            }
        }
        return BaseResult.of(ErrorConstant.FAILURE);
    }

    public BaseResult<Void> closeOrder(String orderNumber, Integer operatorId) {
        return orderClient.closeOrder(orderNumber, operatorId);
    }

    public BaseResult<String> transferRefund(RefundMarkReq req) {
        return orderClient.transferRefund(req);
    }

    public BaseResult<String> generateSignPdf(Integer orderId, Integer companyType) {
        return orderClient.generateSignPdf(orderId, companyType);
    }

    public BaseResult<List<OrderSkuInfoResp>> getMyOrderList(Integer companyType, Integer userId, Integer orderStatus) {

        List<OrderSkuInfo> orderSkuListResult = orderClient.getOrderSkuList(companyType, userId, orderStatus, false);
        List<OrderSkuInfoResp> skuInfoRespList = new ArrayList<>();
        if (ObjectUtil.isEmpty(orderSkuListResult)) {
            return BaseResult.success(skuInfoRespList);
        }
        Set<Integer> orderIdSet = orderSkuListResult.stream().map(x -> x.getRsCourseOrder().getId()).collect(Collectors.toSet());
        List<RsOrderFeedback> orderFeedbackList = orderClient.getOrderFeedbackList(BatchReq.of(orderIdSet)).orElse(Collections.emptyList());
        Map<Integer, RsOrderFeedback> feedbackMap = orderFeedbackList.stream().collect(Collectors.toMap(RsOrderFeedback::getOrderId, Function.identity()));

        Set<Integer> orderSet = orderSkuListResult.stream().map(x -> x.getRsCourseOrder().getId()).collect(Collectors.toSet());
        Set<Integer> salesSet = orderSkuListResult.stream().map(x -> x.getRsCourseOrder().getSalesId()).collect(Collectors.toSet());
        // 销售信息
        BaseResult<Map<Integer, UcUsers>> mapBaseResult = userClient.batchGetUserMap(BatchReq.create(salesSet));
        if (mapBaseResult.isFail()) {
            throw new BusinessException(mapBaseResult);
        }
        Map<Integer, UcUsers> salesMap = mapBaseResult.getData();
        // 销售部门
        BaseResult<Map<Integer, String>> deptInfoResult = userClient.getDeptListByUsers(salesSet);
        if (deptInfoResult.isFail()) {
            throw new BusinessException(deptInfoResult);
        }
        Map<Integer, String> deptMap = deptInfoResult.getData();
        //签字信息
        BaseResult<List<RsCourseOrderSign>> signListResult = orderClient.getSignList(BatchReq.create(orderSet));
        List<RsCourseOrderSign> signList = signListResult.getData();
        Map<Integer, RsCourseOrderSign> signMap = signList.stream().collect(Collectors.toMap(RsCourseOrderSign::getOrderID, x -> x));
        Set<Integer> skuSet = orderSkuListResult.stream().map(OrderSkuInfo::getRsCourseOrder).map(RsCourseOrder::getSkuId).collect(Collectors.toSet());
        Map<Integer, SkuCoverResp> skuCoverMap = orderClient.getSkuCoverList(BatchReq.create(skuSet)).orElseThrow(() -> new BusinessException("sku封面获取失败")).stream().collect(Collectors.toMap(SkuCoverResp::getSkuId, x -> x));
        //子订单金额
        BaseResult<List<CourseSubOrder>> subOrderListResult = orderClient.getSubOrderList(BatchReq.create(orderSet), OrderStatusEnum.PASSED.getValue());
        Map<Integer, List<CourseSubOrder>> subOrderMap = new HashMap<>();
        if (subOrderListResult.isPresent()) {
            subOrderMap = subOrderListResult.getData().stream().collect(Collectors.groupingBy(CourseSubOrder::getOrderId));
        }
        // 退款信息
        List<RsCourseRefundOrder> refundList = orderClient.getRefundList(BatchReq.of(orderSet), RefundOrderAuditStatusEnum.ORS_Passed.getValue()).orElse(emptyList());
        Map<Integer, RsCourseRefundOrder> refundMap = refundList.stream()
                .collect(Collectors.toMap(RsCourseRefundOrder::getOrderId, a -> a, (k1, k2) -> k1));
        for (OrderSkuInfo orderSkuInfo : orderSkuListResult) {
            Integer orderId = orderSkuInfo.getRsCourseOrder().getId();
            OrderSkuInfoResp orderSkuInfoResp = OrderSkuInfoResp.of(orderSkuInfo.getRsCourseOrder());
            orderSkuInfoResp.setHasRefund(refundMap.containsKey(orderSkuInfo.getRsCourseOrder().getId()));
            orderSkuInfoResp.setSkuInfo(SkuSubInfoResp.of(orderSkuInfo.getRsSku()));
            skuInfoRespList.add(orderSkuInfoResp);
            if (skuCoverMap.containsKey(orderSkuInfo.getRsCourseOrder().getSkuId())) {
                orderSkuInfoResp.setSkuCover(skuCoverMap.get(orderSkuInfo.getRsCourseOrder().getSkuId()));
            }
            if (salesMap.containsKey(orderSkuInfo.getRsCourseOrder().getSalesId())) {
                UserInfoResp salesInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(salesMap.get(orderSkuInfo.getRsCourseOrder().getSalesId()), salesInfoResp);
                orderSkuInfoResp.setSalesInfoResp(salesInfoResp);
            }
            if (deptMap.containsKey(orderSkuInfo.getRsCourseOrder().getSalesId())) {
                DeptInfoResp deptInfoResp = new DeptInfoResp();
                deptInfoResp.setDeptInfo(deptMap.get(orderSkuInfo.getRsCourseOrder().getSalesId()));
                orderSkuInfoResp.setDeptInfoResp(deptInfoResp);
            }
            if (signMap.containsKey(orderSkuInfo.getRsCourseOrder().getId())) {
                OrderSignInfoResp orderSignInfoResp = new OrderSignInfoResp();
                BeanUtils.copyProperties(signMap.get(orderSkuInfo.getRsCourseOrder().getId()), orderSignInfoResp);
                orderSkuInfoResp.setOrderSignInfo(orderSignInfoResp);
            }
            if (subOrderMap.containsKey(orderSkuInfo.getRsCourseOrder().getId())) {
                orderSkuInfoResp.setSubOrderAmount(subOrderMap.get(orderSkuInfo.getRsCourseOrder().getId()).stream().mapToInt(CourseSubOrder::getAmount).sum());
            }
            RsOrderFeedback orderFeedback = feedbackMap.get(orderId);
            boolean orderFeedbackStatus = orderFeedback != null;
            orderSkuInfoResp.setFeedbackStatus(orderFeedbackStatus);
        }
        return BaseResult.success(skuInfoRespList);
    }

    public PageResult<List<OrderSkuInfoResp>> getUserOrderList(Integer companyType, String userCode, Integer orderStatus, Integer current, Integer size) {
        if (!DesensitizeUtil.isFakeCode(userCode)) {
            int userId = DesensitizeUtil.maskToId(userCode);
            PageResult<List<OrderSkuInfo>> orderSkuListResult = orderClient.getOrderSkuPage(companyType, userId, orderStatus, false, current, size);
            if (orderSkuListResult.isFail()) {
                return PageResult.empty(current, size);
            }
            List<OrderSkuInfoResp> skuInfoRespList = new ArrayList<>();
            List<OrderSkuInfo> data = orderSkuListResult.getData();
            Pagination pagination = orderSkuListResult.getPagination();

            Set<Integer> orderIdSet = data.stream().map(x -> x.getRsCourseOrder().getId()).collect(Collectors.toSet());
            List<RsOrderFeedback> orderFeedbackList = orderClient.getOrderFeedbackList(BatchReq.of(orderIdSet)).orElse(Collections.emptyList());
            Map<Integer, RsOrderFeedback> feedbackMap = orderFeedbackList.stream().collect(Collectors.toMap(RsOrderFeedback::getOrderId, Function.identity()));

            Set<Integer> orderSet = data.stream().map(x -> x.getRsCourseOrder().getId()).collect(Collectors.toSet());
            Set<Integer> salesSet = data.stream().map(x -> x.getRsCourseOrder().getSalesId()).collect(Collectors.toSet());
            // 销售信息
            BaseResult<Map<Integer, UcUsers>> mapBaseResult = userClient.batchGetUserMap(BatchReq.create(salesSet));
            if (mapBaseResult.isFail()) {
                throw new BusinessException(mapBaseResult);
            }
            Map<Integer, UcUsers> salesMap = mapBaseResult.getData();
            // 销售部门
            BaseResult<Map<Integer, String>> deptInfoResult = userClient.getDeptListByUsers(salesSet);
            if (deptInfoResult.isFail()) {
                throw new BusinessException(deptInfoResult);
            }
            Map<Integer, String> deptMap = deptInfoResult.getData();
            //签字信息
            BaseResult<List<RsCourseOrderSign>> signListResult = orderClient.getSignList(BatchReq.create(orderSet));
            List<RsCourseOrderSign> signList = signListResult.getData();
            Map<Integer, RsCourseOrderSign> signMap = signList.stream().collect(Collectors.toMap(RsCourseOrderSign::getOrderID, x -> x));
            Set<Integer> skuSet = data.stream().map(OrderSkuInfo::getRsCourseOrder).map(RsCourseOrder::getSkuId).collect(Collectors.toSet());
            Map<Integer, SkuCoverResp> skuCoverMap = orderClient.getSkuCoverList(BatchReq.create(skuSet)).orElseThrow(() -> new BusinessException("sku封面获取失败")).stream().collect(Collectors.toMap(SkuCoverResp::getSkuId, x -> x));
            //子订单金额
            BaseResult<List<CourseSubOrder>> subOrderListResult = orderClient.getSubOrderList(BatchReq.create(orderSet), OrderStatusEnum.PASSED.getValue());
            Map<Integer, List<CourseSubOrder>> subOrderMap = new HashMap<>();
            if (subOrderListResult.isPresent()) {
                subOrderMap = subOrderListResult.getData().stream().collect(Collectors.groupingBy(CourseSubOrder::getOrderId));
            }
            // 退款信息
            List<RsCourseRefundOrder> refundList = orderClient.getRefundList(BatchReq.of(orderSet), null).orElse(emptyList());
            Map<Integer, RsCourseRefundOrder> refundMap = refundList.stream()
                    .collect(Collectors.toMap(RsCourseRefundOrder::getOrderId, a -> a, (k1, k2) -> k1));
            for (OrderSkuInfo orderSkuInfo : data) {
                Integer orderId = orderSkuInfo.getRsCourseOrder().getId();
                OrderSkuInfoResp orderSkuInfoResp = OrderSkuInfoResp.of(orderSkuInfo.getRsCourseOrder());
                orderSkuInfoResp.setHasRefund(refundMap.containsKey(orderSkuInfo.getRsCourseOrder().getId()));
                orderSkuInfoResp.setSkuInfo(SkuSubInfoResp.of(orderSkuInfo.getRsSku()));
                skuInfoRespList.add(orderSkuInfoResp);
                if (skuCoverMap.containsKey(orderSkuInfo.getRsCourseOrder().getSkuId())) {
                    orderSkuInfoResp.setSkuCover(skuCoverMap.get(orderSkuInfo.getRsCourseOrder().getSkuId()));
                }
                if (salesMap.containsKey(orderSkuInfo.getRsCourseOrder().getSalesId())) {
                    UserInfoResp salesInfoResp = new UserInfoResp();
                    BeanUtils.copyProperties(salesMap.get(orderSkuInfo.getRsCourseOrder().getSalesId()), salesInfoResp);
                    orderSkuInfoResp.setSalesInfoResp(salesInfoResp);
                }
                if (deptMap.containsKey(orderSkuInfo.getRsCourseOrder().getSalesId())) {
                    DeptInfoResp deptInfoResp = new DeptInfoResp();
                    deptInfoResp.setDeptInfo(deptMap.get(orderSkuInfo.getRsCourseOrder().getSalesId()));
                    orderSkuInfoResp.setDeptInfoResp(deptInfoResp);
                }
                if (signMap.containsKey(orderSkuInfo.getRsCourseOrder().getId())) {
                    OrderSignInfoResp orderSignInfoResp = new OrderSignInfoResp();
                    BeanUtils.copyProperties(signMap.get(orderSkuInfo.getRsCourseOrder().getId()), orderSignInfoResp);
                    orderSkuInfoResp.setOrderSignInfo(orderSignInfoResp);
                }
                if (subOrderMap.containsKey(orderSkuInfo.getRsCourseOrder().getId())) {
                    orderSkuInfoResp.setSubOrderAmount(subOrderMap.get(orderSkuInfo.getRsCourseOrder().getId()).stream().mapToInt(CourseSubOrder::getAmount).sum());
                }
                RsOrderFeedback orderFeedback = feedbackMap.get(orderId);
                boolean orderFeedbackStatus = orderFeedback != null;
                orderSkuInfoResp.setFeedbackStatus(orderFeedbackStatus);
            }
            return PageResult.success(skuInfoRespList, pagination);
        } else {
            FakeDataPropertyResp property = userClient.getFakeUserProperties().getData();
            if (ObjectUtil.isEmpty(property)) {
                return PageResult.empty();
            }
            Random random = new Random();
            String orders = property.getOrders();
            List<String> orderList = JSON.parseArray(orders, String.class);
            String order = orderList.get(random.nextInt(orderList.size()));
            List<String> list = JSON.parseArray(order, String.class);
            List<OrderSkuInfoResp> infoRespList = list.stream().map(i -> JSON.parseObject(i, OrderSkuInfoResp.class)).collect(Collectors.toList());
            return PageResult.success(infoRespList, Pagination.of(current, size, (long) infoRespList.size()));
        }
    }

    public void generateFeedbackPdf(HttpServletResponse response, Integer orderId) {
        BaseResult<Map<String, Object>> result = orderClient.feedbackData(orderId);
        if (result.isPresent()) {
            Map<String, Object> data = result.getData();
            if (data.containsKey("extraFee")) {
                PdfUtils.generatePdf(response, data, orderId, 2);
            } else {
                PdfUtils.generatePdf(response, data, orderId, 0);
            }
            return;
        }
        throw new BusinessException("回访问卷信息获取失败");
    }

    public void generateFeedbackPdf2(HttpServletResponse response, Integer orderId) {
        BaseResult<Map<String, Object>> result = orderClient.getFeedbackData(orderId);
        if (result.isPresent()) {
            Map<String, Object> data = result.getData();
            if (data.containsKey("extraFee")) {
                PdfUtils.generatePdf(response, data, orderId, 2);
            } else {
                PdfUtils.generatePdf(response, data, orderId, 0);
            }
            return;
        }
        throw new BusinessException("回访问卷信息获取失败");
    }

    public BaseResult<Void> syncOrder(Integer orderId) {
        return orderClient.syncOrder(orderId);
    }

    public BaseResult<Void> retryRefundPlan(RetryRefundPlanReq req) {
        return orderClient.retryRefundPlan(req);
    }

    public BaseResult<String> getSignUrl(Integer orderId, Integer companyType) {
        return orderClient.getSignUrl(orderId, companyType);
    }

    public void generateOrderRefundReadPdf(HttpServletResponse response, Integer refundId) {
        BaseResult<Map<String, Object>> result = orderClient.getOrderRefundData(refundId);
        if (ObjectUtil.isNull(result) || ObjectUtil.isNull(result.getData())) {
            throw new BusinessException("退费确认书获取失败");
        }
        PdfUtils.generatePdf(response, result.getData(), refundId, 1);
    }

    public PageResult<List<AssignBatchRecordResp>> getRsOrderAssignBatchRecord(Integer current, Integer size) {
        PageResult<List<RsOrderAssignBatchRecord>> records = orderClient.getRsOrderAssignBatchRecord(current, size);
        if (!records.isPresent()) {
            return PageResult.empty(current, size);
        }
        List<RsOrderAssignBatchRecord> recordList = records.getData();
        Set<Integer> userIds = recordList.stream().map(RsOrderAssignBatchRecord::getOperatorId).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess() ? getUserResult.getData() : Collections.emptyMap();
        List<AssignBatchRecordResp> res = recordList.stream().map(e ->
                AssignBatchRecordResp
                        .builder()
                        .id(e.getId())
                        .operatorName(userMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName())
                        .gmtCreate(e.getGmtCreate())
                        .status(e.getStatus())
                        .build()).collect(Collectors.toList());
        return PageResult.success(res, Pagination.of(current, size, records.getPagination().getTotal()));
    }

    public List<AssignDetailResp> getExtractDetail(Long batchId) {
        BaseResult<List<AssignDetailResp>> extractDetail = orderClient.getExtractDetail(batchId);
        if (!extractDetail.isPresent()) {
            return Collections.emptyList();
        }
        List<AssignDetailResp> detailData = extractDetail.getData();
        Set<Integer> userIds = detailData.stream().map(AssignDetailResp::getUserId).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess() ? getUserResult.getData() : Collections.emptyMap();
        return detailData.stream().peek(e -> e.setName(userMap.getOrDefault(e.getUserId(), new UcUsers()).getRealName())).collect(Collectors.toList());
    }

    public List<AssignDetailResp> getAssignDetail(Long batchId) {
        BaseResult<List<AssignDetailResp>> assignDetail = orderClient.getAssignDetail(batchId);
        if (!assignDetail.isPresent()) {
            return Collections.emptyList();
        }
        List<AssignDetailResp> detailData = assignDetail.getData();
        Set<Integer> userIds = detailData.stream().map(AssignDetailResp::getUserId).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess() ? getUserResult.getData() : Collections.emptyMap();
        return detailData.stream().peek(e -> e.setName(userMap.getOrDefault(e.getUserId(), new UcUsers()).getRealName())).collect(Collectors.toList());
    }

    public PageResult<List<HgAuditingResp>> auditingHg(Integer auditorId, Integer current, Integer size) {
        PageResult<List<OrderInfoDto>> result = orderClient.auditingHg(auditorId, current, size);
        if (!result.isPresent()) {
            return PageResult.empty(current, size);
        }
        List<OrderInfoDto> record = result.getData();
        List<Integer> userIds = new ArrayList<>();
        record.forEach(o -> userIds.add(o.getOrderFlow().getHgAuditorId()));

        // 用户信息
        BaseResult<Map<Integer, UcUsers>> userMapResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        if (userMapResult.isFail()) {
            throw new BusinessException(userMapResult);
        }
        Map<Integer, UcUsers> userMap = userMapResult.getData();

        List<HgAuditingResp> resp = record.stream().map(e -> HgAuditingResp.builder()
                .orderId(e.getOrderInfo().getId())
                .orderNumber(e.getOrderInfo().getOrderNumber())
                .payTime(e.getOrderInfo().getPayTime())
                .productLevel(e.getSkuInfo().getProductLevel())
                .realName(userMap.getOrDefault(e.getOrderFlow().getHgAuditorId(), new UcUsers()).getRealName())
                .build()).collect(Collectors.toList());
        return PageResult.success(resp, Pagination.of(current, size, result.getPagination().getTotal()));
    }

    public List<ChatRecordResp> getChatRecord(Integer companyType, Integer userid) {
        BaseResult<UcWxExt> wxExtInfo = userClient.findByUserId(companyType, userid);
        if (!wxExtInfo.isPresent()) {
            return Collections.emptyList();
        }
        UcWxExt ucWxExt = wxExtInfo.getData();
        String unionId = ucWxExt.getUnionId();
        String nickname = ucWxExt.getNickname();
        List<WorkWxFullContactUserRelation> userRelation = workwxClient.getUserRelation(unionId);
        if (ObjectUtil.isEmpty(userRelation)) {
            return Collections.emptyList();
        }

        BaseResult<List<WwxConfig>> configList = workwxClient.getConfigList(companyType);
        if (configList.isFail()) {
            throw new BusinessException(configList);
        }
        Map<Integer, WwxConfig> configMap = configList.getData().stream().collect(Collectors.toMap(WwxConfig::getAccountType, Function.identity()));

        return userRelation.stream().map(e -> {
            NpWorkWxContactUserRelation relation = e.getRelation();
            ChatRecordResp.ChatRecordRespBuilder builder = ChatRecordResp.builder()
                    .nickName(nickname);
            if (ObjectUtil.isNotEmpty(relation)) {
                builder.remark(relation.getRemark())
                        .relationStatus(relation.getContactEnabled() != null && relation.getUserEnabled() != null && (relation.getContactEnabled() && relation.getUserEnabled()))
                        .relationId(relation.getPkId())
                        .wxAccount(relation.getWxAccount())
                        .externalUserID(relation.getExternalUserId())
                        .accountType(relation.getAccountType());
                if (relation.getAccountType() != null) {
                    builder.corpName(configMap.getOrDefault(e.getRelation().getAccountType(), new WwxConfig()).getCorpName());
                }
            }
            if (ObjectUtil.isNotEmpty(e.getWorkWxUser()) && e.getWorkWxUser().getName() != null) {
                builder.belongName(e.getWorkWxUser().getName());
            }
            return builder.build();
        }).collect(Collectors.toList());
    }

    public BaseResult<String> forceRefundMark(RefundMarkReq req) {
        return orderClient.forceRefundMark(req);
    }

    public PageResult<List<OrderMarkRecordResp>> getMarkRecordList(LocalDate startMarkDate, LocalDate endMarkDate, String searchText, Integer current, Integer size) {

        PageResult<List<RsCourseOrderExt>> pageResult = orderClient.getMarkRecordList(startMarkDate, endMarkDate, searchText, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<RsCourseOrderExt> result = pageResult.getData();
        List<Integer> orderIds = result.stream().map(RsCourseOrderExt::getOrderId).collect(Collectors.toList());
        Set<Integer> markRelationIds = result.stream().map(RsCourseOrderExt::getMarkRelationId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        List<RsCourseOrder> rsCourseOrders = orderClient.batchGetOrderListById(orderIds).orElseThrow();
        Map<Integer, NpWorkWxUser> markWorkWxUserMap = null;
        if (!markRelationIds.isEmpty()) {
            markWorkWxUserMap = workwxClient.batchGetWorkWxUserMapByFriendId(BatchReq.of(markRelationIds)).orElseThrow();
        }
        Map<Integer, NpWorkWxUser> workWxUserMap = markWorkWxUserMap;
        Map<Integer, Integer> orderUserMap = rsCourseOrders.stream().collect(Collectors.toMap(RsCourseOrder::getId, RsCourseOrder::getUserId));
        Map<Integer, String> orderNumberMap = rsCourseOrders.stream().collect(Collectors.toMap(RsCourseOrder::getId, RsCourseOrder::getOrderNumber));
        List<OrderMarkRecordResp> resps = result.stream().map(r -> {
            OrderMarkRecordResp resp = OrderMarkRecordResp.builder()
                    .gmtCreate(r.getMarkTime())
                    .orderNumber(orderNumberMap.get(r.getOrderId()))
                    .userId(orderUserMap.get(r.getOrderId()))
                    .markRelationId(r.getMarkRelationId())
                    .build();
            if (ObjectUtil.isNotEmpty(resp.getUserId())) {
                resp.setUserCode(DesensitizeUtil.idToMask(resp.getUserId()));
            }
            if (ObjectUtil.isNotEmpty(resp.getMarkRelationId())) {
                resp.setWxAccount(workWxUserMap.getOrDefault(resp.getMarkRelationId(), new NpWorkWxUser()).getWxAccount());
                resp.setWxUserName(workWxUserMap.getOrDefault(resp.getMarkRelationId(), new NpWorkWxUser()).getName());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public BaseResult<List<OrderHgAutoAuditResp>> getOrderHgAutoAuditInfo(Integer orderId) {

        List<RsOrderAutoAuditInfo> auditInfoList = orderClient.getOrderHgAutoAuditInfo(orderId).orElseThrow();
        List<OrderHgAutoAuditResp> respList = auditInfoList.stream().map(info -> {
            return OrderHgAutoAuditResp.builder()
                    .type(info.getType())
                    .reason(info.getReason())
                    .build();
        }).sorted(Comparator.comparing(OrderHgAutoAuditResp::getType)).collect(Collectors.toList());

        return BaseResult.success(respList);
    }

    public PageResult<List<RefundManualResp>> getRefundManualList(LocalDateTime createStartTime, LocalDateTime createEndTime, Long payCompanyId, Integer current, Integer size) {
        PageResult<List<RefundPlanResp>> result = orderClient.getRefundManualList(createStartTime, createEndTime, payCompanyId, current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<RefundPlanResp> resultData = result.getData();
        Set<Integer> refundOrderIds = resultData.stream().map(RefundPlanResp::getRefundOrderId).collect(Collectors.toSet());
        List<RsCourseRefundOrder> refundOrders = orderClient.batchGetRefundByRefundId(BatchReq.create(refundOrderIds)).orElse(new ArrayList<>());
        Map<Integer, RsCourseRefundOrder> refundOrderMap = refundOrders.stream().collect(Collectors.toMap(RsCourseRefundOrder::getId, Function.identity()));
        List<Long> merchantIds = resultData.stream().map(RefundPlanResp::getMerchantId).collect(Collectors.toList());
        List<RsMerchantInfo> rsMerchantInfos = orderClient.getMerchantInfoList(merchantIds).orElse(new ArrayList<>());
        Map<Long, RsMerchantInfo> merchantInfoMap = rsMerchantInfos.stream().collect(Collectors.toMap(RsMerchantInfo::getId, Function.identity()));
        List<RefundManualResp> resp = resultData.stream().map(e -> RefundManualResp.builder()
                .id(e.getRefundOrderSubId())
                .subRefundNumber(e.getRefundNumber())
                .refundNumber(refundOrderMap.getOrDefault(e.getRefundOrderId(), new RsCourseRefundOrder()).getRefundNumber())
                .refundCreateTime(e.getGmtCreate())
                .subOrderNumber(e.getSubOrderNumber())
                .amount(e.getOrderAmount())
                .payTime(e.getGmtPay())
                .payType(e.getPayType())
                .platform(merchantInfoMap.getOrDefault(e.getMerchantId(), new RsMerchantInfo()).getPlatform())
                .mchName(merchantInfoMap.getOrDefault(e.getMerchantId(), new RsMerchantInfo()).getName())
                .refundFee(e.getRefundFee())
                .refundStatus(e.getRefundStatus())
                .name(e.getName())
                .accountBank(e.getAccountBank())
                .cardNumber(e.getCardNumber())
                .cardCreateTime(e.getCardCreateTime())
                .build()).collect(Collectors.toList());
        return PageResult.success(resp, result.getPagination());
    }
}

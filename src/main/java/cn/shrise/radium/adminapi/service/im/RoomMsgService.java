package cn.shrise.radium.adminapi.service.im;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.TextScanResultResp;
import cn.shrise.radium.adminapi.resp.im.ImRoomMsgResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsTextScanResult;
import cn.shrise.radium.contentservice.resp.ModerationLabelItem;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImRoomDialog;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoomMsgService {

    private final ImClient imClient;
    private final UserClient userClient;

    private final ContentClient contentClient;

    public PageResult<List<ImRoomMsgResp>> getRoomMsgList(Long sceneId, List<Integer> auditStatus, Integer current, Integer size) {
        PageResult<List<ImRoomDialog>> result = imClient.getRoomMsgList(sceneId, auditStatus,
                current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<ImRoomDialog> msgList = result.getData();
        // 用户信息
        Set<Integer> userIds = msgList.stream().map(ImRoomDialog::getSenderId).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> userResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        if (userResult.isFail()) {
            throw new BusinessException(userResult);
        }
        Map<Integer, UcUsers> userMap = userResult.getData();
        // 用户微信信息
        BaseResult<List<UcWxExt>> wxResult = userClient.getUnionIdsByUserId(new ArrayList<>(userIds));
        if (wxResult.isFail()) {
            throw new BusinessException(wxResult);
        }
        List<UcWxExt> wxs = wxResult.getData();
        Map<Integer, UcWxExt> wxMap = wxs.stream().collect(Collectors.toMap(UcWxExt::getUserId, x -> x, (o, e) -> o));
        // 审核人信息
        Set<Integer> auditorIds = msgList.stream().map(ImRoomDialog::getAuditorId).filter(Objects::nonNull).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> auditorResult = userClient.batchGetUserMap(BatchReq.create(auditorIds));
        if (auditorResult.isFail()) {
            throw new BusinessException(auditorResult);
        }
        Map<Integer, UcUsers> auditorMap = auditorResult.getData();
        Set<Long> resultSet = msgList.stream().map(ImRoomDialog::getResultId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();

        List<ImRoomMsgResp> records = msgList.stream().map(m -> {
            ImRoomMsgResp resp = new ImRoomMsgResp();
            BeanUtils.copyProperties(m, resp);
            resp.setSenderNickname(userMap.get(m.getSenderId()).getNickName());
            if (ObjectUtil.isNotNull(m.getAuditorId())) {
                resp.setAuditorName(auditorMap.get(m.getAuditorId()).getRealName());
            }
            if (wxMap.get(m.getSenderId()) != null) {
                resp.setWxNickname(wxMap.get(m.getSenderId()).getNickname());
                resp.setSenderCode(DesensitizeUtil.idToMask(m.getSenderId()));
            }
            if (ObjectUtil.isNotEmpty(m.getResultId()) && resultMap.containsKey(m.getResultId())) {
                SsTextScanResult scanResult = resultMap.get(resp.getResultId());
                List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                List<String> labelNameList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(labels)) {
                    Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                    for (String label : labels) {
                        labelNameList.add(itemMap.get(label).getName());
                    }
                }
                TextScanResultResp resultResp = new TextScanResultResp();
                resultResp.setLabels(labels);
                resultResp.setLabelMsg(labelNameList);
                resp.setPassed(scanResult.getPassed());
                resp.setResultResp(resultResp);
            }
            return resp;
        }).collect(Collectors.toList());

        return PageResult.success(records, Pagination.of(current, size, result.getPagination().getTotal()));
    }
}

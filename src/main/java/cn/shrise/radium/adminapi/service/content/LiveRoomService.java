package cn.shrise.radium.adminapi.service.content;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.resp.LiveRoomNameResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class LiveRoomService {

    private final ContentClient contentClient;

    public BaseResult<List<LiveRoomNameResp>> getgetLiveRoomName(Boolean isLive, Boolean isDeleted, Integer roomType, Integer companyType) {
        BaseResult<List<LiveRoomNameResp>> liveRoomName = contentClient.getLiveRoomName(null, isLive, isDeleted, roomType, companyType, null);
        if (liveRoomName.isFail()) {
            throw new BusinessException(liveRoomName);
        }
        return liveRoomName;
    }

    public BaseResult<List<LiveRoomNameResp>> getgetLiveRoomName(Boolean isLive, Boolean isDeleted, Integer roomType, Integer companyType, Integer salesId) {
        BaseResult<List<LiveRoomNameResp>> liveRoomName = contentClient.getLiveRoomName(null, isLive, isDeleted, roomType, companyType, salesId);
        if (liveRoomName.isFail()) {
            throw new BusinessException(liveRoomName);
        }
        return liveRoomName;
    }

}

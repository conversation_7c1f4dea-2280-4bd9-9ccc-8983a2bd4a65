package cn.shrise.radium.adminapi.service.im;

import cn.shrise.radium.adminapi.resp.im.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImRoomScene;
import cn.shrise.radium.imservice.entity.ImRoomSceneNotice;
import cn.shrise.radium.imservice.entity.ImRoomScenePlan;
import cn.shrise.radium.imservice.entity.ImRoomVideo;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class LiveSceneService {

    private final ImClient imClient;
    private final UserClient userClient;

    public PageResult<List<ImRoomSceneResp>> getRoomSceneList(Long roomId, Integer companyType, Boolean enabled, Integer status, Integer current, Integer size) {

        PageResult<List<ImRoomScene>> pageResult = imClient.getRoomSceneList(companyType, roomId, enabled, status, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<ImRoomScene> roomScenes = pageResult.getData();
        List<ImRoomSceneResp> resps = roomScenes.stream().map(e -> {
            ImRoomSceneResp resp = new ImRoomSceneResp();
            BeanUtils.copyProperties(e, resp);
            List<Integer> analystIds = JSON.parseArray(e.getAnalystList(), Integer.class);
            resp.setAnalystIds(analystIds);
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public BaseResult<ImRoomSceneResp> getRoomSceneInfo(Long id) {
        BaseResult<ImRoomScene> baseResult = imClient.getRoomSceneInfo(id);
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        ImRoomScene roomScene = baseResult.getData();
        ImRoomSceneResp resp = new ImRoomSceneResp();
        BeanUtils.copyProperties(roomScene, resp);
        List<Integer> analystIds = JSON.parseArray(roomScene.getAnalystList(), Integer.class);
        resp.setAnalystIds(analystIds);
        return BaseResult.success(resp);
    }

    public PageResult<List<RoomSceneNoticeResp>> getRoomSceneNoticeList(Long sceneId, Boolean enabled, Integer current, Integer size) {

        PageResult<List<ImRoomSceneNotice>> pageResult = imClient.getRoomSceneNoticeList(sceneId, enabled, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<ImRoomSceneNotice> sceneNotices = pageResult.getData();

        // 用户信息
        Set<Integer> userIds = sceneNotices.stream().map(ImRoomSceneNotice::getCreatorId).collect(Collectors.toSet());
        BaseResult<List<UcUsers>> userResult = userClient.batchGetUserList(userIds);
        if (userResult.isFail()) {
            throw new BusinessException(userResult);
        }
        List<UcUsers> users = userResult.getData();
        Map<Integer, UcUsers> userMap = users.stream().collect(Collectors.toMap(UcUsers::getId, x -> x));

        List<RoomSceneNoticeResp> resps = sceneNotices.stream().map(e -> {
            RoomSceneNoticeResp resp = new RoomSceneNoticeResp();
            BeanUtils.copyProperties(e, resp);
            resp.setCreatorName(userMap.get(e.getCreatorId()).getRealName());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public BaseResult<SceneAnalystResp> getRoomSceneAnalyst(Long id) {
        BaseResult<ImRoomScene> baseResult = imClient.getRoomSceneInfo(id);
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        if (!baseResult.isPresent()) {
            return BaseResult.success(new SceneAnalystResp());
        }
        ImRoomScene roomScene = baseResult.getData();
        SceneAnalystResp resp = new SceneAnalystResp();
        BeanUtils.copyProperties(roomScene, resp);
        List<Integer> analystIds = JSON.parseArray(roomScene.getAnalystList(), Integer.class);
        resp.setAnalystIds(analystIds);
        return BaseResult.success(resp);
    }

    public PageResult<List<RoomScenePlanResp>> getRoomScenePlanList(Integer companyType, Long roomId, Boolean enabled, Integer current, Integer size) {
        PageResult<List<ImRoomScenePlan>> pageResult = imClient.getRoomScenePlanList(companyType, roomId, enabled, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }

        List<RoomScenePlanResp> resps = pageResult.getData().stream().map(plan -> {
            RoomScenePlanResp resp = new RoomScenePlanResp();
            BeanUtils.copyProperties(plan, resp);
            List<Integer> weekday = JSON.parseArray(plan.getWeekday(), Integer.class);
            resp.setWeekday(weekday);
            resp.setPreheatTime(plan.getPreheatTime().format(DateTimeFormatter.ISO_TIME));
            resp.setPreStartTime(plan.getPreStartTime().format(DateTimeFormatter.ISO_TIME));
            return resp;
        }).collect(Collectors.toList());

        return PageResult.success(resps, pageResult.getPagination());
    }

    public BaseResult<ImRoomScenePlanResp> getRoomScenePlanInfo(Long id) {

        BaseResult<ImRoomScenePlan> baseResult = imClient.getRoomScenePlanInfo(id);
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        ImRoomScenePlan plan = baseResult.getData();
        ImRoomScenePlanResp resp = new ImRoomScenePlanResp();
        BeanUtils.copyProperties(plan, resp);
        List<Integer> weekday = JSON.parseArray(plan.getWeekday(), Integer.class);
        List<Integer> analystList = JSON.parseArray(plan.getAnalystList(), Integer.class);
        resp.setWeekday(weekday);
        resp.setAnalystIds(analystList);
        resp.setPreheatTime(plan.getPreheatTime().format(DateTimeFormatter.ISO_TIME));
        resp.setPreStartTime(plan.getPreStartTime().format(DateTimeFormatter.ISO_TIME));
        return BaseResult.success(resp);
    }

    public BaseResult<List<ImRoomSceneResp>> filterRoomSceneList(Long roomId, List<Integer> status, Integer companyType, Boolean enabled) {

        BaseResult<List<ImRoomScene>> result = imClient.filterRoomSceneList(roomId, companyType, status, enabled);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            return BaseResult.success(Collections.EMPTY_LIST);
        }
        List<ImRoomScene> roomScenes = result.getData();
        List<ImRoomSceneResp> resps = roomScenes.stream().map(e -> {
            ImRoomSceneResp resp = new ImRoomSceneResp();
            BeanUtils.copyProperties(e, resp);
            List<Integer> analystIds = JSON.parseArray(e.getAnalystList(), Integer.class);
            resp.setAnalystIds(analystIds);
            return resp;
        }).collect(Collectors.toList());
        return BaseResult.success(resps);
    }
}

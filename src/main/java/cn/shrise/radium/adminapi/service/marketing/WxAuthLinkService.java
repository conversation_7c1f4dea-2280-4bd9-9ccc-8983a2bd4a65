package cn.shrise.radium.adminapi.service.marketing;

import cn.shrise.radium.adminapi.resp.NpWxAuthLinkResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxAuthLink;
import cn.shrise.radium.marketingservice.req.CreateNpWxAuthLinkReq;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WxAuthLinkService {

    private final MarketingClient marketingClient;
    private final CommonProperties commonProperties;

    public PageResult<List<NpWxAuthLinkResp>> getWxAuthLinkList(Integer companyType, String searchContent,
                                                            Integer current, Integer size) {
        PageResult<List<NpWxAuthLink>> res = marketingClient.getWxAuthLinkList(companyType, searchContent, current, size);
        if (res.isFail()) {
            throw new BusinessException(res);
        }
        List<NpWxAuthLinkResp> records = res.getData().stream().map(e -> {
            NpWxAuthLinkResp record = new NpWxAuthLinkResp();
            BeanUtils.copyProperties(e, record);
            record.setShortUrl(String.format("%s/n/%s", commonProperties.getShortUrl(), e.getNumber()));
            return record;
        }).collect(Collectors.toList());

        return PageResult.success(records, Pagination.of(current, size, res.getPagination().getTotal()));
    }

    public BaseResult<String> createWxAuthLink(CreateNpWxAuthLinkReq req) {
        BaseResult<String> res = marketingClient.createWxAuthLink(req);
        if (res.isFail()) {
            throw new BusinessException(res);
        }
        return res;
    }

    public BaseResult<String> deleteWxAuthLink(Long id) {
        BaseResult<String> res = marketingClient.deleteWxAuthLink(id);
        if (res.isFail()) {
            throw new BusinessException(res);
        }
        return res;
    }
}

package cn.shrise.radium.adminapi.service.user;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.resp.UserChangeRecordResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserChangeService {

    private final UserClient userClient;

    public PageResult<List<UserChangeRecordResp>> getUserChangeRecordList(Integer changeType, Integer userId, LocalDateTime startTime, LocalDateTime endTime, Integer current, Integer size) {
        PageResult<List<UserChangeRecordResp>> result = userClient.getUserChangeRecordList(changeType, userId, startTime, endTime, current, size);
        if (result.isSuccess()) {
            List<UserChangeRecordResp> respList = Optional.ofNullable(result.getData()).map(list -> list.stream().filter(Objects::nonNull)
                    .peek(o -> o.setUserCode(DesensitizeUtil.idToMask(o.getUserId()))).collect(Collectors.toList())).orElse(new ArrayList<>());
            return PageResult.success(respList, result.getPagination());
        }
        return null;
    }

}

package cn.shrise.radium.adminapi.service.marketing;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.adminapi.resp.marketing.StreamRoomPromotionRecordResp;
import cn.shrise.radium.marketingservice.entity.NpMediaAccount;
import cn.shrise.radium.marketingservice.entity.NpStreamRoomPromotionRecord;
import cn.shrise.radium.marketingservice.resp.streamroom.*;
import cn.shrise.radium.marketingservice.req.streamroom.*;
import cn.shrise.radium.marketingservice.entity.NpStreamRoomPlanMemberRelation;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.adminapi.resp.streamroom.StreamRoomRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpStreamRoomRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StreamRoomService {

    private final MarketingClient marketingClient;
    private final UserClient userClient;

    public PageResult<List<StreamRoomConfigPropertyResp>> getStreamRoomConfigList(List<String> propertyNumbers, Boolean enabled, Integer current, Integer size) {
        return marketingClient.getStreamRoomConfigList(propertyNumbers, enabled, current, size);
    }

    public BaseResult<Void> createStreamRoomConfig(CreateStreamRoomConfigPropertyReq req) {
        return marketingClient.createStreamRoomConfig(req);
    }

    public BaseResult<Void> updateStreamRoomConfig(UpdateStreamRoomConfigPropertyReq req) {
        return marketingClient.updateStreamRoomConfig(req);
    }

    public PageResult<List<StreamRoomRecordResp>> getStreamRoomRecordList(Integer current, Integer size) {
        PageResult<List<NpStreamRoomRecord>> respResult = marketingClient.getStreamRoomRecordList(current, size);
        if (respResult.isFail()) {
            throw new BusinessException(respResult);
        }
        List<NpStreamRoomRecord> records = respResult.getData();
        Set<Integer> userIdSet = records.stream().map(NpStreamRoomRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<StreamRoomRecordResp> respList = records.stream()
                .map(e -> StreamRoomRecordResp.builder()
                        .gmtCreate(e.getGmtCreate())
                        .operatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName())
                        .content(e.getContent())
                        .build())
                .collect(Collectors.toList());
        return PageResult.success(respList, respResult.getPagination());
    }

    public PageResult<List<StreamRoomConfigRecordResp>> getStreamRoomConfigRecord(Integer current, Integer size) {
        PageResult<List<StreamRoomConfigRecordResp>> result = marketingClient.getStreamRoomConfigRecord(current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<StreamRoomConfigRecordResp> record = result.getData();
        Set<Integer> userIds = record.stream().map(StreamRoomConfigRecordResp::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        for (StreamRoomConfigRecordResp streamRoomConfigRecord : result.getData()) {
            streamRoomConfigRecord.setOperatorName(userMap.getOrDefault(streamRoomConfigRecord.getOperatorId(), new UcUsers()).getRealName());
        }
        return result;
    }

    public BaseResult<List<StreamRoomPlanPromotionResp>> getStreamRoomPlanList(StreamRoomPlanReq req) {
        List<StreamRoomPlanPromotionResp> resp = new ArrayList<>();

        BaseResult<List<StreamRoomPlanResp>> planResult = marketingClient.getStreamRoomPlanList(req);
        List<StreamRoomPlanResp> planList = planResult.getData();
        List<Long> planIds = planList.stream().map(StreamRoomPlanResp::getPlanId).collect(Collectors.toList());
        Map<Long, List<StreamRoomPromotionResp>> promotionMap = marketingClient.getPromotionMapByPlanIds(BatchReq.of(planIds)).orElse(new HashMap<>());
        promotionMap.values().forEach(e -> {
            setPromotionBaseInfo(e);
            setPromotionPropertyInfos(e);
        });

        List<NpStreamRoomPlanMemberRelation> relations = marketingClient.getPlanMemberRelation(BatchReq.of(planIds)).orElse(new ArrayList<>());
        Map<Long, List<NpStreamRoomPlanMemberRelation>> relationMap = relations.stream().collect(Collectors.groupingBy(NpStreamRoomPlanMemberRelation::getPlanId));
        Set<Integer> userIdSet = relations.stream().map(NpStreamRoomPlanMemberRelation::getMemberId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        for (StreamRoomPlanPromotionResp streamRoomPlanPromotionResp : resp) {
            List<NpStreamRoomPlanMemberRelation> roomPlanMemberRelations = relationMap.get(streamRoomPlanPromotionResp.getPlan().getPlanId());
            List<StreamRoomPlanResp.MemberInfo> memberInfos = new ArrayList<>();
            for (NpStreamRoomPlanMemberRelation roomPlanMemberRelation : roomPlanMemberRelations) {
                StreamRoomPlanResp.MemberInfo memberInfo = new StreamRoomPlanResp.MemberInfo();
                memberInfo.setMemberId(roomPlanMemberRelation.getMemberId());
                memberInfo.setRoleNumber(roomPlanMemberRelation.getRoleNumber());
                memberInfo.setMemberName(usersMap.getOrDefault(roomPlanMemberRelation.getMemberId(), new UcUsers()).getRealName());
                memberInfo.setAvatarUrl(usersMap.getOrDefault(roomPlanMemberRelation.getMemberId(), new UcUsers()).getAvatarUrl());
                memberInfos.add(memberInfo);
            }
            streamRoomPlanPromotionResp.getPlan().setMemberInfos(memberInfos);
            streamRoomPlanPromotionResp.getPlan().setHasPromotion(ObjectUtil.isNotNull(streamRoomPlanPromotionResp.getPromotionList()));
            if (ObjectUtil.isNotNull(streamRoomPlanPromotionResp.getPlan().getValueIdList())) {
                List<Long> valueIds = JSONUtil.toList(streamRoomPlanPromotionResp.getPlan().getValueIdList(), Long.class);
                streamRoomPlanPromotionResp.getPlan().setPropertyInfos(marketingClient.getStreamRoomConfigValueList(BatchReq.of(valueIds)).orElse(new ArrayList<>()));
            }
        }

        for (StreamRoomPlanResp streamRoomPlanResp : planList) {
            StreamRoomPlanPromotionResp streamRoomPlanPromotionResp = new StreamRoomPlanPromotionResp();
            List<NpStreamRoomPlanMemberRelation> roomPlanMemberRelations = relationMap.get(streamRoomPlanResp.getPlanId());
            List<StreamRoomPlanResp.MemberInfo> memberInfos = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(roomPlanMemberRelations)) {
                for (NpStreamRoomPlanMemberRelation roomPlanMemberRelation : roomPlanMemberRelations) {
                    StreamRoomPlanResp.MemberInfo memberInfo = new StreamRoomPlanResp.MemberInfo();
                    memberInfo.setMemberId(roomPlanMemberRelation.getMemberId());
                    memberInfo.setRoleNumber(roomPlanMemberRelation.getRoleNumber());
                    memberInfo.setMemberName(usersMap.getOrDefault(roomPlanMemberRelation.getMemberId(), new UcUsers()).getRealName());
                    memberInfo.setAvatarUrl(usersMap.getOrDefault(roomPlanMemberRelation.getMemberId(), new UcUsers()).getAvatarUrl());
                    memberInfos.add(memberInfo);
                }
            }
            streamRoomPlanResp.setMemberInfos(memberInfos);
            if (ObjectUtil.isNotNull(streamRoomPlanResp.getValueIdList())) {
                List<Long> valueIds = JSONUtil.toList(streamRoomPlanResp.getValueIdList(), Long.class);
                streamRoomPlanResp.setPropertyInfos(marketingClient.getStreamRoomConfigValueList(BatchReq.of(valueIds)).orElse(new ArrayList<>()));
            }
            streamRoomPlanPromotionResp.setPromotionList(promotionMap.getOrDefault(streamRoomPlanResp.getPlanId(), new ArrayList<>()));
            streamRoomPlanResp.setHasPromotion(ObjectUtil.isNotEmpty(streamRoomPlanPromotionResp.getPromotionList()));
            streamRoomPlanPromotionResp.setPlan(streamRoomPlanResp);
            resp.add(streamRoomPlanPromotionResp);
        }
        return BaseResult.success(resp);
    }

    public BaseResult<StreamRoomPlanPromotionResp> getStreamRoomPlanDetail(Long planId) {
        StreamRoomPlanPromotionResp resp = new StreamRoomPlanPromotionResp();

        BaseResult<StreamRoomPlanResp> planResult = marketingClient.getStreamRoomPlanDetail(planId);
        resp.setPlan(planResult.getData());
        BaseResult<List<StreamRoomPromotionResp>> promotionsResult = marketingClient.getStreamRoomPromotionList(planId);
        resp.setPromotionList(promotionsResult.getData());

        setPromotionBaseInfo(resp.getPromotionList());
        setPromotionPropertyInfos(resp.getPromotionList());

        List<NpStreamRoomPlanMemberRelation> relations = marketingClient.getPlanMemberRelationByPlanId(planId).orElse(new ArrayList<>());
        Set<Integer> userIdSet = relations.stream().map(NpStreamRoomPlanMemberRelation::getMemberId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<StreamRoomPlanResp.MemberInfo> memberInfos = new ArrayList<>();
        for (NpStreamRoomPlanMemberRelation relation : relations) {
            StreamRoomPlanResp.MemberInfo memberInfo = new StreamRoomPlanResp.MemberInfo();
            memberInfo.setMemberId(relation.getMemberId());
            memberInfo.setRoleNumber(relation.getRoleNumber());
            memberInfo.setMemberName(usersMap.getOrDefault(relation.getMemberId(), new UcUsers()).getRealName());
            memberInfo.setAvatarUrl(usersMap.getOrDefault(relation.getMemberId(), new UcUsers()).getAvatarUrl());
            memberInfos.add(memberInfo);
        }
        resp.getPlan().setMemberInfos(memberInfos);
        resp.getPlan().setHasPromotion(ObjectUtil.isNotEmpty(resp.getPromotionList()));
        if (ObjectUtil.isNotNull(resp.getPlan().getValueIdList())) {
            List<Long> valueIds = JSONUtil.toList(resp.getPlan().getValueIdList(), Long.class);
            resp.getPlan().setPropertyInfos(marketingClient.getStreamRoomConfigValueList(BatchReq.of(valueIds)).orElse(new ArrayList<>()));
        }

        return BaseResult.success(resp);
    }

    private void setPromotionBaseInfo(List<StreamRoomPromotionResp> promotions) {
        List<Long> mediaIdList = promotions.stream().map(StreamRoomPromotionResp::getMediaId).collect(Collectors.toList());
        Map<Long, NpMediaAccount> mediaAccountMap = marketingClient.getMediaAccountMap(BatchReq.of(mediaIdList)).orElse(new HashMap<>());
        List<Integer> belongIds = mediaAccountMap.values().stream().map(NpMediaAccount::getBelongId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        List<Integer> managerIdList = promotions.stream().map(StreamRoomPromotionResp::getManagerId).collect(Collectors.toList());
        managerIdList.addAll(belongIds);
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(managerIdList)).getData();
        promotions.forEach(e -> {
            NpMediaAccount mediaAccount = mediaAccountMap.getOrDefault(e.getMediaId(), new NpMediaAccount());
            e.setMediaName(mediaAccount.getName());
            e.setPlatform(mediaAccount.getPlatform());
            e.setManagerName(usersMap.getOrDefault(e.getManagerId(), new UcUsers()).getRealName());
            e.setStreamerName(usersMap.getOrDefault(mediaAccount.getBelongId(), new UcUsers()).getRealName());
            e.setStreamerId(mediaAccount.getBelongId());
        });
    }

    private void setPromotionPropertyInfos(List<StreamRoomPromotionResp> promotions) {
        List<Long> promotionIds = promotions.stream().map(StreamRoomPromotionResp::getPromotionId).collect(Collectors.toList());
        Map<Long, List<StreamRoomPromotionResp.PropertyInfo>> propertyInfoMap = marketingClient.getPropertyInfoMap(BatchReq.of(promotionIds)).orElse(new HashMap<>());
        promotions.forEach(e -> e.setPropertyInfos(propertyInfoMap.getOrDefault(e.getPromotionId(), new ArrayList<>())));
    }

    public BaseResult<Long> createStreamRoomPlan(CreateStreamRoomPlanReq req) {
        return marketingClient.createStreamRoomPlan(req);
    }

    public BaseResult<Void> updateStreamRoomPlan(UpdateStreamRoomPlanReq req) {
        return marketingClient.updateStreamRoomPlan(req);
    }

    public BaseResult<Void> updateStreamRoomPlanRemark(UpdateStreamRoomPlanRemarkReq req) {
        return marketingClient.updateStreamRoomPlanRemark(req);
    }

    public BaseResult<Void> updatePromotionRemark(UpdatePromotionRemark req) {
        return marketingClient.updatePromotionRemark(req);
    }

    public BaseResult<Void> cancelStreamRoomPlan(CancelStreamRoomPlanReq req) {
        return marketingClient.cancelStreamRoomPlan(req);
    }

    public PageResult<List<StreamRoomPlanRecordResp>> getStreamRoomPlanRecord(Integer current, Integer size) {
        PageResult<List<StreamRoomPlanRecordResp>> result = marketingClient.getStreamRoomPlanRecord(current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<StreamRoomPlanRecordResp> record = result.getData();
        Set<Integer> userIds = record.stream().map(StreamRoomPlanRecordResp::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        for (StreamRoomPlanRecordResp streamRoomPlanRecord : result.getData()) {
            streamRoomPlanRecord.setOperatorName(userMap.getOrDefault(streamRoomPlanRecord.getOperatorId(), new UcUsers()).getRealName());
        }
        return result;
    }

    public BaseResult<StreamRoomPlanStatisticsResp> getStreamRoomPlanStatistics(LocalDateTime startTime) {
        return marketingClient.getStreamRoomPlanStatistics(startTime);
    }

    public BaseResult<Void> createStreamRoomPromotion(CreateStreamRoomPromotionReq req) {
        return marketingClient.createStreamRoomPromotion(req);
    }

    public BaseResult<Void> updateStreamRoomPromotion(UpdateStreamRoomPromotionReq req) {
        return marketingClient.updateStreamRoomPromotion(req);
    }

    public BaseResult<Void> enabledStreamRoomPromotion(EnableStreamRoomPromotionReq req) {
        return marketingClient.enabledStreamRoomPromotion(req);
    }

    public PageResult<List<StreamRoomPromotionRecordResp>> getStreamRoomPromotionRecordList(Integer current, Integer size) {
        PageResult<List<NpStreamRoomPromotionRecord>> respResult = marketingClient.getStreamRoomPromotionRecordList(current, size);
        if (respResult.isFail()) {
            throw new BusinessException(respResult);
        }
        List<NpStreamRoomPromotionRecord> records = respResult.getData();
        Set<Integer> userIdSet = records.stream().map(NpStreamRoomPromotionRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<StreamRoomPromotionRecordResp> respList = records.stream()
                .map(e -> StreamRoomPromotionRecordResp.builder()
                        .gmtCreate(e.getGmtCreate())
                        .operatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName())
                        .content(e.getContent())
                        .build())
                .collect(Collectors.toList());
        return PageResult.success(respList, respResult.getPagination());
    }
}

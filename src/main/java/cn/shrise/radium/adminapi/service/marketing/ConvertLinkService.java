package cn.shrise.radium.adminapi.service.marketing;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.entity.ContactWayInfo;
import cn.shrise.radium.adminapi.req.ConvertLinkUploadRecordReq;
import cn.shrise.radium.adminapi.resp.ConvertLinkContactWayRelationResp;
import cn.shrise.radium.adminapi.resp.NpConvertLinkRecordResp;
import cn.shrise.radium.adminapi.resp.NpConvertLinkUploadRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpConvertLinkConfig;
import cn.shrise.radium.marketingservice.entity.NpConvertLinkContactWayRelation;
import cn.shrise.radium.marketingservice.entity.NpConvertLinkRecord;
import cn.shrise.radium.marketingservice.entity.NpConvertLinkUploadRecord;
import cn.shrise.radium.marketingservice.error.MsErrorCode;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactWay;
import cn.shrise.radium.workwxservice.resp.WwxContactWayInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConvertLinkService {

    private final MarketingClient marketingClient;
    private final WorkwxClient workwxClient;
    private final UserClient userClient;

    public PageResult<List<NpConvertLinkRecordResp>> getConvertLinkRecordList(Long linkId, Boolean asc, Integer current,
                                                                              Integer size) {
        List<NpConvertLinkRecordResp> respList = new ArrayList<>();
        PageResult<List<NpConvertLinkRecord>> linkRecordList = marketingClient.getConvertLinkRecordList(linkId, asc,
                current, size);
        log.info("marketingClient.getConvertLinkRecordList code: {}, msg: {}", linkRecordList.getCode(), linkRecordList.getMsg());
        if (ObjectUtil.isEmpty(linkRecordList.getData())) {
            return PageResult.success(respList, null);
        }
        respList = linkRecordList.getData().stream().map(e -> {
            NpConvertLinkRecordResp resp = new NpConvertLinkRecordResp();
            BeanUtil.copyProperties(e, resp, CopyOptions.create().ignoreNullValue());
            return resp;
        }).collect(Collectors.toList());

        Set<Integer> operatorIds =
                linkRecordList.getData().stream().map(NpConvertLinkRecord::getOperatorId).collect(Collectors.toSet());
        Set<Integer> wayIds =
                linkRecordList.getData().stream().map(NpConvertLinkRecord::getWayId).collect(Collectors.toSet());

        BaseResult<List<UcUsers>> users = userClient.batchGetUserList(new ArrayList<>(operatorIds));
        log.info("userClient.batchGetUserList code: {}, msg: {}", users.getCode(), users.getMsg());
        BaseResult<List<NpWorkWxContactWay>> ways = workwxClient.getContactWayList(new ArrayList<>(wayIds));
        log.info("workwxClient.getContactWayList code: {}, msg: {}", ways.getCode(), ways.getMsg());

        if (ObjectUtil.isNotEmpty(users.getData())) {
            Map<Integer, UcUsers> userMap = users.getData().stream().collect(Collectors.toMap(UcUsers::getId, t -> t));
            respList.forEach(e -> {
                if (ObjectUtil.isNotEmpty(userMap.get(e.getOperatorId()))) {
                    e.setOperatorName(userMap.get(e.getOperatorId()).getRealName());
                    e.setOperatorAvatarUrl(userMap.get(e.getOperatorId()).getAvatarUrl());
                }
            });
        }
        if (ObjectUtil.isNotEmpty(ways.getData())) {
            Map<Integer, NpWorkWxContactWay> wayMap =
                    ways.getData().stream().collect(Collectors.toMap(NpWorkWxContactWay::getId, t -> t));
            respList.forEach(e -> {
                if (ObjectUtil.isNotEmpty(wayMap.get(e.getWayId()))) {
                    NpWorkWxContactWay way = wayMap.get(e.getWayId());
                    e.setWayName(way.getName());
                }
            });
        }

        return PageResult.success(respList, linkRecordList.getPagination());
    }

    /**
     * 新增一个转化链路
     * @param linkType
     * @param linkName
     * @param number
     * @return
     */
    public BaseResult<NpConvertLinkConfig> saveOne(Integer linkType, String linkName, String number){
        return marketingClient.addConvertLinkConfig(AuthContextHolder.getCompanyType(), AuthContextHolder.getUserId(), linkType, linkName, number);
    }

    /**
     * 转化链路列表
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<NpConvertLinkConfig>> getConvertLinkConfigList(Integer linkType, Boolean isEnabled, String searchContent, Integer current, Integer size) {
        return marketingClient.getConvertLinkConfigList(AuthContextHolder.getCompanyType(), linkType, isEnabled, searchContent, null, current, size);
    }

    /**
     * 链路绑定的渠道信息
     * @param linkId
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<ConvertLinkContactWayRelationResp>> getRelationContactWays(Long linkId, Integer current, Integer size){
        List<ConvertLinkContactWayRelationResp> respList = new ArrayList<>();

        PageResult<List<NpConvertLinkContactWayRelation>> relation = marketingClient.getConvertLinkRelateContactWays(linkId, current, size);
        if (ObjectUtil.isEmpty(relation.getData())){
            log.info("规则{}暂未关联渠道", linkId);
            return PageResult.success(respList, null);
        }
        List<Integer> wayIds = relation.getData().stream().map(NpConvertLinkContactWayRelation::getWayId).collect(Collectors.toList());
        PageResult<List<WwxContactWayInfoResp>> wayInfo = workwxClient.getContactWayDetailList(wayIds, null, null);
        Map<Integer, WwxContactWayInfoResp> wayInfoMap = wayInfo.getData().stream().collect(Collectors.toMap(WwxContactWayInfoResp::getId, t -> t));
        respList = relation.getData().stream().map(e -> {
            ConvertLinkContactWayRelationResp resp = new ConvertLinkContactWayRelationResp();
            BeanUtil.copyProperties(e, resp, CopyOptions.create().ignoreNullValue());
           resp.setWayInfo(wayInfoMap.get(e.getWayId()));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, relation.getPagination());
    }

    /**
     * 启用禁用链路状态
     * @param linkId
     * @param isEnabled
     * @return
     */
    public ErrorConstant updateConvertLinkStatus(Long linkId, Boolean isEnabled){
        BaseResult<MsErrorCode> res = marketingClient.updateConvertLinkStatus(AuthContextHolder.getUserId(), linkId, isEnabled);
        if (res.isSuccess()){
            return ErrorConstant.SUCCESS;
        } else {
            return ErrorConstant.FAILURE;
        }
    }

    /**
     * 修改链路名称
     * @param linkId
     * @param linkName
     * @return
     */
    public ErrorConstant updateConvertLinkName(Long linkId, String linkName){
        BaseResult<MsErrorCode> res = marketingClient.updateConvertLinkName(AuthContextHolder.getUserId(), linkId, linkName);
        if (res.isSuccess()){
            return ErrorConstant.SUCCESS;
        } else {
            return ErrorConstant.FAILURE;
        }
    }


    public PageResult<List<NpConvertLinkUploadRecordResp>> getConvertLinkUploadRecordList (ConvertLinkUploadRecordReq uploadRecordReq) {
        List<NpConvertLinkUploadRecordResp> respList = new ArrayList<>();

        PageResult<List<NpConvertLinkUploadRecord>> uploadRecord = marketingClient.getLinkUploadRecordList(AuthContextHolder.getCompanyType(), uploadRecordReq.getStartTime(), uploadRecordReq.getEndTime(), uploadRecordReq.getActionType(), uploadRecordReq.getWayIds(), uploadRecordReq.getLinkId(), uploadRecordReq.getLinkType(), uploadRecordReq.getCurrent(), uploadRecordReq.getSize());
        if (ObjectUtil.isEmpty(uploadRecord.getData())){
            log.info("无数据");
            return PageResult.success(respList, null);
        }
        List<Long> linkIds = uploadRecord.getData().stream().map(NpConvertLinkUploadRecord::getLinkId).collect(Collectors.toList());
        List<Integer> wayIdList = uploadRecord.getData().stream().map(NpConvertLinkUploadRecord::getWayId).collect(Collectors.toList());
        PageResult<List<NpConvertLinkConfig>> configInfo = marketingClient.getConvertLinkConfigList(AuthContextHolder.getCompanyType(), null, null, null, linkIds, null, null);
        Map<Long, NpConvertLinkConfig> linkInfoMap = configInfo.getData().stream().collect(Collectors.toMap(NpConvertLinkConfig::getId, t -> t));
        BaseResult<List<NpWorkWxContactWay>> wayInfo = workwxClient.getContactWayList(wayIdList);
        Map<Integer, NpWorkWxContactWay> wayInfoMap = wayInfo.getData().stream().collect(Collectors.toMap(NpWorkWxContactWay::getId, t -> t));
        respList = uploadRecord.getData().stream().map(e -> {
            NpConvertLinkUploadRecordResp resp = new NpConvertLinkUploadRecordResp();
            BeanUtil.copyProperties(e, resp, CopyOptions.create().ignoreNullValue());
            NpConvertLinkConfig linkInfo = new NpConvertLinkConfig();
            BeanUtil.copyProperties(linkInfoMap.get(e.getLinkId()), linkInfo, CopyOptions.create().ignoreNullValue());
            ContactWayInfo wInfo = new ContactWayInfo();
            NpWorkWxContactWay ways = wayInfoMap.get(e.getWayId());
            BeanUtil.copyProperties(ways, wInfo, CopyOptions.create().ignoreNullValue());
            resp.setWayInfo(wInfo);
            resp.setLinkInfo(linkInfo);
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, uploadRecord.getPagination());
    }
}

package cn.shrise.radium.adminapi.service.user;

import cn.shrise.radium.adminapi.resp.user.UcCustomerTagRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.req.CreateTagGroupReq;
import cn.shrise.radium.userservice.req.CreateTagReq;
import cn.shrise.radium.userservice.resp.CustomerTagRecordResp;
import cn.shrise.radium.userservice.resp.CustomerTagResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomTagService {
    private final UserClient userClient;

    public BaseResult<List<CustomerTagResp>> getGroupTagList(Integer companyType, Long roleId, Boolean isGlobal, Boolean groupEnabled, Boolean tagEnable) {
        return userClient.getGroupTagList(companyType, roleId, isGlobal, null, groupEnabled, tagEnable);
    }

    public BaseResult<Boolean> updateCustomTag(Integer companyType, String userCode, Integer operator, List<Integer> tagList) {
        int userId = DesensitizeUtil.maskToId(userCode);
        return userClient.updateCustomerTag(companyType, userId, operator, tagList);
    }

    public BaseResult<List<UcCustomerTag>> getUserCustomTagList(Integer userId) {
        return userClient.getCustomerTagList(userId);
    }

    public BaseResult<UcCustomerTagGroup> getCustomerTagGroup(Integer id) {
        return userClient.getCustomerTagGroup(id);
    }

    public BaseResult<UcCustomerTagGroup> createCustomerTagGroup(CreateTagGroupReq req) {
        return userClient.createCustomerTagGroup(req);
    }

    public BaseResult<Void> updateCustomerTagGroup(Integer groupId, Boolean enabled) {
        return userClient.updateCustomerTagGroup(groupId, enabled);
    }

    public BaseResult<List<UcCustomerTag>> getCustomerTagList(Integer groupId) {
        return userClient.getCustomerTagList(groupId, null, null);
    }

    public BaseResult<Void> createCustomerTag(CreateTagReq req) {
        return userClient.createCustomerTag(req);
    }

    public PageResult<List<CustomerTagResp>> getGroupTagPage(Integer companyType, Boolean groupEnabled, Boolean tagEnable, Boolean asc, Integer current, Integer size) {
        return userClient.getGroupTagPage(companyType, groupEnabled, tagEnable, asc, current, size);
    }

    public PageResult<List<CustomerTagRecordResp>> getCustomerTagRecord(Integer companyType, String userCode, Integer current, Integer size) {
        int userId = DesensitizeUtil.maskToId(userCode);
        PageResult<List<CustomerTagRecordResp>> result = userClient.getCustomerTagRecord(companyType, userId, current, size);
        if (result.isPresent()) {
            Set<Integer> saleSet = result.getData().stream().map(CustomerTagRecordResp::getOperator).collect(Collectors.toSet());
            Map<Integer, UcUsers> salesMap = userClient.batchGetUserMap(BatchReq.create(saleSet)).orElse(null);
            Map<Integer, UcRole> salesRoleMap = userClient.batchGetUserRoleMap(BatchReq.create(saleSet)).orElse(null);
            for (CustomerTagRecordResp resp : result.getData()) {
                if (salesMap.containsKey(resp.getOperator())) {
                    resp.setOperatorName(salesMap.get(resp.getOperator()).getRealName());
                    resp.setAvatarUrl(salesMap.get(resp.getOperator()).getAvatarUrl());
                }

                if (salesRoleMap.containsKey(resp.getOperator())) {
                    resp.setOperatorRole(salesRoleMap.get(resp.getOperator()).getName());
                }
                resp.setUserCode(DesensitizeUtil.idToMask(resp.getUserId()));
            }
            return result;
        }
        return PageResult.empty();
    }

    public BaseResult<Void> setOperateRange(Integer groupId, List<Long> roleList) {
        return userClient.setOperateRange(groupId, roleList);
    }

    public BaseResult<List<UcCustomerTagGroupRoleRelation>> getOperateRange(Integer groupId) {
        return userClient.getOperateRange(groupId);
    }

    public BaseResult<Boolean> createBatchCustomerTag(Integer companyType, List<String> userCodeList, Integer operator, List<Integer> tagList) {
        List<Integer> userIdList = Optional.ofNullable(userCodeList).map(list -> list.stream().map(DesensitizeUtil::maskToId).collect(Collectors.toList())).orElse(new ArrayList<>());
        return userClient.createBatchCustomerTag(companyType, userIdList, operator, tagList);
    }

    public PageResult<List<UcCustomerTagRecordResp>> getTagRecordByOperator(Integer operatorId, Integer current, Integer size) {

        PageResult<List<CustomerTagRecordResp>> pageResult = userClient.getTagRecordByOperator(operatorId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<CustomerTagRecordResp> respList = pageResult.getData();
        Set<Integer> userIdSet = respList.stream().map(CustomerTagRecordResp::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).orElse(null);
        List<UcCustomerTagRecordResp> resps = respList.stream().map(record -> {
            UcCustomerTagRecordResp resp = new UcCustomerTagRecordResp();
            BeanUtils.copyProperties(record, resp);
            resp.setOperator(operatorId);
            resp.setNickName(usersMap.get(record.getUserId()).getNickName());
            resp.setRemark(usersMap.get(record.getUserId()).getRemark());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }
}

package cn.shrise.radium.adminapi.service.trade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.trade.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.constant.StockChannelType;
import cn.shrise.radium.tradeservice.dto.RecommendStockDto;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendStockService {

    private final TradeClient tradeClient;
    private final UserClient userClient;
    private final ContentClient contentClient;
    private final OrderClient orderClient;

    public PageResult<List<RecommendStockResp>> getRecommendStockList(Integer typeId, Boolean isTop, String searchText,
                                                                      Integer orderType, Integer timeType,
                                                                      List<Integer> creatorIds, Integer analystId,
                                                                      LocalDateTime startTime, LocalDateTime endTime,
                                                                      Boolean isDesc, Integer auditStatus,
                                                                      Integer current, Integer size) {
        PageResult<List<RecommendStockDto>> stockListResp = tradeClient.getRecommendStockList(AuthContextHolder.getCompanyType(), typeId, isTop, searchText, orderType,
                timeType, creatorIds, analystId, startTime, endTime, isDesc, auditStatus, current, size);
        if (ObjectUtil.isEmpty(stockListResp.getData())) {
            return PageResult.success(null, null);
        }
        Set<Integer> typeIds = stockListResp.getData().stream().map(i -> i.getStock().getTypeId()).collect(Collectors.toSet());
        PageResult<List<TdRecommendStockType>> stockTypeListResp = tradeClient.getRecommendStockTypeList(AuthContextHolder.getCompanyType(), typeIds, null, null, null);
        Map<Integer, String> typeNameMap = new HashMap<>();
        if (stockTypeListResp.isSuccess()) {
            typeNameMap = stockTypeListResp.getData().stream().collect(Collectors.toMap(TdRecommendStockType::getId, TdRecommendStockType::getName));
        }
        Set<Integer> userIds = new HashSet<>();
        List<Integer> analystIds = new ArrayList<>();
        stockListResp.getData().forEach(i -> {
            TdRecommendStock stockInfo = i.getStock();
            userIds.add(stockInfo.getCreatorId());
            if (ObjectUtil.isNotNull(stockInfo.getAuditorId())) {
                userIds.add(stockInfo.getAuditorId());
            }
            analystIds.add(stockInfo.getAnalystId());
        });

        Map<Integer, UserBaseInfoResp> userMap = userClient.batchGetBaseUserMap(BatchReq.create(userIds)).getData();
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, AuthContextHolder.getCompanyType(), null).getData();

        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        Map<Integer, String> finalTypeNameMap = typeNameMap;
        List<RecommendStockResp> recordRespList = stockListResp.getData().stream().map(i -> {
            TdRecommendStock stockInfo = i.getStock();
            TdRecommendStockProfit stockProfit = i.getStockProfit();
            SsAnalystInfo analystInfo = analystInfoMap.get(stockInfo.getAnalystId());
            RecommendStockResp r = new RecommendStockResp();
            BeanUtil.copyProperties(stockInfo, r);
            r.setTypeId(stockInfo.getTypeId());
            r.setTypeName(finalTypeNameMap.get(stockInfo.getTypeId()));
            r.setCreatorInfo(userMap.get(stockInfo.getCreatorId()));
            if (ObjectUtil.isNotNull(stockInfo.getAuditorId())) {
                r.setAuditorInfo(userMap.get(stockInfo.getAuditorId()));
            }
            r.setAnalystId(analystInfo.getId());
            r.setAnalystName(analystInfo.getName());
            if (ObjectUtil.isNotEmpty(stockProfit)) {
                r.setCostPrice(stockProfit.getCost());
                r.setCostTime(stockProfit.getCostTime());
                r.setMaxProfitRatio(stockProfit.getMaxProfitRatio());
            }
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, stockListResp.getPagination());
    }

    public PageResult<List<RecommendStockResp>> getRecommendStockAuditList(Integer companyType, Boolean isAudit,
                                                                           Integer current, Integer size) {
        PageResult<List<RecommendStockDto>> stockListResp = tradeClient.getRecommendStockAuditList(companyType, isAudit, current, size);
        if (ObjectUtil.isEmpty(stockListResp.getData())) {
            return PageResult.success(null, null);
        }
        Set<Integer> typeIds = stockListResp.getData().stream().map(i -> i.getStock().getTypeId()).collect(Collectors.toSet());
        PageResult<List<TdRecommendStockType>> stockTypeListResp = tradeClient.getRecommendStockTypeList(AuthContextHolder.getCompanyType(), typeIds, null, null, null);
        Map<Integer, String> typeNameMap = new HashMap<>();
        if (stockTypeListResp.isSuccess()) {
            typeNameMap = stockTypeListResp.getData().stream().collect(Collectors.toMap(TdRecommendStockType::getId, TdRecommendStockType::getName));
        }
        Set<Integer> userIds = new HashSet<>();
        List<Integer> analystIds = new ArrayList<>();
        stockListResp.getData().forEach(i -> {
            TdRecommendStock stockInfo = i.getStock();
            userIds.add(stockInfo.getCreatorId());
            userIds.add(stockInfo.getAuditorId());
            analystIds.add(stockInfo.getAnalystId());
        });

        Map<Integer, UserBaseInfoResp> userMap = userClient.batchGetBaseUserMap(BatchReq.create(userIds)).getData();
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, companyType, null).getData();

        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        Map<Integer, String> finalTypeNameMap = typeNameMap;
        List<RecommendStockResp> recordRespList = stockListResp.getData().stream().map(i -> {
            TdRecommendStock stockInfo = i.getStock();
            TdRecommendStockProfit stockProfit = i.getStockProfit();
            SsAnalystInfo analystInfo = analystInfoMap.get(stockInfo.getAnalystId());
            RecommendStockResp r = new RecommendStockResp();
            BeanUtil.copyProperties(stockInfo, r);
            r.setTypeId(stockInfo.getTypeId());
            r.setTypeName(finalTypeNameMap.get(stockInfo.getTypeId()));
            r.setCreatorInfo(userMap.get(stockInfo.getCreatorId()));
            r.setAuditorInfo(userMap.get(stockInfo.getAuditorId()));
            r.setAnalystId(analystInfo.getId());
            r.setAnalystName(analystInfo.getName());
            if (ObjectUtil.isNotEmpty(stockProfit)) {
                r.setCostPrice(stockProfit.getCost());
                r.setCostTime(stockProfit.getCostTime());
                r.setMaxProfitRatio(stockProfit.getMaxProfitRatio());
            }
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, stockListResp.getPagination());
    }

    public PageResult<List<RecommendStockModifyResp>> getRecommendStockModifyRecordPage(Integer stockId, Integer current, Integer size) {
        PageResult<List<TdRecommendStockModifyRecord>> pageResult = tradeClient.getRecommendStockModifyRecordPage(stockId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<TdRecommendStockModifyRecord> data = pageResult.getData();

        Set<Integer> userSet = data.stream().map(TdRecommendStockModifyRecord::getOperateId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));

        List<RecommendStockModifyResp> records = data.stream().map(t -> {
            RecommendStockModifyResp resp = new RecommendStockModifyResp();
            BeanUtil.copyProperties(t, resp);
            if (ObjectUtil.isNotEmpty(t.getOperateId())) {
                resp.setOperateName(usersMap.get(t.getOperateId()).getRealName());
                resp.setOperateAvatarUrl(usersMap.get(t.getOperateId()).getAvatarUrl());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }

    public PageResult<List<RecommendStockTypeResp>> getRecommendStockTypeList(Integer companyType, Collection<Integer> ids, Integer seriesType, Integer current, Integer size) {
        Set<Integer> seriesIds = new HashSet<>();
        if (ObjectUtil.isNotEmpty(seriesType)) {
            BaseResult<List<ArticleSeries>> seriesResult = orderClient.filterArticleSeries(null, AuthContextHolder.getCompanyType(), null, seriesType);
            if (seriesResult.isSuccess() && seriesResult.getData().size() != 0) {
                seriesIds = seriesResult.getData().stream().map(ArticleSeries::getId).collect(Collectors.toSet());
            }
        }
        PageResult<List<TdRecommendStockType>> result = tradeClient.getRecommendStockTypeList(companyType, ids, seriesIds, current, size);
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.success(null, null);
        }

        Set<Integer> seriesIdList = result.getData().stream().map(TdRecommendStockType::getSeriesId).collect(Collectors.toSet());
        List<ArticleSeries> seriesResult = orderClient.filterArticleSeries(seriesIdList, AuthContextHolder.getCompanyType(), null, null).getData();
        Map<Integer, String> seriesInfoMap = seriesResult.stream().collect(Collectors.toMap(ArticleSeries::getId, ArticleSeries::getName));

        List<RecommendStockTypeResp> recordRespList = result.getData().stream().map(i -> {
            RecommendStockTypeResp r = new RecommendStockTypeResp();
            BeanUtil.copyProperties(i, r);
            r.setSeriesName(seriesInfoMap.get(i.getSeriesId()));
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public PageResult<List<StockCaseNoticeResp>> getStockCaseNoticeList(Integer companyType, String channelNumber, Boolean isEnabled, Integer current, Integer size) {
        PageResult<List<TdRecommendStockNotice>> stockNoticeListResult = tradeClient.getRecommendStockNoticeList(companyType, "new", null, channelNumber, isEnabled, current, size);
        if (stockNoticeListResult.isFail() || !stockNoticeListResult.isPresent())  {
            return PageResult.empty();
        }
        List<TdRecommendStockNotice> noticeList = stockNoticeListResult.getData();
        Set<Integer> authorIds = noticeList.stream().map(TdRecommendStockNotice::getAuthorID).collect(Collectors.toSet());
        Set<String> channelNumbers = noticeList.stream().map(TdRecommendStockNotice::getChannelNumber).collect(Collectors.toSet());

        Map<Integer, UserBaseInfoResp> userMap = userClient.batchGetBaseUserMap(new BatchReq<>(authorIds)).orElse(new HashMap<>());
        List<ServiceIntroduction> poolCaseServiceIntroductionList = tradeClient.getPoolCaseServiceIntroductionList(companyType, BatchReq.of(channelNumbers)).orElse(Collections.emptyList());
        Map<String, String> caseChannelMap = poolCaseServiceIntroductionList.stream().collect(Collectors.toMap(ServiceIntroduction::getNumber, ServiceIntroduction::getName));
        List<StockCaseNoticeResp> resps = noticeList.stream().map(notice -> StockCaseNoticeResp.builder()
                .id(notice.getId())
                .channelNumber(notice.getChannelNumber())
                .channelName(caseChannelMap.getOrDefault(notice.getChannelNumber(), null))
                .createTime(notice.getCreateTime())
                .authorId(notice.getAuthorID())
                .authorName(userMap.get(notice.getAuthorID()).getRealName())
                .content(notice.getContent())
                .enabled(notice.getEnabled())
                .build()).collect(Collectors.toList());
        return PageResult.success(resps, stockNoticeListResult.getPagination());
    }

    public PageResult<List<RecommendStockNoticeResp>> getRecommendStockNoticeList(Integer companyType, Integer typeId, Boolean isEnabled, Integer current, Integer size) {

        PageResult<List<TdRecommendStockNotice>> stockNoticeListResult = tradeClient.getRecommendStockNoticeList(companyType, typeId, isEnabled, current, size);

        if (stockNoticeListResult.isFail()) {
            throw new BusinessException(stockNoticeListResult);
        }

        if (!stockNoticeListResult.isPresent()) {
            return PageResult.empty();
        }

        List<TdRecommendStockNotice> noticeList = stockNoticeListResult.getData();
        Set<Integer> authorIds = noticeList.stream().map(TdRecommendStockNotice::getAuthorID).collect(Collectors.toSet());
        Set<Integer> typeIds = noticeList.stream().map(TdRecommendStockNotice::getTypeID).collect(Collectors.toSet());

        BaseResult<Map<Integer, UserBaseInfoResp>> userBaseResult = userClient.batchGetBaseUserMap(new BatchReq<>(authorIds));
        if (userBaseResult.isFail()) {
            throw new BusinessException(userBaseResult);
        }
        Map<Integer, UserBaseInfoResp> userMap = userBaseResult.getData();

        PageResult<List<TdRecommendStockType>> stockTypeResult = tradeClient.getRecommendStockTypeList(companyType, typeIds, null, null, null);
        if (stockTypeResult.isFail()) {
            throw new BusinessException(stockTypeResult);
        }
        Map<Integer, String> stockTypeMap = stockTypeResult.getData().stream().collect(Collectors.toMap(TdRecommendStockType::getId, TdRecommendStockType::getName));

        List<RecommendStockNoticeResp> resps = noticeList.stream().map(notice -> {
            return RecommendStockNoticeResp.builder()
                    .id(notice.getId())
                    .typeId(notice.getTypeID())
                    .typeName(stockTypeMap.get(notice.getTypeID()))
                    .createTime(notice.getCreateTime())
                    .authorId(notice.getAuthorID())
                    .authorName(userMap.get(notice.getAuthorID()).getRealName())
                    .content(notice.getContent())
                    .enabled(notice.getEnabled())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(resps, stockNoticeListResult.getPagination());
    }

    public PageResult<List<RecommendStockTypeRecordResp>> getRecommendStockTypeRecordList(Integer typeId, Integer current, Integer size) {
        PageResult<List<TdRecommendStockTypeRecord>> pageResult = tradeClient.getRecommendStockTypeRecordList(typeId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }

        List<TdRecommendStockTypeRecord> data = pageResult.getData();

        Set<Integer> userSet = data.stream().map(TdRecommendStockTypeRecord::getOperatorId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));

        List<RecommendStockTypeRecordResp> records = data.stream().map(t -> {
            RecommendStockTypeRecordResp resp = new RecommendStockTypeRecordResp();
            BeanUtil.copyProperties(t, resp);
            resp.setOperateName(usersMap.get(t.getOperatorId()).getRealName());
            resp.setOperateAvatarUrl(usersMap.get(t.getOperatorId()).getAvatarUrl());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }
}

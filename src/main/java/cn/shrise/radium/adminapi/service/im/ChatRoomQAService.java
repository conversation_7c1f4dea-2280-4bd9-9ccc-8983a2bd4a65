package cn.shrise.radium.adminapi.service.im;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.im.ChatQaAnswerResp;
import cn.shrise.radium.adminapi.resp.im.ChatQaInfoResp;
import cn.shrise.radium.adminapi.resp.im.ChatQaMessageResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatQaAnswer;
import cn.shrise.radium.imservice.entity.ImChatQaMessage;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import cn.shrise.radium.imservice.req.DistributeQABelongReq;
import cn.shrise.radium.imservice.resp.ChatRoomQAInfoResp;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static java.util.concurrent.CompletableFuture.supplyAsync;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatRoomQAService {

    private final ImClient imClient;
    private final UserClient userClient;
    private final ContentClient contentClient;
    private final OrderClient orderClient;

    private final Executor adminExecutor;

    public PageResult<List<ChatQaMessageResp>> getChatRoomQAList(Integer companyType, Long chatRoomId, Integer status, Integer belongId, Boolean isPush, LocalDate startTime, LocalDate endTime, String searchContent, Integer current, Integer size) {

        PageResult<List<ImChatQaMessage>> result = imClient.getChatRoomQAList(chatRoomId, status, belongId, isPush, startTime, endTime, searchContent, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<ImChatQaMessage> qaMessages = result.getData();
        Set<Integer> userIdSet = qaMessages.stream().map(ImChatQaMessage::getCustomerId).collect(Collectors.toSet());
        Set<Integer> belongIdSet = qaMessages.stream().map(ImChatQaMessage::getBelongId).collect(Collectors.toSet());
        userIdSet.addAll(belongIdSet);
        List<Integer> analystIds = qaMessages.stream().map(ImChatQaMessage::getAnalystId).collect(Collectors.toList());

        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElseThrow();
        Map<Integer, SsAnalystInfo> analystInfoMap = contentClient.getAnalystInfoList(analystIds, companyType).orElseThrow().stream().collect(Collectors.toMap(SsAnalystInfo::getId, r -> r));
        List<ChatQaMessageResp> resps = qaMessages.stream().map(e -> {
            VipSubscription vipSubscription;
            VipPackage vipPackage = null;
            // 非营销聊天室需要查询服务包信息
            vipSubscription = orderClient.getUserVipSubscription(e.getCustomerId(), 2).orElse(new VipSubscription());
            if (ObjectUtil.isNotEmpty(vipSubscription.getNumber())) {
                vipPackage = orderClient.getVipPackage(e.getCompanyType(), vipSubscription.getNumber()).orElseThrow();
            }
            ChatQaMessageResp resp = new ChatQaMessageResp();
            BeanUtils.copyProperties(e, resp);
            resp.setAnalystName(analystInfoMap.get(e.getAnalystId()).getName());
            resp.setCustomerName(usersMap.get(e.getCustomerId()).getNickName());
            resp.setBelongName(ObjectUtil.isNull(e.getBelongId()) ? null : usersMap.get(e.getBelongId()).getRealName());
            resp.setCustomerAvatar(usersMap.get(e.getCustomerId()).getAvatarUrl());
            if (ObjectUtil.isAllNotEmpty(vipSubscription, vipPackage)) {
                resp.setVipPackageName(vipPackage.getName());
                resp.setOpenTime(vipSubscription.getOpenTime());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, result.getPagination());
    }

    public BaseResult<ChatQaInfoResp> getChatRoomQAInfo(Long messageId) {
        BaseResult<ChatRoomQAInfoResp> result = imClient.getChatRoomQAInfo(messageId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            return BaseResult.success(new ChatQaInfoResp());
        }
        ChatRoomQAInfoResp data = result.getData();
        ImChatQaMessage message = data.getMessage();
        Integer analystId = message.getAnalystId();
        Integer customerId = message.getCustomerId();
        List<Integer> userIds = new ArrayList<>();
        userIds.add(customerId);
        if (ObjectUtil.isNotEmpty(message.getBelongId())) {
            userIds.add(message.getBelongId());
        }
        CompletableFuture<Map<Integer, UcUsers>> userMapCompletableFuture = supplyAsync(() -> userClient.batchGetUserMap(BatchReq.of(userIds)).orElseThrow(), adminExecutor);
        CompletableFuture<SsAnalystInfo> analystInfoCompletableFuture = supplyAsync(() -> contentClient.getAnalystInfo(analystId).orElse(null), adminExecutor);
        CompletableFuture<ImChatRoom> imChatRoomCompletableFuture = supplyAsync(() -> imClient.getChatRoom(message.getChatId()).orElseThrow(), adminExecutor);
        CompletableFuture<Void> allFuture = CompletableFuture.allOf(
                userMapCompletableFuture,
                analystInfoCompletableFuture,
                imChatRoomCompletableFuture);

        allFuture.join();

        Map<Integer, UcUsers> usersMap = userMapCompletableFuture.join();
        SsAnalystInfo analystInfo = analystInfoCompletableFuture.join();
        ImChatRoom chatRoom = imChatRoomCompletableFuture.join();

        VipSubscription vipSubscription;
        VipPackage vipPackage = null;
        List<String> vipPackageName = new ArrayList<>();
        // 非营销聊天室需要查询服务包信息
        vipSubscription = orderClient.getUserVipSubscription(message.getCustomerId(), 2).orElse(new VipSubscription());
        if (ObjectUtil.isNotEmpty(vipSubscription.getNumber())) {
            vipPackage = orderClient.getVipPackage(message.getCompanyType(), vipSubscription.getNumber()).orElseThrow();
        }
        ChatQaInfoResp resp = new ChatQaInfoResp();
        ChatQaMessageResp messageResp = new ChatQaMessageResp();
        BeanUtils.copyProperties(message, messageResp);
        if (ObjectUtil.isNotEmpty(messageResp.getBelongId())) {
            messageResp.setBelongName(usersMap.get(messageResp.getBelongId()).getRealName());
        }
        messageResp.setCustomerName(usersMap.get(messageResp.getCustomerId()).getNickName());
        messageResp.setAnalystName(analystInfo.getName());
        messageResp.setCustomerAvatar(usersMap.get(messageResp.getCustomerId()).getAvatarUrl());
        resp.setMessage(messageResp);
        if (ObjectUtil.isNotEmpty(data.getAnswer())) {
            List<ImChatQaAnswer> answer = data.getAnswer();
            List<ChatQaAnswerResp> answerRespList = answer.stream().map(e -> {
                ChatQaAnswerResp answerResp = new ChatQaAnswerResp();
                BeanUtils.copyProperties(e, answerResp);
                answerResp.setAnswerName(analystInfo.getName());
                answerResp.setAnswerAvatar(analystInfo.getAvatarUrl());
                return answerResp;
            }).collect(Collectors.toList());
            resp.setAnswer(answerRespList);
        }
        if (ObjectUtil.isAllNotEmpty(vipSubscription, vipPackage)) {
            resp.setVipPackageName(vipPackage.getName());
            resp.setOpenTime(vipSubscription.getOpenTime());
            resp.setExpireTime(vipSubscription.getExpireTime());
        }
        return BaseResult.success(resp);
    }

    public BaseResult<String> distributeChatRoomQABelong(DistributeQABelongReq req) {
        return imClient.distributeChatRoomQABelong(req);
    }
}

package cn.shrise.radium.adminapi.service.trade;

import cn.shrise.radium.adminapi.resp.trade.PromotionStockResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdPromotionStock;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PromotionStockService {

    private final TradeClient tradeClient;

    public PageResult<List<PromotionStockResp>> getPromotionStockList(LocalDateTime startTime, LocalDateTime endTime, Integer channel, Integer current, Integer size) {
        PageResult<List<TdPromotionStock>> result = tradeClient.getPromotionStockList(startTime, endTime, channel, current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<TdPromotionStock> promotionStocks = result.getData();
        return PageResult.success(promotionStocks.stream().map(stock -> PromotionStockResp.builder()
                .id(stock.getId())
                .code(stock.getCode())
                .price(stock.getPrice())
                .channel(stock.getChannel())
                .chooseTime(stock.getChooseTime())
                .build()).collect(Collectors.toList()), result.getPagination());
    }
}

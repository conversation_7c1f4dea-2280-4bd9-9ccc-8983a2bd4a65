package cn.shrise.radium.adminapi.service.douyin;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.douyinservice.DouYinClient;
import cn.shrise.radium.douyinservice.resp.DyNoticeRecordResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DyNoticeRecordService {

    private final DouYinClient douYinClient;

    public PageResult<List<DyNoticeRecordResp>> getNoticeRecord(Integer templateType, Long templateId, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        final PageResult<List<DyNoticeRecordResp>> noticeRecord = douYinClient.getNoticeRecord(templateType, templateId, startTime, endTime, current, size);
        if (noticeRecord.isFail()) {
            throw new BusinessException(noticeRecord);
        }
        final List<DyNoticeRecordResp> noticeRecordData = noticeRecord.getData();
        if (ObjectUtils.isEmpty(noticeRecordData)) {
            return PageResult.empty(current, size);
        }
        return PageResult.success(noticeRecordData, Pagination.of(current, size, noticeRecord.getPagination().getTotal()));
    }

}

package cn.shrise.radium.adminapi.service.baidu;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.adminapi.resp.baidu.BaiduAdSubUserResp;
import cn.shrise.radium.adminapi.resp.baidu.BaiduAdUserResp;
import cn.shrise.radium.adminapi.service.ExportExcelService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpBdAdSubUser;
import cn.shrise.radium.marketingservice.entity.NpBdAdUser;
import cn.shrise.radium.marketingservice.resp.BaiduAdReportResp;
import cn.shrise.radium.orderservice.entity.RsFileExportRecord;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.bean.ExportFileInfo;
import cn.shrise.radium.statisticsservice.constant.ExportFileEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BaiduService {

    private final MarketingClient marketingClient;
    private final ExportExcelService exportExcelService;
    private final StatisticsClient statisticsClient;

    public BaseResult<List<BaiduAdUserResp>> getBaiduAdUserList() {
        List<NpBdAdUser> baiduAdUserList = marketingClient.getBaiduAdUserList().getData();
        List<BaiduAdUserResp> collect = baiduAdUserList.stream().map(user ->
                        BeanUtil.copyProperties(user, BaiduAdUserResp.class))
                .collect(Collectors.toList());
        return BaseResult.success(collect);
    }

    public PageResult<List<BaiduAdSubUserResp>> getBaiduAdSubUserList(
            Long cid, Integer status, Integer searchType, String searchContent, Integer current, Integer size) {
        PageResult<List<NpBdAdSubUser>> pageResult = marketingClient.getBaiduAdSubUserList(cid, status,
                searchType, searchContent, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<NpBdAdSubUser> userList = pageResult.getData();
        Set<Long> masterIdSet = userList.stream().map(NpBdAdSubUser::getMasterId).collect(Collectors.toSet());
        Map<Long, NpBdAdUser> masterMap = marketingClient.getBaiduAdUserList(BatchReq.create(masterIdSet)).getData().stream()
                .collect(Collectors.toMap(NpBdAdUser::getUserId, Function.identity()));
        List<BaiduAdSubUserResp> records = pageResult.getData().stream().map(e ->
                        BaiduAdSubUserResp.builder()
                                .id(e.getId())
                                .gmtCreated(e.getGmtCreate())
                                .gmtModified(e.getGmtModified())
                                .userId(e.getUserId())
                                .userName(e.getUserName())
                                .masterUserId(e.getMasterId())
                                .masterUserName(masterMap.get(e.getMasterId()).getUserName())
                                .cid(e.getCid())
                                .liceName(e.getLiceName())
                                .balance(e.getBalance())
                                .budget(e.getBudget())
                                .balancePackage(e.getBalancePackage())
                                .userStat(e.getUserStat())
                                .uaStatus(e.getUaStatus())
                                .validFlows(JSONUtil.parseArray(e.getValidFlows()).toList(Integer.class))
                                .tradeId(e.getTradeId())
                                .budgetOfflineTime(e.getBudgetOfflineTime())
                                .adType(e.getAdType())
                                .build())
                .collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }

    public BaseResult<RsFileExportRecord> exportBaiduAdStreamReport(String fileName, LocalDate startDate, LocalDate endDate) {
        BaseResult<List<BaiduAdReportResp>> result = marketingClient.getBaiduAdStreamReport(startDate, endDate);
        List<BaiduAdReportResp> list = result.getData();
        RsFileExportRecord record = exportExcelService.genExportRecord(fileName);
        ExportFileInfo info = ExportFileInfo.builder()
                .recordId(record.getId())
                .fileEnum(ExportFileEnum.EXPORT_BAIDU_AD_STREAM_REPORT)
                .t(list)
                .build();
        BaseResult<String> sendRes = statisticsClient.exportExcel(info);
        if (sendRes.isFail()) {
            exportExcelService.markExportFile(info.getRecordId());
        }

        return BaseResult.success(record);
    }
}

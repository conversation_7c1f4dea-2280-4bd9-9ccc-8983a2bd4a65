package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.req.complaint.OrderComplaintReplyReq;
import cn.shrise.radium.adminapi.resp.order.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.resp.complaint.ComplaintDetailWxResp;
import cn.shrise.radium.orderservice.resp.complaint.ComplaintNegotiationWxResp;
import cn.shrise.radium.orderservice.resp.complaint.ComplaintWxOperateRecordResp;
import cn.shrise.radium.orderservice.resp.complaint.OrderComplaintWxResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OrderComplaintService {

    private final OrderClient orderClient;
    private final UserClient userClient;

    public List<AutoReplyOperateRecordResp> getAutoReplyOperateRecord(Long id) {
        BaseResult<List<RsComplaintReplyTemplateRecord>> result = orderClient.getAutoReplyOperateRecord(id);
        if (!result.isPresent()) {
            return Collections.emptyList();
        }
        Set<Integer> operatorIdSet = result.getData().stream().map(RsComplaintReplyTemplateRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(operatorIdSet)).orElse(new HashMap<>());
        List<AutoReplyOperateRecordResp> recordList = new ArrayList<>();
        for (RsComplaintReplyTemplateRecord record : result.getData()) {
            AutoReplyOperateRecordResp resp = new AutoReplyOperateRecordResp();
            BeanUtil.copyProperties(record, resp);
            resp.setOperateTime(record.getGmtCreate());
            resp.setAvatarUrl(usersMap.getOrDefault(record.getOperatorId(), new UcUsers()).getAvatarUrl());
            resp.setRealName(usersMap.getOrDefault(record.getOperatorId(), new UcUsers()).getRealName());
            recordList.add(resp);
        }
        return recordList;
    }

    public BaseResult<Void> complaintAutoReplyIsOpenUpdate(Long id, Boolean isOpen, Integer operatorId) {
        return orderClient.complaintAutoReplyIsOpenUpdate(id, isOpen, operatorId);
    }

    public BaseResult<Void> complaintAutoReplyUpdate(Long id, String content, Integer operatorId) {
        return orderClient.complaintAutoReplyUpdate(id, content, operatorId);
    }

    public ComplaintAutoReplyResp getComplaintAutoReply() {
        BaseResult<RsOrderComplaintReplyTemplate> result = orderClient.getComplaintAutoReply(1000L);
        if (!result.isPresent()) {
            return null;
        }
        ComplaintAutoReplyResp resp = new ComplaintAutoReplyResp();
        BeanUtil.copyProperties(result.getData(), resp);
        return resp;
    }

    public BaseResult<Void> customerReply(OrderComplaintReplyReq req) {
        return orderClient.customerReply(req);
    }

    public PageResult<List<ComplaintNegotiationWxResp>> getComplaintNegotiationWx(Long id, Integer current, Integer size) {
        PageResult<List<ComplaintNegotiationWxResp>> result = orderClient.getComplaintNegotiationWx(id, current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<ComplaintNegotiationWxResp> negotiationWxList = result.getData();
        for (ComplaintNegotiationWxResp complaintNegotiationWxResp : negotiationWxList) {
            if (ObjectUtil.isNotEmpty(complaintNegotiationWxResp.getImageUrlList())) {
                complaintNegotiationWxResp.setImageList(JSON.parseArray(complaintNegotiationWxResp.getImageUrlList()));
            }
        }
        return PageResult.success(negotiationWxList, result.getPagination());
    }

    public BaseResult<ComplaintDetailWxResp> getComplaintDetailWx(Long id) {
        BaseResult<ComplaintDetailWxResp> result = orderClient.getComplaintDetailWx(id);
        if (!result.isPresent()) {
            return BaseResult.success(null);
        }
        ComplaintDetailWxResp complaintDetailWxResp = result.getData();
        CourseSubOrder courseSubOrder = orderClient.getSubOrder(complaintDetailWxResp.getSubOrderNumber()).orElse(new CourseSubOrder());
        RsCourseOrder rsCourseOrder = orderClient.getOrder(courseSubOrder.getOrderId()).orElse(new RsCourseOrder());
        complaintDetailWxResp.setUserCode(DesensitizeUtil.idToMask(rsCourseOrder.getUserId()));
        if (ObjectUtil.isNotNull(rsCourseOrder.getUserId())) {
            UcWxExt ucWxExt = userClient.getWxExtInfo(rsCourseOrder.getUserId()).orElse(new UcWxExt());
            complaintDetailWxResp.setNickName(ucWxExt.getNickname());
        }
        return BaseResult.success(complaintDetailWxResp);
    }

    public PageResult<List<OrderComplaintWxResp>> getOrderComplaintWxList(
            LocalDateTime startTime, LocalDateTime endTime, String complaintStatus, Long merchantId, Integer category, Integer current, Integer size) {
        PageResult<List<OrderComplaintWxResp>> result = orderClient.getOrderComplaintWxList(startTime, endTime, complaintStatus, merchantId, category, current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<OrderComplaintWxResp> orderComplaintWxRespList = result.getData();
        List<String> subOrderNumberList = orderComplaintWxRespList.stream().map(OrderComplaintWxResp::getSubOrderNumber).collect(Collectors.toList());
        List<CourseSubOrder> courseSubOrders = orderClient.getCourseSubOrderList(subOrderNumberList).orElse(new ArrayList<>());
        Map<String, Integer> orderIdMap = courseSubOrders.stream().collect(Collectors.toMap(CourseSubOrder::getNumber, CourseSubOrder::getOrderId));
        List<Integer> orderIdList = courseSubOrders.stream().map(CourseSubOrder::getOrderId).collect(Collectors.toList());
        List<RsCourseOrder> rsCourseOrders = orderClient.batchGetOrderListById(orderIdList).orElse(new ArrayList<>());
        List<Integer> userIdList = rsCourseOrders.stream().map(RsCourseOrder::getUserId).collect(Collectors.toList());
        Map<Integer, RsCourseOrder> orderMap = rsCourseOrders.stream().collect(Collectors.toMap(RsCourseOrder::getId, Function.identity()));
        Map<Integer, UcWxExt> ucWxExtMap = userClient.getUnionIdsByUserId(userIdList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(UcWxExt::getUserId, Function.identity()));

        for (OrderComplaintWxResp orderComplaintWxResp : orderComplaintWxRespList) {
            RsCourseOrder order = orderMap.getOrDefault(orderIdMap.get(orderComplaintWxResp.getSubOrderNumber()), new RsCourseOrder());
            orderComplaintWxResp.setOrderNumber(order.getOrderNumber());
            orderComplaintWxResp.setNickName(ucWxExtMap.getOrDefault(order.getUserId(), new UcWxExt()).getNickname());
            orderComplaintWxResp.setUserCode(DesensitizeUtil.idToMask(order.getUserId()));
        }
        return PageResult.success(orderComplaintWxRespList, result.getPagination());
    }

    public BaseResult<Void> handleCompleted(Long id, Integer operatorId) {
        return orderClient.handleCompleted(id, operatorId);
    }

    public List<ComplaintWxOperateRecordResp> getComplaintWxOperateRecord(Long id) {
        BaseResult<List<RsOrderComplaintWechatOperateRecord>> result = orderClient.getComplaintWxOperateRecord(id);
        if (!result.isPresent()) {
            return Collections.emptyList();
        }
        List<RsOrderComplaintWechatOperateRecord> operateRecordList = result.getData();
        Set<Integer> userIds = operateRecordList.stream().map(RsOrderComplaintWechatOperateRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIds)).orElse(new HashMap<>());
        List<ComplaintWxOperateRecordResp> respList = new ArrayList<>();
        for (RsOrderComplaintWechatOperateRecord record : operateRecordList) {
            ComplaintWxOperateRecordResp resp = new ComplaintWxOperateRecordResp();
            BeanUtil.copyProperties(record, resp);
            resp.setOperateTime(record.getGmtCreate());
            resp.setAvatarUrl(usersMap.getOrDefault(record.getOperatorId(), new UcUsers()).getAvatarUrl());
            resp.setRealName(usersMap.getOrDefault(record.getOperatorId(), new UcUsers()).getRealName());
            respList.add(resp);
        }
        return respList;
    }

    public BaseResult<String> getMobileByComplaintId(Long id, Integer userId, Integer companyType) {
        return orderClient.getMobileByComplaintId(id, userId, companyType);
    }
}

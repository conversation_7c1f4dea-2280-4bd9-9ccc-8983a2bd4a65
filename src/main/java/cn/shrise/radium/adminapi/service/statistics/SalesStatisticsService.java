package cn.shrise.radium.adminapi.service.statistics;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.statistics.SalesOrderStatisticResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.entity.StSalesOrderStatistic;
import cn.shrise.radium.statisticsservice.req.SalesOrderStatisticsReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class SalesStatisticsService {

    private final StatisticsClient statisticsClient;
    private final UserClient userClient;

    public PageResult<List<SalesOrderStatisticResp>> getSalesOrderStatisticList(SalesOrderStatisticsReq req) {
        PageResult<List<StSalesOrderStatistic>> result = statisticsClient.getSalesOrderStatisticList(req);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.success();
        }
        List<StSalesOrderStatistic> data = result.getData();
        List<Integer> salesIds = data.stream().map(StSalesOrderStatistic::getSalesId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(salesIds)).orElse(null);
        List<SalesOrderStatisticResp> resps = data.stream().map(r -> {
            SalesOrderStatisticResp resp = SalesOrderStatisticResp.builder()
                    .salesId(r.getSalesId())
                    .resourceDate(r.getResourceDate())
                    .payDate(r.getPayDate())
                    .orderLevel(req.getOrderLevel())
                    .build();
            if (req.getOrderLevel().equals(1)) {
                resp.setOrderCount(r.getLevel1Count());
                resp.setOrderAmount(r.getLevel1Amount());
            } else if (req.getOrderLevel().equals(2)) {
                resp.setOrderCount(r.getLevel2Count());
                resp.setOrderAmount(r.getLevel2Amount());
            } else if (req.getOrderLevel().equals(3)) {
                resp.setOrderCount(r.getLevel3Count());
                resp.setOrderAmount(r.getLevel3Amount());
            }
            if (ObjectUtil.isNotEmpty(usersMap) && usersMap.containsKey(r.getSalesId())) {
                resp.setSalesName(usersMap.getOrDefault(r.getSalesId(), new UcUsers()).getRealName());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, result.getPagination());
    }
}

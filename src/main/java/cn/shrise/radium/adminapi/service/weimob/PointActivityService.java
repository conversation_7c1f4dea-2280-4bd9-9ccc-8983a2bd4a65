package cn.shrise.radium.adminapi.service.weimob;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.adminapi.resp.weimob.PointActivityResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.weimobservice.WeiMobClient;
import cn.shrise.radium.weimobservice.entity.PointActivity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PointActivityService {

    private final WeiMobClient weiMobClient;

    private final CommonProperties commonProperties;

    public PageResult<List<PointActivityResp>> getPointActivityList(Integer companyType, Integer current, Integer size, Integer status) {
        PageResult<List<PointActivity>> pageResult = weiMobClient.getPointActivityList(companyType, current, size, status);
        if (pageResult.isPresent()) {
            List<PointActivity> pointActivityList = pageResult.getData();
            List<PointActivityResp> pointActivityRespList = new ArrayList<>();
            for (PointActivity pointActivity : pointActivityList) {
                PointActivityResp resp = new PointActivityResp();
                BeanUtil.copyProperties(pointActivity, resp);
                if (ObjectUtil.isNotEmpty(pointActivity.getWxUrlNumber())) {
                    resp.setShortUrl(StrUtil.format("{}/n/{}", commonProperties.getShortUrl(), pointActivity.getWxUrlNumber()));
                }
                pointActivityRespList.add(resp);
            }
            return PageResult.success(pointActivityRespList, pageResult.getPagination());
        }
        return PageResult.empty();
    }
}

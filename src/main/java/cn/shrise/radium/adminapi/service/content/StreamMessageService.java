package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.content.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.constant.StreamMessageAuditStatusConstant;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.resp.ModerationLabelItem;
import cn.shrise.radium.contentservice.resp.StreamMessageResp;
import cn.shrise.radium.contentservice.resp.StreamMessageStatistics;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcContentTeam;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.contentservice.constant.AuditStatusConstant.AUDIT_PASS;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StreamMessageService {

    private final ContentClient contentClient;

    private final UserClient userClient;

    public PageResult<List<StreamMessageResp>> getStreamMessagePage(Integer companyType, Integer userId, LocalDate startTime, LocalDate endTime, Long channelId, Integer current, Integer size) {

        PageResult<List<StreamMessageResp>> streamMessagePage = contentClient.getStreamMessagePage(companyType, userId, startTime, endTime, channelId, current, size);
        if (ObjectUtil.isEmpty(streamMessagePage.getData())) {
            return PageResult.success(null, null);
        }

        Set<Integer> userIds = new HashSet<>();
        List<Integer> analystIds = new ArrayList<>();
        streamMessagePage.getData().forEach(i -> {
            userIds.add(i.getStreamMessage().getCreatorId());
            userIds.add(i.getStreamMessage().getAuditorId());
            analystIds.add(i.getStreamMessage().getAnalystId());
        });

        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        Map<Integer, SsAnalystInfo> analystInfoMap = contentClient.getAnalystInfoList(analystIds, companyType, null).getData().stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<StreamMessageResp> messageRespList = streamMessagePage.getData().stream().peek(i -> {
            i.setAnalystName(analystInfoMap.get(i.getStreamMessage().getAnalystId()).getName());
            i.setAnalystUrl(analystInfoMap.get(i.getStreamMessage().getAnalystId()).getAvatarUrl());
            i.setCreatorName(userMap.getOrDefault(i.getStreamMessage().getCreatorId(), new UcUsers()).getRealName());
            i.setAuditorName(userMap.getOrDefault(i.getStreamMessage().getAuditorId(), new UcUsers()).getRealName());
        }).collect(Collectors.toList());
        return PageResult.success(messageRespList, streamMessagePage.getPagination());
    }

    public PageResult<List<StreamMessageCommentResp>> getCommentPage(Long messageId, Boolean isChoice, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        PageResult<List<SsStreamMessageComment>> commentPage = contentClient.getCommentPage(messageId, isChoice, null, null, startTime, endTime, current, size);

        if (ObjectUtil.isEmpty(commentPage.getData())) {
            return PageResult.success(null, null);
        }

        Set<Integer> userSet = commentPage.getData().stream().map(SsStreamMessageComment::getCustomerId).collect(Collectors.toSet());

        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();

        List<Integer> analystIds = commentPage.getData().stream().map(SsStreamMessageComment::getAnswerId).collect(Collectors.toList());

        Map<Integer, SsAnalystInfo> analystInfoMap = contentClient.getAnalystInfoList(analystIds, AuthContextHolder.getCompanyType(), null).getData().stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<StreamMessageCommentResp> commentRespList = commentPage
                .getData()
                .stream()
                .map(i -> {
                    StreamMessageCommentResp.StreamMessageCommentRespBuilder builder = StreamMessageCommentResp.builder()
                            .streamMessageComment(i)
                            .customerName(userMap.get(i.getCustomerId()).getNickName());
                    if (ObjectUtil.isNotNull(i.getAnswerId())) {
                        builder.analystName(analystInfoMap.get(i.getAnswerId()).getName());
                    }
                    return builder
                            .build();
                })
                .collect(Collectors.toList());

        return PageResult.success(commentRespList, commentPage.getPagination());
    }

    public PageResult<List<TeamStreamMessageResp>> getTeamStreamMessagePage(Integer companyType, LocalDate startTime, LocalDate endTime, Long teamId, Integer current, Integer size) {

        PageResult<List<SsStreamMessage>> streamMessagePage = contentClient.getTeamStreamMessagePage(companyType, Collections.singletonList(teamId), null, startTime, endTime, current, size);
        if (ObjectUtil.isEmpty(streamMessagePage.getData())) {
            return PageResult.success(null, null);
        }

        Set<Integer> userIds = new HashSet<>();
        Set<Long> messageIds = new HashSet<>();
        streamMessagePage.getData().forEach(i -> {
            userIds.add(i.getCreatorId());
            userIds.add(i.getAuditorId());
            messageIds.add(i.getId());
        });

        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        Map<Long, Long> likeCountMap = contentClient.getStreamMessageLikeStatistics(BatchReq.create(messageIds)).getData().stream().collect(toMap(StreamMessageStatistics::getId, StreamMessageStatistics::getCount));
        List<TeamStreamMessageResp> messageRespList = new ArrayList<>();
        for (SsStreamMessage streamMessage : streamMessagePage.getData()) {
            TeamStreamMessageResp teamStreamMessageResp = new TeamStreamMessageResp();
            teamStreamMessageResp.setStreamMessage(streamMessage);

            if (ObjectUtil.isNotEmpty(streamMessage.getImageList())) {
                teamStreamMessageResp.setImageList(JSON.parseArray(streamMessage.getImageList(), String.class));
            }
            if (userMap.containsKey(streamMessage.getCreatorId())) {
                teamStreamMessageResp.setCreatorName(userMap.get(streamMessage.getCreatorId()).getRealName());
            }
            if (userMap.containsKey(streamMessage.getAuditorId())) {
                teamStreamMessageResp.setAuditorName(userMap.get(streamMessage.getAuditorId()).getRealName());
            }
            if (likeCountMap.containsKey(streamMessage.getId())) {
                teamStreamMessageResp.setLikeCount(likeCountMap.get(streamMessage.getId()));
            }
            messageRespList.add(teamStreamMessageResp);
        }
        return PageResult.success(messageRespList, streamMessagePage.getPagination());
    }

    public PageResult<List<TeamStreamMessageCommentResp>> getTeamCommentPage(Long messageId, Boolean audit, Integer auditStatus, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        PageResult<List<SsStreamMessageComment>> commentPage = contentClient.getCommentPage(messageId, null, audit, auditStatus, startTime, endTime, current, size);
        if (ObjectUtil.isEmpty(commentPage.getData())) {
            return PageResult.empty();
        }
        List<SsStreamMessageComment> commentPageData = commentPage.getData();
        Set<Integer> userSet = new HashSet<>();
        Set<Long> resultSet = new HashSet<>();
        commentPageData.forEach(e -> {
            userSet.add(e.getCustomerId());
            userSet.add(e.getAuditorId());
            resultSet.add(e.getResultId());
        });
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();

        List<TeamStreamMessageCommentResp> commentRespList = commentPageData.stream()
                .map(i -> {
                    TeamStreamMessageCommentResp commentResp = TeamStreamMessageCommentResp.of(i);
                    if (userMap.containsKey(i.getCustomerId())) {
                        commentResp.setCustomerName(userMap.get(i.getCustomerId()).getNickName());
                    }
                    if (ObjectUtil.isNotEmpty(i.getAuditorId()) && userMap.containsKey(i.getAuditorId())) {
                        commentResp.setAuditorName(userMap.get(i.getAuditorId()).getRealName());
                    }
                    if (ObjectUtil.isNotEmpty(i.getResultId()) && resultMap.containsKey(i.getResultId())) {
                        SsTextScanResult scanResult = resultMap.get(i.getResultId());
                        List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                        List<String> labelNameList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(labels)) {
                            Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                            for (String label : labels) {
                                labelNameList.add(itemMap.get(label).getName());
                            }
                        }
                        TextScanResultResp resultResp = new TextScanResultResp();
                        resultResp.setLabels(labels);
                        resultResp.setLabelMsg(labelNameList);
                        commentResp.setPassed(scanResult.getPassed());
                        commentResp.setResultResp(resultResp);
                    }
                    return commentResp;
                }).collect(Collectors.toList());
        return PageResult.success(commentRespList, commentPage.getPagination());
    }

    public PageResult<List<TeamStreamMessageResp>> getTeamShareStreamMessagePage(Integer companyType, LocalDate startTime, LocalDate endTime, Long teamId, Integer current, Integer size) {
        List<Long> teamIds = new ArrayList<>();
        if (ObjectUtil.isEmpty(teamId)) {
            teamIds = userClient.getVisibleByUser(AuthContextHolder.getUserId()).orElse(null);
            if (ObjectUtil.isEmpty(teamIds)) {
                return PageResult.empty();
            }
        }
        teamIds.add(teamId);
        PageResult<List<SsStreamMessage>> streamMessagePage = contentClient.getTeamStreamMessagePage(companyType, teamIds, AUDIT_PASS, startTime, endTime, current, size);
        if (ObjectUtil.isEmpty(streamMessagePage.getData())) {
            return PageResult.empty();
        }

        Set<Long> messageIds = new HashSet<>();
        Set<Long> teamSet = new HashSet<>();
        streamMessagePage.getData().forEach(i -> {
            messageIds.add(i.getId());
            teamSet.add(i.getTeamId());
        });

        Map<Long, UcContentTeam> teamMap = userClient.batchContentTeamMap(BatchReq.create(teamSet)).getData();
        Map<Long, Long> likeCountMap = contentClient.getStreamMessageLikeStatistics(BatchReq.create(messageIds)).getData().stream().collect(toMap(StreamMessageStatistics::getId, StreamMessageStatistics::getCount));
        Map<Long, Long> commentCountMap = contentClient.getStreamMessageCommentStatistics(BatchReq.create(messageIds)).getData().stream().collect(toMap(StreamMessageStatistics::getId, StreamMessageStatistics::getCount));
        List<TeamStreamMessageResp> messageRespList = new ArrayList<>();
        for (SsStreamMessage streamMessage : streamMessagePage.getData()) {
            TeamStreamMessageResp teamStreamMessageResp = new TeamStreamMessageResp();
            teamStreamMessageResp.setStreamMessage(streamMessage);

            if (ObjectUtil.isNotEmpty(streamMessage.getImageList())) {
                teamStreamMessageResp.setImageList(JSON.parseArray(streamMessage.getImageList(), String.class));
            }

            if (likeCountMap.containsKey(streamMessage.getId())) {
                teamStreamMessageResp.setLikeCount(likeCountMap.get(streamMessage.getId()));
            }

            if (commentCountMap.containsKey(streamMessage.getId())) {
                teamStreamMessageResp.setCommentCount(commentCountMap.get(streamMessage.getId()));
            }

            if (teamMap.containsKey(streamMessage.getTeamId())) {
                UcContentTeam team = teamMap.get(streamMessage.getTeamId());
                teamStreamMessageResp.setAvatarUrl(team.getAvatarUrl());
                teamStreamMessageResp.setTeamName(team.getName());
            }


            messageRespList.add(teamStreamMessageResp);
        }
        return PageResult.success(messageRespList, streamMessagePage.getPagination());
    }

    public BaseResult<List<SsStreamOperateRecordResp>> getStreamOperateRecord(Long streamId) {
        List<SsStreamOperateRecordResp> resp = new ArrayList<>();
        BaseResult<List<SsStreamOperateRecord>> operateRecord = contentClient.getStreamOperateRecord(streamId);
        if (!operateRecord.isPresent()) {
            return BaseResult.success(resp);
        }
        List<SsStreamOperateRecord> record = operateRecord.getData();
        List<Integer> operatorId = record.stream().map(SsStreamOperateRecord::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(operatorId)) {
            usersMap = userClient.batchGetUserMap(BatchReq.of(operatorId)).getData();
        }
        for (SsStreamOperateRecord ssStreamOperateRecord : record) {
            SsStreamOperateRecordResp ssStreamOperateRecordResp = new SsStreamOperateRecordResp();
            BeanUtils.copyProperties(ssStreamOperateRecord, ssStreamOperateRecordResp);
            if (ObjectUtil.isNotEmpty(ssStreamOperateRecord.getOperatorId())) {
                ssStreamOperateRecordResp.setAvatarUrl(usersMap.get(ssStreamOperateRecord.getOperatorId()).getAvatarUrl());
            }
            resp.add(ssStreamOperateRecordResp);
        }
        return BaseResult.success(resp);
    }

    public PageResult<List<StreamMessageResp>> getStreamMessageAuditList(Integer companyType, Boolean isAudit, Integer current, Integer size) {

        PageResult<List<StreamMessageResp>> pageResult = contentClient.getStreamMessageAuditList(companyType, isAudit, current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.success(null, null);
        }

        Set<Integer> userIds = new HashSet<>();
        List<Integer> analystIds = new ArrayList<>();
        pageResult.getData().forEach(i -> {
            userIds.add(i.getStreamMessage().getCreatorId());
            userIds.add(i.getStreamMessage().getAuditorId());
            analystIds.add(i.getStreamMessage().getAnalystId());
        });

        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        Map<Integer, SsAnalystInfo> analystInfoMap = contentClient.getAnalystInfoList(analystIds, companyType, null).getData().stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<StreamMessageResp> messageRespList = pageResult.getData().stream().peek(i -> {
            i.setCreatorName(userMap.get(i.getStreamMessage().getCreatorId()).getRealName());
            if (ObjectUtil.isNotEmpty(i.getStreamMessage().getAuditorId())) {
                i.setAuditorName(userMap.get(i.getStreamMessage().getAuditorId()).getRealName());
            }
            if (ObjectUtil.isNotEmpty(i.getStreamMessage().getAnalystId())) {
                i.setAnalystName(analystInfoMap.get(i.getStreamMessage().getAnalystId()).getName());
                i.setAnalystUrl(analystInfoMap.get(i.getStreamMessage().getAnalystId()).getAvatarUrl());
            }
        }).collect(Collectors.toList());
        return PageResult.success(messageRespList, pageResult.getPagination());
    }

    public BaseResult<Void> passStreamMessage(Integer userId, Long messageId) {
        return contentClient.auditStreamMessage(userId, messageId, StreamMessageAuditStatusConstant.AUDIT_PASS, null);
    }

    public BaseResult<Void> refuseStreamMessage(Integer userId, Long messageId, String refuseRemark) {
        return contentClient.auditStreamMessage(userId, messageId, StreamMessageAuditStatusConstant.AUDIT_NOT_PASS, refuseRemark);
    }
}

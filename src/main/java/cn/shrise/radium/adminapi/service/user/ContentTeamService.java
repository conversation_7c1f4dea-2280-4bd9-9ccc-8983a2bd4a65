package cn.shrise.radium.adminapi.service.user;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.resp.ContentTeamResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2024/4/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContentTeamService {

    private final UserClient userClient;

    private final ContentClient contentClient;

    public PageResult<List<ContentTeamResp>> getContentTeamList(Integer companyType, List<Long> teamIds, Boolean enabled, Integer userId, Integer current, Integer size) {
        PageResult<List<ContentTeamResp>> pageResult = userClient.getContentTeamList(companyType, teamIds, enabled, userId, current, size);
        if (pageResult.isSuccess()) {
            List<ContentTeamResp> respList = pageResult.getData();
            List<Integer> analystIdList = respList.stream().map(ContentTeamResp::getAnalystId).collect(toList());
            if (analystIdList.isEmpty()) {
                return pageResult;
            }
            Map<Integer, SsAnalystInfo> analystInfoMap = contentClient.getAnalystInfoList(BatchReq.of(analystIdList)).getData().stream().collect(toMap(SsAnalystInfo::getId, x -> x));
            respList.forEach(x -> {
                SsAnalystInfo analystInfo = analystInfoMap.get(x.getAnalystId());
                if (analystInfo != null) {
                    x.setAnalystName(analystInfo.getName());
                }
            });
        }
        return pageResult;
    }
}

package cn.shrise.radium.adminapi.service.user;

import cn.shrise.radium.adminapi.resp.user.StaffRoleRecordResp;
import cn.shrise.radium.adminapi.resp.user.StaffRoleRelationRecordResp;
import cn.shrise.radium.adminapi.resp.user.StaffRoleRelationResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffRoleService {

    private final UserClient userClient;

    public PageResult<List<StaffRoleRecordResp>> getStaffRoleRecordList(Integer current, Integer size) {
        PageResult<List<UcStaffRoleRecord>> respResult = userClient.getStaffRoleRecordList(current, size);
        if (respResult.isFail()) {
            throw new BusinessException(respResult);
        }
        List<UcStaffRoleRecord> records = respResult.getData();
        Set<Integer> userIdSet = records.stream().map(UcStaffRoleRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<StaffRoleRecordResp> respList = records.stream()
                .map(e -> StaffRoleRecordResp.builder()
                        .gmtCreate(e.getGmtCreate())
                        .operatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName())
                        .content(e.getContent())
                        .build())
                .collect(Collectors.toList());
        return PageResult.success(respList, respResult.getPagination());
    }

    public PageResult<List<StaffRoleRelationRecordResp>> getStaffRoleRelationRecordList(Integer current, Integer size) {
        PageResult<List<UcStaffRoleRelationRecord>> respResult = userClient.getStaffRoleRelationRecordList(current, size);
        if (respResult.isFail()) {
            throw new BusinessException(respResult);
        }
        List<UcStaffRoleRelationRecord> records = respResult.getData();
        Set<Integer> userIdSet = records.stream().map(UcStaffRoleRelationRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<StaffRoleRelationRecordResp> respList = records.stream()
                .map(e -> StaffRoleRelationRecordResp.builder()
                        .gmtCreate(e.getGmtCreate())
                        .operatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName())
                        .content(e.getContent())
                        .build())
                .collect(Collectors.toList());
        return PageResult.success(respList, respResult.getPagination());
    }

    public PageResult<List<StaffRoleRelationResp>> getStaffRoleRelationList(List<String> roleNumberList, Integer current, Integer size) {
        PageResult<List<UcStaffRoleRelation>> respResult = userClient.getStaffRoleRelationList(roleNumberList, true, current, size);
        if (respResult.isFail()) {
            throw new BusinessException(respResult);
        }
        List<UcStaffRoleRelation> records = respResult.getData();
        Set<Integer> userIdSet = records.stream().map(UcStaffRoleRelation::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        Set<Long> roleIdSet = records.stream().map(UcStaffRoleRelation::getRoleId).collect(Collectors.toSet());
        Map<Long, UcStaffRole> roleMap = userClient.batchGetStaffRoleMap(BatchReq.of(roleIdSet)).getData();
        List<StaffRoleRelationResp> respList = records.stream()
                .map(e -> StaffRoleRelationResp.builder()
                        .userId(e.getUserId())
                        .userName(usersMap.getOrDefault(e.getUserId(), new UcUsers()).getRealName())
                        .roleId(e.getRoleId())
                        .roleNumber(roleMap.getOrDefault(e.getRoleId(), new UcStaffRole()).getNumber())
                        .roleName(roleMap.getOrDefault(e.getRoleId(), new UcStaffRole()).getName())
                        .gmtCreate(e.getGmtCreate())
                        .build())
                .collect(Collectors.toList());
        return PageResult.success(respList, respResult.getPagination());
    }

    public BaseResult<List<StaffRoleRelationResp>> getStaffRoleRelationListByRoleId(Long roleId) {
        BaseResult<List<UcStaffRoleRelation>> respResult = userClient.getStaffRoleRelationListByRoleId(roleId);
        if (respResult.isFail()) {
            throw new BusinessException(respResult);
        }
        List<UcStaffRoleRelation> records = respResult.getData();
        Set<Integer> userIdSet = records.stream().map(UcStaffRoleRelation::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        Set<Long> roleIdSet = records.stream().map(UcStaffRoleRelation::getRoleId).collect(Collectors.toSet());
        Map<Long, UcStaffRole> roleMap = userClient.batchGetStaffRoleMap(BatchReq.of(roleIdSet)).getData();
        List<StaffRoleRelationResp> respList = records.stream()
                .map(e -> StaffRoleRelationResp.builder()
                        .userId(e.getUserId())
                        .userName(usersMap.getOrDefault(e.getUserId(), new UcUsers()).getRealName())
                        .roleId(e.getRoleId())
                        .roleName(roleMap.getOrDefault(e.getRoleId(), new UcStaffRole()).getName())
                        .gmtCreate(e.getGmtCreate())
                        .build())
                .collect(Collectors.toList());
        return BaseResult.success(respList);
    }
}

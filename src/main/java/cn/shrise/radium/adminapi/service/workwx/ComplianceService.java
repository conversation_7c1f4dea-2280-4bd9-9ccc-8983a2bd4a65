package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.properties.DirectoryOssConfigInfo;
import cn.shrise.radium.common.properties.OssConfigProperties;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsMaterial;
import cn.shrise.radium.contentservice.entity.SsOriginMaterial;
import cn.shrise.radium.workwxchatimagematerialservice.WorkwxChatImageMaterialClient;
import cn.shrise.radium.workwxchatimagematerialservice.entity.ChatImageMaterialRelation;
import cn.shrise.radium.workwxchatservice.WorkwxChatClient;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.elasticsearch.WorkWxChatRecordInfo;
import cn.shrise.radium.workwxservice.entity.WxAccountExternalUserInfo;
import cn.shrise.radium.workwxservice.resp.ChatRecordRiskResp;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComplianceService {

    private final WorkwxClient workwxClient;
    private final WorkwxChatClient workwxChatClient;
    private final WorkwxChatImageMaterialClient workwxChatImageMaterialClient;
    private final ContentClient contentClient;
    private final OssConfigProperties ossConfigProperties;

    public BaseResult<List<WorkWxChatRecordInfo>> messageSearch(String msgId, String msg, Integer companyType, LocalDate startTime, LocalDate endTime, Boolean isNext, Integer size) {
        return workwxClient.messageSearch(msgId, msg, companyType, startTime, endTime, isNext, size);
    }

    public PageResult<List<WorkWxChatRecordInfo>> getChatRecord(Integer relationId, Long startTime, Long endTime, Boolean isAsc, Integer current, Integer size) {
        BaseResult<WxAccountExternalUserInfo> wxAccountExternalUser = workwxClient.getWxAccountExternalUser(relationId);
        if (wxAccountExternalUser.isSuccess()) {
            WxAccountExternalUserInfo data = wxAccountExternalUser.getData();
            return getChatRecord(data.getAccountType(), data.getWxAccount(), data.getExternalUserId(), startTime, endTime, isAsc, current, size);
        }
        return null;
    }

    public Map<Integer, List<ChatRecordRiskResp>> getChatRecordRisk(Integer relationId, Long startTime, Long endTime, Boolean isAsc, Integer current, Integer size) {
        BaseResult<WxAccountExternalUserInfo> wxAccountExternalUser = workwxClient.getWxAccountExternalUser(relationId);
        if (wxAccountExternalUser.isSuccess()) {
            WxAccountExternalUserInfo data = wxAccountExternalUser.getData();
            return getChatRecordRisk(data.getAccountType(), data.getWxAccount(), data.getExternalUserId(), startTime, endTime, isAsc, current, size);
        }
        return null;
    }


    public Map<Integer, List<ChatRecordRiskResp>> getChatRecordRisk(Integer accountType, String wxAccount, String externalUserId, Long startTime, Long endTime, Boolean isAsc, Integer current, Integer size) {
        PageResult<List<ChatRecordRiskResp>> chatRecordRisk = workwxClient.getChatRecordRisk(accountType, wxAccount, externalUserId, startTime, endTime, isAsc, current, size);
        Map<Integer, List<ChatRecordRiskResp>> riskMap = new HashMap<>();
        if (chatRecordRisk.isSuccess()) {
            List<ChatRecordRiskResp> data = chatRecordRisk.getData();
            List<List<ChatRecordRiskResp>> partition = Lists.partition(data, size);
            for (int i = 0; i < partition.size(); i++) {
                riskMap.put(i, partition.get(i));
            }
            return riskMap;
        }
        return null;
    }

    public PageResult<List<WorkWxChatRecordInfo>> getChatRecord(Integer accountType, String wxAccount, String externalUserId, Long startTime, Long endTime, Boolean isAsc, Integer current, Integer size) {
        PageResult<List<WorkWxChatRecordInfo>> chatRecord = workwxClient.getChatRecord(accountType, wxAccount, externalUserId, startTime, endTime, isAsc, current, size);
        populateImageMaterialInfo(chatRecord.getData());
        urlConcatenation(chatRecord.getData());
        return chatRecord;
    }

    public PageResult<List<WorkWxChatRecordInfo>> getDialogue(Integer accountType, String wxAccount, String externalUserId, LocalDateTime startTime, LocalDateTime endTime, Boolean isAsc, Integer current, Integer size) {
        PageResult<List<WorkWxChatRecordInfo>> dialogue = workwxChatClient.getDialogue(accountType, wxAccount, externalUserId, startTime, endTime, isAsc, current, size);
        populateImageMaterialInfo(dialogue.getData());
        urlConcatenation(dialogue.getData());
        return dialogue;
    }

    public BaseResult<List<WorkWxChatRecordInfo>> getChatRecordNew(String wxAccount, String externalUserId, Long flagTime, Boolean isNext, Integer size) {
        BaseResult<List<WorkWxChatRecordInfo>> chatRecordNew = workwxClient.getChatRecordNew(wxAccount, externalUserId, flagTime, isNext, size);
        populateImageMaterialInfo(chatRecordNew.getData());
        urlConcatenation(chatRecordNew.getData());
        return chatRecordNew;
    }

    private void urlConcatenation(List<WorkWxChatRecordInfo> chatRecordInfos) {
        if (ObjectUtil.isEmpty(chatRecordInfos)) {
            return;
        }
        DirectoryOssConfigInfo directoryOssConfigInfo = ossConfigProperties.getDirectoryOssConfigInfo(3010);
        if (ObjectUtil.isEmpty(directoryOssConfigInfo)) {
            return;
        }
        for (WorkWxChatRecordInfo chatRecordInfo : chatRecordInfos) {
            if(StringUtils.isNotBlank(chatRecordInfo.getFileUrl())){
                chatRecordInfo.setFileUrl(directoryOssConfigInfo.getDomain() + "/" + chatRecordInfo.getFileUrl());
            }
        }
    }

    public void populateImageMaterialInfo(List<WorkWxChatRecordInfo> recordInfos) {
        if (ObjectUtil.isEmpty(recordInfos)) {
            return;
        }
        List<String> fileIdList = recordInfos.stream()
                .filter(e -> StringUtils.equals(e.getMsgType(), "image")).map(WorkWxChatRecordInfo::getFileId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(fileIdList)) {
            return;
        }
        List<ChatImageMaterialRelation> materialRelations = workwxChatImageMaterialClient.getChatImageMaterialRelation(fileIdList).orElseThrow();
        if (ObjectUtil.isEmpty(materialRelations)) {
            return;
        }
        List<Long> materialIds = materialRelations.stream().map(ChatImageMaterialRelation::getMaterialId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> originMaterialIds = materialRelations.stream().map(ChatImageMaterialRelation::getOriginMaterialId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<SsMaterial> ssMaterials = contentClient.getMaterialByIdList(materialIds).orElseThrow();
        List<SsOriginMaterial> ssOriginMaterials = contentClient.getOriginMaterialByIdList(originMaterialIds).orElseThrow();
        Map<Long, SsMaterial> materialMap = ssMaterials.stream().collect(Collectors.toMap(SsMaterial::getId, Function.identity()));
        Map<Long, SsOriginMaterial> originMaterialMap = ssOriginMaterials.stream().collect(Collectors.toMap(SsOriginMaterial::getId, Function.identity()));
        Map<String, ChatImageMaterialRelation> fileIdMaterialMap = materialRelations.stream()
                .collect(Collectors.toMap(ChatImageMaterialRelation::getFileId, Function.identity()));
        for (WorkWxChatRecordInfo recordInfo : recordInfos) {
            if (ObjectUtil.isEmpty(recordInfo.getFileId()) || !fileIdMaterialMap.containsKey(recordInfo.getFileId())) {
                continue;
            }
            ChatImageMaterialRelation relation = fileIdMaterialMap.get(recordInfo.getFileId());
            recordInfo.setWatermark(relation.getMaterialMark());
            recordInfo.setMaterialId(relation.getMaterialId());
            recordInfo.setMaterialAuditStatus(materialMap.getOrDefault(relation.getMaterialId(), new SsMaterial()).getAuditStatus());
            recordInfo.setOriginWatermark(relation.getOriginMaterialMark());
            recordInfo.setOriginMaterialId(relation.getOriginMaterialId());
            recordInfo.setOriginMaterialAuditStatus(originMaterialMap.getOrDefault(relation.getOriginMaterialId(), new SsOriginMaterial()).getAuditStatus());
        }
    }

    public BaseResult<Integer> getRelationId(Integer accountType, String wxAccount, String externalId) {
        BaseResult<Integer> relationId = workwxClient.getRelationId(accountType, wxAccount, externalId);
        if (relationId.isSuccess() && relationId.getData() != null) {
            return workwxClient.getRelationId(accountType, wxAccount, externalId);
        }
        return null;
    }
}

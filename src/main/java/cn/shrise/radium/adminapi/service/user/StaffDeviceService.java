package cn.shrise.radium.adminapi.service.user;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.adminapi.resp.user.IpGeoResp;
import cn.shrise.radium.adminapi.resp.user.StaffDeviceResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.StaffDeviceDto;
import cn.shrise.radium.userservice.entity.UcIpGeo;
import cn.shrise.radium.userservice.entity.UcStaffDevice;
import cn.shrise.radium.userservice.entity.UcStaffDeviceRecord;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StaffDeviceService {

    private final UserClient userClient;

    public BaseResult<IpGeoResp> getIpGeo(String ip) {
        BaseResult<UcIpGeo> result = userClient.getIpGeo(ip);
        if (result.isPresent()) {
            IpGeoResp resp = new IpGeoResp();
            BeanUtil.copyProperties(result.getData(), resp);
            return BaseResult.success(resp);
        }
        return BaseResult.success(null);
    }

    public BaseResult<List<StaffDeviceResp>> getStaffDeviceList(Integer staffId, String appNumber) {
        BaseResult<List<UcStaffDevice>> result = userClient.getStaffDeviceList(staffId, appNumber);
        if (result.isPresent()) {
            List<StaffDeviceResp> respList = new ArrayList<>();
            List<UcStaffDevice> staffDeviceList = result.getData();
            Set<String> ipSet = staffDeviceList.stream().map(UcStaffDevice::getIp).collect(Collectors.toSet());
            Map<String, UcIpGeo> ipGeoMap = userClient.batchIpGeoMap(BatchReq.create(ipSet)).getData();
            for (UcStaffDevice staffDevice : staffDeviceList) {
                StaffDeviceResp resp = new StaffDeviceResp();
                BeanUtil.copyProperties(staffDevice, resp);
                if (ipGeoMap.containsKey(staffDevice.getIp())) {
                    IpGeoResp ipGeoResp = new IpGeoResp();
                    BeanUtil.copyProperties(ipGeoMap.get(staffDevice.getIp()), ipGeoResp);
                    resp.setIpGeoResp(ipGeoResp);
                }
                respList.add(resp);
            }
            return BaseResult.success(respList);
        }
        return BaseResult.success(null);
    }

    public PageResult<List<StaffDeviceResp>> getStaffDeviceRecordPage(Integer staffId, String appNumber, Integer current, Integer size) {
        PageResult<List<StaffDeviceDto>> result = userClient.getStaffDeviceRecordPage(staffId, appNumber, current, size);
        if (result.isPresent()) {
            List<StaffDeviceResp> respList = new ArrayList<>();
            List<StaffDeviceDto> dtoList = result.getData();
            Set<String> ipSet = dtoList.stream().map(StaffDeviceDto::getRecord).map(UcStaffDeviceRecord::getIp).collect(Collectors.toSet());
            Set<Integer> userSet = dtoList.stream().map(StaffDeviceDto::getRecord).map(UcStaffDeviceRecord::getOperatorId).collect(Collectors.toSet());
            Map<String, UcIpGeo> ipGeoMap = userClient.batchIpGeoMap(BatchReq.create(ipSet)).getData();
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
            for (StaffDeviceDto dto : dtoList) {
                StaffDeviceResp resp = new StaffDeviceResp();
                UcStaffDeviceRecord record = dto.getRecord();
                BeanUtil.copyProperties(record, resp);
                if (ipGeoMap.containsKey(record.getIp())) {
                    IpGeoResp ipGeoResp = new IpGeoResp();
                    BeanUtil.copyProperties(ipGeoMap.get(record.getIp()), ipGeoResp);
                    resp.setIpGeoResp(ipGeoResp);
                }
                if (usersMap.containsKey(record.getOperatorId())) {
                    resp.setOperateName(usersMap.get(record.getOperatorId()).getRealName());
                }
                resp.setDeviceNumber(dto.getDeviceNumber());
                resp.setOperateTime(record.getGmtCreate());
                respList.add(resp);
            }
            return PageResult.success(respList, result.getPagination());
        }
        return PageResult.empty();
    }
}

package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.rpa.RpaResourceResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.rpaservice.RpaClient;
import cn.shrise.radium.rpaservice.entity.NpRpaResource;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Collections.emptyMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class RpaService {

    private final UserClient userClient;
    private final RpaClient rpaClient;

    public PageResult<List<RpaResourceResp>> getRpaResourceList(String status, Boolean enabled, String searchContent,
                                                                List<Integer> belongIdList, Integer loginStatus, Integer current, Integer size) {
        PageResult<List<NpRpaResource>> pageResult = rpaClient.getRpaResourceList(status, enabled, searchContent, belongIdList, loginStatus, current, size);
        if (pageResult.isSuccess()) {
            List<NpRpaResource> dataList = pageResult.getData();
            Set<Integer> userSet = dataList.stream().map(NpRpaResource::getBelongId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.of(userSet)).orElse(emptyMap());
            List<RpaResourceResp> respList = dataList.stream().map(resource -> {
                RpaResourceResp resp = BeanUtil.copyProperties(resource, RpaResourceResp.class);
                if (resource.getBelongId() != null && userMap.containsKey(resource.getBelongId())) {
                    resp.setBelongName(userMap.get(resource.getBelongId()).getRealName());
                }
                return resp;
            }).collect(Collectors.toList());
            return PageResult.success(respList, pageResult.getPagination());
        }
        return PageResult.fail(pageResult.getCode(), pageResult.getMsg(), pageResult.getPagination());
    }

    public BaseResult<List<RpaResourceResp>> batchRpaResourceList(BatchReq<String> req) {
        List<RpaResourceResp> respList = null;
        List<NpRpaResource> resourceList = rpaClient.batchRpaResourceList(req).orElseThrow();
        if (ObjectUtil.isNotEmpty(resourceList)) {
            Set<Integer> userSet = resourceList.stream().map(NpRpaResource::getBelongId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.of(userSet)).orElse(emptyMap());
            respList = resourceList.stream().map(resource -> {
                RpaResourceResp resp = BeanUtil.copyProperties(resource, RpaResourceResp.class);
                if (resource.getBelongId() != null && userMap.containsKey(resource.getBelongId())) {
                    resp.setBelongName(userMap.get(resource.getBelongId()).getRealName());
                }
                return resp;
            }).collect(Collectors.toList());
        }
        return BaseResult.success(respList);
    }
}

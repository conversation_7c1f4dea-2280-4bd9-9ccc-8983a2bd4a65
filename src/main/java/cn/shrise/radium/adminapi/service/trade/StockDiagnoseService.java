package cn.shrise.radium.adminapi.service.trade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdStockDiagnoseInfo;
import cn.shrise.radium.tradeservice.resp.StockDiagnoseResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zhangjianwu
 * @Date: 2024/11/28 19:45
 * @Desc:
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class StockDiagnoseService {
    private final TradeClient tradeClient;
    private final UserClient userClient;

    public PageResult<List<StockDiagnoseResp>> listStockDiagnose(LocalDate startTime, LocalDate endTime, String searchContent, Integer userId, Integer current, Integer size) {
        PageResult<List<TdStockDiagnoseInfo>> diagnosePageResult = tradeClient.listStockDiagnose(startTime, endTime, searchContent, userId, current, size);
        List<StockDiagnoseResp> stockDiagnoseResps = BeanUtil.copyToList(diagnosePageResult.getData(), StockDiagnoseResp.class);
        if (ObjectUtil.isNotEmpty(stockDiagnoseResps)) {
            List<Integer> userIdList = stockDiagnoseResps.stream().map(StockDiagnoseResp::getUserId).collect(Collectors.toList());
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap((BatchReq.of(userIdList))).getData();
            stockDiagnoseResps.forEach(diagnoseResp -> {
                diagnoseResp.setUserName(usersMap.getOrDefault(diagnoseResp.getUserId(), new UcUsers()).getRealName());
            });
        }
        return PageResult.success(stockDiagnoseResps, diagnosePageResult.getPagination());
    }

}

package cn.shrise.radium.adminapi.service.im;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.im.SceneStoreResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImRoomScene;
import cn.shrise.radium.imservice.entity.ImRoomSceneStore;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsSku;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LiveSceneStoreService {

    private final ImClient imClient;
    private final OrderClient orderClient;

    public BaseResult<SceneStoreResp> getSceneStore(Long sceneId) {

        ImRoomScene imRoomScene = imClient.getRoomSceneInfo(sceneId).orElse(null);
        if (ObjectUtil.isEmpty(imRoomScene)){
            throw new BusinessException("该场次不存在");
        }
        BaseResult<ImRoomSceneStore> result = imClient.getSceneStore(sceneId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            throw new BusinessException("该场次没有商品信息");
        }
        ImRoomSceneStore sceneStore = result.getData();
        BaseResult<RsSku> skuBaseResult = orderClient.getSku(sceneStore.getSkuId());
        if (skuBaseResult.isFail()) {
            throw new BusinessException(skuBaseResult);
        }
        if (!skuBaseResult.isPresent()) {
            throw new BusinessException("该场次没有对应sku信息");
        }
        RsSku sku = skuBaseResult.getData();
        SceneStoreResp resp = SceneStoreResp.builder()
                .id(sceneStore.getId())
                .sceneid(sceneStore.getSceneId())
                .roomId(sceneStore.getRoomId())
                .skuId(sceneStore.getSkuId())
                .skuName(sku.getName())
                .skuShowName(sku.getShowName())
                .skuNumber(sku.getNumber())
                .price(sku.getPrice())
                .isStore(imRoomScene.getIsStore())
                .build();
        return BaseResult.success(resp);
    }
}

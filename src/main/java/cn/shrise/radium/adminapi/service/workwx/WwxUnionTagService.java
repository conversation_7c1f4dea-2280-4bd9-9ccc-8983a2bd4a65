package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.workwxservice.resp.WwxUnionTagBatchDetailResp;
import cn.shrise.radium.workwxservice.resp.WwxUnionTagBatchResp;
import cn.shrise.radium.workwxservice.resp.WwxUnionTagFriendDetailResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxservice.resp.WwxUnionTagRelationRecordResp;
import cn.shrise.radium.adminapi.resp.workWx.WwxUnionTagResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.workwxrobot.TagInfo;
import cn.shrise.radium.workwxservice.entity.NpWwxUnionContactTagRelation;
import cn.shrise.radium.workwxservice.entity.NpWwxUnionTag;
import cn.shrise.radium.workwxservice.entity.NpWwxUnionTagRelation;
import cn.shrise.radium.workwxservice.property.WxCpProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WwxUnionTagService {

    private final WorkwxClient workwxClient;
    private final UserClient userClient;

    public BaseResult<List<WwxUnionTagResp>> getWwxUnionTagList() {
        BaseResult<List<NpWwxUnionTag>> result = workwxClient.getWwxUnionTagList();
        if (!result.isPresent()) {
            return BaseResult.success(Collections.emptyList());
        }
        List<NpWwxUnionTag> npWwxUnionTagList = result.getData();
        List<WwxUnionTagResp> resp = npWwxUnionTagList.stream().map(i -> {
            WwxUnionTagResp wwxUnionTagResp = new WwxUnionTagResp();
            BeanUtil.copyProperties(i, wwxUnionTagResp);
            return wwxUnionTagResp;
        }).collect(Collectors.toList());
        return BaseResult.success(resp);
    }

    public PageResult<List<WwxUnionTagResp>> getWwxUnionTagList(Integer current, Integer size) {
        PageResult<List<NpWwxUnionTag>> result = workwxClient.getWwxUnionTagList(current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }

        List<NpWwxUnionTag> npWwxUnionTagList = result.getData();
        List<Long> unionTagIdList = npWwxUnionTagList.stream().map(NpWwxUnionTag::getId).collect(Collectors.toList());

        List<NpWwxUnionContactTagRelation> unionContactTagRelations = workwxClient.getBatchRelation(BatchReq.of(unionTagIdList)).orElse(new ArrayList<>());
        Map<Long, List<NpWwxUnionContactTagRelation>> relationMap = unionContactTagRelations.stream()
                .collect(Collectors.groupingBy(NpWwxUnionContactTagRelation::getUnionTagId));

        List<Integer> contactTagIds = unionContactTagRelations.stream().map(NpWwxUnionContactTagRelation::getContactTagId).collect(Collectors.toList());
        List<TagInfo> tagInfos = workwxClient.getContactTagList(BatchReq.of(contactTagIds)).orElse(new ArrayList<>());
        Map<Integer, TagInfo> tagInfoMap = tagInfos.stream().collect(Collectors.toMap(TagInfo::getId, Function.identity()));

        Map<Integer, WxCpProperties> wxCpPropertiesMap = workwxClient.getAccountConfig().orElse(new HashMap<>());

        Map<Long, List<WwxUnionTagResp.WwxTagResp>> unionTagRespMap = relationMap.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream().map(relation -> {
                    Integer contactTagId = relation.getContactTagId();
                    TagInfo tagInfo = tagInfoMap.get(contactTagId);
                    WxCpProperties cpProperties = wxCpPropertiesMap.getOrDefault(tagInfo.getAccountType(), new WxCpProperties());

                    return new AbstractMap.SimpleEntry<>(entry.getKey(),
                            WwxUnionTagResp.WwxTagResp.builder()
                                    .accountType(tagInfo.getAccountType())
                                    .corpName(cpProperties.getCorpName())
                                    .groupName(tagInfo.getGroupName())
                                    .tagName(tagInfo.getTagName())
                                    .tagId(tagInfo.getId())
                                    .build());
                }))
                .collect(Collectors.groupingBy(entry -> entry.getKey(),
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        List<WwxUnionTagResp> resp = npWwxUnionTagList.stream().map(i -> {
            WwxUnionTagResp wwxUnionTagResp = new WwxUnionTagResp();
            BeanUtil.copyProperties(i, wwxUnionTagResp);
            wwxUnionTagResp.setWwxTagList(unionTagRespMap.getOrDefault(wwxUnionTagResp.getId(), new ArrayList<>()));
            return wwxUnionTagResp;
        }).collect(Collectors.toList());

        return PageResult.success(resp, result.getPagination());
    }

    public BaseResult<Void> addWwxUnionTag(String name, Integer companyType) {
        return workwxClient.addWwxUnionTag(name, companyType);
    }

    public BaseResult<Void> bindWwxUnionTag(Long unionTagId, Integer contactTagId, Integer creatorId) {
        return workwxClient.bindWwxUnionTag(unionTagId, contactTagId, creatorId);
    }

    public BaseResult<Void> unbindWwxUnionTag(Long unionTagId, Integer contactTagId, Integer creatorId) {
        return workwxClient.unbindWwxUnionTag(unionTagId, contactTagId, creatorId);
    }

    public BaseResult<List<Long>> getTagIdListByUnionId(String unionId, Integer companyType) {
        BaseResult<List<NpWwxUnionTagRelation>> result = workwxClient.getTagUnionRelationListByUnionId(unionId, companyType);
        if (!result.isPresent()) {
            return BaseResult.success(Collections.emptyList());
        }
        List<NpWwxUnionTagRelation> npWwxUnionTagRelations = result.getData();
        return BaseResult.success(npWwxUnionTagRelations.stream().map(NpWwxUnionTagRelation::getUnionTagId).collect(Collectors.toList()));
    }

    public BaseResult<Void> markTag(String unionId, List<Long> tagIdList, Integer companyType, Integer creatorId) {
        return workwxClient.markTag(unionId, tagIdList, companyType, creatorId);
    }

    public BaseResult<List<WwxUnionTagRelationRecordResp>> getMarkTagRecord(String unionId, Integer companyType) {
        BaseResult<List<WwxUnionTagRelationRecordResp>> result = workwxClient.getMarkTagRecord(unionId, companyType);
        if (!result.isPresent()) {
            return BaseResult.success(Collections.emptyList());
        }
        List<WwxUnionTagRelationRecordResp> records = result.getData();
        List<Integer> userIds = records.stream().map(WwxUnionTagRelationRecordResp::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIds)).orElse(new HashMap<>());
        result.getData().forEach(i -> {
            i.setOperatorAvatarUrl(usersMap.getOrDefault(i.getOperatorId(), new UcUsers()).getAvatarUrl());
            i.setOperatorName(usersMap.getOrDefault(i.getOperatorId(), new UcUsers()).getRealName());
        });
        return result;
    }

    public BaseResult<List<WwxUnionTagFriendDetailResp>> getFriendIdRecord(Integer friendId) {
        return workwxClient.getFriendIdRecord(friendId);
    }

    public PageResult<List<WwxUnionTagBatchResp>> getBatchRecord(String searchContent, Integer current, Integer size) {
        PageResult<List<WwxUnionTagBatchResp>> result = workwxClient.getBatchRecord(searchContent, current, size);
        if (!result.isPresent()) {
            return PageResult.empty(current, size);
        }
        List<WwxUnionTagBatchResp> batch = result.getData();
        List<Integer> userIds = batch.stream().map(WwxUnionTagBatchResp::getCreatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIds)).orElse(new HashMap<>());
        result.getData().forEach(i -> i.setCreatorName(usersMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName()));
        return result;
    }

    public PageResult<List<WwxUnionTagBatchDetailResp>> getBatchDetail(String searchContent, String batchNumber, Integer current, Integer size) {
        return workwxClient.getBatchDetail(searchContent, batchNumber, current, size);
    }

    public PageResult<List<WwxUnionTagFriendDetailResp>> getBatchFriendDetail(String unionId, String batchNumber, Integer current, Integer size) {
        PageResult<List<WwxUnionTagFriendDetailResp>> result = workwxClient.getBatchFriendDetail(unionId, batchNumber, current, size);
        if (!result.isPresent()) {
            return PageResult.empty(current, size);
        }
        Map<Integer, WxCpProperties> wxCpPropertiesMap = workwxClient.getAccountConfig().orElse(new HashMap<>());
        result.getData().forEach(i -> i.setCorpName(wxCpPropertiesMap.getOrDefault(i.getAccountType(), new WxCpProperties()).getCorpName()));
        return result;
    }

    public BaseResult<Void> setRemind(Long id, Boolean isRemind) {
        return workwxClient.setRemind(id, isRemind);
    }
}

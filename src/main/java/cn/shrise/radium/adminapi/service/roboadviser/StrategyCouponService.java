package cn.shrise.radium.adminapi.service.roboadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.entity.*;
import cn.shrise.radium.roboadviserservice.resp.*;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.roboadviserservice.resp.StrategyCouponApplyAuditResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyCouponResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyCouponTemplateResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyCouponTemplateVisibleResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class StrategyCouponService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final UserClient userClient;

    public PageResult<List<StrategyCouponResp>> getStrategyCouponTemplateList(Integer companyType, Integer current, Integer size) {
        PageResult<List<RaStrategyCouponTemplate>> strategyCouponPae = roboAdviserServiceClient.getStrategyCouponTemplateList(companyType, current, size);
        if (strategyCouponPae.isPresent()) {
            List<RaStrategyCouponTemplate> data = strategyCouponPae.getData();
            List<String> strategyIdList = data.stream().filter(Objects::nonNull).map(RaStrategyCouponTemplate::getStrategyCode).collect(Collectors.toList());
            Map<String, String> idNameMap = roboAdviserServiceClient.getStrategyByCodeList(strategyIdList).orElse(new ArrayList<>())
                    .stream().collect(Collectors.toMap(RaStrategy::getCode, RaStrategy::getName, (oldVal, newVal) -> newVal));
            List<StrategyCouponResp> resList = new ArrayList<>();
            for (RaStrategyCouponTemplate template : data) {
                if (template != null) {
                    StrategyCouponResp couponResp = new StrategyCouponResp();
                    BeanUtils.copyProperties(template, couponResp);
                    couponResp.setName(idNameMap.get(template.getStrategyCode()));
                    couponResp.setCouponTemplateName(template.getName());
                    resList.add(couponResp);
                }
            }
            return PageResult.success(resList, strategyCouponPae.getPagination());
        } else {
            return PageResult.empty();
        }
    }

    public StrategyCouponTemplateVisibleResp getStrategyCouponTemplateVisibleList(Long couponTemplateId) {
        List<Integer> salesIdList = new ArrayList<>();
        BaseResult<List<RaStrategyCouponTemplateSalesRelation>> couponSalesRelation = roboAdviserServiceClient.getStrategyCouponTemplateSalesRelation(couponTemplateId);
        if (couponSalesRelation.isPresent()) {
            salesIdList = couponSalesRelation.getData().stream()
                    .map(RaStrategyCouponTemplateSalesRelation::getSalesId).collect(Collectors.toList());
        }
        List<Integer> deptIdList = new ArrayList<>();
        BaseResult<List<RaStrategyCouponTemplateDeptRelation>> couponDeptRelation = roboAdviserServiceClient.getStrategyCouponTemplateDeptRelation(couponTemplateId);
        if (couponDeptRelation.isPresent()) {
            deptIdList = couponDeptRelation.getData().stream()
                    .map(RaStrategyCouponTemplateDeptRelation::getDeptId).collect(Collectors.toList());
        }
        return new StrategyCouponTemplateVisibleResp(couponTemplateId, salesIdList, deptIdList);
    }

    public List<StrategyCouponTemplateResp> getStrategyCouponTemplateSalesVisible(Integer salesId) {
        List<StrategyCouponTemplateResp> res = new ArrayList<>();
        BaseResult<List<RaStrategyCouponTemplate>> couponTemplateSalesVisible = roboAdviserServiceClient.getStrategyCouponTemplateSalesVisible(salesId, true);
        if (couponTemplateSalesVisible.isPresent()) {
            for (RaStrategyCouponTemplate template : couponTemplateSalesVisible.getData()) {
                if (template != null) {
                    StrategyCouponTemplateResp templateResp = StrategyCouponTemplateResp.builder()
                            .couponTemplateId(template.getId())
                            .couponTemplateName(template.getName())
                            .createTime(template.getGmtCreate())
                            .build();
                    res.add(templateResp);
                }
            }
        }
        return res.stream()
                .sorted(Comparator.comparing(StrategyCouponTemplateResp::getCreateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

    public PageResult<List<StrategyCouponApplyResp>> getStrategyApplyList(Integer userId, Integer companyType, Integer auditStatus,
                                                                          Integer signStatus, String content, Integer current, Integer size) {
        PageResult<List<StrategyCouponApplyResp>> strategyApplyPageResult =
                roboAdviserServiceClient.getStrategyApplyList(userId, companyType, auditStatus, signStatus, content, current, size);
        if (strategyApplyPageResult.isFail()) {
            throw new BusinessException(strategyApplyPageResult);
        }
        if (ObjectUtil.isEmpty(strategyApplyPageResult.getData())) {
            return PageResult.empty();
        }
        List<StrategyCouponApplyResp> applyRespList = strategyApplyPageResult.getData();
        Set<Integer> userSet = applyRespList.stream()
                .flatMap(resp -> Stream.of(resp.getUserId(), resp.getCreatorId(), resp.getAuditorId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Integer, UcUsers> userMapResult = userClient.batchGetUserMap(BatchReq.create(userSet)).orElseThrow();
        for (StrategyCouponApplyResp resp : applyRespList) {
            if (userMapResult.containsKey(resp.getUserId())) {
                resp.setNickname(userMapResult.get(resp.getUserId()).getNickName());
            }
            if (userMapResult.containsKey(resp.getCreatorId())) {
                resp.setCreatorName(userMapResult.get(resp.getCreatorId()).getRealName());
            }
            if (userMapResult.containsKey(resp.getAuditorId())) {
                resp.setAuditorName(userMapResult.get(resp.getAuditorId()).getRealName());
            }
            resp.setUserCode(DesensitizeUtil.idToMask(resp.getUserId()));
        }
        return PageResult.success(applyRespList, strategyApplyPageResult.getPagination());
    }

    public StrategyCouponApplyDetailResp getStrategyApplyDetail(Long applyId) {
        StrategyCouponApplyDetailResp applyDetailResp = roboAdviserServiceClient.getStrategyApplyDetail(applyId).orElseThrow();
        Set<Integer> userSet = Stream.of(
                        applyDetailResp.getUserId(),
                        applyDetailResp.getCreatorId(),
                        applyDetailResp.getAuditorId())
                .collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).orElseThrow();
        if (userMap.containsKey(applyDetailResp.getUserId())) {
            applyDetailResp.setNickname(userMap.get(applyDetailResp.getUserId()).getNickName());
        }
        if (userMap.containsKey(applyDetailResp.getCreatorId())) {
            applyDetailResp.setCreatorName(userMap.get(applyDetailResp.getCreatorId()).getRealName());
        }
        if (userMap.containsKey(applyDetailResp.getAuditorId())) {
            applyDetailResp.setAuditorName(userMap.get(applyDetailResp.getAuditorId()).getRealName());
        }
        applyDetailResp.setUserCode(DesensitizeUtil.idToMask(applyDetailResp.getUserId()));
        return applyDetailResp;
    }

    public BaseResult<StrategyCouponApplyAuditResp> audit(Long applyId, Integer auditStatus, Integer userId) {
        BaseResult<StrategyCouponApplyAuditResp> strategyCouponApplyAuditResp = roboAdviserServiceClient.audit(applyId, auditStatus, userId);
        if (strategyCouponApplyAuditResp.isPresent()) {
            BaseResult<UcUsers> user = userClient.getUser(userId);
            if (user.isPresent()) {
                strategyCouponApplyAuditResp.getData().setAuditorName(user.getData().getRealName());
            }
        }
        return strategyCouponApplyAuditResp;
    }
}

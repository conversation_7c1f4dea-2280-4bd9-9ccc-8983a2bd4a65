package cn.shrise.radium.adminapi.service.wx;

import cn.shrise.radium.adminapi.entity.Option;
import cn.shrise.radium.adminapi.resp.wx.WxMpPropertiesResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.properties.WxMpProperties;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WxMpConfigService {

    private final WxClient wxClient;

    public List<WxMpPropertiesResp> getWxMpConfig(Integer companyType) {
        List<WxMpProperties> result = wxClient.getAuthorizerConfig(companyType).orElseThrow();

        if (ObjectUtils.isEmpty(result)) {
            return Collections.emptyList();
        }

        return result.stream().map(e -> {
            WxMpPropertiesResp resp = new WxMpPropertiesResp();
            BeanUtils.copyProperties(e, resp);
            return resp;
        }).collect(Collectors.toList());
    }
}

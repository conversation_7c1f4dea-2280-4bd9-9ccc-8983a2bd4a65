package cn.shrise.radium.adminapi.service.im;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.TextScanResultResp;
import cn.shrise.radium.adminapi.resp.im.ChatDialogHistoryResp;
import cn.shrise.radium.adminapi.resp.im.ChatDialogResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsTextScanResult;
import cn.shrise.radium.contentservice.resp.ModerationLabelItem;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatManager;
import cn.shrise.radium.imservice.req.SendImChatDialogReq;
import cn.shrise.radium.imservice.resp.ImChatDialogHistoryResp;
import cn.shrise.radium.imservice.resp.ImChatDialogResp;
import cn.shrise.radium.workwxservice.WorkwxClient;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatDialogService {

    private final ImClient imClient;
    private final WorkwxClient workwxClient;

    private final ContentClient contentClient;


    public BaseResult<String> sendImChatDialogMsg(SendImChatDialogReq req) {

        BaseResult<List<ImChatManager>> baseResult = imClient.getChatRoomManagerInfo(req.getChatId(), true);
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        if (!baseResult.isPresent()) {
            throw new BusinessException("请先关联老师");
        }
        Optional<ImChatManager> first = baseResult.getData().stream().filter(e -> ObjectUtil.equal(req.getUserId(), e.getManagerId())).findFirst();
        if (first.isPresent() && ObjectUtil.isNotEmpty(first.get().getAnalystId())) {
            return imClient.sendImChatDialogMsg(req);
        }
        throw new BusinessException("请先关联老师");
    }

    public PageResult<List<ChatDialogResp>> getBackendChatDialogList(Integer companyType, Long chatId, Boolean isAuditExecuting, Integer auditStatus, Boolean isCustomer, Boolean isEnabled, Long startTime, Long endTime, Integer dateType, Boolean isAsc, Integer current, Integer size) {
        PageResult<List<ImChatDialogResp>> pageResult = imClient.getChatDialogList(companyType, chatId, isAuditExecuting, auditStatus, isCustomer, isEnabled, startTime, endTime, dateType, isAsc, current, size);
        if (pageResult.isPresent()) {
            List<ImChatDialogResp> respList = pageResult.getData();
            Set<Long> resultSet = respList.stream().map(ImChatDialogResp::getResultId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();
            List<ChatDialogResp> chatDialogRespList = new ArrayList<>();
            for (ImChatDialogResp resp : respList) {
                ChatDialogResp chatDialogResp = new ChatDialogResp();
                chatDialogResp.setChatDialogResp(resp);
                if (ObjectUtil.isNotEmpty(resp.getResultId()) && resultMap.containsKey(resp.getResultId())) {
                    SsTextScanResult scanResult = resultMap.get(resp.getResultId());
                    List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                    List<String> labelNameList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(labels)) {
                        Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                        for (String label : labels) {
                            labelNameList.add(itemMap.get(label).getName());
                        }
                    }
                    TextScanResultResp resultResp = new TextScanResultResp();
                    resultResp.setLabels(labels);
                    resultResp.setLabelMsg(labelNameList);
                    chatDialogResp.setResultResp(resultResp);
                    chatDialogResp.setPassed(scanResult.getPassed());
                }
                chatDialogRespList.add(chatDialogResp);
            }
            return PageResult.success(chatDialogRespList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<ChatDialogResp>> getBackendRecentChatDialogList(Integer companyType, Long chatId, Boolean isAuditExecuting, Integer auditStatus, Boolean isCustomer, Boolean isEnabled, LocalDateTime startTime, LocalDateTime endTime, Integer dateType, Boolean isAsc, Integer current, Integer size) {
        PageResult<List<ImChatDialogResp>> pageResult = imClient.getRecentChatDialogList(companyType, chatId, isAuditExecuting, auditStatus, isCustomer, isEnabled, startTime, endTime, dateType, isAsc, current, size);
        if (pageResult.isPresent()) {
            List<ImChatDialogResp> respList = pageResult.getData();
            Set<Long> resultSet = respList.stream().map(ImChatDialogResp::getResultId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();
            List<ChatDialogResp> chatDialogRespList = new ArrayList<>();
            for (ImChatDialogResp resp : respList) {
                ChatDialogResp chatDialogResp = new ChatDialogResp();
                chatDialogResp.setChatDialogResp(resp);
                if (ObjectUtil.isNotEmpty(resp.getResultId()) && resultMap.containsKey(resp.getResultId())) {
                    SsTextScanResult scanResult = resultMap.get(resp.getResultId());
                    List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                    List<String> labelNameList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(labels)) {
                        Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                        for (String label : labels) {
                            labelNameList.add(itemMap.get(label).getName());
                        }
                    }
                    TextScanResultResp resultResp = new TextScanResultResp();
                    resultResp.setLabels(labels);
                    resultResp.setLabelMsg(labelNameList);
                    chatDialogResp.setPassed(scanResult.getPassed());
                    chatDialogResp.setResultResp(resultResp);
                }
                chatDialogRespList.add(chatDialogResp);
            }
            return PageResult.success(chatDialogRespList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public BaseResult<List<ChatDialogHistoryResp>> getBackendChatDialogHistory(Long chatId, Long flagTime, Integer size) {
        BaseResult<List<ImChatDialogHistoryResp>> baseResult = imClient.getChatDialogHistory(chatId, flagTime, size);
        if (baseResult.isPresent()) {
            List<ImChatDialogHistoryResp> respList = baseResult.getData();
            Set<Long> resultSet = respList.stream().map(ImChatDialogHistoryResp::getResultId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();
            List<ChatDialogHistoryResp> chatDialogRespList = new ArrayList<>();
            for (ImChatDialogHistoryResp resp : respList) {
                ChatDialogHistoryResp chatDialogHistoryResp = new ChatDialogHistoryResp();
                chatDialogHistoryResp.setChatDialogHistoryResp(resp);
                if (ObjectUtil.isNotEmpty(resp.getResultId()) && resultMap.containsKey(resp.getResultId())) {
                    SsTextScanResult scanResult = resultMap.get(resp.getResultId());
                    List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                    List<String> labelNameList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(labels)) {
                        Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                        for (String label : labels) {
                            labelNameList.add(itemMap.get(label).getName());
                        }
                    }
                    TextScanResultResp resultResp = new TextScanResultResp();
                    resultResp.setLabels(labels);
                    resultResp.setLabelMsg(labelNameList);
                    chatDialogHistoryResp.setPassed(scanResult.getPassed());
                    chatDialogHistoryResp.setResultResp(resultResp);
                }
                chatDialogRespList.add(chatDialogHistoryResp);
            }
            return BaseResult.success(chatDialogRespList);
        }
        return BaseResult.success(null);
    }

    public PageResult<List<ChatDialogResp>> getSenderChatDialogList(Integer senderId, Long chatId, Long startTime, Long endTime, Integer current, Integer size) {
        PageResult<List<ImChatDialogResp>> pageResult = imClient.getSenderChatDialogList(senderId, chatId, startTime, endTime, current, size);
        if (pageResult.isPresent()) {
            List<ImChatDialogResp> respList = pageResult.getData();
            Set<Long> resultSet = respList.stream().map(ImChatDialogResp::getResultId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();
            List<ChatDialogResp> chatDialogRespList = new ArrayList<>();
            for (ImChatDialogResp resp : respList) {
                ChatDialogResp chatDialogResp = new ChatDialogResp();
                chatDialogResp.setChatDialogResp(resp);
                if (ObjectUtil.isNotEmpty(resp.getResultId()) && resultMap.containsKey(resp.getResultId())) {
                    SsTextScanResult scanResult = resultMap.get(resp.getResultId());
                    List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                    List<String> labelNameList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(labels)) {
                        Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                        for (String label : labels) {
                            labelNameList.add(itemMap.get(label).getName());
                        }
                    }
                    TextScanResultResp resultResp = new TextScanResultResp();
                    resultResp.setLabels(labels);
                    resultResp.setLabelMsg(labelNameList);
                    chatDialogResp.setPassed(scanResult.getPassed());
                    chatDialogResp.setResultResp(resultResp);
                }
                chatDialogRespList.add(chatDialogResp);
            }
            return PageResult.success(chatDialogRespList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    /**
     * 互动消息模糊搜索
     * @param companyType
     * @param chatId
     * @param content
     * @param msgId
     * @param isNext
     * @param size
     * @return
     */
    public BaseResult<List<ChatDialogResp>> searchByContent(Integer companyType, Long chatId, String content, Long msgId, Boolean isNext, Integer size) {
        BaseResult<List<ImChatDialogResp>> baseResult = imClient.searchByContent(companyType, chatId, content, msgId, isNext, size);
        if (baseResult.isPresent()) {
            List<ImChatDialogResp> respList = baseResult.getData();
            Set<Long> resultSet = respList.stream().map(ImChatDialogResp::getResultId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();
            List<ChatDialogResp> chatDialogRespList = new ArrayList<>();
            for (ImChatDialogResp resp : respList) {
                ChatDialogResp chatDialogResp = new ChatDialogResp();
                chatDialogResp.setChatDialogResp(resp);
                if (ObjectUtil.isNotEmpty(resp.getResultId()) && resultMap.containsKey(resp.getResultId())) {
                    SsTextScanResult scanResult = resultMap.get(resp.getResultId());
                    List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                    List<String> labelNameList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(labels)) {
                        Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                        for (String label : labels) {
                            labelNameList.add(itemMap.get(label).getName());
                        }
                    }
                    TextScanResultResp resultResp = new TextScanResultResp();
                    resultResp.setLabels(labels);
                    resultResp.setLabelMsg(labelNameList);
                    chatDialogResp.setResultResp(resultResp);
                    chatDialogResp.setPassed(scanResult.getPassed());
                }
                chatDialogRespList.add(chatDialogResp);
            }
            return BaseResult.success(chatDialogRespList);
        }
        return BaseResult.success(null);
    }
}

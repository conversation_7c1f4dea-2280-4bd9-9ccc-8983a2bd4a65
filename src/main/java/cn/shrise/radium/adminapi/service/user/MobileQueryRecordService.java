package cn.shrise.radium.adminapi.service.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.user.UserMobileQueryRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.secureservice.SecureServiceClient;
import cn.shrise.radium.secureservice.entity.ScMobileQueryRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MobileQueryRecordService {

    private final SecureServiceClient secureServiceClient;
    private final UserClient userClient;

    public PageResult<List<UserMobileQueryRecordResp>> getMobileQueryRecordList(
            Integer companyType, Integer queryType, Instant startTime, Instant endTime, Integer current, Integer size) {
        PageResult<List<ScMobileQueryRecord>> recordListResult = secureServiceClient.getMobileQueryRecordList(
                companyType, queryType, startTime, endTime, current, size);
        if (recordListResult.isFail()) {
            throw new BusinessException(recordListResult);
        }
        Set<Integer> userIds = new HashSet<>();
        recordListResult.getData().forEach(o -> {
            if (ObjectUtil.isNotEmpty(o.getUserId())) {
                userIds.add(o.getUserId());
            }
            if (ObjectUtil.isNotEmpty(o.getQueryId())) {
                userIds.add(o.getQueryId());
            }
        });
        BaseResult<Map<Integer, UcUsers>> userMapResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        if (userMapResult.isFail()) {
            throw new BusinessException(userMapResult);
        }
        Map<Integer, UcUsers> usersMap = userMapResult.getData();
        List<UserMobileQueryRecordResp> records = recordListResult.getData().stream().map(o -> {
            UserMobileQueryRecordResp resp = BeanUtil.copyProperties(o, UserMobileQueryRecordResp.class);
            if (ObjectUtil.isNotEmpty(o.getUserId())) {
                resp.setUserName(usersMap.get(o.getUserId()).getNickName());
                resp.setUserCode(DesensitizeUtil.idToMask(o.getUserId()));
            }
            if (ObjectUtil.isNotEmpty(o.getQueryId())) {
                resp.setQueryName(usersMap.get(o.getQueryId()).getNickName());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, recordListResult.getPagination());
    }
}

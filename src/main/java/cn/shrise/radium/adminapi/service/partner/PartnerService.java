package cn.shrise.radium.adminapi.service.partner;

import cn.shrise.radium.partnerservice.req.*;
import cn.shrise.radium.partnerservice.resp.PartnerBiVisibleResp;
import cn.shrise.radium.partnerservice.resp.PartnerBiInfoResp;
import cn.shrise.radium.partnerservice.resp.PartnerUserResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.partnerservice.PartnerClient;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PartnerService {

    private final PartnerClient partnerClient;

    public BaseResult<Void> createUser(PartnerAddUserReq req) {
        return partnerClient.createUser(req);
    }

    public BaseResult<Void> updateUser(PartnerUpdateUserReq req) {
        return partnerClient.updateUser(req);
    }

    public PageResult<List<PartnerUserResp>> userList(String name, Integer current, Integer size) {
        return partnerClient.userList(name, current, size);
    }

    public PageResult<List<PartnerBiInfoResp>> getPartnerBiInfoList(Boolean enabled, Integer current, Integer size) {
        return partnerClient.getPartnerBiInfoList(enabled, current, size);
    }

    public BaseResult<Void> createPartnerBiInfo(CreatePartnerBiReq req) {
        return partnerClient.createPartnerBiInfo(req);
    }

    public BaseResult<Void> updatePartnerBiInfo(UpdatePartnerBiReq req) {
        return partnerClient.updatePartnerBiInfo(req);
    }

    public BaseResult<Void> enablePartnerBiInfo(Long id, Boolean enabled) {
        return partnerClient.enablePartnerBiInfo(id, enabled);
    }

    public BaseResult<Void> setVisible(SetVisibleReq req) {
        return partnerClient.setVisible(req);
    }

    public BaseResult<PartnerBiVisibleResp> getBiVisible(Long biId) {
        return partnerClient.getBiVisible(biId);
    }
}

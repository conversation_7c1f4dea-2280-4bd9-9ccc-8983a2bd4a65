package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.RoomLotteryDetailResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.dto.SsRoomLotteryDetailDto;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImLiveRoom;
import cn.shrise.radium.imservice.entity.ImRoomScene;
import cn.shrise.radium.secureservice.SecureServiceClient;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUserAddress;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RoomLotteryDetailService {
    private final ContentClient contentClient;
    private final UserClient userClient;
    private final ImClient imClient;

    public PageResult<List<RoomLotteryDetailResp>> getRoomLotteryDetailList(
            Integer current, Integer size, Integer companyType, Long roomId, Long sceneId, Long lotteryId, Long prizeId,
            Integer relationServerId, Integer salesId, Boolean isWin, Boolean isHandled, String searchUserInfo) {
        List<Integer> customerList = new ArrayList<>();
        if (StringUtils.isNotBlank(searchUserInfo)) {
            customerList.add(DesensitizeUtil.maskToId(searchUserInfo));
        }

        PageResult<List<SsRoomLotteryDetailDto>> detailList = contentClient.getRoomLotteryDetailList(
                companyType, roomId, sceneId, lotteryId, prizeId, relationServerId, salesId, customerList, isWin, isHandled, current, size);
        BaseResult<List<ImLiveRoom>> liveRoomResult = imClient.filterLiveRoomList(companyType, null, null, null, true);
        if (liveRoomResult.isFail()) {
            throw new BusinessException(liveRoomResult);
        }
        Map<Long, ImLiveRoom> roomMap = liveRoomResult.getData().stream().collect(Collectors.toMap(ImLiveRoom::getId, Function.identity()));
        List<Long> sceneIdList = detailList.getData().stream().filter(e -> ObjectUtil.isNotEmpty(e.getLottery().getSceneId())).map(e -> e.getLottery().getSceneId()).collect(Collectors.toList());
        Map<Long, String> tempMap = null;
        if (ObjectUtil.isNotEmpty(sceneIdList)) {
            BaseResult<List<ImRoomScene>> batchGetScene = imClient.batchGetScene(BatchReq.of(sceneIdList));
            if (batchGetScene.isFail()) {
                throw new BusinessException(batchGetScene);
            }
            if (batchGetScene.isPresent()) {
                tempMap = batchGetScene.getData().stream().collect(Collectors.toMap(ImRoomScene::getId, ImRoomScene::getTitle));
            }
        }
        Map<Long, String> sceneMap = tempMap;
        Set<Integer> userIds = new HashSet<>();
        List<RoomLotteryDetailResp> details = detailList.getData().stream().map(item -> {
            RoomLotteryDetailResp resp = new RoomLotteryDetailResp();
            userIds.add(item.getDetail().getUserId());
            BeanUtils.copyProperties(item.getDetail(), resp);
            resp.setCustomerId(item.getDetail().getUserId());
            resp.setCustomerCode(DesensitizeUtil.idToMask(item.getDetail().getUserId()));
            resp.setRoomId(item.getLottery().getRoomId());
            if (roomMap.containsKey(item.getLottery().getRoomId())) {
                resp.setRoomName(roomMap.get(item.getLottery().getRoomId()).getName());
            }
            resp.setLotteryName(item.getLottery().getName());
            resp.setPrizeName(item.getPrize().getName());
            resp.setSceneId(item.getLottery().getSceneId());
            resp.setLotteryType(item.getLottery().getType());
            if (ObjectUtil.isNotEmpty(sceneMap) && sceneMap.containsKey(resp.getSceneId())) {
                resp.setSceneName(sceneMap.get(resp.getSceneId()));
            }
            if (item.getDetail().getIsHandled() && ObjectUtil.isNotEmpty(item.getDetail().getMobile())) {
                // 已处理，地址不变
                resp.setAddress(item.getDetail().getAddress());
                resp.setRegion(item.getDetail().getRegion());
                resp.setCustomerName(item.getDetail().getName());
            }
            return resp;
        }).collect(Collectors.toList());
        if (!userIds.isEmpty()) {
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(new BatchReq<>(userIds)).getData();
            Map<Integer, UcUserAddress> addressMap = userClient.getUserAddressMap(new ArrayList<>(userIds)).getData();
            details.forEach(detail -> {
                UcUsers user = userMap.get(detail.getCustomerId());
                if (user != null) {
                    detail.setNickname(user.getNickName());
                }
                UcUserAddress address = addressMap.get(detail.getCustomerId());
                if (!detail.getIsHandled() && address != null) {
                    // 未处理，从地址表取最新地址
                    detail.setCustomerName(address.getName());
                    detail.setRegion(address.getRegion());
                    detail.setAddress(address.getAddress());
                }
            });
        }

        return PageResult.success(details, detailList.getPagination());
    }

    public BaseResult<String> markRoomLotteryDetailHandled(Long detailId, String userCode) {
        int userId = DesensitizeUtil.maskToId(userCode);
        BaseResult<UcUserAddress> result = userClient.findUserAddress(userId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            throw new BusinessException("暂无客户收货信息，请先联系客户填写完成");
        }
        UcUserAddress address = result.getData();
        return contentClient.markRoomLotteryDetailHandled(detailId, address.getMaskMobile(), address.getMobileId(), address.getRegion(),
                address.getAddress(), address.getName());
    }
}

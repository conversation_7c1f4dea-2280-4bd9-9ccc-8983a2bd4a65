package cn.shrise.radium.adminapi.service.notification;

import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.resp.DeviceInfoItem;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.notificationservice.NotificationClient;
import cn.shrise.radium.notificationservice.entity.DeviceInfo;
import cn.shrise.radium.notificationservice.req.CreateOrUpdateDeviceReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceService {

    private final NotificationClient notificationClient;
    private final UserClient userClient;

    public PageResult<List<DeviceInfoItem>> getDeviceList(Integer companyType, Integer productType, Integer userId,
                                                          Integer platform, Integer channel,
                                                          Integer current, Integer size) {
        PageResult<List<DeviceInfo>> pageResult = notificationClient.getDeviceList(companyType, productType, userId, platform, channel, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }
        List<DeviceInfo> deviceList = pageResult.getData();
        if (ObjectUtils.isEmpty(deviceList)) {
            return PageResult.empty(current, size);
        }
        Set<Integer> userIdSet = deviceList.stream().map(DeviceInfo::getUserId).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> batchGetUserResult = userClient.batchGetUserMap(BatchReq.create(userIdSet));
        Map<Integer, UcUsers> usersMap;
        if (batchGetUserResult.isFail()) {
            usersMap = new HashMap<>();
            log.error("batch get user error code: {}, msg: {}", batchGetUserResult.getCode(), batchGetUserResult.getMsg());
        } else {
            usersMap = batchGetUserResult.getData();
        }
        List<DeviceInfoItem> itemList = deviceList.stream().map(e -> {
            Integer uid = e.getUserId();
            UserInfo userInfo = UserInfo.of(usersMap.get(uid));
            DeviceInfoItem item = new DeviceInfoItem();
            BeanUtils.copyProperties(e, item);
            item.setUserInfo(userInfo);
            return item;
        }).collect(Collectors.toList());
        return PageResult.success(itemList, pageResult.getPagination());

    }

    /**
     * 解除绑定
     * @param req
     * @return
     */
    public BaseResult<String> unbind(CreateOrUpdateDeviceReq req) {
        BaseResult<String> result = notificationClient.createOrUpdateDevice(req);
        if (result.isFail()){
            log.error("device unbind error code: {}, msg: {}",result.getCode(),result.getMsg());
            return result;
        }
        return BaseResult.success();
    }
}

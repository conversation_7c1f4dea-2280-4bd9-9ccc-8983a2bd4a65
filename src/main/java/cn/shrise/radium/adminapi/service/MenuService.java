package cn.shrise.radium.adminapi.service;

import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.adminapi.entity.MenuConfig;
import cn.shrise.radium.adminapi.entity.MenusConfig;
import cn.shrise.radium.adminapi.entity.PageConfig;
import cn.shrise.radium.adminapi.properties.MenuProperties;
import cn.shrise.radium.adminapi.properties.MenusProperties;
import cn.shrise.radium.adminapi.properties.PageProperties;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.userservice.UserClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MenuService {

    private final UserClient userClient;
    private final PageProperties pageProperties;
    private final MenuProperties menuProperties;
    private final ObjectMapper objectMapper;
    private final MenusProperties menusProperties;

    public List<MenuConfig> getMenuList(Integer companyType, Integer userId) {
        final List<Integer> permissionList = getPermissionList(userId);
        final Set<Integer> permissionSet = new HashSet<>(permissionList);
        final List<MenuConfig> menuConfigs = menuProperties.getConfigs().getOrDefault(companyType, Collections.emptyList());

        final Map<String, Integer> pageConfigMap = pageProperties.getConfigs().stream()
                .filter(e -> permissionSet.contains(e.getId()))
                .collect(Collectors.toMap(PageConfig::getName, PageConfig::getId));

        final Map<String, PageConfig> pageConfigMapInfo = pageProperties.getConfigs().stream()
                .collect(Collectors.toMap(PageConfig::getName, t -> t));

        if (ObjectUtils.isEmpty(pageConfigMap)) {
            return Collections.emptyList();
        }
        final List<MenuConfig> newMenuConfigs = copyPageConfig(menuConfigs);

        return newMenuConfigs.stream()
                .map(e -> filterMenu(e, pageConfigMap))
                .filter(Objects::nonNull)
                .map(e -> filterEmptyMenu(e, pageConfigMapInfo, permissionSet))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

    }

    private List<MenuConfig> copyPageConfig(List<MenuConfig> configs) {
        return configs.stream().map(e -> {
            final String configValue;
            try {
                configValue = objectMapper.writeValueAsString(e);
                return objectMapper.readValue(configValue, MenuConfig.class);
            } catch (JsonProcessingException ex) {
                throw new BusinessException(ErrorConstant.MENU_INVALID);
            }
        }).collect(Collectors.toList());
    }

    private MenuConfig filterMenu(MenuConfig menuConfig, Map<String, Integer> pageConfigMap) {
        if (menuConfig == null || ObjectUtils.isEmpty(menuConfig.getNodeList())) return null;

        Deque<MenuConfig> deque = new LinkedList<>();
        deque.push(menuConfig);
        while (!deque.isEmpty()) {
            final MenuConfig parent = deque.pop();
            if (!ObjectUtils.isEmpty(parent.getNodeList())) {
                for (MenuConfig current : parent.getNodeList()) {
                    if (!ObjectUtils.isEmpty(current.getNodeList())) {
                        deque.push(current);
                    } else {
                        filterMenu(current, parent, pageConfigMap);
                    }
                }
            }
        }
        return menuConfig;
    }


    private MenuConfig filterEmptyMenu(MenuConfig menuConfig, Map<String, PageConfig> pageConfigMapInfo, Set<Integer> permissionSet) {
        if (menuConfig == null) return null;
        if (Objects.nonNull(menuConfig.getName()) && ObjectUtils.isEmpty(menuConfig.getNodeList())) {
            PageConfig page = pageConfigMapInfo.get(menuConfig.getName());
            if (Objects.nonNull(page.getPermList())) {
                List<PageConfig> permList = page.getPermList().stream().filter(e -> permissionSet.contains(e.getId())).collect(Collectors.toList());
                menuConfig.setPermList(permList);
            }
            menuConfig.setId(page.getId());
            return menuConfig;
        }
        final List<MenuConfig> nodeList = menuConfig.getNodeList().stream()
                .map(e -> filterEmptyMenu(e, pageConfigMapInfo, permissionSet))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(nodeList)) {
            return null;
        }
        menuConfig.setNodeList(nodeList);
        return menuConfig;
    }

    private void filterMenu(MenuConfig current, MenuConfig parent, Map<String, Integer> pageConfigMap) {
        if (!ObjectUtils.isEmpty(current.getName())) {
            final Integer permission = pageConfigMap.get(current.getName());
            // 不在权限
            if (permission == null) {
                final List<MenuConfig> filterMenuConfig = parent.getNodeList().stream()
                        .filter(e -> {
                            if (Objects.isNull(e.getName())) {
                                return true;
                            }
                            return !e.getName().equals(current.getName());
                        })
                        .collect(Collectors.toList());
                parent.setNodeList(filterMenuConfig);
            }
        }
    }

    private List<Integer> getPermissionList(Integer userId) {
        final BaseResult<List<Integer>> result = userClient.getPermissionList(userId);
        if (result.isSuccess()) {
            return result.getData();
        }
        log.error("获取用户权限失败 code: {}, msg: {}", result.getCode(), result.getMsg());
        return Collections.emptyList();
    }

    public MenusConfig getNewMenu(Integer companyType, Integer userId, String domain) {
        final List<Integer> permissionList = getPermissionList(userId);
        final Set<Integer> permissionSet = new HashSet<>(permissionList);
        final List<MenusConfig> menuConfigs = menusProperties.getConfigs().getOrDefault(companyType, Collections.emptyList());
        final MenusConfig menusConfig = menuConfigs.stream().filter(menu -> menu.getDomain().contains(domain)).collect(Collectors.toList()).get(0);
        final Map<String, Integer> pageConfigMap = pageProperties.getConfigs().stream()
                .filter(e -> permissionSet.contains(e.getId()))
                .collect(Collectors.toMap(PageConfig::getName, PageConfig::getId));

        final Map<String, PageConfig> pageConfigMapInfo = pageProperties.getConfigs().stream()
                .collect(Collectors.toMap(PageConfig::getName, t -> t));

        if (ObjectUtils.isEmpty(pageConfigMap)) {
            return new MenusConfig();
        }
        final List<MenuConfig> newMenuConfigs = copyPageConfig(menusConfig.getInfo());

        List<MenuConfig> configList = newMenuConfigs.stream()
                .map(e -> filterMenu(e, pageConfigMap))
                .filter(Objects::nonNull)
                .map(e -> filterEmptyMenu(e, pageConfigMapInfo, permissionSet))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        MenusConfig config = new MenusConfig();
        BeanUtils.copyProperties(menusConfig,config);
        config.setInfo(configList);
        return config;
    }
}

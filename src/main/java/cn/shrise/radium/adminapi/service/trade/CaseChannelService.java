package cn.shrise.radium.adminapi.service.trade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.AnalystInfoResp;
import cn.shrise.radium.adminapi.resp.StockCaseChannelResp;
import cn.shrise.radium.adminapi.resp.trade.StockCaseChannelRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.resp.LiveRoomNameResp;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.constant.TradeErrorCode;
import cn.shrise.radium.tradeservice.entity.TdCaseChannelAnalystRelation;
import cn.shrise.radium.tradeservice.entity.TdStockCaseChannel;
import cn.shrise.radium.tradeservice.entity.TdStockCaseChannelRecord;
import cn.shrise.radium.tradeservice.req.CaseChannelAnalystReq;
import cn.shrise.radium.tradeservice.resp.StrategyUserInfoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseChannelService {

    private final TradeClient tradeClient;
    private final ContentClient contentClient;
    private final OrderClient orderClient;
    private final ImClient imClient;
    private final UserClient userClient;

    public List<StockCaseChannelResp> getAllCaseChannelList(Integer companyType, List<Integer> channelTypes, Boolean enabled) {
        List<TdStockCaseChannel> caseChannels = tradeClient.getAllCaseChannelList(companyType, channelTypes, enabled).orElse(new ArrayList<>());
        return caseChannels.stream().map(channel -> {
            StockCaseChannelResp resp = new StockCaseChannelResp();
            BeanUtils.copyProperties(channel, resp);
            return resp;
        }).collect(Collectors.toList());
    }

    public PageResult<List<StockCaseChannelResp>> getStockCaseChannelList(Integer userId, Integer channelType, Integer companyType, String searchContent, Integer current, Integer size) {

        List<Long> channelIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(userId)) {
            BaseResult<Set<Long>> processableCaseChannelIdsRes = tradeClient.getProcessableCaseChannelIds(userId);
            if (processableCaseChannelIdsRes.isFail()) {
                log.error("获取用户可处理的频道失败： {}", processableCaseChannelIdsRes);
                throw new BusinessException(processableCaseChannelIdsRes);
            }
            Set<Long> channelIdSet = processableCaseChannelIdsRes.getData();
            if (CollUtil.isEmpty(channelIdSet)) {
                return PageResult.empty(current, size);
            }
            channelIds = Lists.newArrayList(channelIdSet);
        }

        PageResult<List<TdStockCaseChannel>> stockCaseChannelBaseResult = tradeClient.getStockCaseChannelList(channelIds, channelType, companyType, searchContent, current, size);

        if (stockCaseChannelBaseResult.isFail()) {
            log.error("获取案例频道失败: {}", stockCaseChannelBaseResult);
            throw new BusinessException(stockCaseChannelBaseResult);
        }
        List<TdStockCaseChannel> caseChannels = stockCaseChannelBaseResult.getData() == null ? Collections.emptyList() : stockCaseChannelBaseResult.getData();
        if (ObjectUtils.isEmpty(caseChannels)) {
            return PageResult.success(Collections.emptyList(), stockCaseChannelBaseResult.getPagination());
        }

        List<Long> roomIds = caseChannels.stream().map(TdStockCaseChannel::getRoomId).collect(Collectors.toList());
        BaseResult<List<LiveRoomNameResp>> liveRoomNameBaseResult = contentClient.getLiveRoomName(roomIds, null, null, null, null, null);
        if (liveRoomNameBaseResult.isFail()) {
            log.error("获取直播室名称失败: {}", liveRoomNameBaseResult);
            throw new BusinessException(liveRoomNameBaseResult);
        }
        List<LiveRoomNameResp> liveRoomNames = liveRoomNameBaseResult.getData() == null ? Collections.emptyList() : liveRoomNameBaseResult.getData();
        Map<Long, String> liveRoomMap = liveRoomNames.stream().collect(Collectors.toMap(LiveRoomNameResp::getPkId, LiveRoomNameResp::getRoomName));

        List<Integer> seriesIds = caseChannels.stream().map(TdStockCaseChannel::getSeriesId).collect(Collectors.toList());
        BaseResult<List<ArticleSeries>> articleSeriesBaseResult = orderClient.filterArticleSeries(seriesIds, null, null, null);
        if (articleSeriesBaseResult.isFail()) {
            log.error("获取研报栏目列表失败: {}", articleSeriesBaseResult);
            throw new BusinessException(articleSeriesBaseResult);
        }
        List<ArticleSeries> articleSeries = articleSeriesBaseResult.getData() == null ? Collections.emptyList() : articleSeriesBaseResult.getData();
        Map<Integer, String> seriesNameMap = articleSeries.stream().collect(Collectors.toMap(ArticleSeries::getId, ArticleSeries::getName));

        List<Long> chatIds = caseChannels.stream().map(TdStockCaseChannel::getChatId).collect(Collectors.toList());
        BaseResult<List<ImChatRoom>> chatRoomResult = imClient.filterChatRoomList(companyType, chatIds, null, null, true);
        if (chatRoomResult.isFail()) {
            log.error("获取聊天室列表失败: {}", articleSeriesBaseResult);
            throw new BusinessException(chatRoomResult);
        }
        List<ImChatRoom> chatRoomList = chatRoomResult.getData() == null ? Collections.emptyList() : chatRoomResult.getData();
        Map<Long, String> chatRoomNameMap = chatRoomList.stream().collect(Collectors.toMap(ImChatRoom::getId, ImChatRoom::getName));

        List<StockCaseChannelResp> resps = caseChannels.stream().map(channel -> {
            StockCaseChannelResp resp = new StockCaseChannelResp();
            BeanUtils.copyProperties(channel, resp);
            resp.setRoomName(liveRoomMap.get(channel.getRoomId()));
            resp.setArticleSeriesName(seriesNameMap.get(channel.getSeriesId()));
            resp.setChatRoomName(chatRoomNameMap.get(channel.getChatId()));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, stockCaseChannelBaseResult.getPagination());
    }

    public BaseResult<List<AnalystInfoResp>> getAnalystInfoByChannelIds(Long channelId) {

        BaseResult<List<TdCaseChannelAnalystRelation>> analystRelationsBaseResult = tradeClient.getCaseChannelAnalystRelations(channelId);

        if (analystRelationsBaseResult.isFail()) {
            log.error("获取老师关系记录失败： {}", analystRelationsBaseResult);
            throw new BusinessException(analystRelationsBaseResult);
        }

        List<TdCaseChannelAnalystRelation> analystRelations = analystRelationsBaseResult.getData() == null ? Collections.emptyList() : analystRelationsBaseResult.getData();
        if (ObjectUtils.isEmpty(analystRelations)) {
            return BaseResult.success(new ArrayList<>());
        }
        List<Integer> analystIds = analystRelations.stream().map(TdCaseChannelAnalystRelation::getAnalystId).collect(Collectors.toList());

        BaseResult<List<SsAnalystInfo>> analystInfoBaseResult = contentClient.getAnalystInfoList(analystIds, null, null);
        if (analystInfoBaseResult.isFail()) {
            log.error("获取老师列表失败： {}", analystInfoBaseResult);
            throw new BusinessException(analystInfoBaseResult);
        }
        List<SsAnalystInfo> analystInfos = analystInfoBaseResult.getData() == null ? Collections.emptyList() : analystInfoBaseResult.getData();
        List<AnalystInfoResp> resps = analystInfos.stream().map(analyst -> {
            AnalystInfoResp resp = new AnalystInfoResp();
            BeanUtils.copyProperties(analyst, resp);
            return resp;
        }).collect(Collectors.toList());

        return BaseResult.success(resps);

    }

    public BaseResult<TradeErrorCode> addAnalyst(CaseChannelAnalystReq req) {

        BaseResult<List<TdCaseChannelAnalystRelation>> analystRelationsBaseResult = tradeClient.getCaseChannelAnalystRelations(req.getChannelId());
        if (analystRelationsBaseResult.isFail()) {
            log.error("获取老师关系失败： {}", analystRelationsBaseResult);
            throw new BusinessException(analystRelationsBaseResult);
        }
        List<TdCaseChannelAnalystRelation> analystRelations = analystRelationsBaseResult.getData() == null ? Collections.emptyList() : analystRelationsBaseResult.getData();
        List<Integer> reqAnalystId = req.getAnalystId();
        List<Long> updateAnalystIds = analystRelations.stream().filter(analyst -> !reqAnalystId.contains(analyst.getAnalystId())).map(TdCaseChannelAnalystRelation::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(updateAnalystIds)) {
            BaseResult<TradeErrorCode> tradeErrorCodeBaseResult = tradeClient.deleteAnalystRelation(updateAnalystIds);
            if (tradeErrorCodeBaseResult.isFail()) {
                log.error("删除老师关系失败： {}", tradeErrorCodeBaseResult);
                throw new BusinessException(tradeErrorCodeBaseResult);
            }
        }
        if (ObjectUtils.isEmpty(reqAnalystId)) {
            return BaseResult.success(TradeErrorCode.SUCCESS);
        }
        return tradeClient.addAnalyst(req);
    }

    public PageResult<List<StockCaseChannelRecordResp>> getStockCaseChannelRecordList(Long channelId, Integer current, Integer size) {
        PageResult<List<TdStockCaseChannelRecord>> pageResult = tradeClient.getStockCaseChannelRecordList(channelId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<TdStockCaseChannelRecord> data = pageResult.getData();

        Set<Integer> userSet = data.stream().map(TdStockCaseChannelRecord::getOperateId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));

        List<StockCaseChannelRecordResp> records = data.stream().map(t -> {
            StockCaseChannelRecordResp resp = new StockCaseChannelRecordResp();
            BeanUtil.copyProperties(t, resp);
            resp.setOperateName(usersMap.get(t.getOperateId()).getRealName());
            resp.setOperateAvatarUrl(usersMap.get(t.getOperateId()).getAvatarUrl());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }

    public PageResult<List<StrategyUserInfoResp>> getStrategyUserInfoList(Long id, String searchContent, Integer current, Integer size) {
        Integer userId = StringUtils.isNotBlank(searchContent) ? DesensitizeUtil.maskToId(searchContent) : null;
        PageResult<List<StrategyUserInfoResp>> strategyUserInfoList = tradeClient.getStrategyUserInfoList(id, userId, current, size);
        if (ObjectUtil.isEmpty(strategyUserInfoList.getData())) {
            return PageResult.empty();
        }
        List<StrategyUserInfoResp> respList = strategyUserInfoList.getData().stream().filter(Objects::nonNull).peek(o -> {
            o.setUserCode(DesensitizeUtil.idToMask(o.getUserId()));
        }).collect(Collectors.toList());
        return PageResult.success(respList, strategyUserInfoList.getPagination());
    }
}

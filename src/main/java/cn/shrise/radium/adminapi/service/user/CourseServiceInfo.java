package cn.shrise.radium.adminapi.service.user;

import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.entity.RsCourseService;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CourseServiceInfo {

    private Integer id;

    private Integer seriesId;

    private Integer serviceType;

    private Integer customerId;

    private String customerCode;

    private Integer wxId;

    private Boolean enabled;

    private Instant openTime;

    private Instant expireTime;

    private Instant createTime;

    private Instant updateTime;

    public static CourseServiceInfo of(RsCourseService service) {
        if (service == null) {
            return null;
        }
        return CourseServiceInfo.builder()
                .id(service.getId())
                .seriesId(service.getSeriesId())
                .customerId(service.getCustomerId())
                .customerCode(DesensitizeUtil.idToMask(service.getCustomerId()))
                .wxId(service.getWxId())
                .enabled(service.getEnabled())
                .openTime(service.getOpenTime())
                .expireTime(service.getExpireTime())
                .createTime(service.getCreateTime())
                .updateTime(service.getUpdateTime())
                .build();
    }
}

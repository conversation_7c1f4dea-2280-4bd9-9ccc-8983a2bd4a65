package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.entity.WorkWxUserInfo;
import cn.shrise.radium.adminapi.entity.WwxUserBlockFlowInfo;
import cn.shrise.radium.adminapi.req.WorkWxBatchCreateCustomerFollowUpBelongReq;
import cn.shrise.radium.adminapi.req.WorkWxCreateCustomerFollowUpBelongReq;
import cn.shrise.radium.adminapi.req.WwxUserListReq;
import cn.shrise.radium.adminapi.resp.*;
import cn.shrise.radium.adminapi.resp.user.*;
import cn.shrise.radium.adminapi.resp.workWx.*;
import cn.shrise.radium.authservice.AuthClient;
import cn.shrise.radium.authservice.req.WorkWxLoginReq;
import cn.shrise.radium.authservice.resp.WorkWxLoginResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.EvaluationUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsCustomerEvaluation;
import cn.shrise.radium.contentservice.entity.SsSurveyChoice;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.OrderStatusConstant;
import cn.shrise.radium.orderservice.constant.RefundOrderAuditStatusEnum;
import cn.shrise.radium.orderservice.entity.UserStatisticsCount;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.resp.PortfolioFollowResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.IdentityTypeEnum;
import cn.shrise.radium.userservice.dto.CustomerTagDto;
import cn.shrise.radium.userservice.dto.ServiceCustomerDto;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.req.*;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WorkWxFullContactUserRelation;
import cn.shrise.radium.workwxservice.bean.WwxConfig;
import cn.shrise.radium.workwxservice.constant.AddWayEnum;
import cn.shrise.radium.workwxservice.entity.*;
import cn.shrise.radium.workwxservice.entity.NpDzAdChannel;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContact;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactTag;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactUserRelation;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactWay;
import cn.shrise.radium.workwxservice.property.WxCpProperties;
import cn.shrise.radium.workwxservice.resp.*;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import cn.shrise.radium.wxservice.properties.WxMpProperties;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.shaded.com.google.common.collect.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.authservice.constant.TokenTypeConstant.ADMIN;
import static cn.shrise.radium.contentservice.constant.IdentityTypeConstant.ID_CARD;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkWxService {

    private final WorkwxClient workwxClient;
    private final WxClient wxClient;
    private final AuthClient authClient;
    private final OrderClient orderClient;
    private final ContentClient contentClient;
    private final UserClient userClient;
    private final CommonProperties commonProperties;
    private final EvaluationUtil evaluationUtil;

    private final TradeClient tradeClient;

    public List<WxCpPropertiesResp> getAccount(Integer companyType) {
        BaseResult<List<WxMpProperties>> wxAccountConfig = wxClient.getWxAccountConfig(companyType);
        BaseResult<Map<Integer, NpWxOaStatus>> account = workwxClient.getAccount(companyType);
        if (wxAccountConfig.isFail()) {
            throw new BusinessException(wxAccountConfig);
        }
        List<WxCpPropertiesResp> wxCpPropertiesResps = new ArrayList<>();
        wxAccountConfig.getData().forEach(e -> {
            WxCpPropertiesResp wxCpPropertiesResp = new WxCpPropertiesResp();
            if (account.getData().containsKey(e.getAccountType())) {
                e.setName(account.getData().get(e.getAccountType()).getName());
            }
            BeanUtil.copyProperties(e, wxCpPropertiesResp);
            wxCpPropertiesResps.add(wxCpPropertiesResp);
        });
        return wxCpPropertiesResps;
    }

    public List<WorkWxUserInfo> getBelongAccount(Integer salesId, Boolean deleted, Boolean enabled, Integer companyType,
                                                 Integer accountType, Integer status, String mobile) {
        final BaseResult<List<NpWorkWxUser>> result = workwxClient.getBelongAccount(salesId, deleted, enabled, companyType, accountType, status, mobile);
        final BaseResult<List<WwxConfig>> configResult = workwxClient.getConfigList(companyType);
        Map<Integer, WwxConfig> configMap;
        if (result.isFail()) {
            log.error("获取企业微信账号失败, {}", result);
            return emptyList();
        }

        if (configResult.isFail()) {
            log.error("获取企业微信配置失败: {}", configResult);
            configMap = Collections.emptyMap();
        } else {
            configMap = configResult.getData().stream().collect(Collectors.toMap(WwxConfig::getAccountType, Function.identity()));
        }

        final List<NpWorkWxUser> data = result.getData();

        Map<Long, NpWwxUserBlockFlow> blockFlowMap = Collections.emptyMap();
        final Set<Long> blockFlowSet = data.stream().map(NpWorkWxUser::getBlockFlowId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (blockFlowSet.size() > 0) {
            final BaseResult<List<NpWwxUserBlockFlow>> blockFlowResult = workwxClient.getWwxUserBlockFlowByIdList(blockFlowSet);
            if (blockFlowResult.isFail()) {
                log.error("获取企业微信配置失败: {}", configResult);
            } else {
                blockFlowMap = blockFlowResult.getData().stream().collect(Collectors.toMap(NpWwxUserBlockFlow::getId, Function.identity()));
            }
        }

        Map<Long, NpWwxUserBlockFlow> finalBlockFlowMap = blockFlowMap;
        return data.stream().map(e -> {
            WorkWxUserInfo info = new WorkWxUserInfo();
            BeanUtils.copyProperties(e, info);
            if (ObjectUtils.isNotEmpty(e.getMobile())) {
                info.setMobile(AESUtil.decrypt(e.getMobile()));
            }
            final WwxConfig config = configMap.get(e.getAccountType());
            if (config != null) {
                info.setCropName(config.getCorpName());
            }
            final NpWwxUserBlockFlow blockFlow = finalBlockFlowMap.get(e.getBlockFlowId());
            if (blockFlow != null) {
                info.setBlockFlowInfo(BeanUtil.copyProperties(blockFlow, WwxUserBlockFlowInfo.class));
            }

            return info;
        }).collect(Collectors.toList());
    }

    public List<WorkWxUserInfo> getBelongAccountConfig(Integer salesId, Boolean deleted, Boolean enabled, Integer companyType,
                                                       Integer accountType, Integer status) {
        final BaseResult<List<NpWorkWxUser>> result = workwxClient.getBelongAccount(salesId, deleted, enabled, companyType, accountType, status, null);
        final BaseResult<List<WwxConfig>> configResult = workwxClient.getConfigList(companyType);
        Map<Integer, WwxConfig> configMap;
        if (result.isFail()) {
            log.error("获取企业微信账号失败, {}", result);
            return emptyList();
        }

        if (configResult.isFail()) {
            log.error("获取企业微信配置失败: {}", configResult);
            configMap = Collections.emptyMap();
        } else {
            configMap = configResult.getData().stream().collect(Collectors.toMap(WwxConfig::getAccountType, Function.identity()));
        }

        List<NpWorkWxUser> data = result.getData();
        if (ObjectUtils.isEmpty(accountType)) {
            data = data.stream().filter(e -> configMap.containsKey(e.getAccountType())).collect(Collectors.toList());
        }

        Map<Long, NpWwxUserBlockFlow> blockFlowMap = Collections.emptyMap();
        final Set<Long> blockFlowSet = data.stream().map(NpWorkWxUser::getBlockFlowId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (blockFlowSet.size() > 0) {
            final BaseResult<List<NpWwxUserBlockFlow>> blockFlowResult = workwxClient.getWwxUserBlockFlowByIdList(blockFlowSet);
            if (blockFlowResult.isFail()) {
                log.error("获取企业微信配置失败: {}", configResult);
            } else {
                blockFlowMap = blockFlowResult.getData().stream().collect(Collectors.toMap(NpWwxUserBlockFlow::getId, Function.identity()));
            }
        }

        Map<Long, NpWwxUserBlockFlow> finalBlockFlowMap = blockFlowMap;
        return data.stream().map(e -> {
            WorkWxUserInfo info = new WorkWxUserInfo();
            BeanUtils.copyProperties(e, info);
            if (ObjectUtils.isNotEmpty(e.getMobile())) {
                info.setMobile(AESUtil.decrypt(e.getMobile()));
            }
            final WwxConfig config = configMap.get(e.getAccountType());
            if (config != null) {
                info.setCropName(config.getCorpName());
            }
            final NpWwxUserBlockFlow blockFlow = finalBlockFlowMap.get(e.getBlockFlowId());
            if (blockFlow != null) {
                info.setBlockFlowInfo(BeanUtil.copyProperties(blockFlow, WwxUserBlockFlowInfo.class));
            }

            return info;
        }).collect(Collectors.toList());
    }

    public String redirectUri(String code, String state, String agent, Integer accountType, String redirectUri) {
        WorkWxLoginReq workWxLoginReq = WorkWxLoginReq.builder()
                .agent(agent)
                .tokenType(ADMIN)
                .accountType(accountType)
                .code(code)
                .state(state)
                .build();
        WorkWxLoginResp workWxLoginResp = authClient.workWxLogin(workWxLoginReq).orElseThrow();

        URL url;
        try {
            url = new URL(redirectUri);
        } catch (MalformedURLException e) {
            log.error("url parse error ", e);
            return "";
        }

        String path = url.getFile();
        String originUrl = String.format("%s://%s", url.getProtocol(), url.getAuthority());
        String params = buildParams(workWxLoginResp);

        return UriComponentsBuilder.fromHttpUrl(originUrl)
                .pathSegment("redirect")
                .queryParam("path", URLEncodeUtil.encodeAll(path))
                .queryParam("params", URLEncodeUtil.encodeAll(params))
                .build()
                .toUriString();
    }

    public String getAuthorizationUrl(Integer accountType, String agent, String redirectUri, String scope, String state) {
        String serverRedirectUrl = getServerRedirectUrl(accountType, redirectUri);
        return workwxClient.getAuthorizationUrl(accountType, agent, serverRedirectUrl, scope, state).orElseThrow();
    }

    public String getRpaAuthorizationUrl(String authType, Integer accountType, String agent, String redirectUri, String scope, String state) {
        String serverRedirectUrl = getServerRedirectUrl(authType, accountType, redirectUri);
        return workwxClient.getAuthorizationUrl(accountType, agent, serverRedirectUrl, scope, state).orElseThrow();
    }

    private String getServerRedirectUrl(String authType, Integer accountType, String redirectUri) {
        String serverUrl = commonProperties.getAdminApiUrl();
        if (Objects.equals(authType, "rpa")) {
            serverUrl = commonProperties.getAdminApiAuthUrl();
        }
        String encodeRedirectUri = URLEncodeUtil.encodeAll(redirectUri, StandardCharsets.UTF_8);
        return UriComponentsBuilder.fromUriString(serverUrl)
                .path("/work_wx/oauth2/redirectUri")
                .queryParam("accountType", accountType)
                .queryParam("redirectUri", encodeRedirectUri)
                .build()
                .toUriString();
    }

    private String getServerRedirectUrl(Integer accountType, String redirectUri) {
        String serverUrl = commonProperties.getAdminApiUrl();
        String encodeRedirectUri = URLEncodeUtil.encodeAll(redirectUri, StandardCharsets.UTF_8);
        return UriComponentsBuilder.fromUriString(serverUrl)
                .path("/work_wx/oauth2/redirectUri")
                .queryParam("accountType", accountType)
                .queryParam("redirectUri", encodeRedirectUri)
                .build()
                .toUriString();
    }

    private String buildParams(WorkWxLoginResp resp) {
        JSONObject jsonObject = new JSONObject();
        Integer accountType = resp.getAccountType();
        Long workWxId = resp.getWorkWxId();
        String wxAccount = resp.getWxAccount();

        String accessToken = Objects.isNull(resp.getAccessToken()) ? "" : resp.getAccessToken().getValue();
        String refreshToken = Objects.isNull(resp.getRefreshToken()) ? "" : resp.getRefreshToken().getValue();

        jsonObject.put("accountType", accountType);
        jsonObject.put("workWxId", workWxId);
        jsonObject.put("wxAccount", wxAccount);
        jsonObject.put("accessToken", accessToken);
        jsonObject.put("refreshToken", refreshToken);
        return jsonObject.toJSONString();
    }

    public WorkWxUserResp getWorkWxAccount(Long id) {
        NpWorkWxUser npWorkWxUser = workwxClient.getWorkWxAccount(id).orElseThrow();
        WorkWxUserResp resp = new WorkWxUserResp();
        BeanUtils.copyProperties(npWorkWxUser, resp);

        if (npWorkWxUser.getBelongId() != null) {
            UcRole ucRole = userClient.getRoleInfo(resp.getBelongId()).orElse(null);
            if (ObjectUtil.isNotEmpty(ucRole)) {
                resp.setRoleId(ucRole.getId());
                resp.setRoleName(ucRole.getName());
            }
            UcUsers ucUsers = userClient.getUser(resp.getBelongId()).orElse(null);
            if (ObjectUtil.isNotEmpty(ucUsers)) {
                resp.setBelongName(ucUsers.getRealName());
            }
        }

        BaseResult<WwxConfig> wwxConfigBaseResult = workwxClient.getConfig(npWorkWxUser.getAccountType());
        if (wwxConfigBaseResult.isSuccess() && ObjectUtil.isNotNull(wwxConfigBaseResult.getData())) {
            WwxConfig config = wwxConfigBaseResult.getData();
            resp.setCorpId(config.getCorpId());
            resp.setCorpName(config.getCorpName());
        }

        return resp;
    }

    public WwxDetailResp.WorkWxInfo getWwxDetail(Long id) {
        NpWorkWxUser npWorkWxUser = workwxClient.getWorkWxAccount(id).orElseThrow();
        WwxDetailResp.WorkWxInfo resp = new WwxDetailResp.WorkWxInfo();
        BeanUtils.copyProperties(npWorkWxUser, resp);

        if (npWorkWxUser.getBelongId() != null) {
            UcRole ucRole = userClient.getRoleInfo(resp.getBelongId()).orElse(null);
            if (ObjectUtil.isNotEmpty(ucRole)) {
                resp.setRoleId(ucRole.getId());
                resp.setRoleName(ucRole.getName());
            }
            UcUsers ucUsers = userClient.getUser(resp.getBelongId()).orElse(null);
            if (ObjectUtil.isNotEmpty(ucUsers)) {
                resp.setBelongName(ucUsers.getRealName());
            }
        }
        BaseResult<WwxConfig> wwxConfigBaseResult = workwxClient.getConfig(npWorkWxUser.getAccountType());
        if (wwxConfigBaseResult.isSuccess() && ObjectUtil.isNotNull(wwxConfigBaseResult.getData())) {
            WwxConfig config = wwxConfigBaseResult.getData();
            resp.setCorpId(config.getCorpId());
            resp.setCorpName(config.getCorpName());
        }
        return resp;
    }

    public BaseResult<WorkWxJsApiSignature> getJsApiSignature(Integer accountType, String agent, String url) {
        return workwxClient.getJsApiSignature(accountType, agent, url);
    }

    public BaseResult<WorkWxJsApiSignature> getAgentJsApiSignature(Integer accountType, String agent, String url) {
        return workwxClient.getAgentJsApiSignature(accountType, agent, url);
    }

    public WorkWxContactResp getWorkWxContact(Integer companyType, Integer accountType, String wxAccount, String externalUserId) {
        WorkWxFullContactUserRelation full = workwxClient.getFullFriendRelation(accountType, wxAccount, externalUserId)
                .orElseThrow();
        NpWorkWxContactUserRelation relation = full.getRelation();
        NpWorkWxContact contact = full.getContact();
        String unionId = contact.getUnionId();

        WorkWxContactResp.WorkWxContactRespBuilder builder = WorkWxContactResp.builder()
                .friendId(relation.getPkId())
                .companyType(companyType)
                .accountType(relation.getAccountType())
                .wxAccount(relation.getWxAccount())
                .externalUserId(relation.getExternalUserId())
                .remark(relation.getRemark())
                .avatar(contact.getAvatar())
                .name(contact.getName())
                .unionId(contact.getUnionId());

        Integer userId = null;
        Integer wxId = null;
        if (ObjectUtils.isNotEmpty(unionId)) {
            UcWxExt wxExt = wxClient.getWxExt(companyType, unionId).orElse(null);
            wxId = wxExt != null ? wxExt.getId() : null;
            userId = wxExt != null ? wxExt.getUserId() : null;
            builder.wxId(wxId).userId(userId).userCode(DesensitizeUtil.idToMask(userId));
        }

        if (Objects.nonNull(userId)) {
            // 用户信息
            UcUsers user = userClient.getUser(userId).orElse(new UcUsers());
            builder.userRealName(user.getRealName());
            builder.userNumber(user.getNumber());
            //订单数
            UserStatisticsCount userOrderStatisticsCount = orderClient.getOrderCount(userId, OrderStatusConstant.PASSED.getValue()).orElse(new UserStatisticsCount(userId, 0L));
            UserStatisticsCount userRefundStatisticsCount = orderClient.getRefundOrderCount(userId, RefundOrderAuditStatusEnum.ORS_Passed.getValue()).orElse(new UserStatisticsCount(userId, 0L));
            builder.orderCount(userOrderStatisticsCount.getCount())
                    .refundCount(userRefundStatisticsCount.getCount());

            //评测信息
            SsCustomerEvaluation evaluation = contentClient.getUserEvaluation(userId).orElse(null);

            if (Objects.nonNull(evaluation)) {
                Integer surveyScore = evaluation.getSurveyScore();
                builder.riskLevel(evaluationUtil.formatEvaluationScore(surveyScore));
                builder.expireTime(evaluation.getExpireTime());

                if (Objects.equals(evaluation.getIdentityType(), ID_CARD)) {
                    String idCardNumber = AESUtil.decrypt(evaluation.getIdentityNumber());
                    try {
                        int idCardAge = IdcardUtil.getAgeByIdCard(idCardNumber);
                        String province = IdcardUtil.getProvinceByIdCard(idCardNumber);
                        int gender = IdcardUtil.getGenderByIdCard(idCardNumber);
                        builder.idCardAge(idCardAge)
                                .idCardRegion(province)
                                .gender(gender);
                    } catch (Exception e) {
                        log.error("身份证号码解析异常", e);
                    }
                }

                List<SsSurveyChoice> choiceList = contentClient.getEvaluationChoiceList(evaluation.getId(), 12, 4).orElse(emptyList());
                if (ObjectUtils.isNotEmpty(choiceList)) {
                    SsSurveyChoice choice = choiceList.get(0);
                    builder.investmentAmount(choice.getContent());
                }
            }

        }

        return builder.build();
    }

    public WorkWxContactResp getWorkWxContactDetail(Integer companyType, Integer accountType, String wxAccount, String externalUserId) {
        WorkWxFullContactUserRelation full = workwxClient.getFullFriendRelation(accountType, wxAccount, externalUserId)
                .orElseThrow();
        NpWorkWxContactUserRelation relation = full.getRelation();
        NpWorkWxContact contact = full.getContact();
        String unionId = contact.getUnionId();

        WorkWxContactResp.WorkWxContactRespBuilder builder = WorkWxContactResp.builder()
                .friendId(relation.getPkId())
                .companyType(companyType)
                .accountType(relation.getAccountType())
                .wxAccount(relation.getWxAccount())
                .externalUserId(relation.getExternalUserId())
                .remark(relation.getRemark())
                .avatar(contact.getAvatar())
                .name(contact.getName())
                .unionId(contact.getUnionId());

        Integer userId = null;
        Integer wxId = null;
        if (ObjectUtils.isNotEmpty(unionId)) {
            UcWxExt wxExt = wxClient.getWxExt(companyType, unionId).orElse(null);
            wxId = wxExt != null ? wxExt.getId() : null;
            userId = wxExt != null ? wxExt.getUserId() : null;
            builder.wxId(wxId).userId(userId).userCode(DesensitizeUtil.idToMask(userId));
        }

        if (Objects.nonNull(userId)) {
            // 用户信息
            UcUsers user = userClient.getUser(userId).orElse(new UcUsers());
            builder.userRealName(user.getRealName());
            builder.userNumber(user.getNumber());
            //订单数
            UserStatisticsCount userOrderStatisticsCount = orderClient.getOrderCount(userId, OrderStatusConstant.PASSED.getValue()).orElse(new UserStatisticsCount(userId, 0L));
            UserStatisticsCount userRefundStatisticsCount = orderClient.getRefundOrderCount(userId, RefundOrderAuditStatusEnum.ORS_Passed.getValue()).orElse(new UserStatisticsCount(userId, 0L));
            builder.orderCount(userOrderStatisticsCount.getCount())
                    .refundCount(userRefundStatisticsCount.getCount());

            //评测信息
            UcEvaluationInfo evaluation = userClient.getEvaluationInfo(userId).orElse(null);
            //认证信息
            UcVerifyInfo verifyInfo = userClient.getVerifyInfo(userId).orElse(null);

            if (Objects.nonNull(evaluation)) {
                builder.riskLevel(evaluation.getLevel());
                builder.expireTime(evaluation.getExpireTime());
                List<UcEvaluationChoice> choiceList = userClient.getEvaluationChoiceList(evaluation.getVersionId(), evaluation.getId(), 4).orElse(null);
                if (ObjectUtils.isNotEmpty(choiceList)) {
                    UcEvaluationChoice choice = choiceList.get(0);
                    builder.investmentAmount(choice.getContent());
                }
            }
            if (Objects.nonNull(verifyInfo)) {
                if (Objects.equals(verifyInfo.getIdentityType(), IdentityTypeEnum.ID_CARD.getCode())) {
                    String idCardNumber = AESUtil.decrypt(verifyInfo.getIdentityNumber());
                    try {
                        int idCardAge = IdcardUtil.getAgeByIdCard(idCardNumber);
                        String province = IdcardUtil.getProvinceByIdCard(idCardNumber);
                        int gender = IdcardUtil.getGenderByIdCard(idCardNumber);
                        builder.idCardAge(idCardAge)
                                .idCardRegion(province)
                                .gender(gender);
                    } catch (Exception e) {
                        log.error("身份证号码解析异常", ExceptionUtil.getCause(e));
                    }
                }
            }
        }
        return builder.build();
    }


    public WorkWxContactTagResp getContactTagList(Integer companyType, Integer accountType, String wxAccount, String externalUserId) {
        WorkWxFullContactUserRelation full = workwxClient.getFullFriendRelation(accountType, wxAccount, externalUserId).orElseThrow();
        NpWorkWxContact contact = full.getContact();
        String unionId = contact.getUnionId();

        UcWxExt wxExt = null;
        if (ObjectUtils.isNotEmpty(unionId)) {
            wxExt = wxClient.getWxExt(companyType, unionId).orElse(null);
        }

        List<NpWorkWxContactTag> contactTags = workwxClient.getWorkWxContactTagList(accountType, wxAccount, externalUserId).orElse(emptyList());
        List<String> relationTagList = contactTags.stream().map(NpWorkWxContactTag::getName).collect(Collectors.toList());
        List<UcCustomerTagGroup> tagGroups = userClient.getCustomerTagGroupList(companyType, null, null, null, true).orElse(emptyList());
        List<String> unionTagList = workwxClient.getUnionTagListBuUnionId(companyType, unionId).getData()
                .stream().map(NpWwxUnionTag::getName).collect(Collectors.toList());

        Map<Integer, List<UcCustomerTag>> groupTagMap;
        if (Objects.nonNull(wxExt) && Objects.nonNull(wxExt.getUserId())) {
            List<UcCustomerTag> customTags = userClient.getCustomerTagList(wxExt.getUserId()).orElse(emptyList());
            groupTagMap = customTags.stream().collect(Collectors.groupingBy(UcCustomerTag::getGroupId));
        } else {
            groupTagMap = new HashMap<>();
        }

        List<CustomerTagGroup> groupList = tagGroups.stream().map(e -> {
            Integer groupId = e.getId();
            String groupName = e.getName();
            List<CustomerTagItem> tagList = groupTagMap.getOrDefault(groupId, emptyList())
                    .stream()
                    .map(t -> CustomerTagItem.builder().id(t.getId()).name(t.getName()).build())
                    .collect(Collectors.toList());

            return CustomerTagGroup.builder()
                    .id(groupId)
                    .groupName(groupName)
                    .tagList(tagList)
                    .build();
        }).collect(Collectors.toList());

        return WorkWxContactTagResp.builder()
                .relationTagList(relationTagList)
                .customTagGroupList(groupList)
                .unionTagList(unionTagList)
                .build();
    }

    public PageResult<List<WwxUserResp>> getUsersByDept(Integer accountType, Integer departmentId, String searchText, Boolean deleted, Integer current, Integer size) {
        List<WwxUserResp> respList = new ArrayList<>();
        PageResult<List<NpWorkWxUser>> result = workwxClient.getUsersByDept(accountType, departmentId, searchText, deleted, current, size);
        if (ObjectUtil.isEmpty(result.getData())) {
            log.info("无数据");
            return PageResult.success(respList, null);
        }
        //获取企业微信部门信息
        Set<Integer> departIdSet = new HashSet<>();
        for (NpWorkWxUser user : result.getData()) {
            if (user.getDepartment() != null) {
                List<Integer> deptIds = JSON.parseArray(user.getDepartment(), Integer.class);
                departIdSet.addAll(deptIds);
            }
        }
        List<WwxDepartmentResp> departmentInfo = workwxClient.filterDepartments(accountType, AuthContextHolder.getCompanyType(), departIdSet);
        Map<Integer, String> wwxUserDeptMap = departmentInfo.stream().collect(Collectors.toMap(WwxDepartmentResp::getDeptId, WwxDepartmentResp::getName));

        respList = result.getData().stream().map(e -> {
            String deptNames = null;
            if (e.getDepartment() != null) {
                List<Integer> deptIds = JSON.parseArray(e.getDepartment(), Integer.class);
                deptNames = deptIds.stream().map(i -> wwxUserDeptMap.getOrDefault(i, "")).collect(Collectors.joining(","));
            }
            return WwxUserResp.builder()
                    .id(e.getId()).name(e.getName()).mobile(AESUtil.decrypt(e.getMobile())).inviteUrl(e.getInviteUrl())
                    .belongId(e.getBelongId()).status(e.getStatus()).userStatus(e.getStatusId())
                    .deptName(deptNames).accountType(e.getAccountType()).wxAccount(e.getWxAccount())
                    .deleted(e.getDeleted())
                    .build();
        }).collect(Collectors.toList());

        return PageResult.success(respList, result.getPagination());
    }

    public List<CustomerFollowUpRelationResp> getCustomerFollowUpBelong(Integer companyType, String unionId) {
        final UcWxExt wxExt = wxClient.getWxExt(companyType, unionId).orElseThrow();
        final Integer userId = wxExt.getUserId();

        if (Objects.isNull(userId)) {
            return null;
        }

        BaseResult<List<UcCustomerFollowUpRelation>> result = userClient.getRelationByUser(userId);

        if (!result.isPresent()) {
            return null;
        }

        List<UcCustomerFollowUpRelation> relationList = result.getData();
        Set<Integer> belongSet = relationList.stream().map(UcCustomerFollowUpRelation::getBelongId).collect(Collectors.toSet());
        Map<Integer, UcRole> roleMap = userClient.batchGetUserRoleMap(BatchReq.of(belongSet)).orElse(emptyMap());
        belongSet.add(userId);
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.of(belongSet)).orElse(emptyMap());

        UserInfo userInfo = UserInfo.of(userMap.get(userId));
        List<CustomerFollowUpRelationResp> respList = new ArrayList<>();

        for (UcCustomerFollowUpRelation relation : relationList) {
            CustomerFollowUpRelationResp.CustomerFollowUpRelationRespBuilder builder = CustomerFollowUpRelationResp.builder()
                    .id(relation.getId())
                    .companyType(relation.getCompanyType())
                    .gmtModified(relation.getGmtModified())
                    .gmtCreate(relation.getGmtCreate())
                    .userId(userId)
                    .userInfo(userInfo)
                    .belongId(relation.getBelongId())
                    .lastFollowUpTime(relation.getLastFollowUpTime())
                    .lastBelongTime(relation.getLastBelongTime());
            if (userMap.containsKey(relation.getBelongId())) {
                builder.belongInfo(UserInfo.of(userMap.get(relation.getBelongId())));
            }
            if (roleMap.containsKey(relation.getBelongId())) {
                builder.role(roleMap.get(relation.getBelongId()));
            }
            respList.add(builder.build());
        }

        return respList;
    }

    public BaseResult<UcCustomerFollowUpRelation> createCustomerFollowUpBelong(Integer companyType, Integer belongId, WorkWxCreateCustomerFollowUpBelongReq req) {
        String unionId = req.getUnionId();
        UcWxExt wxExt = wxClient.getWxExt(companyType, unionId).orElse(null);
        if (Objects.isNull(wxExt)) {
            throw new BusinessException("该用户未注册，无法认领");
        }

        Integer userId = wxExt.getUserId();

        if (Objects.isNull(userId)) {
            throw new BusinessException("该用户未注册，无法认领");
        }

        final List<VipSubscription> subscriptions = orderClient.getUserVipSubscriptionList(userId, null).orElse(emptyList());
        if (ObjectUtils.isEmpty(subscriptions)) {
            throw new BusinessException("该用户无服务，无法认领");
        }

        CreateCustomerFollowUpBelongReq createCustomerFollowUpBelongReq = CreateCustomerFollowUpBelongReq.builder()
                .companyType(companyType)
                .belongId(belongId)
                .userId(userId)
                .build();
        return userClient.createCustomerFollowUpBelong(createCustomerFollowUpBelongReq);
    }

    public BaseResult<Boolean> createCustomerFollowUp(CreateCustomerFollowUpReq req) {
        req.setUserId(StringUtils.isNotBlank(req.getUserCode()) ? DesensitizeUtil.maskToId(req.getUserCode()) : null);
        if (ObjectUtil.isNotEmpty(req.getUnionId())) {
            final String unionId = req.getUnionId();
            final UcWxExt wxExt = wxClient.getWxExt(req.getCompanyType(), unionId).orElseThrow();
            final Integer userId = wxExt.getUserId();

            if (Objects.isNull(userId)) {
                throw new BusinessException("该用户未注册，无法认领");
            }
            req.setUserId(userId);
        }
        BaseResult<Boolean> result = userClient.createCustomerFollowUp(req);
        if (result.isSuccess() && !result.getData()) {
            throw new BusinessException("跟进失败");
        }
        return result;
    }

    public PageResult<List<CustomerFollowUpRecordResp>> getCustomerFollowUpRecordPage(CustomerFollowUpRecordReq req) {
        req.setUserId(StringUtils.isNotBlank(req.getUserCode()) ? DesensitizeUtil.maskToId(req.getUserCode()) : null);
        if (ObjectUtil.isEmpty(req.getUserId())) {
            return PageResult.empty();
        }
        PageResult<List<UcCustomerFollowUpRecord>> pageResult = userClient.getCustomerFollowUpRecordPage(req);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<CustomerFollowUpRecordResp> recordRespList = new ArrayList<>();
        Set<Integer> followSet = pageResult.getData().stream().map(UcCustomerFollowUpRecord::getFollowId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(followSet)).orElse(emptyMap());
        Map<Integer, UcRole> roleMap = userClient.batchGetUserRoleMap(BatchReq.create(followSet)).orElse(emptyMap());
        for (UcCustomerFollowUpRecord record : pageResult.getData()) {
            CustomerFollowUpRecordResp.CustomerFollowUpRecordRespBuilder recordRespBuilder = CustomerFollowUpRecordResp.builder()
                    .record(CustomerFollowUpRecordInfo.of(record));
            if (ObjectUtil.isNotEmpty(record.getImages())) {
                recordRespBuilder.images(JSONObject.parseArray(record.getImages(), String.class));
            }

            if (userMap.containsKey(record.getFollowId())) {
                recordRespBuilder.userInfo(UserInfo.of(userMap.get(record.getFollowId())));
            }

            if (roleMap.containsKey(record.getFollowId())) {
                recordRespBuilder.operateRole(roleMap.get(record.getFollowId()).getName());
            }
            recordRespList.add(recordRespBuilder.build());
        }
        return PageResult.success(recordRespList, pageResult.getPagination());
    }

    public PageResult<List<CustomerFollowUpBelongRecordResp>> getCustomerFollowUpBelongRecordPage(CustomerFollowUpRecordReq req) {
        req.setUserId(StringUtils.isNotBlank(req.getUserCode()) ? DesensitizeUtil.maskToId(req.getUserCode()) : null);
        if (ObjectUtil.isEmpty(req.getUserId())) {
            return PageResult.empty();
        }
        PageResult<List<UcCustomerFollowUpBelongRecord>> pageResult = userClient.getCustomerFollowUpBelongRecordPage(req);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<CustomerFollowUpBelongRecordResp> recordRespList = new ArrayList<>();
        Set<Integer> belongSet = pageResult.getData().stream().map(UcCustomerFollowUpBelongRecord::getBelongId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(belongSet)).orElse(emptyMap());
        Map<Integer, UcRole> userRoleMap = userClient.batchGetUserRoleMap(BatchReq.create(belongSet)).orElse(emptyMap());
        for (UcCustomerFollowUpBelongRecord record : pageResult.getData()) {
            CustomerFollowUpBelongRecordResp.CustomerFollowUpBelongRecordRespBuilder recordRespBuilder = CustomerFollowUpBelongRecordResp.builder()
                    .record(CustomerFollowUpBelongRecordInfo.of(record));
            if (userMap.containsKey(record.getBelongId())) {
                recordRespBuilder.operateName(userMap.get(record.getBelongId()).getRealName());
            }
            if (userRoleMap.containsKey(record.getBelongId())) {
                recordRespBuilder.operateRole(userRoleMap.get(record.getBelongId()).getName());
            }
            recordRespList.add(recordRespBuilder.build());
        }
        return PageResult.success(recordRespList, pageResult.getPagination());
    }

    public PageResult<List<WwxUserResp>> getWxUserList(Integer companyType, WwxUserListReq req) {
        List<WwxUserResp> respList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(req.getSearchText())) {
            BaseResult<List<UcUsers>> userResult = userClient.batchSearchUserByName(req.getSearchText(), companyType);
            if (userResult.isFail()) {
                log.info("用户查询失败!");
                throw new BusinessException(userResult);
            }
            List<Integer> userIds = userResult.getData().stream().map(UcUsers::getId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(req.getBelongIdList())) {
                req.getBelongIdList().addAll(userIds);
            } else {
                req.setBelongIdList(userIds);
            }
        }
        PageResult<List<NpWorkWxUser>> result = workwxClient.filterWwxUser(req.getAccountType(), req.getWxAccountList(), req.getStatus(), req.getUserStatus(), req.getIsRelate(), req.getBelongIdList(), req.getSearchText(), req.getStartTime(), req.getEndTime(), req.getCurrent(), req.getSize());
        if (ObjectUtil.isEmpty(result.getData())) {
            log.info("无数据");
            return PageResult.success(respList, null);
        }
        Map<Long, NpWwxUserBlockFlow> blockFlowMap;
        final Set<Long> blockFlowSet = result.getData().stream().map(NpWorkWxUser::getBlockFlowId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(blockFlowSet)) {
            final BaseResult<List<NpWwxUserBlockFlow>> blockFlowResult = workwxClient.getWwxUserBlockFlowByIdList(blockFlowSet);
            if (blockFlowResult.isSuccess()) {
                blockFlowMap = blockFlowResult.getData().stream().collect(Collectors.toMap(NpWwxUserBlockFlow::getId, Function.identity()));
            } else {
                blockFlowMap = Collections.emptyMap();
            }
        } else {
            blockFlowMap = Collections.emptyMap();
        }
        Set<Integer> belongIds = result.getData().stream().map(NpWorkWxUser::getBelongId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(belongIds)).getData();
        //获取企业微信部门信息
        Set<Integer> departIdSet = new HashSet<>();
        for (NpWorkWxUser user : result.getData()) {
            if (user.getDepartment() != null) {
                List<Integer> deptIds = JSON.parseArray(user.getDepartment(), Integer.class);
                departIdSet.addAll(deptIds);
            }
        }
        List<WwxDepartmentResp> departmentInfo = workwxClient.filterDepartments(req.getAccountType(), companyType, departIdSet);
        Map<Integer, String> wwxUserDeptMap = departmentInfo.stream().collect(Collectors.toMap(WwxDepartmentResp::getDeptId, WwxDepartmentResp::getName));

        respList = result.getData().stream().map(e -> {
            String deptNames = null;
            if (e.getDepartment() != null) {
                List<Integer> deptIds = JSON.parseArray(e.getDepartment(), Integer.class);
                deptNames = deptIds.stream().map(i -> wwxUserDeptMap.getOrDefault(i, "")).collect(Collectors.joining(","));
            }
            return WwxUserResp.builder()
                    .id(e.getId()).name(e.getName()).mobile(AESUtil.decrypt(e.getMobile())).inviteUrl(e.getInviteUrl())
                    .belongId(e.getBelongId()).status(e.getStatus()).userStatus(e.getStatusId()).deleted(e.getDeleted())
                    .belongName(userMap.getOrDefault(e.getBelongId(), new UcUsers()).getRealName())
                    .enable(userMap.getOrDefault(e.getBelongId(), new UcUsers()).getEnabled())
                    .deptName(deptNames).wxAccount(e.getWxAccount()).accountType(e.getAccountType())
                    .leaveTime(e.getLeaveTime())
                    .isBlock(e.getIsBlock())
                    .blockType(blockFlowMap.getOrDefault(e.getBlockFlowId(), new NpWwxUserBlockFlow()).getBlockType())
                    .blockTime(blockFlowMap.getOrDefault(e.getBlockFlowId(), new NpWwxUserBlockFlow()).getBlockTime())
                    .duration(blockFlowMap.getOrDefault(e.getBlockFlowId(), new NpWwxUserBlockFlow()).getDuration())
                    .durationType(blockFlowMap.getOrDefault(e.getBlockFlowId(), new NpWwxUserBlockFlow()).getDurationType())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }

    public List<RelationRecordResp> getRelationRecord(Integer relationId) {
        List<ContactRelationRecordDto> records = workwxClient.getRelationRecord(relationId);
        Set<Long> dzIdSet = records.stream().map(t -> t.getRecord().getDzId()).collect(Collectors.toSet());
        Map<Long, NpDzAdChannel> dzAdChannelMap = workwxClient.getNpDzAdChannelByIdSet(dzIdSet).getData();
        List<RelationRecordResp> relationRecordResps = new ArrayList<>();
        for (ContactRelationRecordDto record : records) {
            RelationRecordResp relationRecordResp = new RelationRecordResp();
            relationRecordResp.setNpWorkWxContactRelationRecord(record.getRecord());
            if (ObjectUtil.isNotNull(record.getRecord().getDzId()) && dzAdChannelMap.containsKey(record.getRecord().getDzId())) {
                relationRecordResp.setNpDzAdChannel(dzAdChannelMap.get(record.getRecord().getDzId()));
            }
            if (ObjectUtil.isNotNull(record.getRecord().getState())) {
                if (ObjectUtil.equals(record.getRecord().getAddWay(), AddWayEnum.p_16.getCode())) {
                    if (ObjectUtil.isNotNull(record.getAcquisitionChannel())) {
                        relationRecordResp.setStateRemark(record.getAcquisitionChannel().getRemark());
                    }
                } else {
                    if (ObjectUtil.isNotNull(record.getContactWay())) {
                        relationRecordResp.setStateRemark(record.getContactWay().getRemark());
                    }
                }
            }
            relationRecordResps.add(relationRecordResp);
        }
        return relationRecordResps;
    }

    public List<WorkWxFullContactUserRelation> getUserRelation(String unionId) {
        List<WorkWxFullContactUserRelation> userRelation = workwxClient.getUserRelation(unionId);
        Map<Integer, WxCpProperties> cpPropertiesMap = workwxClient.getAccountConfig().getData();
        userRelation.forEach(e -> {
            if (cpPropertiesMap.containsKey(e.getRelation().getAccountType())) {
                e.setCorpName(cpPropertiesMap.get(e.getRelation().getAccountType()).getCorpName());
            }
            if (ObjectUtils.isNotEmpty(e.getWorkWxUser()) && ObjectUtils.isNotEmpty(e.getWorkWxUser().getMobile())) {
                NpWorkWxUser workWxUser = e.getWorkWxUser();
                workWxUser.setMobile(DesensitizedUtil.mobilePhone(workWxUser.getMobile()));
                e.setWorkWxUser(workWxUser);
            }
        });
        return userRelation;
    }

    public PageResult<List<WorkWxServiceCustomerResp>> getCustomerList(Integer salesId, Integer companyType, Long roleId, Integer accountType, String wxAccount, Boolean isBelong, String searchContent, Integer current, Integer size) {
        // 1. 先获取全部企业微信好友关系
        BaseResult<Map<String, NpWorkWxContactUserRelation>> result1 = workwxClient.getFriendRelationByUnionMap(salesId, companyType, accountType, wxAccount, null, BatchReq.of(emptyList()));
        if (result1.isFail()) {
            throw new BusinessException(result1);
        }
        if (ObjectUtil.isEmpty(result1.getData())) {
            return PageResult.empty();
        }
        // 2. 若搜索，获取搜索备注的好友关系
        Set<String> searchUnionIdSet = new HashSet<>();
        if (ObjectUtil.isNotEmpty(searchContent)) {
            BaseResult<Map<String, NpWorkWxContactUserRelation>> result2 = workwxClient.getFriendRelationByUnionMap(salesId, companyType, accountType, wxAccount, searchContent, BatchReq.of(emptyList()));
            if (result2.isFail()) {
                throw new BusinessException(result2);
            }
            searchUnionIdSet.addAll(result2.getData().keySet());
        }
        // 3. 根据所有好友关系，查询用户信息（按昵称搜索）
        Set<String> unionIdSet = result1.getData().keySet();
        BatchSearchUserByUnionIdAndContentReq unionIdAndContentReq = BatchSearchUserByUnionIdAndContentReq.builder()
                .searchContent(searchContent)
                .filterReq(BatchReq.of(searchUnionIdSet))
                .req(BatchReq.of(unionIdSet))
                .build();
        BaseResult<List<UcUsers>> result3 = userClient.batchSearchUserByUnionIdAndContent(unionIdAndContentReq);
        if (result3.isFail()) {
            throw new BusinessException(result3);
        }
        if (ObjectUtil.isEmpty(result3.getData())) {
            return PageResult.empty();
        }
        List<UcUsers> ucUsers = result3.getData();
        Map<String, NpWorkWxContactUserRelation> relationMap = result1.getData();
        Map<Integer, UcUsers> usersMap = ucUsers.stream().collect(Collectors.toMap(UcUsers::getId, t -> t));
        List<Integer> userIdList = ucUsers.stream().map(UcUsers::getId).collect(Collectors.toList());

        BaseResult<Map<Integer, String>> baseResult = userClient.batchSearchUnionIdMapByUserId(BatchReq.of(userIdList));
        if (baseResult.isFail()) {
            throw new BusinessException(baseResult);
        }
        if (ObjectUtil.isEmpty(baseResult.getData())) {
            return PageResult.empty();
        }
        Map<Integer, String> userUnionMap = baseResult.getData();
        if (isBelong) {
            // 已认领
            ServiceCustomerReq customerReq = ServiceCustomerReq.builder()
                    .saleId(salesId)
                    .companyType(companyType)
                    .build();
            customerReq.setUserList(userIdList.stream().filter(usersMap::containsKey).collect(Collectors.toList()));
            customerReq.setCurrent(current);
            customerReq.setSize(size);
            PageResult<List<ServiceCustomerDto>> pageResult = userClient.claimedServiceCustomerPage(customerReq);
            if (pageResult.isPresent()) {
                List<WorkWxServiceCustomerResp> resps = pageResult.getData().stream().map(e -> {
                    return WorkWxServiceCustomerResp.builder()
                            .id(e.getRelation().getId())
                            .userId(e.getRelation().getUserId())
                            .nickName(usersMap.get(e.getRelation().getUserId()).getNickName())
                            .avatarUrl(usersMap.get(e.getRelation().getUserId()).getAvatarUrl())
                            .remark(relationMap.get(userUnionMap.get(e.getRelation().getUserId())).getRemark())
                            .isClaimed(true)
                            .claimTime(e.getRelation().getLastBelongTime())
                            .tagList(e.getTag())
                            .externalUserId(relationMap.get(userUnionMap.get(e.getRelation().getUserId())).getExternalUserId())
                            .build();

                }).collect(Collectors.toList());
                return PageResult.success(resps, pageResult.getPagination());
            }
        } else {
            // 未认领
            // 已有归属的用户列表
            BaseResult<List<UcCustomerFollowUpRelation>> relationResult = userClient.getRelationByBelongId(salesId);
            if (relationResult.isFail()) {
                throw new BusinessException(relationResult);
            }
            List<UcCustomerFollowUpRelation> relationList = relationResult.getData() != null ? relationResult.getData() : Collections.emptyList();
            // 没有归属的用户列表
            List<Integer> customerList = relationList.stream().map(UcCustomerFollowUpRelation::getUserId).collect(Collectors.toList());
            List<Integer> customers = userIdList.stream().filter(usersMap::containsKey).filter(e -> !customerList.contains(e)).collect(Collectors.toList());
            if (ObjectUtil.isEmpty(customers)) {
                return PageResult.empty();
            }
            PageResult<List<Integer>> pageResult = orderClient.pageFilterUser(customers, null, null, current, size);
            if (pageResult.isFail()) {
                throw new BusinessException(pageResult);
            }
            if (ObjectUtil.isEmpty(pageResult.getData())) {
                return PageResult.empty();
            }

            Set<Integer> userSet = pageResult.getData().stream().collect(Collectors.toSet());
            BaseResult<List<CustomerTagDto>> tagListBaseResult = userClient.getCustomerTagList(userSet);
            if (tagListBaseResult.isFail()) {
                throw new BusinessException(tagListBaseResult);
            }
            Map<Integer, List<CustomerTagDto>> tagMap = tagListBaseResult.getData().stream().collect(Collectors.groupingBy(CustomerTagDto::getUserId));
            List<WorkWxServiceCustomerResp> resps = pageResult.getData().stream().filter(ObjectUtil::isNotEmpty).map(userId -> {
                return WorkWxServiceCustomerResp.builder()
                        .userId(userId)
                        .nickName(usersMap.getOrDefault(userId, new UcUsers()).getNickName())
                        .avatarUrl(usersMap.getOrDefault(userId, new UcUsers()).getAvatarUrl())
                        .remark(userUnionMap.get(userId) == null ? null : relationMap.getOrDefault(userUnionMap.get(userId), new NpWorkWxContactUserRelation()).getRemark())
                        .isClaimed(false)
                        .tagList(tagMap.getOrDefault(userId, new ArrayList<>()))
                        .externalUserId(userUnionMap.get(userId) == null ? null : relationMap.getOrDefault(userUnionMap.get(userId), new NpWorkWxContactUserRelation()).getExternalUserId())
                        .build();
            }).collect(Collectors.toList());
            return PageResult.success(resps, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<PortfolioFollowResp>> followList(Integer userId, Integer portfolioType, Integer channelType, Integer current, Integer size) {
        return tradeClient.followList(userId, portfolioType, channelType, current, size);
    }

    /**
     * 批量客户认领
     */
    public BaseResult<String> batchCreateCustomerFollowUpBelong(Integer companyType, Integer belongId, WorkWxBatchCreateCustomerFollowUpBelongReq req) {

        BatchCreateCustomerFollowUpBelongReq createCustomerFollowUpBelongReq = BatchCreateCustomerFollowUpBelongReq.builder()
                .companyType(companyType)
                .belongId(belongId)
                .userId(req.getUserIdList())
                .build();
        return userClient.batchCreateCustomerFollowUpBelong(createCustomerFollowUpBelongReq);
    }

    public PageResult<List<UcCustomerFollowUpRecordResp>> getBelongRecordByOperator(Integer operator, Integer current, Integer size) {
        PageResult<List<UcCustomerFollowUpBelongRecord>> pageResult = userClient.getBelongRecordByOperator(operator, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<UcCustomerFollowUpBelongRecord> records = pageResult.getData();
        List<Integer> userIdList = records.stream().map(UcCustomerFollowUpBelongRecord::getUserId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdList)).orElseThrow();
        List<UcCustomerFollowUpRecordResp> resps = records.stream().map(record -> {
            UcCustomerFollowUpRecordResp resp = new UcCustomerFollowUpRecordResp();
            BeanUtils.copyProperties(record, resp);
            resp.setOperator(operator);
            resp.setNickName(usersMap.get(record.getUserId()).getNickName());
            resp.setRemark(usersMap.get(record.getUserId()).getRemark());
            resp.setCreateTime(record.getGmtCreate());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<WorkWxContactUserRelationItem>> getFriendRelationListByUnionId(Integer companyType, Integer accountType, String unionId, Integer current, Integer size) {
        PageResult<List<WorkWxFullContactUserRelation>> pageResult = workwxClient.getFullFriendRelationList(companyType, accountType, unionId, "addTime", current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }

        List<WorkWxFullContactUserRelation> data = pageResult.getData();
        Pagination pagination = pageResult.getPagination();

        //get all dept
        Set<Integer> departmentIdSet = data.stream().map(e -> e.getWorkWxUser() != null ? e.getWorkWxUser().getDepartment() : null)
                .filter(Objects::nonNull)
                .map(e -> JSON.parseArray(e, Integer.class))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<String> tagSet = data.stream().map(e -> {
            String tagsString = e.getRelation().getTags();
            List<WorkWxContactRelationTag> relationTags = JSON.parseArray(tagsString, WorkWxContactRelationTag.class);
            return relationTags.stream().map(WorkWxContactRelationTag::getTagId).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toSet());

        List<WwxDepartmentResp> departmentInfo = workwxClient.filterDepartments(accountType, companyType, departmentIdSet);
        List<NpWorkWxContactTag> contactTags = workwxClient.getWorkWxContactTagList(companyType, accountType, BatchReq.of(tagSet)).orElse(emptyList());
        Map<Integer, String> wwxUserDeptMap = departmentInfo.stream().collect(Collectors.toMap(WwxDepartmentResp::getDeptId, WwxDepartmentResp::getName));
        Map<String, String> tagMap = contactTags.stream().collect(Collectors.toMap(NpWorkWxContactTag::getTagId, NpWorkWxContactTag::getName));

        List<WorkWxContactUserRelationItem> items = data.stream().map(e -> {
            NpWorkWxContactUserRelation relation = e.getRelation();
            NpWorkWxContact contact = e.getContact();
            NpWorkWxUser workWxUser = e.getWorkWxUser();

            String contactName = contact != null ? contact.getName() : null;
            String contactAvatar = contact != null ? contact.getAvatar() : null;
            List<Integer> deptIds = workWxUser != null ? JSON.parseArray(workWxUser.getDepartment(), Integer.class) : emptyList();
            List<String> deptName = deptIds.stream()
                    .map(wwxUserDeptMap::get)
                    .collect(Collectors.toList());
            String wxAccountName = workWxUser != null ? workWxUser.getName() : null;
            String wxAccountAlias = workWxUser != null ? workWxUser.getAlias() : null;

            List<String> tagList = new ArrayList<>();
            String tagString = relation.getTags();
            if (ObjectUtils.isNotEmpty(tagString)) {
                List<WorkWxContactRelationTag> relationTags = JSON.parseArray(tagString, WorkWxContactRelationTag.class);
                tagList = relationTags.stream().map(x -> tagMap.get(x.getTagId()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }

            return WorkWxContactUserRelationItem.builder()
                    .id(relation.getPkId())
                    .externalUserId(relation.getExternalUserId())
                    .wxAccount(relation.getWxAccount())
                    .accountType(relation.getAccountType())
                    .companyType(relation.getCompanyType())
                    .createTime(relation.getCreateTime())
                    .addTime(relation.getAddTime())
                    .remark(relation.getRemark())
                    .description(relation.getDescription())
                    .tags(tagList)
                    .state(relation.getState())
                    .addWay(relation.getAddWay())
                    .enabled(relation.getEnabled())
                    .contactEnabled(relation.getContactEnabled())
                    .userEnabled(relation.getUserEnabled())
                    .lastReplyTime(relation.getLastReplyTime())
                    .source(relation.getSource())
                    .wechatChannels(relation.getWechatChannels())
                    .contactName(contactName)
                    .contactAvatar(contactAvatar)
                    .deptName(deptName)
                    .wxAccountName(wxAccountName)
                    .wxAccountAlias(wxAccountAlias)
                    .build();
        }).collect(Collectors.toList());

        return PageResult.success(items, pagination);
    }

    public BaseResult<WayAndAcquisitionResp> getWayAndAcquisitionByWxAccount(Integer accountType, String wxAccount, Boolean enabled) {
        WayAndAcquisitionResp resp = new WayAndAcquisitionResp();
        Boolean isOnLine = false;
        BaseResult<List<NpWwxCustomerAcquisition>> acquisitionResult = workwxClient.getCustomerAcquisitionByWxAccount(accountType, wxAccount, enabled);
        if (acquisitionResult.isFail()) {
            throw new BusinessException(acquisitionResult);
        }
        BaseResult<List<NpWorkWxContactWay>> wayResult = workwxClient.getContactWayByWxAccount(accountType, wxAccount, enabled);
        if (wayResult.isFail()) {
            throw new BusinessException(wayResult);
        }
        if (ObjectUtils.isNotEmpty(acquisitionResult.getData())) {
            List<Long> acquisitionIdList = acquisitionResult.getData().stream().map(NpWwxCustomerAcquisition::getId).collect(Collectors.toList());
            resp.setLinkIdList(acquisitionIdList);
            isOnLine = true;
        }
        if (ObjectUtils.isNotEmpty(wayResult.getData())) {
            List<Integer> wayIdList = wayResult.getData().stream().map(NpWorkWxContactWay::getId).collect(Collectors.toList());
            resp.setWayIdList(wayIdList);
            isOnLine = true;
        }
        resp.setIsOnLine(isOnLine);
        return BaseResult.success(resp);
    }

    public List<WorkWxServiceCustomerResp> getCustomers(Integer salesId, Integer companyType, Integer accountType, String wxAccount, Boolean isBelong, String searchContent) {
        List<WorkWxCustomerResp> customerResps = workwxClient.getCustomerList(salesId, isBelong).getData();
        List<Integer> userIdList = customerResps.stream().map(WorkWxCustomerResp::getUserId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(userIdList)) {
            return emptyList();
        }
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdList)).getData();
        Map<Integer, String> userUnionMap = customerResps.stream()
                .filter(item -> ObjectUtil.isNotEmpty(item.getUserId()) && ObjectUtil.isNotEmpty(item.getUnionId()))
                .collect(Collectors.toMap(WorkWxCustomerResp::getUserId, WorkWxCustomerResp::getUnionId));
        List<CustomerTagDto> customerTagDtos = userClient.getCustomerTagList(Sets.newHashSet(userIdList)).getData();
        Map<Integer, List<CustomerTagDto>> tagMap = customerTagDtos.stream().collect(Collectors.groupingBy(CustomerTagDto::getUserId));
        Map<String, NpWorkWxContactUserRelation> relationMap = workwxClient.getFriendRelationByUnionMap(salesId, companyType, accountType, wxAccount, null, BatchReq.of(emptyList())).getData();
        List<WorkWxServiceCustomerResp> workWxServiceCustomerResps = new ArrayList<>();
        for (Integer userId : userIdList) {
            WorkWxServiceCustomerResp customerResp = WorkWxServiceCustomerResp.builder()
                    .userId(userId)
                    .nickName(usersMap.getOrDefault(userId, new UcUsers()).getNickName())
                    .avatarUrl(usersMap.getOrDefault(userId, new UcUsers()).getAvatarUrl())
                    .remark(userUnionMap.get(userId) == null ? null : relationMap.getOrDefault(userUnionMap.get(userId), new NpWorkWxContactUserRelation()).getRemark())
                    .isClaimed(isBelong)
                    .tagList(tagMap.getOrDefault(userId, new ArrayList<>()))
                    .externalUserId(userUnionMap.get(userId) == null ? null : relationMap.getOrDefault(userUnionMap.get(userId), new NpWorkWxContactUserRelation()).getExternalUserId())
                    .build();
            workWxServiceCustomerResps.add(customerResp);
        }
        return workWxServiceCustomerResps;
    }

    public BaseResult<List<WorkWxUserBaseInfo>> getBelongAccountBase(Integer salesId, Boolean deleted, Boolean enabled, Integer companyType, Integer accountType, Integer status) {
        final BaseResult<List<NpWorkWxUser>> result = workwxClient.getBelongAccount(salesId, deleted, enabled, companyType, accountType, status, null);
        final BaseResult<List<WwxConfig>> configResult = workwxClient.getConfigList(companyType);
        Map<Integer, WwxConfig> configMap;
        if (result.isFail()) {
            log.error("获取企业微信账号失败, {}", result);
            return BaseResult.success(emptyList());
        }

        if (configResult.isFail()) {
            log.error("获取企业微信配置失败: {}", configResult);
            configMap = Collections.emptyMap();
        } else {
            configMap = configResult.getData().stream().collect(Collectors.toMap(WwxConfig::getAccountType, Function.identity()));
        }

        final List<NpWorkWxUser> data = result.getData();
        List<WorkWxUserBaseInfo> resps = data.stream().map(item -> {
            return WorkWxUserBaseInfo.builder()
                    .accountType(item.getAccountType())
                    .cropName(configMap.getOrDefault(item.getAccountType(), new WwxConfig()).getCorpName())
                    .wxAccount(item.getWxAccount())
                    .name(item.getName())
                    .build();
        }).collect(Collectors.toList());

        return BaseResult.success(resps);
    }

    public BaseResult<List<WorkWxFriendResp>> contactUserRelationCustomerList(Integer userId) {
        cn.shrise.radium.userservice.entity.UcWxExt wxExt = userClient.getWxExtInfo(userId).getData();
        //用unionId去uc_wx_account表中查询accountType
        UcWxAccount account = userClient.findAccountInfoByUnionId(wxExt.getUnionId()).getData();
        return workwxClient.contactUserRelationCustomerList(wxExt.getUnionId(), account.getAccountType());
    }

    public PageResult<List<WorkWxGroupResp>> contactUserRelationGroupList(Integer userId, Integer current, Integer size) {
        return null;
    }
}

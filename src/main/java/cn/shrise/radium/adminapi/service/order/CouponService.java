package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.resp.CouponResp;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Service
@RequiredArgsConstructor
public class CouponService {

    private final OrderClient orderClient;
    private final CommonService commonService;

    public PageResult<List<CouponResp>> getCouponRespList(Integer companyType, Integer category, Integer planId, LocalDate createStartTime,
                                                          LocalDate createEndTime, String userNumber, String userCode, Integer current, Integer size) {
        Integer userId = StringUtils.isNotBlank(userCode) ? DesensitizeUtil.maskToId(userCode) : null;
        if (ObjectUtil.isNotNull(userNumber)) {
            userId = commonService.getUserIdByNumber(companyType, userNumber);
        }
        PageResult<List<CouponResp>> couponRespList = orderClient.getCouponRespList(companyType, category, null, planId, createStartTime, createEndTime, userId, current, size);
        return getCouponListPageResult(couponRespList);
    }

    public PageResult<List<CouponResp>> getCouponRespListInfo(Integer companyType, Integer category, Integer salesId, Integer planId, LocalDate createStartTime,
                                                       LocalDate createEndTime, String userCode, Integer current, Integer size) {
        Integer userId = StringUtils.isNotBlank(userCode) ? DesensitizeUtil.maskToId(userCode) : null;
        PageResult<List<CouponResp>> couponRespList = orderClient.getCouponRespList(companyType, category, salesId, planId, createStartTime, createEndTime, userId, current, size);
        return getCouponListPageResult(couponRespList);
    }

    private PageResult<List<CouponResp>> getCouponListPageResult(PageResult<List<CouponResp>> couponRespList) {
        if (ObjectUtil.isEmpty(couponRespList.getData())) {
            return PageResult.empty();
        }
        List<CouponResp> couponResp = couponRespList.getData().stream().filter(Objects::nonNull).peek(o -> {
            o.setUserCode(DesensitizeUtil.idToMask(o.getUserId()));
        }).collect(Collectors.toList());
        return PageResult.success(couponResp, couponRespList.getPagination());
    }
}

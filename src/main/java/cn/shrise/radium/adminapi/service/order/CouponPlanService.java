package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.NpWxAuthLinkResp;
import cn.shrise.radium.adminapi.resp.order.CouponPlanListResp;
import cn.shrise.radium.adminapi.resp.order.CouponPlanResp;
import cn.shrise.radium.adminapi.resp.order.SalesResp;
import cn.shrise.radium.adminapi.resp.order.SkuResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxSalesInviteUrl;
import cn.shrise.radium.marketingservice.enums.InviteLinkType;
import cn.shrise.radium.marketingservice.req.CreateWxSalesInviteUrlReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.CouponPlanStatusEnum;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.req.CreateCouponPlanReq;
import cn.shrise.radium.orderservice.req.UpdateCouponPlanReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.constant.WxAuthorizerCategoryConstant;
import cn.shrise.radium.wxservice.properties.WxMpProperties;
import io.swagger.models.auth.In;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CouponPlanService {

    private final MarketingClient marketingClient;
    private final OrderClient orderClient;
    private final UserClient userClient;
    private final WxClient wxClient;
    private final CommonProperties commonProperties;

    public PageResult<List<CouponPlanResp>> getPromotionUrlList(Integer companyType, Integer salesId, Integer current,
                                                                Integer size) {
        List<WxMpProperties> authorizerConfig = wxClient.getAuthorizerConfig(companyType, WxAuthorizerCategoryConstant.PRODUCT)
                .orElseThrow();
        if (ObjectUtils.isEmpty(authorizerConfig)) {
            throw new RecordNotExistedException("找不到当前公司的产品授权号");
        }
        WxMpProperties wxMpProperties = authorizerConfig.get(0);
        String prefix = "activity";
        UcUsers sales = userClient.getUser(salesId).orElseThrow();
        List<UcDepartment> departments = userClient.getDepartmentFullPath(salesId, null).orElseThrow();
        List<Integer> deptIds = departments.stream().map(UcDepartment::getId).collect(Collectors.toList());
        PageResult<List<CourseCouponPlan>> recordsRes = orderClient.getCouponPlanBySalesIdOrDeptIds(salesId, deptIds, true,
                CouponPlanStatusEnum.START.getCode(), current, size);
        if (recordsRes.isFail()) {
            throw new BusinessException(recordsRes);
        }
        List<CourseCouponPlan> courseCouponPlans = recordsRes.getData();
        List<CouponPlanResp> respList = new ArrayList<>();
        if (courseCouponPlans.size() > 0 && ObjectUtil.isNotNull(sales.getNumber())) {
            List<String> urlList = courseCouponPlans.stream()
                    .map(e -> String.format("%s/%s/%s", prefix, e.getNumber(), sales.getNumber()))
                    .collect(Collectors.toList());
            Map<String, NpWxSalesInviteUrl> inviteMap = marketingClient.getInviteUrlBySalesId(salesId, null, urlList)
                    .orElse(Collections.emptyList()).stream()
                    .filter(e -> ObjectUtil.isNotNull(e.getLinkUrl()))
                    .collect(Collectors.toMap(NpWxSalesInviteUrl::getLinkUrl, x -> x));
            courseCouponPlans.forEach(e -> {
                CouponPlanResp resp = CouponPlanResp.builder()
                        .name(e.getName())
                        .detail(e.getDetail())
                        .build();
                String number;
                String key = String.format("%s/%s/%s", prefix, e.getNumber(), sales.getNumber());
                NpWxSalesInviteUrl invite = inviteMap.get(key);
                if (ObjectUtil.isNull(invite)) {
                    CreateWxSalesInviteUrlReq req = CreateWxSalesInviteUrlReq.builder()
                            .companyType(companyType)
                            .accountType(wxMpProperties.getAccountType())
                            .salesId(salesId)
                            .linkUrl(key)
                            .linkType(InviteLinkType.ILT_Promotion.getValue())
                            .build();
                    NpWxSalesInviteUrl save = marketingClient.createInviteUrl(req).orElseThrow();
                    number = save.getNumber();
                } else {
                    number = invite.getNumber();
                }
                resp.setUrl(String.format("%s/p/%s", commonProperties.getShortUrl(), number));
                respList.add(resp);
            });
        }
        return PageResult.success(respList, Pagination.of(current, size, recordsRes.getPagination().getTotal()));
    }

    public PageResult<List<CouponPlanListResp>> getCouponsPlanList(Boolean enabled, Integer status, Integer current, Integer size) {

        List<CouponPlanListResp> resps = new ArrayList<>();

        PageResult<List<CourseCouponPlan>> couponsPlanListResult = orderClient.getCouponsPlanList(enabled, status, current, size);
        if (couponsPlanListResult.isFail()) {
            log.info("获取优惠券活动列表失败！");
            throw new BusinessException(couponsPlanListResult);
        }
        List<CourseCouponPlan> couponPlans = couponsPlanListResult.getData() == null ? Collections.emptyList() : couponsPlanListResult.getData();
        if (ObjectUtil.isEmpty(couponPlans)) {
            return PageResult.success(resps, couponsPlanListResult.getPagination());
        }

        resps = couponPlans.stream().map(e -> {
            CouponPlanListResp resp = CouponPlanListResp.builder()
                    .id(e.getId())
                    .number(e.getNumber())
                    .name(e.getName())
                    .status(e.getStatus())
                    .enabled(e.getEnabled())
                    .createTime(e.getCreateTime())
                    .minusAmount(e.getMinusAmount())
                    .detail(e.getDetail())
                    .days(e.getDays())
                    .startTime(e.getStartTime())
                    .type(e.getType())
                    .discountRate(e.getDiscountRate())
                    .build();
            if (ObjectUtil.isNotEmpty(e.getStartTime())){
                Instant endTime = e.getStartTime().plus(resp.getDays(), ChronoUnit.DAYS).minus(1, ChronoUnit.SECONDS);
                resp.setEndTime(endTime);
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, couponsPlanListResult.getPagination());
    }

    public List<SkuResp> getSkuByPlanId(Integer planId) {
        BaseResult<List<RsSku>> skuResult = orderClient.getSkuByPlanId(planId);
        if (skuResult.isFail()) {
            throw new BusinessException(skuResult);
        }
        if (!skuResult.isPresent()) {
            return Collections.emptyList();
        }
        List<RsSku> skuList = skuResult.getData();
        return SkuResp.batchOf(skuList);
    }

    public List<SalesResp> getSalesListByPlanId(Integer planId) {

        BaseResult<List<CourseCouponSalesRelation>> salesRelationResult = orderClient.getCouponPlanSalesRelation(planId);
        if (salesRelationResult.isFail()){
            throw new BusinessException(salesRelationResult);
        }
        if (!salesRelationResult.isPresent()){
            return Collections.emptyList();
        }
        List<CourseCouponSalesRelation> relations = salesRelationResult.getData();
        List<SalesResp> resps = relations.stream().map(e -> {
            return SalesResp.builder()
                    .saleId(e.getUserId())
                    .build();
        }).collect(Collectors.toList());
        return resps;
    }

    public CouponPlanListResp getCouponsPlanInfo(Integer planId) {
        BaseResult<CourseCouponPlan> baseResult = orderClient.getCouponPlan(planId);
        if (baseResult.isFail()){
            throw new BusinessException(baseResult);
        }
        CourseCouponPlan plan = baseResult.getData();
        BaseResult<List<CourseCouponSalesRelation>> salesRelationResult = orderClient.getCouponPlanSalesRelation(planId);
        if (salesRelationResult.isFail()){
            throw new BusinessException(salesRelationResult);
        }
        List<Integer> userIds = salesRelationResult.getData().stream().map(CourseCouponSalesRelation::getUserId).collect(Collectors.toList());

        BaseResult<List<CourseCouponDeptRelation>> deptRelationResult = orderClient.getCouponPlanDeptRelation(planId, null);
        if (deptRelationResult.isFail()){
            throw new BusinessException(deptRelationResult);
        }
        List<Integer> deptIds = deptRelationResult.getData().stream().map(CourseCouponDeptRelation::getDepartmentId).collect(Collectors.toList());
        Instant endTime = null;
        if (ObjectUtil.isNotEmpty(plan.getStartTime())){
            endTime = plan.getStartTime().plus(1, ChronoUnit.DAYS).minus(1, ChronoUnit.SECONDS);
        }
        return CouponPlanListResp.builder()
                .id(plan.getId())
                .number(plan.getNumber())
                .name(plan.getName())
                .status(plan.getStatus())
                .enabled(plan.getEnabled())
                .createTime(plan.getCreateTime())
                .minusAmount(plan.getMinusAmount())
                .userIds(userIds)
                .deptIds(deptIds)
                .detail(plan.getDetail())
                .days(plan.getDays())
                .startTime(plan.getStartTime())
                .endTime(endTime)
                .build();
    }

    public BaseResult<String> createCouponPlan(CreateCouponPlanReq req) {
        if (ObjectUtil.isEmpty(req.getDays()) && ObjectUtil.isEmpty(req.getStartTime()) && ObjectUtil.isEmpty(req.getEndTime())){
            throw new BusinessException("活动有效期不能为空");
        }
        if (ObjectUtil.isEmpty(req.getDiscountRate()) && ObjectUtil.isEmpty(req.getMinusAmount())) {
            throw new BusinessException("优惠金额和折扣率不能都为空");
        }
        return orderClient.createCouponPlan(req);
    }

    public BaseResult<String> updateCouponPlan(UpdateCouponPlanReq req) {
        if (ObjectUtil.isEmpty(req.getDays()) && ObjectUtil.isEmpty(req.getStartTime()) && ObjectUtil.isEmpty(req.getEndTime())){
            throw new BusinessException("活动有效期不能为空");
        }
        return orderClient.updateCouponPlan(req);
    }
}

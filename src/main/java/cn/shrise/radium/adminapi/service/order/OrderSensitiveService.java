package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.complianceservice.ComplianceClient;
import cn.shrise.radium.complianceservice.entity.RsOrderSensitiveRecord;
import cn.shrise.radium.complianceservice.resp.OrderSensitiveCheckResp;
import cn.shrise.radium.complianceservice.resp.OrderSensitiveRecordResp;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhangjianwu
 * @created : 2025/5/29, 星期四
 **/

@Service
@Slf4j
@RequiredArgsConstructor
public class OrderSensitiveService {

    private final ComplianceClient complianceClient;
    private final OrderClient orderClient;
    private final UserClient userClient;

    public PageResult<List<OrderSensitiveRecordResp>> orderSensitiveRecordList(LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        PageResult<List<RsOrderSensitiveRecord>> pageResult = complianceClient.orderSensitiveRecordList(startTime, endTime, current, size);
        if (ObjectUtil.isEmpty(pageResult) || ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty(current, size);
        }
        List<RsOrderSensitiveRecord> sensitiveRecords = pageResult.getData();
        List<Integer> userIdList = sensitiveRecords.stream().map(RsOrderSensitiveRecord::getUserId).collect(Collectors.toList());
        List<Integer> orderIdList = sensitiveRecords.stream().map(RsOrderSensitiveRecord::getOrderId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(new BatchReq<>(userIdList)).getData();
        List<RsCourseOrder> orderList = orderClient.batchGetOrderListById(orderIdList).getData();
        Map<Integer, RsCourseOrder> orderMap = orderList.stream().collect(Collectors.toMap(RsCourseOrder::getId, Function.identity()));

        List<OrderSensitiveRecordResp> recordRespList = sensitiveRecords.stream().map(sensitiveRecord ->
                OrderSensitiveRecordResp.builder()
                        .id(sensitiveRecord.getId())
                        .userName(usersMap.getOrDefault(sensitiveRecord.getUserId(), new UcUsers()).getRealName())
                        .reason(sensitiveRecord.getReason())
                        .orderNumber(orderMap.getOrDefault(sensitiveRecord.getOrderId(), new RsCourseOrder()).getOrderNumber())
                        .gmtCreate(sensitiveRecord.getGmtCreate())
                        .build()).collect(Collectors.toList());
        return PageResult.success(recordRespList, pageResult.getPagination());
    }

    public List<OrderSensitiveCheckResp> orderSensitiveCheck(Integer userId, List<String> orderNumberList) {
        List<RsCourseOrder> orderList = orderClient.getOrderList(orderNumberList).getData();
        if (ObjectUtil.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        Map<Integer, String> orderIdToNumberMap = orderList.stream()
                .collect(Collectors.toMap(RsCourseOrder::getId, RsCourseOrder::getOrderNumber));
        List<OrderSensitiveCheckResp> orderSensitiveCheckRespList = complianceClient.orderSensitiveCheck(userId, orderIdToNumberMap.keySet()).getData();
        for (OrderSensitiveCheckResp orderSensitiveCheckResp : orderSensitiveCheckRespList) {
            orderSensitiveCheckResp.setOrderNumber(orderIdToNumberMap.get(orderSensitiveCheckResp.getOrderId()));
        }
        return orderSensitiveCheckRespList;
    }

}

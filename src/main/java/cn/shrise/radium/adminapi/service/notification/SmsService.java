package cn.shrise.radium.adminapi.service.notification;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.req.LoginVerificationCodeReq;
import cn.shrise.radium.adminapi.resp.LoginVerificationCodeResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.ImageCode;
import cn.shrise.radium.common.entity.MainCompany;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.CompanyProperties;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.ImageCodeUtil;
import cn.shrise.radium.notificationservice.NotificationClient;
import cn.shrise.radium.notificationservice.req.SendVerificationCodeReq;
import cn.shrise.radium.notificationservice.resp.SendSmsResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.ValidateCodePurposeType;
import cn.shrise.radium.userservice.entity.UcMainStaff;
import cn.shrise.radium.userservice.req.ImageCodeReq;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static cn.shrise.radium.notificationservice.constant.SmsSupplier.MAS;
import static cn.shrise.radium.notificationservice.constant.SmsSupplier.TENCENT_CLOUD;

@Service
@RequiredArgsConstructor
public class SmsService {

    private final NotificationClient notificationClient;
    private final ImageCodeUtil imageCodeUtil;
    private final UserClient userClient;
    private final CompanyProperties companyProperties;

    public LoginVerificationCodeResp sendVerificationCode(LoginVerificationCodeReq req) {
        Integer companyType = req.getCompanyType();
        MainCompany mainCompany = companyProperties.getMainCompanyFromCompanyType(companyType)
                .orElseThrow(RecordNotExistedException::new);
        String mobile = req.getMobile();
        if (ObjectUtil.isEmpty(mobile)) {
            UcMainStaff staff = userClient.getStaff(mainCompany.getCompanyType(), req.getUserName(), null, null)
                    .orElseThrow(RecordNotExistedException::new);
            mobile = AESUtil.decrypt(staff.getMobile());
        } else {
            BaseResult<UcMainStaff> staffRes = userClient.getStaff(mainCompany.getCompanyType(), mobile, null);
            if (staffRes.isFail() || ObjectUtil.isNull(staffRes.getData())) {
                throw new BusinessException("无效手机号，请联系运维！");
            }
        }

        ImageCodeReq imageCodeReq = ImageCodeReq.builder()
                .codeKey(req.getCodeKey())
                .code(req.getCode())
                .companyType(companyType)
                .purpose(ValidateCodePurposeType.LOGIN_OR_REGISTER)
                .mobile(mobile)
                .build();
        String smsCode = userClient.verify(imageCodeReq).orElseThrow();
        SendVerificationCodeReq sendVerificationCodeReq = SendVerificationCodeReq.builder()
                .mobile(mobile)
                .code(smsCode)
                .supplier(MAS)
                .build();
        SendSmsResp sendSmsResp = notificationClient.sendVerificationCode(sendVerificationCodeReq).orElseThrow();
        return LoginVerificationCodeResp.builder()
                .mobile(DesensitizedUtil.mobilePhone(mobile))
                .requestId(sendSmsResp.getRequestId())
                .requestContext(sendSmsResp.getRequestContext())
                .batchId(sendSmsResp.getBatchId())
                .build();
    }

    public ImageCode getSmsCertification() {
        return imageCodeUtil.generateCode(4);
    }
}

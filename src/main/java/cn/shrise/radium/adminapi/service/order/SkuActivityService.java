package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.order.SkuActivityMaterialResp;
import cn.shrise.radium.adminapi.resp.order.SkuActivityResp;
import cn.shrise.radium.adminapi.resp.order.SkuResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.dto.SkuActivityDto;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.orderservice.entity.RsSkuActivity;
import cn.shrise.radium.orderservice.req.CreateSkuActivityReq;
import cn.shrise.radium.orderservice.req.UpdateSkuActivityReq;
import cn.shrise.radium.orderservice.resp.SkuMaterialItem;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static cn.shrise.radium.adminapi.constant.ErrorConstant.RECORD_NOT_FOUND;
import static cn.shrise.radium.orderservice.constant.SkuMaterialPositionConstant.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SkuActivityService {

    private final OrderClient orderClient;

    public PageResult<List<SkuActivityResp>> getPage(Integer status, Integer companyType, Integer current, Integer size) {
        if (ObjectUtil.isEmpty(companyType)) {
            throw new BusinessException("公司类型不能为空");
        }

        PageResult<List<SkuActivityDto>> result = orderClient.getSkuActivityPage(status, companyType, current, size);
        if (result.isPresent()) {
            List<SkuActivityResp> respList = new ArrayList<>();
            for (SkuActivityDto dto : result.getData()) {
                SkuActivityResp skuActivityResp = SkuActivityResp.of(dto.getRsSkuActivity());
                skuActivityResp.setSkuResp(SkuResp.of(dto.getRsSku()));
                respList.add(skuActivityResp);
            }
            return PageResult.success(respList, result.getPagination());
        }
        return PageResult.empty();
    }

    public BaseResult<RsSkuActivity> createSkuActivity(CreateSkuActivityReq req) {

        if(LocalDateTime.now().isAfter(req.getPreheatTime())){
            throw new BusinessException("预热时间不能小于当前时间");
        }

        if(req.getPreheatTime().isAfter(req.getStartTime())){
            throw new BusinessException("开始时间不能小于预热时间");
        }

        if(req.getStartTime().isAfter(req.getEndTime())){
            throw new BusinessException("结束时间不能小于开始时间");
        }
        return orderClient.createSkuActivity(req);
    }

    public BaseResult<Boolean> updateSkuActivity(UpdateSkuActivityReq req) {
        return orderClient.updateSkuActivity(req);
    }

    public BaseResult<Void> cancelActivity(Integer activityId) {
        return orderClient.cancelActivity(activityId);
    }

    public SkuActivityMaterialResp getSkuActivity(Integer activityId) {
        BaseResult<RsSkuActivity> result = orderClient.findById(activityId);
        if (result.isPresent()) {
            BaseResult<List<SkuMaterialItem>> baseResult = orderClient.getSkuActivityMaterialList(activityId, null, null, true);
            SkuActivityMaterialResp.SkuActivityMaterialRespBuilder builder = SkuActivityMaterialResp.builder()
                    .activity(result.getData());
            BaseResult<RsSku> skuResult = orderClient.getSku(result.getData().getSkuId());
            if (skuResult.isPresent()) {
                builder.skuNumber(skuResult.getData().getNumber())
                        .name(skuResult.getData().getName());
            }
            if (baseResult.isPresent()) {
                List<SkuMaterialItem> carouselList = new ArrayList<>();
                for (SkuMaterialItem skuMaterialItem : baseResult.getData()) {
                    if (skuMaterialItem.getPosition().equals(POSITION_COVER)) {
                        builder.mainPosition(skuMaterialItem);
                    } else if (skuMaterialItem.getPosition().equals(POSITION_CAROUSEL)) {
                        carouselList.add(skuMaterialItem);
                    } else if (skuMaterialItem.getPosition().equals(POSITION_DESCRIPTION)) {
                        builder.descriptionPosition(skuMaterialItem);
                    }
                }
                builder.carouselPosition(carouselList);
            }
            return builder
                    .build();
        }
        throw new BusinessException(RECORD_NOT_FOUND);
    }

}

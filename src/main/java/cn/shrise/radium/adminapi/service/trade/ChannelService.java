package cn.shrise.radium.adminapi.service.trade;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdPortfolioChannel;
import cn.shrise.radium.tradeservice.resp.TdPortfolioChannelResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelService {

    private final TradeClient tradeClient;

    public List<TdPortfolioChannelResp> getPortfolioChannels(Integer companyType, Integer channelType) {
        BaseResult<List<TdPortfolioChannel>> result = tradeClient.getPortfolioChannels(companyType, channelType);
        if (result.isPresent()) {
            return result.getData().stream().map(TdPortfolioChannelResp::of).collect(Collectors.toList());
        }
        return null;
    }
}

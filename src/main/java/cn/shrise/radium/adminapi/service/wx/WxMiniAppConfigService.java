package cn.shrise.radium.adminapi.service.wx;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.properties.WxMaProperties;
import cn.shrise.radium.wxservice.resp.WxMiniAppPropertiesResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: tangjiajun
 * @Date: 2025/3/6 15:01
 * @Desc:
 **/

@Service
@RequiredArgsConstructor
public class WxMiniAppConfigService {

    private final WxClient wxClient;

    public BaseResult<List<WxMiniAppPropertiesResp>> getWxMiniAppConfig(Integer companyType) {
        BaseResult<List<WxMaProperties>> wxMiniAppConfigs = wxClient.getWxMiniAppConfig(companyType);
        if (ObjectUtil.isEmpty(wxMiniAppConfigs)){
            return BaseResult.success(new ArrayList<>());
        }

        List<WxMiniAppPropertiesResp> configs = new ArrayList<>();
        wxMiniAppConfigs.getData().forEach(i ->{
            WxMiniAppPropertiesResp resp = WxMiniAppPropertiesResp.builder()
                    .accountType(i.getAccountType())
                    .name(i.getName()).build();
            configs.add(resp);
        });
        return BaseResult.success(configs);
    }
}

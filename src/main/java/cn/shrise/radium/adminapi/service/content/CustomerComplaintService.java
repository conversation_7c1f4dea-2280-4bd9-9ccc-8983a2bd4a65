package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.CustomerComplaintResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsCustomerComplaint;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CustomerComplaintService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PageResult<List<CustomerComplaintResp>> getCustomerComplaintList(Boolean isHandle, String source, Integer current, Integer size) {
        PageResult<List<SsCustomerComplaint>> pageResult = contentClient.getCustomerComplaintList(isHandle, source, current, size);
        if (!pageResult.isPresent()) {
            return PageResult.empty(current, size);
        }
        List<SsCustomerComplaint> resultData = pageResult.getData();
        Set<Integer> wxIds = resultData.stream().map(SsCustomerComplaint::getWxId).collect(Collectors.toSet());
        List<UcWxExt> wxExtList = userClient.getUnionIds(wxIds).getData();
        Map<Integer, UcWxExt> wxExtMap = wxExtList.stream().collect(Collectors.toMap(UcWxExt::getId, Function.identity()));
        Set<Integer> userIdSet = resultData.stream().map(SsCustomerComplaint::getUserId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        if (isHandle) {
            Set<Integer> auditIds = resultData.stream().map(SsCustomerComplaint::getAuditId).collect(Collectors.toSet());
            userIdSet.addAll(auditIds);
        }
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<CustomerComplaintResp> resps = resultData.stream().map(i -> {
                    CustomerComplaintResp resp = CustomerComplaintResp.builder()
                            .id(i.getId())
                            .createTime(i.getGmtCreate())
                            .wxId(i.getWxId())
                            .nickName(i.getWxId() != null ? wxExtMap.getOrDefault(i.getWxId(), new UcWxExt()).getNickname() : null)
                            .content(i.getContent())
                            .isHandle(i.getIsHandle())
                            .auditName(i.getAuditId() != null ? usersMap.getOrDefault(i.getAuditId(), new UcUsers()).getRealName() : null)
                            .userId(i.getUserId())
                            .userCode(DesensitizeUtil.idToMask(i.getUserId()))
                            .source(i.getSource())
                            .userNickName(i.getUserId() != null ? usersMap.getOrDefault(i.getUserId(), new UcUsers()).getNickName() : null)
                            .handleResult(i.getHandleResult())
                            .handleTime(i.getIsHandle() ? i.getGmtModified() : null)
                            .build();
                    if (ObjectUtil.isNotEmpty(i.getImageUrls())) {
                        List<String> images = JSON.parseArray(i.getImageUrls(), String.class);
                        resp.setImages(images);
                    }
                    return resp;
                })
                .collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }
}

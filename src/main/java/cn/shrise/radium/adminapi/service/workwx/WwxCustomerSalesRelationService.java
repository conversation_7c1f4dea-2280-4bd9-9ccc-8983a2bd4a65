package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.collection.CollectionUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.req.CustomerSalesRelationRecordReq;
import cn.shrise.radium.workwxservice.resp.CustomerSalesRelationRecordResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WwxCustomerSalesRelationService {

    private final WorkwxClient workwxClient;

    private final UserClient userClient;

    public PageResult<List<CustomerSalesRelationRecordResp>> getCustomerSalesRelationRecordPage(CustomerSalesRelationRecordReq req) {
        if (CollectionUtil.isNotEmpty(req.getDeptIdList())) {
            BaseResult<List<Integer>> deptResult = userClient.getUserIdByDepartmentList(req.getDeptIdList());
            if (deptResult.isSuccess()) {
                List<Integer> deptSalesIdList = deptResult.getData();
                if (CollectionUtil.isNotEmpty(deptSalesIdList)) {
                    req.setSalesIdList(deptSalesIdList);
                }
            }
        }
        PageResult<List<CustomerSalesRelationRecordResp>> result = workwxClient.getCustomerSalesRelationRecordPage(req);
        if (result.isSuccess()) {
            List<CustomerSalesRelationRecordResp> recordRespList = result.getData();
            Set<Integer> salesSet = recordRespList.stream().map(CustomerSalesRelationRecordResp::getSalesId).collect(Collectors.toSet());
            // 销售信息
            BaseResult<Map<Integer, UcUsers>> mapBaseResult = userClient.batchGetUserMap(BatchReq.create(salesSet));
            Map<Integer, UcUsers> salesMap = mapBaseResult.getData();
            // 销售部门
            BaseResult<Map<Integer, String>> deptInfoResult = userClient.getDeptListByUsers(salesSet, 1);
            Map<Integer, String> deptMap = deptInfoResult.getData();
            for (CustomerSalesRelationRecordResp recordResp : recordRespList) {
                recordResp.setSalesName(salesMap.get(recordResp.getSalesId()).getRealName());
                recordResp.setDeptName(deptMap.get(recordResp.getSalesId()));
            }
        }
        return result;
    }
}

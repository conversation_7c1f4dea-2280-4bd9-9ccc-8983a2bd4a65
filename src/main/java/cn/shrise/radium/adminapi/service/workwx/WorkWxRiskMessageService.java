package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.req.workwx.RiskMessageStatisticsReq;
import cn.shrise.radium.adminapi.resp.workWx.WorkWxRiskMessageResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.NpWorkWxRiskMessage;
import cn.shrise.radium.workwxservice.req.RiskMassageReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkWxRiskMessageService {

    private final WorkwxClient workwxClient;

    private final UserClient userClient;

    public PageResult<List<WorkWxRiskMessageResp>> getRiskMessageStatisticsList(Integer userId, RiskMessageStatisticsReq req) {
        List<Integer> salesLst = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(req.getDeptList())) {
            salesLst = userClient.getUserIdByDepartmentList(req.getDeptList()).getData();
        }
        if (ObjectUtil.isNotEmpty(req.getUserList())) {
            salesLst = req.getUserList();
        }
        if (ObjectUtil.isNotEmpty(req.getIsManager()) && !req.getIsManager()) {
            salesLst.add(userId);
        }
        if (CollectionUtil.isNotEmpty(salesLst)) {
            return getListPageResult(req.getStartTime(), req.getEndTime(), req.getCurrent(), req.getSize(), salesLst);
        } else {
            return PageResult.empty();
        }
    }

    private PageResult<List<WorkWxRiskMessageResp>> getListPageResult(LocalDate startTime, LocalDate endTime, Integer current, Integer size, List<Integer> salesLst) {
        RiskMassageReq riskMassageReq = RiskMassageReq.builder()
                .startTime(startTime)
                .endTime(endTime)
                .userList(salesLst)
                .current(current)
                .size(size)
                .build();
        PageResult<List<NpWorkWxRiskMessage>> result = workwxClient.getRiskMessagePage(riskMassageReq);
        if (result.isPresent()) {
            List<NpWorkWxRiskMessage> riskMessageList = result.getData();
            Set<Integer> userSet = riskMessageList.stream().map(NpWorkWxRiskMessage::getBelongId).collect(Collectors.toSet());
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
            Map<Integer, String> deptMap = userClient.getDeptListByUsers(userSet, 1).getData();
            List<WorkWxRiskMessageResp> respList = new ArrayList<>();
            for (NpWorkWxRiskMessage message : riskMessageList) {
                WorkWxRiskMessageResp resp = new WorkWxRiskMessageResp();
                BeanUtil.copyProperties(message, resp);
                if (usersMap.containsKey(resp.getBelongId())) {
                    resp.setSalesName(usersMap.get(resp.getBelongId()).getRealName());
                }
                if (deptMap.containsKey(resp.getBelongId())) {
                    resp.setDeptName(deptMap.get(resp.getBelongId()));
                }
                if (ObjectUtil.isNotEmpty(message.getMobile())) {
                    resp.setMobile(DesensitizeUtil.mobilePhone(AESUtil.decrypt(message.getMobile())));
                }
                respList.add(resp);
            }
            return PageResult.success(respList, result.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<WorkWxRiskMessageResp>> getHgRiskMessageStatisticsList(RiskMessageStatisticsReq req) {
        List<Integer> salesLst = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(req.getDeptList())) {
            salesLst = userClient.getUserIdByDepartmentList(req.getDeptList()).getData();
        }
        if (ObjectUtil.isNotEmpty(req.getUserList())) {
            salesLst = req.getUserList();
        }
        return getListPageResult(req.getStartTime(), req.getEndTime(), req.getCurrent(), req.getSize(), salesLst);
    }
}

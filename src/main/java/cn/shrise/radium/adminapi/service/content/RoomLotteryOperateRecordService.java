package cn.shrise.radium.adminapi.service.content;

import cn.shrise.radium.adminapi.resp.RoomLotteryOperateRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.resp.RoomLotteryOperateResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class RoomLotteryOperateRecordService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public List<RoomLotteryOperateRecordResp> getRoomLotteryOperateRecord(Long lotteryId) {
        BaseResult<List<RoomLotteryOperateResp>> roomLotteryOperateRecord = contentClient.getRoomLotteryOperateRecord(lotteryId);
        if (roomLotteryOperateRecord.isFail()) {
            throw new BusinessException(roomLotteryOperateRecord);
        }
        Set<Integer> integers = new HashSet<>();
        List<RoomLotteryOperateRecordResp> roomLotteryOperateRecordRespList = new ArrayList<>();
        roomLotteryOperateRecord.getData().forEach(e -> {
            RoomLotteryOperateRecordResp roomLotteryOperateRecordResp = new RoomLotteryOperateRecordResp();
            BeanUtils.copyProperties(e, roomLotteryOperateRecordResp);
            integers.add(e.getOperatorId());
            roomLotteryOperateRecordRespList.add(roomLotteryOperateRecordResp);
        });
        BaseResult<List<UcUsers>> userList = userClient.batchGetUserList(integers);
        if (userList.isFail()) {
            throw new BusinessException(userList);
        }
        roomLotteryOperateRecordRespList.forEach(e -> {
            if (!ObjectUtils.isEmpty(e.getOperatorId())) {
                userList.getData().forEach(u -> {
                    if (e.getOperatorId().equals(u.getId())) {
                        e.setOperatorName(u.getRealName());
                        e.setAvatarUrl(u.getAvatarUrl());
                    }
                });
            }
        });
        return roomLotteryOperateRecordRespList;
    }
}

package cn.shrise.radium.adminapi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.adminapi.constant.VodCateNameEnum;
import cn.shrise.radium.adminapi.req.AdminVodCreateUploadReq;
import cn.shrise.radium.adminapi.resp.vod.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.AliyunProperties;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsVideoTranscode;
import cn.shrise.radium.contentservice.req.VodCreateUploadReq;
import cn.shrise.radium.contentservice.resp.VodCreateUploadResp;
import com.aliyuncs.vod.model.v20170321.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class VodService {

    private final ContentClient contentClient;
    private final AliyunProperties aliyunProperties;

    public BaseResult<AliInfoResp> getAliInfo() {
        AliInfoResp resp = AliInfoResp.builder()
                .region(aliyunProperties.getRegion())
                .userId(aliyunProperties.getUserId())
                .build();
        return BaseResult.success(resp);
    }

    public BaseResult<List<VodCateResp>> getCompanyCateList(Integer companyType, Long cateId, VodCateNameEnum cateName) {
        BaseResult<List<GetCategoriesResponse.Category>> result =
                contentClient.getCompanyCateList(companyType, cateId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }

        List<GetCategoriesResponse.Category> resultList = Collections.emptyList();
        List<GetCategoriesResponse.Category> categories = result.getData();
        if (ObjectUtils.isNotEmpty(cateName)) {
            String searchName = cateName.name().toLowerCase();
            List<GetCategoriesResponse.Category> filterList = findCategoryByName(categories, searchName);
            if (ObjectUtils.isEmpty(filterList)) {
                for (GetCategoriesResponse.Category c : categories) {
                    List<GetCategoriesResponse.Category> subCategories = contentClient.getCompanyCateList(companyType, c.getCateId()).orElse(Collections.emptyList());
                    List<GetCategoriesResponse.Category> subFilterList = findCategoryByName(subCategories, searchName);
                    if (ObjectUtils.isNotEmpty(subFilterList)) {
                        resultList = subFilterList;
                        break;
                    }
                }
            } else {
                resultList = filterList;
            }
        } else {
            resultList = categories;
        }

        List<VodCateResp> records = resultList.stream()
                .map(e -> VodCateResp.builder().cateId(e.getCateId()).cateName(e.getCateName()).build())
                .collect(Collectors.toList());
        return BaseResult.success(records);
    }

    public List<GetCategoriesResponse.Category> findCategoryByName(List<GetCategoriesResponse.Category> categories, String name) {
        if (ObjectUtils.isEmpty(categories)) {
            return Collections.emptyList();
        }

        return categories.stream()
                .filter(e -> Objects.equals(e.getCateName(), name)).collect(Collectors.toList());
    }

    public BaseResult<List<VodTranscodeResp>> getTranscodeTemplateGroupList() {
        BaseResult<List<ListTranscodeTemplateGroupResponse.TranscodeTemplateGroup>> result =
                contentClient.getTranscodeTemplateGroupList();
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<VodTranscodeResp> records = result.getData().stream()
                .map(e -> VodTranscodeResp.builder().name(e.getName()).transcodeTemplateGroupId(e.getTranscodeTemplateGroupId()).build())
                .collect(Collectors.toList());
        return BaseResult.success(records);
    }

    public BaseResult<VodUploadVideoResp> createUploadVideoNormal(String title, String fileName, Long cateId,
                                                            String templateGroupId, String description,
                                                            String coverURL) {
        BaseResult<CreateUploadVideoResponse> result = contentClient.createUploadVideoNormal(title, fileName,
                cateId, templateGroupId, description, coverURL);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        CreateUploadVideoResponse video = result.getData();
        VodUploadVideoResp record = VodUploadVideoResp.builder()
                .videoId(video.getVideoId())
                .uploadAddress(video.getUploadAddress())
                .uploadAuth(video.getUploadAuth())
                .build();
        return BaseResult.success(record);
    }

    public BaseResult<VodUploadVideoResp> createUploadVideo(Integer companyType, Integer creatorId, String title,
                                                            String videoType, String fileName, Long cateId,
                                                            String templateGroupId, String description,
                                                            String coverURL) {
        BaseResult<CreateUploadVideoResponse> result = contentClient.createUploadVideo(companyType, creatorId, title, videoType, fileName,
                cateId, templateGroupId, description, coverURL);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        CreateUploadVideoResponse video = result.getData();
        VodUploadVideoResp record = VodUploadVideoResp.builder()
                .videoId(video.getVideoId())
                .uploadAddress(video.getUploadAddress())
                .uploadAuth(video.getUploadAuth())
                .build();
        return BaseResult.success(record);
    }

    public VodUploadVideoResp vodCreateUploadVideo(Integer companyType, AdminVodCreateUploadReq adminVodCreateUploadReq) {
        VodCreateUploadReq uploadReq = VodCreateUploadReq.builder()
                .companyType(companyType)
                .title(adminVodCreateUploadReq.getTitle())
                .fileName(adminVodCreateUploadReq.getFileName())
                .cateId(adminVodCreateUploadReq.getCateId())
                .coverUrl(adminVodCreateUploadReq.getCoverUrl())
                .description(adminVodCreateUploadReq.getDescription())
                .templateGroupId(adminVodCreateUploadReq.getTemplateGroupId())
                .build();
        VodCreateUploadResp uploadResp = contentClient.vodCreateUploadVideo(uploadReq).orElseThrow();
        return VodUploadVideoResp.builder()
                .videoId(uploadResp.getVideoId())
                .uploadAddress(uploadResp.getUploadAddress())
                .uploadAuth(uploadResp.getUploadAuth())
                .build();
    }

    public BaseResult<VodUploadVideoResp> refreshUploadVideo(String videoId) {
        BaseResult<RefreshUploadVideoResponse> result = contentClient.refreshUploadVideo(videoId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        RefreshUploadVideoResponse data = result.getData();
        VodUploadVideoResp record = VodUploadVideoResp.builder()
                .videoId(data.getVideoId())
                .uploadAddress(data.getUploadAddress())
                .uploadAuth(data.getUploadAuth())
                .build();
        return BaseResult.success(record);
    }

    public BaseResult<String> getVideoPlayAuth(String videoId) {
        BaseResult<GetVideoPlayAuthResponse> result = contentClient.getVideoPlayAuth(videoId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        GetVideoPlayAuthResponse data = result.getData();
        return BaseResult.success(data.getPlayAuth());
    }

    public BaseResult<List<VodVideoPlayInfoResp>> getVideoPlayInfo(String videoId) {
        BaseResult<GetPlayInfoResponse> result = contentClient.getVideoPlayInfo(videoId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<VodVideoPlayInfoResp> playInfoList = result.getData().getPlayInfoList().stream().map(p -> {
            VodVideoPlayInfoResp playInfoResp = new VodVideoPlayInfoResp();
            BeanUtil.copyProperties(p, playInfoResp);
            return playInfoResp;
        }).collect(Collectors.toList());
        return BaseResult.success(playInfoList);
    }

    public PageResult<List<VodVideoInfoResp>> videoSearch(String title, Long cateId, Boolean isAsc, String status,
                                                          Instant startTime, Instant endTime,
                                                          Integer current, Integer size) {
        PageResult<List<SearchMediaResponse.Media>> result = contentClient.vodVideoSearch(title, cateId, isAsc, status,
                startTime, endTime, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<SearchMediaResponse.Media> data = result.getData();
        List<VodVideoInfoResp> records = data.stream().map(m -> VodVideoInfoResp.builder()
                .videoId(m.getVideo().getVideoId())
                .mediaSource(m.getVideo().getMediaSource())
                .title(m.getVideo().getTitle())
                .status(m.getVideo().getStatus())
                .coverURL(m.getVideo().getCoverURL())
                .cateId(m.getVideo().getCateId())
                .cateName(m.getVideo().getCateName())
                .size(m.getVideo().getSize())
                .description(m.getVideo().getDescription())
                .duration(m.getVideo().getDuration())
                .creationTime(Instant.parse(m.getVideo().getCreationTime()))
                .modificationTime(Instant.parse(m.getVideo().getModificationTime()))
                .build())
                .collect(Collectors.toList());
        return PageResult.success(records, result.getPagination());
    }

    public VideoTranscodeResp getVideoTranscode(String videoId) {
        SsVideoTranscode ssVideoTranscode = contentClient.getVideoTranscode(videoId).orElse(null);
         if (ssVideoTranscode == null) {
             return null;
         }
        return VideoTranscodeResp.builder()
                .videoId(ssVideoTranscode.getVideoId())
                .status(ssVideoTranscode.getStatus())
                .build();
    }
}

package cn.shrise.radium.adminapi.service.im;

import cn.shrise.radium.adminapi.resp.im.VideoResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImRoomVideo;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoomVideoService {

    private final ImClient imClient;
    private final UserClient userClient;

    public PageResult<List<VideoResp>> getRoomVideoList(Long roomId, Boolean enabled, Integer current, Integer size) {
        PageResult<List<ImRoomVideo>> result = imClient.getRoomVideoList(null,roomId, "admin", enabled, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<ImRoomVideo> videoList = result.getData();
        // 用户信息
        Set<Integer> userIds = videoList.stream().map(ImRoomVideo::getCreatorId).collect(Collectors.toSet());
        BaseResult<List<UcUsers>> userResult = userClient.batchGetUserList(userIds);
        if (userResult.isFail()) {
            throw new BusinessException(userResult);
        }
        List<UcUsers> users = userResult.getData();
        Map<Integer, UcUsers> userMap = users.stream().collect(Collectors.toMap(UcUsers::getId, x -> x));

        List<VideoResp> records = videoList.stream().map(v -> {
            VideoResp resp = new VideoResp();
            BeanUtils.copyProperties(v, resp);
            resp.setCreatorName(userMap.getOrDefault(v.getCreatorId(), new UcUsers()).getRealName());
            return resp;
        }).collect(Collectors.toList());

        return PageResult.success(records, Pagination.of(current, size, result.getPagination().getTotal()));
    }
}

package cn.shrise.radium.adminapi.service.quote;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.entity.quote.NoticeSort;
import cn.shrise.radium.adminapi.resp.stock.TradeStatusResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.quoteservice.constant.NoticeSortEnum;
import cn.shrise.radium.quoteservice.entity.NoticeSortInfo;
import cn.shrise.radium.quoteservice.resp.NoticeInfoResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StockInfoService {

    private final QuoteClient quoteClient;

    public PageResult<List<NoticeInfoResp>> getStockNoticeInfoList(String label, List<String> sortCodeList, LocalDate startDate, LocalDate endDate, Integer dateType, Boolean isAsc, Integer current, Integer size) {

        if (ObjectUtil.isNotEmpty(sortCodeList)) {
            List<NoticeSortInfo> noticeSortInfos = quoteClient.getStockNoticeSubSortInfoList(sortCodeList).orElseThrow();
            sortCodeList.addAll(noticeSortInfos.stream().map(NoticeSortInfo::getSortCode).collect(Collectors.toList()));
        }

        PageResult<List<NoticeInfoResp>> pageResult = quoteClient.getStockNoticeInfoList(label, sortCodeList, startDate, endDate, dateType, isAsc, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<NoticeInfoResp> respList = pageResult.getData();
        List<String> codeList = new ArrayList<>();
        respList.forEach(r -> {
            codeList.addAll(r.getSortInfo().stream().map(s -> s.getSortCode()).collect(Collectors.toList()));
        });
        List<NoticeSortInfo> noticeSortInfos = quoteClient.getStockNoticeSortInfoList(codeList).orElseThrow();
        Map<String, String> sortCodeMap = noticeSortInfos.stream().collect(Collectors.toMap(NoticeSortInfo::getSortCode, NoticeSortInfo::getSortName));
        respList = respList.stream().map(resp -> {
            List<NoticeInfoResp.SortCodeResp> sortInfoList = resp.getSortInfo();
            List<NoticeInfoResp.SortCodeResp> codeRespList = sortInfoList.stream().map(s -> {
                s.setSortName(sortCodeMap.getOrDefault(s.getSortCode(), null));
                return s;
            }).collect(Collectors.toList());
            resp.setSortInfo(codeRespList);
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, pageResult.getPagination());
    }

    public List<NoticeSort> getNoticeSortTree() {

        BaseResult<List<NoticeSortInfo>> result = quoteClient.getStockNoticeSubSortInfoList(Arrays.asList(NoticeSortEnum.PUB_COMPANY.getCode()));
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<NoticeSortInfo> sortInfoList = result.getData();
        List<NoticeSort> noticeSortList = new ArrayList<>();
        noticeSortList.add(new NoticeSort(NoticeSortEnum.PUB_COMPANY.getCode(), NoticeSortEnum.PUB_COMPANY.getMsg(), null, true));
        for (NoticeSortInfo sortInfo : sortInfoList) {
            noticeSortList.add(new NoticeSort(sortInfo.getSortCode(), sortInfo.getSortName(), sortInfo.getParentCode(), sortInfo.getEnabled()));
        }
        return build(noticeSortList);
    }

    public List<NoticeSort> build(List<NoticeSort> noticeSortList) {
        for (int i = 0; i < noticeSortList.size(); i++) {
            if (noticeSortList.get(i).getParentId() == null) {
                Collections.swap(noticeSortList, i, 0);
            }
        }
        List<NoticeSort> trees = new ArrayList<>();
        for (NoticeSort treeNode : noticeSortList) {
            if (treeNode.getParentId() == null) {
                trees.add(treeNode);
            }
            for (int i = 1; i < noticeSortList.size(); i++) {
                if (treeNode.getId().equals(noticeSortList.get(i).getParentId())) {
                    if (treeNode.getChildren() == null) {
                        treeNode.setChildren(new ArrayList<>());
                    }
                    treeNode.getChildren().add(noticeSortList.get(i));
                }
            }
        }
        return trees;
    }

    public BaseResult<TradeStatusResp> checkTradeStatus(LocalDate date) {
        Boolean isTradeDay = DateUtils.checkTradeDays(date);
        TradeStatusResp resp = TradeStatusResp.builder().isTradeDay(isTradeDay).build();
        return BaseResult.success(resp);
    }
}

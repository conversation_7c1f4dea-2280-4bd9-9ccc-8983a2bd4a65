package cn.shrise.radium.adminapi.service.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.order.VipSubscriptionInfo;
import cn.shrise.radium.adminapi.resp.user.CustomerTagInfo;
import cn.shrise.radium.adminapi.resp.user.ServiceCustomerResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.CustomerTagDto;
import cn.shrise.radium.userservice.dto.ServiceCustomerDto;
import cn.shrise.radium.userservice.entity.UcRole;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.req.ClaimedCustomerReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ServeCustomerService {

    private final UserClient userClient;

    private final OrderClient orderClient;

    public List<ServiceCustomerResp> search(Integer companyType, String text, Integer userId) {
        String content  = StringUtils.isNotBlank(text) ? String.valueOf(DesensitizeUtil.maskToId(text)) : null;
        BaseResult<UcUsers> result = userClient.searchUser(content, companyType);
        if (!result.isPresent()) {
            return null;
        }
        List<VipSubscription> vipSubscriptions = orderClient.getUserVipSubscription(result.getData().getId()).orElse(null);

        if (CollectionUtil.isEmpty(vipSubscriptions)) {
            return null;
        }

        List<ServiceCustomerResp> respList = new ArrayList<>();
        List<ServiceCustomerDto> dtoList = userClient.serviceCustomer(result.getData().getId(), userId).orElse(null);

        for (ServiceCustomerDto dto : dtoList) {
            ServiceCustomerResp.ServiceCustomerRespBuilder customerRespBuilder = ServiceCustomerResp
                    .builder();
            if (ObjectUtil.isNull(dto.getRelation())) {
                customerRespBuilder.isClaimed(false);
                if (ObjectUtil.isNotNull(userId)) {
                    return null;
                }
            } else {
                UcUsers sales = userClient.getUser(dto.getRelation().getBelongId()).orElse(null);
                UcRole role = userClient.getRoleInfo(dto.getRelation().getBelongId()).orElse(null);
                String salesName = sales != null ? sales.getRealName() : null;
                String roleName = role != null ? role.getName() : null;
                customerRespBuilder.isClaimed(true)
                        .salesName(salesName)
                        .operateRole(roleName)
                        .id(dto.getRelation().getId())
                        .claimTime(dto.getRelation().getLastBelongTime())
                        .lastTime(dto.getRelation().getLastFollowUpTime());
            }
            Map<String, String> vipMap = orderClient.getVipPackageList(companyType).orElse(null).stream().collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));

            for (VipSubscription next : vipSubscriptions) {
                next.setName(vipMap.getOrDefault(next.getNumber(), "未知"));
            }

            List<VipSubscriptionInfo> infoList = getVipSubscriptionInfos(vipSubscriptions);
            List<CustomerTagDto> customerTagInfos = getCustomerTagInfos(dto.getTag());
            ServiceCustomerResp resp = customerRespBuilder
                    .tagList(customerTagInfos)
                    .vipSubscriptionList(infoList)
                    .userId(result.getData().getId())
                    .userCode(DesensitizeUtil.idToMask(result.getData().getId()))
                    .avatarUrl(result.getData().getAvatarUrl())
                    .gender(result.getData().getGender())
                    .nickName(result.getData().getNickName())
                    .build();
            respList.add(resp);
        }
        return respList;
    }

    public List<ServiceCustomerResp> mySearch(Integer companyType, String searchContent, Integer userId) {
        String content = String.valueOf(DesensitizeUtil.maskToId(searchContent));
        BaseResult<UcUsers> result = userClient.searchUser(content, companyType);
        if (!result.isPresent()) {
            return null;
        }
        List<VipSubscription> vipSubscriptions = orderClient.getUserVipSubscription(result.getData().getId()).orElse(null);

        if (CollectionUtil.isEmpty(vipSubscriptions)) {
            return null;
        }
        ServiceCustomerDto dto = userClient.myServiceCustomer(result.getData().getId(), userId).orElse(null);
        ServiceCustomerResp.ServiceCustomerRespBuilder customerRespBuilder = ServiceCustomerResp
                .builder();
        if (ObjectUtil.isNull(dto.getRelation())) {
            customerRespBuilder.isClaimed(false);
            if (ObjectUtil.isNotNull(userId)) {
                return null;
            }
        } else {
            UcUsers sales = userClient.getUser(dto.getRelation().getBelongId()).orElse(null);
            UcRole role = userClient.getRoleInfo(dto.getRelation().getBelongId()).orElse(null);
            String salesName = sales != null ? sales.getRealName() : null;
            String roleName = role != null ? role.getName() : null;
            customerRespBuilder.isClaimed(true)
                    .salesName(salesName)
                    .operateRole(roleName)
                    .id(dto.getRelation().getId())
                    .claimTime(dto.getRelation().getLastBelongTime())
                    .lastTime(dto.getRelation().getLastFollowUpTime());
        }
        Map<String, String> vipMap = orderClient.getVipPackageList(companyType).orElse(null).stream().collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));

        for (VipSubscription next : vipSubscriptions) {
            next.setName(vipMap.getOrDefault(next.getNumber(), "未知"));
        }
        List<VipSubscriptionInfo> infoList = getVipSubscriptionInfos(vipSubscriptions);
        List<CustomerTagDto> customerTagInfos = getCustomerTagInfos(dto.getTag());
        ServiceCustomerResp customerResp = customerRespBuilder
                .tagList(customerTagInfos)
                .vipSubscriptionList(infoList)
                .userId(result.getData().getId())
                .userCode(DesensitizeUtil.idToMask(result.getData().getId()))
                .avatarUrl(result.getData().getAvatarUrl())
                .gender(result.getData().getGender())
                .nickName(result.getData().getNickName())
                .build();
        List<ServiceCustomerResp> customerRespList = new ArrayList<>();
        customerRespList.add(customerResp);
        return customerRespList;
    }

    public PageResult<List<ServiceCustomerResp>> getClaimedPage(ClaimedCustomerReq req) {
        PageResult<List<ServiceCustomerDto>> pageResult = userClient.claimedPage(req);
        if (pageResult.isPresent()) {
            Set<Integer> userSet = pageResult.getData().stream().map(x -> x.getRelation().getUserId()).collect(Collectors.toSet());
            List<VipSubscription> vipSubscriptions = orderClient.batchVipSubscription(userSet, req.getIsExpired()).orElse(null);

            Map<String, String> vipMap = orderClient.getVipPackageList(req.getCompanyType()).orElse(null).stream().collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));
            Set<Integer> saleSet = pageResult.getData().stream().map(x -> x.getRelation().getBelongId()).collect(Collectors.toSet());
            Map<Integer, UcUsers> salesMap = userClient.batchGetUserMap(BatchReq.create(saleSet)).orElse(null);
            Map<Integer, UcRole> salesRoleMap = userClient.batchGetUserRoleMap(BatchReq.create(saleSet)).orElse(null);
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).orElse(null);
            List<ServiceCustomerResp> serviceCustomerRespList = new ArrayList<>();

            Map<Integer, List<VipSubscription>> vipSubscriptionMap = new HashMap<>(16);
            if (ObjectUtil.isNotEmpty(vipSubscriptions)) {
                vipSubscriptionMap = vipSubscriptions.stream().collect(Collectors.groupingBy(VipSubscription::getUserId));
            }
            for (ServiceCustomerDto customerDto : pageResult.getData()) {

                if (ObjectUtil.isNotEmpty(vipSubscriptionMap.get(customerDto.getRelation().getUserId()))) {
                    for (VipSubscription next : vipSubscriptionMap.get(customerDto.getRelation().getUserId())) {
                        next.setName(vipMap.getOrDefault(next.getNumber(), "未知"));
                    }
                }
                List<CustomerTagDto> customerTagInfos = getCustomerTagInfos(customerDto.getTag());
                ServiceCustomerResp.ServiceCustomerRespBuilder respBuilder = ServiceCustomerResp.builder()
                        .id(customerDto.getRelation().getId())
                        .userId(customerDto.getRelation().getUserId())
                        .userCode(DesensitizeUtil.idToMask(customerDto.getRelation().getUserId()))
                        .claimTime(customerDto.getRelation().getLastBelongTime())
                        .lastTime(customerDto.getRelation().getLastFollowUpTime())
                        .tagList(customerTagInfos);

                if (vipSubscriptionMap.containsKey(customerDto.getRelation().getUserId())) {
                    List<VipSubscription> subscriptions = vipSubscriptionMap.get(customerDto.getRelation().getUserId());
                    List<VipSubscriptionInfo> infoList = getVipSubscriptionInfos(subscriptions);
                    respBuilder.vipSubscriptionList(infoList);
                }
                if (usersMap.containsKey(customerDto.getRelation().getUserId())) {
                    respBuilder.avatarUrl(usersMap.get(customerDto.getRelation().getUserId()).getAvatarUrl())
                            .gender(usersMap.get(customerDto.getRelation().getUserId()).getGender())
                            .nickName(usersMap.get(customerDto.getRelation().getUserId()).getNickName());
                }
                if (salesMap.containsKey(customerDto.getRelation().getBelongId())) {
                    respBuilder.salesName(salesMap.get(customerDto.getRelation().getBelongId()).getRealName());
                }

                if (salesRoleMap.containsKey(customerDto.getRelation().getBelongId())) {
                    respBuilder.operateRole(salesRoleMap.get(customerDto.getRelation().getBelongId()).getName());
                }
                serviceCustomerRespList.add(respBuilder.build());
            }
            return PageResult.success(serviceCustomerRespList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    private List<VipSubscriptionInfo> getVipSubscriptionInfos(List<VipSubscription> vipSubscriptions) {
        return Optional.ofNullable(vipSubscriptions).map(list -> list.stream().filter(Objects::nonNull).map(o -> {
            VipSubscriptionInfo info = new VipSubscriptionInfo();
            BeanUtils.copyProperties(o, info);
            info.setUserCode(DesensitizeUtil.idToMask(o.getUserId()));
            return info;
        }).collect(Collectors.toList())).orElse(new ArrayList<>());
    }

    private List<CustomerTagDto> getCustomerTagInfos(List<CustomerTagDto> tagList) {
        return Optional.ofNullable(tagList).map(list -> list.stream().filter(Objects::nonNull).
                peek(o -> o.setUserCode(DesensitizeUtil.idToMask(o.getUserId()))).collect(Collectors.toList())).orElse(new ArrayList<>());
    }
}

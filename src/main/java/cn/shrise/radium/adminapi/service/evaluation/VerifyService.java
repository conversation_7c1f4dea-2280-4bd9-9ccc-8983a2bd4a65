package cn.shrise.radium.adminapi.service.evaluation;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcVerifyInfo;
import cn.shrise.radium.userservice.resp.FakeDataPropertyResp;
import cn.shrise.radium.userservice.resp.evaluation.VerifyInfoResp;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class VerifyService {

    private final UserClient userClient;

    public PageResult<List<VerifyInfoResp>> getVerifyList(String userCode, Integer current, Integer size) {
        if (!DesensitizeUtil.isFakeCode(userCode)) {
            Integer userId = DesensitizeUtil.maskToId(userCode);
            PageResult<List<UcVerifyInfo>> result = userClient.getVerifyList(userId, current, size);
            if (ObjectUtil.isEmpty(result)) {
                return PageResult.empty();
            }
            List<UcVerifyInfo> verifyInfoList = result.getData();
            List<VerifyInfoResp> respList = verifyInfoList.stream().map(i ->
                    VerifyInfoResp.builder()
                            .id(i.getId())
                            .gmtCreate(i.getGmtCreate())
                            .identityType(i.getIdentityType())
                            .verifyType(i.getVerifyType())
                            .verifyStatus(i.getVerifyStatus())
                            .maskMobile(i.getMaskMobile())
                            .identityNumber(DesensitizedUtil.idCardNum(AESUtil.decrypt(i.getIdentityNumber()), 4, 4))
                            .name(i.getName())
                            .age(i.getAge())
                            .finishTime(i.getFinishTime())
                            .build()
            ).collect(Collectors.toList());
            return PageResult.success(respList, result.getPagination());
        } else {
            FakeDataPropertyResp property = userClient.getFakeUserProperties().getData();
            if (ObjectUtil.isEmpty(property)) {
                return PageResult.empty();
            }
            Random random = new Random();
            String identities = property.getIdentities();
            List<String> identityList = JSON.parseArray(identities, String.class);
            String identity = identityList.get(random.nextInt(identityList.size()));
            List<String> list = JSON.parseArray(identity, String.class);
            List<VerifyInfoResp> infoRespList = list.stream().map(i -> JSON.parseObject(i, VerifyInfoResp.class)).collect(Collectors.toList());
            return PageResult.success(infoRespList, Pagination.of(current, size, (long) infoRespList.size()));
        }
    }
}

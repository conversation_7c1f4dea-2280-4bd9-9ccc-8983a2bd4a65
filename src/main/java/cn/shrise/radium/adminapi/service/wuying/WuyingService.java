package cn.shrise.radium.adminapi.service.wuying;


import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.rpaservice.RpaClient;
import cn.shrise.radium.rpaservice.entity.NpWyAccountInfo;
import cn.shrise.radium.rpaservice.req.wuying.WuyingAccountReq;
import cn.shrise.radium.rpaservice.resp.wuying.WuyingAccountDetailResp;
import cn.shrise.radium.rpaservice.resp.wuying.WuyingAccountResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WuyingService {

    private final RpaClient rpaClient;
    private final UserClient userClient;

    public BaseResult<WuyingAccountDetailResp> getWyAccountDetail(String endUserId) {
        WuyingAccountDetailResp result = rpaClient.getWyAccountDetail(endUserId).orElseThrow();
        WuyingAccountDetailResp resp = new WuyingAccountDetailResp();
        BeanUtils.copyProperties(result, resp);
        if (ObjectUtil.isNotEmpty(result.getBelongId())) {
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(Collections.singletonList(result.getBelongId()))).getData();
            resp.setRealName(userMap.getOrDefault(result.getBelongId(), new UcUsers()).getRealName());
        }
        return BaseResult.success(resp);
    }

    public PageResult<List<WuyingAccountResp>> getWyAccountList(WuyingAccountReq req) {
        PageResult<List<NpWyAccountInfo>> result = rpaClient.getWyAccountList(req);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        Set<Integer> salesSet = result.getData().stream().map(NpWyAccountInfo::getBelongId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(salesSet)).getData();
        List<WuyingAccountResp> respList = result.getData().stream().map(i -> {
            WuyingAccountResp resp = WuyingAccountResp.builder()
                    .id(i.getId())
                    .belongId(i.getBelongId())
                    .wyAccountId(i.getAccountId())
                    .wyUserId(i.getEndUserId())
                    .build();
            if (ObjectUtil.isNotEmpty(i.getBelongId())) {
                resp.setRealName(userMap.getOrDefault(i.getBelongId(), new UcUsers()).getRealName());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }
}

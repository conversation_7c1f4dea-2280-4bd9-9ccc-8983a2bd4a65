package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.adminapi.resp.workWx.ChangeCustomerAcquisitionBatchRecordResp;
import cn.shrise.radium.adminapi.resp.workWx.ChangeCustomerAcquisitionBatchResp;
import cn.shrise.radium.adminapi.resp.workWx.CustomerAcquisitionResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WwxConfig;
import cn.shrise.radium.workwxservice.entity.NpWwxChangeCustomerAcquisitionBatch;
import cn.shrise.radium.workwxservice.entity.NpWwxChangeCustomerAcquisitionBatchRecord;
import cn.shrise.radium.workwxservice.entity.NpWwxCustomerAcquisition;
import cn.shrise.radium.workwxservice.property.WxCpProperties;
import cn.shrise.radium.workwxservice.req.WwxChangeUserLinkReq;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NpWwxCustomerAcquisitionService {

    private final WorkwxClient workwxClient;

    public PageResult<List<CustomerAcquisitionResp>> getCustomerAcquisitionList(Integer companyType, Integer channelType, Boolean enabled,
                                                                                Integer accountType, String searchContent, Integer current, Integer size) {
        List<Integer> accountTypeList = new ArrayList<>();
        if (ObjectUtil.isNull(accountType)) {
            List<WwxConfig> configList = workwxClient.getConfigList(companyType).orElseThrow(RecordNotExistedException::new);
            List<Integer> configAccountList = configList.stream().map(WwxConfig::getAccountType).distinct().collect(Collectors.toList());
            accountTypeList.addAll(configAccountList);
        } else {
            accountTypeList.add(accountType);
        }
        PageResult<List<NpWwxCustomerAcquisition>> pageResult = workwxClient.getCustomerAcquisitionList(companyType,
                channelType, enabled, accountTypeList, searchContent, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }

        List<NpWwxCustomerAcquisition> pageList = pageResult.getData();
        List<CustomerAcquisitionResp> items = pageList.stream()
                .map(e -> {
                    JSONArray jsonArray = JSONUtil.parseArray(e.getUserList());
                    Set<String> userSet = new HashSet<>(jsonArray.toList(String.class));
                    return CustomerAcquisitionResp.builder()
                            .id(e.getId())
                            .accountType(e.getAccountType())
                            .gmtCreate(e.getGmtCreate())
                            .enabled(e.getEnabled())
                            .linkName(e.getLinkName())
                            .remark(e.getRemark())
                            .linkId(e.getLinkId())
                            .url(e.getUrl())
                            .enabled(e.getEnabled())
                            .skipVerify(e.getSkipVerify())
                            .userList(jsonArray.toList(String.class))
                            .userCount(userSet.size())
                            .build();
                }).collect(Collectors.toList());

        return PageResult.success(items, pageResult.getPagination());
    }

    public PageResult<List<ChangeCustomerAcquisitionBatchResp>> getChangeLinkBatch(Integer companyType, Integer accountType, Integer current, Integer size) {
        PageResult<List<NpWwxChangeCustomerAcquisitionBatch>> pageResult = workwxClient.getChangeLinkBatch(companyType, accountType, true, current, size);
        if (pageResult.isPresent()) {
            List<ChangeCustomerAcquisitionBatchResp> respList = new ArrayList<>();
            Map<Integer, WxCpProperties> accountMap = workwxClient.getAccountConfig().getData();
            for (NpWwxChangeCustomerAcquisitionBatch customerAcquisitionBatch : pageResult.getData()) {
                ChangeCustomerAcquisitionBatchResp resp = ChangeCustomerAcquisitionBatchResp.builder()
                        .id(customerAcquisitionBatch.getId())
                        .gmtCreate(customerAcquisitionBatch.getGmtCreate())
                        .accountType(customerAcquisitionBatch.getAccountType())
                        .accountName(accountMap.getOrDefault(customerAcquisitionBatch.getAccountType(), new WxCpProperties()).getCorpName())
                        .wxAccount(customerAcquisitionBatch.getWxAccount())
                        .status(customerAcquisitionBatch.getStatus())
                        .build();
                respList.add(resp);
            }
            return PageResult.success(respList, pageResult.getPagination());
        }
        return PageResult.empty();
    }


    public PageResult<List<ChangeCustomerAcquisitionBatchRecordResp>> getBatchRecordPage(Long batchId, Integer current, Integer size) {
        PageResult<List<NpWwxChangeCustomerAcquisitionBatchRecord>> pageResult = workwxClient.getBatchRecordPage(batchId, current, size);
        if (pageResult.isPresent()) {
            List<ChangeCustomerAcquisitionBatchRecordResp> recordRespList = new ArrayList<>();
            Set<Long> customerAcquisitionSet = pageResult.getData().stream().map(NpWwxChangeCustomerAcquisitionBatchRecord::getCustomerAcquisitionId).collect(Collectors.toSet());
            Map<Long, NpWwxCustomerAcquisition> customerAcquisitionMap = workwxClient.batchGetCustomerAcquisitionMap(BatchReq.create(customerAcquisitionSet)).getData();
            for (NpWwxChangeCustomerAcquisitionBatchRecord record : pageResult.getData()) {
                ChangeCustomerAcquisitionBatchRecordResp recordResp = ChangeCustomerAcquisitionBatchRecordResp.builder()
                        .linkId(record.getCustomerAcquisitionId())
                        .result(record.getResult())
                        .build();
                if (customerAcquisitionMap.containsKey(record.getCustomerAcquisitionId())) {
                    recordResp.setLinkName(customerAcquisitionMap.get(record.getCustomerAcquisitionId()).getLinkName());
                }
                recordRespList.add(recordResp);
            }
            return PageResult.success(recordRespList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public BaseResult<String> changeUserLink(WwxChangeUserLinkReq req) {
        return workwxClient.changeUserLink(req);
    }


}

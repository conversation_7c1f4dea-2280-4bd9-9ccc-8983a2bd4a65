package cn.shrise.radium.adminapi.service.douyin;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.douyinservice.DouYinClient;
import cn.shrise.radium.douyinservice.resp.DyAuthLogResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class DyAuthLogService {

    private final DouYinClient douYinClient;

    public List<DyAuthLogResp> getDyAuthLog(Integer operate, String nickname) {
        final BaseResult<List<DyAuthLogResp>> dyAuthLog = douYinClient.getDyAuthLog(operate, nickname);
        if (dyAuthLog.isFail()) {
            throw new BusinessException(dyAuthLog);
        }
        return dyAuthLog.getData();
    }

    public String getAuthUrl(Integer clientType) {
        final BaseResult<String> result = douYinClient.genAuthUrl(clientType);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        return result.getData();
    }
}

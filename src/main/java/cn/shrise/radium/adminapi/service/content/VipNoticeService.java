package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.VipNoticeResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsVipNotice;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class VipNoticeService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PageResult<List<VipNoticeResp>> getVipNoticeList(Integer companyType, Integer product, Boolean enable,
                                                            Integer current, Integer size) {

        PageResult<List<SsVipNotice>> vipNoticeListResult = contentClient.getVipNoticeList(companyType, product, enable, current, size);
        if (ObjectUtil.isEmpty(vipNoticeListResult.getData())) {
            return PageResult.success(null, null);
        }

        Set<Integer> userIds = vipNoticeListResult.getData().stream().map(SsVipNotice::getCreatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();

        List<VipNoticeResp> respList = vipNoticeListResult.getData().stream().map(i -> {
            VipNoticeResp resp = new VipNoticeResp();
            BeanUtil.copyProperties(i, resp);
            resp.setCreatorName(userMap.get(i.getCreatorId()).getRealName());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, vipNoticeListResult.getPagination());
    }
}

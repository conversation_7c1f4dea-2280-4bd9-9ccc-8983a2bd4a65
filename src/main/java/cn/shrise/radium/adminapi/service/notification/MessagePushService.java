package cn.shrise.radium.adminapi.service.notification;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.VipPackageItem;
import cn.shrise.radium.adminapi.resp.notification.PcMessagePushInfoResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.notificationservice.NotificationClient;
import cn.shrise.radium.notificationservice.entity.NtPcMessagePushInfo;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessagePushService {

    private final NotificationClient notificationClient;

    private final UserClient userClient;

    private final OrderClient orderClient;

    public PageResult<List<PcMessagePushInfoResp>> getPcMessagePushPage(Integer companyType, Integer status, String searchContent, Integer current, Integer size) {
        PageResult<List<NtPcMessagePushInfo>> result = notificationClient.getMessagePushList(companyType, status, searchContent, current, size);
        if (result.isFail()) {
            return PageResult.empty(current, size);
        }
        final List<NtPcMessagePushInfo> records = result.getData();
        if (ObjectUtils.isEmpty(records)) {
            return PageResult.empty(current, size);
        }
        List<VipPackage> vipPackages = orderClient.getVipPackageList(companyType).orElse(null);
        Map<String, VipPackage> packageMap = new HashMap<>(16);
        if (ObjectUtil.isNotEmpty(vipPackages)) {
            Map<String, VipPackage> map = vipPackages.stream().collect(Collectors.toMap(VipPackage::getNumber, x -> x));
            packageMap.putAll(map);
        }
        Set<Integer> userIdSet = records.stream().map(NtPcMessagePushInfo::getCreatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).getData();
        List<PcMessagePushInfoResp> respList = records.stream().map(e -> {
            PcMessagePushInfoResp resp = new PcMessagePushInfoResp();
            BeanUtil.copyProperties(e, resp);
            if (usersMap.containsKey(e.getCreatorId())) {
                resp.setCreatorName(usersMap.get(e.getCreatorId()).getRealName());
            }
            if (ObjectUtil.isNotEmpty(e.getLevelNumberList())) {
                List<String> numberList = JSON.parseArray(e.getLevelNumberList(), String.class);
                List<VipPackageItem> itemList = new ArrayList<>();
                for (String number : numberList) {
                    VipPackageItem item = VipPackageItem.builder()
                            .number(number)
                            .name(packageMap.get(number).getName())
                            .build();
                    itemList.add(item);
                    resp.setPackageList(itemList);
                }
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }
}

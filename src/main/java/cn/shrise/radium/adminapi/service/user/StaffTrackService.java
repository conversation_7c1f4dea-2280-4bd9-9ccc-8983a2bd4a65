package cn.shrise.radium.adminapi.service.user;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.shrise.radium.adminapi.resp.user.StaffTrackEventRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.StaffTrackEventTypeConstant;
import cn.shrise.radium.userservice.entity.UcStaffTrackApp;
import cn.shrise.radium.userservice.entity.UcStaffTrackPage;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.lindorm.entity.LdStaffTrackEvent;
import cn.shrise.radium.userservice.resp.StaffAppPageInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffTrackService {

    private final UserClient userClient;

    public PageResult<List<StaffTrackEventRecordResp>> getStaffTrackEventRecord(Integer companyType, String appNumber, Long startTime, Long endTime, Integer trackType, Integer staffId, Integer current, Integer size) {
        PageResult<List<LdStaffTrackEvent>> pageResult = userClient.filterStaffTrackEventPage(companyType, appNumber, null, startTime, endTime, trackType, staffId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<LdStaffTrackEvent> trackData = pageResult.getData();
        List<Integer> staffIdList = trackData.stream().map(LdStaffTrackEvent::getStaffId).collect(Collectors.toList());
        BaseResult<List<UcUsers>> usersResult = userClient.batchGetUserList(BatchReq.of(staffIdList));
        Map<String, String> pageMap;
        Map<String, String> appMap;
        if (ObjectUtil.equal(trackType, StaffTrackEventTypeConstant.PAGE)) {
            StaffAppPageInfoResp pageInfoResp = userClient.getStaffAppPageInfo(null, appNumber).orElse(null);
            if (ObjectUtil.isNotEmpty(pageInfoResp) && ObjectUtil.isNotEmpty(pageInfoResp.getPageList())) {
                pageMap = pageInfoResp.getPageList().stream().collect(toMap(UcStaffTrackPage::getNumber, UcStaffTrackPage::getName));
            } else {
                pageMap = new HashMap<>();
            }
        } else {
            pageMap = new HashMap<>();
        }
        if (ObjectUtil.equal(trackType, StaffTrackEventTypeConstant.LOGIN)) {
            List<UcStaffTrackApp> ucStaffTrackApps = userClient.getStaffAppList(companyType, null, null).orElse(null);
            if (ObjectUtil.isNotEmpty(ucStaffTrackApps)) {
                appMap = ucStaffTrackApps.stream().collect(toMap(UcStaffTrackApp::getNumber, UcStaffTrackApp::getName));
            } else {
                appMap = new HashMap<>();
            }
        } else {
            appMap = new HashMap<>();
        }
        Map<Integer, UcUsers> userMap;
        if (usersResult.isFail()) {
            log.warn("getSalesUser fail: {}", usersResult);
            userMap = new HashMap<>();
        } else {
            userMap = usersResult.getData().stream().collect(toMap(UcUsers::getId, e -> e));
        }
        List<StaffTrackEventRecordResp> resps = trackData.stream().map(track -> {
            StaffTrackEventRecordResp resp = StaffTrackEventRecordResp.builder()
                    .gmtCreate(track.getGmtCreate())
                    .staffId(track.getStaffId())
                    .appNumber(track.getAppId())
                    .pageNumber(track.getPageId())
                    .url(track.getUrl())
                    .ip(track.getIp())
                    .userAgent(track.getUserAgent())
                    .clientVersion(track.getClientVersion())
                    .build();
            if (ObjectUtil.equal(trackType, StaffTrackEventTypeConstant.PAGE) && pageMap.containsKey(track.getPageId())) {
                resp.setPageName(pageMap.get(track.getPageId()));
            }
            if (ObjectUtil.equal(trackType, StaffTrackEventTypeConstant.LOGIN) && appMap.containsKey(track.getAppId())) {
                resp.setAppName(appMap.get(track.getAppId()));
            }
            if (userMap.containsKey(resp.getStaffId())) {
                resp.setRealName(userMap.get(resp.getStaffId()).getRealName());
                resp.setIsEnabled(userMap.get(resp.getStaffId()).getEnabled());
            }
            UserAgent userAgent = UserAgentUtil.parse(track.getUserAgent());
            if (ObjectUtil.isNotEmpty(userAgent)) {
                resp.setBrowser(userAgent.getBrowser().getName());
                resp.setOs(userAgent.getOs().getName());
                resp.setPlatform(userAgent.getPlatform().getName());
            }
            return resp;
        }).collect(Collectors.toList());
        Pagination pagination = pageResult.getPagination();
        if (pagination.getTotal() > 5000) {
            pagination.setTotal(5000L);
        }
        return PageResult.success(resps, pagination);
    }
}

package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.SsAdvertCategoryResp;
import cn.shrise.radium.adminapi.resp.content.SsAdvertResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAdvert;
import cn.shrise.radium.contentservice.entity.SsAdvertCategory;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AdvertService {

    private final ContentClient contentClient;

    private final UserClient userClient;

    public PageResult<List<SsAdvertResp>> getAdvertList(Integer companyType, Boolean enabled, String label, Long categoryId, Integer current, Integer size) {
        PageResult<List<SsAdvert>> pageResult = contentClient.getAdvertList(companyType, enabled, label, categoryId, current, size);
        if (pageResult.isPresent()) {
            List<SsAdvert> advertList = pageResult.getData();
            Set<Integer> creatorIds = advertList.stream().map(SsAdvert::getCreatorId).collect(Collectors.toSet());
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(creatorIds)).getData();

            Set<Long> categoryIds = advertList.stream()
                    .filter(advert -> ObjectUtil.isNotNull(advert.getCategoryId()))
                    .map(SsAdvert::getCategoryId)
                    .collect(Collectors.toSet());

            Map<Long, SsAdvertCategory> categoryMap = categoryIds.isEmpty() ?
                    new HashMap<>() :
                    contentClient.batchGetAdvertCategoryMap(BatchReq.create(categoryIds)).orElse(new HashMap<>());

            List<SsAdvertResp> respList = advertList.stream()
                    .map(advert -> {
                        SsAdvertResp resp = SsAdvertResp.of(advert);
                        UcUsers user = userMap.get(advert.getCreatorId());
                        if (user != null) {
                            resp.setCreatorName(user.getRealName());
                        }
                        SsAdvertCategory category = categoryMap.getOrDefault(advert.getCategoryId(), new SsAdvertCategory());
                        resp.setCategoryName(category.getName());
                        return resp;
                    })
                    .collect(Collectors.toList());

            return PageResult.success(respList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<SsAdvertCategoryResp>> getAdvertCategoryList(Boolean enable, Boolean isAsc, Integer current, Integer size) {
        PageResult<List<SsAdvertCategory>> result = contentClient.getAdvertCategoryList(enable, isAsc, current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<SsAdvertCategory> categoryList = result.getData();
        Set<Integer> userSet = categoryList.stream().map(SsAdvertCategory::getCreatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        List<SsAdvertCategoryResp> respList = categoryList.stream().map(e -> SsAdvertCategoryResp.builder()
                .id(e.getId())
                .name(e.getName())
                .createTime(e.getGmtCreate())
                .creatorId(e.getCreatorId())
                .creator(userMap.getOrDefault(e.getCreatorId(), new UcUsers()).getRealName())
                .enable(e.getEnabled())
                .build()).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }
}

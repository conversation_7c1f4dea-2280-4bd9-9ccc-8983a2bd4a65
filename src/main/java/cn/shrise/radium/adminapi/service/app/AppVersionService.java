package cn.shrise.radium.adminapi.service.app;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAppVersion;
import cn.shrise.radium.contentservice.req.AppVersionReq;
import cn.shrise.radium.contentservice.req.UpdateVersionReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppVersionService {

    private final ContentClient contentClient;

    public PageResult<List<SsAppVersion>> getAppVersionPage(Integer platform, Integer current, Integer size) {
        return contentClient.getAppVersionPage(platform, current, size);
    }

    public BaseResult<Void> addAppVersion(AppVersionReq req) {
        checkVersionFormat(req.getVersion());
        if (!checkVersion(req.getNoticeVersion())) {
            throw new BusinessException("版本格式错误");
        }
        return contentClient.addAppVersion(req);
    }

    public BaseResult<Void> updateAppVersion(UpdateVersionReq req) {
        if (!checkVersion(req.getNoticeVersion())) {
            throw new BusinessException("版本格式错误");
        }
        return contentClient.updateAppVersion(req);
    }

    public void checkVersionFormat(String version) {
        if (!version.matches("^\\d+(\\.\\d+)*$")) {
            throw new BusinessException("版本号格式有误");
        }
    }

    public boolean checkVersion(String noticeVersion) {
        // 判断是否是*
        if ("*".equals(noticeVersion)) {
            return true;
        }

        // 判断是否是由数字和.组成的版本号
        if (noticeVersion.matches("^\\d+(\\.\\d+)*$")) {
            return true;
        }

        // 判断是否是由，分隔开的多组字符串
        if (noticeVersion.contains(",")) {
            String[] versions = noticeVersion.split(",");
            for (String version : versions) {
                if (version.contains("-") && !version.startsWith("-") && !version.endsWith("-")) {
                    String[] parts = version.split("-");
                    if (parts.length != 2) {
                        return false;
                    }
                    String version1 = parts[0];
                    String version2 = parts[1];
                    if (version1.matches("^\\d+(\\.\\d+)*$") && version2.matches("^\\d+(\\.\\d+)*$")) {
                        return compareVersions(version1, version2) < 0;
                    } else {
                        return false;
                    }
                } else if (!checkVersion(version)) {
                    return false;
                }
            }
            return true;
        }

        // 判断是否是由-分隔开的两组由数字和.组成的版本号并且后者版本大于前者
        if (noticeVersion.contains("-") && !noticeVersion.startsWith("-") && !noticeVersion.endsWith("-")) {
            String[] parts = noticeVersion.split("-");
            if (parts.length != 2) {
                return false;
            }
            String version1 = parts[0];
            String version2 = parts[1];
            if (version1.matches("^\\d+(\\.\\d+)*$") && version2.matches("^\\d+(\\.\\d+)*$")) {
                return compareVersions(version1, version2) < 0;
            } else {
                return false;
            }
        }
        return false;
    }

    // 比较两个版本号的大小，如果version1 < version2，返回-1；如果version1 = version2，返回0；如果version1 > version2，返回1
    private int compareVersions(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");

        int length = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < length; i++) {
            int part1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int part2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
            if (part1 < part2) {
                return -1;
            } else if (part1 > part2) {
                return 1;
            }
        }
        return 0;
    }
}

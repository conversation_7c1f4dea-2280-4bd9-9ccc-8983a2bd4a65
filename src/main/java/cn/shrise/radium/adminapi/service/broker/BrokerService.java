package cn.shrise.radium.adminapi.service.broker;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.OpenAccountManageResp;
import cn.shrise.radium.adminapi.resp.OpenAccountOrderResp;
import cn.shrise.radium.adminapi.resp.broker.DbAccountFileResp;
import cn.shrise.radium.brokerservice.BrokerClient;
import cn.shrise.radium.brokerservice.entity.BkDbAccountFile;
import cn.shrise.radium.brokerservice.entity.BkDbAccountInfo;
import cn.shrise.radium.brokerservice.req.AccountInfoReq;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.orderservice.properties.merchant.Merchant;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BrokerService {

    private final BrokerClient brokerClient;
    private final UserClient userClient;

    public PageResult<List<OpenAccountOrderResp>> getOpenAccountOrderResp(AccountInfoReq accountInfoReq) {
        PageResult<List<BkDbAccountInfo>> bkDbAccountInfo = brokerClient.getBkDbAccountInfo(accountInfoReq);
        if (bkDbAccountInfo.isFail()) {
            throw new BusinessException(bkDbAccountInfo);
        }
        List<BkDbAccountInfo> bkDbAccountInfoData = bkDbAccountInfo.getData();
        if (bkDbAccountInfoData.isEmpty()) {
            return PageResult.empty(accountInfoReq.getCurrent(), accountInfoReq.getSize());
        }
        Set<Integer> wxIdList = bkDbAccountInfoData.stream().map(BkDbAccountInfo::getWxId).collect(Collectors.toSet());
        Map<Integer, UcWxExt> wxExtMap = userClient.getUnionIds(wxIdList).getData().stream().collect(Collectors.toMap(UcWxExt::getId, r -> r));
        List<OpenAccountOrderResp> openAccountOrderRespList = new ArrayList<>();
        bkDbAccountInfoData.forEach(e -> {
            OpenAccountOrderResp openAccountOrderResp = new OpenAccountOrderResp();
            BeanUtil.copyProperties(e, openAccountOrderResp);
            if (wxExtMap.containsKey(e.getWxId())){
                openAccountOrderResp.setHeadImgUrl(wxExtMap.get(e.getWxId()).getHeadImgUrl());
                openAccountOrderResp.setNickName(wxExtMap.get(e.getWxId()).getNickname());
            }
            openAccountOrderRespList.add(openAccountOrderResp);
        });
        return PageResult.success(openAccountOrderRespList, bkDbAccountInfo.getPagination());
    }

    public PageResult<List<OpenAccountManageResp>> getOpenAccountManageResp(AccountInfoReq accountInfoReq) {
        PageResult<List<BkDbAccountInfo>> bkDbAccountInfo = brokerClient.getBkDbAccountInfo(accountInfoReq);
        if (bkDbAccountInfo.isFail()) {
            throw new BusinessException(bkDbAccountInfo);
        }
        List<BkDbAccountInfo> bkDbAccountInfoData = bkDbAccountInfo.getData();
        if (bkDbAccountInfoData.isEmpty()) {
            return PageResult.empty(accountInfoReq.getCurrent(), accountInfoReq.getSize());
        }
        Set<Integer> wxIdList = bkDbAccountInfoData.stream().map(BkDbAccountInfo::getWxId).collect(Collectors.toSet());
        Map<Integer, UcWxExt> wxExtMap = userClient.getUnionIds(wxIdList).getData().stream().collect(Collectors.toMap(UcWxExt::getId, r -> r));
        Set<Integer> userIds = bkDbAccountInfoData.stream().map(BkDbAccountInfo::getBelongId).collect(Collectors.toSet());
        Map<Integer, UcUsers> ucUsersMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        List<OpenAccountManageResp> openAccountManageRespList = new ArrayList<>();
        bkDbAccountInfoData.forEach(e -> {
            OpenAccountManageResp openAccountManageResp = new OpenAccountManageResp();
            BeanUtil.copyProperties(e, openAccountManageResp);
            if (wxExtMap.containsKey(e.getWxId())){
                openAccountManageResp.setHeadImgUrl(wxExtMap.get(e.getWxId()).getHeadImgUrl());
                openAccountManageResp.setNickName(wxExtMap.get(e.getWxId()).getNickname());
            }
            openAccountManageResp.setSalesName(ucUsersMap.get(e.getBelongId()).getRealName());
            openAccountManageRespList.add(openAccountManageResp);
        });
        return PageResult.success(openAccountManageRespList, bkDbAccountInfo.getPagination());
    }

    public PageResult<List<DbAccountFileResp>> getFileList(Integer current, Integer size) {
        List<DbAccountFileResp> respList = new ArrayList<>();
        PageResult<List<BkDbAccountFile>> fileList = brokerClient.getFileList(AuthContextHolder.getCompanyType(), current, size);
        if (ObjectUtil.isEmpty(fileList.getData())) {
            return PageResult.success(respList, null);
        }
        Set<Integer> userIds = fileList.getData().stream().map(BkDbAccountFile::getCreatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> ucUsersMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        respList = fileList.getData().stream().map(i -> {
            DbAccountFileResp resp = new DbAccountFileResp();
            BeanUtil.copyProperties(i, resp);
            resp.setCreatorName(ucUsersMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
            resp.setUploadTime(i.getGmtCreate());
            return resp;
        }).collect(Collectors.toList());

        return PageResult.success(respList, fileList.getPagination());
    }
}

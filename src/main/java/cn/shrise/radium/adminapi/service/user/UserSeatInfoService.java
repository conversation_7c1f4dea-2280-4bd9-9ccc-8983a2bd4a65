package cn.shrise.radium.adminapi.service.user;

import cn.shrise.radium.adminapi.resp.user.SeatUserRelationResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcJustCallSeatInfo;
import cn.shrise.radium.userservice.entity.UcTxCallSeatInfo;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.call.JustCallSeatResp;
import cn.shrise.radium.userservice.resp.call.TencentSeatResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static cn.shrise.radium.userservice.constant.CallSeatTypeConstant.JUST;
import static cn.shrise.radium.userservice.constant.CallSeatTypeConstant.TENCENT;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSeatInfoService {

    private final UserClient userClient;

    public SeatUserRelationResp findUserSeatInfo(Integer userId) {
        UcJustCallSeatInfo ucJustCallSeatInfo = userClient.getUserJustCallSeatInfo(userId).getData();
        UcTxCallSeatInfo ucTxCallSeatInfo = userClient.getUserTxCallSeatInfo(userId).getData();
        UcUsers user = userClient.getUser(userId).getData();
        if (ucJustCallSeatInfo != null) {
            JustCallSeatResp justCallSeatResp = new JustCallSeatResp();
            BeanUtils.copyProperties(ucJustCallSeatInfo, justCallSeatResp);
            justCallSeatResp.setUserName(user.getRealName());
            return SeatUserRelationResp.builder()
                    .seatId(ucJustCallSeatInfo.getId())
                    .seatType(JUST)
                    .justCallSeatResp(justCallSeatResp)
                    .build();
        } else if (ucTxCallSeatInfo != null) {
            TencentSeatResp tencentSeatResp = new TencentSeatResp();
            BeanUtils.copyProperties(ucTxCallSeatInfo, tencentSeatResp);
            tencentSeatResp.setUserName(user.getRealName());
            return SeatUserRelationResp.builder()
                    .seatId(ucTxCallSeatInfo.getId())
                    .seatType(TENCENT)
                    .tencentSeatResp(tencentSeatResp)
                    .build();
        } else {
            return new SeatUserRelationResp();
        }
    }

    public PageResult<List<TencentSeatResp>> getTencentCallSeatList(Integer current, Integer size) {
        return userClient.getTencentCallSeatList(current,size);
    }

}

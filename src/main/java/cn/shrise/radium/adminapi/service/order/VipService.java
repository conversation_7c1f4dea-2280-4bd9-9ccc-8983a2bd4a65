package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.req.AdminManualCancelVipSubscriptionReq;
import cn.shrise.radium.adminapi.req.AdminManualCreateOrRenewVipSubscriptionReq;
import cn.shrise.radium.adminapi.resp.AdminManualCreateOrRenewVipSubscriptionResp;
import cn.shrise.radium.adminapi.resp.UserAllSubscriptionItem;
import cn.shrise.radium.adminapi.resp.UserSubscriptionItem;
import cn.shrise.radium.adminapi.resp.VipPackageItem;
import cn.shrise.radium.adminapi.resp.order.UnclaimedCustomerResp;
import cn.shrise.radium.adminapi.resp.order.VipSubscriptionInfo;
import cn.shrise.radium.adminapi.resp.order.VipSubscriptionRecordResp;
import cn.shrise.radium.adminapi.resp.user.CustomerResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.entity.VipSubscriptionRecord;
import cn.shrise.radium.orderservice.properties.vip.UserVipPackageInfo;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.orderservice.req.ManualCancelVipSubscriptionReq;
import cn.shrise.radium.orderservice.req.ManualCreateOrRenewVipSubscriptionReq;
import cn.shrise.radium.orderservice.req.UnclaimedCustomerReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class VipService {

    private final OrderClient orderClient;
    private final UserClient userClient;

    public List<VipPackageItem> getVipPackageList(Integer companyType) {
        List<VipPackage> packageList = orderClient.getVipPackageList(companyType)
                .orElse(Collections.emptyList());
        return packageList.stream().map(e -> VipPackageItem.builder()
                        .name(e.getName())
                        .level(e.getLevel())
                        .number(e.getNumber())
                        .build())
                .collect(Collectors.toList());
    }

    public AdminManualCreateOrRenewVipSubscriptionResp manualCreateOrRenewVipSubscription(Integer operatorId, AdminManualCreateOrRenewVipSubscriptionReq req) {
        ManualCreateOrRenewVipSubscriptionReq manualCreateOrRenewVipSubscriptionReq = ManualCreateOrRenewVipSubscriptionReq.builder()
                .userId(DesensitizeUtil.maskToId(req.getUserCode()))
                .level(req.getLevel())
                .number(req.getNumber())
                .period(req.getPeriod())
                .operatorId(operatorId)
                .remark(req.getRemark())
                .build();
        VipSubscription subscription = orderClient.manualCreateOrRenewVipSubscription(manualCreateOrRenewVipSubscriptionReq).orElseThrow();
        return AdminManualCreateOrRenewVipSubscriptionResp.builder()
                .id(subscription.getId())
                .userId(subscription.getUserId())
                .userCode(DesensitizeUtil.idToMask(subscription.getUserId()))
                .level(subscription.getLevel())
                .number(subscription.getNumber())
                .openTime(subscription.getOpenTime())
                .expireTime(subscription.getExpireTime())
                .build();
    }

    public void manualCancelVipSubscription(Integer operatorId, AdminManualCancelVipSubscriptionReq req) {
        ManualCancelVipSubscriptionReq manualCancelVipSubscriptionReq = ManualCancelVipSubscriptionReq.builder()
                .operatorId(operatorId)
                .subscriptionId(req.getSubscriptionId())
                .remark(req.getRemark())
                .build();
        orderClient.manualCancelVipSubscription(manualCancelVipSubscriptionReq);
    }

    public List<UserSubscriptionItem> getUserVipSubscription(String userCode) {
        int userId = DesensitizeUtil.maskToId(userCode);
        List<VipSubscription> subscriptionList = orderClient.getUserVipSubscriptionList(userId).orElseThrow();
        return subscriptionList.stream().map(e -> UserSubscriptionItem.builder()
                        .subscriptionId(e.getId())
                        .level(e.getLevel())
                        .number(e.getNumber())
                        .openTime(e.getOpenTime())
                        .expireTime(e.getExpireTime())
                        .build())
                .collect(Collectors.toList());
    }

    public PageResult<List<VipSubscriptionRecordResp>> getVipSubscriptionRecordList(String number, Integer operateType, String searchText, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        List<Integer> userIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(searchText)) {
            try {
                int userId = DesensitizeUtil.maskToId(searchText);
                userIdList.add(userId);
            } catch (Exception e) {
                PageResult<List<UcUsers>> userListResult = userClient.getUserList(null, null, true, searchText, null, 1, 100);
                List<UcUsers> users = userListResult.getData() == null ? Collections.emptyList() : userListResult.getData();
                if (ObjectUtil.isNotEmpty(users)) {
                    userIdList = users.stream().map(UcUsers::getId).collect(Collectors.toList());
                }
            }
            if (ObjectUtil.isEmpty(userIdList)) {
                return PageResult.success();
            }
        }
        PageResult<List<VipSubscriptionRecord>> vipSubscriptionRecordListPageResult = orderClient.getVipSubscriptionRecordList(userIdList, number, operateType, startTime, endTime, current, size);
        if (vipSubscriptionRecordListPageResult.isFail()) {
            log.error("获取vip开通记录失败, msg: {}", vipSubscriptionRecordListPageResult);
            throw new BusinessException(vipSubscriptionRecordListPageResult);
        }
        List<VipSubscriptionRecord> vipSubscriptionRecords = vipSubscriptionRecordListPageResult.getData() != null ? vipSubscriptionRecordListPageResult.getData() : Collections.emptyList();
        if (ObjectUtil.isEmpty(vipSubscriptionRecords)) {
            return PageResult.empty();
        }
        List<Integer> userIds = vipSubscriptionRecords.stream().map(e -> e.getUserId()).collect(Collectors.toList());
        List<Integer> operatorIds = vipSubscriptionRecords.stream().map(e -> e.getOperatorId()).collect(Collectors.toList());
        userIds.addAll(operatorIds);
        BaseResult<Map<Integer, UcUsers>> userMapResult = userClient.batchGetUserMap(new BatchReq<>(userIds));
        if (userMapResult.isFail()) {
            log.error("获取用户信息失败, msg：{}", userMapResult);
            throw new BusinessException(userMapResult);
        }
        Map<Integer, UcUsers> usersMap = userMapResult.getData();
        ArrayList<VipSubscriptionRecordResp> resps = new ArrayList<>();
        vipSubscriptionRecords.stream().forEach(e -> {
            VipSubscriptionRecordResp resp = new VipSubscriptionRecordResp();
            BeanUtils.copyProperties(e, resp);
            resp.setNickName(usersMap.get(e.getUserId()).getNickName());
            if (ObjectUtil.isNotEmpty(e.getOperatorId())) {
                resp.setOperatorName(usersMap.get(e.getOperatorId()).getRealName());
            }
            resp.setUserCode(DesensitizeUtil.idToMask(e.getUserId()));
            resps.add(resp);
        });
        return PageResult.success(resps, vipSubscriptionRecordListPageResult.getPagination());
    }

    public PageResult<List<UnclaimedCustomerResp>> getUnclaimedList(UnclaimedCustomerReq req) {
        List<Integer> customerList = userClient.getCustomerList();
        req.setUserList(customerList);
        PageResult<List<VipSubscription>> unclaimedList = orderClient.getUnclaimedList(req);
        if (ObjectUtil.isEmpty(unclaimedList)) {
            return PageResult.empty();
        }

        Set<Integer> userSet = unclaimedList.getData().stream().map(VipSubscription::getUserId).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userSet));
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess() ? getUserResult.getData() : Collections.emptyMap();

        List<UnclaimedCustomerResp> unclaimedCustomerResps = new ArrayList<>();

        Map<String, String> vipMap = orderClient.getVipPackageList(req.getCompanyType()).orElse(null).stream().collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));

        for (VipSubscription next : unclaimedList.getData()) {
            next.setName(vipMap.getOrDefault(next.getNumber(), "未知"));
            VipSubscriptionInfo info = new VipSubscriptionInfo();
            BeanUtils.copyProperties(next, info);
            info.setUserCode(DesensitizeUtil.idToMask(next.getUserId()));
            UnclaimedCustomerResp customerResp = UnclaimedCustomerResp.builder()
                    .vipSubscription(info)
                    .nickName(userMap.getOrDefault(next.getUserId(), new UcUsers()).getNickName())
                    .avatarUrl(userMap.getOrDefault(next.getUserId(), new UcUsers()).getAvatarUrl())
                    .gender(userMap.getOrDefault(next.getUserId(), new UcUsers()).getGender())
                    .build();
            unclaimedCustomerResps.add(customerResp);
        }

        return PageResult.success(unclaimedCustomerResps, unclaimedList.getPagination());
    }

    /**
     * 获取用户全部开通过的服务包
     */
    public List<UserAllSubscriptionItem> getUserAllVipSubscription(String userCode, Boolean isExpired) {
        int userId = DesensitizeUtil.maskToId(userCode);
        List<VipSubscription> subscriptionList = orderClient.getUserVipSubscriptionList(userId, isExpired).orElseThrow();
        return subscriptionList.stream().map(e -> {
                            UserAllSubscriptionItem item = UserAllSubscriptionItem.builder()
                                    .subscriptionId(e.getId())
                                    .level(e.getLevel())
                                    .number(e.getNumber())
                                    .openTime(e.getOpenTime())
                                    .expireTime(e.getExpireTime())
                                    .build();
                            if (item.getExpireTime().isBefore(Instant.now())) {
                                item.setIsExpire(true);
                            }
                            return item;
                        }
                )
                .collect(Collectors.toList());
    }

    public BaseResult<CustomerResp> filterUserVipSubscription(String userCode, Integer level, Boolean isExpired) {
        int userId = DesensitizeUtil.maskToId(userCode);
        UcUsers users = userClient.getUser(userId).orElseThrow();
        if (ObjectUtil.isEmpty(users) || !users.getEnabled()) {
            return BaseResult.success(null);
        }
        List<VipSubscription> vipSubscriptions = orderClient.batchUserVipSubscription(level, BatchReq.of(Arrays.asList(userId)), isExpired).orElseThrow();
        List<UserSubscriptionItem> userSubscriptionItems = null;
        if (ObjectUtil.isNotEmpty(vipSubscriptions)) {
            userSubscriptionItems = vipSubscriptions.stream().map(e -> {
                return UserSubscriptionItem.builder()
                        .subscriptionId(e.getId())
                        .level(e.getLevel())
                        .number(e.getNumber())
                        .openTime(e.getOpenTime())
                        .expireTime(e.getExpireTime())
                        .build();
            }).collect(Collectors.toList());
        }
        CustomerResp resp = CustomerResp.builder()
                .userId(userId)
                .userCode(DesensitizeUtil.idToMask(userId))
                .userNumber(users.getNumber())
                .avatarUrl(users.getAvatarUrl())
                .nickName(users.getNickName())
                .createTime(users.getCreateTime())
                .subscriptionItemList(userSubscriptionItems)
                .build();
        return BaseResult.success(resp);
    }

    public BaseResult<List<UserVipPackageInfo>> getSubscriptionsInfoList(Integer companyType, Integer userId) {
        List<UserVipPackageInfo> userVipPackageInfoList = orderClient.getSubscriptionsInfoList(companyType, userId).orElse(null);
        return BaseResult.success(userVipPackageInfoList);
    }
}

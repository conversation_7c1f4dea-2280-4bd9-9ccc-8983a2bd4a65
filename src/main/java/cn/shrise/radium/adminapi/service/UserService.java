package cn.shrise.radium.adminapi.service;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.entity.CustomerInfo;
import cn.shrise.radium.adminapi.entity.User;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.req.GetAccountActivityListReq;
import cn.shrise.radium.adminapi.resp.user.AccountActivityItem;
import cn.shrise.radium.adminapi.resp.user.AccountActivityResp;
import cn.shrise.radium.adminapi.resp.user.UserDetail;
import cn.shrise.radium.authservice.AuthClient;
import cn.shrise.radium.authservice.req.AdminVerifyReq;
import cn.shrise.radium.authservice.resp.AdminVerifyResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ProductTypeConstant;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CompanyProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.MD5Utils;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.DepartmentStaffHistoryDto;
import cn.shrise.radium.userservice.dto.UserDTO;
import cn.shrise.radium.userservice.entity.UcAccountActivity;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.req.DeviceInfoReq;
import cn.shrise.radium.userservice.req.GetAccountActivityReq;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import ua_parser.Client;
import ua_parser.Parser;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.DateUtils.localDateTimeToInstant;
import static cn.shrise.radium.userservice.constant.UserTypeConstant.CUSTOMER;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserClient userClient;
    private final AuthClient authClient;
    private final MD5Utils md5Utils;
    private final CompanyProperties companyProperties;
    private final static List<Integer> ProductTypeList = Arrays.asList(ProductTypeConstant.JCJ_PC, ProductTypeConstant.ZT_PC, ProductTypeConstant.KSZT_PC);
    private final static List<Integer> AppProductTypeList = Arrays.asList(ProductTypeConstant.APP_IOS, ProductTypeConstant.APP_ANDROID);

    public PageResult<List<UcUsers>> getUserList(Integer companyType, Integer userType, Boolean enabled, Boolean asc, Integer current, Integer size) {
        PageResult<List<UcUsers>> result = userClient.getUserList(companyType, userType, enabled, null, asc, current, size);
        log.info("userClient.getUserList code: {}, msg: {}", result.getCode(), result.getMsg());
        if (!result.isSuccess()) {
            throw new BusinessException(result);
        }
        return result;
    }

    public UcUsers getUser(Integer userId) {
        BaseResult<UcUsers> result = userClient.getUser(userId);
        if (!result.isSuccess()) {
            throw new BusinessException(result);
        }
        return result.getData();
    }

    public UserDetail searchUser(int userId, Integer companyType, Integer userType) {
        BaseResult<UcUsers> result = userClient.searchUser(String.valueOf(userId), companyType,userType);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        UcUsers users = result.getData();
        users.setMobile(users.getMaskMobile());
        UserDetail userDetail = new UserDetail();
        BeanUtils.copyProperties(users, userDetail);
        userDetail.setUserCode(DesensitizeUtil.idToMask(users.getId()));
        return userDetail;
    }

    public CustomerInfo getCustomerInfo(String userNumber, Integer companyType) {
        BaseResult<UcUsers> result = userClient.getUserByNumber(companyType, CUSTOMER, userNumber, true);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        return CustomerInfo.of(result.getData());
    }

    public BaseResult<List<UcUsers>> getUserListByDepartmentId(List<Integer> userIds) {
        BaseResult<List<UcUsers>> userLists = userClient.batchGetUserList(BatchReq.create(userIds));
        if (userLists.isFail()) {
            throw new BusinessException(userLists);
        }
        List<UcUsers> usersList = userLists.getData();
        usersList.sort((o1, o2) -> {
            return o2.getId().compareTo(o1.getId());
        });
        return BaseResult.success(usersList);

    }

    public List<User> getUserListByDepartmentId(Integer deptId, Boolean enabled, Integer userId, Integer companyType) {
        BaseResult<List<UserDTO>> result = userClient.getUsersByDepartmentId(deptId, enabled, userId, companyType);
        log.info("userClient.getUsersByDepartmentId code: {}, msg: {}", result.getCode(), result.getMsg());
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<User> users = new ArrayList<>();
        for (UserDTO datum : result.getData()) {
            User user = new User(datum.getId(), datum.getUserType(), datum.getCompanyType(), datum.getUserName(), datum.getResetPassword(), datum.getNumber(), datum.getNickName(), datum.getRealName(), datum.getAvatarUrl(), datum.getRemark(), datum.getDepartment(), datum.getEnabled(), null, null, null);
            users.add(user);
        }
        return users;
    }

    public List<User> getUserListWithDepartment(Integer companyType, Boolean enabled) {
        BaseResult<List<UserDTO>> result = userClient.getUserListWithDepartment(companyType, enabled);
        log.info("userClient.getUserListWithDepartment code: {}, msg: {}", result.getCode(), result.getMsg());
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<User> users = new ArrayList<>();
        for (UserDTO datum : result.getData()) {
            User user = new User(datum.getId(), datum.getUserType(), datum.getCompanyType(), datum.getUserName(), datum.getResetPassword(), datum.getNumber(), datum.getNickName(), datum.getRealName(), datum.getAvatarUrl(), datum.getRemark(), datum.getDepartment(), datum.getEnabled(), null, null, null);
            users.add(user);
        }
        return users;
    }

    public PageResult<List<UcUsers>> getUsersByDeptId(List<Integer> deptIds, LocalDate flagTime, Integer current, Integer size) {
        BaseResult<List<DepartmentStaffHistoryDto>> departmentStaffHistory = userClient.getDepartmentStaffHistory(deptIds, flagTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        if (departmentStaffHistory.isFail()) {
            throw new BusinessException(departmentStaffHistory);
        }
        List<DepartmentStaffHistoryDto> staffHistoryData = departmentStaffHistory.getData();
        List<Integer> userIdList = staffHistoryData.get(0).getWorkUserIdList();
        return userClient.getUsersByDeptId(userIdList, current, size);
    }

    public BaseResult<List<UcUsers>> getAllUser() {
        return userClient.getAllUcUsers();
    }


    public PageResult<List<AccountActivityResp>> getAccountActivityByUserId(Integer userId, List<Integer> productTypeList, Integer current, Integer size) {
        PageResult<List<UcAccountActivity>> accountActivity = userClient.getAccountActivityByUserId(userId, productTypeList, current, size);
        List<AccountActivityResp> accountLogsResps = accountActivity.getData().stream().map(login -> {
            AccountActivityResp accountLogsResp = new AccountActivityResp();
            accountLogsResp.setLoginTime(login.getGmtCreate());
            accountLogsResp.setPlatform(login.getPlatform());
            accountLogsResp.setDeviceInfo(login.getDeviceInfo());
            accountLogsResp.setProductType(login.getProductType());
            accountLogsResp.setIp(login.getIp());
            accountLogsResp.setId(login.getId());
            if (ProductTypeList.contains(login.getProductType())) {
                if (ObjectUtil.isNotEmpty(login.getDeviceInfo()) && login.getDeviceInfo().contains("\"macAddress\"")) {
                    DeviceInfoReq deviceInfoReq = JSON.parseObject(JSON.parse(login.getDeviceInfo()).toString(), DeviceInfoReq.class);
                    if (ObjectUtil.isNotEmpty(deviceInfoReq)) {
                        accountLogsResp.setDeviceInfo(deviceInfoReq.getMacAddress());
                        accountLogsResp.setVersion(deviceInfoReq.getVersion());
                    }
                }
            } else if (AppProductTypeList.contains(login.getProductType())) {
                accountLogsResp.setDeviceInfo(login.getDeviceInfo());
            }
            return accountLogsResp;
        }).collect(Collectors.toList());
        return PageResult.success(accountLogsResps, accountActivity.getPagination());
    }

    public BaseResult<AccountActivityResp> getLatestAccountActivity(Integer userId, List<Integer> productTypeList) {
        UcAccountActivity data = userClient.getAccountLastLogin(userId, productTypeList).getData();
        if (ObjectUtil.isNotEmpty(data)) {
            AccountActivityResp accountActivityResp = AccountActivityResp.builder()
                    .id(data.getId())
                    .loginTime(data.getGmtCreate())
                    .platform(data.getPlatform())
                    .deviceInfo(data.getDeviceInfo())
                    .productType(data.getProductType())
                    .ip(data.getIp())
                    .build();
            return BaseResult.success(accountActivityResp);
        }
        return BaseResult.success(null);
    }

    public PageResult<List<AccountActivityItem>> getAccountActivityList(GetAccountActivityListReq req) {
        LocalDateTime startDate = req.getStartDate();
        LocalDateTime endDate = req.getEndDate();
        Integer current = req.getCurrent();
        Integer size = req.getSize();
        Instant startTime = startDate == null ? null : localDateTimeToInstant(startDate);
        Instant endTime = endDate == null ? null : localDateTimeToInstant(endDate);

        GetAccountActivityReq getAccountActivityReq = GetAccountActivityReq.builder()
                .userIds(req.getUserIds())
                .startTime(startTime)
                .endTime(endTime)
                .current(current)
                .size(size)
                .build();
        PageResult<List<UcAccountActivity>> pageResult = userClient.getAccountActivityList(getAccountActivityReq);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }

        List<UcAccountActivity> data = pageResult.getData();
        Set<Integer> userIdSet = data.stream().map(UcAccountActivity::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElse(Collections.emptyMap());

        List<AccountActivityItem> items = data.stream().map(e -> {
            String deviceInfo = e.getDeviceInfo();
            JSONObject jsonObject = JSONObject.parseObject(deviceInfo);
            String ua = jsonObject.getString("ua");

            String os = "";
            String browser = "";
            if (ObjectUtils.isNotEmpty(ua)) {
                Parser uaParser = new Parser();
                Client c = uaParser.parse(ua);
                os = c.os.family;
                browser = c.userAgent.family;
            }

            return AccountActivityItem.builder()
                    .id(e.getId())
                    .companyType(e.getCompanyType())
                    .productType(e.getProductType())
                    .userId(e.getUserId())
                    .userInfo(UserInfo.of(usersMap.get(e.getUserId())))
                    .platform(e.getPlatform())
                    .ip(e.getIp())
                    .gmtCreate(e.getGmtCreate())
                    .deviceInfo(deviceInfo)
                    .os(os)
                    .browser(browser)
                    .build();
        }).collect(Collectors.toList());

        return PageResult.success(items, pageResult.getPagination());
    }

    public BaseResult<UcUsers> auditUser(AdminVerifyReq req) {
        AdminVerifyResp verifyResp = authClient.adminVerify(req).orElseThrow();
        UcUsers user = verifyResp.getUser();
        return BaseResult.success(user);
    }

    public List<User> getAllUsersByDepartmentId(Integer deptId, Boolean enabled, Integer companyType) {
        BaseResult<List<UserDTO>> result = userClient.getAllUsersByDepartmentId(deptId, enabled, companyType);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<User> users = new ArrayList<>();
        for (UserDTO datum : result.getData()) {
            User user = new User(datum.getId(), datum.getUserType(), datum.getCompanyType(), datum.getUserName(), datum.getResetPassword(), datum.getNumber(), datum.getNickName(), datum.getRealName(), datum.getAvatarUrl(), datum.getRemark(), datum.getDepartment(), datum.getEnabled(), null, null, null);
            users.add(user);
        }
        return users;
    }

    public BaseResult<List<UcUsers>> getRoleUserList(Long roleId) {
        BaseResult<List<Integer>> result = userClient.getUserListByRole(roleId);
        if (result.isPresent()) {
            BaseResult<List<UcUsers>> result1 = userClient.batchGetUserList(BatchReq.create(result.getData()));
            if (result1.isFail()) {
                throw new BusinessException(result1);
            }
            if (!result1.isPresent()) {
                return result1;
            }
            List<UcUsers> data = result1.getData();
            List<UcUsers> resps = data.stream().peek(e -> {
                if (ObjectUtils.isNotEmpty(e.getMobile())) {
                    e.setMobile(DesensitizedUtil.mobilePhone(e.getMobile()));
                }
            }).collect(Collectors.toList());
            return BaseResult.success(resps);
        }
        return BaseResult.success(null);
    }

//    public PageResult<List<CallLogResp>> findCallLogByFilter(
//            Integer companyType, Integer userId, Boolean callType, Integer callStatus, LocalDateTime startTime,
//            LocalDateTime endTime, String mobile, String number, String callId, String searchContent, Integer current, Integer size) {
//        PageResult<List<CallLogResp>> result = userClient.findCallLogByFilter(companyType, userId, callType, callStatus,
//                startTime, endTime, mobile, number, callId, searchContent, current, size);
//        if (result.isFail()) {
//            throw new BusinessException(result);
//        }
//        if (!result.isPresent()) {
//            return result;
//        }
//        List<CallLogResp> data = result.getData();
//        List<CallLogResp> resps = data.stream().peek(e -> {
//            if (ObjectUtils.isNotEmpty(e.getCustomerNumber())) {
//                e.setCustomerNumber(DesensitizedUtil.mobilePhone(e.getCustomerNumber()));
//            }
//        }).collect(Collectors.toList());
//        return PageResult.success(resps, result.getPagination());
//    }
}

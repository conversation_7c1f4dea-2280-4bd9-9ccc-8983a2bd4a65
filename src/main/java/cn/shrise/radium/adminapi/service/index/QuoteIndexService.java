package cn.shrise.radium.adminapi.service.index;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.req.QuoteIndexInfoReq;
import cn.shrise.radium.contentservice.req.QuoteIndexSortReq;
import cn.shrise.radium.contentservice.resp.index.QuoteIndexResp;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2024/12/20 13:37
 * @Desc:
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class QuoteIndexService {

    private final ContentClient contentClient;

    public BaseResult<Void> updateIndexSort(QuoteIndexSortReq req) {

        Map<Long, Integer> sortMap = new HashMap<>();
        //获取当前类型和位置下全部已上架指标
        BaseResult<List<QuoteIndexResp>> IndexList = contentClient.getIndexList(req.getType(), req.getPosition(), true);
        List<Long> reqIdList = req.getIdList();
        List<Long> IdList = IndexList.getData().stream().map(QuoteIndexResp::getId).collect(Collectors.toList());
        //校验传递列表是否完整
        if (!compareList(reqIdList, IdList)) {
            throw new BusinessException("状态已更新，请刷新重试");
        }
        //获取当前排序值
        for (int i = 0; i < reqIdList.size(); i++) {
            sortMap.put(reqIdList.get(i), i + 1);
        }
        String param = JSON.toJSONString(sortMap);
        contentClient.updateIndexSort(param);
        return BaseResult.successful();
    }

    public boolean compareList(List<Long> l1, List<Long> l2) {
        if (l1.size() != l2.size()) {
            return false;
        }
        return l1.stream().allMatch(l2::contains);
    }

    public BaseResult<Void> setEnabled(Long id, Boolean enabled) {
        if (ObjectUtil.equals(enabled, true)) {
            //查询当前id对应的type, position
            QuoteIndexResp resp = contentClient.getTypeAndPosition(id).getData();
            //获取当前类型和位置下最大排序值
            final Integer maxSort = contentClient.getMaxSort(resp.getType(), resp.getPosition()).getData();
            final Integer sort = maxSort + 1;
            //更新排序值和启用状态
            contentClient.setEnabled(id, enabled, sort);
        } else {
            contentClient.setEnabled(id, enabled, null);
        }
        return BaseResult.successful();
    }

    public BaseResult<Void> createOrEditIndex(QuoteIndexInfoReq req) {
        if (ObjectUtil.isEmpty(req.getId())) {
            final Integer maxSort = contentClient.getMaxSort(req.getType(), req.getPosition()).getData();
            final Integer sort = maxSort + 1;
            req.setSort(sort);
            return contentClient.createIndex(req);
        } else {
            return contentClient.editIndex(req);
        }
    }
}

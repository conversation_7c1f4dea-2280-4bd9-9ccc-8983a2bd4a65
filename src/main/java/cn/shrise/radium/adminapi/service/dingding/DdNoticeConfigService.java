package cn.shrise.radium.adminapi.service.dingding;

import cn.shrise.radium.adminapi.resp.dingding.DingDingNoticeConfigRecordResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.dingdingservice.DingDingClient;
import cn.shrise.radium.dingdingservice.entity.DdNoticeConfigRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DdNoticeConfigService {

    private final DingDingClient dingDingClient;
    private final UserClient userClient;

    public PageResult<List<DingDingNoticeConfigRecordResp>> getDingDingNoticeConfigRecordList(Integer current, Integer size) {
        PageResult<List<DdNoticeConfigRecord>> respResult = dingDingClient.getDingDingNoticeConfigRecordList(current, size);
        if (respResult.isFail()) {
            throw new BusinessException(respResult);
        }
        List<DdNoticeConfigRecord> records = respResult.getData();
        Set<Integer> userIdSet = records.stream().map(DdNoticeConfigRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<DingDingNoticeConfigRecordResp> respList = records.stream()
                .map(e -> DingDingNoticeConfigRecordResp.builder()
                        .gmtCreate(e.getGmtCreate())
                        .operatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName())
                        .content(e.getContent())
                        .build())
                .collect(Collectors.toList());
        return PageResult.success(respList, respResult.getPagination());
    }
}

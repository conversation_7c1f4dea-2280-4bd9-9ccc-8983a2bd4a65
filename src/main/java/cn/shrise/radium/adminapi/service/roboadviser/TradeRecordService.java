package cn.shrise.radium.adminapi.service.roboadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.resp.TradeRecordResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.req.DeviceInfoReq;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TradeRecordService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;

    private final UserClient userClient;

    public PageResult<List<TradeRecordResp>> getUserTradeRecordList(String userCode, String searchContent, LocalDate startDate, LocalDate endDate, Integer current, Integer size) {
        Integer userId = StringUtils.isNotBlank(userCode) ? DesensitizeUtil.maskToId(userCode) : null;
        PageResult<List<TradeRecordResp>> pageResult = roboAdviserServiceClient.getUserTradeRecordList(userId, searchContent, startDate, endDate, current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<TradeRecordResp> respList = pageResult.getData();
        List<Integer> userList = respList.stream().map(TradeRecordResp::getUserId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userList)).orElseThrow();
        respList.forEach(resp -> {
            resp.setNickName(usersMap.getOrDefault(resp.getUserId(), new UcUsers()).getNickName());
            if (ObjectUtil.isNotEmpty(resp.getDeviceInfo())) {
                DeviceInfoReq deviceInfoReq = JSON.parseObject(JSON.parse(resp.getDeviceInfo()).toString(), DeviceInfoReq.class);
                if (ObjectUtil.isNotEmpty(deviceInfoReq)) {
                    resp.setDeviceInfo(deviceInfoReq.getMacAddress());
                }
            }
            resp.setUserCode(DesensitizeUtil.idToMask(resp.getUserId()));
        });
        return pageResult;
    }
}

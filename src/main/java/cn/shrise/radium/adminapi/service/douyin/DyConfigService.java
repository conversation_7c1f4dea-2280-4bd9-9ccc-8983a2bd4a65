package cn.shrise.radium.adminapi.service.douyin;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.BaseConfig;
import cn.shrise.radium.douyinservice.DouYinClient;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DyConfigService {

    private final DouYinClient douYinClient;

    public List<BaseConfig> getClientList(Integer companyType) {
        final BaseResult<List<BaseConfig>> result = douYinClient.getClientList(companyType);
        if (result.isFail()) {
            return Collections.emptyList();
        }
        return result.getData();
    }
}

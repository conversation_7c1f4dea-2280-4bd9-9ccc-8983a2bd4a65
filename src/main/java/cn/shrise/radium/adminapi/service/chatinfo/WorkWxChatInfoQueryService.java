package cn.shrise.radium.adminapi.service.chatinfo;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.chat.ChatQueryRecordResp;
import cn.shrise.radium.adminapi.resp.chat.ChatQueryResultResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.NpWorkWxChatQueryRecord;
import cn.shrise.radium.workwxservice.entity.NpWorkWxChatQueryResult;
import cn.shrise.radium.workwxservice.entity.NpWorkWxUser;
import cn.shrise.radium.workwxservice.property.WxCpProperties;
import cn.shrise.radium.workwxservice.req.chat.ChatQueryReq;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkWxChatInfoQueryService {

    private final WorkwxClient workwxClient;
    private final UserClient userClient;

    public PageResult<List<ChatQueryResultResp>> getChatQueryResultList(Long recordId, Integer accountType, Boolean asc, Integer current, Integer size) {

        PageResult<List<NpWorkWxChatQueryResult>> pageResult = workwxClient.getChatQueryResultList(recordId, accountType, asc, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<NpWorkWxChatQueryResult> results = pageResult.getData();
        if (ObjectUtil.isEmpty(results)) {
            return PageResult.empty();
        }
        Map<Integer, WxCpProperties> wxCpPropertiesMap = workwxClient.getAccountConfig().getData();
        Set<String> wxAccountSet = results.stream().map(NpWorkWxChatQueryResult::getWxAccount).collect(Collectors.toSet());
        List<NpWorkWxUser> workWxUsers = workwxClient.getAccountInfoList(wxAccountSet);
        Map<String, List<NpWorkWxUser>> wxAccountMap = new HashMap<>();
        workWxUsers.forEach(workWxUser -> {
            String wxAccount = workWxUser.getWxAccount();
            if (wxAccountMap.containsKey(wxAccount)) {
                List<NpWorkWxUser> userList = wxAccountMap.get(wxAccount);
                if (!userList.contains(workWxUser)) {
                    userList.add(workWxUser);
                    wxAccountMap.put(wxAccount, userList);
                }
            } else {
                List<NpWorkWxUser> list = new ArrayList<>();
                list.add(workWxUser);
                wxAccountMap.put(wxAccount, list);
            }
        });
        List<ChatQueryResultResp> respList = results.stream().map(result -> {
            String userName = null;
            List<NpWorkWxUser> wxUsers = wxAccountMap.getOrDefault(result.getWxAccount(), new ArrayList<>());
            if (ObjectUtil.isNotEmpty(wxUsers)) {
                NpWorkWxUser workWxUser = wxUsers.stream().filter(wxUser -> wxUser.getAccountType().equals(result.getAccountType())).findFirst().orElse(new NpWorkWxUser());
                if (ObjectUtil.isNotEmpty(workWxUser)) {
                    userName = workWxUser.getName();
                }
            }
            return ChatQueryResultResp.builder()
                    .id(result.getId())
                    .accountType(result.getAccountType())
                    .wxAccount(result.getWxAccount())
                    .corpName(wxCpPropertiesMap.getOrDefault(result.getAccountType(), new WxCpProperties()).getCorpName())
                    .userName(userName)
                    .msgCount(result.getMsgCount())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(respList, pageResult.getPagination());
    }

    public PageResult<List<ChatQueryRecordResp>> getChatQueryRecordList(Integer companyType, Integer current, Integer size) {
        PageResult<List<NpWorkWxChatQueryRecord>> pageResult = workwxClient.getChatQueryRecordList(companyType, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<NpWorkWxChatQueryRecord> recordList = pageResult.getData();
        Set<Integer> userIdSet = recordList.stream().map(NpWorkWxChatQueryRecord::getCreatorId).collect(Collectors.toSet());
        Map<Integer, UserBaseInfoResp> userBaseInfoRespMap = userClient.batchGetBaseUserMap(BatchReq.create(userIdSet)).orElseThrow();
        List<ChatQueryRecordResp> resps = recordList.stream().map(record -> {
            return ChatQueryRecordResp.builder()
                    .id(record.getId())
                    .creatorName(userBaseInfoRespMap.getOrDefault(record.getCreatorId(), new UserBaseInfoResp()).getRealName())
                    .status(record.getStatus())
                    .completeTime(record.getCompleteTime())
                    .gmtCreate(record.getGmtCreate())
                    .chatQueryReq(JSONObject.parseObject(record.getQueryCondition(), ChatQueryReq.class))
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }
}

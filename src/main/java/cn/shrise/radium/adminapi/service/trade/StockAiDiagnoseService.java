package cn.shrise.radium.adminapi.service.trade;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.StockUtils;
import cn.shrise.radium.stockaidiagnoseservice.StockAiDiagnoseClient;
import cn.shrise.radium.stockaidiagnoseservice.resp.StockAiDiagnoseResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcWxExt;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.DesensitizeUtil.isValidUserCode;
import static cn.shrise.radium.common.util.DesensitizeUtil.maskToId;

/**
 * @Author: tangjiajun
 * @Date: 2025/3/19 15:34
 * @Desc:
 **/
@Service
@RequiredArgsConstructor
public class StockAiDiagnoseService {

    private final StockAiDiagnoseClient stockAiDiagnoseClient;
    private final UserClient userClient;

    public PageResult<List<StockAiDiagnoseResp>> stockAiDiagnoseList(LocalDate startCreateTime, LocalDate endCreateTime, Integer sourceType,
                                                                     String searchContent, Integer current, Integer size) {
        Integer userId = null;
        String stockCode = null;
        if (ObjectUtil.isNotEmpty(searchContent)) {
            if (isValidUserCode(searchContent)) { //搜索内容可精确搜索用户code或股票代码
                userId = maskToId(searchContent);
            } else {
                stockCode = StockUtils.isStockCode(searchContent) ? StockUtils.stockLabelFormat(searchContent) : searchContent;
            }
        }
        PageResult<List<StockAiDiagnoseResp>> result = stockAiDiagnoseClient.stockAiDiagnoseList(startCreateTime, endCreateTime, sourceType, userId, stockCode, current, size);
        if (ObjectUtils.isEmpty(result)) {
            return PageResult.empty();
        }
        Set<Integer> wxIds = result.getData().stream().map(StockAiDiagnoseResp::getWxId).collect(Collectors.toSet());
        Map<Integer, UcWxExt> extMapByWxId = userClient.getUnionIds(wxIds).getData().stream().collect(Collectors.toMap(UcWxExt::getId, i -> i));
        //过滤出wxId为空的记录，去UcWxExt表实时查询对应的微信信息
        Set<Integer> userIds = result.getData().stream().filter(i -> ObjectUtil.isEmpty(i.getWxId())).map(StockAiDiagnoseResp::getUserId).collect(Collectors.toSet());
        Map<Integer, UcWxExt> extMapByUserId = userClient.getUnionIdsByUserId(new ArrayList<>(userIds)).getData().stream().collect(Collectors.toMap(UcWxExt::getUserId, i -> i));
        result.getData().forEach(i -> {
            if (ObjectUtil.isEmpty(i.getWxId())) {
                i.setWxId(extMapByUserId.getOrDefault(i.getUserId(), new UcWxExt()).getId());
                i.setWxNickName(extMapByUserId.getOrDefault(i.getUserId(), new UcWxExt()).getNickname());
            } else {
                i.setWxNickName(extMapByWxId.getOrDefault(i.getWxId(), new UcWxExt()).getNickname());
            }
            i.setUserCode(DesensitizeUtil.idToMask(i.getUserId()));
        });
        return result;
    }
}

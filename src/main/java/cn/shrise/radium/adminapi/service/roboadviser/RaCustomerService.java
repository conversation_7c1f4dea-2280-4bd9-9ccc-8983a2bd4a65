package cn.shrise.radium.adminapi.service.roboadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.roboadviser.CustomerTradeAuthRecordResp;
import cn.shrise.radium.adminapi.resp.roboadviser.CustomerTradeAuthResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.entity.RaCustomerTradeAuth;
import cn.shrise.radium.roboadviserservice.entity.RaCustomerTradeAuthRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RaCustomerService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final UserClient userClient;

    public PageResult<List<CustomerTradeAuthRecordResp>> getRaCustomerTradeAuthRecordList(Integer userId, Boolean isAuth, Integer current, Integer size) {
        PageResult<List<RaCustomerTradeAuthRecord>> pageResult = roboAdviserServiceClient.getRaCustomerTradeAuthRecordList(userId, isAuth, current, size);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<RaCustomerTradeAuthRecord> result = pageResult.getData();
        Set<Integer> userSet = result.stream().map(RaCustomerTradeAuthRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMapResult = userClient.batchGetUserMap(BatchReq.create(userSet)).orElse(new HashMap<>());
        List<CustomerTradeAuthRecordResp> respList = result.stream().map(i -> {
            CustomerTradeAuthRecordResp resp = new CustomerTradeAuthRecordResp();
            BeanUtils.copyProperties(i, resp);
            resp.setOperatorName(userMapResult.getOrDefault(i.getOperatorId(), new UcUsers()).getRealName());
            resp.setOperatorNickName(userMapResult.getOrDefault(i.getOperatorId(), new UcUsers()).getNickName());
            resp.setUserCode(DesensitizeUtil.idToMask(i.getUserId()));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, pageResult.getPagination());
    }

    public BaseResult<CustomerTradeAuthResp> getRaCustomerTradeAuthInfo(Integer userId) {
        RaCustomerTradeAuth result = roboAdviserServiceClient.getRaCustomerTradeAuthInfo(userId).orElse(null);
        if (ObjectUtil.isEmpty(result)) {
            return BaseResult.success(null);
        }
        CustomerTradeAuthResp resp = new CustomerTradeAuthResp();
        BeanUtils.copyProperties(result, resp);
        return BaseResult.success(resp);
    }

}

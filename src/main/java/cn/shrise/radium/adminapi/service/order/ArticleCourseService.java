package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.order.ArticleCourseResp;
import cn.shrise.radium.adminapi.resp.trade.RecommendStockResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.dto.ArticleCourseDto;
import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.orderservice.entity.ArticleSeriesOperatorRelation;
import cn.shrise.radium.orderservice.entity.RsArticleCourse;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.dto.RecommendStockDto;
import cn.shrise.radium.tradeservice.entity.TdRecommendStock;
import cn.shrise.radium.tradeservice.entity.TdRecommendStockType;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleCourseService {

    private final UserClient userClient;
    private final ContentClient contentClient;
    private final OrderClient orderClient;

    public PageResult<List<ArticleCourseResp>> getArticleCourseList(
            Integer seriesId, String searchText, Integer status, Integer timeType, LocalDateTime startTime,
            LocalDateTime endTime, Integer orderType, Boolean isDesc, Boolean isTop, Integer current, Integer size){

        List<ArticleSeriesOperatorRelation> managerRelations = orderClient.getResearchReportManagerRelations(null, AuthContextHolder.getUserId()).orElse(Collections.emptyList());
        Set<Integer> relateSeriesIds = managerRelations.stream().map(ArticleSeriesOperatorRelation::getSeriesId).collect(Collectors.toSet());

        if (relateSeriesIds.size() == 0){
            return PageResult.success(null, Pagination.of(current, size));
        }

        PageResult<List<ArticleCourseDto>> articleListResp = orderClient.getArticleCourseList(AuthContextHolder.getCompanyType(), relateSeriesIds, seriesId, searchText, status, orderType, timeType, startTime, endTime,
                isDesc, isTop, current, size);
        if (ObjectUtil.isEmpty(articleListResp.getData())){
            return PageResult.success(null, null);
        }
        List<Integer> seriesIds = articleListResp.getData().stream().map(i -> i.getArticleInfo().getSeriesId()).collect(Collectors.toList());
        BaseResult<List<ArticleSeries>> seriesListResp = orderClient.filterArticleSeries(seriesIds, AuthContextHolder.getCompanyType(), null, null);
        Map<Integer, String> typeNameMap = new HashMap<>();
        Map<Integer, Integer> seriesTypeMap = new HashMap<>();
        if (seriesListResp.isSuccess()){
            typeNameMap = seriesListResp.getData().stream().collect(Collectors.toMap(ArticleSeries::getId, ArticleSeries::getName));
            seriesTypeMap = seriesListResp.getData().stream().collect(Collectors.toMap(ArticleSeries::getId, ArticleSeries::getType));
        }
        Set<Integer> userIds = new HashSet<>();
        List<Integer> analystIds = new ArrayList<>();
        articleListResp.getData().forEach(i -> {
            RsArticleCourse article = i.getArticleInfo();
            userIds.add(article.getCreatorId());
            userIds.add(article.getAuditorId());
            analystIds.add(article.getAnalystId());
        });

        Map<Integer, UserBaseInfoResp> userMap = userClient.batchGetBaseUserMap(BatchReq.create(userIds)).getData();
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, AuthContextHolder.getCompanyType(), null).getData();

        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i->i));

        Map<Integer, String> finalTypeNameMap = typeNameMap;
        Map<Integer, Integer> finalSeriesTypeMap = seriesTypeMap;
        List<ArticleCourseResp> recordRespList = articleListResp.getData().stream().map(i -> {
            RsArticleCourse article = i.getArticleInfo();
            SsAnalystInfo analystInfo = analystInfoMap.getOrDefault(article.getAnalystId(), new SsAnalystInfo());
            ArticleCourseResp r = new ArticleCourseResp();
            BeanUtil.copyProperties(article, r);
            r.setSeriesId(article.getSeriesId());
            r.setSeriesName(finalTypeNameMap.get(article.getSeriesId()));
            r.setSeriesType(finalSeriesTypeMap.get(article.getSeriesId()));
            r.setCreatorInfo(userMap.get(article.getCreatorId()));
            r.setAnalystId(analystInfo.getId());
            r.setAnalystName(analystInfo.getName());
            r.setCodeList(i.getCodeList());
            r.setTagList(i.getTagList());
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, articleListResp.getPagination());
    }

}

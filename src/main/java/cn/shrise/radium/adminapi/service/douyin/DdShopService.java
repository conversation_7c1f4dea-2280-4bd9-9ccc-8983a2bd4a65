package cn.shrise.radium.adminapi.service.douyin;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.adminapi.resp.douyin.DdShopOrderResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.douyinservice.DouYinClient;
import cn.shrise.radium.douyinservice.entity.DdShopOrder;
import cn.shrise.radium.douyinservice.resp.DouDianShopInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DdShopService {

    private final DouYinClient douYinClient;

    public PageResult<List<DdShopOrderResp>> getDdShopOrderList(Long shopId, Integer status, LocalDate startTime, LocalDate endTime, String searchContent, Integer current, Integer size) {
        PageResult<List<DdShopOrder>> pageResult = douYinClient.getDdShopOrderList(shopId, status, startTime, endTime, searchContent, current, size);
        if (pageResult.isSuccess()) {
            List<DdShopOrder> shopOrderList = pageResult.getData();
            List<DdShopOrderResp> ddShopOrderRespList = new ArrayList<>();
            BaseResult<List<DouDianShopInfo>> ddShopListResult = douYinClient.getDdShopList();
            Map<Long, String> shopMap = new HashMap<>();
            if (ddShopListResult.isSuccess()) {
                List<DouDianShopInfo> shopInfoList = ddShopListResult.getData();
                for (DouDianShopInfo shopInfo : shopInfoList) {
                    shopMap.put(shopInfo.getShopId(), shopInfo.getShopName());
                }
            }
            for (DdShopOrder shopOrder : shopOrderList) {
                DdShopOrderResp ddShopOrderResp = new DdShopOrderResp();
                BeanUtil.copyProperties(shopOrder, ddShopOrderResp);
                if (shopMap.containsKey(shopOrder.getShopId())) {
                    ddShopOrderResp.setShopName(shopMap.get(shopOrder.getShopId()));
                }
                ddShopOrderRespList.add(ddShopOrderResp);
            }
            return PageResult.success(ddShopOrderRespList, pageResult.getPagination());
        }
        return PageResult.empty(current, size);
    }
}

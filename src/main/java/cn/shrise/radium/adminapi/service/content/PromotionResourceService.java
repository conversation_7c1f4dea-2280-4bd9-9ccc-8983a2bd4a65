package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.PromotionResourceFileResp;
import cn.shrise.radium.adminapi.resp.content.PromotionResourceFolderResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.resp.PromotionResourceWxLinkResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcWxAccount;
import cn.shrise.radium.userservice.entity.UcWxExt;
import com.aliyun.oss.model.ListObjectsV2Result;
import com.aliyun.oss.model.OSSObjectSummary;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PromotionResourceService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PromotionResourceFolderResp getPromotionResourceFolder(String cursor, Integer size) {
        BaseResult<ListObjectsV2Result> result = contentClient.getPromotionResourceFolder(cursor, size);
        if (!result.isPresent()) {
            return null;
        }
        ListObjectsV2Result listObjectsV2Result = result.getData();
        List<String> folderNames = listObjectsV2Result.getCommonPrefixes();
        PromotionResourceFolderResp folderResp = new PromotionResourceFolderResp();
        List<PromotionResourceFolderResp.Folder> folders = new ArrayList<>();
        for (String folderName : folderNames) {
            PromotionResourceFolderResp.Folder folder = new PromotionResourceFolderResp.Folder();
            folder.setName(folderName.substring(0, folderName.length() - 1));
            folders.add(folder);
        }
        folderResp.setNextCursor(listObjectsV2Result.getNextContinuationToken());
        folderResp.setFolders(folders);
        return folderResp;
    }

    public BaseResult<Void> createPromotionResourceFolder(String folderName, Integer userId) {
        return contentClient.createPromotionResourceFolder(folderName, userId);
    }

    public BaseResult<Void> deletePromotionResourceFolder(String folderName, Integer userId) {
        return contentClient.deletePromotionResourceFolder(folderName, userId);
    }

    public PromotionResourceFileResp getPromotionResourceFile(String folderName, String cursor, Integer size) {
        BaseResult<ListObjectsV2Result> result = contentClient.getPromotionResourceFile(folderName + "/", cursor, size);
        if (!result.isPresent()) {
            return null;
        }
        ListObjectsV2Result listObjectsV2Result = result.getData();
        List<OSSObjectSummary> fileInfoList = listObjectsV2Result.getObjectSummaries();
        PromotionResourceFileResp fileResp = new PromotionResourceFileResp();
        List<PromotionResourceFileResp.File> files = new ArrayList<>();
        for (OSSObjectSummary fileInfo : fileInfoList) {
            PromotionResourceFileResp.File file = new PromotionResourceFileResp.File();
            String path = fileInfo.getKey();
            file.setName(path.substring(path.indexOf("/") + 1, path.lastIndexOf(".")));
            file.setSize(fileInfo.getSize());
            file.setPath(path);
            file.setType(path.substring(path.lastIndexOf("."), path.length()));
            file.setLastModified(fileInfo.getLastModified());
            files.add(file);
        }
        fileResp.setNextCursor(listObjectsV2Result.getNextContinuationToken());
        fileResp.setFiles(files);
        return fileResp;
    }

    public BaseResult<Void> deletePromotionResourceFile(String path, Integer userId) {
        return contentClient.deletePromotionResourceFile(path, userId);
    }

    public BaseResult<URL> getFileUrl(String originUrl) {
        return contentClient.getFileUrl(originUrl, 20);
    }

    public String promotionResourceLinkUrl(String folderName, Integer dayType) {
        return contentClient.promotionResourceLinkUrl(folderName, dayType, null).orElse("");
    }

    public PageResult<List<PromotionResourceWxLinkResp>> promotionResourceWxLinkList(Integer current, Integer size) {
        PageResult<List<PromotionResourceWxLinkResp>> result = contentClient.promotionResourceWxLinkList(current, size);
        if (ObjectUtil.isEmpty(result)) {
            return PageResult.empty();
        }
        Set<Integer> wxIdSet = result.getData().stream().map(PromotionResourceWxLinkResp::getWxId).collect(Collectors.toSet());
        Map<Integer, UcWxExt> extMap = userClient.getUnionIds(wxIdSet).getData().stream().collect(Collectors.toMap(
                UcWxExt::getId,
                i -> i
        ));
        result.getData().forEach(i -> {
            i.setUnionId(extMap.getOrDefault(i.getWxId(), new UcWxExt()).getUnionId());
            i.setNickName(extMap.getOrDefault(i.getWxId(), new UcWxExt()).getNickname());
        });
        return result;
    }
}

package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.order.OrderFixInfoResp;
import cn.shrise.radium.adminapi.resp.order.OrderFixRecordResp;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.orderservice.entity.RsOrderFixInfo;
import cn.shrise.radium.orderservice.entity.RsOrderFixRecord;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderFixService {

    private final OrderClient orderClient;
    private final UserClient userClient;
    private final CommonService commonService;

    public PageResult<List<OrderFixRecordResp>> getOrderFixRecordList(Long fixId, Integer current, Integer size) {

        PageResult<List<RsOrderFixRecord>> pageResult = orderClient.getOrderFixRecordList(fixId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }

        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }

        List<RsOrderFixRecord> recordList = pageResult.getData();
        List<Integer> userIdList = recordList.stream().map(RsOrderFixRecord::getOperatorId).collect(Collectors.toList());
        List<Integer> salesIdList = recordList.stream().map(RsOrderFixRecord::getSalesId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        userIdList.addAll(salesIdList);
        BaseResult<Map<Integer, UcUsers>> mapBaseResult = userClient.batchGetUserMap(BatchReq.of(userIdList));
        if (mapBaseResult.isFail()) {
            throw new BusinessException(mapBaseResult);
        }
        Map<Integer, UcUsers> usersMap = mapBaseResult.getData();
        List<OrderFixRecordResp> resps = recordList.stream().map(record -> {
            OrderFixRecordResp resp = new OrderFixRecordResp();
            BeanUtil.copyProperties(record, resp);
            resp.setOperatorName(usersMap.getOrDefault(resp.getOperatorId(), new UcUsers()).getRealName());
            resp.setOperatorAvatarUrl(usersMap.getOrDefault(resp.getOperatorId(), new UcUsers()).getAvatarUrl());
            if (ObjectUtil.isNotEmpty(resp.getSalesId())) {
                resp.setSalesName(usersMap.getOrDefault(resp.getSalesId(), new UcUsers()).getRealName());
            }
            if (ObjectUtil.isNotEmpty(record.getImageUrls())) {
                List<String> imageUrlList = JSON.parseArray(record.getImageUrls(), String.class);
                resp.setImageUrls(imageUrlList);
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<OrderFixInfoResp>> getOrderFixInfoList(Integer companyType, String orderNumber, Integer current, Integer size) {

        Integer orderId = null;
        if (ObjectUtil.isNotEmpty(orderNumber)) {
            orderId = commonService.getOrderIdByCompanyNumber(companyType, orderNumber);
        }
        PageResult<List<RsOrderFixInfo>> pageResult = orderClient.getOrderFixInfoList(companyType, orderId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }

        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<RsOrderFixInfo> infoList = pageResult.getData();
        List<Integer> orderIdList = infoList.stream().map(RsOrderFixInfo::getOrderId).collect(Collectors.toList());
        List<Integer> fixSalesList = infoList.stream().map(RsOrderFixInfo::getSalesId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        BaseResult<List<RsCourseOrder>> orderResult = orderClient.batchGetOrderListById(orderIdList);
        if (orderResult.isFail()) {
            throw new BusinessException(orderResult);
        }
        if (ObjectUtil.isEmpty(orderResult.getData())) {
            return PageResult.empty();
        }
        BaseResult<Map<Integer, Integer>> mapBaseResult = orderClient.batchGetAllSubPaidAmount(orderIdList);
        if (mapBaseResult.isFail()) {
            throw new BusinessException(mapBaseResult);
        }
        Map<Integer, Integer> paidAmountMap = mapBaseResult.getData();
        Map<Integer, RsCourseOrder> orderMap = orderResult.getData().stream().collect(Collectors.toMap(RsCourseOrder::getId, o -> o));
        List<Integer> salesList = orderMap.values().stream().map(RsCourseOrder::getSalesId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        List<Integer> customerList = orderMap.values().stream().map(RsCourseOrder::getUserId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        Map<Integer, Integer> orderSkuMap = orderMap.values().stream().collect(Collectors.toMap(RsCourseOrder::getId, RsCourseOrder::getSkuId));
        fixSalesList.addAll(salesList);
        fixSalesList.addAll(customerList);
        BaseResult<Map<Integer, UcUsers>> userMapBaseResult = userClient.batchGetUserMap(BatchReq.of(fixSalesList));
        if (userMapBaseResult.isFail()) {
            throw new BusinessException(userMapBaseResult);
        }
        Map<Integer, UcUsers> usersMap = userMapBaseResult.getData();

        BaseResult<Map<Integer, RsSku>> skuMapBaseResult = orderClient.batchGetSkuMap(BatchReq.of(orderSkuMap.values()));
        if (skuMapBaseResult.isFail()) {
            throw new BusinessException(skuMapBaseResult);
        }
        Map<Integer, RsSku> skuMap = skuMapBaseResult.getData();

        List<OrderFixInfoResp> resps = infoList.stream().map(info -> {
            OrderFixInfoResp resp = OrderFixInfoResp.builder()
                    .id(info.getId())
                    .orderNumber(orderMap.getOrDefault(info.getOrderId(), new RsCourseOrder()).getOrderNumber())
                    .skuId(orderMap.getOrDefault(info.getOrderId(), new RsCourseOrder()).getSkuId())
                    .customerId(orderMap.getOrDefault(info.getOrderId(), new RsCourseOrder()).getUserId())
                    .openTime(orderMap.getOrDefault(info.getOrderId(), new RsCourseOrder()).getPayTime())
                    .salesId(orderMap.getOrDefault(info.getOrderId(), new RsCourseOrder()).getSalesId())
                    .fixOpenTime(info.getFixTime())
                    .fixSalesId(info.getSalesId())
                    .fixType(info.getFixType())
                    .gmtModified(info.getGmtModified())
                    .build();
            if (paidAmountMap.containsKey(info.getOrderId())){
                resp.setOrderAmount(paidAmountMap.get(info.getOrderId()));
            }
            resp.setSkuNumber(skuMap.getOrDefault(resp.getSkuId(), new RsSku()).getNumber());
            resp.setSkuName(skuMap.getOrDefault(resp.getSkuId(), new RsSku()).getName());
            resp.setCustomerName(usersMap.getOrDefault(resp.getCustomerId(), new UcUsers()).getNickName());
            resp.setSalesName(usersMap.getOrDefault(resp.getSalesId(), new UcUsers()).getRealName());
            resp.setFixSalesName(usersMap.getOrDefault(resp.getFixSalesId(), new UcUsers()).getRealName());
            if (ObjectUtil.isEmpty(resp.getFixOpenTime())) {
                resp.setFixOpenTime(resp.getOpenTime());
            }
            if (!info.getIsFixSales()) {
                resp.setFixSalesId(resp.getSalesId());
                resp.setFixSalesName(resp.getSalesName());
            }
            return resp;
        }).collect(Collectors.toList());

        return PageResult.success(resps, pageResult.getPagination());
    }
}

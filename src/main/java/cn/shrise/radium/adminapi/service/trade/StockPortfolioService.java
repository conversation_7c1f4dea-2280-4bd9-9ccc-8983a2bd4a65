package cn.shrise.radium.adminapi.service.trade;

import cn.shrise.radium.adminapi.entity.AnalystInfo;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.req.trade.AdminAuditStockPortfolioReq;
import cn.shrise.radium.adminapi.resp.trade.*;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.quoteservice.entity.DynamicInfo;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.req.AuditStockPortfolioReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Collections.emptyList;

@Service
@RequiredArgsConstructor
public class StockPortfolioService {

    private final TradeClient tradeClient;
    private final UserClient userClient;
    private final ContentClient contentClient;
    private final QuoteClient quoteClient;

    public StockPortfolioDetail getStockPortfolio(Integer id) {
        TdStockPortfolio portfolio = tradeClient.getStockPortfolio(id).orElseThrow();

        Set<Integer> userIdSet = Stream.of(portfolio.getUserId(), portfolio.getAuditorId())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElse(Collections.emptyMap());
        List<TdPortfolioAnalystRelation> analystRelationList = tradeClient.getPortfolioAnalystRelationList(id).orElse(emptyList());
        Map<Integer, List<TdPortfolioAnalystRelation>> portfolioAnalystMap = analystRelationList.stream()
                .collect(Collectors.groupingBy(TdPortfolioAnalystRelation::getPortfolioId));
        Set<Integer> analystIdSet = analystRelationList.stream()
                .map(TdPortfolioAnalystRelation::getAnalystId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(BatchReq.of(analystIdSet)).orElse(emptyList());
        Map<Integer, SsAnalystInfo> analystMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, Function.identity()));
        Map<Integer, TdPortfolioChannel> channelMap = tradeClient.getPortfolioChannelMap(BatchReq.of(Collections.singletonList(portfolio.getChannelId()))).orElse(Collections.emptyMap());
        //持仓
        Map<Integer, List<StockPositionDynamicItem>> positionMap = getPositionMap(Collections.singletonList(id));

        Integer auditorId = portfolio.getAuditorId();
        Integer userId = portfolio.getUserId();
        Integer channelId = portfolio.getChannelId();

        UserInfo creatorInfo = UserInfo.of(userMap.get(userId));
        UserInfo auditorInfo = UserInfo.of(userMap.get(auditorId));
        List<TdPortfolioAnalystRelation> analystRelations = portfolioAnalystMap.getOrDefault(id, emptyList());
        List<AnalystInfo> analystList = analystRelations.stream()
                .map(a -> AnalystInfo.of(analystMap.get(a.getAnalystId())))
                .collect(Collectors.toList());
        TdPortfolioChannel tdPortfolioChannel = channelMap.get(channelId);
        String channelName = tdPortfolioChannel != null ? tdPortfolioChannel.getName() : null;
        List<StockPositionDynamicItem> positions = positionMap.getOrDefault(id, emptyList());

        StockPortfolioDetail detail = StockPortfolioDetail.of(portfolio);
        detail.setAnalystList(analystList);
        detail.setCreatorInfo(creatorInfo);
        detail.setAuditorInfo(auditorInfo);
        detail.setChannelName(channelName);
        detail.setStockList(positions);
        return detail;
    }

    public PageResult<List<StockPortfolioItem>> getStockPortfolioList(Integer companyType, Integer channelType, Boolean audit, Integer auditStatus, Boolean enabled, Integer current, Integer size) {
        PageResult<List<TdStockPortfolio>> pageResult = tradeClient.getStockPortfolioList(companyType, channelType, audit, auditStatus, enabled, current, size);

        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }
        List<TdStockPortfolio> data = pageResult.getData();
        Pagination pagination = pageResult.getPagination();

        Set<Integer> portfolioIdSet = data.stream().map(TdStockPortfolio::getId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> userIdSet = data.stream().flatMap(e -> Stream.of(e.getUserId(), e.getAuditorId()))
                .filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> channelSet = data.stream().map(TdStockPortfolio::getChannelId)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElse(Collections.emptyMap());
        List<TdPortfolioAnalystRelation> analystRelationList = tradeClient.getPortfolioAnalystRelationList(BatchReq.of(portfolioIdSet)).orElse(emptyList());
        Map<Integer, List<TdPortfolioAnalystRelation>> portfolioAnalystMap = analystRelationList.stream()
                .collect(Collectors.groupingBy(TdPortfolioAnalystRelation::getPortfolioId));
        Set<Integer> analystIdSet = analystRelationList.stream()
                .map(TdPortfolioAnalystRelation::getAnalystId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        //分析师
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(BatchReq.of(analystIdSet)).orElse(emptyList());
        Map<Integer, SsAnalystInfo> analystMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, Function.identity()));
        //频道
        Map<Integer, TdPortfolioChannel> channelMap = tradeClient.getPortfolioChannelMap(BatchReq.of(channelSet)).orElse(Collections.emptyMap());
        //持仓
        Map<Integer, List<StockPositionDynamicItem>> positionMap = getPositionMap(portfolioIdSet);


        List<StockPortfolioItem> items = data.stream().map(e -> {
            Integer portfolioId = e.getId();
            Integer auditorId = e.getAuditorId();
            Integer userId = e.getUserId();
            Integer channelId = e.getChannelId();

            UserInfo creatorInfo = UserInfo.of(userMap.get(userId));
            UserInfo auditorInfo = UserInfo.of(userMap.get(auditorId));
            List<TdPortfolioAnalystRelation> analystRelations = portfolioAnalystMap.getOrDefault(portfolioId, emptyList());
            List<AnalystInfo> analystList = analystRelations.stream()
                    .map(a -> AnalystInfo.of(analystMap.get(a.getAnalystId())))
                    .collect(Collectors.toList());
            TdPortfolioChannel tdPortfolioChannel = channelMap.get(channelId);
            String channelName = tdPortfolioChannel != null ? tdPortfolioChannel.getName() : null;
            List<StockPositionDynamicItem> positions = positionMap.getOrDefault(portfolioId, emptyList());

            StockPortfolioItem stockPortfolioItem = StockPortfolioItem.of(e);
            stockPortfolioItem.setAnalystList(analystList);
            stockPortfolioItem.setCreatorInfo(creatorInfo);
            stockPortfolioItem.setAuditorInfo(auditorInfo);
            stockPortfolioItem.setChannelName(channelName);
            stockPortfolioItem.setStockList(positions);
            return stockPortfolioItem;
        }).collect(Collectors.toList());

        return PageResult.success(items, pagination);
    }

    private Map<Integer, List<StockPositionDynamicItem>> getPositionMap(Collection<Integer> portfolioIdSet) {
        List<TdPortfolioPosition> positionList = tradeClient.getPortfolioPositionList(BatchReq.of(portfolioIdSet)).orElse(emptyList());
        Map<Integer, List<TdPortfolioPosition>> positionMap = positionList.stream().collect(Collectors.groupingBy(TdPortfolioPosition::getPortfolioId));

        Map<Integer, List<StockPositionDynamicItem>> map = new HashMap<>(positionMap.size());
        positionMap.forEach((k, v) -> {
            List<StockPositionDynamicItem> itemList = v.stream().map(e -> {
                        String label = e.getLabel();
                        return StockPositionDynamicItem.builder()
                                .label(label)
                                .build();
                    }
            ).collect(Collectors.toList());
            map.put(k, itemList);
        });
        return map;
    }

    public void auditStockPortfolio(Integer auditId, AdminAuditStockPortfolioReq req) {
        AuditStockPortfolioReq auditStockPortfolioReq = AuditStockPortfolioReq.builder()
                .auditorId(auditId)
                .id(req.getId())
                .auditStatus(req.getAuditStatus())
                .auditReason(req.getAuditReason())
                .build();

        tradeClient.auditStockPortfolio(auditStockPortfolioReq).orElseThrow();
    }

    public List<PortfolioAdjustHistoryItem> getStockPortfolioAdjustHistory(Integer portfolioId) {
        List<TdPortfolioAdjustDetail> adjustDetailList = tradeClient.getPortfolioAdjustDetailList(portfolioId).orElse(emptyList());

        Set<Integer> recordIdSet = adjustDetailList.stream().map(TdPortfolioAdjustDetail::getRecordId).collect(Collectors.toSet());
        List<TdPortfolioAdjustRecord> adjustRecordList = tradeClient.getPortfolioAdjustRecordList(BatchReq.of(recordIdSet)).orElse(emptyList());
        List<Integer> operatorIds = adjustRecordList.stream().map(TdPortfolioAdjustRecord::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.of(operatorIds)).orElse(Collections.emptyMap());
        Map<Integer, List<TdPortfolioAdjustDetail>> adjustDetailMap = adjustDetailList.stream().collect(Collectors.groupingBy(TdPortfolioAdjustDetail::getRecordId));

        return adjustRecordList.stream().map(record -> {
            Integer recordId = record.getId();
            Integer operatorId = record.getOperatorId();
            PortfolioAdjustHistoryItem item = new PortfolioAdjustHistoryItem();
            item.setRemark(record.getRemark());
            item.setOperatorId(operatorId);
            if (Objects.nonNull(operatorId)) {
                UserInfo operatorInfo = UserInfo.of(userMap.get(operatorId));
                item.setOperatorInfo(operatorInfo);
            }

            List<TdPortfolioAdjustDetail> adjustDetails = adjustDetailMap.getOrDefault(recordId, emptyList());
            List<PortfolioAdjustDetailItem> adjustList = adjustDetails.stream().map(detail -> {
                return PortfolioAdjustDetailItem.of(detail);
            }).collect(Collectors.toList());
            item.setAdjustList(adjustList);
            return item;
        }).collect(Collectors.toList());

    }
}

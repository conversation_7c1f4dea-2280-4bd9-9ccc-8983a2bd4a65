package cn.shrise.radium.adminapi.service.trade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.trade.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.dto.CustomerStockCaseDto;
import cn.shrise.radium.tradeservice.dto.StockCaseDealDto;
import cn.shrise.radium.tradeservice.dto.StockCaseDto;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.resp.UserStockCaseSubInfoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StockCaseService {

    private final TradeClient tradeClient;
    private final UserClient userClient;
    private final ContentClient contentClient;
    private final QuoteClient quoteClient;
    private final ImClient imClient;
    private final OrderClient orderClient;

    public PageResult<List<StockCaseDetailResp>> getCaseDetailList(Integer companyType, Integer userId, Boolean checkChannel,
                                                                   Long channelId, String searchText, Integer timeType, LocalDateTime startTime,
                                                                   LocalDateTime endTime, Boolean isAsc, Boolean isClosed, Boolean isTop,
                                                                   Integer sourceType, Integer ratioType, List<Integer> auditStatusList, Integer analystId,
                                                                   Boolean isResearch, Integer current, Integer size) {
        Set<Long> channelIds = null;
        if (checkChannel) {
            BaseResult<Set<Long>> processableCaseChannelIdsRes = tradeClient.getProcessableCaseChannelIds(userId);
            if (processableCaseChannelIdsRes.isFail()) {
                log.error("获取用户可处理的频道失败： {}", processableCaseChannelIdsRes);
                throw new BusinessException(processableCaseChannelIdsRes);
            }
            channelIds = processableCaseChannelIdsRes.getData();
            if (ObjectUtil.isEmpty(channelIds) || (ObjectUtil.isNotNull(channelId) && !channelIds.contains(channelId))) {
                return PageResult.success(null, null);
            }
        }
        PageResult<List<StockCaseDto>> result = tradeClient.getStockCaseDetailList(companyType, channelId,
                channelIds, searchText, timeType, startTime, endTime, isAsc, isClosed, isTop, null, sourceType, ratioType, auditStatusList, analystId, isResearch, current, size);
        if (result.getData() == null || result.getData().size() == 0) {
            return PageResult.success(null, result.getPagination());
        }
        Set<Integer> userIds = new HashSet<>();
        List<Integer> analystIds = new ArrayList<>();
        result.getData().forEach(i -> {
            TdStockCase caseInfo = i.getCaseInfo();
            userIds.add(caseInfo.getCreatorId());
            if (ObjectUtil.isNotEmpty(caseInfo.getCreatorId())) {
                userIds.add(caseInfo.getCreatorId());
            }
            if (ObjectUtil.isNotEmpty(caseInfo.getAuditorId())) {
                userIds.add(caseInfo.getAuditorId());
            }
            if (ObjectUtil.isNotNull(i.getCaseInfo().getAnalystId())) {
                analystIds.add(caseInfo.getAnalystId());
            }
        });

        Map<Integer, UserBaseInfoResp> userMap = userClient.batchGetBaseUserMap(BatchReq.create(userIds)).getData();
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(analystIds, AuthContextHolder.getCompanyType(), null).getData();

        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        List<StockCaseDetailResp> recordRespList = result.getData().stream().map(i -> {
            TdStockCase caseInfo = i.getCaseInfo();
            TdStockCaseDeal dealInfo = i.getCreateDealInfo();
            TdCaseProfit caseProfit = ObjectUtil.isNotEmpty(i.getCaseProfit()) ? i.getCaseProfit() : new TdCaseProfit();
            SsAnalystInfo analystInfo = analystInfoMap.get(caseInfo.getAnalystId());
            StockCaseDetailResp r = new StockCaseDetailResp();
            BeanUtil.copyProperties(caseInfo, r);
            r.setChannelId(caseInfo.getChannelId());
            if (ObjectUtil.isNotEmpty(i.getChannelInfo())) {
                r.setChannelName(i.getChannelInfo().getName());
                r.setChannelType(i.getChannelInfo().getChannelType());
            }
            r.setCreatorInfo(userMap.get(caseInfo.getCreatorId()));
            if (ObjectUtil.isNotEmpty(caseInfo.getAuditorId())) {
                r.setAuditorInfo(userMap.get(caseInfo.getAuditorId()));
                r.setAuditTime(caseInfo.getAuditTime());
                r.setRejectReason(caseInfo.getRejectReason());
                r.setAuditStatus(caseInfo.getAuditStatus());
            }
            if (ObjectUtil.isNotEmpty(analystInfo)) {
                r.setAnalystId(analystInfo.getId());
                r.setAnalystName(analystInfo.getName());
            }
            if (ObjectUtil.isNotEmpty(dealInfo)) {
                r.setPriceUp(dealInfo.getPriceUp());
                r.setPriceDown(dealInfo.getPriceDown());
                r.setTakeProfitUp(dealInfo.getTakeProfitUp());
                r.setTakeProfitDown(dealInfo.getTakeProfitDown());
                r.setStopLossUp(dealInfo.getStopLossUp());
                r.setStopLossDown(dealInfo.getStopLossDown());
                r.setLossPrice(dealInfo.getLossPrice());
                r.setTargetPrice(dealInfo.getTargetPrice());
            }
            if (ObjectUtil.isNotEmpty(caseProfit)) {
                r.setSumFactor(caseProfit.getSumFactor());
                r.setMax7Ratio(caseProfit.getMax7Ratio());
                r.setMax15Ratio(caseProfit.getMax15Ratio());
                r.setMax30Ratio(caseProfit.getMax30Ratio());
                r.setMax180Ratio(caseProfit.getMax180Ratio());
                r.setMaxRatio(caseProfit.getMaxRatio());
                r.setChangeRatio(caseProfit.getChangeRatio());
                r.setMax9Ratio(caseProfit.getMax9Ratio());
                r.setMin9Ratio(caseProfit.getMin9Ratio());
            }
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    /**
     * 获取案例操作
     *
     * @param channelId
     * @param labelCode
     * @param caseId
     * @param isResearch
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<StockCaseDealResp>> getCaseDealByFilter(Integer companyType, Long channelId, String labelCode,
                                                                   Long caseId, Boolean isAudit, Integer analystId,
                                                                   Integer orderType, Boolean isAsc,
                                                                   Boolean isResearch, Integer current, Integer size) {
        PageResult<List<StockCaseDealDto>> result = tradeClient.getStockCaseDealList(companyType,
                channelId, caseId, labelCode, isAudit, null, analystId, orderType, isAsc, isResearch, current, size);
        if (result.getData() == null || result.getData().size() == 0) {
            return PageResult.success(null, result.getPagination());
        }
        Set<Integer> userIds = new HashSet<>();
        Set<Integer> analystIds = new HashSet<>();
        result.getData().forEach(i -> {
            TdStockCaseDeal dealInfo = i.getDealInfo();
            userIds.add(dealInfo.getOperatorId());
            if (ObjectUtil.isNotEmpty(dealInfo.getAuditorId())) {
                userIds.add(dealInfo.getAuditorId());
            }
            if (ObjectUtil.isNotNull(i.getCaseInfo().getAnalystId())) {
                analystIds.add(i.getCaseInfo().getAnalystId());
            }
        });

        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(new ArrayList<>(analystIds), companyType, null).getData();

        Map<Integer, SsAnalystInfo> analystInfoMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, i -> i));

        Map<Integer, UserBaseInfoResp> userMap = userClient.batchGetBaseUserMap(BatchReq.create(userIds)).getData();
        List<StockCaseDealResp> recordRespList = result.getData().stream().map(i -> {
            TdStockCaseDeal dealInfo = i.getDealInfo();
            StockCaseDealResp r = new StockCaseDealResp();
            BeanUtil.copyProperties(dealInfo, r);
            r.setCategory(i.getCaseInfo().getCategory());
            r.setChannelId(i.getCaseInfo().getChannelId());
            if (ObjectUtil.isNotEmpty(i.getChannelInfo())) {
                r.setChannelName(i.getChannelInfo().getName());
            }
            r.setOperatorInfo(userMap.get(dealInfo.getOperatorId()));
            if (ObjectUtil.isNotEmpty(dealInfo.getAuditorId())) {
                r.setAuditorInfo(userMap.get(dealInfo.getAuditorId()));
                r.setAuditTime(dealInfo.getAuditTime());
                r.setRejectReason(dealInfo.getRejectReason());
                r.setAuditStatus(dealInfo.getAuditStatus());
            }
            r.setLabelCode(i.getCaseInfo().getLabelCode());
            if (analystInfoMap.containsKey(i.getCaseInfo().getAnalystId())) {
                SsAnalystInfo analystInfo = analystInfoMap.get(i.getCaseInfo().getAnalystId());
                r.setAnalystId(analystInfo.getId());
                r.setAnalystName(analystInfo.getName());
            }
            r.setTakeProfitUp(i.getDealInfo().getTakeProfitUp());
            r.setTakeProfitDown(i.getDealInfo().getTakeProfitDown());
            r.setStopLossUp(i.getDealInfo().getStopLossUp());
            r.setStopLossDown(i.getDealInfo().getStopLossDown());
            r.setLossPrice(i.getDealInfo().getLossPrice());
            r.setTargetPrice(i.getDealInfo().getTargetPrice());
            TdStockCase caseInfo = i.getCaseInfo();
            r.setScore(caseInfo.getScore());
            r.setIsResearch(caseInfo.getIsResearch());
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(recordRespList, result.getPagination());
    }

    public PageResult<List<StockCaseSubInfoResp>> getStockCaseSubInfoList(Long caseId, Integer companyType, String content, Integer current, Integer size) {

        PageResult<List<TdCaseSubInfo>> pageResult = tradeClient.getStockCaseSubInfoList(caseId, content, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<TdCaseSubInfo> subInfos = pageResult.getData();
        List<Integer> userIds = subInfos.stream().map(TdCaseSubInfo::getUserId).collect(Collectors.toList());
        Map<Integer, VipSubscription> subscriptionMap = orderClient.batchUserVipSubscription(2, BatchReq.of(userIds), null).orElseThrow()
                .stream().collect(Collectors.toMap(VipSubscription::getUserId, Function.identity()));
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIds)).orElseThrow();
        Map<String, String> vipPackageMap = orderClient.getVipPackageList(companyType, 2).orElseThrow()
                .stream().collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));
        List<StockCaseSubInfoResp> resps = subInfos.stream().map(sub -> {
            return StockCaseSubInfoResp.builder()
                    .caseId(caseId)
                    .userId(sub.getUserId())
                    .userCode(DesensitizeUtil.idToMask(sub.getUserId()))
                    .nickName(usersMap.getOrDefault(sub.getUserId(), new UcUsers()).getNickName())
                    .subTime(sub.getGmtModified())
                    .vipPackage(vipPackageMap.getOrDefault(
                            subscriptionMap.getOrDefault(sub.getUserId(),new VipSubscription()).getNumber()
                            , null))
                    .openTime(subscriptionMap.getOrDefault(sub.getUserId(), new VipSubscription()).getOpenTime())
                    .expireTime(subscriptionMap.getOrDefault(sub.getUserId(), new VipSubscription()).getExpireTime())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<StockCaseSubRecordResp>> getStockCaseSubRecordList(Long caseId, String content, Integer current, Integer size) {

        PageResult<List<TdCaseSubRecord>> pageResult = tradeClient.getStockCaseSubRecordList(caseId, content, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<TdCaseSubRecord> recordList = pageResult.getData();
        StockCaseDto stockCaseDto = tradeClient.getStockCaseDetailInfo(caseId).orElseThrow();
        List<Integer> userIdList = recordList.stream().map(TdCaseSubRecord::getUserId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdList)).orElseThrow();
        List<StockCaseSubRecordResp> resps = recordList.stream().map(record -> {
            StockCaseSubRecordResp resp = StockCaseSubRecordResp.builder()
                    .caseId(caseId)
                    .userId(record.getUserId())
                    .userCode(DesensitizeUtil.idToMask(record.getUserId()))
                    .nickName(usersMap.getOrDefault(record.getUserId(), new UcUsers()).getNickName())
                    .isSub(record.getIsSub())
                    .gmtCreate(record.getGmtCreate())
                    .build();
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<StockCaseBaseInfoResp>> getStockCaseBaseInfoByChat(Long chatId, Integer companyType, String content, Long caseStartTime, Long caseEndTime, Integer current, Integer size) {

        ImChatRoom chatRoom = imClient.getChatRoom(chatId).orElseThrow();
        Long channelId = chatRoom.getCaseChannelId();
        if (ObjectUtil.isEmpty(channelId)) {
            return PageResult.empty();
        }
        PageResult<List<TdStockCase>> pageResult = tradeClient.filterStockCaseByChannel(companyType, channelId, content, caseStartTime, caseEndTime, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<TdStockCase> stockCaseList = pageResult.getData();
        List<StockCaseBaseInfoResp> resps = stockCaseList.stream().map(stock -> {
            StockCaseBaseInfoResp resp = new StockCaseBaseInfoResp();
            BeanUtils.copyProperties(stock, resp);
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<WhitelistUserStockCaseSubInfoResp>> getWhitelistUserSubInfoList(Integer companyType, String searchContent, Integer current, Integer size) {
        Integer userId = null;
        if (ObjectUtil.isNotEmpty(searchContent)) {
            if (!NumberUtil.isNumber(searchContent)) {
                return PageResult.empty();
            }
            userId = Integer.valueOf(searchContent);
        }
        PageResult<List<UcUsers>> pageResult = userClient.getWhitelistUserList(companyType, userId, true, current, size);
        if (pageResult.isSuccess()) {
            List<UcUsers> data = pageResult.getData();
            List<Integer> userList = data.stream().map(UcUsers::getId).collect(Collectors.toList());
            BaseResult<List<UserStockCaseSubInfoResp>> baseResult = tradeClient.getUserSubInfoList(userList);
            Map<Integer, List<UserStockCaseSubInfoResp>> subInfoMap = baseResult.getData().stream().collect(Collectors.groupingBy(UserStockCaseSubInfoResp::getUserId));
            List<WhitelistUserStockCaseSubInfoResp> respList = data.stream().map(users -> WhitelistUserStockCaseSubInfoResp.builder()
                            .userId(users.getId())
                            .nickName(users.getNickName())
                            .subInfoList(subInfoMap.getOrDefault(users.getId(), new ArrayList<>()))
                            .build())
                    .collect(Collectors.toList());
            return PageResult.success(respList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<CustomerSubStockCaseResp>> getCustomerSubStockCase(Integer companyType, Integer userId, Integer current, Integer size) {

        PageResult<List<CustomerStockCaseDto>> pageResult = tradeClient.getCustomerSubStockCaseList(companyType, userId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.success(Collections.emptyList(), pageResult.getPagination());
        }
        List<CustomerStockCaseDto> caseInfoList = pageResult.getData();
        List<CustomerSubStockCaseResp> recordRespList = caseInfoList.stream()
                .map(i -> CustomerSubStockCaseResp.of(i.getCaseInfo(), i.getLatestCaseDeal(),
                        i.getChannelInfo(), i.getSubInfo(),i.getCaseProfit(),true))
                .collect(Collectors.toList());
        return PageResult.success(recordRespList, pageResult.getPagination());
    }

    public PageResult<List<StockCaseRecordResp>> getStockCaseRecordList(Long caseId, Integer current, Integer size) {
        PageResult<List<TdStockCaseRecord>> pageResult = tradeClient.getStockCaseRecordList(caseId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<TdStockCaseRecord> data = pageResult.getData();

        Set<Integer> userSet = data.stream().map(TdStockCaseRecord::getOperateId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));

        List<StockCaseRecordResp> records = data.stream().map(t -> {
            StockCaseRecordResp resp = new StockCaseRecordResp();
            BeanUtil.copyProperties(t, resp);
            if (ObjectUtil.isNotEmpty(t.getOperateId())) {
                resp.setOperateName(usersMap.get(t.getOperateId()).getRealName());
                resp.setOperateAvatarUrl(usersMap.get(t.getOperateId()).getAvatarUrl());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }

    public void refreshCaseProfit(Long caseId) {
        tradeClient.refreshCaseProfit(caseId);
    }
}

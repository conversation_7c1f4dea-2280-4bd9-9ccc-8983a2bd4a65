package cn.shrise.radium.adminapi.service.evaluation;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcEvaluationChoice;
import cn.shrise.radium.userservice.entity.UcEvaluationInfo;
import cn.shrise.radium.userservice.entity.UcEvaluationVersion;
import cn.shrise.radium.userservice.resp.FakeDataPropertyResp;
import cn.shrise.radium.userservice.resp.evaluation.NewEvaluateInfoResp;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class NewEvaluationService {

    private final UserClient userClient;

    public BaseResult<NewEvaluateInfoResp> getEvaluationInfo(Integer userId) {
        String content = "您的年龄：";
        BaseResult<UcEvaluationInfo> result = userClient.getEvaluationInfo(userId);
        if (result.isPresent()) {
            UcEvaluationInfo info = result.getData();
            NewEvaluateInfoResp resp = NewEvaluateInfoResp.builder()
                    .userCode(DesensitizeUtil.idToMask(info.getUserId()))
                    .surveyScore(info.getSurveyScore())
                    .Level(info.getLevel())
                    .surveyTime(info.getSurveyTime())
                    .expireTime(info.getExpireTime())
                    .build();
            UcEvaluationChoice choice = userClient.getEvaluationChoiceInfo(info.getId(), content).orElse(null);
            if (ObjectUtil.isNotEmpty(choice)) {
                resp.setAnswerAge(choice.getContent());
            }
            return BaseResult.success(resp);
        }
        return BaseResult.success(null);
    }

    public PageResult<List<NewEvaluateInfoResp>> getEvaluationList(String userCode, Integer current, Integer size) {
        if (!DesensitizeUtil.isFakeCode(userCode)) {
            Integer userId = DesensitizeUtil.maskToId(userCode);
            PageResult<List<UcEvaluationInfo>> result = userClient.getEvaluationList(userId, current, size);
            if (ObjectUtil.isEmpty(result.getData())) {
                return PageResult.empty();
            }
            List<UcEvaluationInfo> infoList = result.getData();
            Set<Long> versionIdSet = infoList.stream().map(UcEvaluationInfo::getVersionId).collect(toSet());
            List<UcEvaluationVersion> versionList = userClient.getVersionBatch(versionIdSet).orElse(null);
            Map<Long, String> versionMap = Optional.ofNullable(versionList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(UcEvaluationVersion::getId, UcEvaluationVersion::getVersion));
            List<NewEvaluateInfoResp> respList = infoList.stream().map(i -> {
                Long versionId = i.getVersionId();
                return NewEvaluateInfoResp.builder()
                        .id(i.getId())
                        .number(i.getNumber())
                        .versionId(versionId)
                        .versionName(versionMap.get(versionId))
                        .isDeprecated(i.getIsDeprecated())
                        .surveyScore(ObjectUtil.isNotEmpty(i.getSurveyScore()) ? i.getSurveyScore() : null)
                        .surveyTime(ObjectUtil.isNotEmpty(i.getSurveyTime()) ? i.getSurveyTime() : null)
                        .expireTime(ObjectUtil.isNotEmpty(i.getExpireTime()) ? i.getExpireTime() : null)
                        .Level(ObjectUtil.isNotEmpty(i.getLevel()) ? i.getLevel() : null)
                        .fileUrl(i.getFileUrl())
                        .build();
            }).collect(Collectors.toList());
            return PageResult.success(respList, result.getPagination());
        } else {
            FakeDataPropertyResp property = userClient.getFakeUserProperties().getData();
            if (ObjectUtil.isEmpty(property)) {
                return PageResult.empty();
            }
            Random random = new Random();
            String evaluations = property.getEvaluations();
            List<String> evaluationList = JSON.parseArray(evaluations, String.class);
            String evaluation = evaluationList.get(random.nextInt(evaluationList.size()));
            List<String> list = JSON.parseArray(evaluation, String.class);
            List<NewEvaluateInfoResp> infoRespList = list.stream().map(i -> JSON.parseObject(i, NewEvaluateInfoResp.class)).collect(Collectors.toList());
            return PageResult.success(infoRespList, Pagination.of(current, size, (long) infoRespList.size()));
        }
    }
}

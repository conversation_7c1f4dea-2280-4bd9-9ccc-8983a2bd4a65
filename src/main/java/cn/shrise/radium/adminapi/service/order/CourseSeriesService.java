package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.RsCourseSeriesResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxAuthUrl;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsCourseCatalog;
import cn.shrise.radium.orderservice.entity.RsCourseSeries;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CourseSeriesService {

    private final OrderClient orderClient;
    private final CommonProperties commonProperties;

    public PageResult<List<RsCourseSeriesResp>> getRsCourseSeriesPage(Integer categoryType, Boolean enabled, Integer current, Integer size) {
        PageResult<List<RsCourseSeries>> rsCourseSeriesPage = orderClient.getRsCourseSeriesPage(AuthContextHolder.getCompanyType(), categoryType, enabled, current, size);
        List<RsCourseSeries> rsCourseSeries = rsCourseSeriesPage.getData();
        Set<Long> catalogIdSet = new HashSet<>();
        Map<Long, RsCourseCatalog> catalogMap = new HashMap<>();
        for (RsCourseSeries rsCourseSery : rsCourseSeries) {
            List<Long> catalogIdList = JSON.parseArray(rsCourseSery.getCatalogList(), Long.class);
            if (ObjectUtil.isNotNull(catalogIdList)) {
                catalogIdSet.addAll(catalogIdList);
            }
        }
        if (ObjectUtil.isNotEmpty(catalogIdSet)) {
            catalogMap = orderClient.getCatalogMapByIds(catalogIdSet);
        }

        List<RsCourseSeriesResp> rsCourseSeriesResps = new ArrayList<>();
        for (RsCourseSeries rsCourseSery : rsCourseSeries) {
            RsCourseSeriesResp rsCourseSeriesResp = new RsCourseSeriesResp();
            BeanUtil.copyProperties(rsCourseSery, rsCourseSeriesResp);
            List<Long> catalogIdList = JSON.parseArray(rsCourseSery.getCatalogList(), Long.class);
            List<RsCourseCatalog> rsCourseCatalogs = new ArrayList<>();
            if (ObjectUtil.isNotNull(catalogIdList)) {
                for (Long id : catalogIdList) {
                    if (catalogMap.containsKey(id)) {
                        rsCourseCatalogs.add(catalogMap.get(id));
                    }
                }
            }
            rsCourseSeriesResp.setCourseCatalogList(rsCourseCatalogs);
            rsCourseSeriesResp.setShortUrl(StrUtil.format("{}/v/{}", commonProperties.getShortUrl(), rsCourseSeriesResp.getNumber()));
            rsCourseSeriesResps.add(rsCourseSeriesResp);
        }
        return PageResult.success(rsCourseSeriesResps, rsCourseSeriesPage.getPagination());
    }

}

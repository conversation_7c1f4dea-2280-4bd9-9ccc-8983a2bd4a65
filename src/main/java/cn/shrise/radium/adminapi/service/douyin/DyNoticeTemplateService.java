package cn.shrise.radium.adminapi.service.douyin;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.douyinservice.DouYinClient;
import cn.shrise.radium.douyinservice.entity.DyNoticeTemplate;
import cn.shrise.radium.douyinservice.entity.DyNoticeTemplateRelation;
import cn.shrise.radium.douyinservice.req.CreateDyNoticeTemplateReq;
import cn.shrise.radium.douyinservice.req.UpdateDyNoticeTemplateReq;
import cn.shrise.radium.douyinservice.resp.DyNoticeTempResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class DyNoticeTemplateService {

    private final DouYinClient douYinClient;

    public List<DyNoticeTempResp> getDyNoticeTemplateList(Integer templateType, Boolean isEnabled) {
        BaseResult<List<DyNoticeTempResp>> dyNoticeTemplateList = douYinClient.getDyNoticeTemplateList(templateType, isEnabled);
        if (dyNoticeTemplateList.isFail()) {
            throw new BusinessException(dyNoticeTemplateList);
        }
        return dyNoticeTemplateList.getData();
    }

    public PageResult<List<DyNoticeTempResp>> getDyNoticeTemplateList(Integer templateType, Boolean isEnabled, Integer current, Integer size) {
        final PageResult<List<DyNoticeTempResp>> dyNoticeTemplateList = douYinClient.getDyNoticeTemplateList(templateType, isEnabled, current, size);
        if (dyNoticeTemplateList.isFail()) {
            throw new BusinessException(dyNoticeTemplateList);
        }
        return PageResult.success(dyNoticeTemplateList.getData(), Pagination.of(current, size, dyNoticeTemplateList.getPagination().getTotal()));
    }

    public DyNoticeTempResp updateDyNoticeTemplate(Long id, UpdateDyNoticeTemplateReq req) {
        final BaseResult<DyNoticeTemplate> dyNoticeTemplateBaseResult = douYinClient.updateDyNoticeTemplate(id, req);
        if (dyNoticeTemplateBaseResult.isFail()) {
            throw new BusinessException(dyNoticeTemplateBaseResult);
        }
        return new DyNoticeTempResp(
                dyNoticeTemplateBaseResult.getData().getId(),
                dyNoticeTemplateBaseResult.getData().getName(),
                dyNoticeTemplateBaseResult.getData().getTemplateType(),
                dyNoticeTemplateBaseResult.getData().getContent(),
                dyNoticeTemplateBaseResult.getData().getIsEnabled()
        );
    }

    public DyNoticeTempResp createDyNoticeTemplate(CreateDyNoticeTemplateReq req) {
        final BaseResult<DyNoticeTemplate> dyNoticeTemplate = douYinClient.createDyNoticeTemplate(req);
        if (dyNoticeTemplate.isFail()) {
            throw new BusinessException(dyNoticeTemplate);
        }
        return new DyNoticeTempResp(
                dyNoticeTemplate.getData().getId(),
                dyNoticeTemplate.getData().getName(),
                dyNoticeTemplate.getData().getTemplateType(),
                dyNoticeTemplate.getData().getContent(),
                dyNoticeTemplate.getData().getIsEnabled()
        );

    }


    public DyNoticeTemplateRelation updateDyNoticeTemplateRelation(Integer relationType, Long relationId, Long templateId) {
        BaseResult<DyNoticeTemplateRelation> dyNoticeTemplateRelationBaseResult = douYinClient.updateDyNoticeTemplateRelation(relationType, relationId, templateId);
        if (dyNoticeTemplateRelationBaseResult.isFail()) {
            throw new BusinessException(dyNoticeTemplateRelationBaseResult);
        }
        return dyNoticeTemplateRelationBaseResult.getData();
    }
}

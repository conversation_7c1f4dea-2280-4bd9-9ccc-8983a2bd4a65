package cn.shrise.radium.adminapi.service.workwx;

import cn.shrise.radium.adminapi.req.WorkWxContactUnionMarkTagReq;
import cn.shrise.radium.adminapi.resp.workWx.WorkWxContactMarkTagResp;
import cn.shrise.radium.adminapi.resp.workWx.WorkWxContactTagGroupItem;
import cn.shrise.radium.adminapi.resp.workWx.WorkWxContactTagItem;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactMarkTag;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactTag;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactTagGroup;
import cn.shrise.radium.workwxservice.req.WorkWxContactMarkTagReq;
import cn.shrise.radium.workwxservice.resp.WorkWxBatchMarkTagResult;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WorkWxContactTagService {

    private final WorkwxClient workwxClient;

    /**
     * 获取企业微信标签列表
     *
     * @param companyType
     * @param accountType
     * @return
     */
    public List<WorkWxContactTagGroupItem> getWorkWxContactTagList(Integer companyType, Integer accountType) {
        List<NpWorkWxContactTag> contactTags = workwxClient.getWorkWxContactTagList(companyType, accountType, null, true)
                .orElse(Collections.emptyList());

        Set<String> groupIdSet = contactTags.stream().map(NpWorkWxContactTag::getGroupId).collect(Collectors.toSet());
        List<NpWorkWxContactTagGroup> tagGroups = workwxClient.getWorkWxContactTagGroupList(companyType, null, BatchReq.of(groupIdSet))
                .orElse(Collections.emptyList());

        Map<String, List<NpWorkWxContactTag>> tagMap = contactTags.stream().collect(Collectors.groupingBy(NpWorkWxContactTag::getGroupId));
        Map<String, NpWorkWxContactTagGroup> groupMap = tagGroups.stream().filter(e -> e.getAccountType().equals(accountType)).collect(Collectors.toMap(NpWorkWxContactTagGroup::getGroupId, Function.identity()));

        return tagMap.entrySet().stream()
                .map(e -> {
                    String groupId = e.getKey();
                    List<NpWorkWxContactTag> workWxContactTags = e.getValue();
                    NpWorkWxContactTagGroup npWorkWxContactTagGroup = groupMap.get(groupId);
                    String groupName = npWorkWxContactTagGroup != null ? npWorkWxContactTagGroup.getGroupName() : null;
                    Integer groupOrder = npWorkWxContactTagGroup != null ? npWorkWxContactTagGroup.getOrder() : null;

                    List<WorkWxContactTagItem> tags = workWxContactTags.stream()
                            .map(t -> WorkWxContactTagItem.builder()
                                    .id(t.getId())
                                    .tagId(t.getTagId())
                                    .name(t.getName())
                                    .order(t.getOrder())
                                    .build()
                            ).collect(Collectors.toList());

                    return WorkWxContactTagGroupItem.builder()
                            .groupId(groupId)
                            .groupName(groupName)
                            .order(groupOrder)
                            .tags(tags)
                            .build();
                })
                .collect(Collectors.toList());
    }

    public BaseResult<WorkWxBatchMarkTagResult> workWxContactMarkTag(Integer companyType, WorkWxContactUnionMarkTagReq workWxContactUnionMarkTagReq) {
        WorkWxContactMarkTagReq workWxContactMarkTagReq = WorkWxContactMarkTagReq.builder()
                .companyType(companyType)
                .unionId(workWxContactUnionMarkTagReq.getUnionId())
                .tagName(workWxContactUnionMarkTagReq.getTagName())
                .build();
        return workwxClient.workWxContactMarkTag(workWxContactMarkTagReq);

    }

    public WorkWxContactMarkTagResp getWorkWxContactMarkTag(Integer companyType, String unionId) {
        NpWorkWxContactMarkTag contactMarkTag = workwxClient.getWorkWxContactMarkTag(companyType, unionId).orElse(null);
        List<String> tags = new ArrayList<>();
        if (contactMarkTag != null) {
            String markTags = contactMarkTag.getMarkTags();
            if (ObjectUtils.isNotEmpty(markTags)) {
                tags = JSON.parseArray(markTags, String.class);
            }
        }
        return WorkWxContactMarkTagResp.builder()
                .unionId(unionId)
                .tags(tags)
                .build();
    }
}

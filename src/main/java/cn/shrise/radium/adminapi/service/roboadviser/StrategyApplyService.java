package cn.shrise.radium.adminapi.service.roboadviser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ProductTypeConstant;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxSalesInviteUrl;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.constant.InviteLinkUrlTemplate;
import cn.shrise.radium.roboadviserservice.constant.StrategyApplyAuditStatusEnum;
import cn.shrise.radium.roboadviserservice.constant.StrategyApplyTypeConstant;
import cn.shrise.radium.roboadviserservice.constant.StrategyStatus;
import cn.shrise.radium.roboadviserservice.entity.RaCustomer;
import cn.shrise.radium.roboadviserservice.entity.RaStrategy;
import cn.shrise.radium.roboadviserservice.entity.RaStrategyApplyRemark;
import cn.shrise.radium.roboadviserservice.entity.RaStrategySubscription;
import cn.shrise.radium.roboadviserservice.req.StrategyApplyReq;
import cn.shrise.radium.roboadviserservice.resp.StrategyApplyFeedbackRemarkResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyApplyOperateRecordResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyApplyPackageResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyApplyResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.adminapi.constant.ErrorConstant.*;

/**
 * @Author: tangjiajun
 * @Date: 2024/8/26 10:38
 * @Desc:
 **/
@Service
@RequiredArgsConstructor
public class StrategyApplyService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final UserClient userClient;
    private final MarketingClient marketingClient;
    private final CommonProperties commonProperties;

    public PageResult<List<StrategyApplyResp>> strategyApplyList(String searchContent, Integer auditStatus, Integer signStatus, Integer feedbackStatus, Integer userId, Integer applyType, Integer current, Integer size) {
        PageResult<List<StrategyApplyResp>> pageResult = roboAdviserServiceClient.strategyApplyList(userId, searchContent, auditStatus, signStatus, feedbackStatus, applyType, current, size);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        Set<Integer> customerSet = pageResult.getData().stream().map(StrategyApplyResp::getCustomerId).collect(Collectors.toSet());
        Set<Integer> auditorSet = pageResult.getData().stream().map(StrategyApplyResp::getAuditorId).collect(Collectors.toSet());
        Set<Integer> creatorSet = pageResult.getData().stream().map(StrategyApplyResp::getCreatorId).collect(Collectors.toSet());
        Set<Integer> feedbackSet = pageResult.getData().stream().map(StrategyApplyResp::getFeedbackId).collect(Collectors.toSet());

        Map<Integer, UcUsers> customerMap = userClient.batchGetUserMap(BatchReq.create(customerSet)).orElse(new HashMap<>());
        Map<Integer, UcUsers> auditorMap = userClient.batchGetUserMap(BatchReq.create(auditorSet)).orElse(new HashMap<>());
        Map<Integer, UcUsers> creatorMap = userClient.batchGetUserMap(BatchReq.create(creatorSet)).orElse(new HashMap<>());
        Map<Integer, UcUsers> feedbackMap = userClient.batchGetUserMap(BatchReq.create(feedbackSet)).orElse(new HashMap<>());

        List<StrategyApplyResp> pagelist = pageResult.getData().stream().map(i -> {
            i.setCustomerName(customerMap.getOrDefault(i.getCustomerId(), new UcUsers()).getNickName());
            i.setAuditorName(auditorMap.getOrDefault(i.getAuditorId(), new UcUsers()).getRealName());
            i.setCreatorName(creatorMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
            i.setFeedbackName(feedbackMap.getOrDefault(i.getFeedbackId(), new UcUsers()).getRealName());
            if (Objects.equals(i.getAuditStatus(), StrategyApplyAuditStatusEnum.AUDIT_PASS.getValue())) {
                String inviteUrl = getStrategyApplyUrl(i.getId(), i.getCreatorId());
                i.setUrl(inviteUrl);
            }
            if (ObjectUtil.equals(i.getApplyType(), StrategyApplyTypeConstant.NEW_BUY)) {
                i.setExpectServiceExpireTime(DateUtils.getDayOfEnd(Instant.now()).plus(i.getPeriod(), ChronoUnit.DAYS));
            } else if (ObjectUtil.equals(i.getApplyType(), StrategyApplyTypeConstant.ADD_PERIOD)) {
                i.setExpectServiceExpireTime((i.getCurrentServiceExpireTime().isAfter(Instant.now()) ? i.getCurrentServiceExpireTime() : DateUtils.getDayOfEnd(Instant.now())).plus(i.getPeriod(), ChronoUnit.DAYS));
            }
            i.setCustomerCode(DesensitizeUtil.idToMask(i.getCustomerId()));
            return i;
        }).collect(Collectors.toList());
        return new PageResult<>(pagelist, pageResult.getPagination());
    }


    public StrategyApplyResp strategyDetail(Long applyId) {
        StrategyApplyResp resp = roboAdviserServiceClient.strategyDetail(applyId).orElse(null);
        if (ObjectUtil.isEmpty(resp)) {
            return null;
        }
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(Arrays.asList(resp.getCustomerId(), resp.getAuditorId(), resp.getCreatorId(), resp.getFeedbackId()))).orElse(new HashMap<>());
        resp.setCustomerName(usersMap.getOrDefault(resp.getCustomerId(), new UcUsers()).getNickName());
        resp.setAuditorName(usersMap.getOrDefault(resp.getAuditorId(), new UcUsers()).getRealName());
        resp.setCreatorName(usersMap.getOrDefault(resp.getCreatorId(), new UcUsers()).getRealName());
        resp.setFeedbackName(usersMap.getOrDefault(resp.getFeedbackId(), new UcUsers()).getRealName());
        resp.setCustomerCode(DesensitizeUtil.idToMask(resp.getCustomerId()));
        if (ObjectUtil.equals(resp.getApplyType(), StrategyApplyTypeConstant.NEW_BUY)) {
            resp.setExpectServiceExpireTime(DateUtils.getDayOfEnd(Instant.now()).plus(resp.getPeriod(), ChronoUnit.DAYS));
        } else if (ObjectUtil.equals(resp.getApplyType(), StrategyApplyTypeConstant.ADD_PERIOD)) {
            resp.setExpectServiceExpireTime((resp.getCurrentServiceExpireTime().isAfter(Instant.now()) ? resp.getCurrentServiceExpireTime() : DateUtils.getDayOfEnd(Instant.now())).plus(resp.getPeriod(), ChronoUnit.DAYS));
        }
        return resp;
    }

    public BaseResult<List<StrategyApplyResp>> customerApplyList(Integer customerId) {
        List<StrategyApplyResp> applyList = roboAdviserServiceClient.customerApplyList(customerId);
        if (applyList.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        }
        Set<Integer> customerSet = applyList.stream().map(StrategyApplyResp::getCustomerId).collect(Collectors.toSet());
        Set<Integer> auditorSet = applyList.stream().map(StrategyApplyResp::getAuditorId).collect(Collectors.toSet());
        Set<Integer> creatorSet = applyList.stream().map(StrategyApplyResp::getCreatorId).collect(Collectors.toSet());
        Set<Integer> feedbackSet = applyList.stream().map(StrategyApplyResp::getFeedbackId).collect(Collectors.toSet());

        Map<Integer, UcUsers> customerMap = userClient.batchGetUserMap(BatchReq.create(customerSet)).orElse(new HashMap<>());
        Map<Integer, UcUsers> auditorMap = userClient.batchGetUserMap(BatchReq.create(auditorSet)).orElse(new HashMap<>());
        Map<Integer, UcUsers> creatorMap = userClient.batchGetUserMap(BatchReq.create(creatorSet)).orElse(new HashMap<>());
        Map<Integer, UcUsers> feedbackMap = userClient.batchGetUserMap(BatchReq.create(feedbackSet)).orElse(new HashMap<>());

        List<StrategyApplyResp> respList = applyList.stream().map(i -> {
            StrategyApplyResp resp = new StrategyApplyResp();
            BeanUtil.copyProperties(i, resp);
            resp.setCustomerName(customerMap.getOrDefault(i.getCustomerId(), new UcUsers()).getNickName());
            resp.setAuditorName(auditorMap.getOrDefault(i.getAuditorId(), new UcUsers()).getRealName());
            resp.setCreatorName(creatorMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
            resp.setFeedbackName(feedbackMap.getOrDefault(i.getFeedbackId(), new UcUsers()).getRealName());
            resp.setCustomerCode(DesensitizeUtil.idToMask(i.getCustomerId()));
            return resp;
        }).collect(Collectors.toList());
        return BaseResult.success(respList);
    }

    public BaseResult<String> commitApply(StrategyApplyReq req) {
        StrategyApplyResp applyResp = check(req.getCreatorId(), req.getCustomerCode(), req.getCustomerId(), req.getPackageId(), req.getApplyType(), req.getAmount());
        return roboAdviserServiceClient.commitApply(applyResp.getCreatorId(), applyResp.getCustomerId(),
                applyResp.getPackageId(), applyResp.getAmount(), applyResp.getCurrentAmount(), applyResp.getRemark(),
                applyResp.getApplyType(), null, applyResp.getCurrentServiceExpireTime());
    }

    public BaseResult<StrategyApplyResp> previewApply(StrategyApplyReq req) {
        StrategyApplyResp applyResp = check(req.getCreatorId(), req.getCustomerCode(), req.getCustomerId(), req.getPackageId(), req.getApplyType(), req.getAmount());
        return BaseResult.success(applyResp);
    }

    public String getStrategyApplyUrl(Long applyId, Integer salesId) {
        String linkUrl = String.format(InviteLinkUrlTemplate.STRATEGY_APPLY, applyId);
        List<NpWxSalesInviteUrl> salesInviteUrls = marketingClient.getInviteUrlBySalesId(salesId, null, Collections.singletonList(linkUrl)).orElse(null);
        if (ObjectUtils.isEmpty(salesInviteUrls)) {
            return null;
        }
        NpWxSalesInviteUrl salesInvite = salesInviteUrls.get(0);
        return String.format("%s/i/%d/%s", commonProperties.getShortUrl(), ProductTypeConstant.GC_ROBO, salesInvite.getNumber());
    }

    public List<StrategyApplyOperateRecordResp> getStrategyApplyOperateRecord(Long applyId) {
        BaseResult<List<StrategyApplyOperateRecordResp>> result = roboAdviserServiceClient.getStrategyApplyOperateRecord(applyId);
        if (!result.isPresent()) {
            return Collections.emptyList();
        }
        List<StrategyApplyOperateRecordResp> recordList = result.getData();
        List<Integer> operatorIds = recordList.stream().map(StrategyApplyOperateRecordResp::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> operatorMap = userClient.batchGetUserMap(BatchReq.create(operatorIds)).orElse(new HashMap<>());
        recordList.forEach(e -> e.setOperatorName(e.getOperatorId() == null ? "系统" : operatorMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName()));
        return recordList;
    }

    public PageResult<List<StrategyApplyFeedbackRemarkResp>> feedbackRemarkList(Long applyId, Integer current, Integer size) {
        PageResult<List<RaStrategyApplyRemark>> pageResult = roboAdviserServiceClient.feedbackRemarkList(applyId, current, size);
        Set<Integer> userIds = pageResult.getData().stream().map(RaStrategyApplyRemark::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        List<StrategyApplyFeedbackRemarkResp> respList = new ArrayList<>();
        for (RaStrategyApplyRemark remark : pageResult.getData()) {
            StrategyApplyFeedbackRemarkResp build = StrategyApplyFeedbackRemarkResp.builder()
                    .gmtCreate(remark.getGmtCreate())
                    .operatorId(remark.getUserId())
                    .operatorName(userMap.getOrDefault(remark.getUserId(), new UcUsers()).getRealName())
                    .content(remark.getContent())
                    .imageList(JSON.parseArray(remark.getImageList(), String.class))
                    .avatarUrl(userMap.getOrDefault(remark.getUserId(), new UcUsers()).getAvatarUrl())
                    .build();
            respList.add(build);
        }
        return PageResult.success(respList, pageResult.getPagination());
    }

    private StrategyApplyResp check(Integer creatorId, String customerCode, Integer customerId, Long packageId, Integer applyType, Long amount) {
        UcUsers customer = userClient.getUser(customerId).getData();
        StrategyApplyPackageResp packageInfo = roboAdviserServiceClient.packageInfo(packageId).getData();
        if (ObjectUtil.isNull(packageInfo)) {
            throw new BusinessException(STRATEGY_APPLY_PACKAGE_NOT_EXISTED);
        }
        if (ObjectUtil.equals(false, packageInfo.getEnabled())) {
            throw new BusinessException(STRATEGY_APPLY_PACKAGE_NOT_ON_ENABLED);
        }
        RaStrategy strategy = roboAdviserServiceClient.getStrategy(packageInfo.getStrategyId()).getData();
        if (ObjectUtil.isNull(strategy) || !ObjectUtil.equals(StrategyStatus.SEll.getCode(), strategy.getStatus())) {
            throw new BusinessException(STRATEGY_NOT_ON_SALE);
        }
        RaStrategySubscription strategySubscription = null;
        RaCustomer raCustomer = roboAdviserServiceClient.findOneByUserId(customerId).getData();
        if (ObjectUtil.isNotNull(raCustomer)) {
            strategySubscription = roboAdviserServiceClient.getCustomerSubscription(raCustomer.getCustomerId(), strategy.getCode()).getData();
        }
        if (ObjectUtil.equals(applyType, StrategyApplyTypeConstant.NEW_BUY) && ObjectUtil.isNotNull(strategySubscription)
                && strategySubscription.getExpireTime().isAfter(Instant.now())) {
            throw new BusinessException(STRATEGY_ORDER_LINK_NEW_BUY_BUT_IN_SERVICE);
        }
        if (ObjectUtil.equals(applyType, StrategyApplyTypeConstant.NEW_BUY) && (ObjectUtil.isNull(amount)
                || amount < strategy.getFixed() * 1000000L)) {
            throw new BusinessException(STRATEGY_ORDER_LINK_AMOUNT_LESS);
        }
        if (ObjectUtil.equals(applyType, StrategyApplyTypeConstant.ADD_PERIOD) && (ObjectUtil.isNull(strategySubscription)
                || strategySubscription.getExpireTime().isBefore(Instant.now()))) {
            throw new BusinessException(STRATEGY_ORDER_LINK_ADD_PERIOD_BUT_NO_SERVICE);
        }
        if ((ObjectUtil.equals(applyType, StrategyApplyTypeConstant.ADD_AMOUNT)) && (ObjectUtil.isNull(strategySubscription)
                || strategySubscription.getExpireTime().isBefore(Instant.now().plus(30, ChronoUnit.DAYS)))) {
            throw new BusinessException(STRATEGY_ORDER_LINK_ADD_AMOUNT_BUT_SERVICE_NOT_MATCH);
        }
        Long currentAmount = ObjectUtil.equals(applyType, StrategyApplyTypeConstant.NEW_BUY) ? null : strategySubscription.getAmount();
        Instant currentServiceExpireTime = ObjectUtil.equals(applyType, StrategyApplyTypeConstant.NEW_BUY) ? null : strategySubscription.getExpireTime();
        Instant expectServiceExpireTime = null;
        if (ObjectUtil.equals(applyType, StrategyApplyTypeConstant.NEW_BUY)) {
            expectServiceExpireTime = DateUtils.getDayOfEnd(Instant.now()).plus(packageInfo.getPeriod(), ChronoUnit.DAYS);
        } else if (ObjectUtil.equals(applyType, StrategyApplyTypeConstant.ADD_PERIOD)) {
            expectServiceExpireTime = currentServiceExpireTime.plus(packageInfo.getPeriod(), ChronoUnit.DAYS);
        }

        return StrategyApplyResp.builder()
                .creatorId(creatorId)
                .packageId(packageId)
                .packageName(packageInfo.getPackageName())
                .strategyName(strategy.getName())
                .customerId(customerId)
                .customerCode(customerCode)
                .customerName(customer.getNickName())
                .amount(amount)
                .currentAmount(currentAmount)
                .expectServiceExpireTime(expectServiceExpireTime)
                .currentServiceExpireTime(currentServiceExpireTime)
                .period(packageInfo.getPeriod())
                .applyType(applyType)
                .build();
    }
}

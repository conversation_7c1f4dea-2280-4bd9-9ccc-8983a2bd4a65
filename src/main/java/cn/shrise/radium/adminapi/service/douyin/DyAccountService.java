package cn.shrise.radium.adminapi.service.douyin;

import cn.shrise.radium.adminapi.entity.DouyinConvertTool;
import cn.shrise.radium.adminapi.entity.DouyinUserInfo;
import cn.shrise.radium.adminapi.req.CreateDyConvertToolReq;
import cn.shrise.radium.adminapi.req.UpdateDyAccountReq;
import cn.shrise.radium.adminapi.req.UpdateDyConvertToolReq;
import cn.shrise.radium.adminapi.resp.GetDyAccountResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.douyinservice.DouYinClient;
import cn.shrise.radium.douyinservice.constant.ConvertToolIdType;
import cn.shrise.radium.douyinservice.entity.DyConvertTool;
import cn.shrise.radium.douyinservice.entity.DyUserAccount;
import cn.shrise.radium.douyinservice.entity.DyUserInfo;
import cn.shrise.radium.douyinservice.req.CreateConvertToolReq;
import cn.shrise.radium.douyinservice.req.UpdateAccountReq;
import cn.shrise.radium.douyinservice.req.UpdateConvertToolReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DyAccountService {

    private final DouYinClient douyinClient;

    public PageResult<List<GetDyAccountResp>> getAccountList(Integer clientType, Boolean enabled, Integer current, Integer size) {
        final PageResult<List<DyUserAccount>> getAccountListResult = douyinClient.getAccountList(clientType, enabled, current, size);
        if (getAccountListResult.isFail()) {
            throw new BusinessException(getAccountListResult);
        }
        final List<DyUserAccount> accountList = getAccountListResult.getData();
        final Pagination pagination = getAccountListResult.getPagination();

        Set<Long> accountIdSet = new HashSet<>(accountList.size());
        Set<Long> userIdSet = new HashSet<>(accountList.size());
        for (DyUserAccount account : accountList) {
            accountIdSet.add(account.getId());
            userIdSet.add(account.getDyId());
        }
        final BaseResult<List<DyUserInfo>> getUserListResult = douyinClient.batchGetUserList(new BatchReq<>(userIdSet));
        final BaseResult<List<DyConvertTool>> getConvertToolListResult = douyinClient.batchGetConvertToolList(ConvertToolIdType.ACCOUNT_ID, new BatchReq<>(accountIdSet));

        if (getUserListResult.isFail()) {
            log.warn("获取抖音用户信息失败");
        }
        if (getConvertToolListResult.isFail()) {
            log.warn("获取抖音转化工具失败");
        }

        final List<DyUserInfo> userList = getUserListResult.getData() != null?
                getUserListResult.getData(): Collections.emptyList();
        final List<DyConvertTool> toolList = getConvertToolListResult.getData() != null?
                getConvertToolListResult.getData(): Collections.emptyList();
        Map<Long, DyUserInfo> userMap = userList.stream()
                .collect(Collectors.toMap(DyUserInfo::getId, Function.identity()));
        Map<Long, List<DyConvertTool>> toolMap = toolList.stream()
                .collect(Collectors.groupingBy(DyConvertTool::getAccountId));

        final List<GetDyAccountResp> data = accountList.stream().map(e -> {
            GetDyAccountResp resp = new GetDyAccountResp();
            BeanUtils.copyProperties(e, resp);
            final Long userId = e.getDyId();
            final Long accountId = e.getId();
            final DyUserInfo userInfo = userMap.get(userId);
            final List<DyConvertTool> convertTools = toolMap.get(accountId);
            if (userInfo != null) {
                DouyinUserInfo douyinUserInfo = new DouyinUserInfo();
                BeanUtils.copyProperties(userInfo, douyinUserInfo);
                resp.setUserInfo(douyinUserInfo);
            }
            if (convertTools != null) {
                final List<DouyinConvertTool> tools = convertTools.stream()
                        .map(tool -> new DouyinConvertTool(tool.getId(), tool.getName(), tool.getNumber(), tool.getIsDeleted()))
                        .collect(Collectors.toList());
                resp.setConvertTools(tools);
            }
            return resp;
        }).collect(Collectors.toList());

        return PageResult.success(data, pagination);
    }

    public BaseResult<String> updateAccount(Long id, UpdateDyAccountReq req) {
        UpdateAccountReq updateAccountReq = new UpdateAccountReq();
        BeanUtils.copyProperties(req, updateAccountReq);
        final BaseResult<DyUserAccount> updateResult = douyinClient.updateAccount(id, updateAccountReq);
        if (updateResult.isFail()) {
            throw new BusinessException(updateResult);
        }
        return BaseResult.success();
    }

    public BaseResult<String> updateConvertTool(Long id, UpdateDyConvertToolReq req) {
        UpdateConvertToolReq updateConvertToolReq = new UpdateConvertToolReq();
        BeanUtils.copyProperties(req, updateConvertToolReq);
        final BaseResult<DyConvertTool> updateResult = douyinClient.updateConvertTool(id, updateConvertToolReq);
        if (updateResult.isFail()) {
            throw new BusinessException(updateResult);
        }
        return BaseResult.success();
    }

    public BaseResult<DouyinConvertTool> createConvertTool(Long accountId, CreateDyConvertToolReq req) {
        CreateConvertToolReq createConvertToolReq = new CreateConvertToolReq(accountId);
        BeanUtils.copyProperties(req, createConvertToolReq);
        final BaseResult<DyConvertTool> updateResult = douyinClient.createConvertTool(createConvertToolReq);
        if (updateResult.isFail()) {
            throw new BusinessException(updateResult);
        }
        final DyConvertTool data = updateResult.getData();
        DouyinConvertTool record = new DouyinConvertTool();
        BeanUtils.copyProperties(data, record);
        return BaseResult.success(record);
    }
}

package cn.shrise.radium.adminapi.service.douyin;

import cn.shrise.radium.adminapi.resp.DyUserInfoGroupResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.douyinservice.DouYinClient;
import cn.shrise.radium.douyinservice.entity.DyNoticeTemplate;
import cn.shrise.radium.douyinservice.resp.DyConvertToolResp;
import cn.shrise.radium.douyinservice.resp.DyUserInfoResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DyUserInfoService {

    private final DouYinClient douYinClient;

    public List<DyUserInfoGroupResp> getDyUserInfoGroupResp(Boolean isActive, Boolean isEnabled, Boolean isDeleted) {
        BaseResult<List<DyConvertToolResp>> dyConvertTool = douYinClient.getDyConvertTool(isDeleted);
        if (dyConvertTool.isFail()) {
            throw new BusinessException(dyConvertTool);
        }
        List<DyConvertToolResp> dyConvertToolRespList = dyConvertTool.getData();
        Map<Long, List<DyConvertToolResp>> map = dyConvertToolRespList.stream().collect(Collectors.groupingBy(DyConvertToolResp::getAccountId));
        List<DyUserInfoGroupResp> dyUserInfoGroupResps = new ArrayList<>();
        BaseResult<List<DyUserInfoResp>> dyUserINfo = douYinClient.getDyUserINfo(isActive, isEnabled);
        if (dyUserINfo.isFail()) {
            throw new BusinessException(dyUserINfo);
        }
        List<DyUserInfoResp> dyUserInfoRespList = dyUserINfo.getData();
        dyUserInfoRespList.forEach(e -> {
            DyUserInfoGroupResp dyUserInfoGroupResp = new DyUserInfoGroupResp(
                    e.getId(),
                    e.getAccountId(),
                    e.getNickName(),
                    e.getAvatar(),
                    e.getAccountRole(),
                    e.getTemplateId(),
                    map.get(e.getAccountId())
            );
            dyUserInfoGroupResps.add(dyUserInfoGroupResp);
        });
        return dyUserInfoGroupResps;
    }

    public DyNoticeTemplate getDyNoticeTemplateById(Long id) {
        BaseResult<DyNoticeTemplate> dyNoticeTemplateById = douYinClient.getDyNoticeTemplateById(id);
        if (dyNoticeTemplateById.isFail()) {
            throw new BusinessException(dyNoticeTemplateById);
        }
        return dyNoticeTemplateById.getData();
    }
}

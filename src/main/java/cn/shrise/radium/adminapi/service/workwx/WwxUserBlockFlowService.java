package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.entity.WwxUserBlockFlowInfo;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WwxConfig;
import cn.shrise.radium.workwxservice.entity.NpWorkWxUser;
import cn.shrise.radium.workwxservice.entity.NpWwxUserBlockFlow;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Collections.emptyMap;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WwxUserBlockFlowService {

    private final WorkwxClient workwxClient;

    private final UserClient userClient;

    public PageResult<List<WwxUserBlockFlowInfo>> getWwxUserBlockFlowInfoList(Integer companyType, List<Long> wwxUserIdList, Integer flowType, Integer auditStatus, Boolean confirmStatus, String searchContent, Integer current, Integer size) {
        if (ObjectUtil.isNotEmpty(searchContent)) {
            BaseResult<List<NpWorkWxUser>> baseResult = workwxClient.searchWorkWxUser(searchContent);
            if (baseResult.isSuccess()) {
                List<NpWorkWxUser> data = baseResult.getData();
                if (CollectionUtil.isEmpty(data)) {
                    return PageResult.empty(current, size);
                }
                wwxUserIdList = data.stream().map(NpWorkWxUser::getId).collect(Collectors.toList());
            }
        }
        PageResult<List<NpWwxUserBlockFlow>> pageResult = workwxClient.getListByFilter(companyType, flowType, auditStatus, confirmStatus, wwxUserIdList, current, size);
        if (pageResult.isSuccess()) {
            List<NpWwxUserBlockFlow> dataList = pageResult.getData();
            List<Long> workWxUserList = new ArrayList<>();
            List<Integer> userList = new ArrayList<>();
            dataList.forEach(e -> {
                        workWxUserList.add(e.getWwxUserId());
                        userList.add(e.getCreatorId());
                        if (ObjectUtil.isNotNull(e.getAuditorId())) {
                            userList.add(e.getAuditorId());
                        }
                        if (ObjectUtil.isNotNull(e.getConfirmId())) {
                            userList.add(e.getConfirmId());
                        }
                    }
            );
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.of(userList)).orElse(emptyMap());
            Map<Long, NpWorkWxUser> workWxUserMap = workwxClient.batchGetWorkWxUserMap(BatchReq.of(workWxUserList)).orElse(emptyMap());
            Map<Integer, WwxConfig> configMap = workwxClient.getConfigList(companyType).getData().stream()
                    .collect(Collectors.toMap(WwxConfig::getAccountType, Function.identity()));
            List<WwxUserBlockFlowInfo> respList = new ArrayList<>();
            for (NpWwxUserBlockFlow wwxUserBlockFlow : dataList) {
                WwxUserBlockFlowInfo info = new WwxUserBlockFlowInfo();
                BeanUtils.copyProperties(wwxUserBlockFlow, info);
                if (userMap.containsKey(wwxUserBlockFlow.getCreatorId())) {
                    info.setCreatorName(userMap.get(wwxUserBlockFlow.getCreatorId()).getRealName());
                }
                if (wwxUserBlockFlow.getAuditorId() != null && userMap.containsKey(wwxUserBlockFlow.getAuditorId())) {
                    info.setAuditorName(userMap.get(wwxUserBlockFlow.getAuditorId()).getRealName());
                }
                if (wwxUserBlockFlow.getConfirmId() != null && userMap.containsKey(wwxUserBlockFlow.getConfirmId())) {
                    info.setConfirmName(userMap.get(wwxUserBlockFlow.getConfirmId()).getRealName());
                }
                if (workWxUserMap.containsKey(wwxUserBlockFlow.getWwxUserId())) {
                    final NpWorkWxUser wwxUser = workWxUserMap.get(wwxUserBlockFlow.getWwxUserId());
                    info.setWxAccount(wwxUser.getWxAccount());
                    info.setAccountName(wwxUser.getName());
                    info.setMobile(AESUtil.decrypt(wwxUser.getMobile()));
                    info.setAccountType(wwxUser.getAccountType());
                    info.setCropName(configMap.getOrDefault(wwxUser.getAccountType(), new WwxConfig()).getCorpName());
                }
                respList.add(info);
            }
            return PageResult.success(respList, pageResult.getPagination());
        }
        return PageResult.fail(pageResult.getCode(), pageResult.getMsg(), pageResult.getPagination());
    }
}

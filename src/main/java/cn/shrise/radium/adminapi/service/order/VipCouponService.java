package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ProductTypeConstant;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxSalesInviteUrl;
import cn.shrise.radium.marketingservice.enums.InviteLinkType;
import cn.shrise.radium.marketingservice.req.GetOrCreateUserInviteUrlReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.InviteLinkUrlTemplate;
import cn.shrise.radium.orderservice.constant.VipCouponSignStatusEnum;
import cn.shrise.radium.orderservice.entity.RsVipCouponTemplate;
import cn.shrise.radium.orderservice.entity.RsVipCouponTemplateDeptRelation;
import cn.shrise.radium.orderservice.entity.RsVipCouponTemplateSalesRelation;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.orderservice.req.VipCouponReq;
import cn.shrise.radium.orderservice.resp.VipCouponResp;

import cn.shrise.radium.orderservice.req.VipCouponTemplateResp;
import cn.shrise.radium.orderservice.req.VipCouponTemplateVisibleResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.DepartmentDTO;
import cn.shrise.radium.userservice.dto.UserDTO;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class VipCouponService {

    private final OrderClient orderClient;
    private final UserClient userClient;
    private final MarketingClient marketingClient;
    private final CommonProperties commonProperties;

    public PageResult<List<VipCouponTemplateResp>> getVipCouponTemplateList(Integer companyType, Integer current, Integer size) {
        Map<String, String> vipMap = orderClient.getVipPackageList(companyType).getData().stream()
                .collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));
        PageResult<List<RsVipCouponTemplate>> vipCouponPae = orderClient.getVipCouponTemplateList(companyType, current, size);
        if (vipCouponPae.isPresent()) {
            List<VipCouponTemplateResp> records = vipCouponPae.getData().stream().map(template -> {
                VipCouponTemplateResp resp = BeanUtil.copyProperties(template, VipCouponTemplateResp.class);
                resp.setVipName(vipMap.get(template.getVipNumber()));
                return resp;
            }).collect(Collectors.toList());
            return PageResult.success(records, vipCouponPae.getPagination());
        } else {
            return PageResult.empty();
        }
    }

    public VipCouponTemplateVisibleResp getVipCouponTemplateVisibleList(Long couponTemplateId) {
        List<Integer> salesIdList = new ArrayList<>();
        BaseResult<List<RsVipCouponTemplateSalesRelation>> couponSalesRelation = orderClient.getVipCouponTemplateSalesRelation(couponTemplateId);
        if (couponSalesRelation.isPresent()) {
            salesIdList = couponSalesRelation.getData().stream()
                    .map(RsVipCouponTemplateSalesRelation::getSalesId).collect(Collectors.toList());
        }
        List<Integer> deptIdList = new ArrayList<>();
        BaseResult<List<RsVipCouponTemplateDeptRelation>> couponDeptRelation = orderClient.getVipCouponTemplateDeptRelation(couponTemplateId);
        if (couponDeptRelation.isPresent()) {
            deptIdList = couponDeptRelation.getData().stream()
                    .map(RsVipCouponTemplateDeptRelation::getDeptId).collect(Collectors.toList());
        }
        return new VipCouponTemplateVisibleResp(couponTemplateId, salesIdList, deptIdList);
    }

    public PageResult<List<VipCouponResp>> vipCouponPage(VipCouponReq req) {
        if (ObjectUtil.isNotEmpty(req.getSearchContent())) { //搜索时，获取当前账号所在部门及子部门的所有用户id
            if (ObjectUtil.isNotEmpty(req.getUserId())) {
                BaseResult<List<DepartmentDTO>> department = userClient.getDepartment(req.getUserId(), false);
                List<Integer> deptList = department.getData().stream().map(DepartmentDTO::getId).collect(Collectors.toList());
                Set<Integer> userSet = new HashSet<>();
                if (!deptList.isEmpty()) {
                    List<Integer> userList = userClient.batchUserIdsByDepartmentId(BatchReq.create(deptList), true).getData();
                    userSet.addAll(userList);
                    req.setSalesList(new ArrayList<>(userSet));
                }
            }
        }
        PageResult<List<VipCouponResp>> pageResult = orderClient.vipCouponPage(req);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        List<Integer> customerList = pageResult.getData().stream().map(VipCouponResp::getCustomerId).collect(Collectors.toList());
        List<Integer> salesList = pageResult.getData().stream().map(VipCouponResp::getSalesId).collect(Collectors.toList());
        Map<Integer, UcUsers> customerMap = userClient.batchGetUserMap(BatchReq.create(customerList)).orElse(new HashMap<>());
        Map<Integer, UcUsers> salesMap = userClient.batchGetUserMap(BatchReq.create(salesList)).orElse(new HashMap<>());
        List<VipCouponResp> pagelist = pageResult.getData().stream().map(i -> {
            if (ObjectUtil.isNotEmpty(i.getSignId())) {
                i.setSignStatus(i.getIsSigned() ? VipCouponSignStatusEnum.SIGNED.getValue() : VipCouponSignStatusEnum.SIGN_CLOSE.getValue());
            } else {
                i.setSignStatus(VipCouponSignStatusEnum.NO_SIGNED.getValue());
            }
            i.setCustomerName(customerMap.get(i.getCustomerId()).getNickName());
            i.setSalesName(salesMap.get(i.getSalesId()).getRealName());
            i.setUserCode(DesensitizeUtil.idToMask(i.getCustomerId()));
            return i;
        }).collect(Collectors.toList());

        return new PageResult<>(pagelist, pageResult.getPagination());
    }

    public BaseResult<VipCouponResp> vipCouponInfo(Long couponId) {
        BaseResult<VipCouponResp> resp = orderClient.vipCouponInfo(couponId);
        if (!resp.isPresent()) {
            return BaseResult.success(new VipCouponResp());
        }
        VipCouponResp couponResp = resp.getData();
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(Arrays.asList(couponResp.getCustomerId(), couponResp.getSalesId()))).orElse(new HashMap<>());
        couponResp.setCustomerName(usersMap.getOrDefault(couponResp.getCustomerId(), new UcUsers()).getNickName());
        couponResp.setSalesName(usersMap.getOrDefault(couponResp.getSalesId(), new UcUsers()).getRealName());
        couponResp.setUserCode(DesensitizeUtil.idToMask(couponResp.getCustomerId()));
        return BaseResult.success(couponResp);
    }

    public List<VipCouponTemplateResp> getVipCouponTemplateSalesVisible(Integer salesId) {
        List<VipCouponTemplateResp> res = new ArrayList<>();
        BaseResult<List<RsVipCouponTemplate>> couponTemplateSalesVisible = orderClient.getVipCouponTemplateSalesVisible(salesId, true);
        if (couponTemplateSalesVisible.isPresent()) {
            for (RsVipCouponTemplate template : couponTemplateSalesVisible.getData()) {
                if (template != null) {
                    VipCouponTemplateResp templateResp = VipCouponTemplateResp.builder()
                            .id(template.getId())
                            .vipLevel(template.getVipLevel())
                            .vipNumber(template.getVipNumber())
                            .name(template.getName())
                            .period(template.getPeriod())
                            .enabled(template.getEnabled())
                            .imageUrl(template.getImageUrl())
                            .brief(template.getBrief())
                            .gmtCreate(template.getGmtCreate())
                            .build();
                    res.add(templateResp);
                }
            }
        }
        return res.stream()
                .sorted(Comparator.comparing(VipCouponTemplateResp::getGmtCreate, Comparator.naturalOrder()))
                .collect(Collectors.toList());
    }

    public String getInviteUrl(Long templateId, Integer salesId, Integer companyType) {
        UcUsers salesInfo = userClient.getUser(salesId).getData();
        String linkUrl = String.format(InviteLinkUrlTemplate.VIP_COUPON_APPLY, templateId, salesInfo.getNumber());
        GetOrCreateUserInviteUrlReq req = GetOrCreateUserInviteUrlReq.builder()
                .salesId(salesId)
                .linkUrl(linkUrl)
                .companyType(companyType)
                .linkType(InviteLinkType.ILT_Vip_Apply.getValue())
                .build();
        BaseResult<NpWxSalesInviteUrl> salesInviteUrls = marketingClient.getOrCreateUserInviteUrl(req);
        if (ObjectUtils.isEmpty(salesInviteUrls)) {
            return null;
        }
        NpWxSalesInviteUrl salesInvite = salesInviteUrls.getData();
        return String.format("%s/i/%d/%s", commonProperties.getShortUrl(), ProductTypeConstant.GC_VIP_APPLY, salesInvite.getNumber());
    }
}

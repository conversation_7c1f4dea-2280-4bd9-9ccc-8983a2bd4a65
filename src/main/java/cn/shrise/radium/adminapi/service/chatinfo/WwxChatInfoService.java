package cn.shrise.radium.adminapi.service.chatinfo;

import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.resp.chatinfo.RiskDetailResp;
import cn.shrise.radium.statisticsservice.resp.chatinfo.SingleChatStatisticsResp;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.constant.WwxErrorCode;
import cn.shrise.radium.workwxservice.elasticsearch.WorkWxChatRecordInfo;
import cn.shrise.radium.workwxservice.req.ChatInfoReq;
import cn.shrise.radium.workwxservice.resp.WwxSingleChatResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WwxChatInfoService {

    private final WorkwxClient workwxClient;
    private final StatisticsClient statisticsClient;

    public PageResult<List<WwxSingleChatResp>> findSingleAuditList(Integer companyType, ChatInfoReq req) {
        return workwxClient.findSingleAudit(companyType, true, req);
    }

    public BaseResult<List<WwxSingleChatResp>> findNewSingleAuditList(ChatInfoReq req, Integer companyType) {
        return workwxClient.findNewSingleAudit(companyType, req);
    }

    public BaseResult<List<WwxSingleChatResp>> findNewSingleAuditAliList(ChatInfoReq req, Integer companyType) {
        return workwxClient.findNewSingleAudit(companyType, req);
    }

    public BaseResult<String> updateAuditStatus(ChatInfoReq req) {
        return workwxClient.updateAuditStatus(AuthContextHolder.getUserId(), req);
    }

    public ErrorConstant updateChatInfo(String primaryId, String auditRemark) {
        BaseResult<WwxErrorCode> res = workwxClient.updateChatInfo(primaryId, auditRemark);
        if (res.isSuccess()) {
            return ErrorConstant.SUCCESS;
        } else {
            return ErrorConstant.FAILURE;
        }
    }

    public SingleChatStatisticsResp getSingleChatStatistics(ChatInfoReq req) {
        return statisticsClient.getSingleChatStatistics(AuthContextHolder.getCompanyType(), req.getSelectTime());
    }

    public List<RiskDetailResp> getRiskDetail(ChatInfoReq req) {
        return statisticsClient.getRiskDetail(req.getPkId(), req.getIsNew());
    }

    public PageResult<List<WwxSingleChatResp>> getRepeatMsgInfo(ChatInfoReq req) {
        return workwxClient.getRepeatMsgInfo(AuthContextHolder.getCompanyType(), req);
    }

    public BaseResult<List<WwxSingleChatResp>> getRepeatMsgInfoNew(Integer companyType, ChatInfoReq req) {
        return workwxClient.getRepeatMsgInfoNew(companyType, req);
    }

    public PageResult<List<WorkWxChatRecordInfo>> searchByBriefMd5(String msg, Long flag, Boolean isNext, Integer size) {
        return workwxClient.searchByBriefMd5(msg, flag, isNext, size);
    }
}

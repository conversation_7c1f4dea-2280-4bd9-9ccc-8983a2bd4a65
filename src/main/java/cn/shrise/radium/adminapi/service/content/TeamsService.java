package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.TeamContentAuditResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsArticle;
import cn.shrise.radium.contentservice.entity.SsShortVideo;
import cn.shrise.radium.contentservice.entity.SsStreamMessage;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcContentTeam;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.userservice.constant.ContentTeamModuleTypeConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeamsService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PageResult<List<TeamContentAuditResp>> getTeamContentAuditList(Integer companyType, Integer moduleType, List<Integer> statusList, Integer current, Integer size) {
        List<TeamContentAuditResp> respList = new ArrayList<>();
        Pagination pagination = null;
        if (Objects.equals(moduleType, VIDEO)) {
            PageResult<List<SsShortVideo>> videoResult = contentClient.getTeamVideoList(null, companyType, null, null, null, null, null, statusList, current, size);
            if (videoResult.isPresent() && ObjectUtil.isNotEmpty(videoResult.getData())) {
                pagination = videoResult.getPagination();
                respList = videoResult.getData().stream().map(i -> {
                    TeamContentAuditResp.VideoInfo videoInfo = new TeamContentAuditResp.VideoInfo();
                    BeanUtils.copyProperties(i, videoInfo);
                    return TeamContentAuditResp.builder()
                            .id(i.getId())
                            .teamId(i.getTeamId())
                            .status(i.getStatus())
                            .creatorId(i.getCreatorId())
                            .createTime(i.getGmtCreate())
                            .auditorId(i.getAuditId())
                            .auditTime(i.getAuditTime())
                            .auditRemark(i.getAuditRemark())
                            .videoInfo(videoInfo)
                            .build();
                }).collect(Collectors.toList());
            }
        }else if (Objects.equals(moduleType, ARTICLE)) {
            PageResult<List<SsArticle>> articleResult = contentClient.getContentTeamArticlePage(null, companyType, null, null, null, statusList, null, null, current, size);
            if (articleResult.isPresent() && ObjectUtil.isNotEmpty(articleResult.getData())) {
                pagination = articleResult.getPagination();
                respList = articleResult.getData().stream().map(i -> {
                    TeamContentAuditResp.ArticleInfo articleInfo = new TeamContentAuditResp.ArticleInfo();
                    BeanUtils.copyProperties(i, articleInfo);
                    return TeamContentAuditResp.builder()
                            .id(i.getId())
                            .teamId(i.getTeamId())
                            .status(i.getArticleStatus())
                            .creatorId(i.getCreatorId())
                            .createTime(i.getCreateTime())
                            .auditorId(i.getAuditorId())
                            .auditTime(i.getAuditTime())
                            .auditRemark(i.getAuditRemark())
                            .articleInfo(articleInfo)
                            .build();
                }).collect(Collectors.toList());
            }
        }else if (Objects.equals(moduleType, STREAM)) {
            PageResult<List<SsStreamMessage>> streamResult = contentClient.getTeamStreamMessagePage(companyType, null, null, null, null, statusList, current, size);
            if (streamResult.isPresent() && ObjectUtil.isNotEmpty(streamResult.getData())) {
                pagination = streamResult.getPagination();
                respList = streamResult.getData().stream().map(i -> {
                    TeamContentAuditResp.StreamInfo streamInfo = new TeamContentAuditResp.StreamInfo();
                    BeanUtils.copyProperties(i, streamInfo);
                    return TeamContentAuditResp.builder()
                            .id(i.getId())
                            .teamId(i.getTeamId())
                            .status(i.getStatus())
                            .creatorId(i.getCreatorId())
                            .createTime(i.getGmtCreate())
                            .auditorId(i.getAuditorId())
                            .auditTime(i.getAuditTime())
                            .auditRemark(i.getAuditRemark())
                            .streamInfo(streamInfo)
                            .build();
                }).collect(Collectors.toList());
            }
        }
        if (ObjectUtil.isEmpty(respList)) {
            return PageResult.empty(current, size);
        }
        Set<Long> teamSet = respList.stream().map(TeamContentAuditResp::getTeamId).collect(Collectors.toSet());
        Set<Integer> userSet = new HashSet<>();
        respList.forEach(e -> {
            if (ObjectUtil.isNotEmpty(e.getAuditorId())) {
                userSet.add(e.getAuditorId());
            }
            if (ObjectUtil.isNotEmpty(e.getCreatorId())) {
                userSet.add(e.getCreatorId());
            }
        });
        Map<Long, UcContentTeam> teamMap = userClient.batchContentTeamMap(BatchReq.create(teamSet)).getData();
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        respList.forEach(resp -> {
            resp.setTeamNumber(teamMap.getOrDefault(resp.getTeamId(), new UcContentTeam()).getNumber());
            resp.setCreateName(userMap.getOrDefault(resp.getCreatorId(), new UcUsers()).getRealName());
            resp.setAuditorName(userMap.getOrDefault(resp.getAuditorId(), new UcUsers()).getRealName());
        });
        return PageResult.success(respList, pagination);
    }

}

package cn.shrise.radium.adminapi.service.content;

import cn.shrise.radium.adminapi.resp.AnalystInfoResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.entity.SsStreamChannel;
import cn.shrise.radium.contentservice.entity.SsStreamChannelAnalystRelation;
import cn.shrise.radium.contentservice.entity.SsStreamChannelOperatorRelation;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.req.StreamChannelAnalystReq;
import cn.shrise.radium.contentservice.req.StreamServiceManagerReq;
import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.orderservice.entity.ArticleSeriesAnalystRelation;
import cn.shrise.radium.orderservice.entity.ArticleSeriesOperatorRelation;
import cn.shrise.radium.tradeservice.constant.TradeErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StreamService {

    private final ContentClient contentClient;

    /**
     * 为解盘频道添加老师
     * @param req
     * @return
     */
    public BaseResult addAnalyst(StreamChannelAnalystReq req) {

        BaseResult<List<SsStreamChannelAnalystRelation>> analystRelationsBaseResult = contentClient.getStreamChannelAnalystRelations(req.getChannelId());
        if (analystRelationsBaseResult.isFail()){
            log.error("获取老师关系失败： {}", analystRelationsBaseResult);
            throw new BusinessException(analystRelationsBaseResult);
        }
        List<SsStreamChannelAnalystRelation> analystRelations = analystRelationsBaseResult.getData() == null ? Collections.emptyList() : analystRelationsBaseResult.getData();
        List<Integer> oldAnalysts = analystRelations.stream().map(analyst -> analyst.getAnalystId()).collect(Collectors.toList());
        List<Integer> reqAnalystId = req.getAnalystId();
        // 需要删除的关系
        List<Long> deleteAnalystIds = analystRelations.stream().filter(analyst -> !reqAnalystId.contains(analyst.getAnalystId())).map(SsStreamChannelAnalystRelation::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(deleteAnalystIds)) {
            BaseResult<ContentErrorCode> deleteStreamChannelAnalystRelation = contentClient.deleteStreamChannelAnalystRelation(deleteAnalystIds);
            if (deleteStreamChannelAnalystRelation.isFail()) {
                log.error("删除老师关系失败： {}", deleteStreamChannelAnalystRelation);
                throw new BusinessException(deleteStreamChannelAnalystRelation);
            }
        }
        // 需要新增的关系
        List<Integer> updateAnalystIds = reqAnalystId.stream().filter(e -> !oldAnalysts.contains(e)).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(updateAnalystIds)){
            return BaseResult.success();
        }
        req.setAnalystId(updateAnalystIds);
        return contentClient.addStreamChannelAnalyst(req);
    }

    /**
     * 为直播流频道配置处理人
     * @param req
     * @return
     */
    public BaseResult addManager(StreamServiceManagerReq req) {
        BaseResult<List<SsStreamChannelOperatorRelation>> managerRelationsBaseResult = contentClient.getStreamChannelManagerRelations(req.getChannelId(), null);
        if (managerRelationsBaseResult.isFail()){
            log.error("获取处理人关系失败： {}", managerRelationsBaseResult);
            throw new BusinessException(managerRelationsBaseResult);
        }
        List<SsStreamChannelOperatorRelation> operatorRelations = managerRelationsBaseResult.getData() == null ? Collections.emptyList() : managerRelationsBaseResult.getData();
        List<Integer> oldOperators = operatorRelations.stream().map(analyst -> analyst.getOperatorId()).collect(Collectors.toList());
        List<Integer> reqOperatorIds = req.getOperatorId();
        // 需要删除的关系
        List<Long> deleteOperatorIds = operatorRelations.stream().filter(operator -> !reqOperatorIds.contains(operator.getOperatorId())).map(SsStreamChannelOperatorRelation::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(deleteOperatorIds)) {
            BaseResult<ContentErrorCode> deleteStreamChannelManagerRelation = contentClient.deleteStreamChannelManagerRelation(deleteOperatorIds);
            if (deleteStreamChannelManagerRelation.isFail()) {
                log.error("删除处理人关系失败： {}", deleteStreamChannelManagerRelation);
                throw new BusinessException(deleteStreamChannelManagerRelation);
            }
        }
        // 需要新增的关系
        List<Integer> updateOperatorIds = reqOperatorIds.stream().filter(e -> !oldOperators.contains(e)).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(updateOperatorIds)){
            return BaseResult.success();
        }
        req.setOperatorId(updateOperatorIds);
        return contentClient.addStreamChannelManager(req);
    }

    /**
     * 获取直播流频道老师详情
     * @param channelId 频道Id
     * @return
     */
    public BaseResult<List<AnalystInfoResp>> getStreamChannelAnalysts(Long channelId) {

        BaseResult<List<SsStreamChannelAnalystRelation>> analystRelationsBaseResult = contentClient.getStreamChannelAnalystRelations(channelId);
        if (analystRelationsBaseResult.isFail()){
            log.error("获取老师关系失败： {}", analystRelationsBaseResult);
            throw new BusinessException(analystRelationsBaseResult);
        }
        List<SsStreamChannelAnalystRelation> analystRelations = analystRelationsBaseResult.getData() == null ? Collections.emptyList() : analystRelationsBaseResult.getData();
        if (ObjectUtils.isEmpty(analystRelations)) {
            return BaseResult.success(new ArrayList<>());
        }
        List<Integer> analystIds = analystRelations.stream().map(SsStreamChannelAnalystRelation::getAnalystId).collect(Collectors.toList());
        BaseResult<List<SsAnalystInfo>> analystInfoBaseResult = contentClient.getAnalystInfoList(analystIds, null, null);
        if (analystInfoBaseResult.isFail()) {
            log.error("获取老师列表失败： {}", analystInfoBaseResult);
            throw new BusinessException(analystInfoBaseResult);
        }
        List<SsAnalystInfo> analystInfos = analystInfoBaseResult.getData() == null ? Collections.emptyList() : analystInfoBaseResult.getData();
        List<AnalystInfoResp> resps = analystInfos.stream().map(analyst -> {
            AnalystInfoResp resp = new AnalystInfoResp();
            BeanUtils.copyProperties(analyst, resp);
            return resp;
        }).collect(Collectors.toList());
        return BaseResult.success(resps);
    }

    /**
     * 获取当前用户处理的直播流列表
     * @param userId
     * @return
     */
    public BaseResult<List<SsStreamChannel>> getStreamsByManager(Integer userId) {
        BaseResult<List<SsStreamChannelOperatorRelation>> managerRelationsBaseResult = contentClient.getStreamChannelManagerRelations(null, userId);
        if (managerRelationsBaseResult.isFail()){
            log.error("获取处理人关系失败： {}", managerRelationsBaseResult);
            throw new BusinessException(managerRelationsBaseResult);
        }
        List<SsStreamChannelOperatorRelation> managerRelations = managerRelationsBaseResult.getData() == null ? Collections.emptyList() : managerRelationsBaseResult.getData();
        List<Long> channelIds = managerRelations.stream().map(e -> e.getChannelId()).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(channelIds)){
            return BaseResult.success(new ArrayList<>());
        }
        PageResult<List<SsStreamChannel>> streamChannelsBaseResult = contentClient.getStreamChannels(channelIds, null, null, null);
        if (streamChannelsBaseResult.isFail()){
            log.error("获取直播流列表失败： {}", streamChannelsBaseResult);
            throw new BusinessException(streamChannelsBaseResult);
        }
        List<SsStreamChannel> streamChannels = streamChannelsBaseResult.getData();
        return BaseResult.success(streamChannels);
    }
}

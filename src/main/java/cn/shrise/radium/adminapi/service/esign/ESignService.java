package cn.shrise.radium.adminapi.service.esign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsEsignTemplate;
import cn.shrise.radium.contentservice.entity.SsEsignTemplateRecord;
import cn.shrise.radium.contentservice.error.CsErrorCode;
import cn.shrise.radium.contentservice.resp.*;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsPayCompany;
import cn.shrise.radium.orderservice.properties.esign.CompaniesConfig;
import cn.shrise.radium.orderservice.properties.esign.SignConfig;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ESignService {

    private final OrderClient orderClient;
    private final ContentClient contentClient;
    private final UserClient userClient;

    public List<ESignAgreementResp> getAgreementList() {
        BaseResult<List<SignConfig>> result = orderClient.getSignConfig();
        List<SignConfig> signList = result.getData();
        BaseResult<List<CompaniesConfig>> payCompanyConfigResult = orderClient.getCompaniesConfig();
        List<CompaniesConfig> companiesConfigList = payCompanyConfigResult.getData();
        List<Long> payCompanyIds = companiesConfigList.stream().map(CompaniesConfig::getPayCompany).collect(Collectors.toList());
        Map<Long, RsPayCompany> payCompanyMap = orderClient.batchPayCompanyList(BatchReq.of(payCompanyIds)).getData().stream().collect(Collectors.toMap(RsPayCompany::getId, x -> x, (x, y) -> x));
        List<SsEsignTemplate> eSignTemplateList = contentClient.getTemplateInfoList().getData();
        Map<String, String> templateIdMap = new HashMap<>();
        for (SsEsignTemplate eSignTemplate : eSignTemplateList) {
            templateIdMap.put(eSignTemplate.getAppId() + eSignTemplate.getSignType(), eSignTemplate.getTemplateId());
        }
        List<ESignAgreementResp> eSignAgreementList = new ArrayList<>();
        signList.forEach(e -> companiesConfigList.forEach(i -> {
            ESignAgreementResp eSignAgreementResp = new ESignAgreementResp();
            BeanUtil.copyProperties(e, eSignAgreementResp);
            eSignAgreementResp.setAppId(i.getAppId());
            eSignAgreementResp.setPayCompanyId(i.getPayCompany());
            eSignAgreementResp.setPayCompanyName(payCompanyMap.getOrDefault(i.getPayCompany(), new RsPayCompany()).getName());
            eSignAgreementResp.setTemplateId(templateIdMap.getOrDefault(eSignAgreementResp.getAppId() + eSignAgreementResp.getSignType(), null));
            eSignAgreementList.add(eSignAgreementResp);
        }));
        return eSignAgreementList;
    }

    public List<ESignTemplateRecordResp> getESignTemplateOperateRecordList(Integer signType, String appId) {
        BaseResult<List<SsEsignTemplateRecord>> result = contentClient.getESignTemplateOperateRecordList(signType, appId);
        if (!result.isPresent()) {
            return Collections.emptyList();
        }
        List<SsEsignTemplateRecord> recordList = result.getData();
        Set<Integer> userSet = recordList.stream().map(SsEsignTemplateRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userSet)).getData();
        List<ESignTemplateRecordResp> eSignTemplateRecordList = new ArrayList<>();
        recordList.forEach(e -> {
            ESignTemplateRecordResp eSignTemplateRecordResp = new ESignTemplateRecordResp();
            BeanUtil.copyProperties(e, eSignTemplateRecordResp);
            eSignTemplateRecordResp.setAvatarUrl(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getAvatarUrl());
            eSignTemplateRecordResp.setOperatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName());
            eSignTemplateRecordList.add(eSignTemplateRecordResp);
        });
        return eSignTemplateRecordList;
    }

    public BaseResult<ESignTemplateInfoResp> getTemplateInfo(String templateId) {

        String appId = contentClient.getTemplateAppId(templateId).getData();
        JSONObject jsonObject = orderClient.getDocTemplates(appId, templateId);
        if (ObjectUtil.isEmpty(jsonObject)) {
            throw new BusinessException("获取合同模板详情失败");
        }
        log.info("模板内容", jsonObject);
        ESignTemplateInfoResp eSignTemplateInfoResp = new ESignTemplateInfoResp();
        List<ESignComponentInfo> componentInfoList = new ArrayList<>();
        eSignTemplateInfoResp.setFileDownloadUrl(jsonObject.getString("downloadUrl"));
        JSONArray componentsArray = jsonObject.getJSONArray("structComponents");

        for (int i = 0; i < componentsArray.size(); i++) {
            JSONObject componentJson = componentsArray.getJSONObject(i);
            ESignComponentInfo componentInfo = new ESignComponentInfo();

            componentInfo.setKey(componentJson.getString("key"));
            componentInfo.setName(componentJson.getJSONObject("context").getString("label"));
            componentInfo.setRequired(componentJson.getJSONObject("context").getBoolean("required"));
            componentInfo.setType(componentJson.getInteger("type"));

            JSONObject positionJson = componentJson.getJSONObject("context").getJSONObject("pos");
            if (positionJson != null) {
                ESignPosition position = new ESignPosition();
                position.setX(positionJson.getFloat("x"));
                position.setY(positionJson.getFloat("y"));
                position.setPageNum(positionJson.getInteger("page"));
                componentInfo.setPosition(position);
            }
            componentInfoList.add(componentInfo);
        }
        eSignTemplateInfoResp.setComponents(componentInfoList);
        return BaseResult.success(eSignTemplateInfoResp);
    }

    public BaseResult<SsEsignTemplate> createTemplate(String appId, String templateId) {
        SsEsignTemplate ssEsignTemplate = contentClient.getTemplateById(templateId).orElse(null);
        if (ObjectUtil.isNotEmpty(ssEsignTemplate)) {
            throw new BusinessException(CsErrorCode.RECORD_EXISTED);
        }
        JSONObject jsonObject = orderClient.getDocTemplates(appId, templateId);
        String templateName = jsonObject.getString("templateName");
        if (ObjectUtil.isEmpty(templateName)) {
            throw new BusinessException("未查询到该模板");
        }
        return contentClient.createTemplate(appId, templateId, templateName);
    }

    public BaseResult<List<ESignAppInfo>> getAppList() {
        List<CompaniesConfig> companiesConfigList = orderClient.getCompaniesConfig().getData();
        List<ESignAppInfo> eSignAppInfos = new ArrayList<>();
        Map<String, Long> appIdPayCompanyMap = companiesConfigList.stream()
                .collect(Collectors.toMap(CompaniesConfig::getAppId, CompaniesConfig::getPayCompany));
        List<RsPayCompany> rsPayCompanies = orderClient.batchPayCompanyList(BatchReq.create(appIdPayCompanyMap.values())).getData();

        Map<Long, String> payCompanyNameMap = rsPayCompanies.stream()
                .collect(Collectors.toMap(RsPayCompany::getId, RsPayCompany::getName));

        appIdPayCompanyMap.forEach((appId, payCompanyId) -> {
            ESignAppInfo eSignAppInfo = new ESignAppInfo();
            eSignAppInfo.setAppId(appId);
            eSignAppInfo.setPayCompanyId(payCompanyId);
            eSignAppInfo.setPayCompanyName(payCompanyNameMap.get(payCompanyId));
            eSignAppInfos.add(eSignAppInfo);
        });
        return BaseResult.success(eSignAppInfos);
    }

    public PageResult<List<TemplateResp>> getTemplateList(String appId, Boolean enabled, Integer current, Integer size) {
        PageResult<List<TemplateResp>> templateResps = contentClient.getTemplateList(appId, enabled, current, size);
        List<ESignAppInfo> eSignAppInfos = getAppList().getData();
        Map<String, String> appIdPayCompanyNameMap = eSignAppInfos.stream()
                .collect(Collectors.toMap(ESignAppInfo::getAppId, ESignAppInfo::getPayCompanyName));

        if (ObjectUtil.isEmpty(templateResps.getData())) {
            return PageResult.empty();
        }
        List<TemplateResp> template = new ArrayList<>();
        for (TemplateResp templateResp : templateResps.getData()) {
            TemplateResp newTemplateResp = new TemplateResp();
            newTemplateResp.setTemplateId(templateResp.getTemplateId());
            newTemplateResp.setTemplateName(templateResp.getTemplateName());
            newTemplateResp.setAppId(templateResp.getAppId());
            newTemplateResp.setGmtCreate(templateResp.getGmtCreate());
            newTemplateResp.setEnabled(templateResp.getEnabled());

            String templateAppId = templateResp.getAppId();
            if (appIdPayCompanyNameMap.containsKey(templateAppId)) {
                newTemplateResp.setPayCompanyName(appIdPayCompanyNameMap.get(templateAppId));
            }
            template.add(newTemplateResp);
        }
        return PageResult.success(template, templateResps.getPagination());
    }
}

package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.order.SkuResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.CourseCouponTemplate;
import cn.shrise.radium.orderservice.entity.RsSku;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponTemplateService {

    private final OrderClient orderClient;

    public List<SkuResp> getSkuByTemplateId(Integer templateId) {
        BaseResult<List<RsSku>> result = orderClient.getSkuByTemplateId(templateId);
        List<SkuResp> skuRespList = new ArrayList<>();
        if (result.isPresent()) {
            for (RsSku sku : result.getData()) {
                skuRespList.add(SkuResp.of(sku));
            }
            return skuRespList;
        }
        return Collections.emptyList();
    }

    public PageResult<List<CourseCouponTemplate>> getCouponTemplatePage(Integer companyType, Boolean enable, Integer current, Integer size) {
        if (ObjectUtil.isEmpty(companyType)) {
            throw new BusinessException("公司类型不能为空");
        }
        return orderClient.getCouponTemplatePage(companyType, enable, current, size);
    }
}

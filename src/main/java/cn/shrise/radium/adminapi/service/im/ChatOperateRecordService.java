package cn.shrise.radium.adminapi.service.im;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.adminapi.resp.ChatOperateRecordResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatOperateRecord;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatOperateRecordService {

    private final ImClient imClient;
    private final UserClient userClient;

    public PageResult<List<ChatOperateRecordResp>> getChatOperateRecordPage(LocalDateTime startTime, LocalDateTime endTime, Long chatId, Integer current, Integer size) {
        PageResult<List<ImChatOperateRecord>> result = imClient.getChatOperateRecordPage(startTime, endTime, chatId, current, size);
        if (result.isPresent()) {
            List<ImChatOperateRecord> records = result.getData();
            List<Integer> operatorIds = records.stream().map(ImChatOperateRecord::getOperatorId).collect(Collectors.toList());
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(operatorIds)).getData();
            List<Long> chatIds = records.stream().map(ImChatOperateRecord::getChatId).collect(Collectors.toList());
            Map<Long, ImChatRoom> chatRoomMap = imClient.batchGetChatRoomMap(BatchReq.create(chatIds)).getData();
            List<ChatOperateRecordResp> resps = new ArrayList<>();
            for (ImChatOperateRecord record : records) {
                ChatOperateRecordResp resp = new ChatOperateRecordResp();
                BeanUtil.copyProperties(record, resp);
                resp.setChatName(chatRoomMap.getOrDefault(resp.getChatId(), new ImChatRoom()).getName());
                resp.setOperator(userMap.getOrDefault(resp.getOperatorId(), new UcUsers()).getRealName());
                resp.setEnable(userMap.getOrDefault(resp.getOperatorId(), new UcUsers()).getEnabled());
                resps.add(resp);
            }
            return PageResult.success(resps, result.getPagination());
        }
        return PageResult.empty(current, size);
    }
}

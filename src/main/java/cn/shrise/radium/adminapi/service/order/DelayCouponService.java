package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.order.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.dto.DelayCouponApplyDto;
import cn.shrise.radium.orderservice.entity.DelayCoupon;
import cn.shrise.radium.orderservice.entity.DelayCouponApply;
import cn.shrise.radium.orderservice.entity.DelayCouponDeptRelation;
import cn.shrise.radium.orderservice.entity.DelayCouponSalesRelation;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.orderservice.req.CreateDelayCouponReq;
import cn.shrise.radium.orderservice.resp.DelayCouponApplyResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.DelayCouponSignStatus.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DelayCouponService {

    private final OrderClient orderClient;

    private final UserClient userClient;

    public DelayCouponApplyDetailResp getDelayCouponApplyDetail(Long applyId) {
        BaseResult<DelayCouponApplyDto> delayCouponApplyDetail = orderClient.getDelayCouponApplyDetail(applyId);
        if (delayCouponApplyDetail.isPresent()) {
            DelayCoupon delayCoupon = delayCouponApplyDetail.getData().getDelayCoupon();
            DelayCouponApply delayCouponApply = delayCouponApplyDetail.getData().getDelayCouponApply();
            DelayCouponApplyDetailResp detailResp = DelayCouponApplyDetailResp.of(delayCouponApply);
            if (ObjectUtil.isNotEmpty(delayCoupon)) {
                detailResp.setName(delayCoupon.getName());
            }
            Set<Integer> userSet = new HashSet<>();
            userSet.add(delayCouponApply.getCustomerId());
            userSet.add(delayCouponApply.getCreatorId());
            if (ObjectUtil.isNotEmpty(delayCouponApply.getAuditorId())) {
                userSet.add(delayCouponApply.getAuditorId());
            }
            BaseResult<Map<Integer, UcUsers>> batchGetUserMap = userClient.batchGetUserMap(BatchReq.create(userSet));
            if (batchGetUserMap.isPresent()) {
                Map<Integer, UcUsers> userMap = batchGetUserMap.getData();
                if (userMap.containsKey(delayCouponApply.getCustomerId())) {
                    detailResp.setNickname(userMap.get(delayCouponApply.getCustomerId()).getNickName());
                }
                if (userMap.containsKey(delayCouponApply.getCreatorId())) {
                    detailResp.setCreatorName(userMap.get(delayCouponApply.getCreatorId()).getRealName());
                }
                if (userMap.containsKey(delayCouponApply.getAuditorId())) {
                    detailResp.setAuditName(userMap.get(delayCouponApply.getAuditorId()).getRealName());
                } else {
                    detailResp.setAuditName("系统");
                }
            }
            return detailResp;
        }
        return null;
    }

    public PageResult<List<DelayCouponResp>> getDelayCouponPage(Integer companyType, Integer current, Integer size) {
        PageResult<List<DelayCoupon>> pageResult = orderClient.getDelayCouponPage(current, size);
        Map<String, String> vipMap = orderClient.getVipPackageList(companyType).orElse(null).stream().collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));
        if (pageResult.isPresent()) {
            List<DelayCouponResp> delayCouponRespList = new ArrayList<>();
            for (DelayCoupon delayCoupon : pageResult.getData()) {
                DelayCouponResp couponResp = DelayCouponResp.of(delayCoupon);
                if (vipMap.containsKey(delayCoupon.getVipNumber())) {
                    couponResp.setVipName(vipMap.get(delayCoupon.getVipNumber()));
                }
                delayCouponRespList.add(couponResp);
            }
            return PageResult.success(delayCouponRespList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public BaseResult<DelayCoupon> createDelayCoupon(CreateDelayCouponReq req) {
        return orderClient.createDelayCoupon(req);
    }

    public List<DelayCouponSalesVisibleResp> getDelayCouponSalesRelation(Long delayCouponId) {
        BaseResult<List<DelayCouponSalesRelation>> baseResult = orderClient.getDelayCouponSalesRelation(delayCouponId);
        Set<Integer> userList = baseResult.getData().stream().map(DelayCouponSalesRelation::getSalesId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userList)).getData();
        List<DelayCouponSalesVisibleResp> respList = new ArrayList<>();
        for (DelayCouponSalesRelation relation : baseResult.getData()) {
            DelayCouponSalesVisibleResp build = DelayCouponSalesVisibleResp.builder()
                    .salesId(relation.getSalesId())
                    .delayCouponId(relation.getCouponId())
                    .createTime(relation.getGmtCreate())
                    .build();
            if (userMap.containsKey(relation.getSalesId())) {
                build.setNickName(userMap.get(relation.getSalesId()).getNickName());
            }
            respList.add(build);
        }
        return respList;
    }

    public List<DelayCouponDeptVisibleResp> getDelayCouponDeptRelation(Long delayCouponId) {
        BaseResult<List<DelayCouponDeptRelation>> baseResult = orderClient.getDelayCouponDeptRelation(delayCouponId);
        List<Integer> deptIdList = baseResult.getData().stream().map(DelayCouponDeptRelation::getDeptId).collect(Collectors.toList());
        List<UcDepartment> deptList = userClient.batchGetDeptList(deptIdList).getData();
        List<DelayCouponDeptVisibleResp> respList = new ArrayList<>();
        Map<Integer, UcDepartment> deptMap = deptList.stream().collect(Collectors.toMap(UcDepartment::getId, x -> x, (oldVal, newVal) -> newVal));
        for (DelayCouponDeptRelation record : baseResult.getData()) {
            DelayCouponDeptVisibleResp build = DelayCouponDeptVisibleResp.builder()
                    .id(record.getId())
                    .deptId(record.getDeptId())
                    .delayCouponId(record.getCouponId())
                    .createTime(record.getGmtCreate())
                    .build();
            if (deptMap.containsKey(record.getDeptId())) {
                build.setDeptName(deptMap.get(record.getDeptId()).getName());
            }
            respList.add(build);
        }
        return respList;
    }

    /**
     * 延期券管理
     */
    public PageResult<List<DelayCouponApplyManageResp>> getDelayCouponApplyManagePage(Integer auditStatus, Integer confirmStatus, Integer signStatus, String content, Integer current, Integer size) {
        PageResult<List<DelayCouponApplyResp>> result = orderClient.getDelayCouponApplyPage(null, null, auditStatus, confirmStatus, signStatus, content, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<DelayCouponApplyResp> applyResps = result.getData();
        Set<Integer> creatorId = applyResps.stream().map(DelayCouponApplyResp::getCreatorId).collect(Collectors.toSet());
        Set<Integer> auditorId = applyResps.stream().map(DelayCouponApplyResp::getAuditorId).collect(Collectors.toSet());
        creatorId.addAll(auditorId);
        BaseResult<Map<Integer, UserBaseInfoResp>> baseUserMap = userClient.batchGetBaseUserMap(new BatchReq<>(creatorId));
        if (baseUserMap.isFail()) {
            throw new BusinessException(baseUserMap);
        }
        Map<Integer, UserBaseInfoResp> userMap = baseUserMap.getData();
        List<DelayCouponApplyManageResp> resps = applyResps.stream().map(apply -> {
            DelayCouponApplyManageResp resp = new DelayCouponApplyManageResp();
            BeanUtils.copyProperties(apply, resp);
            resp.setCustomerCode(DesensitizeUtil.idToMask(apply.getCustomerId()));
            resp.setProposerId(apply.getCreatorId());
            resp.setProposerName(userMap.get(apply.getCreatorId()).getRealName());
            if (ObjectUtil.isNotEmpty(apply.getAuditorId())) {
                resp.setAuditorName(userMap.get(apply.getAuditorId()).getRealName());
            }
            if (ObjectUtil.isNotEmpty(apply.getNumber())) {
                VipPackage vipPackage = orderClient.getVipPackage(apply.getCompanyType(), apply.getNumber()).orElse(new VipPackage());
                resp.setCloseVipLevel(vipPackage.getLevel());
                resp.setCloseVipNumber(vipPackage.getNumber());
                resp.setCloseVipName(vipPackage.getName());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, result.getPagination());
    }

    /**
     * 延期券审核
     */
    public DelayCouponApplyAuditResp auditDelayCouponApply(Long applyId, Integer userId, Integer auditStatus) {
        BaseResult<DelayCouponApply> result = orderClient.auditDelayCouponApply(applyId, userId, auditStatus);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        DelayCouponApplyAuditResp resp = new DelayCouponApplyAuditResp();
        BeanUtils.copyProperties(result.getData(), resp);
        UcUsers users = userClient.getUser(resp.getAuditorId()).orElseThrow();
        resp.setAuditorName(users.getRealName());
        return resp;
    }

    public BaseResult<List<DelayCouponApplyResp>> getDelayCouponApplyList(String userCode, Integer applyType) {
        int userId = DesensitizeUtil.maskToId(userCode);
        BaseResult<List<DelayCouponApplyResp>> baseResult = orderClient.getDelayCouponApplyByUser(userId, applyType);
        if (baseResult.isPresent()) {
            List<DelayCouponApplyResp> respList = baseResult.getData();
            Set<Integer> userSet = respList.stream().map(DelayCouponApplyResp::getCreatorId).collect(Collectors.toSet());
            userSet.add(userId);
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
            for (DelayCouponApplyResp resp : respList) {
                if (userMap.containsKey(resp.getCustomerId())) {
                    resp.setNickname(userMap.get(resp.getCustomerId()).getNickName());
                }
                if (userMap.containsKey(resp.getCreatorId())) {
                    resp.setCreatorName(userMap.get(resp.getCreatorId()).getRealName());
                }
                if (ObjectUtil.isNotEmpty(resp.getIsSigned())) {
                    if (resp.getIsSigned()) {
                        resp.setSignStatus(SIGN_FINISHED);
                    } else {
                        resp.setSignStatus(SIGN_UNFINISHED);
                    }
                } else {
                    resp.setSignStatus(SIGN_NOT_START);
                }
                resp.setCustomerCode(DesensitizeUtil.idToMask(resp.getCustomerId()));
            }
        }
        return baseResult;
    }
}

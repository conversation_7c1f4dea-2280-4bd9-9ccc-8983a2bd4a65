package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.MaterialContentResp;
import cn.shrise.radium.adminapi.resp.content.MaterialOperateRecordResp;
import cn.shrise.radium.adminapi.resp.content.MaterialResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.enums.ContentErrorCode;
import cn.shrise.radium.contentservice.req.MaterialListReq;
import cn.shrise.radium.contentservice.resp.SameMaterialContentResp;
import cn.shrise.radium.contentservice.resp.WatermarkCode;
import cn.shrise.radium.contentservice.resp.WatermarkMaterialInfoResp;
import cn.shrise.radium.contentservice.util.GenCodeUtils;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class MaterialService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PageResult<List<MaterialResp>> getCreatorMaterialListPage(MaterialListReq materialListReq) {

        if (ObjectUtil.isNotEmpty(materialListReq.getCreatorDeptList())) {
            BaseResult<List<Integer>> result = userClient.batchUserIdsByDepartmentId(BatchReq.of(materialListReq.getCreatorDeptList()), materialListReq.getCreatorEnabled());
            if (result.isFail()) {
                throw new BusinessException(result);
            }
            if (result.isPresent()) {
                materialListReq.setCreatorList(result.getData());
            }
        }
        PageResult<List<SsMaterial>> result = contentClient.getCreatorMaterialListPage(materialListReq);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<SsMaterial> materials = result.getData();
        List<MaterialResp> records = assemble(materials);
        return PageResult.success(records, result.getPagination());
    }

    public PageResult<List<MaterialResp>> getMarketingMaterialListPage(Integer companyType, Integer userId, Integer contentType,
                                                                       Instant createStartTime, Instant createEndTime,
                                                                       String searchContent, Integer current, Integer size) {
        BaseResult<List<UcDepartment>> deptList = userClient.getDepartmentFullPath(userId, null);
        if (deptList.isFail()) {
            throw new BusinessException(deptList);
        }
        Set<Integer> deptIdSet = deptList.getData().stream().map(UcDepartment::getId).collect(Collectors.toSet());
        PageResult<List<SsMaterial>> result = contentClient.getMarketingMaterialListPage(companyType,
                userId, deptIdSet, contentType, createStartTime, createEndTime, searchContent, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<SsMaterial> materials = result.getData();
        List<MaterialResp> records = assemble(materials);
        return PageResult.success(records, result.getPagination());
    }

    public PageResult<List<MaterialResp>> getPromotionMaterialListPage(Integer companyType, Integer contentType,
                                                                       Instant createStartTime, Instant createEndTime,
                                                                       String searchContent, Integer current, Integer size) {
        PageResult<List<SsMaterial>> result = contentClient.getPromotionMaterialListPage(companyType,
                contentType, createStartTime, createEndTime, searchContent, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<SsMaterial> materials = result.getData();
        List<MaterialResp> records = assemble(materials);
        return PageResult.success(records, result.getPagination());
    }

    public PageResult<List<MaterialResp>> getBrandMaterialListPage(Integer companyType, Integer contentType,
                                                                       Instant createStartTime, Instant createEndTime,
                                                                       String searchContent, Integer current, Integer size) {
        PageResult<List<SsMaterial>> result = contentClient.getBrandMaterialListPage(companyType,
                contentType, createStartTime, createEndTime, searchContent, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<SsMaterial> materials = result.getData();
        List<MaterialResp> records = assemble(materials);
        return PageResult.success(records, result.getPagination());
    }

    private List<MaterialResp> assemble(List<SsMaterial> materials) {

        // 素材内容
        Map<Long, List<SsMaterialContent>> materialContentMap = contentClient.batchGetMaterialContent(true, BatchReq.create(materials.stream().map(SsMaterial::getId).collect(Collectors.toList()))).orElse(null);
        // 可见部门
        List<Long> materialIds = materials.stream().map(SsMaterial::getId).collect(Collectors.toList());
        Map<Long, List<SsMaterialDepartment>> visibleDepartmentMap = contentClient.getDepartmentByMaterial(materialIds).getData()
                .stream().collect(Collectors.groupingBy(SsMaterialDepartment::getMaterialId));
        // 创建人/审核人信息
        Set<Integer> userSet = materials.stream().map(SsMaterial::getCreatorId).collect(Collectors.toSet());
        Set<Integer> auditorIds = materials.stream().map(SsMaterial::getAuditorId).filter(Objects::nonNull).collect(Collectors.toSet());
        userSet.addAll(auditorIds);
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, String> creatorDepartmentMap = userClient.getDeptListByUsers(userSet).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));
        List<Long> originMaterialIdList = materials.stream().map(SsMaterial::getOriginMaterialId).filter(Objects::nonNull).collect(Collectors.toList());
        List<SsOriginMaterial> ssOriginMaterials = contentClient.getOriginMaterialByIdList(originMaterialIdList).orElseThrow();
        Map<Long, SsOriginMaterial> ssOriginMaterialMap = ssOriginMaterials.stream().collect(Collectors.toMap(SsOriginMaterial::getId, Function.identity()));

        return materials.stream().map(t -> {
            MaterialResp resp = new MaterialResp();
            BeanUtil.copyProperties(t, resp);
            if (usersMap.containsKey(t.getCreatorId())) {
                resp.setCreatorName(usersMap.get(t.getCreatorId()).getRealName());
            }
            if (usersMap.containsKey(t.getAuditorId())) {
                resp.setAuditorName(usersMap.get(t.getAuditorId()).getRealName());
            }
            if (visibleDepartmentMap.containsKey(t.getId())) {
                resp.setDepartmentList(visibleDepartmentMap.get(t.getId()).stream().map(SsMaterialDepartment::getDepartmentId).collect(Collectors.toList()));
            }
            if (ObjectUtil.isNotEmpty(t.getImageList())) {
                List<String> list = JSON.parseArray(t.getImageList(), String.class);
                resp.setAuditImageList(list);
            }
            if (ObjectUtil.isNotEmpty(materialContentMap) && materialContentMap.containsKey(t.getId())) {
                List<SsMaterialContent> contentList = materialContentMap.get(t.getId());
                List<MaterialContentResp> contentResps = contentList.stream()
                        .map(content -> {
                            MaterialContentResp contentResp = new MaterialContentResp();
                            BeanUtil.copyProperties(content, contentResp);
                            return contentResp;
                        }).collect(Collectors.toList());
                resp.setContentList(contentResps);
            }
            if (creatorDepartmentMap.containsKey(t.getCreatorId())) {
                resp.setCreatorDepartment(creatorDepartmentMap.getOrDefault(t.getCreatorId(), ""));
            }
            resp.setOriginMaterialId(t.getOriginMaterialId());
            resp.setOriginMaterialLevel(ssOriginMaterialMap.getOrDefault(t.getOriginMaterialId(), new SsOriginMaterial()).getLevel());
            return resp;
        }).collect(Collectors.toList());
    }

    public WatermarkMaterialInfoResp getMaterialInfo(String code) {
        if (ObjectUtil.isEmpty(code)) {
            return null;
        }
        WatermarkCode watermark = GenCodeUtils.decodeWatermarkCode(code);
        if (watermark == null) {
            return null;
        }
        BaseResult<SsMaterial> result = contentClient.getMaterial(watermark.getMaterialId());
        Map<Long, List<SsMaterialContent>> data = contentClient.batchGetMaterialContent(true, BatchReq.create(Arrays.asList(watermark.getMaterialId()))).getData();
        if (result.isPresent()) {
            SsMaterial ssMaterial = result.getData();
            List<SsMaterialContent> ssMaterialContentList = data.getOrDefault(watermark.getMaterialId(), Collections.emptyList());
//            List<SsMaterialContent> materialContents = ssMaterialContentList.stream().filter(e -> MaterialContentTypeConstant.IMAGE.equals(e.getContentType())).collect(Collectors.toList());
//            if (ObjectUtil.isEmpty(materialContents)) {
//                return null;
//            }
            WatermarkMaterialInfoResp resp = new WatermarkMaterialInfoResp();
            BeanUtils.copyProperties(ssMaterial, resp);
            Set<Integer> userSet = new HashSet<>();
            userSet.add(ssMaterial.getCreatorId());
            if (ObjectUtil.isNotEmpty(ssMaterial.getAuditorId())) {
                userSet.add(ssMaterial.getAuditorId());
            }
            userSet.add(watermark.getUserId());
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
            resp.setCreatorName(userMap.getOrDefault(ssMaterial.getCreatorId(), new UcUsers()).getRealName());
            if (ObjectUtil.isNotEmpty(ssMaterial.getAuditorId())) {
                resp.setAuditorName(userMap.getOrDefault(ssMaterial.getAuditorId(), new UcUsers()).getRealName());
            }
            if (ObjectUtil.isNotEmpty(ssMaterial.getImageList())) {
                List<String> list = JSON.parseArray(ssMaterial.getImageList(), String.class);
                resp.setAuditImageList(list);
            }
            resp.setReferrer(userMap.getOrDefault(watermark.getUserId(), new UcUsers()).getRealName());
            resp.setContentList(ssMaterialContentList);
            resp.setOriginalMaterialId(ssMaterial.getOriginMaterialId());
            return resp;
        }
        return null;
    }

    public BaseResult<List<MaterialOperateRecordResp>> getMaterialOperateRecord(Long materialId) {
        BaseResult<List<SsMaterialOperateRecord>> result = contentClient.getMaterialOperateRecord(materialId);
        if (!result.isPresent()) {
            return BaseResult.success(Collections.emptyList());
        }
        List<MaterialOperateRecordResp> resp = new ArrayList<>();
        List<SsMaterialOperateRecord> record = result.getData();
        List<Integer> operatorId = record.stream().map(SsMaterialOperateRecord::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(operatorId)) {
            usersMap = userClient.batchGetUserMap(BatchReq.of(operatorId)).getData();
        }
        for (SsMaterialOperateRecord ssMaterialOperateRecord : record) {
            MaterialOperateRecordResp materialOperateRecordResp = new MaterialOperateRecordResp();
            BeanUtils.copyProperties(ssMaterialOperateRecord, materialOperateRecordResp);
            if (ObjectUtil.isNotEmpty(ssMaterialOperateRecord.getOperatorId())) {
                materialOperateRecordResp.setOperatorName(usersMap.getOrDefault(ssMaterialOperateRecord.getOperatorId(), new UcUsers()).getRealName());
                materialOperateRecordResp.setAvatarUrl(usersMap.getOrDefault(ssMaterialOperateRecord.getOperatorId(), new UcUsers()).getAvatarUrl());
            }
            resp.add(materialOperateRecordResp);
        }
        return BaseResult.success(resp);
    }

    public MaterialResp getMaterialById(Long materialId) {
        BaseResult<SsMaterial> result = contentClient.getMaterial(materialId);
        if (!result.isPresent()) {
            throw new BusinessException(ContentErrorCode.RECORD_NOT_EXISTED);
        }
        MaterialResp resp = new MaterialResp();
        BeanUtil.copyProperties(result.getData(), resp);
        List<SsMaterialContent> materialContents = contentClient.getContentByMaterialId(materialId).orElseThrow(() -> new BusinessException("无可用的素材内容"));
        List<MaterialContentResp> materialContentList = materialContents.stream().map(i -> {
            MaterialContentResp materialContentResp = new MaterialContentResp();
            BeanUtils.copyProperties(i, materialContentResp);
            return materialContentResp;
        }).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(materialContentList)) {
            resp.setContentList(materialContentList);
        }
        return resp;
    }

    public PageResult<List<SameMaterialContentResp>> getSameMaterialContent(Long materialId, String contentMd5, Integer current, Integer size) {
        PageResult<List<SameMaterialContentResp>> pageResult = contentClient.getSameMaterialContent(materialId, contentMd5, current, size);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        Set<Integer> userIdSet = pageResult.getData().stream().flatMap(e -> Stream.of(e.getCreatorId(), e.getAuditorId())).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).getData();
        Set<Long> materialIdSet = pageResult.getData().stream().map(SameMaterialContentResp::getMaterialId).collect(Collectors.toSet());
        Map<Long, List<SsMaterialContent>> materialContentMap = contentClient.batchGetMaterialContent(true, BatchReq.of(materialIdSet)).orElse(new HashMap<>());
        List<SameMaterialContentResp> respList = pageResult.getData().stream().map(i -> {
            SameMaterialContentResp resp = new SameMaterialContentResp();
            BeanUtils.copyProperties(i, resp);
            resp.setCreatorName(userMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
            resp.setCreatorAvatarUrl(userMap.getOrDefault(i.getCreatorId(), new UcUsers()).getAvatarUrl());
            resp.setAuditorName(userMap.getOrDefault(i.getAuditorId(), new UcUsers()).getRealName());
            List<SsMaterialContent> materialContents = materialContentMap.getOrDefault(i.getMaterialId(), null);
            if (ObjectUtil.isNotEmpty(materialContents)) {
                List<SameMaterialContentResp.MaterialContentResp> contentRespList = materialContents.stream().map(e ->
                        SameMaterialContentResp.MaterialContentResp.builder()
                                .id(e.getId())
                                .materialId(i.getMaterialId())
                                .contentType(e.getContentType())
                                .content(e.getContent())
                                .url(e.getUrl())
                                .enabled(e.getEnabled())
                                .build()).collect(Collectors.toList());
                resp.setContentList(contentRespList);
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, pageResult.getPagination());
    }
}

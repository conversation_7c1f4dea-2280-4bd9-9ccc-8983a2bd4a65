package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.adminapi.resp.content.OriginMaterialOperateRecordResp;
import cn.shrise.radium.adminapi.resp.content.OriginMaterialResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsOriginMaterial;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialChannel;
import cn.shrise.radium.contentservice.entity.SsOriginMaterialOperateRecord;
import cn.shrise.radium.contentservice.resp.WatermarkCode;
import cn.shrise.radium.contentservice.util.GenCodeUtils;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class OriginMaterialService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public List<OriginMaterialOperateRecordResp> getOriginMaterialOperateRecord(Long materialId) {
        BaseResult<List<SsOriginMaterialOperateRecord>> result = contentClient.getOriginMaterialOperateRecord(materialId);
        if (!result.isPresent()) {
            return Collections.emptyList();
        }
        List<SsOriginMaterialOperateRecord> operateRecordList = result.getData();
        Set<Integer> userSet = operateRecordList.stream().map(SsOriginMaterialOperateRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        List<OriginMaterialOperateRecordResp> respList = new ArrayList<>();
        operateRecordList.forEach(operateRecord -> {
            OriginMaterialOperateRecordResp resp = new OriginMaterialOperateRecordResp();
            BeanUtils.copyProperties(operateRecord, resp);
            if (userMap.containsKey(operateRecord.getOperatorId())) {
                resp.setOperatorName(userMap.get(operateRecord.getOperatorId()).getRealName());
                resp.setAvatarUrl(userMap.get(operateRecord.getOperatorId()).getAvatarUrl());
            }
            respList.add(resp);
        });
        return respList;
    }

    public PageResult<List<OriginMaterialResp>> getOriginMaterialList(Integer userId, LocalDateTime startTime, LocalDateTime endTime, Long channelId, Integer category, List<Integer> auditStatusList, String searchContent, Integer current, Integer size) {
        if (ObjectUtil.isNotNull(channelId)) {
            checkOriginMaterialChannel(userId, channelId);
            return getOriginMaterialList(null, startTime, endTime, Collections.singletonList(channelId), category, true, auditStatusList, searchContent, current, size);
        }
        List<SsOriginMaterialChannel> originMaterialChannelList = getOriginMaterialChannelListByUserId(userId);
        if (originMaterialChannelList.isEmpty()) {
            return PageResult.empty();
        }
        List<Long> channelList = originMaterialChannelList.stream().map(SsOriginMaterialChannel::getId).collect(Collectors.toList());
        return getOriginMaterialList(null, startTime, endTime, channelList, category, true, auditStatusList, searchContent, current, size);
    }

    public PageResult<List<OriginMaterialResp>> getOriginMaterialList(Integer userId, LocalDateTime startTime, LocalDateTime endTime, List<Long> channelIds, Integer category, Boolean status, List<Integer> auditStatusList, String searchContent, Integer current, Integer size) {
        PageResult<List<SsOriginMaterial>> originMaterialList = contentClient.getOriginMaterialList(startTime, endTime, userId, channelIds, category, status, auditStatusList, searchContent, current, size);
        if (originMaterialList.isPresent()) {
            List<OriginMaterialResp> respList = new ArrayList<>();
            List<SsOriginMaterial> data = originMaterialList.getData();
            Set<Integer> userIdSet = data.stream()
                    .flatMap(d -> Stream.of(d.getCreatorId(), d.getCustomerId(), d.getProviderId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).getData();
            Set<Long> channelSet = data.stream().map(SsOriginMaterial::getChannelId).collect(Collectors.toSet());
            Map<Long, SsOriginMaterialChannel> channelMap = contentClient.batchGetOriginMaterialChannelMap(BatchReq.create(channelSet)).getData();
            List<Long> originMaterialIds = data.stream().map(SsOriginMaterial::getId).collect(Collectors.toList());
            Map<Long, List<Long>> originMaterialId2MaterialIdMap = contentClient.getMaterialIdsMapByOriginMaterialIds(originMaterialIds).orElseThrow();
            data.forEach(originMaterial -> {
                OriginMaterialResp resp = new OriginMaterialResp();
                BeanUtils.copyProperties(originMaterial, resp, "imageList", "fileList");
                resp.setCreatorName(userMap.containsKey(originMaterial.getCreatorId()) ? userMap.get(originMaterial.getCreatorId()).getRealName() : null);
                resp.setProviderName(userMap.containsKey(originMaterial.getProviderId()) ? userMap.get(originMaterial.getProviderId()).getRealName() : null);
                resp.setCustomerCode(DesensitizeUtil.idToMask(originMaterial.getCustomerId()));
                resp.setCustomerId(originMaterial.getCustomerId());
                resp.setOriginMaterialLevel(originMaterial.getLevel());
                resp.setMaterialIdList(originMaterialId2MaterialIdMap.getOrDefault(originMaterial.getId(), Collections.emptyList()));
                resp.setChannelName(channelMap.containsKey(originMaterial.getChannelId()) ? channelMap.get(originMaterial.getChannelId()).getName() : null);
                resp.setFileList(ObjectUtil.isNotNull(originMaterial.getFileList()) ? JSON.parseArray(originMaterial.getFileList(), String.class) : null);
                respList.add(resp);
            });
            return PageResult.success(respList, originMaterialList.getPagination());
        }
        return PageResult.empty();
    }

    public List<SsOriginMaterialChannel> getOriginMaterialChannelListByUserId(Integer userId) {
        List<Integer> deptIdList = userClient.getDepartmentFullPath(userId, null).getData().stream()
                .map(UcDepartment::getId).collect(Collectors.toList());
        return contentClient.getOriginMaterialVisibleChannelList(userId, deptIdList).getData();
    }

    public void checkOriginMaterialChannel(Integer userId, Long channelId) {
        List<SsOriginMaterialChannel> originMaterialChannelList = getOriginMaterialChannelListByUserId(userId);
        Set<Long> channelIdSet = originMaterialChannelList.stream().map(SsOriginMaterialChannel::getId).collect(Collectors.toSet());
        if (!channelIdSet.contains(channelId)) {
            throw new BusinessException("没有该频道权限");
        }
    }

    public OriginMaterialResp getMaterialInfo(String code) {
        if (ObjectUtil.isEmpty(code)) {
            return null;
        }
        WatermarkCode watermarkCode = GenCodeUtils.decodeOriginMaterialWatermarkCode(code);
        if (ObjectUtil.isEmpty(watermarkCode)) {
            return null;
        }
        SsOriginMaterial ssOriginMaterial = contentClient.getOriginMaterial(watermarkCode.getMaterialId())
                .orElseThrow(() -> new BusinessException("好评素材不存在"));
        OriginMaterialResp resp = BeanUtil.copyProperties(ssOriginMaterial, OriginMaterialResp.class, "imageList","fileList");
        List<String> fileList= JSONUtil.toList(ssOriginMaterial.getFileList(), String.class);
        resp.setFileList(fileList);
        List<SsOriginMaterialOperateRecord> originMaterialOperateRecords = contentClient.getOriginMaterialOperateRecord(watermarkCode.getMaterialId()).getData();
        List<OriginMaterialOperateRecordResp> originMaterialOperateRecordResps = BeanUtil.copyToList(originMaterialOperateRecords, OriginMaterialOperateRecordResp.class);
        resp.setRecordList(originMaterialOperateRecordResps);
        resp.setOriginMaterialLevel(ssOriginMaterial.getLevel());
        // 填充 素材创建人,引用人
        Set<Integer> userSet = new HashSet<>();
        userSet.add(watermarkCode.getUserId());
        userSet.add(ssOriginMaterial.getCreatorId());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        if (ObjectUtil.isNotNull(watermarkCode.getUserId())) {
            resp.setCiterName(userMap.getOrDefault(watermarkCode.getUserId(), new UcUsers()).getRealName());
        }
        if (ObjectUtil.isNotNull(ssOriginMaterial.getCreatorId())) {
            resp.setCreatorName(userMap.getOrDefault(ssOriginMaterial.getCreatorId(), new UcUsers()).getRealName());
        }
        contentClient.getMaterialIdsMapByOriginMaterialIds(Collections.singletonList(ssOriginMaterial.getId()))
                .ifPresent(map -> resp.setMaterialIdList(map.get(ssOriginMaterial.getId())));
        return resp;
    }

    public OriginMaterialResp getOriginMaterialById(Long originMaterialId) {
        SsOriginMaterial ssOriginMaterial = contentClient.getOriginMaterial(originMaterialId).getData();
        if (ObjectUtil.isNull(ssOriginMaterial)) {
            return null;
        }
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(Sets.newHashSet(ssOriginMaterial.getCreatorId(), ssOriginMaterial.getProviderId(), ssOriginMaterial.getCustomerId()))).orElseThrow();
        OriginMaterialResp resp = BeanUtil.copyProperties(ssOriginMaterial, OriginMaterialResp.class, "imageList", "fileList");
        List<String> fileList = JSONUtil.toList(ssOriginMaterial.getFileList(), String.class);
        resp.setFileList(fileList);
        resp.setCreatorName(usersMap.getOrDefault(ssOriginMaterial.getCreatorId(), new UcUsers()).getRealName());
        resp.setProviderName(usersMap.getOrDefault(ssOriginMaterial.getProviderId(), new UcUsers()).getRealName());
        resp.setCustomerCode(DesensitizeUtil.idToMask(ssOriginMaterial.getCustomerId()));
        resp.setOriginMaterialLevel(ssOriginMaterial.getLevel());
        contentClient.getMaterialIdsMapByOriginMaterialIds(Collections.singletonList(ssOriginMaterial.getId()))
                .ifPresent(map -> resp.setMaterialIdList(map.get(ssOriginMaterial.getId())));
        List<SsOriginMaterialOperateRecord> originMaterialOperateRecords = contentClient.getOriginMaterialOperateRecord(originMaterialId).getData();
        List<OriginMaterialOperateRecordResp> originMaterialOperateRecordResps = BeanUtil.copyToList(originMaterialOperateRecords, OriginMaterialOperateRecordResp.class);
        resp.setRecordList(originMaterialOperateRecordResps);
        resp.setOriginMaterialLevel(ssOriginMaterial.getLevel());
        return resp;
    }
}

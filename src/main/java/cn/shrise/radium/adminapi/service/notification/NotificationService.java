package cn.shrise.radium.adminapi.service.notification;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.NumberUtils;
import cn.shrise.radium.notificationservice.NotificationClient;
import cn.shrise.radium.notificationservice.constant.PushTypeEnum;
import cn.shrise.radium.notificationservice.entity.*;
import cn.shrise.radium.notificationservice.req.PushEventCreateReq;
import cn.shrise.radium.notificationservice.resp.*;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.properties.vip.UserVipPackageInfo;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.resp.StrategySubscriptionResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhangjianwu
 * @created : 2025/4/16, 星期三
 **/

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationService {

    private final NotificationClient notificationClient;
    private final UserClient userClient;
    private final CommonService commonService;
    private final OrderClient orderClient;
    private final RoboAdviserServiceClient roboAdviserServiceClient;

    public BaseResult<String> createPushEvent(PushEventCreateReq req) {
        return notificationClient.createPushEvent(req);
    }

    public PageResult<List<PushEventResp>> listPushEvents(Integer current, Integer size) {
        PageResult<List<NtPushEvent>> pageResult = notificationClient.listPushEvents(current, size);
        List<NtPushEvent> pushEvents = pageResult.getData();
        if (ObjectUtil.isEmpty(pushEvents)) {
            return PageResult.success(Collections.emptyList(), pageResult.getPagination());
        }
        List<PushEventResp> pushEventResps = BeanUtil.copyToList(pushEvents, PushEventResp.class);
        List<Long> categoryIds = pushEvents.stream()
                .map(NtPushEvent::getCategoryId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        fillCategoryNames(categoryIds, pushEventResps);
        return PageResult.success(pushEventResps, pageResult.getPagination());
    }

    private void fillCategoryNames(List<Long> categoryIds, List<PushEventResp> pushEventResps) {
        if (ObjectUtil.isEmpty(pushEventResps) || ObjectUtil.isEmpty(categoryIds)) {
            return;
        }
        List<NtPushCategory> categoryList = notificationClient.getPushCategoryListByIds(categoryIds).getData();
        Map<Long, NtPushCategory> categoryMap = categoryList.stream().filter(Objects::nonNull).collect(Collectors.toMap(NtPushCategory::getId, Function.identity()));
        for (PushEventResp pushEventResp : pushEventResps) {
            if (ObjectUtil.isEmpty(pushEventResp.getCategoryId())) {
                continue;
            }
            pushEventResp.setCategoryName(categoryMap.getOrDefault(pushEventResp.getCategoryId(), new NtPushCategory()).getName());
        }
    }

    public PageResult<List<PushEventLineResp>> listPushEventLines(Long eventId, Integer current, Integer size) {
        return notificationClient.listPushEventLines(eventId, current, size);
    }

    public PageResult<List<PushEventRecordResp>> listPushEventRecords(Long eventId, Integer current, Integer size) {
        PageResult<List<NtPushEventRecord>> pageResult = notificationClient.listPushEventRecords(eventId, current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.success(Collections.emptyList(), pageResult.getPagination());
        }
        List<PushEventRecordResp> pushEventRecordResps = BeanUtil.copyToList(pageResult.getData(), PushEventRecordResp.class);
        fillUserNames(pushEventRecordResps);
        return PageResult.success(pushEventRecordResps, pageResult.getPagination());
    }

    private void fillUserNames(List<PushEventRecordResp> recordResps) {
        List<Integer> userIdList = recordResps.stream()
                .map(PushEventRecordResp::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> userMap = commonService.getUserMapByIds(userIdList);
        for (PushEventRecordResp recordResp : recordResps) {
            if (StringUtils.isBlank(recordResp.getContent())) {
                continue;
            }
            UcUsers user = userMap.get(recordResp.getOperatorId());
            if (user == null) {
                continue;
            }
            recordResp.setAvatarUrl(user.getAvatarUrl());
            recordResp.setContent("{" + user.getRealName() + "}" + recordResp.getContent());
        }
    }

    public PageResult<List<PushLineResp>> getPushLineList(Integer current, Integer size) {
        PageResult<List<NtPushLine>> pageResult = notificationClient.getPushLineList(current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<NtPushLine> pushLineList = pageResult.getData();
        Set<Integer> creatorIds = pushLineList.stream().filter(Objects::nonNull).map(NtPushLine::getCreatorId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(creatorIds)).orElse(Collections.emptyList());
        Map<Integer, UcUsers> userMap = userList.stream().filter(Objects::nonNull).collect(Collectors.toMap(UcUsers::getId, Function.identity()));
        List<PushLineResp> respList = BeanUtil.copyToList(pushLineList, PushLineResp.class);
        respList.forEach(resp -> {
            UcUsers user = userMap.get(resp.getCreatorId());
            resp.setCreatorName(user.getRealName());
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

    public PageResult<List<PushCategoryResp>> getPushCategoryList(Integer current, Integer size) {
        PageResult<List<NtPushCategory>> pageResult = notificationClient.getPushCategoryList(current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<NtPushCategory> pushCategoryList = pageResult.getData();
        Set<Integer> creatorIds = pushCategoryList.stream().filter(Objects::nonNull).map(NtPushCategory::getCreatorId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(creatorIds)).orElse(Collections.emptyList());
        Map<Integer, UcUsers> userMap = userList.stream().filter(Objects::nonNull).collect(Collectors.toMap(UcUsers::getId, Function.identity()));
        List<PushCategoryResp> respList = BeanUtil.copyToList(pushCategoryList, PushCategoryResp.class);
        respList.forEach(resp -> {
            UcUsers user = userMap.get(resp.getCreatorId());
            resp.setCreatorName(user.getRealName());
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

    public PageResult<List<PushSubscriptionResp>> getPushSubscriptionList(Integer userId, Integer current, Integer size) {
        PageResult<List<PushSubscriptionResp>> pageResult = notificationClient.getPushSubscriptionList(userId, current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<PushSubscriptionResp> respList = pageResult.getData();
        respList.forEach(i -> {
            if (!ObjectUtil.equal(i.getIsSubscribe(), true)) {
                i.setIsSubscribe(false);
            }
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

    public BaseResult<Void> setPushSubscriptionStatus(Integer companyType, Integer operatorId, Integer userId, Long eventId, Boolean isSubscribe) {
        NtPushEvent event = notificationClient.getPushEventById(eventId).getData();
        Map<String, String> eventmap = notificationClient.getPushSubscriptionEventMap().orElse(null);
        if (isSubscribe) {
            if (ObjectUtil.equal(eventmap.getOrDefault(event.getNumber(), null), "strategy")) {
                String customerId = commonService.getCustomerIdByUserId(userId);
                List<StrategySubscriptionResp> strategySubscriptionRespList = roboAdviserServiceClient.getAllSubscriptionStrategyList(false, customerId).orElse(null);
                if (ObjectUtil.isNotEmpty(strategySubscriptionRespList)) {
                    notificationClient.setPushSubscriptionStatus(operatorId, userId, eventId, isSubscribe);
                } else {
                    throw new BusinessException("用户没有对应的策略权限");
                }
            } else {
                List<UserVipPackageInfo> userVipPackageInfoList = orderClient.getSubscriptionsInfoList(companyType, userId).orElse(null);
                Set<String> numbers = userVipPackageInfoList.stream().filter(Objects::nonNull).map(UserVipPackageInfo::getNumber).collect(Collectors.toSet());
                Set<String> types = orderClient.getServiceTypeList(companyType, numbers).orElse(new HashSet<>());
                if (numbers.contains("l13")) {
                    types.add("yyz");
                }
                if (types.contains(eventmap.getOrDefault(event.getNumber(), null))) {
                    notificationClient.setPushSubscriptionStatus(operatorId, userId, eventId, isSubscribe);
                } else {
                    throw new BusinessException("用户没有对应的服务包权限");
                }
            }
        } else {
            notificationClient.setPushSubscriptionStatus(operatorId, userId, eventId, isSubscribe);
        }
        return BaseResult.successful();
    }

    public PageResult<List<PushSubscriptionRecordResp>> getPushSubscriptionRecordList(Integer userId, Long eventId, Integer current, Integer size) {
        PageResult<List<NtPushSubscriptionRecord>> pageResult = notificationClient.getPushSubscriptionRecordList(userId, eventId, current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<NtPushSubscriptionRecord> pushSubscriptionRecordList = pageResult.getData();
        Set<Integer> operatorIds = pushSubscriptionRecordList.stream().filter(Objects::nonNull).map(NtPushSubscriptionRecord::getOperatorId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(operatorIds)).orElse(Collections.emptyList());
        Map<Integer, UcUsers> userMap = userList.stream().filter(Objects::nonNull).collect(Collectors.toMap(UcUsers::getId, Function.identity()));
        List<PushSubscriptionRecordResp> respList = BeanUtil.copyToList(pushSubscriptionRecordList, PushSubscriptionRecordResp.class);
        respList.forEach(resp -> {
            if (ObjectUtil.isEmpty(resp.getOperatorId())) {
                resp.setOperatorName("系统");
            } else {
                UcUsers user = userMap.get(resp.getOperatorId());
                resp.setOperatorName(user.getRealName());
            }
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

}

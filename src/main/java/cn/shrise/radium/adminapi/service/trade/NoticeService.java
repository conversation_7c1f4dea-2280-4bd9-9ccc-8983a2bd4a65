package cn.shrise.radium.adminapi.service.trade;

import cn.shrise.radium.adminapi.resp.trade.NoticeSubInfoResp;
import cn.shrise.radium.adminapi.resp.trade.NoticeSubRecordDtoResp;
import cn.shrise.radium.adminapi.resp.trade.NoticeSubRecordResp;
import cn.shrise.radium.adminapi.resp.user.UserInfoResp;
import cn.shrise.radium.adminapi.resp.wx.WxExtResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdNoticeSubInfo;
import cn.shrise.radium.tradeservice.entity.TdNoticeSubRecord;
import cn.shrise.radium.tradeservice.req.CreateSubReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NoticeService {

    private final TradeClient tradeClient;
    private final UserClient userClient;

    public void createSubInfo(Integer companyType, String userCode, Integer operatorId, List<CreateSubReq> noticeList) {
        if (Objects.nonNull(noticeList) && noticeList.size() > 0) {
            int userId = DesensitizeUtil.maskToId(userCode);
            tradeClient.createSubInfo(companyType, userId, operatorId, noticeList);
        } else {
            return;
        }
    }

    public BaseResult<List<NoticeSubInfoResp>> getSubList(String customerCode, Boolean isSub){
        int userId = DesensitizeUtil.maskToId(customerCode);
        List<TdNoticeSubInfo> tdNoticeSubInfos = tradeClient.getSubList(userId, isSub).orElseThrow();
        List<NoticeSubInfoResp> infoResps = tdNoticeSubInfos.stream().filter(Objects::nonNull).map(o -> {
            NoticeSubInfoResp resp = new NoticeSubInfoResp();
            BeanUtils.copyProperties(o, resp);
            resp.setCustomerCode(DesensitizeUtil.idToMask(o.getCustomerId()));
            return resp;
        }).collect(Collectors.toList());
        return BaseResult.success(infoResps);
    }

    public PageResult<List<NoticeSubRecordDtoResp>> getSubRecordList(Integer companyType, Instant startTime, Instant endTime,
                                                                     Boolean isSub, Integer noticeType, Integer customerId,
                                                                     Integer current, Integer size) {
        PageResult<List<TdNoticeSubRecord>> recordsRes = tradeClient.getSubRecordList(companyType, startTime, endTime,
                isSub, noticeType, customerId, current, size);
        if (recordsRes.isFail()) {
            throw new BusinessException(recordsRes);
        }
        List<TdNoticeSubRecord> records = recordsRes.getData();
        List<Integer> customerList =
                records.stream().map(TdNoticeSubRecord::getCustomerId).collect(Collectors.toList());
        List<Integer> salesList = records.stream().map(TdNoticeSubRecord::getOperatorId).collect(Collectors.toList());
        // 用户微信信息
        BaseResult<List<UcWxExt>> wxResult = userClient.getUnionIdsByUserId(customerList);
        if (wxResult.isFail()) {
            throw new BusinessException(wxResult);
        }
        List<UcWxExt> wxs = wxResult.getData();
        Map<Integer, UcWxExt> wxMap = wxs.stream().collect(Collectors.toMap(UcWxExt::getUserId, x -> x, (o, e) -> o));
        // 用户信息
        BaseResult<List<UcUsers>> usersResult = userClient.batchGetUserList(customerList);
        if (usersResult.isFail()) {
            throw new BusinessException(usersResult);
        }
        List<UcUsers> users = usersResult.getData();
        Map<Integer, UcUsers> userMap = users.stream().collect(Collectors.toMap(UcUsers::getId, x -> x));
        // 销售信息
        BaseResult<List<UcUsers>> salesResult = userClient.batchGetUserList(salesList);
        if (salesResult.isFail()) {
            throw new BusinessException(salesResult);
        }
        List<UcUsers> sales = salesResult.getData();
        Map<Integer, UcUsers> salesMap = sales.stream().collect(Collectors.toMap(UcUsers::getId, x -> x));

        List<NoticeSubRecordDtoResp> res = records.stream().map(o -> {
            NoticeSubRecordDtoResp resp = new NoticeSubRecordDtoResp();
            NoticeSubRecordResp record = new NoticeSubRecordResp();
            BeanUtils.copyProperties(o, record);
            record.setCustomerCode(DesensitizeUtil.idToMask(o.getCustomerId()));
            resp.setNoticeSubRecord(record);
            if (userMap.get(o.getCustomerId()) != null) {
                UserInfoResp userInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(userMap.get(o.getCustomerId()), userInfoResp);
                userInfoResp.setUserCode(DesensitizeUtil.idToMask(userInfoResp.getId()));
                resp.setCustomerInfo(userInfoResp);
            }
            if (salesMap.get(o.getOperatorId()) != null) {
                UserInfoResp salesInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(salesMap.get(o.getOperatorId()), salesInfoResp);
                resp.setOperatorInfo(salesInfoResp);
            }
            if (wxMap.get(o.getCustomerId()) != null) {
                WxExtResp wxExtResp = new WxExtResp();
                UcWxExt ucWxExt = wxMap.get(o.getCustomerId());
                BeanUtils.copyProperties(ucWxExt, wxExtResp);
                wxExtResp.setUserCode(DesensitizeUtil.idToMask(ucWxExt.getUserId()));
                resp.setWxExtInfo(wxExtResp);
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(res, Pagination.of(current, size, recordsRes.getPagination().getTotal()));
    }
}

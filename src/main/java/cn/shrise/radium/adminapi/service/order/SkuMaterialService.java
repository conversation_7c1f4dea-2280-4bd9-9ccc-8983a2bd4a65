package cn.shrise.radium.adminapi.service.order;

import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.resp.order.SkuMaterialItem;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.SkuCategoryConstant;
import cn.shrise.radium.orderservice.constant.SkuMaterialCategoryConstant;
import cn.shrise.radium.orderservice.entity.SkuMaterial;
import cn.shrise.radium.orderservice.req.CreateSkuMaterialReq;
import cn.shrise.radium.orderservice.req.UpdateSkuMaterialReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SkuMaterialService {

    private final OrderClient orderClient;
    private final UserClient userClient;

    public SkuMaterial createSkuMaterial(Integer companyType, Integer creatorId, cn.shrise.radium.adminapi.req.CreateSkuMaterialReq req) {
        CreateSkuMaterialReq createSkuMaterialReq = CreateSkuMaterialReq.builder()
                .companyType(companyType)
                .creatorId(creatorId)
                .category(req.getCategory())
                .fileName(req.getFileName())
                .fileUrl(req.getFileUrl())
                .description(req.getDescription())
                .build();
        BaseResult<SkuMaterial> result = orderClient.createSkuMaterial(createSkuMaterialReq);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        return result.getData();
    }

    public void createSkuMaterial(Integer companyType, Integer creatorId, Collection<cn.shrise.radium.adminapi.req.CreateSkuMaterialReq> req) {
        List<CreateSkuMaterialReq> batchReq = req.stream()
                .map(e -> CreateSkuMaterialReq.builder()
                        .companyType(companyType)
                        .creatorId(creatorId)
                        .category(e.getCategory())
                        .fileName(e.getFileName())
                        .fileUrl(e.getFileUrl())
                        .description(e.getDescription())
                        .build())
                .collect(Collectors.toList());
        BaseResult<Void> result = orderClient.createSkuMaterial(BatchReq.of(batchReq));
        if (result.isFail()) {
            throw new BusinessException(result);
        }
    }

    public void updateSkuMaterial(Long id, cn.shrise.radium.adminapi.req.UpdateSkuMaterialReq req) {
        UpdateSkuMaterialReq updateSkuMaterialReq = UpdateSkuMaterialReq.builder()
                .fileName(req.getFileName())
                .fileUrl(req.getFileUrl())
                .description(req.getDescription())
                .enabled(req.getEnabled())
                .build();
        orderClient.updateSkuMaterial(id, updateSkuMaterialReq);
    }

    public PageResult<List<SkuMaterialItem>> getSkuMaterialList(Integer companyType, Integer category, Boolean enabled,
                                                                Integer current, Integer size, Sort sort) {

        if (sort.isUnsorted()) {
            sort = Objects.equals(category, SkuMaterialCategoryConstant.CATEGORY_DESCRIPTION)?
                    Sort.by(Sort.Direction.DESC, "gmtModified", "id"): Sort.by(Sort.Direction.DESC, "id");
        }

        PageResult<List<SkuMaterial>> pageResult = orderClient.getSkuMaterialList(companyType, category, enabled, current, size, sort);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }
        Pagination pagination = pageResult.getPagination();
        List<Integer> userIdList = pageResult.getData().stream().map(SkuMaterial::getCreatorId).distinct().collect(Collectors.toList());
        BaseResult<Map<Integer, UcUsers>> batchGetUserResult = userClient.batchGetUserMap(BatchReq.create(userIdList));
        Map<Integer, UcUsers> userMap = batchGetUserResult.getData();
        List<SkuMaterialItem> items = pageResult.getData().stream().map(skuMaterial -> {
            Integer creatorId = skuMaterial.getCreatorId();
            return SkuMaterialItem.builder()
                    .id(skuMaterial.getId())
                    .companyType(skuMaterial.getCompanyType())
                    .category(skuMaterial.getCategory())
                    .fileName(skuMaterial.getFileName())
                    .fileUrl(skuMaterial.getFileUrl())
                    .description(skuMaterial.getDescription())
                    .creatorId(creatorId)
                    .creatorInfo(UserInfo.of(userMap.get(creatorId)))
                    .enabled(skuMaterial.getEnabled())
                    .gmtCreate(skuMaterial.getGmtCreate())
                    .gmtModified(skuMaterial.getGmtModified())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(items, pagination);
    }
}


package cn.shrise.radium.adminapi.service.marketing;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsArticle;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxSalesInviteUrl;
import cn.shrise.radium.marketingservice.req.GetOrCreateUserInviteUrlReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.req.CreateMaUrlLinkReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static cn.shrise.radium.common.constant.ProductTypeConstant.GC_PROD;
import static cn.shrise.radium.marketingservice.enums.InviteLinkType.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InviteLinkService {

    private final MarketingClient marketingClient;

    private final CommonProperties commonProperties;

    private final ContentClient contentClient;

    private final UserClient userClient;

    private final WxClient wxClient;


    public BaseResult<String> getContentShareInviteUrl(Long id, Integer salesId, Integer companyType, Integer accountType, Integer linkType) {
        BaseResult<UcUsers> userResult = userClient.getUser(salesId);
        if (userResult.isPresent()) {
            UcUsers salesInfo = userResult.getData();
            Integer productType = null;
            String linkUrl;
            boolean isMa = false;
            String query = "";
            if (linkType.equals(ILT_StreamMessage_Account.getValue())) {
                productType = GC_PROD;
                linkUrl = String.format("information/stream-detail?id=%s&salesNumber=%s", id, salesInfo.getNumber());
            } else if (linkType.equals(ILT_StreamMessage_Ma.getValue())) {
                isMa = true;
                linkUrl = "pages/streamDetail/streamDetail";
                query = String.format("id=%s&salesNumber=%s", id, salesInfo.getNumber());
            } else if (linkType.equals(ILT_Article_Account.getValue())) {
                SsArticle article = contentClient.getArticleInfoById(id).getData();
                linkUrl = String.format("daily-stock-detail?number=%s&salesNumber=%s", article.getNumber(), salesInfo.getNumber());
                productType = GC_PROD;
            } else if (linkType.equals(ILT_Article_Ma.getValue())) {
                SsArticle article = contentClient.getArticleInfoById(id).getData();
                isMa = true;
                linkUrl = "pages/articleDetail/articleDetail";
                query = String.format("number=%s&salesNumber=%s", article.getNumber(), salesInfo.getNumber());
            } else if (linkType.equals(ILT_Video_Account.getValue())) {
                linkUrl = String.format("video-detail/%s?salesNumber=%s", id, salesInfo.getNumber());
                productType = GC_PROD;
            } else if (linkType.equals(ILT_Video_Ma.getValue())) {
                isMa = true;
                linkUrl = "pages/videoStream/index";
                query = String.format("videoId=%s&salesNumber=%s", id, salesInfo.getNumber());
            } else if (linkType.equals(ILT_Video_Public.getValue())) {
                linkUrl =  String.format("information/native-video-play?videoId=%s&salesNumber=%s", id, salesInfo.getNumber());
                productType = GC_PROD;
            } else {
                log.info("不支持的链接类型");
                return BaseResult.success();
            }
            if (isMa) {
                query = query + "&source=crm";
                if (ObjectUtil.isEmpty(accountType)) {
                    accountType = 2099;
                }
                CreateMaUrlLinkReq createMaUrlLinkReq = CreateMaUrlLinkReq.builder()
                        .accountType(accountType)
                        .path(linkUrl)
                        .query(query)
                        .expireInterval(1)
                        .expireType(1)
                        .isExpire(true)
                        .expireInterval(7)
                        .build();
                return wxClient.generateMaUrlLink(createMaUrlLinkReq);
            }
            linkUrl = linkUrl + "&source=crm";
            GetOrCreateUserInviteUrlReq req = GetOrCreateUserInviteUrlReq.builder()
                    .salesId(salesId)
                    .linkType(linkType)
                    .linkUrl(linkUrl)
                    .companyType(companyType)
                    .build();
            NpWxSalesInviteUrl inviteInfo = marketingClient.getOrCreateUserInviteUrl(req).getData();
            return BaseResult.success(String.format("%s/i/%d/%s", commonProperties.getWebApiUrl(), productType, inviteInfo.getNumber()));
        }
        return BaseResult.success();
    }
}

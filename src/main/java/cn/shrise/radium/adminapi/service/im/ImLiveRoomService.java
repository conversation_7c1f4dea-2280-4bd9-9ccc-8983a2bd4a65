package cn.shrise.radium.adminapi.service.im;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.im.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatBlackInfo;
import cn.shrise.radium.imservice.entity.ImLiveRoom;
import cn.shrise.radium.imservice.entity.ImRoomBlackInfo;
import cn.shrise.radium.imservice.entity.ImRoomBlackRecord;
import cn.shrise.radium.imservice.resp.ImLiveRoomResp;
import cn.shrise.radium.imservice.properties.ImPageMappingProperties;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import cn.shrise.radium.userservice.resp.PageStatistics;
import cn.shrise.radium.userservice.resp.UserAndWxInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImLiveRoomService {

    private final ImClient imClient;

    private final UserClient userClient;

    public LiveRoomResp getLiveRoom(Long roomId) {
        BaseResult<ImLiveRoom> result = imClient.getLiveRoom(roomId);
        if (result.isPresent()) {
            return LiveRoomResp.of(result.getData());
        }
        return null;
    }

    public ImLiveRoomDecorateResp getLiveRoomDecorate(Long roomId) {
        BaseResult<ImLiveRoom> result = imClient.getLiveRoom(roomId);
        if (result.isPresent()) {
            return ImLiveRoomDecorateResp.of(result.getData());
        }
        return null;
    }

    public BaseResult<List<ImLiveRoomResp>> getMyLiveRoom(Integer userId) {
        BaseResult<List<ImLiveRoom>> result = imClient.getVisibleLiveRoomBySales(userId);
        if (result.isFail()){
            throw new BusinessException(result);
        }
        if (!result.isPresent()){
            return BaseResult.success(Collections.EMPTY_LIST);
        }

        List<ImLiveRoomResp> resps = result.getData().stream().map(room -> {
            ImLiveRoomResp resp = new ImLiveRoomResp();
            BeanUtil.copyProperties(room, resp, CopyOptions.create().ignoreNullValue());
            return resp;
        }).collect(Collectors.toList());

        return BaseResult.success(resps);
    }

    public BaseResult<PageStatistics> getPageStatistics(Long roomId, Long date) {
        BaseResult<ImPageMappingProperties.RoomPage> result = imClient.getRoomPageInfo(roomId);
        if (result.isPresent()) {
            ImPageMappingProperties.RoomPage page = result.getData();
            long start = DateUtils.getDayOfStart(Instant.ofEpochMilli(date)).getEpochSecond();
            long end = DateUtils.getDayOfEnd(Instant.ofEpochMilli(date)).getEpochSecond();
            return userClient.getPageStatistics(page.getAppId(), page.getPageId(), start, end);
        }
        return null;
    }

    public PageResult<List<RoomBlackInfoResp>> getRoomBlackList(Long roomId, String searchContent, Integer current, Integer size) {
        PageResult<List<ImChatBlackInfo>> roomBlackList = imClient.getRoomBlackList(roomId, searchContent, current, size);
        if (ObjectUtil.isEmpty(roomBlackList.getData())) {
            return PageResult.success(null, roomBlackList.getPagination());
        }
        List<Integer> userIdList = roomBlackList.getData().stream().filter(Objects::nonNull).map(ImChatBlackInfo::getUserId).collect(Collectors.toList());
        List<UserAndWxInfoResp> userAndWxInfoRespList = userClient.getUserAndWxInfoBatch(userIdList).orElse(new ArrayList<>());
        Map<Integer, UserAndWxInfoResp> respMap = userAndWxInfoRespList.stream().collect(Collectors.toMap(i -> i.getUcUsers().getId(), i -> i));
        List<RoomBlackInfoResp> respList = roomBlackList.getData().stream().filter(Objects::nonNull).map(info -> {
            UserAndWxInfoResp infoResp = respMap.getOrDefault(info.getUserId(), new UserAndWxInfoResp());
            UcUsers ucUsers = infoResp.getUcUsers() != null ? infoResp.getUcUsers() : new UcUsers();
            UcWxExt ucWxExt = infoResp.getUcWxExt() != null ? infoResp.getUcWxExt() : new UcWxExt();
            return RoomBlackInfoResp.builder()
                    .id(info.getId())
                    .createTime(info.getGmtModified())
                    .userCode(DesensitizeUtil.idToMask(info.getUserId()))
                    .avatarUrl(ucUsers.getAvatarUrl())
                    .nickName(ucUsers.getNickName())
                    .maskMobile(ucUsers.getMaskMobile())
                    .wxId(ucWxExt.getId())
                    .remark(info.getRemark())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(respList, roomBlackList.getPagination());
    }

    public List<RoomUserBlackInfoResp> searchRoomBlackInfo(Long roomId, String searchContent) {
        List<UserAndWxInfoResp> userAndWxInfoRespList = userClient.getUserAndWxInfo(searchContent).orElseThrow();
        List<Integer> userIdList = userAndWxInfoRespList.stream()
                .filter(o -> o != null && o.getUcUsers() != null)
                .map(o -> o.getUcUsers().getId())
                .collect(Collectors.toList());
        List<ImRoomBlackInfo> imRoomBlackInfos = imClient.getRoomBlackListByUserList(roomId, userIdList).orElse(new ArrayList<>());
        Map<Integer, ImRoomBlackInfo> infoMap = imRoomBlackInfos.stream().collect(Collectors.toMap(ImRoomBlackInfo::getUserId, i -> i));
        return userAndWxInfoRespList.stream().filter(Objects::nonNull)
                .map(o -> {
                    UcUsers ucUsers = o.getUcUsers() != null ? o.getUcUsers() : new UcUsers();
                    UcWxExt ucWxExt = o.getUcWxExt() != null ? o.getUcWxExt() : new UcWxExt();
                    ImRoomBlackInfo blackInfo = infoMap.getOrDefault(ucUsers.getId(), new ImRoomBlackInfo());
                    return RoomUserBlackInfoResp.builder()
                            .userCode(DesensitizeUtil.idToMask(ucUsers.getId()))
                            .nickName(ucUsers.getNickName())
                            .wxNickname(ucWxExt.getNickname())
                            .avatarUrl(ucUsers.getAvatarUrl())
                            .id(blackInfo.getId())
                            .enabled(ObjectUtil.equal(blackInfo.getEnabled(), true))
                            .build();
                }).collect(Collectors.toList());
    }

    public BaseResult<List<RoomBlackModifyRecordResp>> getBlackOperateRecord(Long roomId, String userCode) {
        if (!DesensitizeUtil.isValidUserCode(userCode)) {
            throw new BusinessException("用户编码格式错误");
        }
        int userId = DesensitizeUtil.maskToId(userCode);
        List<ImRoomBlackRecord> imRoomBlackRecords = imClient.getRoomBlackOperateRecord(roomId, userId).orElseThrow();
        List<Integer> operatorIdList = imRoomBlackRecords.stream().filter(Objects::nonNull)
                .map(ImRoomBlackRecord::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(operatorIdList)).orElse(new HashMap<>());
        return BaseResult.success(imRoomBlackRecords.stream().map(record -> {
            Integer operatorId = record.getOperatorId();
            UcUsers operator = usersMap.getOrDefault(operatorId, new UcUsers());
            return RoomBlackModifyRecordResp.builder()
                    .operatorId(operatorId)
                    .operatorName(operator.getRealName())
                    .operatorAvatarUrl(operator.getAvatarUrl())
                    .remark(record.getRemark())
                    .enabled(record.getEnabled())
                    .gmtCreate(record.getGmtCreate())
                    .userCode(DesensitizeUtil.idToMask(record.getUserId()))
                    .build();
        }).collect(Collectors.toList()));
    }
}

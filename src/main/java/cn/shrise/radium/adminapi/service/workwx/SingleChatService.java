package cn.shrise.radium.adminapi.service.workwx;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.WxAccountExternalUserInfo;
import cn.shrise.radium.workwxservice.resp.ChatRecordRiskTwoResp;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SingleChatService {

    private final WorkwxClient workwxClient;

    public Map<Integer, List<ChatRecordRiskTwoResp>> getChatRecordRisk(Integer relationId, Long startTime, Long endTime, Boolean isAsc, Integer current, Integer size) {
        BaseResult<WxAccountExternalUserInfo> wxAccountExternalUser = workwxClient.getWxAccountExternalUser(relationId);
        if (wxAccountExternalUser.isSuccess()) {
            WxAccountExternalUserInfo data = wxAccountExternalUser.getData();
            return getChatRecordRisk(data.getAccountType(), data.getWxAccount(), data.getExternalUserId(), startTime, endTime, isAsc, current, size);
        }
        return null;
    }

    public Map<Integer, List<ChatRecordRiskTwoResp>> getChatRecordRisk(Integer accountType, String wxAccount, String externalUserId, Long startTime, Long endTime, Boolean isAsc, Integer current, Integer size) {
        PageResult<List<ChatRecordRiskTwoResp>> chatRecordRisk = workwxClient.getChatRecordRiskNew(accountType, wxAccount, externalUserId, startTime, endTime, isAsc, current, size);
        Map<Integer,List<ChatRecordRiskTwoResp>> riskMap = new HashMap<>();
        if (chatRecordRisk.isSuccess()) {
            List<ChatRecordRiskTwoResp> data = chatRecordRisk.getData();
            List<List<ChatRecordRiskTwoResp>> partition = Lists.partition(data, size);
            for (int i = 0; i < partition.size(); i++) {
                riskMap.put(i,partition.get(i));
            }
            return riskMap;
        }
        return null;
    }
}

package cn.shrise.radium.adminapi.service.marketing;

import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.marketingservice.req.media.MediaAccountReq;
import cn.shrise.radium.marketingservice.resp.media.MediaAccountRecordResp;
import cn.shrise.radium.marketingservice.req.media.EnableMediaAccountReq;
import cn.shrise.radium.marketingservice.req.media.UpdateMediaAccountReq;
import cn.shrise.radium.marketingservice.req.media.CreateMediaAccountReq;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.marketingservice.resp.media.MediaAccountResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MediaAccountService {

    private final MarketingClient marketingClient;
    private final UserClient userClient;

    public PageResult<List<MediaAccountResp>> getMediaAccountList(MediaAccountReq req, Integer current, Integer size) {
        PageResult<List<MediaAccountResp>> result = marketingClient.getMediaAccountList(req, current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<MediaAccountResp> mediaAccountResp = result.getData();
        Set<Integer> userIds = mediaAccountResp.stream().map(MediaAccountResp::getBelongId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        for (MediaAccountResp mediaAccount : result.getData()) {
            mediaAccount.setBelongName(userMap.getOrDefault(mediaAccount.getBelongId(), new UcUsers()).getRealName());
        }
        return result;
    }

    public BaseResult<Void> createMediaAccount(CreateMediaAccountReq req) {
        return marketingClient.createMediaAccount(req);
    }

    public BaseResult<Void> updateMediaAccount(UpdateMediaAccountReq req) {
        return marketingClient.updateMediaAccount(req);
    }

    public BaseResult<Void> enableMediaAccount(EnableMediaAccountReq req) {
        return marketingClient.enableMediaAccount(req);
    }

    public PageResult<List<MediaAccountRecordResp>> getMediaAccountOperateRecord(Integer current, Integer size) {
        PageResult<List<MediaAccountRecordResp>> result = marketingClient.getMediaAccountOperateRecord(current, size);
        if (!result.isPresent()) {
            return PageResult.empty();
        }
        List<MediaAccountRecordResp> record = result.getData();
        Set<Integer> userIds = record.stream().map(MediaAccountRecordResp::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        for (MediaAccountRecordResp mediaAccountRecordResp : result.getData()) {
            mediaAccountRecordResp.setOperatorName(userMap.getOrDefault(mediaAccountRecordResp.getOperatorId(), new UcUsers()).getRealName());
        }
        return result;
    }
}

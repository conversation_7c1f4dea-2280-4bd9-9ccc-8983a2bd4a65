package cn.shrise.radium.adminapi.service.roboadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.roboadviser.StrategyPassagewayResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxSalesInviteUrl;
import cn.shrise.radium.marketingservice.req.GetOrCreateUserInviteUrlReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.orderservice.resp.SkuStrategy;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.entity.RaPassagewayRevenue;
import cn.shrise.radium.roboadviserservice.entity.RaStrategy;
import cn.shrise.radium.roboadviserservice.resp.StrategyBackTestListResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyInfoResp;
import cn.shrise.radium.roboadviserservice.resp.StrategySimulationListResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.ProductTypeConstant.GC_ROBO;
import static cn.shrise.radium.marketingservice.enums.InviteLinkType.ILT_Robo_Adviser;

@Service
@RequiredArgsConstructor
public class StrategyService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final UserClient userClient;
    private final MarketingClient marketingClient;
    private final CommonProperties commonProperties;
    private final OrderClient orderClient;

    public List<StrategyPassagewayResp> listStrategyPassageway(String code) {
        BaseResult<List<RaPassagewayRevenue>> baseResult = roboAdviserServiceClient.listStrategyPassagewayRevenue(code);
        if (baseResult.isSuccess()) {
            List<StrategyPassagewayResp> respList = new ArrayList<>();
            baseResult.getData().forEach(raPassagewayRevenue -> {
                StrategyPassagewayResp resp = new StrategyPassagewayResp();
                BeanUtils.copyProperties(raPassagewayRevenue, resp);
                resp.setPassagewayName(String.format("通道%d", raPassagewayRevenue.getPassagewayId()));
                respList.add(resp);
            });
            return respList;
        }
        return Collections.emptyList();
    }


    public BaseResult<String> getStrategyUrl(Integer companyType, Integer userId, String strategyCode, Integer skuId, Boolean isList) {
        UcUsers salesInfo = userClient.getUser(userId).getData();
        String linkUrl;
        String skuNumber = null;
        if (ObjectUtil.isNotEmpty(skuId)) {
            RsSku rsSku = orderClient.getSku(skuId).orElseThrow();
            skuNumber = rsSku.getNumber();
        }
        if (Boolean.TRUE.equals(isList)) {
            linkUrl = String.format("strategy-pre/strategy-list?salesNumber=%s", salesInfo.getNumber());
        } else {
            if (ObjectUtil.isNotEmpty(skuNumber)) {
                linkUrl = String.format("strategy-pre/strategy-detail/%s?salesNumber=%s?skuNumber=%s", strategyCode, salesInfo.getNumber(), skuNumber);
            } else {
                linkUrl = String.format("strategy-pre/strategy-detail/%s?salesNumber=%s", strategyCode, salesInfo.getNumber());
            }
        }
        GetOrCreateUserInviteUrlReq req = GetOrCreateUserInviteUrlReq.builder()
                .salesId(userId)
                .linkType(ILT_Robo_Adviser.getValue())
                .linkUrl(linkUrl)
                .companyType(companyType)
                .build();
        NpWxSalesInviteUrl inviteInfo = marketingClient.getOrCreateUserInviteUrl(req).getData();
        return BaseResult.success(String.format("%s/i/%d/%s", commonProperties.getWebApiUrl(), GC_ROBO, inviteInfo.getNumber()));
    }

    public PageResult<List<StrategyBackTestListResp>> getStrategyBackTestList(Integer userId, LocalDate createStart, LocalDate createEnd, Integer current, Integer size) {

        PageResult<List<StrategyBackTestListResp>> pageResult = roboAdviserServiceClient.getStrategyBackTestList(userId, createStart, createEnd, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty(current, size);
        }
        List<StrategyBackTestListResp> respList = pageResult.getData();

        List<StrategyInfoResp> strategyList = roboAdviserServiceClient.getAllStrategy(null).orElseThrow();
        Map<String, StrategyInfoResp> strategyMap = strategyList.stream().collect(Collectors.toMap(StrategyInfoResp::getCode, Function.identity()));
        respList.forEach(resp -> {
            resp.setStrategyName(strategyMap.getOrDefault(resp.getStrategy(), new StrategyInfoResp()).getName());
            resp.setEnabled(strategyMap.getOrDefault(resp.getStrategy(), new StrategyInfoResp()).getEnabled());
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

    public PageResult<List<StrategySimulationListResp>> getStrategyTerminateList(Integer userId, LocalDate createStart, LocalDate createEnd, Integer current, Integer size) {
        PageResult<List<StrategySimulationListResp>> pageResult = roboAdviserServiceClient.getStrategyTerminateList(userId, createStart, createEnd, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty(current, size);
        }
        List<StrategySimulationListResp> respList = pageResult.getData();

        List<StrategyInfoResp> strategyList = roboAdviserServiceClient.getAllStrategy(null).orElseThrow();
        Map<String, StrategyInfoResp> strategyMap = strategyList.stream().collect(Collectors.toMap(StrategyInfoResp::getCode, Function.identity()));
        respList.forEach(resp -> {
            resp.setStrategyName(strategyMap.getOrDefault(resp.getStrategy(), new StrategyInfoResp()).getName());
            resp.setStrategyEnabled(strategyMap.getOrDefault(resp.getStrategy(), new StrategyInfoResp()).getEnabled());
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

    public BaseResult<List<SkuStrategy>> getStrategySkuList(Long strategyId, Integer status) {

        RaStrategy raStrategy = roboAdviserServiceClient.getStrategy(strategyId).orElseThrow();
        List<SkuStrategy> skuStrategyList = orderClient.getSkuStrategyList(strategyId, status).orElseThrow();
        List<Integer> skuIds = skuStrategyList.stream().map(SkuStrategy::getSkuId).collect(Collectors.toList());
        Map<Integer, RsSku> skuMap = orderClient.batchGetSkuList(BatchReq.of(skuIds)).orElseThrow().stream().collect(Collectors.toMap(RsSku::getId, Function.identity()));
        skuStrategyList.forEach(skuStrategy -> {
            skuStrategy.setSkuName(skuMap.getOrDefault(skuStrategy.getSkuId(), new RsSku()).getShowName());
            skuStrategy.setSkuNumber(skuMap.getOrDefault(skuStrategy.getSkuId(), new RsSku()).getNumber());
            skuStrategy.setStrategyName(raStrategy.getName());
        });

        return BaseResult.success(skuStrategyList);

    }
}

package cn.shrise.radium.adminapi.service.workwx;

import cn.shrise.radium.adminapi.resp.workWx.FullRelationInfoResp;
import cn.shrise.radium.common.base.BaseResult;


import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WorkWxFullContactUserRelation;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContact;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactUserRelation;
import cn.shrise.radium.workwxservice.entity.NpWorkWxUser;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WorkWxContactService {

    private final WorkwxClient workwxClient;

    public FullRelationInfoResp getWorkWxFriendInfo(Integer accountType, String wxAccount, String externalUserId) {
        WorkWxFullContactUserRelation fullRelation = workwxClient.getFullFriendRelation(accountType, wxAccount, externalUserId).getData();
        NpWorkWxContactUserRelation relation = fullRelation.getRelation();
        NpWorkWxUser user = fullRelation.getWorkWxUser();
        NpWorkWxContact contact = fullRelation.getContact();
        return FullRelationInfoResp.builder()
                .id(relation.getPkId())
                .accountType(relation.getAccountType())
                .wxAccount(wxAccount)
                .userName(user.getName())
                .contactName(contact.getName())
                .externalUserId(externalUserId)
                .contactEnabled(relation.getContactEnabled())
                .userEnabled(relation.getUserEnabled())
                .createTime(relation.getCreateTime())
                .build();
    }


}

package cn.shrise.radium.adminapi.service.roboadviser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.adminapi.resp.roboadviser.StrategyOrderLinkResp;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ProductTypeConstant;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxSalesInviteUrl;
import cn.shrise.radium.marketingservice.enums.InviteLinkType;
import cn.shrise.radium.marketingservice.req.GetOrCreateUserInviteUrlReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.OrderStatusEnum;
import cn.shrise.radium.orderservice.constant.SkuProductLevelConstant;
import cn.shrise.radium.orderservice.constant.SkuStatusConstant;
import cn.shrise.radium.orderservice.constant.StrategyOrderLinkBuyTypeConstant;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.orderservice.entity.RsOrderArtificialFeedback;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.orderservice.entity.RsStrategyOrderLink;
import cn.shrise.radium.orderservice.resp.SkuStrategy;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.constant.StrategyStatus;
import cn.shrise.radium.roboadviserservice.entity.RaCustomer;
import cn.shrise.radium.roboadviserservice.entity.RaStrategy;
import cn.shrise.radium.roboadviserservice.entity.RaStrategySubscription;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.adminapi.constant.ErrorConstant.*;
import static cn.shrise.radium.common.util.LockUtils.getStrategyOrderLinkLockKey;
import static cn.shrise.radium.common.util.LockUtils.locked;

@Slf4j
@Service
@RequiredArgsConstructor
public class StrategyOrderLinkService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final OrderClient orderClient;
    private final UserClient userClient;
    private final CommonService commonService;
    private final RedissonClient redissonClient;
    private final CommonProperties commonProperties;
    private final MarketingClient marketingClient;

    public StrategyOrderLinkResp previewStrategyLink(String customerCode, Integer skuId, Integer buyType, Long fund) {
        int customerId = DesensitizeUtil.maskToId(customerCode);
        return check(customerCode, customerId, skuId, buyType, fund);
    }

    public StrategyOrderLinkResp createStrategyLink(Integer companyType, Integer creatorId, String customerCode,
                                                    Integer skuId, Integer buyType, Long fund) {
        int customerId = DesensitizeUtil.maskToId(customerCode);
        String orderKey = getStrategyOrderLinkLockKey(customerId);
        RLock lock = redissonClient.getLock(orderKey);
        return locked(lock, () -> {
            StrategyOrderLinkResp resp = check(customerCode, customerId, skuId, buyType, fund);
            RsStrategyOrderLink strategyOrderLink = orderClient.createStrategyLink(companyType, creatorId,
                    customerId, skuId, buyType, fund, resp.getCurrentFund(), null,
                    resp.getCurrentServiceExpireTime(), resp.getPayAmount(), resp.getFormula()).getData();
            resp.setNumber(strategyOrderLink.getNumber());
            resp.setGmtCreate(strategyOrderLink.getGmtCreate());
            resp.setExpireTime(strategyOrderLink.getExpireTime());
            return resp;
        });
    }

    public PageResult<List<StrategyOrderLinkResp>> getStrategyLinkList(Integer companyType, Integer userId, Integer current, Integer size) {
        PageResult<List<RsStrategyOrderLink>> result = orderClient.getStrategyLinkList(companyType, userId, current, size);
        if (!result.isPresent()) {
            return PageResult.empty(current, size);
        }
        List<RsStrategyOrderLink> strategyOrderLinkList = result.getData();
        Set<Integer> orderIdSet = new HashSet<>();
        Set<Integer> customerIdSet = new HashSet<>();
        Set<Integer> skuIdSet = new HashSet<>();
        strategyOrderLinkList.forEach(e -> {
            customerIdSet.add(e.getCustomerId());
            skuIdSet.add(e.getSkuId());
            if (ObjectUtil.isNotNull(e.getOrderId())) {
                orderIdSet.add(e.getOrderId());
            }
        });
        Map<Integer, RsCourseOrder> orderMap = orderClient.batchGetOrderListById(new ArrayList<>(orderIdSet)).getData()
                .stream().collect(Collectors.toMap(RsCourseOrder::getId, Function.identity()));
        Map<Integer, UcUsers> customerMap = userClient.batchGetUserMap(BatchReq.create(customerIdSet)).getData();
        Map<Integer, RsSku> skuMap = orderClient.batchGetSkuMap(BatchReq.create(skuIdSet)).getData();
        Map<Integer, SkuStrategy> skuStrategyMap = orderClient.batchSkuStrategyBySku(BatchReq.create(skuIdSet)).getData()
                .stream().collect(Collectors.toMap(SkuStrategy::getSkuId, Function.identity()));
        Set<Long> strategyIdSet = skuStrategyMap.values().stream().map(SkuStrategy::getStrategyId).collect(Collectors.toSet());
        Map<Long, RaStrategy> strategyMap = roboAdviserServiceClient.batchStrategyById(BatchReq.create(strategyIdSet)).getData()
                .stream().collect(Collectors.toMap(RaStrategy::getId, Function.identity()));
        List<StrategyOrderLinkResp> recordList = strategyOrderLinkList.stream().map(e -> {
            StrategyOrderLinkResp resp = new StrategyOrderLinkResp();
            resp.setCustomerId(e.getCustomerId());
            resp.setCustomerCode(DesensitizeUtil.idToMask(e.getCustomerId()));
            resp.setCustomerName(customerMap.get(e.getCustomerId()).getNickName());
            resp.setSkuId(e.getSkuId());
            resp.setSkuName(skuMap.get(e.getSkuId()).getName());
            resp.setSkuStatus(skuMap.get(e.getSkuId()).getStatus());
            resp.setStrategyName(strategyMap.get(skuStrategyMap.get(e.getSkuId()).getStrategyId()).getName());
            resp.setNumber(e.getNumber());
            resp.setBuyType(e.getBuyType());
            resp.setFund(e.getFund());
            resp.setCurrentFund(e.getCurrentFund());
            resp.setServiceExpireTime(e.getServiceExpireTime());
            resp.setCurrentServiceExpireTime(e.getCurrentServiceExpireTime());
            resp.setPayAmount(e.getPayAmount());
            resp.setFormula(e.getFormula());
            resp.setRate(skuStrategyMap.get(e.getSkuId()).getRate());
            resp.setPeriod(skuStrategyMap.get(e.getSkuId()).getPeriod());
            resp.setExpireTime(e.getExpireTime());
            resp.setGmtCreate(e.getGmtCreate());
            resp.setEnabled(e.getEnabled());
            if (ObjectUtil.equals(e.getEnabled(), true) && !ObjectUtil.equal(e.getBuyType(), StrategyOrderLinkBuyTypeConstant.ADD_AMOUNT)) {
                Instant expectServiceExpireTime;
                if (ObjectUtil.equal(e.getBuyType(), StrategyOrderLinkBuyTypeConstant.NEW_BUY)) {
                    expectServiceExpireTime = DateUtils.getDayOfEnd(Instant.now()).plus(skuStrategyMap.get(e.getSkuId()).getPeriod(), ChronoUnit.DAYS);
                } else if (ObjectUtil.equal(e.getBuyType(), StrategyOrderLinkBuyTypeConstant.ADD_PERIOD)) {
                    expectServiceExpireTime = (e.getCurrentServiceExpireTime().isAfter(Instant.now()) ?
                            e.getCurrentServiceExpireTime() : DateUtils.getDayOfEnd(Instant.now())).plus(skuStrategyMap.get(e.getSkuId()).getPeriod(), ChronoUnit.DAYS);
                } else {
                    expectServiceExpireTime = e.getCurrentServiceExpireTime().plus(skuStrategyMap.get(e.getSkuId()).getPeriod(), ChronoUnit.DAYS);
                }
                resp.setExpectServiceExpireTime(expectServiceExpireTime);
            }
            if (ObjectUtil.isNotNull(e.getOrderId())) {
                resp.setOrderNumber(orderMap.get(e.getOrderId()).getOrderNumber());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(recordList, result.getPagination());
    }

    private StrategyOrderLinkResp check(String customerCode, Integer customerId, Integer skuId, Integer buyType, Long fund) {
        UcUsers customer = userClient.getUser(customerId).getData();
        RsSku sku = orderClient.getSku(skuId).getData();
        if (ObjectUtil.isNull(sku) || !ObjectUtil.equals(SkuProductLevelConstant.STRATEGY.getValue(), sku.getProductLevel())) {
            throw new BusinessException(SKU_NOT_EXISTED);
        }
        if (!ObjectUtil.equals(SkuStatusConstant.ON_SALE, sku.getStatus())) {
            throw new BusinessException(SKU_NOT_ON_SALE);
        }
        SkuStrategy skuStrategy = orderClient.getSkuStrategyBySku(skuId).getData();
        RaStrategy strategy = roboAdviserServiceClient.getStrategy(skuStrategy.getStrategyId()).getData();
        if (ObjectUtil.isNull(strategy) || !ObjectUtil.equals(StrategyStatus.SEll.getCode(), strategy.getStatus())) {
            throw new BusinessException(STRATEGY_NOT_ON_SALE);
        }
        RsStrategyOrderLink strategyOrderLink = orderClient.getUsefulLink(customerId, skuStrategy.getStrategyId()).getData();
        if (ObjectUtil.isNotNull(strategyOrderLink)) {
            throw new BusinessException(STRATEGY_ORDER_LINK_EXISTED);
        }
        RaStrategySubscription strategySubscription = null;
        RaCustomer raCustomer = roboAdviserServiceClient.findOneByUserId(customerId).getData();
        if (ObjectUtil.isNotNull(raCustomer)) {
            strategySubscription = roboAdviserServiceClient.getCustomerSubscription(raCustomer.getCustomerId(), strategy.getCode()).getData();
        }
        if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.NEW_BUY) && ObjectUtil.isNotNull(strategySubscription)
                && strategySubscription.getExpireTime().isAfter(Instant.now())) {
            throw new BusinessException(STRATEGY_ORDER_LINK_NEW_BUY_BUT_IN_SERVICE);
        }
        if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.NEW_BUY) && (ObjectUtil.isNull(fund)
                || fund < strategy.getFixed() * 1000000L)) {
            throw new BusinessException(STRATEGY_ORDER_LINK_AMOUNT_LESS);
        }
        if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_PERIOD) && (ObjectUtil.isNull(strategySubscription)
                || strategySubscription.getExpireTime().isBefore(Instant.now()))) {
            throw new BusinessException(STRATEGY_ORDER_LINK_ADD_PERIOD_BUT_NO_SERVICE);
        }
        if ((ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_AMOUNT) ||
                ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_AMOUNT_PERIOD)) && (ObjectUtil.isNull(strategySubscription)
                || strategySubscription.getExpireTime().isBefore(Instant.now().plus(30, ChronoUnit.DAYS)))) {
            throw new BusinessException(STRATEGY_ORDER_LINK_ADD_AMOUNT_BUT_SERVICE_NOT_MATCH);
        }
        Long currentFund = ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.NEW_BUY) ? null : strategySubscription.getAmount();
        Instant currentServiceExpireTime = ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.NEW_BUY) ? null : strategySubscription.getExpireTime();
        Instant expectServiceExpireTime = null;
        if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.NEW_BUY)) {
            expectServiceExpireTime = DateUtils.getDayOfEnd(Instant.now()).plus(skuStrategy.getPeriod(), ChronoUnit.DAYS);
        } else if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_PERIOD) ||
                ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_AMOUNT_PERIOD)) {
            expectServiceExpireTime = currentServiceExpireTime.plus(skuStrategy.getPeriod(), ChronoUnit.DAYS);
        }
        Double payAmount = null;
        String formula = null;
        if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.NEW_BUY)) {
            payAmount = fund * skuStrategy.getRate() / 100.0;
            formula = StrUtil.format("{}*{}%", fund / 100, skuStrategy.getRate());
        } else if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_PERIOD)) {
            payAmount = currentFund * skuStrategy.getRate() / 100.0;
            formula = StrUtil.format("{}*{}%", currentFund / 100, skuStrategy.getRate());
        } else if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_AMOUNT)) {
            long days = ChronoUnit.DAYS.between(LocalDateTime.now(), currentServiceExpireTime.atZone(ZoneId.systemDefault()).toLocalDateTime());
            payAmount = fund * skuStrategy.getRate() * days / 100.0 / skuStrategy.getPeriod();
            formula = StrUtil.format("{}*{}%*{}/{}", fund / 100, skuStrategy.getRate(), days, skuStrategy.getPeriod());
        } else if (ObjectUtil.equals(buyType, StrategyOrderLinkBuyTypeConstant.ADD_AMOUNT_PERIOD)) {
            long days = ChronoUnit.DAYS.between(LocalDateTime.now(), currentServiceExpireTime.atZone(ZoneId.systemDefault()).toLocalDateTime());
            payAmount = fund * skuStrategy.getRate() * days / 100.0 / skuStrategy.getPeriod() + (currentFund + fund) * skuStrategy.getRate() / 100.0;
            formula = StrUtil.format("{}*{}%*{}/{}+{}*{}%", fund / 100, skuStrategy.getRate(), days, skuStrategy.getPeriod(), (currentFund + fund) / 100, skuStrategy.getRate());
        }
        return StrategyOrderLinkResp.builder()
                .skuId(skuId)
                .skuName(sku.getName())
                .strategyName(strategy.getName())
                .customerId(customerId)
                .customerCode(customerCode)
                .customerName(customer.getNickName())
                .buyType(buyType)
                .rate(skuStrategy.getRate())
                .fund(fund)
                .currentFund(currentFund)
                .expectServiceExpireTime(expectServiceExpireTime)
                .currentServiceExpireTime(currentServiceExpireTime)
                .period(skuStrategy.getPeriod())
                .skuStatus(sku.getStatus())
                .payAmount(Math.round(payAmount))
                .formula(formula)
                .build();
    }

    public String getInviteUrl(String number) {
        RsStrategyOrderLink strategyOrderLink = orderClient.getStrategyLinkByNumber(number).getData();
        String linkUrl = StrUtil.format("strategyInfo/{}", number);
        GetOrCreateUserInviteUrlReq req = GetOrCreateUserInviteUrlReq.builder()
                .salesId(strategyOrderLink.getCreatorId())
                .linkUrl(linkUrl)
                .companyType(strategyOrderLink.getCompanyType())
                .linkType(InviteLinkType.ILT_Robo_Order_Link.getValue())
                .build();
        BaseResult<NpWxSalesInviteUrl> salesInviteUrls = marketingClient.getOrCreateUserInviteUrl(req);
        if (ObjectUtils.isEmpty(salesInviteUrls)) {
            return null;
        }
        NpWxSalesInviteUrl salesInvite = salesInviteUrls.getData();
        return String.format("%s/i/%d/%s", commonProperties.getShortUrl(), ProductTypeConstant.GC_PAY, salesInvite.getNumber());
    }

    public StrategyOrderLinkResp getStrategyLinkInfo(Integer orderId) {
        RsStrategyOrderLink strategyOrderLink = orderClient.getStrategyLinkByOrderId(orderId).orElse(null);
        if (ObjectUtil.isEmpty(strategyOrderLink)) {
            return null;
        }
        RsCourseOrder rsCourseOrder = orderClient.getOrder(orderId).orElseThrow();
        SkuStrategy skuStrategy = orderClient.getSkuStrategyBySku(rsCourseOrder.getSkuId()).orElseThrow();
        UcUsers customer = userClient.getUser(strategyOrderLink.getCustomerId()).getData();
        RsSku sku = orderClient.getSku(strategyOrderLink.getSkuId()).getData();
        RaStrategy strategy = roboAdviserServiceClient.getStrategy(skuStrategy.getStrategyId()).getData();
        StrategyOrderLinkResp resp = new StrategyOrderLinkResp();
        BeanUtil.copyProperties(strategyOrderLink, resp);
        resp.setCustomerCode(DesensitizeUtil.idToMask(resp.getCustomerId()));
        resp.setCustomerName(customer.getNickName());
        resp.setSkuName(sku.getName());
        resp.setSkuStatus(sku.getStatus());
        resp.setStrategyName(strategy.getName());
        resp.setRate(skuStrategy.getRate());
        resp.setPeriod(skuStrategy.getPeriod());
        resp.setOrderNumber(rsCourseOrder.getOrderNumber());
        resp.setServiceExpireTime(strategyOrderLink.getServiceExpireTime());
        if (ObjectUtil.equals(strategyOrderLink.getEnabled(), true) && !ObjectUtil.equal(strategyOrderLink.getBuyType(), StrategyOrderLinkBuyTypeConstant.ADD_AMOUNT)) {
            Instant expectServiceExpireTime;
            if (ObjectUtil.equal(strategyOrderLink.getBuyType(), StrategyOrderLinkBuyTypeConstant.NEW_BUY)) {
                expectServiceExpireTime = DateUtils.getDayOfEnd(Instant.now()).plus(skuStrategy.getPeriod(), ChronoUnit.DAYS);
            } else if (ObjectUtil.equal(strategyOrderLink.getBuyType(), StrategyOrderLinkBuyTypeConstant.ADD_PERIOD)) {
                expectServiceExpireTime = (strategyOrderLink.getCurrentServiceExpireTime().isAfter(Instant.now()) ?
                        strategyOrderLink.getCurrentServiceExpireTime() : DateUtils.getDayOfEnd(Instant.now())).plus(skuStrategy.getPeriod(), ChronoUnit.DAYS);
            } else {
                expectServiceExpireTime = strategyOrderLink.getCurrentServiceExpireTime().plus(skuStrategy.getPeriod(), ChronoUnit.DAYS);
            }
            resp.setExpectServiceExpireTime(expectServiceExpireTime);
        }
        return resp;
    }
}

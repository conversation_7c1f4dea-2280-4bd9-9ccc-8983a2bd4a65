package cn.shrise.radium.adminapi.service.wx;

import cn.shrise.radium.adminapi.util.ClickKeyUtils;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.req.oa.GenerateWxCustomMsgReq;
import cn.shrise.radium.wxservice.req.oa.WxOaMenuReq;
import cn.shrise.radium.wxservice.resp.oa.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

@Service
@RequiredArgsConstructor
public class WxOaService {

    private final WxClient wxClient;

    public BaseResult<WxReplyResp> getWxReplyList(Integer accountType) {
        return wxClient.getWxReplyList(accountType);
    }

    public BaseResult<Void> deleteReplyMsg(Long id) {
        return wxClient.deleteReplyMsg(id);
    }

    public BaseResult<String> generateClickKey() {
        String clickKey = ClickKeyUtils.generateClickKey();
        return BaseResult.success(clickKey);
    }

    public BaseResult<Void> addReplyMsg(GenerateWxCustomMsgReq req) {
        return wxClient.addReplyMsg(req);
    }

    public BaseResult<Void> updateReplyMsg(GenerateWxCustomMsgReq req) {
        return wxClient.updateReplyMsg(req);
    }

    public BaseResult<WxCustomMsgResp> getWxOaMenuContent(Integer accountType, String clickKey) {
        return wxClient.getWxOaMenuContent(accountType, clickKey);
    }

    public BaseResult<String> updateWxOaMenu(WxOaMenuReq req) {
        return wxClient.updateWxOaMenu(req);
    }

    public BaseResult<WxOaMenuResp> getWxOaMenu(Integer accountType) {
        return wxClient.getWxOaMenu(accountType);
    }

    public PageResult<List<WxOaGroupMessageResp>> getWxOaGroupMessageRespList(List<Integer> accountTypes, Integer status, LocalDateTime preSendStartTime, LocalDateTime preSendEndTime, Integer type, Integer current, Integer size) {
        return wxClient.getWxOaGroupMessageRespList(accountTypes, status, preSendStartTime, preSendEndTime, type, current, size);
    }

    public BaseResult<WxCustomMsgDetailResp> getWxOaGroupMessageDetail(Long id) {
        return wxClient.getWxOaGroupMessageDetail(id);
    }
}

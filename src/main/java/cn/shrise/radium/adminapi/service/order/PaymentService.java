package cn.shrise.radium.adminapi.service.order;

import cn.shrise.radium.adminapi.resp.order.MerchantModifyRecordResp;
import cn.shrise.radium.adminapi.resp.order.PayCompanyModifyRecordResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsMerchantInfoModifyRecord;
import cn.shrise.radium.orderservice.entity.RsPayCompanyModifyRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentService {

    private final OrderClient orderClient;
    private final UserClient userClient;

    public BaseResult<List<PayCompanyModifyRecordResp>> getPaymentCompanyOperateRecord(Long id) {
        List<RsPayCompanyModifyRecord> modifyRecords = orderClient.getPaymentCompanyOperateRecord(id).orElseThrow();
        List<Integer> idList = modifyRecords.stream().filter(Objects::nonNull)
                .map(RsPayCompanyModifyRecord::getOperatorId)
                .collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(idList)).orElse(Collections.emptyMap());
        List<PayCompanyModifyRecordResp> list = modifyRecords.stream().filter(Objects::nonNull).map(o -> {
            PayCompanyModifyRecordResp resp = new PayCompanyModifyRecordResp();
            BeanUtils.copyProperties(o, resp);
            UcUsers users = usersMap.get(o.getOperatorId());
            if (users != null) {
                resp.setOperatorName(users.getRealName());
                resp.setAvatarUrl(users.getAvatarUrl());
            }
            return resp;
        }).collect(Collectors.toList());
        return BaseResult.success(list);
    }

    public BaseResult<List<MerchantModifyRecordResp>> getMerchantOperateRecord(@RequestParam @ApiParam("商户id") Long id) {
        List<RsMerchantInfoModifyRecord> modifyRecords = orderClient.getMerchantOperateRecord(id).orElseThrow();
        List<Integer> idList = modifyRecords.stream().filter(Objects::nonNull)
                .map(RsMerchantInfoModifyRecord::getOperatorId)
                .collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(idList)).orElse(Collections.emptyMap());
        List<MerchantModifyRecordResp> list = modifyRecords.stream().filter(Objects::nonNull).map(o -> {
            MerchantModifyRecordResp resp = new MerchantModifyRecordResp();
            BeanUtils.copyProperties(o, resp);
            UcUsers ucUsers = usersMap.get(o.getOperatorId());
            if (ucUsers != null) {
                resp.setOperatorName(ucUsers.getRealName());
                resp.setAvatarUrl(ucUsers.getAvatarUrl());
            }
            return resp;
        }).collect(Collectors.toList());
        return BaseResult.success(list);
    }
}

package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.adminapi.resp.order.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.resp.GiftCouponStatistics;
import cn.shrise.radium.orderservice.resp.gift.GiftCouponInfoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class GiftService {

    private final OrderClient orderClient;

    private final CommonProperties commonProperties;

    private final UserClient userClient;


    public PageResult<List<GiftGoodResp>> getGiftGoodList(Integer companyType, Integer current, Integer size) {
        PageResult<List<RsGiftGood>> result = orderClient.getGiftGoodList(companyType, current, size);
        if (result.isPresent()) {
            List<GiftGoodResp> goodRespList = new ArrayList<>();
            List<RsGiftGood> goodList = result.getData();
            Set<Long> goodIdSet = goodList.stream().map(RsGiftGood::getId).collect(Collectors.toSet());
            BaseResult<List<GiftCouponStatistics>> statisticsResult = orderClient.getGiftCouponStatistics(BatchReq.create(goodIdSet));
            Map<Long, Long> statisticsMap = new HashMap<>(size);
            if (statisticsResult.isPresent()) {
                statisticsMap = statisticsResult.getData().stream().collect(Collectors.toMap(GiftCouponStatistics::getId, GiftCouponStatistics::getCount));
            }
            for (RsGiftGood good : goodList) {
                GiftGoodResp resp = new GiftGoodResp();
                BeanUtils.copyProperties(good, resp);
                resp.setCodeCount(statisticsMap.getOrDefault(good.getId(), 0L));
                String shortUrl = ObjectUtil.isNotEmpty(good.getWxUrlNumber()) ? StrUtil.format("{}/n/{}", commonProperties.getShortUrl(), good.getWxUrlNumber()) : null;
                resp.setShortUrl(shortUrl);
                goodRespList.add(resp);
            }
            return PageResult.success(goodRespList, result.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<GiftCouponManageResp>> getGiftCouponList(Long goodId, String searchContent, LocalDate startTime,
                                                                    LocalDate endTime, Integer userId, Integer current, Integer size) {
        if (ObjectUtil.isNotEmpty(searchContent) && searchContent.trim().length() <= 10) {
            int searchUserId = DesensitizeUtil.maskToId(searchContent);
            if (ObjectUtil.isNotEmpty(searchUserId)) {
                searchContent = String.valueOf(searchUserId);
            } else {
                return PageResult.empty();
            }
        }
        PageResult<List<RsGiftCouponRender>> result = orderClient.getGiftCouponRenderList(goodId, searchContent, startTime, endTime, userId, current, size);
        if (result.isPresent()) {
            List<GiftCouponManageResp> manageRespList = new ArrayList<>();
            List<RsGiftCouponRender> renderList = result.getData();
            Set<Integer> couponIdSet = renderList.stream().map(RsGiftCouponRender::getOperatorId).collect(Collectors.toSet());
            BaseResult<Map<Integer, UcUsers>> userMapResult = userClient.batchGetUserMap(BatchReq.create(couponIdSet));
            Map<Integer, UcUsers> userMap = new HashMap<>(size);
            if (userMapResult.isPresent()) {
                userMap = userMapResult.getData();
            }
            List<Integer> orderIdList = renderList.stream().map(RsGiftCouponRender::getOrderId).collect(Collectors.toList());
            BaseResult<List<RsCourseOrder>> orderResult = orderClient.batchGetOrderListById(orderIdList);
            Map<Integer, RsCourseOrder> orderMap = new HashMap<>();
            if (orderResult.isPresent()) {
                List<RsCourseOrder> orderList = orderResult.getData();
                orderMap = orderList.stream()
                        .collect(Collectors.toMap(RsCourseOrder::getId, Function.identity()));
            }
            Set<Long> idSet = renderList.stream().map(RsGiftCouponRender::getId).collect(Collectors.toSet());
            BaseResult<Map<Long, List<GiftCouponInfoResp>>> couponMapResult = orderClient.batchGetCouponMap(BatchReq.create(idSet));
            Map<Long, List<GiftCouponInfoResp>> couponMap = new HashMap<>();
            if (couponMapResult.isPresent()) {
                couponMap = couponMapResult.getData();
            }

            Set<Long> goodIdSet = renderList.stream().map(RsGiftCouponRender::getGoodId).collect(Collectors.toSet());
            BaseResult<List<RsGiftGood>> giftGoodList = orderClient.getGiftGoodList(goodIdSet);
            HashMap<Long, String> goodIdNameMap = new HashMap<>();
            if (giftGoodList.isPresent()) {
                goodIdNameMap = giftGoodList.getData().stream()
                        .collect(Collectors.toMap(RsGiftGood::getId, RsGiftGood::getName, (k1, k2) -> k1, HashMap::new));
            }

            for (RsGiftCouponRender render : renderList) {
                GiftCouponManageResp manageResp = new GiftCouponManageResp();
                BeanUtils.copyProperties(render, manageResp);
                manageResp.setUserId(render.getBelongId());
                manageResp.setUserCode(DesensitizeUtil.idToMask(render.getBelongId()));
                manageResp.setGoodName(goodIdNameMap.get(render.getGoodId()));
                if (userMap.containsKey(render.getOperatorId())) {
                    manageResp.setOperatorName(userMap.get(render.getOperatorId()).getRealName());
                }
                if (orderMap.containsKey(render.getOrderId())) {
                    manageResp.setOrderNumber(orderMap.get(render.getOrderId()).getOrderNumber());
                }
                if (couponMap.containsKey(render.getId())) {
                    List<GiftCouponResp> couponRespList = new ArrayList<>();
                    List<GiftCouponInfoResp> rsGiftCoupons = couponMap.get(render.getId());
                    for (GiftCouponInfoResp rsGiftCoupon : rsGiftCoupons) {
                        GiftCouponResp giftCouponResp = new GiftCouponResp();
                        BeanUtils.copyProperties(rsGiftCoupon, giftCouponResp);
                        couponRespList.add(giftCouponResp);
                    }
                    manageResp.setGiftCouponRespList(couponRespList);
                }
                manageRespList.add(manageResp);
            }
            return PageResult.success(manageRespList, result.getPagination());
        }
        return PageResult.empty();
    }

    public GiftCouponUsageDetailResp getGiftCouponUsageDetail(String code) {
        BaseResult<RsGiftCouponUsageDetail> result = orderClient.getGiftCouponUsageDetail(code);
        GiftCouponUsageDetailResp resp = new GiftCouponUsageDetailResp();
        if (result.isPresent()) {
            RsGiftCouponUsageDetail usageDetail = result.getData();
            BeanUtils.copyProperties(usageDetail, resp);
        }
        return resp;
    }

    public GiftCouponUsageDetailResp getGiftCouponUsageDetailById(Long pkId) {
        BaseResult<RsGiftCouponUsageDetail> result = orderClient.getGiftCouponUsageDetailById(pkId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        return BeanUtil.copyProperties(result.getData(), GiftCouponUsageDetailResp.class);
    }

    public List<CouponOperateRecordResp> getCouponOperateRecord(Long couponId) {
        BaseResult<List<RsCouponOperateRecord>> result = orderClient.getCouponOperateRecord(couponId);
        if (!result.isPresent()) {
            return Collections.emptyList();
        }
        List<RsCouponOperateRecord> records = result.getData();
        Set<Integer> userIds = records.stream().map(RsCouponOperateRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIds)).getData();
        return records.stream().map(e -> {
            CouponOperateRecordResp couponRecordResp = new CouponOperateRecordResp();
            BeanUtil.copyProperties(e, couponRecordResp);
            if (usersMap.containsKey(e.getOperatorId())) {
                couponRecordResp.setOperatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName());
            }
            return couponRecordResp;
        }).collect(Collectors.toList());
    }
}

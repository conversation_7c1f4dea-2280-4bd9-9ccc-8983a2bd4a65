package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.req.AdminCreateTeamShortVideoReq;
import cn.shrise.radium.adminapi.req.UpdateTeamShortVideoCommentAuditStatusReq;
import cn.shrise.radium.adminapi.resp.AdminCreateTeamShortVideoResp;
import cn.shrise.radium.adminapi.resp.content.*;
import cn.shrise.radium.adminapi.resp.vod.VodUploadVideoResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.constant.ArticleStatusEnum;
import cn.shrise.radium.contentservice.entity.*;
import cn.shrise.radium.contentservice.req.CreateTeamShortVideoReq;
import cn.shrise.radium.contentservice.req.UpdateTeamShortVideoCommentReq;
import cn.shrise.radium.contentservice.resp.CreateTeamShortVideoResp;
import cn.shrise.radium.contentservice.resp.ModerationLabelItem;
import cn.shrise.radium.contentservice.resp.SsShortVideoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcContentTeam;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.util.DateUtils.localDateTimeToInstant;
import static cn.shrise.radium.contentservice.constant.ArticleStatusEnum.AS_Released;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShortVideoService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PageResult<List<ShortVideoResp>> getShortVideoList(Integer analystId, Integer current, Integer size) {
        PageResult<List<SsShortVideoResp>> shortVideoList = contentClient.getShortVideoList(analystId, null, null, current, size);
        if (shortVideoList.isFail()) {
            log.info("拉取短视频列表失败！");
            throw new BusinessException(shortVideoList);
        }
        List<SsShortVideoResp> shortVideoListData = shortVideoList.getData();
        List<Long> videoIds = shortVideoListData.stream().map(SsShortVideoResp::getId).collect(Collectors.toList());
        BaseResult<List<SsShortVideoTag>> tagResult = contentClient.getTagByShortVideoIds(videoIds);
        if (tagResult.isFail()) {
            log.info("拉取标签信息失败！");
            throw new BusinessException(tagResult);
        }
        List<SsShortVideoTag> tagList = tagResult.getData() == null ? Collections.emptyList() : tagResult.getData();
        Map<Long, SsShortVideoTag> videoTagMap = tagList.stream().collect(Collectors.toMap(SsShortVideoTag::getVideoId, Function.identity()));
        List<Integer> analystIds = shortVideoListData.stream().map(SsShortVideoResp::getAnalystId).collect(Collectors.toList());
        List<SsAnalystInfo> analystList = contentClient.getAnalystInfoList(BatchReq.of(analystIds)).orElse(Collections.emptyList());
        Map<Integer, SsAnalystInfo> analystMap = analystList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, Function.identity()));
        List<ShortVideoResp> shortVideoResps = new ArrayList<>();
        shortVideoListData.forEach(e -> {
            ShortVideoResp shortVideoResp = new ShortVideoResp();
            BeanUtil.copyProperties(e, shortVideoResp);
            if (analystMap.containsKey(e.getAnalystId())) {
                shortVideoResp.setAnalyst(analystMap.get(e.getAnalystId()).getName());
            }
            if (ObjectUtil.isNotEmpty(videoTagMap) && videoTagMap.containsKey(e.getId())) {
                String tagsStr = videoTagMap.get(e.getId()).getTags();
                String[] tags = JSON.parseObject(tagsStr, String[].class);
                shortVideoResp.setTags(tags);
                shortVideoResp.setTagStatus(videoTagMap.get(e.getId()).getStatus());
                shortVideoResp.setTagMsg(videoTagMap.get(e.getId()).getMessage());
            }
            shortVideoResps.add(shortVideoResp);
        });
        return PageResult.success(shortVideoResps, shortVideoList.getPagination());
    }

    public PageResult<List<ShortVideoCommentResp>> getCommentList(Long videoId, Boolean isChose, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {

        PageResult<List<SsShortVideoComment>> pageResult = contentClient.getShortVideoCommentList(videoId, null, isChose, startTime, endTime, current, size);
        if (pageResult.isFail()) {
            log.info("获取短视频评论列表失败！");
            throw new BusinessException(pageResult);
        }

        final List<SsShortVideoComment> commentList = pageResult.getData() == null ? Collections.emptyList() : pageResult.getData();
        final List<Integer> customerId = commentList.stream().map(e -> e.getCustomerId()).collect(Collectors.toList());

        BaseResult<Map<Integer, UcUsers>> customerResult = userClient.batchGetUserMap(new BatchReq<>(customerId));
        if (customerResult.isFail()) {
            log.info("获取用户信息失败！");
            throw new BusinessException(customerResult);
        }
        Map<Integer, UcUsers> customerMap = customerResult.getData();

        List<ShortVideoCommentResp> resps = commentList.stream().map(e -> {
            return ShortVideoCommentResp.builder()
                    .id(e.getId())
                    .videoId(e.getVideoId())
                    .customerId(e.getCustomerId())
                    .customerNickName(customerMap.get(e.getCustomerId()).getNickName())
                    .commentContent(e.getCommentContent())
                    .commentTime(e.getCommentTime())
                    .isChose(e.getIsChose())
                    .build();
        }).collect(Collectors.toList());

        return PageResult.success(resps, pageResult.getPagination());
    }

    public BaseResult<VodUploadVideoResp> createUploadVideo(Integer companyType, Integer analystId, String title, String fileTitle, String fileName, Long cateId, String templateGroupId, String description, String coverURL) {
        BaseResult<CreateUploadVideoResponse> result = contentClient.createVideo(companyType, analystId, title, fileTitle, fileName, cateId, templateGroupId, description, coverURL);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        CreateUploadVideoResponse video = result.getData();
        VodUploadVideoResp record = VodUploadVideoResp.builder()
                .videoId(video.getVideoId())
                .uploadAddress(video.getUploadAddress())
                .uploadAuth(video.getUploadAuth())
                .build();
        return BaseResult.success(record);
    }

    public BaseResult<String> updateVideo(Long id, String title, String coverUrl) {
        return contentClient.updateVideo(id, title, coverUrl);
    }

    public BaseResult<ShortVideoTagsResp> reacquireTags(Integer companyType, String mediaId, Long videoId) {
        BaseResult<String> result = contentClient.reacquireShortVideoTag(companyType, mediaId, videoId);
        if (result.isFail()) {
            log.info("重新获取标签失败！");
            throw new BusinessException(result);
        }
        List<Long> videoIds = new ArrayList<>();
        videoIds.add(videoId);
        BaseResult<List<SsShortVideoTag>> tagResult = contentClient.getTagByShortVideoIds(videoIds);
        if (!tagResult.isPresent()) {
            throw new BusinessException(tagResult);
        }
        SsShortVideoTag videoTag = tagResult.getData().get(0);
        String[] tags = JSON.parseObject(videoTag.getTags(), String[].class);
        ShortVideoTagsResp resp = ShortVideoTagsResp.builder()
                .videoId(videoId)
                .tagStatus(videoTag.getStatus())
                .tagMsg(videoTag.getMessage())
                .tags(tags)
                .build();
        return BaseResult.success(resp);
    }

    public PageResult<List<TeamVideoResp>> getTeamVideoPage(Long teamId, Integer companyType, LocalDate startTime, LocalDate endTime, String field, Boolean isAsc, Integer current, Integer size) {
        List<Long> teamIds = new ArrayList<>();
        if (ObjectUtil.isEmpty(teamId)) {
            teamIds = userClient.getVisibleByUser(AuthContextHolder.getUserId()).orElse(null);
            if (ObjectUtil.isEmpty(teamIds)) {
                return PageResult.empty();
            }
        }
        teamIds.add(teamId);
        PageResult<List<SsShortVideo>> pageResult = contentClient.getTeamVideoList(teamIds, companyType, AS_Released.getValue(), startTime, endTime, field, isAsc, current, size);
        if (pageResult.isPresent()) {
            List<SsShortVideo> videoList = pageResult.getData();
            List<TeamVideoResp> respList = new ArrayList<>();
            Set<Long> teamSet = new HashSet<>();
            videoList.forEach(e -> {
                teamSet.add(e.getTeamId());
            });
            Map<Long, UcContentTeam> teamMap = userClient.batchContentTeamMap(BatchReq.create(teamSet)).getData();
            for (SsShortVideo video : videoList) {
                TeamVideoResp resp = new TeamVideoResp();
                BeanUtil.copyProperties(video, resp);
                if (teamMap.containsKey(video.getTeamId())) {
                    UcContentTeam team = teamMap.get(video.getTeamId());
                    resp.setAvatarUrl(team.getAvatarUrl());
                    resp.setTeamName(team.getName());
                }
                respList.add(resp);
            }
            return PageResult.success(respList, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<TeamShortVideoItem>> getTeamShortVideoList(Integer companyType, Long teamId, Integer current, Integer size) {
        PageResult<List<SsShortVideo>> pageResult = contentClient.getTeamVideoList(Collections.singletonList(teamId), companyType, null, null, null, null, null, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }

        List<SsShortVideo> data = pageResult.getData();
        Set<Integer> userIdSet = data.stream().map(SsShortVideo::getCreatorId).filter(Objects::nonNull).collect(Collectors.toSet());
        userIdSet.addAll(data.stream().map(SsShortVideo::getAuditId).filter(Objects::nonNull).collect(Collectors.toSet()));
        List<Long> videoIdList = data.stream().map(SsShortVideo::getId).filter(Objects::nonNull).collect(Collectors.toList());

        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).orElse(Collections.emptyMap());
        List<SsShortVideoTag> videoTags = contentClient.getTagByShortVideoIds(videoIdList).orElse(Collections.emptyList());
        Map<Long, SsShortVideoTag> tagMap = videoTags.stream().collect(Collectors.toMap(SsShortVideoTag::getVideoId, Function.identity()));

        List<TeamShortVideoItem> items = data.stream().map(e -> {
            TeamShortVideoItem item = TeamShortVideoItem.of(e);
            Integer creatorId = e.getCreatorId();
            UcUsers creatorUser = creatorId != null ? userMap.get(creatorId) : null;
            UserInfo creatorInfo = UserInfo.of(creatorUser);
            item.setCreatorInfo(creatorInfo);

            Integer auditId = e.getAuditId();
            UcUsers auditUser = auditId != null ? userMap.get(auditId) : null;
            UserInfo auditInfo = UserInfo.of(auditUser);
            item.setAuditInfo(auditInfo);
            item.setAuditTime(e.getAuditTime() != null ? e.getAuditTime() : null);
            item.setStatus(e.getStatus() != null ? e.getStatus() : null);
            item.setAuditRemark(e.getAuditRemark() != null ? e.getAuditRemark() : null);

            SsShortVideoTag videoTag = tagMap.get(e.getId());
            String tagString = videoTag != null ? videoTag.getTags() : null;
            List<String> tags = ObjectUtils.isNotEmpty(tagString) ?
                    JSON.parseArray(videoTag.getTags(), String.class) : Collections.emptyList();
            item.setTags(tags);
            return item;
        }).collect(Collectors.toList());

        return PageResult.success(items, pageResult.getPagination());
    }

    public PageResult<List<TeamShortVideoCommentItem>> getTeamShortVideoCommentList(Long videoId, Boolean isChose, Boolean audit, Integer auditStatus,
                                                                                    LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        Instant startTimeInstant = startTime != null ? DateUtils.getDayOfStart(startTime) : null;
        Instant endTimeInstant = endTime != null ? DateUtils.getDayOfEnd(endTime) : null;

        PageResult<List<SsShortVideoComment>> pageResult = contentClient.getTeamVideoCommentList(videoId, isChose, audit, auditStatus, startTimeInstant, endTimeInstant, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }

        List<SsShortVideoComment> data = pageResult.getData();

        Set<Integer> userIdSet = new HashSet<>();
        data.stream().map(SsShortVideoComment::getCustomerId).filter(Objects::nonNull).forEach(userIdSet::add);
        data.stream().map(SsShortVideoComment::getAuditorId).filter(Objects::nonNull).forEach(userIdSet::add);
        List<Long> resultIdSet = data.stream().map(SsShortVideoComment::getResultId).filter(Objects::nonNull).collect(Collectors.toList());

        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).orElse(Collections.emptyMap());
        Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultIdSet)).orElse(Collections.emptyMap());
        List<ModerationLabelItem> moderationLabelItems = contentClient.getModerationLabels().orElse(Collections.emptyList());
        Map<String, String> labelMap = moderationLabelItems.stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, ModerationLabelItem::getName));

        List<TeamShortVideoCommentItem> items = data.stream().map(e -> {
            Integer customerId = e.getCustomerId();
            Integer auditorId = e.getAuditorId();
            Long resultId = e.getResultId();

            UcUsers customerUser = customerId != null ? userMap.get(customerId) : null;
            UcUsers auditorUser = auditorId != null ? userMap.get(auditorId) : null;
            SsTextScanResult result = resultId != null ? resultMap.get(resultId) : null;
            String labelString = result != null ? result.getLabels() : null;
            List<String> labels = ObjectUtils.isNotEmpty(labelString) ?
                    JSON.parseArray(labelString, String.class) : Collections.emptyList();
            List<String> labelNameList = labels.stream().map(labelMap::get).collect(Collectors.toList());

            boolean passed = result != null ? result.getPassed() : false;

            UserInfo customerInfo = UserInfo.of(customerUser);
            UserInfo auditorInfo = UserInfo.of(auditorUser);
            return TeamShortVideoCommentItem.builder().id(e.getId())
                    .videoId(e.getVideoId())
                    .customerId(e.getCustomerId())
                    .customerInfo(customerInfo)
                    .auditorId(e.getAuditorId())
                    .auditorInfo(auditorInfo)
                    .commentContent(e.getCommentContent())
                    .commentTime(e.getCommentTime())
                    .isChose(e.getIsChose())
                    .auditStatus(e.getAuditStatus())
                    .resultId(e.getResultId())
                    .labels(labelNameList)
                    .passed(passed)
                    .build();
        }).collect(Collectors.toList());

        return PageResult.success(items, pageResult.getPagination());
    }

    public void updateTeamShortVideoCommentStatus(Integer auditorId, UpdateTeamShortVideoCommentAuditStatusReq updateTeamShortVideoCommentAuditStatusReq) {
        UpdateTeamShortVideoCommentReq updateTeamShortVideoCommentReq = UpdateTeamShortVideoCommentReq.builder()
                .id(updateTeamShortVideoCommentAuditStatusReq.getId())
                .auditorId(auditorId)
                .auditStatus(updateTeamShortVideoCommentAuditStatusReq.getAuditStatus())
                .build();
        BaseResult<Void> voidBaseResult = contentClient.updateTeamVideoComment(updateTeamShortVideoCommentReq);
        if (voidBaseResult.isFail()) {
            throw new BusinessException(voidBaseResult);
        }
    }

    public AdminCreateTeamShortVideoResp createTeamShortVideo(Integer companyType, Integer creatorId, AdminCreateTeamShortVideoReq req) {
        CreateTeamShortVideoReq createTeamShortVideoReq = CreateTeamShortVideoReq.builder()
                .companyType(companyType)
                .creatorId(creatorId)
                .teamId(req.getTeamId())
                .title(req.getTitle())
                .bannerUrl(req.getBannerUrl())
                .videoId(req.getVideoId())
                .duration(req.getDuration())
                .preReleaseTime(localDateTimeToInstant(req.getReleaseTime()))
                .build();
        BaseResult<CreateTeamShortVideoResp> result = contentClient.createTeamShortVideo(createTeamShortVideoReq);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        CreateTeamShortVideoResp data = result.getData();
        AdminCreateTeamShortVideoResp resp = new AdminCreateTeamShortVideoResp();
        BeanUtils.copyProperties(data, resp);
        return resp;
    }

    public BaseResult<List<SsVideoOperateRecordResp>> getVideoOperateRecord(Long videoId) {
        List<SsVideoOperateRecordResp> resp = new ArrayList<>();
        BaseResult<List<SsVideoOperateRecord>> operateRecord = contentClient.getVideoOperateRecord(videoId);
        if (!operateRecord.isPresent()) {
            return BaseResult.success(resp);
        }
        List<SsVideoOperateRecord> record = operateRecord.getData();
        List<Integer> operatorId = record.stream().map(SsVideoOperateRecord::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(operatorId)) {
            usersMap = userClient.batchGetUserMap(BatchReq.of(operatorId)).getData();
        }
        for (SsVideoOperateRecord ssVideoOperateRecord : record) {
            SsVideoOperateRecordResp ssVideoOperateRecordResp = new SsVideoOperateRecordResp();
            BeanUtils.copyProperties(ssVideoOperateRecord, ssVideoOperateRecordResp);
            if (ObjectUtil.isNotEmpty(ssVideoOperateRecord.getOperatorId())) {
                ssVideoOperateRecordResp.setAvatarUrl(usersMap.get(ssVideoOperateRecord.getOperatorId()).getAvatarUrl());
            }
            resp.add(ssVideoOperateRecordResp);
        }
        return BaseResult.success(resp);
    }
}

package cn.shrise.radium.adminapi.service.cdadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.cdadviserservice.req.CdAdviserStrategyReq;
import cn.shrise.radium.cdadviserservice.req.OpenCdAdviserSubscriptionReq;
import cn.shrise.radium.cdadviserservice.resp.*;
import cn.shrise.radium.cdadviserservice.CdAdviserServiceClient;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.shrise.radium.cdadviserservice.constant.ErrorConstant.CUSTOMER_NOT_EXISTED;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Service
@RequiredArgsConstructor
public class CdAdviserService {

    private final CdAdviserServiceClient cdAdviserServiceClient;
    private final UserClient userClient;

    public PageResult<List<CdAdviserStrategyResp>> getCdAdviserStrategyList(Integer current, Integer size) {
        return cdAdviserServiceClient.getCdAdviserStrategyList(current, size);
    }

    public BaseResult<Void> createCdAdviserStrategy(CdAdviserStrategyReq req) {
        return cdAdviserServiceClient.createCdAdviserStrategy(req);
    }

    public BaseResult<Void> updateCdAdviserStrategy(CdAdviserStrategyReq req) {
        return cdAdviserServiceClient.updateCdAdviserStrategy(req);
    }

    public PageResult<List<CdAdviserStrategyRecordResp>> getCdAdviserStrategyOperateRecordList(String code, Integer current, Integer size) {
        PageResult<List<CdAdviserStrategyRecordResp>> pageResult = cdAdviserServiceClient.getCdAdviserStrategyOperateRecordList(code, current, size);
        Set<Integer> userIds = pageResult.getData().stream().map(CdAdviserStrategyRecordResp::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        List<CdAdviserStrategyRecordResp> result = pageResult.getData().stream().peek(r -> r.setOperatorName(userMap.getOrDefault(r.getOperatorId(), new UcUsers()).getRealName())).collect(Collectors.toList());
        return PageResult.success(result, pageResult.getPagination());
    }

    public PageResult<List<CdAdviserStrategySelectionStockResp>> getCdAdviserStrategySelectionStockList(LocalDate date, String code, Integer current, Integer size) {
        return cdAdviserServiceClient.getCdAdviserStrategySelectionStockList(date, code, current, size);
    }

    public BaseResult<CdAdviserRequestDetailResp> getCdAdviserRequestDetail(String requestId) {
        return cdAdviserServiceClient.getCdAdviserRequestDetail(requestId);
    }

    public PageResult<List<CdAdviserStrategyTimePositionRecordResp>> getCdAdviserStrategyTimePositionRecordList(String code, Integer current, Integer size) {
        PageResult<List<CdAdviserStrategyTimePositionRecordResp>> pageResult = cdAdviserServiceClient.getCdAdviserStrategyTimePositionRecordList(code, current, size);
        Set<Integer> userIds = pageResult.getData().stream().map(CdAdviserStrategyTimePositionRecordResp::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();
        List<CdAdviserStrategyTimePositionRecordResp> result = pageResult.getData().stream().peek(r -> r.setOperatorName(userMap.getOrDefault(r.getOperatorId(), new UcUsers()).getRealName())).collect(Collectors.toList());
        return PageResult.success(result, pageResult.getPagination());
    }

    public BaseResult<Void> updateCdAdviserStrategyTimePosition(String code, Double position, Integer operatorId) {
        return cdAdviserServiceClient.updateCdAdviserStrategyTimePosition(code, position, operatorId);
    }

}

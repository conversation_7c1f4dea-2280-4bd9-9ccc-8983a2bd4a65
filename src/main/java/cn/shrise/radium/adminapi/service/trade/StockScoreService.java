package cn.shrise.radium.adminapi.service.trade;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.StockUtils;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdStockFactorRelation;
import cn.shrise.radium.tradeservice.entity.TdStockScoreInfo;
import cn.shrise.radium.tradeservice.req.StockFactorMapReq;
import cn.shrise.radium.tradeservice.req.StockScoreReq;
import cn.shrise.radium.tradeservice.resp.StockFactorInfoResp;
import cn.shrise.radium.tradeservice.resp.StockFactorResp;
import cn.shrise.radium.tradeservice.resp.StockScoreResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: tangjiajun
 * @Date: 2025/4/10 17:10
 * @Desc:
 **/
@Service
@RequiredArgsConstructor
public class StockScoreService {

    private final TradeClient tradeClient;

    public PageResult<List<StockScoreResp>> stockScoreList(StockScoreReq stockScoreReq) {
        PageResult<List<TdStockScoreInfo>> result = tradeClient.stockScoreList(stockScoreReq);
        if (ObjectUtil.isEmpty(result)) {
            return PageResult.empty();
        }
        List<TdStockScoreInfo> scoreInfos = result.getData();
        List<String> stockCodes = scoreInfos.stream().map(TdStockScoreInfo::getStockCode).collect(Collectors.toList());
        StockFactorMapReq req = StockFactorMapReq.builder().stockCodes(stockCodes).date(stockScoreReq.getDate()).build();
        Map<String, List<TdStockFactorRelation>> stockFactorMap = tradeClient.getStockFactorMapByFactorIds(req).getData();
        Map<Long, StockFactorResp> factorMap = tradeClient.factorList(true, 1, 500).getData().stream().collect(Collectors.toMap(StockFactorResp::getId, Function.identity()));
        List<StockScoreResp> respList = scoreInfos.stream().map(stock -> {
            StockScoreResp resp = new StockScoreResp();
            BeanUtils.copyProperties(stock, resp);
            if (ObjectUtil.isNotEmpty(resp.getAddDate())) {
                resp.setDate(DateUtils.instantToLocalDate(resp.getAddDate()));
            }
            resp.setJjScore(resp.getJjScore());
            resp.setDxScore(resp.getDxScore());
            resp.setStockCode(StockUtils.stockLabelFormat(resp.getStockCode()));
            resp.setIsLimitUp(resp.getLimitStatus() != 0);
            List<Long> factorIds = stockFactorMap.getOrDefault(stock.getStockCode(), Collections.emptyList()).stream().map(TdStockFactorRelation::getFactorId).collect(Collectors.toList());
            List<StockFactorInfoResp> factorResps = factorIds.stream().map(factorId -> {
                StockFactorResp factorResp = factorMap.getOrDefault(factorId, null);
                if (ObjectUtil.isNotEmpty(factorResp)) {
                    return StockFactorInfoResp.builder()
                            .id(factorResp.getId())
                            .number(factorResp.getNumber())
                            .name(factorResp.getName())
                            .type(factorResp.getType())
                            .build();
                }
                return null;
            }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            resp.setFactors(factorResps);
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, result.getPagination());
    }
}

package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.adminapi.req.workwx.AdminCreateCustomerAcquisitionChannelReq;
import cn.shrise.radium.adminapi.resp.workWx.CustomerAcquisitionChannelItem;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.NpDzAdChannel;
import cn.shrise.radium.workwxservice.entity.NpWwxCustomerAcquisition;
import cn.shrise.radium.workwxservice.entity.NpWwxCustomerAcquisitionChannel;
import cn.shrise.radium.workwxservice.req.CreateCustomerAcquisitionChannelReq;
import cn.shrise.radium.workwxservice.req.UpdateCustomerAcquisitionChannelReq;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;

@Service
@RequiredArgsConstructor
public class NpWwxCustomerAcquisitionChannelService {

    private final WorkwxClient workwxClient;


    public PageResult<List<CustomerAcquisitionChannelItem>> getCustomerAcquisitionChannelList(Long customerAcquisitionId, Boolean enabled, String searchContent, Integer current, Integer size) {
        PageResult<List<NpWwxCustomerAcquisitionChannel>> pageResult = workwxClient.getCustomerAcquisitionChannelList(customerAcquisitionId, enabled, searchContent, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }

        NpWwxCustomerAcquisition npWwxCustomerAcquisition = workwxClient.getCustomerAcquisition(customerAcquisitionId).orElse(null);
        String url = npWwxCustomerAcquisition != null? npWwxCustomerAcquisition.getUrl() : null;

        List<NpWwxCustomerAcquisitionChannel> pageList = pageResult.getData();
        Set<Long> linkIdSet = pageList.stream().map(NpWwxCustomerAcquisitionChannel::getDzId).collect(Collectors.toSet());
        Map<Long, NpDzAdChannel> channelMap = workwxClient.getNpDzAdChannelByIdSet(linkIdSet).orElse(Collections.emptyMap());

        List<CustomerAcquisitionChannelItem> items = pageList.stream()
                .map(e -> {
                    String linkUrl = null;
                    if (ObjectUtils.isNotEmpty(url)) {
                        linkUrl = UriComponentsBuilder.fromUriString(url)
                                .queryParam("customer_channel", e.getState())
                                .build()
                                .toUriString();
                    }
                    NpDzAdChannel npDzAdChannel = channelMap.get(e.getDzId());
                    String linkName = npDzAdChannel != null? npDzAdChannel.getName(): null;
                    Integer channelType = npDzAdChannel != null? npDzAdChannel.getChannelType(): null;
                    return CustomerAcquisitionChannelItem.builder()
                            .id(e.getId())
                            .gmtModified(e.getGmtModified())
                            .gmtCreate(e.getGmtCreate())
                            .enabled(e.getEnabled())
                            .name(e.getName())
                            .remark(e.getRemark())
                            .state(e.getState())
                            .customerAcquisitionId(e.getCustomerAcquisitionId())
                            .dzId(e.getDzId())
                            .linkName(linkName)
                            .linkType(channelType)
                            .url(linkUrl)
                            .build();
                        }
                )
                .collect(Collectors.toList());

        return PageResult.success(items, pageResult.getPagination());
    }

    public NpWwxCustomerAcquisitionChannel createCustomerAcquisitionChannel(AdminCreateCustomerAcquisitionChannelReq req) {
        Long dzId = req.getDzId();
        List<NpDzAdChannel> npDzAdChannels = workwxClient.getNpDzAdChannelByIds(Collections.singleton(dzId)).orElse(emptyList());
        if (ObjectUtils.isEmpty(npDzAdChannels)) {
            throw new BusinessException("广告链路不存在");
        }
        NpDzAdChannel npDzAdChannel = npDzAdChannels.get(0);
        String dzNumber = npDzAdChannel.getDzNumber();
        String state = StrUtil.format("{}-{}", dzNumber, String.format("%x", System.currentTimeMillis() / 1000));

        CreateCustomerAcquisitionChannelReq createCustomerAcquisitionChannelReq = CreateCustomerAcquisitionChannelReq.builder()
                .customerAcquisitionId(req.getCustomerAcquisitionId())
                .name(req.getName())
                .remark(req.getRemark())
                .dzId(req.getDzId())
                .state(state)
                .build();
        return workwxClient.createCustomerAcquisitionChannel(createCustomerAcquisitionChannelReq).orElseThrow();
    }

    public void updateCustomerAcquisitionChannel(UpdateCustomerAcquisitionChannelReq updateBody) {
        workwxClient.updateCustomerAcquisitionChannel(updateBody).orElseThrow();
    }
}

package cn.shrise.radium.adminapi.service.marketing;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.resp.NpDzLinkRuleUploadRecordResp;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.constant.AdChannelTypeEnum;
import cn.shrise.radium.workwxservice.entity.NpDzAdChannel;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactWay;
import cn.shrise.radium.workwxservice.entity.NpWwxCustomerAcquisitionChannel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class NpDzLinkRuleUploadRecordService {

    private final MarketingClient marketingClient;
    private final WorkwxClient workwxClient;

    public PageResult<List<NpDzLinkRuleUploadRecordResp>> getNpDzLinkRuleUploadRecord(Integer companyType, @NonNull Long id, Integer linkType, Integer actionType, LocalDateTime startTime, LocalDateTime endTime, Integer current, Integer size) {
        List<Integer> dzChannelTypeList = Arrays.asList(AdChannelTypeEnum.ACT_Flink.getCode(), AdChannelTypeEnum.ACT_Link.getCode());
        PageResult<List<NpDzLinkRuleUploadRecordResp>> npDzLinkRuleUploadRecord = marketingClient.getNpDzLinkRuleUploadRecord(companyType, id, linkType, actionType, startTime, endTime, current, size);
        List<NpDzLinkRuleUploadRecordResp> npDzLinkRuleUploadRecordData = npDzLinkRuleUploadRecord.getData();
        if (ObjectUtil.isEmpty(npDzLinkRuleUploadRecordData)) {
            return PageResult.empty();
        }
        Set<Long> dzIdSet = npDzLinkRuleUploadRecordData.stream().map(NpDzLinkRuleUploadRecordResp::getDzId).collect(Collectors.toSet());
        Set<Integer> wayIdSet = npDzLinkRuleUploadRecordData.stream().map(NpDzLinkRuleUploadRecordResp::getWayId).collect(Collectors.toSet());
        Map<Long, NpDzAdChannel> longNpDzAdChannelMap = workwxClient.getNpDzAdChannelByIdSet(dzIdSet).getData();
        Map<Integer, NpWorkWxContactWay> npWorkWxContactWayMap = workwxClient.getNpWorkWxContactWayByIdSet(wayIdSet).getData();
        List<Integer> friendIdList = npDzLinkRuleUploadRecordData.stream().filter(e -> dzChannelTypeList.contains(e.getDzChannelType())).map(NpDzLinkRuleUploadRecordResp::getWwxFriendId).collect(Collectors.toList());
        Map<Integer, NpWwxCustomerAcquisitionChannel> channelMap = workwxClient.getCustomerAcquisitionChannelMapByRelationId(friendIdList).getData();

        npDzLinkRuleUploadRecordData.forEach(e -> {
            if (ObjectUtil.isNotNull(npWorkWxContactWayMap) && npWorkWxContactWayMap.containsKey(e.getWayId())) {
                e.setFriendWay(npWorkWxContactWayMap.get(e.getWayId()).getName());
            }
            if (longNpDzAdChannelMap.containsKey(e.getDzId())) {
                e.setName(longNpDzAdChannelMap.get(e.getDzId()).getName());
            }
            if (ObjectUtil.isNotEmpty(channelMap) && channelMap.containsKey(e.getWwxFriendId())) {
                e.setAcquisitionWay(channelMap.get(e.getWwxFriendId()).getName());
            }
        });
        return npDzLinkRuleUploadRecord;
    }
}

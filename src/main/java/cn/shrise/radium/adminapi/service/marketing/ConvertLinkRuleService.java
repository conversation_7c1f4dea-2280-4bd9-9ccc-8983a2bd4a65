package cn.shrise.radium.adminapi.service.marketing;

import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpConvertLinkRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConvertLinkRuleService {
    private final MarketingClient marketingClient;

    /**
     * 新增链路转化规则
     * @param linkId
     * @param actionType
     * @param content
     * @return
     */
    public BaseResult<NpConvertLinkRule> addConvertLinkRule(Long linkId, Integer actionType, String content){
        return marketingClient.addConvertLinkRule(AuthContextHolder.getCompanyType(), AuthContextHolder.getUserId(), linkId, actionType, content);
    };

    /**
     * 修改链路转化规则
     * @param ruleId
     * @param content
     * @return
     */
    public BaseResult<Void> updateConvertLinkRule(Long ruleId, String content){
        return marketingClient.updateConvertLinkRule(AuthContextHolder.getCompanyType(), AuthContextHolder.getUserId(), ruleId, content);
    };

    /**
     * 删除链路转化规则
     * @param ruleId
     * @return
     */
    public BaseResult<Void> deleteConvertLinkRule(Long ruleId){
        return marketingClient.deleteConvertLinkRule(AuthContextHolder.getCompanyType(), AuthContextHolder.getUserId(), ruleId);
    };

    /**
     * 获取链路转化规则列表
     * @param linkId
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<NpConvertLinkRule>> getConvertLinkRuleList(Long linkId, Integer current, Integer size){
        return marketingClient.getConvertLinkRuleList(linkId, current, size);
    };
}

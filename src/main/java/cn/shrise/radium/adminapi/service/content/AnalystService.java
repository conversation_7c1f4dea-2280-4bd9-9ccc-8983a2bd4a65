package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.AnalystAuditApplyResp;
import cn.shrise.radium.adminapi.resp.content.AnalystInfoResp;
import cn.shrise.radium.adminapi.resp.content.AnalystModifyResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystAuditApply;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.entity.SsAnalystModifyRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class AnalystService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PageResult<List<AnalystInfoResp>> getAnalystInfoListPage(Integer companyType, Boolean enabled, Boolean gwShow,
                                                                    Integer auditStatus, Integer current, Integer size) {
        PageResult<List<SsAnalystInfo>> pageResult = contentClient.getAnalystInfoListPage(companyType, enabled, gwShow, auditStatus, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<SsAnalystInfo> data = pageResult.getData();

        Set<Integer> userSet = data.stream().map(SsAnalystInfo::getAuditId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));
        List<AnalystInfoResp> records = data.stream().map(t -> {
            AnalystInfoResp resp = new AnalystInfoResp();
            BeanUtil.copyProperties(t, resp);
            if (ObjectUtil.isNotNull(t.getAuditId())) {
                resp.setAuditName(usersMap.get(t.getAuditId()).getRealName());
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }

    public PageResult<List<AnalystModifyResp>> getAnalystModifyRecordPage(Integer analystId, Integer current, Integer size) {
        PageResult<List<SsAnalystModifyRecord>> pageResult = contentClient.getAnalystModifyRecordPage(analystId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<SsAnalystModifyRecord> data = pageResult.getData();

        Set<Integer> userSet = data.stream().map(SsAnalystModifyRecord::getOperateId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));

        List<AnalystModifyResp> records = data.stream().map(t -> {
            AnalystModifyResp resp = new AnalystModifyResp();
            BeanUtil.copyProperties(t, resp);
            resp.setOperateName(usersMap.get(t.getOperateId()).getRealName());
            resp.setOperateAvatarUrl(usersMap.get(t.getOperateId()).getAvatarUrl());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }

    public PageResult<List<AnalystAuditApplyResp>> getAuditApplyPage(Integer auditStatus, Integer analystId, Integer current, Integer size) {
        PageResult<List<SsAnalystAuditApply>> pageResult = contentClient.getAuditRecordPage(auditStatus, analystId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<SsAnalystAuditApply> data = pageResult.getData();
        Set<Integer> userSet = data.stream().flatMap(apply -> Stream.of(apply.getOperateId(), apply.getAuditorId())).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        Set<Integer> analystSet = data.stream().map(SsAnalystAuditApply::getAnalystId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        List<SsAnalystInfo> analystInfoList = contentClient.getAnalystInfoList(BatchReq.create(analystSet)).getData();
        Map<Integer, SsAnalystInfo> analystMap = analystInfoList.stream().collect(Collectors.toMap(SsAnalystInfo::getId, Function.identity()));

        List<AnalystAuditApplyResp> records = data.stream().map(t -> {
            AnalystAuditApplyResp resp = new AnalystAuditApplyResp();
            BeanUtil.copyProperties(t, resp);
            resp.setOperateName(usersMap.get(t.getOperateId()).getRealName());
            if (ObjectUtil.isNotEmpty(t.getAuditorId())) {
                resp.setAuditorName(usersMap.get(t.getAuditorId()).getRealName());
            }
            resp.setAnalystName(analystMap.getOrDefault(t.getAnalystId(), new SsAnalystInfo()).getName());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }
}

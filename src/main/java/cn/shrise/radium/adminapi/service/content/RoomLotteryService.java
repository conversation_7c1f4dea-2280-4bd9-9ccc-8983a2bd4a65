package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.enums.RoomLotteryType;
import cn.shrise.radium.contentservice.req.CreateRoomLotteryReq;
import cn.shrise.radium.contentservice.req.UpdateRoomLotteryReq;
import cn.shrise.radium.contentservice.resp.RoomLotteryResp;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImLiveRoom;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RoomLotteryService {

    private final ContentClient contentClient;
    private final ImClient imClient;

    public PageResult<List<RoomLotteryResp>> getRoomLottery(Long roomId, Long sceneId, Integer status, LocalDate startTime, LocalDate endTime, Integer salesId, Integer companyType, Integer current, Integer size) {

        List<Long> roomList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(roomId)){
            roomList.add(roomId);
        }
        if (ObjectUtil.isNotEmpty(salesId)){
            BaseResult<List<Long>> baseResult = imClient.getLiveRoomByManager(salesId);
            if (baseResult.isPresent()){
                roomList.addAll(baseResult.getData());
            }
        }

        PageResult<List<RoomLotteryResp>> roomLottery = contentClient.getRoomLottery(roomList, sceneId, status, startTime, endTime, salesId, current, size);
        if (roomLottery.isFail()) {
            throw new BusinessException(roomLottery);
        }
        if (!roomLottery.isPresent()){
            return PageResult.empty();
        }
        BaseResult<List<ImLiveRoom>> liveRoomResult = imClient.filterLiveRoomList(companyType, null, null, null, true);
        if (liveRoomResult.isFail()){
            throw new BusinessException(liveRoomResult);
        }
        if (!liveRoomResult.isPresent()){
            return roomLottery;
        }
        Map<Long, ImLiveRoom> roomMap = liveRoomResult.getData().stream().collect(Collectors.toMap(ImLiveRoom::getId, Function.identity()));
        List<RoomLotteryResp> resps = roomLottery.getData();
        resps.forEach(resp->{
            if (roomMap.containsKey(resp.getRoomId())){
                resp.setRoomName(roomMap.get(resp.getRoomId()).getName());
            }
        });
        return PageResult.success(resps,roomLottery.getPagination());
    }

    public BaseResult<String> createRoomLottery(CreateRoomLotteryReq req) {
        BaseResult<String> roomLottery = contentClient.createRoomLottery(req);
        if (roomLottery.isFail()) {
            throw new BusinessException(roomLottery);
        }
        return roomLottery;
    }

    public BaseResult<String> updateRoomLottery(UpdateRoomLotteryReq req) {
        BaseResult<String> contentErrorCodeBaseResult = contentClient.updateRoomLottery(req);
        if (contentErrorCodeBaseResult.isFail()) {
            throw new BusinessException(contentErrorCodeBaseResult);
        }
        return contentErrorCodeBaseResult;
    }

    public BaseResult<String> updateRoomLotteryStatus(Long lotteryId, Integer status, Integer userId) {
        RoomLotteryType roomLotteryTypeByValue = RoomLotteryType.findRoomLotteryTypeByValue(status);
        return contentClient.updateRoomLotteryStatus(lotteryId, roomLotteryTypeByValue, userId);
    }
}

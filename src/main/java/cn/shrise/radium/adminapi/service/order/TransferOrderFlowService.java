package cn.shrise.radium.adminapi.service.order;

import cn.shrise.radium.adminapi.entity.SubOrderInfo;
import cn.shrise.radium.adminapi.entity.TransferOrderFlowDetail;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.resp.TransferOrderFlowItem;
import cn.shrise.radium.adminapi.resp.TransferOrderFlowRecordItem;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.adminapi.req.CreateTransferOrderFlowReq;
import cn.shrise.radium.orderservice.properties.merchant.Merchant;
import cn.shrise.radium.orderservice.req.UpdateTransferOrderFlowReq;
import cn.shrise.radium.orderservice.req.payment.CreateTransferOrderReq;
import cn.shrise.radium.orderservice.resp.FullSubOrder;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;

import static cn.shrise.radium.orderservice.constant.TransferOrderFlowStatusConstant.*;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class TransferOrderFlowService {

    private final OrderClient orderClient;
    private final UserClient userClient;

    public BaseResult<TransferOrderFlowDetail> getTransferOrderFlow(Long id) {
        BaseResult<TransferOrderFlow> result = orderClient.getTransferOrderFlow(id);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        TransferOrderFlow flow = result.getData();
        TransferOrderFlowDetail detail = transformToTransferOrderFlowDetail(flow);
        return BaseResult.success(detail);
    }

    public BaseResult<TransferOrderFlowDetail> getTransferOrderFlow(Integer subOrderId) {
        BaseResult<TransferOrderFlow> result = orderClient.getTransferOrderFlow(subOrderId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        TransferOrderFlow flow = result.getData();
        TransferOrderFlowDetail detail = transformToTransferOrderFlowDetail(flow);
        return BaseResult.success(detail);
    }

    private TransferOrderFlowDetail transformToTransferOrderFlowDetail(TransferOrderFlow flow) {
        Set<Integer> userIdSet = new HashSet<>(Arrays.asList(flow.getAuditorId(), flow.getCreatorId()));
        BaseResult<CourseSubOrder> getSubOrderResult = orderClient.getSubOrder(flow.getSubOrderId());
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userIdSet));

        CourseSubOrder subOrder = getSubOrderResult.orElseThrow();
        RsMerchantInfo merchantInfo = orderClient.getMerchantInfo(subOrder.getMerchantId()).orElse(new RsMerchantInfo());
        Integer mchType = subOrder.getMchType();
        String mchName = null;
        if (mchType != null) {
            BaseResult<Merchant> getMerchantResult = orderClient.getMerchant(mchType);
            if (getMerchantResult.isSuccess()) {
                mchName = getMerchantResult.getData().getName();
            }
        }
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess()? getUserResult.getData(): Collections.emptyMap();
        UcUsers auditor = userMap.get(flow.getAuditorId());
        UcUsers creator = userMap.get(flow.getCreatorId());
        String images = flow.getImages();
        List<String> imageList = JSONObject.parseArray(images, String.class);
        return TransferOrderFlowDetail.builder()
                .subOrderId(flow.getSubOrderId())
                .subOrderInfo(SubOrderInfo.of(subOrder))
                .auditorId(flow.getAuditorId())
                .creatorId(flow.getCreatorId())
                .companyType(flow.getCompanyType())
                .flowStatus(flow.getFlowStatus())
                .auditTime(flow.getAuditTime())
                .id(flow.getId())
                .images(imageList)
                .auditorInfo(UserInfo.of(auditor))
                .creatorInfo(UserInfo.of(creator))
                .mchType(mchType)
                .mchName(mchName)
                .gmtCreate(flow.getGmtCreate())
                .gmtModified(flow.getGmtModified())
                .remark(flow.getRemark())
                .platform(merchantInfo.getPlatform())
                .channelType(merchantInfo.getChannelType())
                .merchantName(merchantInfo.getName())
                .build();
    }

    public PageResult<List<TransferOrderFlowItem>> getTransferOrderFlowList(
            Integer companyType,
            Integer flowStatus,
            Integer creatorId,
            Instant startTime,
            Instant endTime,
            Integer current,
            Integer size) {

        PageResult<List<TransferOrderFlow>> result = orderClient
                .getTransferOrderFlowList(companyType, flowStatus, creatorId, startTime, endTime, current, size);
        if (result.isFail()) {
            return PageResult.empty(current, size);
        }
        List<TransferOrderFlow> flowList = result.getData();
        Long total = result.getPagination().getTotal();

        List<TransferOrderFlowItem> transferOrderFlowItems = transformToTransferOrderFlowItem(flowList);

        return PageResult.success(transferOrderFlowItems, Pagination.of(current, size, total));
    }

    public PageResult<List<TransferOrderFlowItem>> searchTransferOrderFlow(
            Integer companyType,
            Integer creatorId,
            String orderNumber,
            Integer current,
            Integer size) {

        PageResult<List<TransferOrderFlow>> result = orderClient
                .searchTransferOrderFlow(companyType, creatorId, orderNumber, null, current, size);
        List<TransferOrderFlow> flowList = result.orElseThrow();
        Long total = result.getPagination().getTotal();

        List<TransferOrderFlowItem> transferOrderFlowItems = transformToTransferOrderFlowItem(flowList);
        return PageResult.success(transferOrderFlowItems, Pagination.of(current, size, total));
    }

    private List<TransferOrderFlowItem> transformToTransferOrderFlowItem(List<TransferOrderFlow> flowList) {
        Set<Integer> subOrderIdSet = new HashSet<>();
        Set<Integer> userIdSet = new HashSet<>();

        flowList.forEach(flow -> {
            subOrderIdSet.add(flow.getSubOrderId());
            userIdSet.add(flow.getAuditorId());
            userIdSet.add(flow.getCreatorId());
        });

        BaseResult<List<FullSubOrder>> getSubOrderResult = orderClient
                .getFullSubOrderList(BatchReq.create(subOrderIdSet));
        if (getSubOrderResult.isFail()) {
            return Collections.emptyList();
        }
        List<FullSubOrder> fullOrderList = getSubOrderResult.getData();
        fullOrderList.forEach(order -> {
            RsCourseOrder courseOrder = order.getOrder();
            userIdSet.add(courseOrder.getUserId());
            userIdSet.add(courseOrder.getSalesId());
        });

        BaseResult<Map<Integer, UcUsers>> batchGetUserResult = userClient.batchGetUserMap(BatchReq.create(userIdSet));
        Map<Integer, UcUsers> userMap = batchGetUserResult.isSuccess() ? batchGetUserResult.getData(): Collections.emptyMap();

        Map<Integer, FullSubOrder> subOrderMap = fullOrderList.stream().collect(toMap(FullSubOrder::getSubOrderId, Function.identity()));

        return flowList.stream()
                .map(flow -> {
                    Integer subOrderId = flow.getSubOrderId();
                    FullSubOrder fullOrder = subOrderMap.get(subOrderId);
                    RsCourseOrder order = fullOrder.getOrder();
                    Integer orderId = order.getId();
                    RsSku sku = fullOrder.getSku();
                    CourseSubOrder subOrder = fullOrder.getSubOrder();

                    UcUsers customer = userMap.get(order.getUserId());
                    UcUsers sales = userMap.get(order.getSalesId());
                    UcUsers auditor = userMap.get(flow.getAuditorId());
                    UcUsers creator = userMap.get(flow.getCreatorId());

                    return TransferOrderFlowItem.builder()
                            .id(flow.getId())
                            .userId(order.getUserId())
                            .userInfo(UserInfo.of(customer))
                            .salesId(order.getSalesId())
                            .salesInfo(UserInfo.of(sales))
                            .orderId(orderId)
                            .orderNumber(order.getOrderNumber())
                            .skuName(sku.getShowName())
                            .skuId(sku.getId())
                            .subOrderId(subOrderId)
                            .subOrderNumber(subOrder.getNumber())
                            .subOrderAmount(subOrder.getAmount())
                            .subOrderPayType(subOrder.getPayType())
                            .companyType(flow.getCompanyType())
                            .flowStatus(flow.getFlowStatus())
                            .auditorId(flow.getAuditorId())
                            .auditorInfo(UserInfo.of(auditor))
                            .auditTime(flow.getAuditTime())
                            .creatorId(flow.getCreatorId())
                            .creatorInfo(UserInfo.of(creator))
                            .gmtCreate(flow.getGmtCreate())
                            .gmtModified(flow.getGmtModified())
                            .build();
                })
                .collect(toList());
    }

    public PageResult<List<TransferOrderFlowRecordItem>> getTransferOrderFlowRecordList(Integer subOrderId, Integer current, Integer size) {
        PageResult<List<TransferOrderFlowRecord>> pageResult = orderClient.getTransferOrderFlowRecordList(subOrderId, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }
        Pagination pagination = pageResult.getPagination();
        List<TransferOrderFlowRecord> recordList = pageResult.getData();
        List<Integer> userIds = recordList.stream().map(TransferOrderFlowRecord::getOperatorId).distinct().collect(toList());
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userIds));
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess()? getUserResult.getData(): Collections.emptyMap();
        List<TransferOrderFlowRecordItem> items = recordList.stream()
                .map(e -> TransferOrderFlowRecordItem.builder()
                        .id(e.getId())
                        .operatorId(e.getOperatorId())
                        .operateType(e.getOperateType())
                        .subOrderId(e.getSubOrderId())
                        .gmtCreate(e.getGmtCreate())
                        .gmtModified(e.getGmtModified())
                        .operatorInfo(UserInfo.of(userMap.get(e.getOperatorId())))
                        .build())
                .collect(toList());
        return PageResult.success(items, pagination);
    }

    public void closeTransferOrderFlow(Long flowId, Integer auditorId) {
        UpdateTransferOrderFlowReq req = UpdateTransferOrderFlowReq.builder()
                .auditorId(auditorId)
                .auditTime(Instant.now())
                .flowStatus(STATUS_CLOSED)
                .build();
        orderClient.closeTransferOrder(flowId, req);
    }

    public void confirmTransferOrderFlow(Long flowId, Integer auditorId) {
        BaseResult<TransferOrderFlow> transferOrderFlowResult = orderClient.getTransferOrderFlow(flowId);
        if (transferOrderFlowResult.isFail()) {
            throw new BusinessException(transferOrderFlowResult);
        }
        TransferOrderFlow orderFlow = transferOrderFlowResult.getData();
        if (orderFlow.getFlowStatus() != STATUS_PENDING) {
            throw new BusinessException("订单状态不正确");
        }
        UpdateTransferOrderFlowReq req = UpdateTransferOrderFlowReq.builder()
                .flowStatus(STATUS_SUCCESS)
                .auditorId(auditorId)
                .auditTime(Instant.now())
                .build();
        orderClient.confirmTransferOrder(flowId, req);
    }

    public BaseResult<Void> createTransferOrder(Integer companyType, Integer creatorId, CreateTransferOrderFlowReq req) {
        Integer orderId = req.getOrderId();
        int transferAmount = req.getAmount();
        List<String> imageList = ObjectUtils.isNotEmpty(req.getImages())? req.getImages():Collections.emptyList();
        String imageString = JSONObject.toJSONString(imageList);
        CreateTransferOrderReq createTransferOrderFlowReq = CreateTransferOrderReq.builder()
                .orderId(orderId)
                .amount(transferAmount)
                .companyType(companyType)
                .creatorId(creatorId)
                .images(imageString)
                .merchantId(req.getMerchantId())
                .remark(req.getRemark())
                .build();
        return orderClient.createTransferOrder(createTransferOrderFlowReq);
    }
}

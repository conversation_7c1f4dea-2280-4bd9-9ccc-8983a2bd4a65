package cn.shrise.radium.adminapi.service.trade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.secureservice.SecureServiceClient;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdMyStockDetail;
import cn.shrise.radium.tradeservice.entity.TdMyStockRecord;
import cn.shrise.radium.tradeservice.resp.MyStockRecordResp;
import cn.shrise.radium.tradeservice.resp.MyStockDetailResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.UserWxExtDto;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class MyStockService {

    private final TradeClient tradeClient;
    private final SecureServiceClient secureServiceClient;
    private final UserClient userClient;

    public PageResult<List<MyStockDetailResp>> getMyStockList(String searchContent, Integer current, Integer size) {
        List<Integer> userIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(searchContent)) {
            if (PhoneUtil.isMobile(searchContent)) {
                Long mobileId = secureServiceClient.getMobileId(searchContent).getData();
                Optional.ofNullable(userClient.getUserByMobileId(mobileId, 45, null).getData())
                        .map(UcUsers::getId)
                        .ifPresent(userIdList::add);
            } else if (NumberUtil.isInteger(searchContent)) {
                Integer wxId = Integer.valueOf(searchContent);
                Optional.ofNullable(userClient.findById(wxId).getData())
                        .map(UcWxExt::getUserId)
                        .ifPresent(userIdList::add);
            } else if (DesensitizeUtil.isValidUserCode(searchContent)) {
                int userId = DesensitizeUtil.maskToId(searchContent);
                Optional.ofNullable(userClient.getUser(userId).getData())
                        .map(UcUsers::getId)
                        .ifPresent(userIdList::add);
            }
            if (CollUtil.isEmpty(userIdList)) {
                return PageResult.success(Collections.emptyList(), Pagination.of(current, size));
            }
        }

        return buildStockDetailResp(tradeClient.getMyStockList(userIdList, current, size));
    }

    private PageResult<List<MyStockDetailResp>> buildStockDetailResp(PageResult<List<TdMyStockDetail>> stockListPage) {
        List<TdMyStockDetail> stockDetailList = stockListPage.getData();
        if (CollUtil.isEmpty(stockDetailList)) {
            return PageResult.success(Collections.emptyList(), stockListPage.getPagination());
        }
        List<Integer> userIdList = stockDetailList.stream().map(TdMyStockDetail::getUserId).collect(Collectors.toList());
        List<UserWxExtDto> listBaseResult = userClient.filterWxExtByUserIds(new HashSet<>(userIdList)).orElseThrow();
        Map<Integer, UcWxExt> userWxExtMap = listBaseResult.stream()
                .filter(userWxExtDto -> ObjectUtil.isNotEmpty(userWxExtDto.getUcUsers()))
                .filter(userWxExtDto -> ObjectUtil.isNotEmpty(userWxExtDto.getUcWxExt()))
                .collect(Collectors.toMap(e -> e.getUcUsers().getId(), userWxExtDto -> userWxExtDto.getUcWxExt()));
        List<MyStockDetailResp> myStockDetailResps = new ArrayList<>();
        for (TdMyStockDetail tdMyStockDetail : stockDetailList) {
            MyStockDetailResp detailResp = new MyStockDetailResp();
            BeanUtil.copyProperties(tdMyStockDetail, detailResp);
            detailResp.setWxId(userWxExtMap.getOrDefault(tdMyStockDetail.getUserId(), new UcWxExt()).getId());
            detailResp.setWxNickname(userWxExtMap.getOrDefault(tdMyStockDetail.getUserId(), new UcWxExt()).getNickname());
            detailResp.setUserCode(DesensitizeUtil.idToMask(tdMyStockDetail.getUserId()));
            myStockDetailResps.add(detailResp);
        }
        return PageResult.success(myStockDetailResps, stockListPage.getPagination());
    }

    public PageResult<List<MyStockRecordResp>> getMyStockRecord(String searchContent, Integer current, Integer size) {
        List<Integer> userIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(searchContent)) {
            if (PhoneUtil.isMobile(searchContent)) {
                Long mobileId = secureServiceClient.getMobileId(searchContent).getData();
                Optional.ofNullable(userClient.getUserByMobileId(mobileId, 45, null).getData())
                        .map(UcUsers::getId)
                        .ifPresent(userIdList::add);
            } else if (NumberUtil.isInteger(searchContent)) {
                Integer wxId = Integer.valueOf(searchContent);
                Optional.ofNullable(userClient.findById(wxId).getData())
                        .map(UcWxExt::getUserId)
                        .ifPresent(userIdList::add);
            } else if (DesensitizeUtil.isValidUserCode(searchContent)) {
                int userId = DesensitizeUtil.maskToId(searchContent);
                Optional.ofNullable(userClient.getUser(userId).getData())
                        .map(UcUsers::getId)
                        .ifPresent(userIdList::add);
            }
            if (CollUtil.isEmpty(userIdList)) {
                return PageResult.success(Collections.emptyList(), Pagination.of(current, size));
            }
        }
        return buildStockRecordResp(tradeClient.getMyStockRecordList(userIdList, current, size));
    }

    private PageResult<List<MyStockRecordResp>> buildStockRecordResp(PageResult<List<TdMyStockRecord>> stockRecordPage) {
        List<TdMyStockRecord> stockRecordList = stockRecordPage.getData();
        if (CollUtil.isEmpty(stockRecordList)) {
            return PageResult.success(Collections.emptyList(), stockRecordPage.getPagination());
        }
        List<Integer> userIdList = stockRecordList.stream().map(TdMyStockRecord::getUserId).collect(Collectors.toList());
        List<UserWxExtDto> listBaseResult = userClient.filterWxExtByUserIds(new HashSet<>(userIdList)).orElseThrow();
        Map<Integer, UcWxExt> userWxExtMap = listBaseResult.stream()
                .filter(userWxExtDto -> ObjectUtil.isNotEmpty(userWxExtDto.getUcUsers()))
                .filter(userWxExtDto -> ObjectUtil.isNotEmpty(userWxExtDto.getUcWxExt()))
                .collect(Collectors.toMap(e -> e.getUcUsers().getId(), userWxExtDto -> userWxExtDto.getUcWxExt()));
        List<MyStockRecordResp> myStockRecordResps = new ArrayList<>();
        for (TdMyStockRecord tdMyStockRecord : stockRecordList) {
            MyStockRecordResp recordResp = new MyStockRecordResp();
            BeanUtil.copyProperties(tdMyStockRecord, recordResp);
            recordResp.setWxId(userWxExtMap.getOrDefault(tdMyStockRecord.getUserId(), new UcWxExt()).getId());
            recordResp.setWxNickname(userWxExtMap.getOrDefault(tdMyStockRecord.getUserId(), new UcWxExt()).getNickname());
            recordResp.setUserCode(DesensitizeUtil.idToMask(tdMyStockRecord.getUserId()));
            myStockRecordResps.add(recordResp);
        }
        return PageResult.success(myStockRecordResps, stockRecordPage.getPagination());
    }

}
package cn.shrise.radium.adminapi.service.weimob;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.OrderStatusConstant;
import cn.shrise.radium.orderservice.entity.CourseSubOrder;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.weimobservice.WeiMobClient;
import cn.shrise.radium.weimobservice.req.OrderPointListReq;
import cn.shrise.radium.weimobservice.resp.OrderPointActivityResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderPointService {

    private final WeiMobClient weiMobClient;
    private final UserClient userClient;

    private final OrderClient orderClient;

    public PageResult<List<OrderPointActivityResp>> getOrderPointInfoPage(OrderPointListReq req, Integer status, String searchText, Integer userId, String timeType, LocalDate startDate, LocalDate endDate, Integer current, Integer size) {
        Integer orderId = null;
        // 搜索逻辑
        if (ObjectUtil.isNotEmpty(searchText)) {
            if (DesensitizeUtil.isValidUserCode(searchText)) {
                userId = DesensitizeUtil.maskToId(searchText);
            }else {
                RsCourseOrder order = orderClient.getOrder(searchText).orElse(null);
                if (ObjectUtil.isEmpty(order)) {
                    return PageResult.empty();
                }
                orderId = order.getId();
            }
        }
        List<Integer> salesIds = ObjectUtil.isNotEmpty(req)?req.getSalesIds(): new ArrayList<>();
        PageResult<List<OrderPointActivityResp>> orderPointInfoPage = weiMobClient.getOrderPointInfoPage(salesIds, status, orderId, userId, timeType, startDate, endDate, current, size);
        List<OrderPointActivityResp> infoList = orderPointInfoPage.getData();
        Set<Integer> userSet = infoList.stream().flatMap(i -> Stream.of(i.getUserId(), i.getSalesId())).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        List<Integer> orderList = infoList.stream().map(OrderPointActivityResp::getOrderId).collect(Collectors.toList());
        Map<Integer, String> orderMap = orderClient.batchGetOrderListById(orderList).getData().stream().collect(Collectors.toMap(RsCourseOrder::getId, RsCourseOrder::getOrderNumber));
        List<CourseSubOrder> subOrderList = orderClient.getSubOrderList(BatchReq.of(orderList), OrderStatusConstant.PASSED.getValue()).orElse(Collections.emptyList());
        Map<Integer, Integer> paidMap = subOrderList.stream().collect(Collectors.groupingBy(CourseSubOrder::getOrderId, Collectors.summingInt(CourseSubOrder::getAmount)));
        for (OrderPointActivityResp resp : infoList) {
            resp.setUserName(usersMap.getOrDefault(resp.getUserId(), new UcUsers()).getNickName());
            resp.setSalesName(usersMap.getOrDefault(resp.getSalesId(), new UcUsers()).getRealName());
            resp.setOrderNumber(orderMap.get(resp.getOrderId()));
            Integer paidAmount = paidMap.getOrDefault(resp.getOrderId(), 0);
            resp.setPaidAmount(paidAmount);
            resp.setUserCode(DesensitizeUtil.idToMask(resp.getUserId()));
        }
        return orderPointInfoPage;
    }
}

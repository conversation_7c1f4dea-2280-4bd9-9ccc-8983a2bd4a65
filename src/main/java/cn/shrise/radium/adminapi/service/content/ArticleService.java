package cn.shrise.radium.adminapi.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.content.ArticleCommentResp;
import cn.shrise.radium.adminapi.resp.content.ContentTeamArticleResp;
import cn.shrise.radium.adminapi.resp.content.SsArticleOperateRecordResp;
import cn.shrise.radium.adminapi.resp.content.TextScanResultResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsArticle;
import cn.shrise.radium.contentservice.entity.SsArticleComment;
import cn.shrise.radium.contentservice.entity.SsArticleOperateRecord;
import cn.shrise.radium.contentservice.entity.SsTextScanResult;
import cn.shrise.radium.contentservice.req.CreateArticleReq;
import cn.shrise.radium.contentservice.req.EditArticleReq;
import cn.shrise.radium.contentservice.req.UpdateArticleReq;
import cn.shrise.radium.contentservice.resp.ModerationLabelItem;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcContentTeam;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.contentservice.constant.ArticleStatusEnum.AS_Released;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ArticleService {

    private final ContentClient contentClient;

    private final UserClient userClient;

    private final WxClient wxClient;

    public PageResult<List<ContentTeamArticleResp>> getContentTeamArticlePage(Long teamId, Integer companyType, Integer articleStatus, Integer current, Integer size) {
        PageResult<List<SsArticle>> contentHostArticlePageResult = contentClient.getContentTeamArticlePage(Collections.singletonList(teamId), companyType, articleStatus, null, null, null, null, current, size);
        if (contentHostArticlePageResult.isPresent()) {
            List<ContentTeamArticleResp> respList = new ArrayList<>();
            List<SsArticle> data = contentHostArticlePageResult.getData();
            Set<Integer> userSet = new HashSet<>();
            data.forEach(e -> {
                userSet.add(e.getCreatorId());
                userSet.add(e.getAuditorId());
            });
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
            for (SsArticle article : data) {
                ContentTeamArticleResp resp = new ContentTeamArticleResp();
                resp.setArticle(article);
                if (userMap.containsKey(article.getCreatorId())) {
                    resp.setCreatorName(userMap.get(article.getCreatorId()).getRealName());
                }

                if (userMap.containsKey(article.getAuditorId())) {
                    resp.setAuditorName(userMap.get(article.getAuditorId()).getRealName());
                }
                respList.add(resp);
            }
            return PageResult.success(respList, contentHostArticlePageResult.getPagination());
        }
        return PageResult.empty();
    }

    public BaseResult<Void> createArticle(CreateArticleReq req) {
        return contentClient.createArticle(req);
    }

    public BaseResult<Void> updateArticle(UpdateArticleReq req) {
        return contentClient.updateArticle(req);
    }

    public BaseResult<Void> editArticle(EditArticleReq req) {
        return contentClient.editArticle(req);
    }

    public PageResult<List<ArticleCommentResp>> getArticleCommentPage(Long articleId, Boolean audit, Integer status, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {

        PageResult<List<SsArticleComment>> articleCommentPageResult = contentClient.getArticleCommentPage(articleId, audit, status, startTime, endTime, current, size);
        if (articleCommentPageResult.isPresent()) {
            List<SsArticleComment> articleCommentList = articleCommentPageResult.getData();
            List<ArticleCommentResp> respList = new ArrayList<>();
            Set<Integer> userSet = new HashSet<>();
            List<Integer> wxList = new ArrayList<>();
            Set<Long> resultSet = new HashSet<>();
            articleCommentList.forEach(e -> {
                if (ObjectUtil.isNotEmpty(e.getUserId())) {
                    userSet.add(e.getUserId());
                }
                if (ObjectUtil.isNotEmpty(e.getAuditorId())) {
                    userSet.add(e.getAuditorId());
                }
                if (ObjectUtil.isNotEmpty(e.getWxId())) {
                    wxList.add(e.getWxId());
                }
                if (ObjectUtil.isNotEmpty(e.getResultId())) {
                    resultSet.add(e.getResultId());
                }
            });
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
            Map<Integer, UcWxExt> wxMap = wxClient.batchGetUserWxExt(null, wxList).getData().stream().collect(Collectors.toMap(UcWxExt::getId, x -> x));
            Map<Long, SsTextScanResult> resultMap = contentClient.batchGetResultMap(BatchReq.create(resultSet)).getData();
            for (SsArticleComment articleComment : articleCommentList) {
                ArticleCommentResp resp = ArticleCommentResp.of(articleComment);
                if (ObjectUtil.isNotEmpty(articleComment.getUserId())) {
                    resp.setNickname(usersMap.get(articleComment.getUserId()).getNickName());
                } else if (ObjectUtil.isNotEmpty(articleComment.getWxId())) {
                    resp.setNickname(wxMap.get(articleComment.getWxId()).getNickname());
                }
                if (ObjectUtil.isNotEmpty(articleComment.getAuditorId())) {
                    resp.setAuditorName(usersMap.get(articleComment.getAuditorId()).getRealName());
                }
                if (ObjectUtil.isNotEmpty(articleComment.getResultId()) && resultMap.containsKey(articleComment.getResultId())) {
                    SsTextScanResult scanResult = resultMap.get(articleComment.getResultId());
                    List<String> labels = JSON.parseArray(scanResult.getLabels(), String.class);
                    List<String> labelNameList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(labels)) {
                        Map<String, ModerationLabelItem> itemMap = contentClient.getModerationLabels().getData().stream().collect(Collectors.toMap(ModerationLabelItem::getLabel, y -> y));
                        for (String label : labels) {
                            labelNameList.add(itemMap.get(label).getName());
                        }
                    }
                    TextScanResultResp resultResp = new TextScanResultResp();
                    resultResp.setLabels(labels);
                    resultResp.setLabelMsg(labelNameList);
                    resp.setPassed(scanResult.getPassed());
                    resp.setResultResp(resultResp);
                }
                respList.add(resp);
            }
            return PageResult.success(respList, articleCommentPageResult.getPagination());
        }
        return PageResult.empty();
    }

    public BaseResult<Void> auditArticleComment(Integer auditorId, Integer commentId, Integer status) {
        if (ObjectUtil.isEmpty(auditorId)) {
            throw new BusinessException("审核人不能为空");
        }
        return contentClient.auditArticleComment(auditorId, commentId, status);
    }

    public PageResult<List<ContentTeamArticleResp>> getTeamShareArticlePage(Long teamId, Integer companyType, LocalDate startTime, LocalDate endTime, String field, Boolean isAsc, Integer current, Integer size) {
        List<Long> teamIds = new ArrayList<>();
        if (ObjectUtil.isEmpty(teamId)) {
            teamIds = userClient.getVisibleByUser(AuthContextHolder.getUserId()).orElse(null);
            if (ObjectUtil.isEmpty(teamIds)) {
                return PageResult.empty();
            }
        }
        teamIds.add(teamId);
        PageResult<List<SsArticle>> contentHostArticlePageResult = contentClient.getContentTeamArticlePage(teamIds, companyType, AS_Released.getValue(), startTime, endTime, field, isAsc, current, size);
        if (contentHostArticlePageResult.isPresent()) {
            List<ContentTeamArticleResp> respList = new ArrayList<>();
            List<SsArticle> data = contentHostArticlePageResult.getData();
            Set<Long> teamSet = new HashSet<>();
            data.forEach(e -> {
                teamSet.add(e.getTeamId());
            });
            Map<Long, UcContentTeam> teamMap = userClient.batchContentTeamMap(BatchReq.create(teamSet)).getData();
            for (SsArticle article : data) {
                ContentTeamArticleResp resp = new ContentTeamArticleResp();
                resp.setArticle(article);
                if (teamMap.containsKey(article.getTeamId())) {
                    UcContentTeam team = teamMap.get(article.getTeamId());
                    resp.setAvatarUrl(team.getAvatarUrl());
                    resp.setTeamName(team.getName());
                }
                respList.add(resp);
            }
            return PageResult.success(respList, contentHostArticlePageResult.getPagination());
        }
        return PageResult.empty();
    }

    public BaseResult<List<SsArticleOperateRecordResp>> getOperateRecord(Long articleId) {
        List<SsArticleOperateRecordResp> resp = new ArrayList<>();
        BaseResult<List<SsArticleOperateRecord>> operateRecord = contentClient.getOperateRecord(articleId);
        if (!operateRecord.isPresent()) {
            return BaseResult.success(resp);
        }
        List<SsArticleOperateRecord> record = operateRecord.getData();
        List<Integer> operatorId = record.stream().map(SsArticleOperateRecord::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(operatorId)) {
            usersMap = userClient.batchGetUserMap(BatchReq.of(operatorId)).getData();
        }
        for (SsArticleOperateRecord ssArticleOperateRecord : record) {
            SsArticleOperateRecordResp ssArticleOperateRecordResp = new SsArticleOperateRecordResp();
            BeanUtils.copyProperties(ssArticleOperateRecord, ssArticleOperateRecordResp);
            if (ObjectUtil.isNotEmpty(ssArticleOperateRecord.getOperatorId())) {
                ssArticleOperateRecordResp.setAvatarUrl(usersMap.get(ssArticleOperateRecord.getOperatorId()).getAvatarUrl());
            }
            resp.add(ssArticleOperateRecordResp);
        }
        return BaseResult.success(resp);
    }
}

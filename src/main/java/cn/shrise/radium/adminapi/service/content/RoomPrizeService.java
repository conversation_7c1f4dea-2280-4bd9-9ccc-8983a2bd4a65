package cn.shrise.radium.adminapi.service.content;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsRoomPrize;
import cn.shrise.radium.contentservice.req.CreateRoomPrizeReq;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RoomPrizeService {

    private final ContentClient contentClient;

    public PageResult<List<SsRoomPrize>> getRoomPrizeList(Long id, Boolean isEnabled, Integer current, Integer size) {
        PageResult<List<SsRoomPrize>> roomPrizeList = contentClient.getRoomPrizeList(id, isEnabled, current, size);
        if (roomPrizeList.isFail()) {
            throw new BusinessException(roomPrizeList);
        }
        return roomPrizeList;
    }

    public BaseResult<SsRoomPrize> createRoomPrize(CreateRoomPrizeReq req) {
        BaseResult<SsRoomPrize> roomPrize = contentClient.createRoomPrize(req);
        if (roomPrize.isFail()) {
            throw new BusinessException(roomPrize);
        }
        return roomPrize;
    }

    public BaseResult<SsRoomPrize> updateRoomPrize(Long id, Boolean isEnabled) {
        BaseResult<SsRoomPrize> roomPrize = contentClient.updateRoomPrize(id, isEnabled);
        if (roomPrize.isFail()) {
            throw new BusinessException(roomPrize);
        }
        return roomPrize;
    }
}

package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.DepartmentDTO;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxrobotservice.WorkwxRobotServiceClient;
import cn.shrise.radium.workwxrobotservice.bean.PersonTagInfo;
import cn.shrise.radium.workwxrobotservice.constant.WorkWxRobotBatchStatusEnum;
import cn.shrise.radium.workwxrobotservice.constant.WorkWxRobotTaskStatusEnum;
import cn.shrise.radium.workwxrobotservice.entity.NpWwxRobotMsgLimitConfig;
import cn.shrise.radium.workwxrobotservice.entity.NpWwxRobotMsgLimitRecord;
import cn.shrise.radium.workwxrobotservice.req.WorkWxRobotTaskUploadReq;
import cn.shrise.radium.workwxrobotservice.req.WwxRobotMsgLimitReq;
import cn.shrise.radium.workwxrobotservice.resp.*;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WwxConfig;
import cn.shrise.radium.workwxservice.entity.NpWorkWxUser;
import cn.shrise.radium.workwxservice.property.WxCpProperties;
import cn.shrise.radium.workwxservice.resp.WorkWxRobotWorkWxInfoResp;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: tangjiajun
 * @Date: 2024/10/29 14:19
 * @Desc:
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkWxRobotService {

    private final WorkwxClient workwxClient;
    private final UserClient userClient;
    private final WorkwxRobotServiceClient workwxRobotServiceClient;

    public PageResult<List<WorkWxRobotBatchTaskResp>> getWorkWxRobotTaskBatchList(
            Integer userId, Integer status, String searchContent, LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        PageResult<List<WorkWxRobotBatchTaskResp>> batchTaskResp = workwxRobotServiceClient.getWorkWxRobotTaskBatchList(userId, status,
                searchContent, startTime, endTime, current, size);
        if (!batchTaskResp.isPresent()) {
            return PageResult.empty();
        }
        Set<Integer> salesSet = batchTaskResp.getData().stream().map(WorkWxRobotBatchTaskResp::getSalesId).collect(Collectors.toSet());
        Set<Long> batchIdSet = batchTaskResp.getData().stream().map(WorkWxRobotBatchTaskResp::getId).collect(Collectors.toSet());
        Map<Integer, UcUsers> salesMap = userClient.batchGetUserMap(BatchReq.create(salesSet)).orElse(new HashMap<>());
        Map<Long, List<WorkWxRobotMessageResp>> messageMap = workwxRobotServiceClient.getMessageMapByBatchId(BatchReq.create(batchIdSet)).orElse(new HashMap<>());
        List<WorkWxRobotBatchTaskResp> batchTaskList = batchTaskResp.getData().stream().map(i -> {
            i.setSalesName(salesMap.getOrDefault(i.getSalesId(), new UcUsers()).getRealName());
            i.setMessageList(messageMap.getOrDefault(i.getId(), new ArrayList<WorkWxRobotMessageResp>()));
            return i;
        }).collect(Collectors.toList());
        return new PageResult<>(batchTaskList, batchTaskResp.getPagination());
    }

    public PageResult<List<WorkWxRobotTaskResp>> getWorkWxRobotTaskListById(Long batchId, Integer current, Integer size) {

        PageResult<List<WorkWxRobotTaskResp>> taskList = workwxRobotServiceClient.getWorkWxRobotTaskListById(batchId, current, size);
        if (!taskList.isPresent()) {
            return PageResult.empty();
        }
        List<String> tagIds = new ArrayList<>();
        //获取全部的tagId
        taskList.getData().forEach(i -> {
                    if (ObjectUtils.isNotEmpty(i.getTagIdList())) {
                        tagIds.addAll(JSON.parseArray(i.getTagIdList(), String.class));
                        i.setTagIds(JSON.parseArray(i.getTagIdList(), String.class));
                        List<WorkWxTagInfo> collect = i.getTagIds().stream().map(e -> {
                            WorkWxTagInfo workWxTagInfo = new WorkWxTagInfo();
                            workWxTagInfo.setTagId(e);
                            return workWxTagInfo;
                        }).collect(Collectors.toList());
                        i.setTagList(collect);
                    }
                    if(ObjectUtils.isNotEmpty(i.getPersonTagMsg())) {
                        List<PersonTagInfo> personTagList = JSON.parseArray(i.getPersonTagMsg(), PersonTagInfo.class);
                        i.setPersonTagList(personTagList);
                    }
                }
        );
        //根据tagId获取tagMap
        Map<String, String> tagMap = workwxRobotServiceClient.getTagMapByTagIds(BatchReq.create(tagIds)).orElse(new HashMap<>());
        //拉取全部企业微信配置
        Map<Integer, WxCpProperties> configMap = workwxClient.getAccountConfig().getData();
        taskList.getData().forEach(i -> {
            if (ObjectUtils.isNotEmpty(i.getAddStartTime())) {
                i.setStartTime(DateUtils.instantToLocalDate(i.getAddStartTime()));
            }
            if (ObjectUtils.isNotEmpty(i.getAddEndTime())) {
                i.setEndTime(DateUtils.instantToLocalDate(i.getAddEndTime()));
            }
            if (ObjectUtils.isNotEmpty(i.getPhone())) {
                i.setPhone(DesensitizeUtil.mobilePhone(AESUtil.decrypt(i.getPhone())));
            }
            i.setCorpName(configMap.getOrDefault(i.getAccountType(), new WxCpProperties()).getCorpName());
            i.getTagList().forEach(e -> {
                e.setTagName(tagMap.get(e.getTagId()));
            });
            if (ObjectUtils.isEmpty(i.getTotalCount())) {
                i.setTotalCount(0);
            }
        });
        return taskList;
    }

    public BaseResult<Void> uploadWorkWxRobotTask(WorkWxRobotTaskUploadReq req) {
        //更新任务相关信息
        workwxRobotServiceClient.uploadWorkWxRobotTask(req);
        //查询该任务所在批次是否全部任务完成
        Long batchId = workwxRobotServiceClient.getBatchIdByTaskId(req.getTaskId()).getData();
        BaseResult<List<Integer>> statusList = workwxRobotServiceClient.getAllTaskStatus(batchId);
        List<Boolean> booleanList = statusList.getData().stream()
                .map(this::isContain)
                .collect(Collectors.toList());
        //若全部完成，更新批次状态
        if (!booleanList.contains(false)){
            workwxRobotServiceClient.uploadBatchStatus(batchId, WorkWxRobotBatchStatusEnum.SEND_SUCCESS.getCode());
        }
        return BaseResult.successful();
    }

    public Boolean isContain(Integer status){
        //判断发送状态是否在枚举类列表中(下发失败, 发送完成, 发送失败)
        List<Integer> enumList = Arrays.asList(WorkWxRobotTaskStatusEnum.SEND_FAIL.getCode(), WorkWxRobotTaskStatusEnum.TRANSMIT_COMPLETE.getCode(), WorkWxRobotTaskStatusEnum.TRANSMIT_FAIL.getCode());
        return enumList.contains(status);
    }


    public BaseResult<List<WorkWxRobotWorkWxInfoResp>> getMyAccountList(Integer userId, Integer companyType, Integer accountType, Boolean isAvailable){
        UcUsers user = userClient.getUser(userId).getData();
        final BaseResult<List<NpWorkWxUser>> result = workwxClient.getMyAccountList(userId, companyType, accountType,1);
        final BaseResult<List<WwxConfig>> configResult = workwxClient.getConfigList(companyType);
        Map<Integer, WwxConfig> configMap;
        if (result.isFail()) {
            log.error("获取企业微信账号失败, {}", result);
            throw new BusinessException("获取企业微信账号失败");
        }

        if (configResult.isFail()) {
            log.error("获取企业微信配置失败: {}", configResult);
            configMap = Collections.emptyMap();
        } else {
            configMap = configResult.getData().stream().collect(Collectors.toMap(WwxConfig::getAccountType, Function.identity()));
        }
        final List<NpWorkWxUser> data = result.getData();
        List<WorkWxRobotWorkWxInfoResp> resps = new ArrayList<>();
        data.stream().forEach(
                e-> resps.add(
                        WorkWxRobotWorkWxInfoResp.builder()
                                .id(e.getId())
                                .wxAccount(e.getWxAccount())
                                .accountType(e.getAccountType())
                                .companyType(e.getCompanyType())
                                .name(e.getName())
                                .mobile(ObjectUtils.isNotEmpty(e.getMobile())? DesensitizeUtil.mobilePhone(AESUtil.decrypt(e.getMobile())):e.getMobile())
                                .isAvailable(false)
                                .saleName(user!=null?user.getRealName():null)
                                .cropName(configMap.get(e.getAccountType())!=null?configMap.get(e.getAccountType()).getCorpName():null)
                                .build()
                )

        );
        // 通过接口检查每个企微号的可用状态
        resps.forEach(
                e->{
                    if (workwxRobotServiceClient.getAccountConnectStatus(e.getAccountType(),e.getWxAccount()).getData()) {
                        e.setIsAvailable(true);
                    }
                }
        );
        if (ObjectUtils.isEmpty(isAvailable)) {
            return BaseResult.success(resps) ;
        }
        List<WorkWxRobotWorkWxInfoResp> respList = resps.stream().filter(e->e.getIsAvailable().equals(isAvailable)).collect(Collectors.toList());
        return BaseResult.success(respList);
    }

    public BaseResult<List<WwxRobotMsgLimitResp>> msgLimitList() {
        BaseResult<List<WwxRobotMsgLimitResp>> wwxRobotMsgLimitResps = workwxRobotServiceClient.msgLimitList();
        List<Integer> deptIds = wwxRobotMsgLimitResps.getData().stream()
                .map(WwxRobotMsgLimitResp::getDepartmentId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<UcDepartment> ucDepartments = userClient.batchGetDeptList(deptIds).orElse(new ArrayList<UcDepartment>());
        Map<Integer, UcDepartment> deptMap = ucDepartments.stream().collect(Collectors.toMap(UcDepartment::getId, department -> department));
        wwxRobotMsgLimitResps.getData().forEach(resp -> {
            Integer departmentId = resp.getDepartmentId();
            if (departmentId != null && deptMap.containsKey(departmentId)) {
                UcDepartment department = deptMap.get(departmentId);
                resp.setDepartmentName(department.getName());
            }
        });
        return wwxRobotMsgLimitResps;
    }

    public PageResult<List<WwxRobotMsgLimitRecordResp>> msgLimitRecord(Integer current, Integer size) {
        PageResult<List<NpWwxRobotMsgLimitRecord>> pageResult = workwxRobotServiceClient.msgLimitRecord(current, size);
        if(pageResult.isPresent()) {
            List<NpWwxRobotMsgLimitRecord> recordList = pageResult.getData();
            Set<Integer> userSet = recordList.stream().map(NpWwxRobotMsgLimitRecord::getOperatorId).collect(Collectors.toSet());
            List<Integer> deptIds = recordList.stream().map(NpWwxRobotMsgLimitRecord::getDepartmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
            List<UcDepartment> ucDepartments = userClient.batchGetDeptList(deptIds).orElse(new ArrayList<UcDepartment>());
            Map<Integer, UcDepartment> deptMap = ucDepartments.stream().collect(Collectors.toMap(UcDepartment::getId, department -> department));
            List<WwxRobotMsgLimitRecordResp> recordResps = new ArrayList<>();
            for (NpWwxRobotMsgLimitRecord record : recordList) {
                WwxRobotMsgLimitRecordResp recordResp = new WwxRobotMsgLimitRecordResp();
                BeanUtil.copyProperties(record, recordResp);
                if (userMap.containsKey(record.getOperatorId())) {
                    recordResp.setOperatorName(userMap.get(record.getOperatorId()).getRealName());
                }
                if (deptMap.containsKey(record.getDepartmentId())) {
                    recordResp.setDepartmentName(deptMap.get(record.getDepartmentId()).getName());
                }
                recordResps.add(recordResp);
            }
            return PageResult.success(recordResps, pageResult.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<WwxRobotSendDetailResp>> msgDetail(Long id, Integer current, Integer size) {
        PageResult<List<WwxRobotSendDetailResp>> pageResult = workwxRobotServiceClient.msgDetail(id, current, size);
        return pageResult;
    }

    public BaseResult<Void> createMsgLimit(Integer userId, WwxRobotMsgLimitReq req) {
        List<DepartmentDTO> deptDtos =  userClient.getDepartmentTree(req.getDepartmentId(),null).orElse(new ArrayList<>());
        List<Integer> allDeptIds =  deptDtos.stream().map(DepartmentDTO::getId).collect(Collectors.toList());
        return workwxRobotServiceClient.createMsgLimit(userId, req, allDeptIds);
    }

    public BaseResult<WorkWxRobotBatchTaskResp> getWorkWxRobotTaskBatchDetail(Integer salesId, Long batchId) {
        BaseResult<WorkWxRobotBatchTaskResp> result = workwxRobotServiceClient.getWorkWxRobotTaskBatchDetail(salesId, batchId);
        if (!result.isPresent()) {
            return result;
        }
        WorkWxRobotBatchTaskResp data = result.getData();
        UcUsers ucUsers = userClient.getUser(salesId).orElse(new UcUsers());
        Map<Long, List<WorkWxRobotMessageResp>> messageMap = workwxRobotServiceClient.getMessageMapByBatchId(BatchReq.create(Sets.newHashSet(batchId))).orElse(new HashMap<>());
        data.setSalesName(ucUsers.getRealName());
        data.setMessageList(messageMap.getOrDefault(batchId, new ArrayList<>()));
        return BaseResult.success(data);
    }
}

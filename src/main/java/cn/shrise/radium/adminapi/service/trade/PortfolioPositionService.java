package cn.shrise.radium.adminapi.service.trade;

import cn.shrise.radium.adminapi.resp.trade.PortfolioPositionItem;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdPortfolioPosition;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PortfolioPositionService {

    private final TradeClient tradeClient;

    public List<PortfolioPositionItem> getPortfolioPositionList(Integer portfolioId) {
        List<TdPortfolioPosition> positionList = tradeClient.getPortfolioPositionList(portfolioId).orElse(Collections.emptyList());
        return positionList.stream().map(e -> {
            return PortfolioPositionItem.of(e);
        }).collect(Collectors.toList());
    }
}

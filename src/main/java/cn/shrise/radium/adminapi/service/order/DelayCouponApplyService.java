package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.req.DelayCouponApplyReq;
import cn.shrise.radium.orderservice.resp.DelayCouponApplyResp;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DelayCouponApplyService {

    private final OrderClient orderClient;

    public PageResult<List<DelayCouponApplyResp>> getDelayCouponApplyPage(Integer creatorId, Integer companyType, Integer auditStatus, Integer confirmStatus,
                                                                          Integer signStatus, String content, Integer current, Integer size) {
        PageResult<List<DelayCouponApplyResp>> delayCouponApplyPage = orderClient.getDelayCouponApplyPage(creatorId, companyType, auditStatus, confirmStatus, signStatus, content, current, size);
        if (ObjectUtil.isEmpty(delayCouponApplyPage.getData())) {
            return PageResult.empty();
        }
        List<DelayCouponApplyResp> respList = delayCouponApplyPage.getData().stream().filter(Objects::nonNull).peek(o -> {
            o.setCustomerCode(DesensitizeUtil.idToMask(o.getCustomerId()));
        }).collect(Collectors.toList());
        return PageResult.success(respList, delayCouponApplyPage.getPagination());
    }

    public DelayCouponApplyResp applyDelayCoupon(Integer companyType, Integer creatorId, DelayCouponApplyReq req) {
        req.setCreatorId(creatorId);
        req.setCompany(companyType);
        req.setUserId(DesensitizeUtil.maskToId(req.getUserCode()));
        DelayCouponApplyResp couponApplyResp = orderClient.applyDelayCoupon(req).orElseThrow();
        couponApplyResp.setCustomerCode(DesensitizeUtil.idToMask(couponApplyResp.getCustomerId()));
        return couponApplyResp;
    }
}

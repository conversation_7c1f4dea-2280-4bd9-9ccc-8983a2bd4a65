package cn.shrise.radium.adminapi.service.content;

import cn.shrise.radium.adminapi.resp.content.SalesVideoDtoResp;
import cn.shrise.radium.adminapi.resp.content.SsSalesVideoRecordResp;
import cn.shrise.radium.adminapi.resp.user.UserInfoResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsSalesVideoRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SalesVideoService {

    private final ContentClient contentClient;
    private final UserClient userClient;
    
    public PageResult<List<SalesVideoDtoResp>> getSalesVideoByFilter(Integer companyType, List<Integer> creatorIdList,
                                                                     String title, Boolean enabled, Instant startTime,
                                                                     Instant endTime, Integer current, Integer size) {
        PageResult<List<SsSalesVideoRecord>> result = contentClient.getSalesVideoByFilter(companyType, "video_channel",
                creatorIdList, title, enabled, startTime, endTime, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        List<SsSalesVideoRecord> records = result.getData();
        // 销售信息
        Set<Integer> salesIds = records.stream().map(SsSalesVideoRecord::getCreatorId).collect(Collectors.toSet());
        BaseResult<List<UcUsers>> salesResult = userClient.batchGetUserList(salesIds);
        if (salesResult.isFail()) {
            throw new BusinessException(salesResult);
        }
        List<UcUsers> sales = salesResult.getData();
        Map<Integer, UcUsers> salesMap = sales.stream().collect(Collectors.toMap(UcUsers::getId, x -> x));
        // 组装数据
        List<SalesVideoDtoResp> res = records.stream().map(e -> {
            SalesVideoDtoResp resp = new SalesVideoDtoResp();

            SsSalesVideoRecordResp video = new SsSalesVideoRecordResp();
            BeanUtils.copyProperties(e, video);
            resp.setVideoInfo(video);
            if (salesMap.get(e.getCreatorId()) != null) {
                UserInfoResp salesInfoResp = new UserInfoResp();
                BeanUtils.copyProperties(salesMap.get(e.getCreatorId()), salesInfoResp);
                resp.setCreatorInfo(salesInfoResp);
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(res, Pagination.of(current, size, result.getPagination().getTotal()));
    }
}

package cn.shrise.radium.adminapi.service.user;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.resp.call.JustCallRecordResp;
import cn.shrise.radium.userservice.resp.call.TxCallRecordResp;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Slf4j
@Service
@RequiredArgsConstructor
public class CallService {

    private final CommonService commonService;
    private final UserClient userClient;

    public PageResult<List<JustCallRecordResp>> getJustCallRecordList(Integer companyType, String type, Integer userId, String customerNumber,
                                                               String callId, Boolean isCallIn, Integer callStatus, LocalDate startDay,
                                                               LocalDate endDay, String searchContent, Integer current, Integer size) {
        Integer serverId = null;
        Long customerMobileId = null;
        if (Objects.equals(type, "PERSON")) {
            if (ObjectUtil.isNotEmpty(customerNumber)) {
                customerMobileId = commonService.getUserMobileByNumber(companyType, customerNumber);
                if (ObjectUtil.isEmpty(customerMobileId)) {
                    return PageResult.empty();
                }
            } else if (ObjectUtil.isNotEmpty(callId)) {
                customerMobileId = commonService.getJustCallRecordByCallId(callId).getMobileId();
            } else {
                serverId = userId;
            }
        }

        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        if (ObjectUtil.isNotEmpty(startDay)) {
            startTime = startDay.atStartOfDay();
        }
        if (ObjectUtil.isNotEmpty(endDay)) {
            endTime = endDay.plusDays(1).atStartOfDay();
        }
        PageResult<List<JustCallRecordResp>> pageResult = userClient.getJustCallRecordList(companyType, serverId, customerMobileId, isCallIn, callStatus, startTime, endTime, searchContent, current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<JustCallRecordResp> recordResp = pageResult.getData().stream().filter(Objects::nonNull).peek(o -> {
            o.setCustomerCode(DesensitizeUtil.idToMask(o.getCustomerId()));
        }).collect(Collectors.toList());
        return PageResult.success(recordResp, pageResult.getPagination());
    }

    public PageResult<List<TxCallRecordResp>> getTxCallRecordList(Integer companyType, String type, Integer userId, String customerNumber,
                                                                  Boolean isCallIn, Integer callStatus, LocalDate callTime, String searchText,
                                                                  Integer current, Integer size) {
        Integer serverId = null;
        Long mobileId = null;
        if (Objects.equals(type, "PERSON")) {
            if (ObjectUtil.isNotEmpty(customerNumber)) {
                mobileId = commonService.getUserMobileIdByNumber(companyType, customerNumber);
            } else {
                serverId = userId;
            }
        }
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        if (ObjectUtil.isNotEmpty(callTime)) {
            startTime = callTime.atStartOfDay();
            endTime = startTime.plus(1, ChronoUnit.MONTHS);
        }
        PageResult<List<TxCallRecordResp>> pageResult = userClient.getTxCallRecordList(companyType, serverId, mobileId, isCallIn, callStatus, startTime, endTime, searchText, current, size);
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<TxCallRecordResp> recordResp = pageResult.getData().stream().filter(Objects::nonNull).peek(o -> {
            o.setUserCode(DesensitizeUtil.idToMask(o.getUserId()));
        }).collect(Collectors.toList());
        return PageResult.success(recordResp, pageResult.getPagination());
    }
}

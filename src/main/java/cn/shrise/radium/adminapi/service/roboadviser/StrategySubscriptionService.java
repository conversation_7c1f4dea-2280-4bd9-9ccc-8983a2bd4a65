package cn.shrise.radium.adminapi.service.roboadviser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.roboadviser.AmountAdjustRecordResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.constant.IdVerifyStatusConstant;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.constant.ChooseTimeStatusConstant;
import cn.shrise.radium.roboadviserservice.entity.RaCustomerStrategyAmountAdjustRecord;
import cn.shrise.radium.roboadviserservice.entity.RaRequestRecord;
import cn.shrise.radium.roboadviserservice.entity.RaStrategy;
import cn.shrise.radium.roboadviserservice.resp.StrategyChooseTimeRecordResp;
import cn.shrise.radium.roboadviserservice.resp.StrategyInfoResp;
import cn.shrise.radium.roboadviserservice.resp.StrategySubscriptionRecordResp;
import cn.shrise.radium.roboadviserservice.resp.StrategySubscriptionResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcCustomerFollowUpRelation;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcVerifyInfo;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.util.DesensitizedUtil.DesensitizedType.CHINESE_NAME;

@Slf4j
@Service
@RequiredArgsConstructor
public class StrategySubscriptionService {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final UserClient userClient;


    public PageResult<List<StrategySubscriptionRecordResp>> getStrategySubscriptionRecordList(String searchContent, Integer operateType, Long strategyId, Integer current, Integer size) {
        String content = StringUtils.isNotBlank(searchContent) ? String.valueOf(DesensitizeUtil.maskToId(searchContent)) : null;
        PageResult<List<StrategySubscriptionRecordResp>> pageResult = roboAdviserServiceClient.getStrategySubscriptionRecordList(content, operateType, strategyId, current, size);

        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<StrategySubscriptionRecordResp> respList = pageResult.getData();
        List<Integer> userIdList = respList.stream().map(StrategySubscriptionRecordResp::getOperatorId).collect(Collectors.toList());
        userIdList.addAll(respList.stream().map(StrategySubscriptionRecordResp::getUserId).collect(Collectors.toList()));
        List<Long> requestIdList = respList.stream().map(StrategySubscriptionRecordResp::getRequestId).collect(Collectors.toList());

        List<StrategyInfoResp> strategyInfoResps = roboAdviserServiceClient.getAllStrategy(null).orElseThrow();
        Map<Long, String> strategyMap = strategyInfoResps.stream().collect(Collectors.toMap(StrategyInfoResp::getId, StrategyInfoResp::getName));

        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdList)).orElseThrow();
        List<RaRequestRecord> requestRecords = roboAdviserServiceClient.batchRequestRecord(BatchReq.of(requestIdList)).orElseThrow();
        Map<Long, RaRequestRecord> requestRecordMap = requestRecords.stream().collect(Collectors.toMap(RaRequestRecord::getId, Function.identity()));

        List<StrategySubscriptionRecordResp> resps = respList.stream().map(r -> {
            r.setUserNumber(usersMap.getOrDefault(r.getUserId(), new UcUsers()).getNumber());
            r.setNickName(usersMap.getOrDefault(r.getUserId(), new UcUsers()).getNickName());
            if (ObjectUtil.isNotEmpty(r.getOperatorId())) {
                r.setOperator(usersMap.getOrDefault(r.getOperatorId(), new UcUsers()).getRealName());
            } else {
                r.setOperator("系统");
            }
            r.setStrategyName(strategyMap.getOrDefault(r.getStrategyId(), null));
            r.setRequestRecord(requestRecordMap.getOrDefault(r.getRequestId(), null));
            r.setUserCode(DesensitizeUtil.idToMask(r.getUserId()));
            return r;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<StrategyInfoResp>> getStrategyList(Integer chooseTimeStatus, String searchContent, Integer status, Integer current, Integer size) {
        PageResult<List<RaStrategy>> pageResult = roboAdviserServiceClient.getStrategyList(chooseTimeStatus, searchContent, status, current, size);
        List<StrategyInfoResp> respList = new ArrayList<>();
        if (pageResult.isSuccess()) {
            if (ObjectUtil.isEmpty(pageResult.getData())) {
                return PageResult.empty();
            }
            List<String> strategyList = pageResult.getData().stream().map(RaStrategy::getCode).collect(Collectors.toList());
            Map<String, Integer> chooseTimeMap = roboAdviserServiceClient.getStrategyChooseTimeMap(strategyList).orElseThrow();
            pageResult.getData().forEach(raStrategy -> {
                StrategyInfoResp resp = new StrategyInfoResp();
                if (ObjectUtil.isNotNull(raStrategy.getIndexList())) {
                    resp.setIndexList(JSON.parseArray(raStrategy.getIndexList(), String.class));
                }
                BeanUtils.copyProperties(raStrategy, resp);
                if (ObjectUtil.isNotEmpty(raStrategy.getTradeType()) && raStrategy.getTradeType().startsWith("[")) {
                    resp.setTradeType(JSON.parseArray(raStrategy.getTradeType(), Integer.class));
                }
                resp.setIsBackTesting(roboAdviserServiceClient.isBackTesting(resp.getCode()));
                resp.setChooseTimeStatus(chooseTimeMap.getOrDefault(raStrategy.getCode(), ChooseTimeStatusConstant.TRADEING));
                respList.add(resp);
            });
        }
        return PageResult.success(respList, pageResult.getPagination());
    }

    public PageResult<List<StrategySubscriptionResp>> getSubscriptionStrategyList(
            String searchContent, String code, Integer orderType, Boolean isAsc, List<Integer> usrIds, Integer current, Integer size) {
        if (ObjectUtil.isNotEmpty(searchContent) && NumberUtil.isNumber(searchContent)) {
            return PageResult.success();
        }
        if (ObjectUtil.isNotEmpty(searchContent) && DesensitizeUtil.isValidUserCode(searchContent)) {
            searchContent = String.valueOf(DesensitizeUtil.maskToId(searchContent));
        }
        PageResult<List<StrategySubscriptionResp>> pageResult = roboAdviserServiceClient.getStrategySubscriptionList(
                searchContent, code, current, size, null, orderType, isAsc, usrIds);

        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<StrategySubscriptionResp> respList = pageResult.getData();
        Set<Integer> userIdSet = respList.stream().map(StrategySubscriptionResp::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElseThrow();
        Instant now = Instant.now();
        respList.forEach(resp -> {
            resp.setNickName(usersMap.getOrDefault(resp.getUserId(), new UcUsers()).getNickName());
            resp.setUserNumber(usersMap.getOrDefault(resp.getUserId(), new UcUsers()).getNumber());
            resp.setIsExpired(resp.getExpireTime().isBefore(now));
            resp.setUserCode(DesensitizeUtil.idToMask(resp.getUserId()));
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

    public PageResult<List<StrategySubscriptionResp>> getMySubscriptionStrategyList(
            String searchContent, String code, Integer orderType, Boolean isAsc, List<Integer> usrIds,
            List<Integer> salesList, Integer current, Integer size) {
        if (ObjectUtil.isNotEmpty(searchContent) && NumberUtil.isNumber(searchContent)) {
            return PageResult.success();
        }
        if (ObjectUtil.isNotEmpty(searchContent) && DesensitizeUtil.isValidUserCode(searchContent)) {
            searchContent = String.valueOf(DesensitizeUtil.maskToId(searchContent));
        }
        PageResult<List<StrategySubscriptionResp>> pageResult = roboAdviserServiceClient.getStrategySubscriptionList(
                searchContent, code, current, size, null, orderType, isAsc, usrIds);

        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<StrategySubscriptionResp> respList = pageResult.getData();
        Set<Integer> userIdSet = respList.stream().map(StrategySubscriptionResp::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElseThrow();
        List<UcCustomerFollowUpRelation> fuRelations = userClient.getRelationByUserIds(BatchReq.of(userIdSet)).getData()
                .stream().filter(r -> salesList.contains(r.getBelongId())).collect(Collectors.toList());
        Map<Integer, Integer> relationMap = fuRelations.stream().collect(Collectors.toMap(UcCustomerFollowUpRelation::getUserId,
                UcCustomerFollowUpRelation::getBelongId, (v1, v2) -> v2));
        Map<Integer, UcUsers> belongMap = userClient.batchGetUserMap(BatchReq.of(relationMap.values())).orElseThrow();
        Map<Integer, String> deptMap = userClient.getDeptListByUsers(relationMap.values(), 1).getData();
        Instant now = Instant.now();
        Map<Integer, UcVerifyInfo> verifyInfoMap = userClient.getVerificationInfoList(BatchReq.of(userIdSet)).orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(UcVerifyInfo::getUserId, obj -> obj));
        respList.forEach(resp -> {
            resp.setNickName(usersMap.getOrDefault(resp.getUserId(), new UcUsers()).getNickName());
            resp.setUserNumber(usersMap.getOrDefault(resp.getUserId(), new UcUsers()).getNumber());
            UcVerifyInfo verifyInfo = verifyInfoMap.get(resp.getUserId());
            if (verifyInfo != null && ObjectUtil.equals(verifyInfo.getVerifyStatus(), IdVerifyStatusConstant.PASS)) {
                resp.setMaskName(DesensitizedUtil.desensitized(usersMap.getOrDefault(resp.getUserId(), new UcUsers()).getRealName(), CHINESE_NAME));
            } else {
                resp.setMaskName("--");
            }
            resp.setIsExpired(resp.getExpireTime().isBefore(now));
            resp.setUserCode(DesensitizeUtil.idToMask(resp.getUserId()));
            if (relationMap.containsKey(resp.getUserId())) {
                resp.setBelongName(belongMap.getOrDefault(relationMap.get(resp.getUserId()), new UcUsers()).getRealName());
                resp.setDeptName(deptMap.getOrDefault(relationMap.get(resp.getUserId()), ""));
            }
        });
        return PageResult.success(respList, pageResult.getPagination());
    }

    public PageResult<List<StrategyChooseTimeRecordResp>> getStrategyChooseTimeRecord(String strategy, LocalDate createStart, LocalDate createEnd, Integer current, Integer size) {
        PageResult<List<StrategyChooseTimeRecordResp>> pageResult = roboAdviserServiceClient.getStrategyChooseTimeRecord(strategy, createStart, createEnd, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<StrategyChooseTimeRecordResp> resps = pageResult.getData();
        Set<Integer> userIdSet = resps.stream().map(StrategyChooseTimeRecordResp::getOperatorId).collect(Collectors.toSet());
        if (ObjectUtil.isNotEmpty(userIdSet)) {
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElseThrow();
            resps.forEach(resp -> {
                resp.setOperatorName(usersMap.getOrDefault(resp.getOperatorId(), new UcUsers()).getRealName());
            });
        }
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<AmountAdjustRecordResp>> getAmountAdjustRecordList(
            String strategyCode, String userCode, String customerId, Integer adjustType, Instant startTime, Instant endTime,
            Integer current, Integer size) {
        PageResult<List<RaCustomerStrategyAmountAdjustRecord>> pageResult = roboAdviserServiceClient.getAmountAdjustRecordList(
                strategyCode, customerId, adjustType, startTime, endTime, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        RaStrategy raStrategy = roboAdviserServiceClient.getStrategy(strategyCode).getData();
        List<RaCustomerStrategyAmountAdjustRecord> recordList = pageResult.getData();
        Set<Integer> userIdSet = recordList.stream().map(RaCustomerStrategyAmountAdjustRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(userIdSet)) {
            usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElseThrow();
        }
        Map<Integer, UcUsers> finalUsersMap = usersMap;
        List<AmountAdjustRecordResp> respList = recordList.stream().map(record -> {
            AmountAdjustRecordResp resp = BeanUtil.copyProperties(record, AmountAdjustRecordResp.class);
            resp.setOperatorName(finalUsersMap.getOrDefault(record.getOperatorId(), new UcUsers()).getRealName());
            resp.setUserCode(userCode);
            resp.setStrategyName(raStrategy.getName());
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, pageResult.getPagination());
    }
}

package cn.shrise.radium.adminapi.service.dingding;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.DingDingUserInfoResp;
import cn.shrise.radium.adminapi.resp.dingding.DdBindRecord;
import cn.shrise.radium.adminapi.resp.dingding.DdStaffInfoResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.dingdingservice.DingDingClient;
import cn.shrise.radium.dingdingservice.entity.DdStaffInfo;
import cn.shrise.radium.dingdingservice.resp.DdStaffInfoDeptResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.DingDingBindTypeConstant;
import cn.shrise.radium.userservice.entity.UcServerDdBindRecord;
import cn.shrise.radium.userservice.entity.UcServerExt;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DingDingStaffService {

    private final DingDingClient dingDingClient;
    private final UserClient userClient;

    public PageResult<List<DdStaffInfoResp>> getDingDingStaff(Integer companyType, String searchContent, String sexType, Boolean gwShow, Integer employeeStatus, Integer current, Integer size) {
        PageResult<List<DdStaffInfoDeptResp>> ddStaffInfoPage = dingDingClient.getDdStaffInfoPage(companyType, searchContent, sexType, gwShow, employeeStatus, current, size);
        if (!ddStaffInfoPage.isPresent()) {
            return PageResult.empty(current, size);
        }
        List<DdStaffInfoDeptResp> infoPageData = ddStaffInfoPage.getData();

        List<DdStaffInfoResp> infoResps = infoPageData.stream().map(i -> DdStaffInfoResp.builder()
                .id(i.getId())
                .staffId(i.getStaffId())
                .jobNumber(i.getJobNumber())
                .name(i.getName())
                .deptName(i.getDeptName())
                .sexType(i.getSexType())
                .employeeStatus(i.getEmployeeStatus())
                .certificate(i.getCertificate())
                .certificateNo(i.getCertificateNo())
                .gwShow(i.getGwShow())
                .lastWorkDay(i.getLastWorkDay())
                .build()).collect(Collectors.toList());
        return PageResult.success(infoResps, ddStaffInfoPage.getPagination());
    }

    public PageResult<List<DdBindRecord>> getDdBindRecord(Integer operatorType, String searchContent, Integer companyType, Integer current, Integer size) {
        PageResult<List<UcServerDdBindRecord>> ddBindRecord = userClient.getDdBindRecord(operatorType, searchContent, companyType, current, size);
        if (!ddBindRecord.isPresent()) {
            return PageResult.empty(current, size);
        }
        List<UcServerDdBindRecord> recordData = ddBindRecord.getData();
        Set<Long> ddIdSet = recordData.stream().map(UcServerDdBindRecord::getDdId).collect(Collectors.toSet());
        Map<Long, DdStaffInfo> staffInfoMap = dingDingClient.getStaffInfoMap(BatchReq.of(ddIdSet));
        Set<Integer> operatorIdSet = recordData.stream().map(UcServerDdBindRecord::getOperatorId).collect(Collectors.toSet());
        Set<Integer> userIdSet = recordData.stream().map(UcServerDdBindRecord::getUserId).collect(Collectors.toSet());
        Set<Integer> idSet = new HashSet<>();
        idSet.addAll(operatorIdSet);
        idSet.addAll(userIdSet);
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(idSet)).getData();
        List<DdBindRecord> recordList = recordData.stream().map(i -> {
            DdBindRecord.DdBindRecordBuilder bindRecordBuilder = DdBindRecord.builder()
                    .gmtCreate(i.getGmtCreate())
                    .ddId(i.getDdId())
                    .operatorType(i.getOperateType());
            bindRecordBuilder.ddName(staffInfoMap.getOrDefault(i.getDdId(), new DdStaffInfo()).getName());
            bindRecordBuilder.name(i.getOperateType() == DingDingBindTypeConstant.UNBIND ? null : usersMap.getOrDefault(i.getUserId(), new UcUsers()).getRealName());
            bindRecordBuilder.operatorName(usersMap.getOrDefault(i.getOperatorId(), new UcUsers()).getRealName());
            return bindRecordBuilder.build();
        }).collect(Collectors.toList());
        return PageResult.success(recordList, ddBindRecord.getPagination());
    }

    public BaseResult<DingDingUserInfoResp> getDdUserInfo(Integer userId) {
        BaseResult<UcServerExt> serverExtRes = userClient.getServerExtById(userId);
        if (serverExtRes.isFail()) {
            throw new BusinessException(serverExtRes);
        }
        UcServerExt serverExt = serverExtRes.getData();
        if (ObjectUtil.isNull(serverExt.getDdId())) {
            return BaseResult.success(null);
        }
        BaseResult<DdStaffInfo> ddInfoRes = dingDingClient.getStaffInfoById(serverExt.getDdId());
        if (ddInfoRes.isFail()) {
            throw new BusinessException(ddInfoRes);
        }
        DdStaffInfo ddInfo = ddInfoRes.getData();
        DingDingUserInfoResp resp = DingDingUserInfoResp.builder()
                .ddId(ddInfo.getId())
                .ddUserId(ddInfo.getStaffId())
                .name(ddInfo.getName())
                .jobNumber(ddInfo.getJobNumber())
                .build();
        return BaseResult.success(resp);
    }

    public BaseResult<List<DdStaffInfoDeptResp>> getDingDingStaff(Integer companyType, String searchContent) {
        return dingDingClient.getDdStaffInfo(companyType, searchContent);
    }
}

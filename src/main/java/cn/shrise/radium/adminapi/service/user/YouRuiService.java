package cn.shrise.radium.adminapi.service.user;

import cn.shrise.radium.adminapi.resp.user.YrExaminingStockResp;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUserYrExaminingStock;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class YouRuiService {

    private final UserClient userClient;

    public PageResult<List<YrExaminingStockResp>> getYrExaminingStockPage(String searchContent, Integer current, Integer size) {
        PageResult<List<UcUserYrExaminingStock>> pageResult = userClient.getYrExaminingStockPage(searchContent, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        List<UcUserYrExaminingStock> data = pageResult.getData();

        Set<Integer> userSet = data.stream().map(UcUserYrExaminingStock::getUserId).collect(Collectors.toSet());
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userSet)).getData();
        Map<Integer, UcUsers> usersMap = userList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));

        List<YrExaminingStockResp> records = data.stream().map(t -> YrExaminingStockResp.builder()
                .record(t)
                .userName(usersMap.get(t.getUserId()).getRealName())
                .build()).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }
}

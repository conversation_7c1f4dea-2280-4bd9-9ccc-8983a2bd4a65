package cn.shrise.radium.adminapi.service.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.shrise.radium.adminapi.resp.user.CustomerTrackEventRecordResp;
import cn.shrise.radium.adminapi.resp.user.CustomerTrackEventResp;
import cn.shrise.radium.adminapi.resp.user.CustomerTrackEventSummaryResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.OrderRefundStatusConstant;
import cn.shrise.radium.orderservice.constant.OrderStatusConstant;
import cn.shrise.radium.orderservice.entity.UserStatisticsCount;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.CustomerTrackEventSummaryDto;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.lindorm.entity.LdCustomerTrackEvent;
import cn.shrise.radium.userservice.req.CustomerTrackEventSummaryReq;
import cn.shrise.radium.userservice.resp.AppPageInfoResp;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContact;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerTrackService {

    private final UserClient userClient;
    private final WorkwxClient workwxClient;
    private final OrderClient orderClient;
    private final WxClient wxClient;


    public PageResult<List<CustomerTrackEventResp>> getCustomerTrackEvent(Integer wxId, String userCode, Integer companyType, String appId, String pageId, Long startTime, Long endTime, Integer current, Integer size) {
        Integer userId = StringUtils.isNotBlank(userCode) ? DesensitizeUtil.maskToId(userCode) : null;
        PageResult<List<LdCustomerTrackEvent>> result = userClient.getCustomerTrackEvent(wxId, companyType, userId, appId, pageId, null, startTime, endTime, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            return PageResult.success();
        }
        AppPageInfoResp pageInfoResp = userClient.getAppPageInfo(appId).orElseThrow();
        UcTrackApp app = pageInfoResp.getApp();
        Map<String, String> pageMap = pageInfoResp.getPageList().stream().collect(toMap(UcTrackPage::getPageId, UcTrackPage::getName));
        List<CustomerTrackEventResp> resps = result.getData().stream().map(event -> {
            CustomerTrackEventResp resp = new CustomerTrackEventResp();
            BeanUtils.copyProperties(event, resp);
            resp.setAppName(app.getName());
            resp.setPageName(pageMap.getOrDefault(resp.getPageId(), null));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, result.getPagination());
    }

    public PageResult<List<CustomerTrackEventSummaryResp>> getCustomerTrackEventSummary(CustomerTrackEventSummaryReq req) {
        AppPageInfoResp pageInfoResp = userClient.getAppPageInfo(req.getAppId()).orElseThrow();
        if (ObjectUtil.isEmpty(pageInfoResp.getApp()) || ObjectUtil.isEmpty(pageInfoResp.getPageList())) {
            return PageResult.success();
        }
        UcTrackApp app = pageInfoResp.getApp();
        req.setIsOpen(app.getIsOpen());
        PageResult<List<CustomerTrackEventSummaryDto>> pageResult = userClient.getCustomerTrackEventSummary(req);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (!pageResult.isPresent()) {
            return PageResult.success();
        }
        Map<String, String> pageMap = pageInfoResp.getPageList().stream().collect(toMap(UcTrackPage::getPageId, UcTrackPage::getName));
        List<CustomerTrackEventSummaryDto> trackData = pageResult.getData();
        Set<Integer> wxIdSet = new HashSet<>();
        Set<Integer> userIdSet = new HashSet<>();
        for (CustomerTrackEventSummaryDto trackDatum : trackData) {
            UcCustomerTrackEventSummary summary = trackDatum.getTrackEventSummary();
            if (ObjectUtil.isNotEmpty(summary)) {
                if (ObjectUtil.isNotEmpty(summary.getWxId()) && !summary.getWxId().equals(-1)) {
                    wxIdSet.add(summary.getWxId());
                }
                if (ObjectUtil.isNotEmpty(summary.getUserId()) && !summary.getUserId().equals(-1)) {
                    userIdSet.add(summary.getUserId());
                }
            }
        }
        BaseResult<List<UserStatisticsCount>> orderStatisticsResult = orderClient.getOrderCount(new BatchReq<>(userIdSet), OrderStatusConstant.PASSED.getValue());
        BaseResult<List<UserStatisticsCount>> refundStatisticsResult = orderClient.getRefundOrderCount(new BatchReq<>(userIdSet), OrderRefundStatusConstant.PASSED.getValue());
        Map<Integer, Long> orderStatisticsMap;
        Map<Integer, Long> refundStatisticsMap;
        if (orderStatisticsResult.isFail()) {
            log.warn("getOrderCount fail: {}", orderStatisticsResult);
            orderStatisticsMap = new HashMap<>();
        } else {
            orderStatisticsMap = orderStatisticsResult.getData().stream().collect(toMap(UserStatisticsCount::getUserId, UserStatisticsCount::getCount));
        }
        if (refundStatisticsResult.isFail()) {
            log.warn("getRefundOrderCount fail: {}", refundStatisticsResult);
            refundStatisticsMap = new HashMap<>();
        } else {
            refundStatisticsMap = refundStatisticsResult.getData().stream().collect(toMap(UserStatisticsCount::getUserId, UserStatisticsCount::getCount));
        }
        List<Integer> salesIdList = req.getSalesIdList();
        salesIdList.addAll(trackData.stream().filter(e -> ObjectUtil.isNotEmpty(e.getTrackEventSummary())).map(e -> e.getTrackEventSummary().getLastSalesId()).collect(Collectors.toList()));
        BaseResult<List<UcUsers>> usersResult = userClient.batchGetUserList(BatchReq.create(salesIdList));
        Map<Integer, String> salesUserMap;
        if (ObjectUtil.isNotEmpty(usersResult.getData())) {
            salesUserMap = usersResult.getData().stream().filter(e -> ObjectUtil.isNotEmpty(e.getRealName())).collect(toMap(UcUsers::getId, UcUsers::getRealName));
        } else {
            salesUserMap = new HashMap<>();
        }
        Map<Integer, Boolean> isTradedMap;
        Map<Integer, UcUsers> usersMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(userIdSet)) {
            List<UcCustomerExt> customerExtList = userClient.batchUserExt(BatchReq.create(userIdSet)).orElse(null);
            usersMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).getData();
            if (ObjectUtil.isNotEmpty(customerExtList)) {
                isTradedMap = customerExtList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getIsTraded())).collect(toMap(UcCustomerExt::getId, UcCustomerExt::getIsTraded));
            } else {
                isTradedMap = new HashMap<>();
            }
        } else {
            isTradedMap = new HashMap<>();
        }
        BaseResult<List<UcWxExt>> wxExtResult = new BaseResult<>();
        BaseResult<List<UcWxExt>> userWxExtResult = new BaseResult<>();
        if (ObjectUtil.isNotEmpty(wxIdSet)) {
            wxExtResult = wxClient.batchGetUserWxExt(null, new ArrayList<>(wxIdSet));
        }
        if (ObjectUtil.isNotEmpty(userIdSet)) {
            userWxExtResult = wxClient.batchGetUserWxExt(new ArrayList<>(userIdSet), null);
        }
        Map<Integer, UcWxExt> wxExtMap;
        Map<Integer, UcWxExt> userWxExtMap;
        Map<String, String> externalUserMap = null;
        if (ObjectUtil.isNotEmpty(wxExtResult.getData())) {
            wxExtMap = wxExtResult.getData().stream().filter(e -> ObjectUtil.isNotEmpty(e.getId())).collect(toMap(UcWxExt::getId, e -> e));
            if (ObjectUtil.isNotEmpty(req.getAccountType())) {
                Set<String> unionIdSet = wxExtResult.getData().stream().filter(e -> ObjectUtil.isNotEmpty(e.getUnionId())).map(UcWxExt::getUnionId).collect(toSet());
                if (ObjectUtil.isNotEmpty(unionIdSet)) {
                    List<NpWorkWxContact> contactList = workwxClient.batchContactInfo(req.getAccountType(), BatchReq.create(unionIdSet)).orElseThrow();
                    externalUserMap = contactList.stream().collect(toMap(NpWorkWxContact::getUnionId, NpWorkWxContact::getExternalUserId));
                }
            }
        } else {
            wxExtMap = new HashMap<>();
        }
        if (ObjectUtil.isNotEmpty(userWxExtResult.getData())) {
            userWxExtMap = userWxExtResult.getData().stream().filter(e -> ObjectUtil.isNotEmpty(e.getUserId())).collect(toMap(UcWxExt::getUserId, e -> e));
            if (ObjectUtil.isNotEmpty(req.getAccountType())) {
                Set<String> unionIdSet = userWxExtResult.getData().stream().filter(e -> ObjectUtil.isNotEmpty(e.getUnionId())).map(UcWxExt::getUnionId).collect(toSet());
                if (ObjectUtil.isNotEmpty(unionIdSet)) {
                    List<NpWorkWxContact> contactList = workwxClient.batchContactInfo(req.getAccountType(), BatchReq.create(unionIdSet)).orElseThrow();
                    if (ObjectUtil.isNotEmpty(externalUserMap)) {
                        externalUserMap.putAll(contactList.stream().collect(toMap(NpWorkWxContact::getUnionId, NpWorkWxContact::getExternalUserId)));
                    } else {
                        externalUserMap = contactList.stream().collect(toMap(NpWorkWxContact::getUnionId, NpWorkWxContact::getExternalUserId));
                    }
                }
            }
        } else {
            userWxExtMap = new HashMap<>();
        }

        Map<String, String> externalMap = externalUserMap;
        Map<Integer, UcUsers> finalUsersMap = usersMap;
        List<CustomerTrackEventSummaryResp> resps = trackData.stream().map(track -> {
            Integer userId = track.getTrackEventSummary().getUserId();
            if (ObjectUtil.equal(userId, -1)) {
                userId = null;
            }
            Integer wxId = track.getTrackEventSummary().getWxId();
            final Integer salesId = track.getTrackEventSummary().getSalesId();
            CustomerTrackEventSummaryResp resp = CustomerTrackEventSummaryResp.builder()
                    .id(track.getTrackEventSummary().getId())
                    .userId(userId)
                    .userCode(DesensitizeUtil.idToMask(userId))
                    .wxId(wxId)
                    .pageId(track.getTrackEventSummary().getPageId())
                    .page(pageMap.getOrDefault(track.getTrackEventSummary().getPageId(), null))
                    .category(track.getTrackEventSummary().getCategory())
                    .accessTime(track.getTrackEventSummary().getAccessTime())
                    .onLinePage(pageMap.getOrDefault(track.getOnLine(), null))
                    .build();

            if (ObjectUtil.isNotEmpty(userId) && CollectionUtil.isNotEmpty(finalUsersMap)) {
                resp.setSource(finalUsersMap.getOrDefault(userId, new UcUsers()).getSource());
                resp.setSourceProductType(finalUsersMap.getOrDefault(userId, new UcUsers()).getSourceProductType());
            }
            if (ObjectUtil.isNotEmpty(salesUserMap)) {
                resp.setSaleName(salesUserMap.getOrDefault(salesId, salesUserMap.getOrDefault(track.getTrackEventSummary().getLastSalesId(), null)));
            }
            if (ObjectUtil.isNotEmpty(track.getTrackEventSummary().getLastPageId())) {
                resp.setPageId(track.getTrackEventSummary().getLastPageId());
                resp.setPage(pageMap.getOrDefault(track.getTrackEventSummary().getLastPageId(), null));
            }
            if (ObjectUtil.isNotEmpty(externalMap)) {
                if (!ObjectUtil.equal(wxId, -1) && ObjectUtil.isAllNotEmpty(wxExtMap, wxExtMap.get(wxId))) {
                    resp.setExternalUserId(externalMap.getOrDefault(wxExtMap.getOrDefault(wxId, new UcWxExt()).getUnionId(), null));
                }
                if (!ObjectUtil.equal(userId, -1) && ObjectUtil.isAllNotEmpty(userWxExtMap, userWxExtMap.get(userId))) {
                    resp.setExternalUserId(externalMap.getOrDefault(userWxExtMap.getOrDefault(userId, new UcWxExt()).getUnionId(), null));
                }
            }
            if (!wxId.equals(-1) && ObjectUtil.isNotEmpty(wxExtMap) && ObjectUtil.isNotEmpty(wxExtMap.get(wxId))) {
                resp.setHeadImg(wxExtMap.getOrDefault(wxId, new UcWxExt()).getHeadImgUrl());
                resp.setNickName(wxExtMap.getOrDefault(wxId, new UcWxExt()).getNickname());
            }
            if (ObjectUtil.isNotEmpty(userId)) {
                resp.setOrderCount(orderStatisticsMap.getOrDefault(userId, 0L));
                resp.setRefundCount(refundStatisticsMap.getOrDefault(userId, 0L));
                resp.setIsTraded(isTradedMap.getOrDefault(userId, false));
                if (!userId.equals(-1) && ObjectUtil.isNotEmpty(userWxExtMap) && ObjectUtil.isNotEmpty(userWxExtMap.get(userId))) {
                    resp.setWxId(userWxExtMap.getOrDefault(userId, new UcWxExt()).getId());
                    resp.setHeadImg(userWxExtMap.getOrDefault(userId, new UcWxExt()).getHeadImgUrl());
                    resp.setNickName(userWxExtMap.getOrDefault(userId, new UcWxExt()).getNickname());
                }
            }
            return resp;
        }).collect(toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<CustomerTrackEventRecordResp>> getCustomerTrackEventRecord(Integer companyType, String appId, Long startTime, Long endTime, Integer current, Integer size) {
        PageResult<List<LdCustomerTrackEvent>> pageResult = userClient.filterCustomerTrackEventPage(companyType, appId, null, startTime, endTime, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }

        List<LdCustomerTrackEvent> trackData = pageResult.getData();
        AppPageInfoResp pageInfoResp = userClient.getAppPageInfo(appId).orElseThrow();
        Map<String, String> pageMap = pageInfoResp.getPageList().stream().collect(toMap(UcTrackPage::getPageId, UcTrackPage::getName));
        final List<Integer> wxIdList = trackData.stream().map(e -> e.getWxId()).collect(Collectors.toList());
        final List<Integer> userIdList = trackData.stream().map(e -> e.getUserId()).collect(toList());
        List<UcWxExt> ucWxExts = wxClient.batchGetUserWxExtByUserOrWxId(userIdList, wxIdList).orElseThrow();
        Map<Integer, String> wxNickNameMap = ucWxExts.stream().filter(e -> ObjectUtil.isNotEmpty(e.getNickname())).collect(toMap(UcWxExt::getId, UcWxExt::getNickname));
        Map<Integer, String> userWxNickNameMap = ucWxExts.stream().filter(e -> ObjectUtil.isNotEmpty(e.getUserId())).collect(toMap(UcWxExt::getUserId, UcWxExt::getNickname));
        BaseResult<List<UserStatisticsCount>> orderStatisticsResult = orderClient.getOrderCount(new BatchReq<>(userIdList), OrderStatusConstant.PASSED.getValue());
        BaseResult<List<UserStatisticsCount>> refundStatisticsResult = orderClient.getRefundOrderCount(new BatchReq<>(userIdList), OrderRefundStatusConstant.PASSED.getValue());
        Map<Integer, Long> orderStatisticsMap;
        Map<Integer, Long> refundStatisticsMap;
        if (orderStatisticsResult.isFail()) {
            log.warn("getOrderCount fail: {}", orderStatisticsResult);
            orderStatisticsMap = new HashMap<>();
        } else {
            orderStatisticsMap = orderStatisticsResult.getData().stream().collect(toMap(UserStatisticsCount::getUserId, UserStatisticsCount::getCount));
        }
        if (refundStatisticsResult.isFail()) {
            log.warn("getRefundOrderCount fail: {}", refundStatisticsResult);
            refundStatisticsMap = new HashMap<>();
        } else {
            refundStatisticsMap = refundStatisticsResult.getData().stream().collect(toMap(UserStatisticsCount::getUserId, UserStatisticsCount::getCount));
        }
        BaseResult<List<UcUsers>> usersResult = userClient.batchGetUserList(BatchReq.create(userIdList));
        Map<Integer, String> userMap;
        if (usersResult.isFail()) {
            log.warn("getSalesUser fail: {}", usersResult);
            userMap = new HashMap<>();
        } else {
            userMap = usersResult.getData().stream().collect(toMap(UcUsers::getId, UcUsers::getNickName));
        }
        List<CustomerTrackEventRecordResp> resps = trackData.stream().map(track -> {
            CustomerTrackEventRecordResp resp = CustomerTrackEventRecordResp.builder()
                    .gmtCreate(track.getGmtCreate())
                    .wxId(track.getWxId())
                    .wxNickName(wxNickNameMap.getOrDefault(track.getWxId(), null))
                    .userId(track.getUserId())
                    .userCode(DesensitizeUtil.idToMask(track.getUserId()))
                    .appId(track.getAppId())
                    .pageId(track.getPageId())
                    .pageName(pageMap.getOrDefault(track.getPageId(), null))
                    .category(track.getCategory())
                    .action(track.getAction())
                    .label(track.getLabel())
                    .url(track.getUrl())
                    .ip(track.getIp())
                    .userAgent(track.getUserAgent())
                    .build();
            if (ObjectUtil.isNotEmpty(resp.getUserId())) {
                Integer userId = resp.getUserId();
                resp.setNickName(userMap.getOrDefault(userId, null));
                resp.setOrderCount(orderStatisticsMap.getOrDefault(userId, 0L));
                resp.setRefundCount(refundStatisticsMap.getOrDefault(userId, 0L));
                resp.setWxNickName(userWxNickNameMap.getOrDefault(userId, wxNickNameMap.getOrDefault(track.getWxId(), null)));
            }
            UserAgent userAgent = UserAgentUtil.parse(track.getUserAgent());
            if (ObjectUtil.isNotEmpty(userAgent)) {
                resp.setBrowser(userAgent.getBrowser().getName());
                resp.setOs(userAgent.getOs().getName());
                resp.setPlatform(userAgent.getPlatform().getName());
            }

            return resp;
        }).collect(toList());
        Pagination pagination = pageResult.getPagination();
        if (pagination.getTotal() > 5000) {
            pagination.setTotal(5000L);
        }
        return PageResult.success(resps, pagination);
    }

    public PageResult<List<CustomerTrackEventResp>> getWorkWxCustomerTrackEvent(Integer wxId, Integer userId, Integer companyType, Integer salesId, String appId, String pageId, Long startTime, Long endTime, Integer current, Integer size) {

        if (ObjectUtil.isEmpty(salesId)) {
            // 企业未绑定销售，不返回任何数据
            return PageResult.empty();
        }
        if (ObjectUtil.isAllEmpty(wxId, userId)) {
            return PageResult.empty();
        }
        AppPageInfoResp pageInfoResp = userClient.getAppPageInfo(appId).orElseThrow();
        UcTrackApp app = pageInfoResp.getApp();
        PageResult<List<LdCustomerTrackEvent>> result = null;
        if (app.getIsOpen()) {
            // 公开应用 所有记录
            result = userClient.getCustomerTrackEvent(wxId, companyType, userId, appId, pageId, null, startTime, endTime, current, size);
        } else {
            // 非公开应用 查看当前销售链接记录
            result = userClient.getCustomerTrackEvent(wxId, companyType, userId, appId, pageId, salesId, startTime, endTime, current, size);
        }
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            return PageResult.success();
        }
        Map<String, String> pageMap = pageInfoResp.getPageList().stream().collect(toMap(UcTrackPage::getPageId, UcTrackPage::getName));
        List<CustomerTrackEventResp> resps = result.getData().stream().map(event -> {
            CustomerTrackEventResp resp = new CustomerTrackEventResp();
            BeanUtils.copyProperties(event, resp);
            resp.setPageName(pageMap.getOrDefault(resp.getPageId(), null));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, result.getPagination());
    }
}

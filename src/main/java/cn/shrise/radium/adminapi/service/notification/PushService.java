package cn.shrise.radium.adminapi.service.notification;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.resp.PushRecordItem;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.notificationservice.NotificationClient;
import cn.shrise.radium.notificationservice.constant.PushTypeEnum;
import cn.shrise.radium.notificationservice.entity.NtBatchPushInfo;
import cn.shrise.radium.notificationservice.lindorm.entity.LdPushRecord;
import cn.shrise.radium.notificationservice.resp.BatchPushRecordInfoResp;
import cn.shrise.radium.notificationservice.resp.PushRecordInfoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PushService {

    private final NotificationClient notificationClient;
    private final UserClient userClient;

    public PageResult<List<PushRecordItem>> getPushRecordList(Integer companyType, Integer userId, Integer productType,
                                                              Integer pushType, Integer msgType, Integer current, Integer size) {
        PageResult<List<LdPushRecord>> pageResult = notificationClient.getPushRecordList(companyType, userId, productType,
                pushType, msgType, null, null, current, size);
        if (pageResult.isFail()) {
            return PageResult.empty(current, size);
        }
        final List<LdPushRecord> records = pageResult.getData();
        if (ObjectUtils.isEmpty(records)) {
            return PageResult.empty(current, size);
        }
        Set<Integer> userIdSet = records.stream().map(LdPushRecord::getUserId).collect(Collectors.toSet());
        BaseResult<Map<Integer, UcUsers>> batchGetUserResult = userClient.batchGetUserMap(BatchReq.create(userIdSet));
        Map<Integer, UcUsers> usersMap;
        if (batchGetUserResult.isFail()) {
            usersMap = new HashMap<>();
            log.error("batch get user error code: {}, msg: {}", batchGetUserResult.getCode(), batchGetUserResult.getMsg());
        } else {
            usersMap = batchGetUserResult.getData();
        }
        List<PushRecordItem> pushInfoList = records.stream().map(e -> {
            Integer uid = e.getUserId();
            UserInfo userInfo = UserInfo.of(usersMap.get(uid));
            return PushRecordItem.builder()
                    .companyType(e.getCompanyType())
                    .productType(e.getProductType())
                    .pushType(e.getPushType())
                    .receiver(e.getReceiver())
                    .msgType(e.getMsgType())
                    .msgBody(e.getMsgBody())
                    .userId(uid)
                    .userInfo(userInfo)
                    .batchId(e.getBatchId())
                    .handleId(e.getHandleId())
                    .gmtCreate(e.getGmtCreate())
                    .gmtModified(e.getGmtModified())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(pushInfoList, pageResult.getPagination());
    }

    public PageResult<List<BatchPushRecordInfoResp>> getNtBatchPushInfoList(Integer companyType, Integer pushType, Integer status, LocalDate createStart, LocalDate createEnd, LocalDate completeStart, LocalDate completeEnd, Integer current, Integer size) {
        PageResult<List<NtBatchPushInfo>> ntBatchPushInfoList = notificationClient.getNtBatchPushInfoList(companyType, pushType, status, createStart, createEnd, completeStart, completeEnd, current, size);
        if (ntBatchPushInfoList.isFail()) {
            return PageResult.empty(current, size);
        }
        List<NtBatchPushInfo> ntBatchPushInfoListData = ntBatchPushInfoList.getData();
        if (ObjectUtils.isEmpty(ntBatchPushInfoListData)) {
            return PageResult.empty(current, size);
        }
        List<BatchPushRecordInfoResp> batchPushRecordInfoResps = new ArrayList<>();
        ntBatchPushInfoListData.forEach(e -> {
            BatchPushRecordInfoResp batchPushRecordInfoResp = new BatchPushRecordInfoResp();
            BeanUtil.copyProperties(e, batchPushRecordInfoResp);
            batchPushRecordInfoResps.add(batchPushRecordInfoResp);
        });
        return PageResult.success(batchPushRecordInfoResps, ntBatchPushInfoList.getPagination());
    }

    public PageResult<List<PushRecordInfoResp>> getMessagePushInfo(Long batchId, String searchContent, Integer current, Integer size, Integer companyType) {
        Integer userId = null;
        if (ObjectUtil.isNotEmpty(searchContent) && !NumberUtil.isNumber(searchContent)) {
            return PageResult.empty(current, size);
        }
        if (PhoneUtil.isMobile(searchContent)) {
            BaseResult<UcUsers> result = userClient.searchUser(searchContent, companyType);
            if (result.isPresent()) {
                userId = result.getData().getId();
            }
        }
        PageResult<List<LdPushRecord>> templateDetail = notificationClient.getTemplateDetail(batchId, searchContent, userId, current, size);
        if (templateDetail.isFail()) {
            return PageResult.empty(current, size);
        }
        List<LdPushRecord> templateDetailData = templateDetail.getData();
        if (ObjectUtils.isEmpty(templateDetailData)) {
            return PageResult.empty(current, size);
        }
        Map<String, String> resultMap = new HashMap<>(size);
        Set<String> serialNumberSet = templateDetailData.stream().map(LdPushRecord::getSerialNumber)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (!ObjectUtils.isEmpty(serialNumberSet)) {
            Integer pushType = templateDetailData.get(0).getPushType();
            if (ObjectUtil.isNotEmpty(pushType) && ObjectUtil.equals(PushTypeEnum.PTT_APP.getCode(), pushType)) {
                resultMap = notificationClient.batchGetEmasMap(BatchReq.create(serialNumberSet)).getData();
            } else if (ObjectUtil.isNotEmpty(pushType) && ObjectUtil.equals(PushTypeEnum.PTT_PC.getCode(), pushType)) {
                resultMap = notificationClient.batchGetPcMap(BatchReq.create(serialNumberSet)).getData();
            } else if (ObjectUtil.isNotEmpty(pushType) && ObjectUtil.equals(PushTypeEnum.PTT_PHONE.getCode(), pushType)) {
                resultMap = notificationClient.batchGetCallMap(BatchReq.create(serialNumberSet)).getData();
            } else if (ObjectUtil.isNotEmpty(pushType) && ObjectUtil.equals(PushTypeEnum.PTT_TEMPLATE.getCode(), pushType)) {
                resultMap = notificationClient.batchGetWxMap(BatchReq.create(serialNumberSet)).getData();
            } else if (ObjectUtil.isNotEmpty(pushType) && ObjectUtil.equals(PushTypeEnum.PTT_SMS.getCode(), pushType)) {
                resultMap = notificationClient.batchGetSmsMap(BatchReq.create(serialNumberSet)).getData();
            }
        }
        List<PushRecordInfoResp> pushRecordInfoResps = new ArrayList<>();
        Map<String, String> finalResultMap = resultMap;
        templateDetailData.forEach(e -> {
            PushRecordInfoResp pushRecordInfoResp = new PushRecordInfoResp();
            BeanUtil.copyProperties(e, pushRecordInfoResp);
            if (CollectionUtil.isNotEmpty(finalResultMap) && finalResultMap.containsKey(e.getSerialNumber())) {
                pushRecordInfoResp.setResult(finalResultMap.get(e.getSerialNumber()));
            }
            pushRecordInfoResps.add(pushRecordInfoResp);
        });
        return PageResult.success(pushRecordInfoResps, templateDetail.getPagination());
    }
}

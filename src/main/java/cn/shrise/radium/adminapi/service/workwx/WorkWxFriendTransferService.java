package cn.shrise.radium.adminapi.service.workwx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.adminapi.resp.workWx.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.UserDTO;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.constant.WwxFriendTransferSendStatusConst;
import cn.shrise.radium.workwxservice.constant.WwxFriendTransferStatusConst;
import cn.shrise.radium.workwxservice.constant.WwxFriendTransferTakeoverStatusConst;
import cn.shrise.radium.workwxservice.constant.WwxGlobalErrorCodeMsgEnum;
import cn.shrise.radium.workwxservice.entity.*;
import cn.shrise.radium.workwxservice.req.FilterFriendTransferReq;
import cn.shrise.radium.workwxservice.req.FilterTransferFriendReq;
import cn.shrise.radium.workwxservice.req.GetRelationRecordReq;
import cn.shrise.radium.workwxservice.resp.ContactUserRelationDto;
import cn.shrise.radium.workwxservice.resp.WwxFriendTakeoverResp;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkWxFriendTransferService {

    private final WorkwxClient workwxClient;
    private final UserClient userClient;


    public FilterFriendTransferReq initTransferApplyResp(Set<Integer> filterUserSet, FilterFriendTransferReq req) {

        if (ObjectUtil.isNotEmpty(req.getDepartmentIdList()) || ObjectUtil.isNotEmpty(req.getOperatorIdList())) {
            if (ObjectUtil.isNotEmpty(req.getDepartmentIdList())) {
                // 根据部门筛选
                List<Integer> userIdList = userClient.getUserIdByDepartmentList(req.getDepartmentIdList()).getData();
                if (ObjectUtil.isNotEmpty(userIdList)) {
                    filterUserSet = SetUtils.intersection(filterUserSet, new HashSet<>(userIdList));
                }
            }
            if (ObjectUtil.isNotEmpty(req.getOperatorIdList())) {
                filterUserSet = SetUtils.intersection(filterUserSet, new HashSet<>(req.getOperatorIdList()));
            }
            req.setOperatorIdList(new ArrayList<>(filterUserSet));
        }
        return req;
    }

    public FilterFriendTransferReq initTransferAuditResp(FilterFriendTransferReq req) {
        if (ObjectUtil.isNotEmpty(req.getDepartmentIdList()) || ObjectUtil.isNotEmpty(req.getOperatorIdList())) {
            Set<Integer> filterOperatorSet = new HashSet<>();
            if (ObjectUtil.isNotEmpty(req.getDepartmentIdList())) {
                // 根据部门筛选
                List<Integer> userIdList = userClient.getUserIdByDepartmentList(req.getDepartmentIdList()).getData();
                if (ObjectUtil.isNotEmpty(userIdList)) {
                    filterOperatorSet = ObjectUtil.isNotEmpty(filterOperatorSet) ? SetUtils.intersection(filterOperatorSet, new HashSet<>(userIdList)) : new HashSet<>(userIdList);
                }
            }
            if (ObjectUtil.isNotEmpty(req.getOperatorIdList())) {
                filterOperatorSet = ObjectUtil.isNotEmpty(filterOperatorSet) ? SetUtils.intersection(filterOperatorSet, new HashSet<>(req.getOperatorIdList())) : new HashSet<>(req.getOperatorIdList());
            }
            req.setOperatorIdList(new ArrayList<>(filterOperatorSet));
        }
        return req;
    }

    public PageResult<List<WwxFriendTransferResp>> list(FilterFriendTransferReq transferReq) {

        // 过滤员工查看范围
        UcDepartment department = userClient.getDepartmentByUserId(transferReq.getUserId()).orElseThrow();
        List<UserDTO> userDTOS = userClient.getAllUsersByDepartmentId(department.getId(), null, transferReq.getCompanyType()).orElseThrow();
        List<Integer> resultList = userDTOS.stream().map(UserDTO::getId).collect(Collectors.toList());
        Set<Integer> filterUserSet = new HashSet<>(resultList);
        FilterFriendTransferReq req;
        if (Objects.equals(transferReq.getIsFilterAudit(), true)) {
            req = this.initTransferAuditResp(transferReq);
        } else {
            req = this.initTransferApplyResp(filterUserSet, transferReq);
        }

        PageResult<List<NpWwxFriendTransferBatch>> result = workwxClient.filterFriendTransfer(req);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.empty();
        }
        List<NpWwxFriendTransferBatch> friendTransferBatches = result.getData();
        List<Integer> batchIdList = friendTransferBatches.stream().map(NpWwxFriendTransferBatch::getId).collect(Collectors.toList());
        Set<String> saleAccountSet = friendTransferBatches.stream().map(NpWwxFriendTransferBatch::getHandoverUserId).collect(Collectors.toSet());
        saleAccountSet.addAll(friendTransferBatches.stream().map(NpWwxFriendTransferBatch::getTakeoverUserId).collect(Collectors.toSet()));
        List<NpWorkWxUser> workWxUsers = workwxClient.getAccountInfoList(saleAccountSet);
        Map<String, NpWorkWxUser> wxUserMap = workWxUsers.stream().filter(e -> e.getAccountType().equals(req.getAccountType())).collect(Collectors.toMap(NpWorkWxUser::getWxAccount, Function.identity()));
        List<Integer> salesIdList = workWxUsers.stream().map(NpWorkWxUser::getBelongId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        salesIdList.addAll(friendTransferBatches.stream().map(NpWwxFriendTransferBatch::getOperatorId).collect(Collectors.toList()));
        salesIdList.addAll(friendTransferBatches.stream().map(NpWwxFriendTransferBatch::getAuditorId).collect(Collectors.toList()));
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(salesIdList)).orElseThrow(null);
        Map<Integer, Long> successCountMap = workwxClient.getSendCountMap(BatchReq.of(batchIdList), WwxFriendTransferSendStatusConst.SUCCESS).orElseThrow(null);
        Map<Integer, Long> failCountMap = workwxClient.getSendCountMap(BatchReq.of(batchIdList), WwxFriendTransferSendStatusConst.FAIL).orElseThrow(null);
        Map<Integer, Long> successTransferCountMap = workwxClient.getTransferCountMap(BatchReq.of(batchIdList), WwxFriendTransferTakeoverStatusConst.FINISHED).orElseThrow(null);
        Map<Integer, Long> allTransferCountMap = workwxClient.getTransferCountMap(BatchReq.of(batchIdList), null).orElseThrow(null);
        Map<Integer, String> deptMap = userClient.getDeptListByUsers(friendTransferBatches.stream().map(NpWwxFriendTransferBatch::getOperatorId).collect(Collectors.toList()), 1).getData();
        List<WwxFriendTransferResp> resps = friendTransferBatches.stream().map(e -> {
            WwxFriendTransferResp resp = new WwxFriendTransferResp();
            BeanUtils.copyProperties(e, resp);
            if (ObjectUtil.isNotEmpty(wxUserMap)) {
                resp.setHandoverUserName(wxUserMap.getOrDefault(e.getHandoverUserId(), new NpWorkWxUser()).getName());
                resp.setTakeoverUserName(wxUserMap.getOrDefault(e.getTakeoverUserId(), new NpWorkWxUser()).getName());
            }
            if (ObjectUtil.isNotEmpty(usersMap)) {
                resp.setOperatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName());
                resp.setAuditorName(usersMap.getOrDefault(e.getAuditorId(), new UcUsers()).getRealName());
            }
            if (ObjectUtil.isNotEmpty(successCountMap)) {
                resp.setSuccessCount(successCountMap.get(e.getId()));
            }
            if (ObjectUtil.isNotEmpty(failCountMap)) {
                resp.setFailCount(failCountMap.get(e.getId()));
            }
            if (ObjectUtil.isAllNotEmpty(allTransferCountMap, successTransferCountMap)) {
                resp.setSuccessTransferCount(successTransferCountMap.getOrDefault(e.getId(), 0L));
                resp.setFailTransferCount(allTransferCountMap.getOrDefault(e.getId(), 0L) - successTransferCountMap.getOrDefault(e.getId(), 0L));
            }
            if (ObjectUtil.isNotEmpty(e.getBelongId())) {
                if (ObjectUtil.equal(e.getBelongId(), e.getOperatorId())) {
                    resp.setIsOneself(true);
                } else {
                    resp.setIsOneself(false);
                }
            }
            resp.setExUserList(JSONUtil.toList(e.getExUserList(), String.class));
            resp.setDepartment(deptMap.get(e.getOperatorId()));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, result.getPagination());
    }

    public BaseResult<WwxFriendTransferInfoResp> getFriendTransferInfo(Integer id) {
        BaseResult<NpWwxFriendTransferBatch> result = workwxClient.getFriendTransferInfo(id);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        NpWwxFriendTransferBatch data = result.getData();
        Set<String> accountSet = new HashSet<>();
        accountSet.add(data.getTakeoverUserId());
        accountSet.add(data.getHandoverUserId());
        List<NpWorkWxUser> workWxUsers = workwxClient.getAccountInfoList(accountSet);
        WwxFriendTakeoverResp recordDetail = workwxClient.getTakeoverCountResp(data.getId()).orElse(new WwxFriendTakeoverResp());
        List<Integer> salesIdList = workWxUsers.stream().map(NpWorkWxUser::getBelongId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toCollection(ArrayList::new));
        Map<String, NpWorkWxUser> wxAccountMap = workWxUsers.stream().filter(e -> e.getAccountType().equals(data.getAccountType())).collect(Collectors.toMap(NpWorkWxUser::getWxAccount, Function.identity()));
        salesIdList.add(data.getOperatorId());
        salesIdList.add(data.getAuditorId());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(salesIdList)).orElseThrow();

        WwxFriendTransferInfoResp resp = new WwxFriendTransferInfoResp();
        resp.setIsOneself(false);
        BeanUtils.copyProperties(data, resp);
        if (ObjectUtil.isNotEmpty(data.getTagList())) {
            List<String> tagIdList = JSON.parseArray(data.getTagList(), String.class);
            List<NpWorkWxContactTag> tagList = workwxClient.getWorkWxContactTagList(data.getCompanyType(), data.getAccountType(), BatchReq.of(tagIdList)).orElseThrow();
            if (ObjectUtil.isNotEmpty(tagList)) {
                List<String> tagNameList = tagList.stream().map(NpWorkWxContactTag::getName).collect(Collectors.toList());
                resp.setTagList(tagNameList);
            }
        }
        BaseResult<Long> relationCountResult = workwxClient.getFriendRelationCount(wxAccountMap.get(data.getHandoverUserId()).getAccountType(), data.getHandoverUserId(), true, true);
        if (relationCountResult.isFail()) {
            throw new BusinessException(relationCountResult);
        }
        resp.setHandoverUserName(wxAccountMap.getOrDefault(data.getHandoverUserId(), new NpWorkWxUser()).getName());
        resp.setTakeoverUserName(wxAccountMap.getOrDefault(data.getTakeoverUserId(), new NpWorkWxUser()).getName());
        resp.setHandoverUserMobile(AESUtil.decrypt(wxAccountMap.getOrDefault(data.getHandoverUserId(), new NpWorkWxUser()).getMobile()));
        resp.setTakeoverUserMobile(AESUtil.decrypt(wxAccountMap.getOrDefault(data.getTakeoverUserId(), new NpWorkWxUser()).getMobile()));
        resp.setOperatorName(usersMap.getOrDefault(data.getOperatorId(), new UcUsers()).getRealName());
        resp.setAuditorName(usersMap.getOrDefault(data.getAuditorId(), new UcUsers()).getRealName());
        resp.setExUserList(JSONUtil.toList(data.getExUserList(), String.class));
        if (ObjectUtil.isNotEmpty(data.getBelongId())) {
            if (ObjectUtil.equal(data.getOperatorId(), data.getBelongId())) {
                resp.setIsOneself(true);
            }
        }
        if (ObjectUtil.isNotEmpty(recordDetail)) {
            resp.setSuccessCount(recordDetail.getSendSuccessCount());
            resp.setFailCount(recordDetail.getSendFailCount());
            resp.setSuccessTransferCount(recordDetail.getSuccessCount());
            resp.setFailTransferCount(recordDetail.getFailCount());
            resp.setCustomerCount(recordDetail.getAllCount());
        }
        resp.setCustomerCount(ObjectUtil.isNotEmpty(relationCountResult.getData()) ? relationCountResult.getData() : null);
        return BaseResult.success(resp);
    }

    public PageResult<List<FriendTransferResp>> filterFriend(FilterTransferFriendReq req) {

        Map<String, Boolean> tradedUnionMap = null;
        List<String> externalUserIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(req.getIsBuy())) {
            List<NpWorkWxContact> contactList = workwxClient.getListByWxAccount(req.getAccountType(), req.getWxAccount());
            if (ObjectUtil.isEmpty(contactList)) {
                return PageResult.empty();
            }
            List<String> unionList = contactList.stream().map(NpWorkWxContact::getUnionId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(unionList)) {
                tradedUnionMap = userClient.batchGetTradedUnionMap(req.getCompanyType(), true, BatchReq.of(unionList)).orElseThrow();
                Set<String> unionSet = tradedUnionMap.keySet();
                if (req.getIsBuy()) {
                    contactList.forEach(e -> {
                        if (unionSet.contains(e.getUnionId())) {
                            externalUserIdList.add(e.getExternalUserId());
                        }
                    });
                } else {
                    contactList.forEach(e -> {
                        if (!unionSet.contains(e.getUnionId())) {
                            externalUserIdList.add(e.getExternalUserId());
                        }
                    });
                }
            } else {
                if (req.getIsBuy()) {
                    // 没有unionId，并且需要查询已购买，返回
                    return PageResult.empty();
                }
            }
            if (ObjectUtil.isEmpty(externalUserIdList)) {
                // 没有满足是否购买的客户
                return PageResult.empty();
            }
        }
        req.setExternalUserIdList(externalUserIdList);
        PageResult<List<ContactUserRelationDto>> pageResult = workwxClient.filterFriend(req);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<ContactUserRelationDto> resultData = pageResult.getData();
        List<String> unionIdList = resultData.stream().map(ContactUserRelationDto::getUnionId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        List<String> externalUserIds = resultData.stream().map(e -> e.getRelation().getExternalUserId()).collect(Collectors.toList());
        Map<String, List<NpWorkWxContactTag>> contactTagMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(externalUserIds)) {
            contactTagMap = workwxClient.batchWorkWxContactTag(req.getAccountType(), req.getWxAccount(), BatchReq.of(externalUserIds)).orElseThrow();
        }
        if (ObjectUtil.isEmpty(req.getIsBuy()) && ObjectUtil.isNotEmpty(unionIdList)) {
            tradedUnionMap = userClient.batchGetTradedUnionMap(req.getCompanyType(), null, BatchReq.of(unionIdList)).orElseThrow();
        }
        // 查询处理中状态的客户
        Set<String> processUserSet = new HashSet<>();
        BaseResult<List<NpWwxFriendTransferBatch>> friendTransferBaseResult = workwxClient.getFriendTransferList(req.getCompanyType(), req.getAccountType(), Arrays.asList(WwxFriendTransferStatusConst.WAITING_AUDIT, WwxFriendTransferStatusConst.PROCESSING));
        if (friendTransferBaseResult.isFail()) {
            throw new BusinessException(friendTransferBaseResult);
        }
        if (ObjectUtil.isNotEmpty(friendTransferBaseResult.getData())) {
            friendTransferBaseResult.getData().forEach(e -> {
                if (ObjectUtil.isNotEmpty(e.getExUserList())) {
                    List<String> userList = JSON.parseArray(e.getExUserList(), String.class);
                    processUserSet.addAll(userList);
                }
            });
        }

        Map<String, Boolean> tradedMap = tradedUnionMap;
        Map<String, List<NpWorkWxContactTag>> tagMap = contactTagMap;
        List<FriendTransferResp> resps = resultData.stream().map(e -> {
            FriendTransferResp resp = FriendTransferResp.builder()
                    .exUserId(e.getRelation().getExternalUserId())
                    .nickname(e.getRelation().getRemark())
                    .firstAddTime(e.getAddTime())
                    .lastAddTime(e.getRelation().getAddTime())
                    .lastReplyTime(e.getRelation().getLastReplyTime())
                    .isBuy(false)
                    .isChoose(true)
                    .build();
            if (ObjectUtil.isNotEmpty(processUserSet) && processUserSet.contains(resp.getExUserId())) {
                resp.setIsChoose(false);
            }
            if (CollectionUtil.isNotEmpty(tradedMap) && tradedMap.containsKey(e.getUnionId())) {
                resp.setIsBuy(tradedMap.get(e.getUnionId()));
            }
            if (ObjectUtil.isNotEmpty(tagMap) && tagMap.containsKey(e.getRelation().getExternalUserId())) {
                List<String> tags = tagMap.get(e.getRelation().getExternalUserId()).stream().map(NpWorkWxContactTag::getName).collect(Collectors.toList());
                resp.setTagList(tags);
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public PageResult<List<WwxFriendTransferRecordResp>> getFriendTransferRecordByBatchId(Integer batchId, Integer current, Integer size) {
        PageResult<List<NpWwxFriendTransferRecord>> pageResult = workwxClient.getFriendTransferRecordByBatchId(batchId, current, size);
        if (pageResult.isFail()) {
            throw new BusinessException(pageResult);
        }
        if (ObjectUtil.isEmpty(pageResult.getData())) {
            return PageResult.empty();
        }
        List<NpWwxFriendTransferRecord> records = pageResult.getData();
        NpWwxFriendTransferRecord record = records.get(0);
        List<String> exUserIdList = records.stream().map(NpWwxFriendTransferRecord::getExUserId).collect(Collectors.toList());
        List<String> accountList = Arrays.asList(record.getHandoverUserId(), record.getTakeoverUserId());
        List<NpWorkWxUser> workWxUsers = workwxClient.getAccountInfoList(new HashSet<>(accountList));
        Map<String, NpWorkWxUser> wxUserMap = workWxUsers.stream().filter(e -> e.getAccountType().equals(record.getAccountType())).collect(Collectors.toMap(NpWorkWxUser::getWxAccount, Function.identity()));
        List<NpWorkWxContact> contactInfoList = workwxClient.getContactInfoList(new HashSet<>(exUserIdList));
        Map<String, String> userNameMap = contactInfoList.stream().filter(e -> e.getAccountType().equals(record.getAccountType())).collect(Collectors.toMap(NpWorkWxContact::getExternalUserId, NpWorkWxContact::getName));
        List<WwxFriendTransferRecordResp> resps = records.stream().map(e ->
                        WwxFriendTransferRecordResp.builder()
                                .exUserId(e.getExUserId())
                                .nickname(userNameMap.get(e.getExUserId()))
                                .handoverUserId(e.getHandoverUserId())
                                .handoverUserName(wxUserMap.getOrDefault(e.getHandoverUserId(), new NpWorkWxUser()).getName())
                                .takeoverUserId(e.getTakeoverUserId())
                                .takeoverUserName(wxUserMap.getOrDefault(e.getTakeoverUserId(), new NpWorkWxUser()).getName())
                                .completeTime(e.getCompleteTime())
                                .status(e.getSendStatus())
                                .description(WwxGlobalErrorCodeMsgEnum.findMsgByCode(e.getErrorCode()))
                                .takeoverStatus(e.getTakeoverStatus())
                                .build())
                .collect(Collectors.toList());
        return PageResult.success(resps, pageResult.getPagination());
    }

    public BaseResult<List<WwxFriendTransferCustomerResp>> getFriendTransferCustomerList(Integer batchId) {
        BaseResult<NpWwxFriendTransferBatch> result = workwxClient.getFriendTransferInfo(batchId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return BaseResult.success(Collections.emptyList());
        }
        NpWwxFriendTransferBatch batch = result.getData();
        String exUsers = batch.getExUserList();
        if (ObjectUtil.isEmpty(exUsers)) {
            return BaseResult.success(Collections.emptyList());
        }
        List<String> exUserList = JSON.parseArray(exUsers, String.class);
        List<NpWorkWxContact> contactInfoList = workwxClient.getContactInfoList(new HashSet<>(exUserList));
        Map<String, String> userNameMap = contactInfoList.stream().filter(e -> e.getAccountType().equals(batch.getAccountType())).collect(Collectors.toMap(NpWorkWxContact::getExternalUserId, NpWorkWxContact::getName));
        List<WwxFriendTransferCustomerResp> resps = exUserList.stream().map(e -> {
            return WwxFriendTransferCustomerResp.builder()
                    .exUserId(e)
                    .nickname(userNameMap.get(e))
                    .build();
        }).collect(Collectors.toList());
        return BaseResult.success(resps);
    }
}

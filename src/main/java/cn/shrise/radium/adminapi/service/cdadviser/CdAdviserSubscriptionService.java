package cn.shrise.radium.adminapi.service.cdadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.cdadviserservice.CdAdviserServiceClient;
import cn.shrise.radium.cdadviserservice.req.OpenCdAdviserSubscriptionReq;
import cn.shrise.radium.cdadviserservice.resp.CdAdviserSubscriptionInfoResp;
import cn.shrise.radium.cdadviserservice.resp.CdAdviserSubscriptionRecordListResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.shrise.radium.cdadviserservice.constant.ErrorConstant.CUSTOMER_NOT_EXISTED;

@Service
@RequiredArgsConstructor
public class CdAdviserSubscriptionService {

    private final UserClient userClient;
    private final CdAdviserServiceClient cdAdviserServiceClient;

    public BaseResult<Void> openCdAdviserSubscription(Integer operatorId, OpenCdAdviserSubscriptionReq req) {
        int userId;
        try {
            userId = DesensitizeUtil.maskToId(req.getUserCode());
        }catch (BusinessException e) {
            throw new BusinessException(CUSTOMER_NOT_EXISTED);
        }
        //判断用户是否存在
        BaseResult<UcUsers> user = userClient.getUser(userId);
        if (!user.isPresent() || ObjectUtil.isEmpty(user.getData())) {
            throw new BusinessException(CUSTOMER_NOT_EXISTED);
        }
        req.setUserId(userId);
        return cdAdviserServiceClient.openCdAdviserSubscription(operatorId, req);
    }

    public BaseResult<Void> cancelCdAdviserSubscription(Integer operatorId, Long id) {
        return cdAdviserServiceClient.cancelCdAdviserSubscription(operatorId, id);
    }


    public PageResult<List<CdAdviserSubscriptionInfoResp>> getCdAdviserCustomerList(Integer current, Integer size) {
        return cdAdviserServiceClient.getCdAdviserCustomerList(current, size);
    }


    public PageResult<List<CdAdviserSubscriptionRecordListResp>> getCdAdviserSubscriptionRecordList(Integer current, Integer size) {
        PageResult<List<CdAdviserSubscriptionRecordListResp>> pageResult = cdAdviserServiceClient.getCdAdviserSubscriptionRecordList(current, size);
        List<CdAdviserSubscriptionRecordListResp> resultList = pageResult.getData();
        Set<Integer> operatorIds = resultList.stream().map(CdAdviserSubscriptionRecordListResp::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(operatorIds)).getData();
        resultList.forEach(i -> i.setOperateName(usersMap.getOrDefault(i.getOperatorId(), new UcUsers()).getRealName()));
        return PageResult.success(resultList, pageResult.getPagination());
    }
}

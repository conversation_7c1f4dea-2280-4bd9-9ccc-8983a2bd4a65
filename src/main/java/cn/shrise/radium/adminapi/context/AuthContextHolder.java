package cn.shrise.radium.adminapi.context;

import cn.shrise.radium.common.constant.HeaderConstant;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * replace use @RequestHeader
 */
@Deprecated
public class AuthContextHolder {

    @Nullable
    public static String getHeader(String name) {
        final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes)requestAttributes).getRequest();
            return request.getHeader(name);
        }
        return null;
    }

    /**
     * @deprecated use @RequestHeader(USER_ID)
     * @return
     */
    @Deprecated
    @Nullable
    public static Integer getUserId() {
        final String userId = getHeader(HeaderConstant.USER_ID);
        if (ObjectUtils.isEmpty(userId)) {
            return null;
        }
        return Integer.valueOf(userId);
    }

    /**
     * @deprecated use @RequestHeader(COMPANY_TYPE)
     * @return
     */
    @Deprecated
    @Nullable
    public static Integer getCompanyType() {
        final String companyType = getHeader(HeaderConstant.COMPANY_TYPE);
        if (ObjectUtils.isEmpty(companyType)) {
            return null;
        }
        return Integer.valueOf(companyType);
    }

}

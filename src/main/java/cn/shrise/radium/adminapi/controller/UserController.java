package cn.shrise.radium.adminapi.controller;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.entity.*;
import cn.shrise.radium.adminapi.req.GetAccountActivityListReq;
import cn.shrise.radium.adminapi.resp.DepartmentNameOrderVolumeResp;
import cn.shrise.radium.adminapi.resp.user.AccountActivityItem;
import cn.shrise.radium.adminapi.resp.user.AccountActivityResp;
import cn.shrise.radium.adminapi.resp.user.UserDetail;
import cn.shrise.radium.adminapi.resp.workWx.WwxDetailResp;
import cn.shrise.radium.adminapi.service.OrderService;
import cn.shrise.radium.adminapi.service.UserService;
import cn.shrise.radium.adminapi.service.workwx.WorkWxService;
import cn.shrise.radium.authservice.req.AdminVerifyReq;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.properties.LoginProperties;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.constant.IdentityTypeConstant;
import cn.shrise.radium.contentservice.entity.SsCustomerEvaluation;
import cn.shrise.radium.contentservice.enums.RiskLevelType;
import cn.shrise.radium.orderservice.resp.SalesOrderDetailResp;
import cn.shrise.radium.orderservice.resp.SalesRefundDetailResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.UserTypeConstant;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.req.*;
import cn.shrise.radium.userservice.resp.*;
import cn.shrise.radium.workwxservice.resp.ResourceDetailResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.HeaderConstant.*;
import static cn.shrise.radium.userservice.constant.UserTypeConstant.CUSTOMER;
import static cn.shrise.radium.userservice.constant.UserTypeConstant.STAFF;

@Api
@Slf4j
@RestController
@RequestMapping("users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final WorkWxService workWxService;
    private final OrderService orderService;
    private final UserClient userClient;
    private final ContentClient contentClient;
    private final LoginProperties loginProperties;

    @ApiOperation("获取用户信息")
    @GetMapping("/user-detail")
    public BaseResult<User> getUser(@RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
                                    @RequestParam @ApiParam("用户code") String userCode,
                                    @RequestParam(required = false, defaultValue = "false") @ApiParam("微信信息") Boolean needWxInfo,
                                    @RequestParam(required = false, defaultValue = "false") @ApiParam("评测信息") Boolean needEvaluationInfo) {
        int userId = DesensitizeUtil.maskToId(userCode);
        UserDetail ucUser = userService.searchUser(userId, companyType, UserTypeConstant.CUSTOMER);
        if (ObjectUtil.isEmpty(ucUser) || !ObjectUtil.equal(ucUser.getUserType(), UserTypeConstant.CUSTOMER)) {
            return BaseResult.success(new User());
        }
        User user = new User();
        BeanUtils.copyProperties(ucUser, user);
        if (needWxInfo) {
            UcWxExt wxInfo = userClient.getWxExtInfo(userId).getData();
            if (wxInfo != null) {
                WxExtInfo wxExtInfo = new WxExtInfo();
                BeanUtils.copyProperties(wxInfo, wxExtInfo);
                wxExtInfo.setUserCode(DesensitizeUtil.idToMask(wxInfo.getUserId()));
                user.setWxInfo(wxExtInfo);
            }
        }

        if (needEvaluationInfo) {
            SsCustomerEvaluation evalInfo = contentClient.getUserEvaluation(userId).getData();
            if (evalInfo != null) {
                CustomerEvaluation customerEvaluation = new CustomerEvaluation();
                BeanUtils.copyProperties(evalInfo, customerEvaluation);
                customerEvaluation.setUserCode(DesensitizeUtil.idToMask(evalInfo.getUserId()));
                if (ObjectUtil.isNotEmpty(evalInfo.getIdentityNumber()) && evalInfo.getIdentityType().equals(IdentityTypeConstant.ID_CARD)) {
                    String idCardNumber = AESUtil.decrypt(evalInfo.getIdentityNumber());
                    int idCardAge = IdcardUtil.getAgeByIdCard(idCardNumber);
                    customerEvaluation.setAge(idCardAge);
                }
                if (evalInfo.getSurveyScore() != null) {
                    customerEvaluation.setRiskLevel(RiskLevelType.getLevelByScore(evalInfo.getSurveyScore()));
                }
                user.setEvaluationInfo(customerEvaluation);
            }
        }
        return BaseResult.success(user);
    }

    @ApiOperation("获取用户基础信息")
    @GetMapping("/me-base")
    public BaseResult<UserBaseInfoResp> getUserBase(@RequestHeader(USER_ID) @ApiIgnore Integer userId) {
        UcUsers ucUser = userService.getUser(userId);
        UserBaseInfoResp resp = UserBaseInfoResp.builder()
                .id(ucUser.getId())
                .companyType(ucUser.getCompanyType())
                .realName(ucUser.getRealName())
                .build();
        return BaseResult.success(resp);
    }

    @ApiOperation("获取当前用户信息")
    @GetMapping("/me")
    public BaseResult<User> getCurrentUser() {
        final Integer userId = AuthContextHolder.getUserId();
        UcUsers ucUser = userService.getUser(userId);
        User user = new User();
        BeanUtils.copyProperties(ucUser, user);
        if (ObjectUtil.isNotEmpty(ucUser.getBirthday())) {
            user.setBirthday(ucUser.getBirthday().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        }
        if (Objects.equals(user.getUserType(), STAFF)) {
            user.setRole(userClient.getRoleInfo(userId).getData());
            user.setStaffExt(userClient.getStaffExt(userId).getData());
            UcServerExt serverExt = userClient.getServerExtById(userId).getData();
            if (serverExt != null) {
                user.setWorkNumber(serverExt.getWorkNumber());
            }
            UcServer2department server2department = userClient.getServer2department(userId).getData();
            user.setIsManager(ObjectUtil.isNotEmpty(server2department) && server2department.getIsManager());
            UcMainStaff staff = userClient.getStaffByUserId(user.getCompanyType(), userId).getData();
            if (ObjectUtil.isNotEmpty(server2department)) {
                user.setStaffId(staff.getId());
            }
            if (staff.getPwTime().plus(loginProperties.getPasswordExpire(), ChronoUnit.DAYS).isBefore(Instant.now())) {
                user.setResetPassword(true);
            }
        } else if (Objects.equals(user.getUserType(), CUSTOMER)) {
            user.setUserCode(DesensitizeUtil.idToMask(user.getId()));
        }
        populateStaffRoleList(user);
        return BaseResult.success(user);
    }

    private void populateStaffRoleList(User user) {
        List<UcStaffRole> staffRoleList = userClient.getStaffRoleListByUser(user.getId()).getData();
        if (ObjectUtil.isNotEmpty(staffRoleList)) {
            List<User.StaffRoleResp> roleResps = staffRoleList.stream().map(e -> User.StaffRoleResp.builder()
                            .name(e.getName())
                            .number(e.getNumber())
                            .build())
                    .collect(Collectors.toList());
            user.setStaffRoleList(roleResps);
        }
    }

    @ApiOperation("获取用户完整信息")
    @GetMapping("all-detail")
    BaseResult<WwxDetailResp> getUserAllDetail(
            @ApiIgnore @RequestHeader(value = USER_ID, required = false) Integer userId,
            @ApiIgnore @RequestHeader(WORK_WX_ID) Long id) {
        WwxDetailResp resp = new WwxDetailResp();
        WwxDetailResp.WorkWxInfo workWxInfo = workWxService.getWwxDetail(id);
        if (ObjectUtil.isNotEmpty(workWxInfo)) {
            resp.setWorkWxInfo(workWxInfo);
        }
        if (ObjectUtil.isEmpty(userId)) {
            return BaseResult.success(resp);
        }
        WwxDetailResp.UserInfo userInfo = new WwxDetailResp.UserInfo();
        UcUsers ucUser = userService.getUser(userId);
        if (ObjectUtil.isNotEmpty(ucUser)) {
            BeanUtils.copyProperties(ucUser, userInfo);
            resp.setUserInfo(userInfo);
        }
        return BaseResult.success(resp);
    }

    @ApiOperation("获取用户销量排行")
    @PostMapping("/order/volume")
    public BaseResult<DepartmentNameOrderVolumeResp> getUserOrderVolume(
            @RequestParam(required = true) @ApiParam("部门ID") Integer deptId,
            @RequestParam(required = true) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = true) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "10") @ApiParam("显示数量") Integer size,
            @RequestParam(defaultValue = "1") @ApiParam("前端后端") Integer category) {
        DepartmentNameOrderVolumeResp userOrderVolume = orderService.getUserOrderVolume(deptId, startTime, endTime, size, category);
        return BaseResult.success(userOrderVolume);
    }

    @ApiOperation("分页获取用户信息")
    @GetMapping("by/deptId")
    public PageResult<List<UcUsers>> getUsersByDeptId(
            @RequestParam @ApiParam("部门id") List<Integer> deptIds,
            @RequestParam(required = true) @ApiParam("时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return userService.getUsersByDeptId(deptIds, flagTime, current, size);
    }

    @ApiOperation("获取销售订单明细")
    @GetMapping("sales/order/detail")
    public PageResult<List<SalesOrderDetailResp>> getSalesOrderDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return orderService.getSalesOrderDetail(salesId, flagTime, periodType, orderLevel, dzId, current, size);
    }

    @ApiOperation("获取销售退款订单明细")
    @GetMapping("sales/refund/detail")
    public PageResult<List<SalesRefundDetailResp>> getSalesRefundDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return orderService.getSalesRefundDetail(salesId, flagTime, periodType, orderLevel, dzId, current, size);
    }

    @ApiOperation("获取销售有效退款订单明细")
    @GetMapping("sales/valid/refund/detail")
    public PageResult<List<SalesRefundDetailResp>> getSalesValidRefundDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return orderService.getSalesValidRefundDetail(salesId, flagTime, periodType, orderLevel, dzId, current, size);
    }

    @ApiOperation("获取销售资源明细")
    @GetMapping("sales/detail")
    public PageResult<List<ResourceDetailResp>> getSalesResourceDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return orderService.getSalesResourceDetail(salesId, flagTime, periodType, dzId, current, size);
    }

    @ApiOperation("获取所有员工信息")
    @GetMapping("all")
    public BaseResult<List<UcUsers>> getAllUsers() {
        return userService.getAllUser();
    }

    @ApiOperation("获取用户登录详情")
    @GetMapping("account-activity/list")
    PageResult<List<AccountActivityResp>> getAccountActivityByUserId(
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("产品类型") List<Integer> productTypeList,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return userService.getAccountActivityByUserId(userId, productTypeList, current, size);
    }

    @ApiOperation("获取用户最后一条登录记录")
    @GetMapping("account-activity/latest")
    BaseResult<AccountActivityResp> getLatestAccountActivity(
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("产品类型") List<Integer> productTypeList) {
        return userService.getLatestAccountActivity(userId, productTypeList);
    }

    @ApiOperation("获取登录详情")
    @PostMapping("account-activity/detail")
    PageResult<List<AccountActivityItem>> getAccountActivityList(@RequestBody @Valid GetAccountActivityListReq req) {
        return userService.getAccountActivityList(req);
    }

    @ApiOperation("校验审核信息")
    @PostMapping("audit")
    public BaseResult<UcUsers> auditUser(@RequestBody @Valid AdminVerifyReq req) {
        return userService.auditUser(req);
    }

    @ApiOperation("拨打电话")
    @PostMapping("makingCall")
    public BaseResult<Void> makingCall(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid MakingCallReq req) {
        req.setUserId(userId);
        req.setCompanyType(companyType);
        return userClient.makingCall(req);
    }

//    @ApiOperation("通话记录")
//    @GetMapping("call/log/list")
//    public PageResult<List<CallLogResp>> findCallLogList(
//            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
//            @RequestParam(required = false) @ApiParam("通话类型") Boolean callType,
//            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
//            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
//            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
//            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
//            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
//            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
//        return userService.findCallLogByFilter(companyType, null, callType, callStatus,
//                startTime, endTime, null, null, null, searchContent, current, size);
//    }
//
//    @ApiOperation("我的通话记录")
//    @GetMapping("call/log/my/list")
//    public PageResult<List<CallLogResp>> findMyCallLogList(
//            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
//            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
//            @RequestParam(required = false) @ApiParam("通话类型") Boolean callType,
//            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
//            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
//            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
//            @RequestParam(required = false) @ApiParam("搜索手机号") String mobile,
//            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
//            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
//        return userService.findCallLogByFilter(companyType, userId, callType, callStatus, startTime, endTime, mobile,
//                null, null, null, current, size);
//    }
//
//    @ApiOperation("客户通话记录")
//    @GetMapping("call/log/customer/list")
//    public PageResult<List<CallLogResp>> findCustomerCallLogList(
//            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
//            @RequestParam(required = false) @ApiParam("客户number") String number,
//            @RequestParam(required = false) @ApiParam("callId") String callId,
//            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
//            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
//        return userService.findCallLogByFilter(companyType, null, null, null, null,
//                null, null, number, callId, null, current, size);
//    }

    @ApiOperation("获取所属角色所有员工")
    @GetMapping("role/userList")
    public BaseResult<List<UcUsers>> getRoleUserList(@RequestParam @ApiParam("角色id") Long roleId) {
        return userService.getRoleUserList(roleId);
    }

    @ApiOperation("获取员工角色信息")
    @GetMapping("role/info")
    public BaseResult<UcRole> getRoleInfo(@RequestParam @ApiParam("用户id") Integer userId) {
        return userClient.getRoleInfo(userId);
    }

    @PostMapping("createOrUpdateSubUser")
    @ApiOperation("创建/更新子账号")
    public BaseResult<Void> createOrUpdateSubUser(
            @RequestBody @Valid CreateOrUpdateSubUserReq req) {
        req.setCompanyType(AuthContextHolder.getCompanyType());
        return userClient.createOrUpdateSubUser(req);
    }

    @PostMapping("createOrUpdateStaffExt")
    @ApiOperation("创建/更新员工信息")
    public BaseResult<Void> createOrUpdateStaffExt(
            @RequestBody @Valid CreateOrUpdateStaffExtReq req) {
        return userClient.createOrUpdateStaffExt(req);
    }

    @GetMapping("findSubServerByFilter")
    @ApiOperation("获取子账号列表")
    PageResult<List<SubServerResp>> findSubServerByFilter(
            @RequestParam(required = false) @ApiParam("账号状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("部门id") Integer departmentId,
            @RequestParam(required = false) @ApiParam("角色id") Long roleId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return userClient.findSubServerByFilter(AuthContextHolder.getCompanyType(), enabled, departmentId, roleId,
                searchContent, current, size);
    }

    @GetMapping("findSalesDeptChatVisibleByFilter")
    @ApiOperation("获取聊天记录子账号可见部门列表")
    public PageResult<List<SalesDeptChatVisibleResp>> findSalesDeptChatVisibleByFilter(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return userClient.findSalesDeptChatVisibleByFilter(companyType, searchContent, current, size);
    }

    @PostMapping("deptChat/visible")
    @ApiOperation("修改聊天记录子账号可见范围")
    public BaseResult<Void> updateDeptChatVisibleConfig(@RequestBody @Valid UpdateDeptChatVisibleConfigReq req) {
        return userClient.updateDeptChatVisibleConfig(req);
    }

    @GetMapping("findDeptInfoChatVisibleBySales")
    @ApiOperation("获取销售聊天记录可见部门")
    public BaseResult<List<DepartmentInfoResp>> findDeptInfoChatVisibleBySales(
            @RequestHeader(USER_ID) @ApiIgnore Integer salesId) {
        return userClient.findDeptInfoChatVisibleBySales(salesId);
    }

    @GetMapping("findMainServerByFilter")
    @ApiOperation("获取主账号列表")
    public PageResult<List<MainServerResp>> findMainServerByFilter(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) @ApiParam("账号状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return userClient.findMainServerByFilter(companyType, enabled, searchContent, current, size);
    }

    @PostMapping("syncStaffInfo")
    @ApiOperation("同步主账号信息")
    BaseResult<Void> syncStaffInfo() {
        return userClient.syncStaffInfo(AuthContextHolder.getCompanyType());
    }

    @PostMapping("createMainStaff")
    @ApiOperation("创建主账号信息")
    public BaseResult<String> createMainStaff(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestBody @Valid CreateMainStaffReq req) {
        req.setCompanyType(companyType);
        return userClient.createMainStaff(req);
    }

    @PostMapping("quit")
    @ApiOperation("离职/取消离职")
    public BaseResult<Void> quit(
            @RequestParam @ApiParam("主账号ID") Integer id,
            @RequestParam @ApiParam("离职/取消离职") Boolean enabled
    ) {
        return userClient.quit(id, enabled);
    }

    @GetMapping("search")
    @ApiOperation("通过id搜索用户")
    public BaseResult<UserDetail> searchUser(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("用户code") String content) {
        int userId = DesensitizeUtil.maskToId(content);
        UserDetail users = userService.searchUser(userId, companyType, CUSTOMER);
        return BaseResult.success(users);
    }

    @PostMapping("userBindDd")
    @ApiOperation("绑定钉钉账号")
    public BaseResult<Void> userBindDd(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("钉钉ID") Long ddId,
            @RequestParam @ApiParam("钉钉姓名") String name
    ) {
        return userClient.userBindDd(userId, ddId, name, companyType);
    }

    @ApiOperation("通过number获取客户信息")
    @GetMapping("customerInfo")
    public BaseResult<CustomerInfo> getCustomerInfoByNumber(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("用户number") String userNumber) {
        CustomerInfo customerInfo = userService.getCustomerInfo(userNumber, companyType);
        return BaseResult.success(customerInfo);
    }


    @PostMapping("edit/{id}/name")
    @ApiOperation("修改主账号员工姓名")
    public BaseResult<String> editName(
            @PathVariable Integer id,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("员工姓名") String name) {
        return userClient.editName(id, companyType, name);
    }

    @PostMapping("edit/{id}/mobile")
    @ApiOperation("修改主账号手机号")
    public BaseResult<String> editMobile(
            @PathVariable Integer id,
            @RequestParam @ApiParam("手机号") String mobile) {
        return userClient.editMobile(id, mobile);
    }
}

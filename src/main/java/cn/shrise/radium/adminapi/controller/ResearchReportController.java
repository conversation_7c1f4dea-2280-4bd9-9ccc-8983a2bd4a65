package cn.shrise.radium.adminapi.controller;

import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.AnalystInfoResp;
import cn.shrise.radium.adminapi.service.ResearchReportService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.orderservice.entity.ArticleSeriesOperatorRelation;
import cn.shrise.radium.orderservice.entity.ArticleTag;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import cn.shrise.radium.orderservice.req.AnalystReq;
import cn.shrise.radium.orderservice.req.ArticleSeriesReq;
import cn.shrise.radium.orderservice.req.ManagerReq;
import cn.shrise.radium.orderservice.resp.ArticleSeriesResp;
import cn.shrise.radium.orderservice.resp.ArticleTagResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 研报管理
 *
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("researchReport")
@RequiredArgsConstructor
public class ResearchReportController {

    private final OrderClient orderClient;
    private final ResearchReportService researchReportService;

    @GetMapping("getArticleSeries")
    @ApiOperation("获取栏目列表")
    public PageResult<List<ArticleSeriesResp>> getCustomerService(
            @RequestParam(required = false) @ApiParam("产品栏目类型") Integer seriesType,
            @RequestParam @ApiParam("栏目类型") Integer type,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size) {
        Integer companyType = AuthContextHolder.getCompanyType();
        return orderClient.getArticleSeries(companyType, seriesType, type, current, size);
    }

    @PostMapping("creatOrUpdateArticleSeries")
    @ApiOperation("创建/编辑研报栏目")
    public BaseResult creatOrUpdateArticle(@Validated @RequestBody ArticleSeriesReq req){
        req.setCompanyType(AuthContextHolder.getCompanyType());
        return orderClient.creatOrUpdateArticleSeries(req);
    }

    @PostMapping("addAnalyst")
    @ApiOperation("为栏目添加投顾老师")
    public BaseResult addAnalyst(@Validated @RequestBody AnalystReq req) {
        return researchReportService.addAnalyst(req);
    }

    @GetMapping("getAnalystList")
    @ApiOperation("获取栏目对映老师")
    public BaseResult<List<AnalystInfoResp>> getAnalystInfoByChannelId(
            @RequestParam @ApiParam("栏目ID") Integer seriesId) {
        return researchReportService.getAnalystInfoBySeriesIdIds(seriesId);
    }

    @PostMapping("addManager")
    @ApiOperation("为栏目配置处理人")
    public BaseResult addResearchReportManager(@Validated @RequestBody ManagerReq req) {
        return researchReportService.addManager(req);
    }

    @GetMapping("getManagerRelation")
    @ApiOperation("获取栏目对映处理人关系")
    public BaseResult<List<ArticleSeriesOperatorRelation>> getResearchReportManagerRelations(
            @RequestParam(required = false) @ApiParam("栏目ID") Integer seriesId) {
        return orderClient.getResearchReportManagerRelations(seriesId, null);
    }

    @GetMapping("getArticleSeriesByManager")
    @ApiOperation("获取处理人对应栏目列表")
    public BaseResult<List<ArticleSeries>> getArticleSeriesByManager() {
        Integer userId = AuthContextHolder.getUserId();
        return researchReportService.getArticleSeriesByManager(userId);
    }

    @GetMapping("getArticleTags")
    @ApiOperation("获取研报栏目标签列表")
    public BaseResult<List<ArticleTag>> getArticleSeries(
            @RequestParam @ApiParam("栏目Id") Integer seriesId,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enable) {
        return orderClient.getArticleTag(seriesId, enable);
    }

    @PostMapping("creatOrUpdateArticleTag")
    @ApiOperation("创建/编辑栏目标签")
    public BaseResult<ArticleTagResp> creatOrUpdateArticleTag(
            @RequestParam(required = false) @ApiParam("标签Id") Long tagId,
            @RequestParam @ApiParam("栏目Id") Integer seriesId,
            @RequestParam @ApiParam("标签名称") String tagName) {
        return orderClient.creatOrUpdateArticleTag(tagId, seriesId, tagName);
    }

    @PostMapping("isDisabledArticleTag")
    @ApiOperation("启用/禁用栏目标签")
    public BaseResult disabledArticleTag(
            @RequestParam(required = false) @ApiParam("标签Id") Long tagId,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enable) {
        return orderClient.disabledArticleTag(tagId, enable);
    }
}

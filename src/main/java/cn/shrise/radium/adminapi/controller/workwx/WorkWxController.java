package cn.shrise.radium.adminapi.controller.workwx;

import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.entity.WorkWxUserInfo;
import cn.shrise.radium.adminapi.req.WorkWxBatchCreateCustomerFollowUpBelongReq;
import cn.shrise.radium.adminapi.req.WorkWxCreateCustomerFollowUpBelongReq;
import cn.shrise.radium.adminapi.req.WwxUserListReq;
import cn.shrise.radium.adminapi.resp.*;
import cn.shrise.radium.adminapi.resp.user.*;
import cn.shrise.radium.adminapi.resp.workWx.*;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.workwx.WorkWxContactService;
import cn.shrise.radium.adminapi.service.workwx.WorkWxService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.HeaderConstant;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.tradeservice.resp.PortfolioFollowResp;
import cn.shrise.radium.userservice.entity.UcCustomerFollowUpRelation;
import cn.shrise.radium.userservice.req.CreateCustomerFollowUpReq;
import cn.shrise.radium.userservice.req.CustomerFollowUpRecordReq;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WorkWxFullContactUserRelation;
import cn.shrise.radium.workwxservice.bean.WwxConfig;
import cn.shrise.radium.workwxservice.constant.AddWayEnum;
import cn.shrise.radium.workwxservice.constant.WwxErrorCode;
import cn.shrise.radium.workwxservice.constant.WwxUserBindTypeEnum;
import cn.shrise.radium.workwxservice.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.*;

@Api
@Slf4j
@RestController
@RequestMapping("work_wx")
@RequiredArgsConstructor
public class WorkWxController {

    private final WorkWxService workWxService;

    private final WorkwxClient workwxClient;

    private final CommonService commonService;

    private final WorkWxContactService workWxContactService;

    @ApiOperation("获取当前公司全部企业微信配置")
    @GetMapping("config/all")
    public BaseResult<List<WwxConfig>> getConfigList(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId) {
        return workwxClient.getConfigList(companyType);
    }

    @ApiOperation("获取公众号")
    @GetMapping("official_account")
    public BaseResult<List<WxCpPropertiesResp>> getAccount(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType) {
        List<WxCpPropertiesResp> account = workWxService.getAccount(companyType);
        return BaseResult.success(account);
    }

    @ApiOperation("获取当前企业微信账号")
    @GetMapping("account/me")
    BaseResult<WorkWxUserResp> getCurrentAccount(@ApiIgnore @RequestHeader(WORK_WX_ID) Long id) {
        WorkWxUserResp resp = workWxService.getWorkWxAccount(id);
        return BaseResult.success(resp);
    }

    @ApiOperation("获取销售的企业微信账号")
    @GetMapping("account/belong")
    BaseResult<List<WorkWxUserInfo>> getBelongAccount(
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Integer accountType,
            @RequestParam(required = false) Integer status,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer salesId) {
        final List<WorkWxUserInfo> belongAccount = workWxService.getBelongAccount(salesId, false, enabled, companyType, accountType, status, null);
        return BaseResult.success(belongAccount);
    }

    @ApiOperation("获取销售的企业微信账号基础信息")
    @GetMapping("account/belong-base")
    BaseResult<List<WorkWxUserBaseInfo>> getBelongAccountBase(
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Integer accountType,
            @RequestParam(required = false) Integer status,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer salesId) {
        return workWxService.getBelongAccountBase(salesId, false, enabled, companyType, accountType, status);
    }

    @ApiOperation("获取销售的企业微信账号")
    @GetMapping("account/belong/all")
    BaseResult<List<WorkWxUserInfo>> getAllBelongAccount(
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Integer accountType,
            @RequestParam(required = false) Integer status,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer salesId) {
        final List<WorkWxUserInfo> belongAccount = workWxService.getBelongAccountConfig(salesId, null, enabled, companyType, accountType, status);
        return BaseResult.success(belongAccount);
    }

    @ApiOperation("根据number筛选销售的企业微信账号")
    @GetMapping("account/belong/filter")
    BaseResult<List<WorkWxUserInfo>> filterBelongAccount(
            @RequestParam @ApiParam("员工编号") String userNumber,
            @RequestParam(required = false) @ApiParam("是否退出企业") Boolean deleted,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled,
            @RequestParam(required = false)  @ApiParam("账号类型") Integer accountType,
            @RequestParam(required = false) @ApiParam("1 = 已激活，2 = 已禁用，4 = 未激活，5=退出企业") Integer status,
            @RequestParam(required = false)  @ApiParam("手机号") String mobile,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType) {
        Integer salesId = commonService.getStaffIdByNumber(companyType, userNumber);
        final List<WorkWxUserInfo> belongAccount = workWxService.getBelongAccount(salesId, deleted, enabled, companyType, accountType, status, mobile);
        return BaseResult.success(belongAccount);
    }

    @ApiOperation("获取企业微信授权链接")
    @GetMapping("oauth2/url")
    public BaseResult<String> getAuthorizationUrl(@RequestParam Integer accountType,
                                                  @RequestParam String redirectUri,
                                                  @RequestParam(required = false, defaultValue = "snsapi_base") String scope,
                                                  @RequestParam(required = false) String state) {
        String authorizationUrl = workWxService.getAuthorizationUrl(accountType, "auth_app", redirectUri, scope, state);
        log.info("企业微信授权链接 {}", authorizationUrl);
        return BaseResult.success(authorizationUrl);
    }

    @ApiOperation("获取企业微信授权链接(rpa版)")
    @GetMapping("rpa/oauth2/url")
    public BaseResult<String> getRpaAuthorizationUrl(@RequestParam Integer accountType,
                                                     @RequestParam String redirectUri,
                                                     @RequestParam(required = false) @ApiParam("授权类型") String authType,
                                                     @RequestParam(required = false, defaultValue = "snsapi_base") String scope,
                                                     @RequestParam(required = false) String state) {
        String authorizationUrl = workWxService.getRpaAuthorizationUrl(authType, accountType, "auth_app", redirectUri, scope, state);
        log.info("企业微信授权链接 {}", authorizationUrl);
        return BaseResult.success(authorizationUrl);
    }

    @ApiOperation("企业微信授权回调")
    @GetMapping("oauth2/redirectUri")
    public ResponseEntity<Void> redirectUri(@RequestParam String code,
                                            @RequestParam(required = false) String state,
                                            @RequestParam Integer accountType,
                                            @RequestParam String redirectUri) {
        String url = workWxService.redirectUri(code, state, "auth_app", accountType, redirectUri);
        log.info("企业微信授权回调url: {}", url);
        return ResponseEntity.status(HttpStatus.FOUND)
                .location(URI.create(url))
                .build();
    }

    @ApiOperation("获取企业微信jsapi配置")
    @GetMapping("jsapi/signature")
    public BaseResult<WorkWxJsApiSignature> getJsApiSignature(
            @RequestParam Integer accountType,
            @RequestParam String url) {
        return workWxService.getJsApiSignature(accountType, "auth_app", url);
    }

    @ApiOperation("获取企业微信agent jsapi配置")
    @GetMapping("jsapi/agent/signature")
    public BaseResult<WorkWxJsApiSignature> getAgentJsApiSignature(
            @RequestParam Integer accountType,
            @RequestParam String url) {
        return workWxService.getAgentJsApiSignature(accountType, "auth_app", url);
    }

    @GetMapping("contact")
    @ApiOperation("获取企业微信联系人详情")
    public BaseResult<WorkWxContactResp> getWorkWxContact(
            @RequestHeader(ACCOUNT_TYPE) @ApiIgnore Integer accountType,
            @RequestHeader(WORK_WX_ACCOUNT) @ApiIgnore String wxAccount,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam String externalUserId) {
        WorkWxContactResp workWxContact = workWxService.getWorkWxContact(companyType, accountType, wxAccount, externalUserId);
        return BaseResult.success(workWxContact);
    }

    @GetMapping("contact/detail")
    @ApiOperation("获取企业微信联系人详情")
    public BaseResult<WorkWxContactResp> getWorkWxContactDetail(
            @RequestHeader(ACCOUNT_TYPE) @ApiIgnore Integer accountType,
            @RequestHeader(WORK_WX_ACCOUNT) @ApiIgnore String wxAccount,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam String externalUserId) {
        WorkWxContactResp workWxContact = workWxService.getWorkWxContactDetail(companyType, accountType, wxAccount, externalUserId);
        return BaseResult.success(workWxContact);
    }

    @GetMapping("contact/tags")
    @ApiOperation("获取企业微信联系人标签")
    public BaseResult<WorkWxContactTagResp> getWorkWxContactTagList(
            @RequestHeader(ACCOUNT_TYPE) @ApiIgnore Integer accountType,
            @RequestHeader(WORK_WX_ACCOUNT) @ApiIgnore String wxAccount,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam String externalUserId) {
        WorkWxContactTagResp resp = workWxService.getContactTagList(companyType, accountType, wxAccount, externalUserId);
        return BaseResult.success(resp);
    }

    @GetMapping("contact/belong")
    @ApiOperation("获取客户服务归属")
    public BaseResult<List<CustomerFollowUpRelationResp>> getCustomerFollowUpBelong(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam String unionId) {
        List<CustomerFollowUpRelationResp> respList = workWxService.getCustomerFollowUpBelong(companyType, unionId);
        return BaseResult.success(respList);
    }

    @PostMapping("contact/belong")
    @ApiOperation("客户认领")
    public BaseResult<UcCustomerFollowUpRelation> createCustomerFollowUpBelong(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer belongId,
            @RequestBody WorkWxCreateCustomerFollowUpBelongReq req) {
        return workWxService.createCustomerFollowUpBelong(companyType, belongId, req);
    }

    @PostMapping("contact/follow/up")
    @ApiOperation("客户跟进")
    public BaseResult<Boolean> createCustomerFollowUp(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer followId,
            @RequestBody CreateCustomerFollowUpReq req) {
        req.setFollowId(followId);
        req.setCompanyType(companyType);
        return workWxService.createCustomerFollowUp(req);
    }

    @PostMapping("contact/follow/up/page")
    @ApiOperation("客户跟进记录")
    public PageResult<List<CustomerFollowUpRecordResp>> getCustomerFollowUpRecordPage(
            @RequestBody @Valid CustomerFollowUpRecordReq req) {
        return workWxService.getCustomerFollowUpRecordPage(req);
    }

    @PostMapping("contact/follow/up/belong/page")
    @ApiOperation("客户认领记录")
    public PageResult<List<CustomerFollowUpBelongRecordResp>> getCustomerFollowUpBelongRecordPage(
            @RequestBody @Valid CustomerFollowUpRecordReq req) {
        return workWxService.getCustomerFollowUpBelongRecordPage(req);
    }

    @ApiOperation("联系人管理")
    @GetMapping("contact_user_relation")
    public PageResult<List<WwxContactUserRelationResp>> contactUserRelation(
            @RequestParam(required = false) @ApiParam("添加开始时间 精确到秒") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime addStart,
            @RequestParam(required = false) @ApiParam("添加结束时间 精确到秒") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime addEnd,
            @RequestParam(required = false) @ApiParam("创建开始时间 精确到秒") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createStart,
            @RequestParam(required = false) @ApiParam("创建结束时间 精确到秒") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createEnd,
            @RequestParam(required = false) @ApiParam("dzId") String dzId,
            @RequestParam(required = false) @ApiParam("添加方式多选") List<Integer> addWayList,
            @RequestParam(required = false) @ApiParam("员工添加用户状态") Boolean userEnabled,
            @RequestParam(required = false) @ApiParam("客户添加员工状态") Boolean contactEnabled,
            @RequestHeader(HeaderConstant.COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) @ApiParam("企业") Integer accountType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return workwxClient.contactUserList(addStart, addEnd, createStart, createEnd, dzId, addWayList, userEnabled, contactEnabled, 45, accountType, current, size);
    }

    @ApiOperation("添加方式枚举")
    @GetMapping("addWay/list")
    public List<AddWayResp> getAddWayList() {
        AddWayEnum[] values = AddWayEnum.values();
        List<AddWayResp> list = new ArrayList<>();
        for (AddWayEnum tmp : values) {
            AddWayResp addWayResp = AddWayResp.builder().code(tmp.getCode())
                    .msg(tmp.getMsg()).build();
            list.add(addWayResp);
        }
        Collections.sort(list);
        return list;
    }

    @ApiOperation("获取某个部门下企业员工信息")
    @GetMapping("filterByDepts")
    public PageResult<List<WwxUserResp>> getUsersByDept(
            @RequestParam Integer accountType,
            @RequestParam Integer departmentId,
            @RequestParam(required = false) String searchText,
            @RequestParam(required = false) Boolean deleted,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size) {
        return workWxService.getUsersByDept(accountType, departmentId, searchText, deleted, current, size);
    }

    @ApiOperation("获取企业微信部门树")
    @GetMapping("dept")
    public List<WwxDepartmentResp> getAllDepartments(
            @RequestParam Integer accountType,
            @RequestParam(required = false) Boolean enabled) {
        return workwxClient.getAllDepartments(accountType, enabled);
    }

    @ApiOperation("根据wxAccount获取对应企业微信子部门")
    @GetMapping("dept/byWxAccount")
    public List<WwxDepartmentResp> getDepartmentsByWxAccount(
            @RequestParam Integer accountType,
            @RequestParam String wxAccount,
            @RequestParam(required = false) Boolean enabled) {
        return workwxClient.getDepartmentsByWxAccount(accountType, wxAccount, enabled);
    }

    @ApiOperation("联系人关系记录")
    @GetMapping("contact_user_relation/record/list")
    public PageResult<List<WwxContactUserRelationRecordResp>> contactUserRelationRecordList(
            @RequestParam(required = false) @ApiParam("创建开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createStart,
            @RequestParam(required = false) @ApiParam("创建结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createEnd,
            @RequestParam(required = false) @ApiParam("添加好友 true-好友 false-删除好友") Boolean isAdd,
            @RequestParam(required = false) @ApiParam("True-外部联系人删除成员，False-成员删除外部联系人 (仅在isAdd为false时有值)") Boolean deleteFlag,
            @RequestParam(required = false) @ApiParam("企业") Integer accountType,
            @RequestParam(required = false) @ApiParam("支持按好友关系ID或关系记录ID精确搜索") String searchContent,
            @RequestHeader(value = HeaderConstant.COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) Long dzId,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        return workwxClient.contactUserRelationRecodeList(createStart, createEnd, isAdd, deleteFlag, accountType, searchContent, 45, dzId, current, size);
    }

    @ApiOperation("根据unionId查询好友关系列表")
    @GetMapping("contact_user_relation/union")
    public PageResult<List<WorkWxContactUserRelationItem>> getFriendRelationListByUnionId(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("企业微信账号类型") Integer accountType,
            @RequestParam @ApiParam("unionId") String unionId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return workWxService.getFriendRelationListByUnionId(companyType, accountType, unionId, current, size);
    }

    @ApiOperation("获取当前companyType下全部企业微信配置")
    @GetMapping("config/this/all")
    public BaseResult<List<WwxConfig>> getConfigList(@RequestHeader(value = HeaderConstant.COMPANY_TYPE) @ApiIgnore Integer companyType) {
        return workwxClient.getConfigList(companyType);
    }

    @ApiOperation("企业微信管理")
    @PostMapping("wwx_user_list")
    public PageResult<List<WwxUserResp>> getWxContactList(
            @RequestHeader(value = HeaderConstant.COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestBody @Validated WwxUserListReq wwxUserListReq) {
        return workWxService.getWxUserList(companyType, wwxUserListReq);
    }

    @ApiOperation("配置企业微信员工邀请链接")
    @PostMapping("account/updateInviteUrl")
    public BaseResult<Boolean> updateInviteUrl(@RequestParam @ApiParam("id") Long id,
                                               @RequestParam @ApiParam("邀请链接") String url) {
        return workwxClient.updateInviteUrl(id, url);
    }

    @ApiOperation("企业微信管理-修改关联用户")
    @PostMapping("account/bindBelong")
    public BaseResult<WwxErrorCode> bindBelong(
            @RequestParam @ApiParam("企业微信员工Id") Long id,
            @RequestParam @ApiParam("绑定类型") Integer bindType,
            @RequestParam @ApiParam("关联用户Id") Integer belongId,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer operatorId) {
        return workwxClient.updateBelongBind(id, bindType, belongId, companyType, operatorId);
    }

    @ApiOperation("企业微信管理-解绑")
    @PostMapping("account/unBindBelong")
    public BaseResult<WwxErrorCode> unBindBelong(
            @RequestParam @ApiParam("企业微信员工Id") Long id,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer operatorId) {
        return workwxClient.updateBelongBind(id, WwxUserBindTypeEnum.BT_UNBIND.getType(), null, companyType, operatorId);
    }

    @ApiOperation("离职企业微信员工")
    @PostMapping("account/delete")
    public BaseResult<Boolean> deleteWorkWxUser(
            @RequestParam @ApiParam("企业微信公司类型") Integer accountType,
            @RequestParam @ApiParam("员工wxAccount") String wxAccount) {
        return workwxClient.deleteWorkWxUser(accountType, wxAccount);
    }

    @GetMapping("user/relation")
    @ApiOperation("相关好友")
    public BaseResult<List<WorkWxFullContactUserRelation>> getUserRelation(
            @RequestParam @ApiParam("unionId") String unionId
    ) {
        List<WorkWxFullContactUserRelation> userRelation = workWxService.getUserRelation(unionId);
        return BaseResult.success(userRelation);
    }

    @GetMapping("relation/record")
    @ApiOperation("关系记录")
    public BaseResult<List<RelationRecordResp>> getRelationRecord(@RequestParam @ApiParam("relationId") Integer relationId) {
        List<RelationRecordResp> relationRecord = workWxService.getRelationRecord(relationId);
        return BaseResult.success(relationRecord);
    }

    @GetMapping("contact/group/page")
    @ApiOperation("获取群聊信息")
    public PageResult<List<ContactGroupResp>> getContactGroupPage(
            @RequestParam(required = false) @ApiParam("accountType") Integer accountType,
            @RequestParam(required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(required = false) @ApiParam("每页数量") Integer size
    ) {
        return workwxClient.getContactGroupPage(AuthContextHolder.getCompanyType(), accountType, current, size);
    }

    @GetMapping("contact/group/member")
    @ApiOperation("获取群成员列表")
    public PageResult<List<ContactGroupMemberResp>> getContactGroupMemberPage(
            @RequestParam @ApiParam("accountType") Integer accountType,
            @RequestParam @ApiParam("chatId") String chatId,
            @RequestParam(required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(required = false) @ApiParam("每页数量") Integer size
    ) {
        return workwxClient.getContactGroupMemberPage(accountType, chatId, current, size);
    }

    @ApiOperation("获取群聊天记录")
    @GetMapping("contact/group/msg")
    BaseResult<List<ContactGroupMsgResp>> getContactGroupMsg(
            @RequestParam @ApiParam("accountType") Integer accountType,
            @RequestParam @ApiParam("chatId") String chatId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam @ApiParam("正序排列") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("消息时间") Long msgTime,
            @RequestParam(required = false) @ApiParam("消息id") String id,
            @RequestParam @ApiParam("是否下一页") Boolean isNext,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return workwxClient.getContactGroupMsg(accountType, chatId, startTime, endTime, isAsc, msgTime, id, isNext, size);
    }

    @ApiOperation("获取员工服务客户列表")
    @GetMapping("contact/customer/list")
    PageResult<List<WorkWxServiceCustomerResp>> getCustomerList(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer salesId,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("角色Id") Long roleId,
            @RequestParam @ApiParam("accountType") Integer accountType,
            @RequestParam @ApiParam("员工wxAccount") String wxAccount,
            @RequestParam @ApiParam(value = "是否已认领", defaultValue = "true") Boolean isBelong,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前分页") Integer current,
            @RequestParam(required = false) @ApiParam("每页数量") Integer size) {
        return workWxService.getCustomerList(salesId, companyType, roleId, accountType, wxAccount, isBelong, searchContent, current, size);
    }

    @ApiOperation("获取员工服务客户列表")
    @GetMapping("contact/customers")
    BaseResult<List<WorkWxServiceCustomerResp>> getCustomers(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer salesId,
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = ACCOUNT_TYPE) @ApiIgnore Integer accountType,
            @RequestHeader(value = WORK_WX_ACCOUNT) @ApiIgnore String wxAccount,
            @RequestParam @ApiParam(value = "是否已认领", defaultValue = "true") Boolean isBelong
    ) {
        return BaseResult.success(workWxService.getCustomers(salesId, companyType, accountType, wxAccount, isBelong, null));
    }


    @GetMapping("follow/{userId}")
    @ApiOperation("关注列表")
    public PageResult<List<PortfolioFollowResp>> followList(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("组合类型") Integer portfolioType,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return workWxService.followList(userId, portfolioType, channelType, current, size);
    }

    @PostMapping("contact/belong/batch")
    @ApiOperation("批量客户认领")
    public BaseResult<String> batchCreateCustomerFollowUpBelong(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer belongId,
            @RequestBody WorkWxBatchCreateCustomerFollowUpBelongReq req) {
        return workWxService.batchCreateCustomerFollowUpBelong(companyType, belongId, req);
    }

    @GetMapping("contact/follow/up/belong/operator")
    @ApiOperation("获取客户认领记录")
    public PageResult<List<UcCustomerFollowUpRecordResp>> getBelongRecordByOperator(
            @RequestHeader(USER_ID) @ApiIgnore Integer operator,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size) {
        return workWxService.getBelongRecordByOperator(operator, current, size);
    }

    @ApiOperation("校验企微账号是否开启会话存档")
    @GetMapping("checkMsgAudit")
    public BaseResult<CheckMsgAuditResp> checkMsgAudit(
            @RequestParam @ApiParam("企业微信公司类型") Integer accountType,
            @RequestParam @ApiParam("员工wxAccount") String wxAccount) {
        return workwxClient.checkMsgAudit(accountType, wxAccount);
    }

    @ApiOperation("查询wxAccount是否存在上线渠道和获客链接")
    @GetMapping("getWayAndAcquisitionByWxAccount")
    public BaseResult<WayAndAcquisitionResp> getWayAndAcquisitionByWxAccount(
            @RequestParam @ApiParam("企业微信公司类型") Integer accountType,
            @RequestParam @ApiParam("企业微信账号") String wxAccount,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled) {
        return workWxService.getWayAndAcquisitionByWxAccount(accountType, wxAccount, enabled);
    }

    @ApiOperation("查询好友关系信息")
    @GetMapping("friend-info")
    public BaseResult<FullRelationInfoResp> getWorkWxFriendInfo(
            @RequestParam @ApiParam("企业微信公司类型") Integer accountType,
            @RequestParam @ApiParam("企业微信账号") String wxAccount,
            @RequestParam @ApiParam("企业微信外部联系人id") String externalUserId) {
        return BaseResult.success(workWxContactService.getWorkWxFriendInfo(accountType,wxAccount,externalUserId));
    }
}

package cn.shrise.radium.adminapi.controller.im;

import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.resp.ChatStreamMessageResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("chat-stream-msg")
public class ChatStreamMsgNewController {

    private final ImClient imClient;

    @GetMapping("/search-list")
    @ApiOperation("互动解盘搜索列表")
    public PageResult<List<ChatStreamMessageResp>> getStreamMessagePage(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("聊天室id") Long chatId,
            @RequestParam @ApiParam("搜索内容") String searchText,
            @RequestParam(defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) Integer size) {
//        return imClient.getStreamMessageSearchList(companyType, chatId, searchText, current, size);
        return imClient.getStreamMessagePage(companyType, chatId, searchText, null, null, null, current, size);
    }
}

package cn.shrise.radium.adminapi.controller.marketing;

import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.service.marketing.InviteLinkService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.entity.NpWxSalesInviteUrl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;
import static cn.shrise.radium.common.constant.ProductTypeConstant.GC_LIVE_ROOM;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Api
@Slf4j
@RestController
@RequestMapping("marketing/invite")
@RequiredArgsConstructor
public class InviteLinkController {

    private final MarketingClient marketingClient;
    private final CommonProperties commonProperties;

    private final InviteLinkService inviteLinkService;

    @GetMapping("room/{roomId}")
    @ApiOperation("获取直播室邀请链接")
    BaseResult<String> getRoomInviteUrl(@PathVariable Long roomId) {
        NpWxSalesInviteUrl inviteInfo = marketingClient.genInviteUrl(AuthContextHolder.getUserId(), roomId).getData();
        String url = String.format("%s/i/%d/%s", commonProperties.getWebApiUrl(), GC_LIVE_ROOM, inviteInfo.getNumber());
        return BaseResult.success(url);
    }

    @GetMapping("content/share/{id}")
    @ApiOperation("获取内容分享邀请链接")
    public BaseResult<String> getContentShareInviteUrl(
            @PathVariable Long id,
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @RequestParam(required = false) Integer accountType,
            @RequestParam(required = false) @ApiParam("链接类型") Integer linkType) {
        return inviteLinkService.getContentShareInviteUrl(id, userId, companyType, accountType, linkType);
    }
}

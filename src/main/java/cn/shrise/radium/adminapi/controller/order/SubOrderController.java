package cn.shrise.radium.adminapi.controller.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.order.SubOrderDtoResp;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.OrderService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.req.RefundBatchMarkReq;
import cn.shrise.radium.orderservice.req.RefundMarkReq;
import cn.shrise.radium.orderservice.resp.RefundBatchMarkResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;
import static cn.shrise.radium.common.util.DateUtils.DEFAULT_PATTERN_DATE;
import static cn.shrise.radium.common.util.DateUtils.localDateToInstant;

@RestController
@RequestMapping("orders/sub")
@RequiredArgsConstructor
public class SubOrderController {

    private final OrderService orderService;
    private final CommonService commonService;
    private final OrderClient orderClient;

    @ApiOperation("查询子订单列表")
    @GetMapping("list")
    public PageResult<List<SubOrderDtoResp>> getSubOrderByFilter(
            @RequestParam(required = false) @ApiParam("主订单编号") String orderNumber,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("销售ids") List<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("创建开始时间") @DateTimeFormat(pattern =
                    DEFAULT_PATTERN_DATE) LocalDate startCreateTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endCreateTime,
            @RequestParam(required = false) @ApiParam("支付开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startPayTime,
            @RequestParam(required = false) @ApiParam("支付结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endPayTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer companyType = AuthContextHolder.getCompanyType();
        Instant startCreateInstant = startCreateTime == null ? null : localDateToInstant(startCreateTime);
        Instant endCreateInstant = endCreateTime == null ? null : localDateToInstant(endCreateTime.plusDays(1));
        Instant startPayInstant = startPayTime == null ? null : localDateToInstant(startPayTime);
        Instant endPayInstant = endPayTime == null ? null : localDateToInstant(endPayTime.plusDays(1));
        Integer orderId = null;
        if (ObjectUtils.isNotEmpty(orderNumber)) {
            orderId = commonService.getOrderIdByNumber(orderNumber);
        }
        return orderService.getSubOrderByFilter(companyType, orderId, orderStatus, salesIds,
                startCreateInstant, endCreateInstant, startPayInstant, endPayInstant, searchContent, current, size);
    }

    @ApiOperation("通过主订单ID获取子订单信息")
    @GetMapping("byOrderId")
    public BaseResult<List<SubOrderDtoResp>> findSubOrderList(
            @RequestParam @ApiParam("主订单编号") String orderNumber,
            @RequestParam(required = false) @ApiParam("订单状态 1：已支付，2：待支付，3：已关闭，4：已开通，5：已退款") Integer orderStatus) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return orderService.getSubOrderByFilter(orderId, orderStatus);
    }

    @ApiOperation("标记退款")
    @PostMapping("markRefund")
    public BaseResult<ErrorConstant> markRefund(
            @RequestParam @ApiParam("子订单id") Integer subOrderId,
            @RequestParam(required = false) @ApiParam("子退款number") String refundNumber) {
        if (ObjectUtil.isNotEmpty(refundNumber)) {
            commonService.checkMarkRefund(refundNumber);
        }
        return orderService.subOrderMarkRefund(subOrderId, AuthContextHolder.getUserId());
    }

    @ApiOperation("对公转账标记")
    @PostMapping("transferRefund")
    public BaseResult<String> transferRefund(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid RefundMarkReq req
    ) {
        req.setMarkerId(userId);
        commonService.checkMarkRefund(req.getRefundNumber());
        return orderService.transferRefund(req);
    }

    @ApiOperation("强制标记")
    @PostMapping("forceRefundMark")
    public BaseResult<String> forceRefundMark(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid RefundMarkReq req
    ) {
        commonService.checkMarkRefund(req.getRefundNumber());
        req.setMarkerId(userId);
        return orderService.forceRefundMark(req);
    }

    @ApiOperation("关闭子订单")
    @PostMapping("close")
    public BaseResult<Boolean> closeSubOrder(@RequestParam @ApiParam("子订单id") Integer subOrderId) {
        return orderClient.closePaymentSubOrder(subOrderId);
    }

    @ApiOperation("批量标记退款")
    @PostMapping("refund-batch-mark")
    public BaseResult<RefundBatchMarkResp> refundBatchMark(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid RefundBatchMarkReq req
    ) {
        req.setMarkerId(userId);
        return orderClient.refundBatchMark(req);
    }

}

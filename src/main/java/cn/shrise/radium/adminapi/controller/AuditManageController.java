package cn.shrise.radium.adminapi.controller;

import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.OrderAuditManageResp;
import cn.shrise.radium.adminapi.resp.order.OrderAuditFlowResp;
import cn.shrise.radium.adminapi.resp.order.OrderFlowStepsResp;
import cn.shrise.radium.adminapi.service.AuditManageService;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.ExportExcelService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.OrderErrorCode;
import cn.shrise.radium.orderservice.constant.OrderFlowOperateTypeConstant;
import cn.shrise.radium.orderservice.entity.CourseSubOrder;
import cn.shrise.radium.orderservice.entity.RsCourseRefundOrder;
import cn.shrise.radium.orderservice.entity.RsFileExportRecord;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import cn.shrise.radium.orderservice.req.AuditManageReq;
import cn.shrise.radium.orderservice.req.ExportBusinessAuditReq;
import cn.shrise.radium.orderservice.resp.OrderInfoResp;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.bean.ExportFileInfo;
import cn.shrise.radium.statisticsservice.constant.ExportFileEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("auditManages")
@RequiredArgsConstructor
public class AuditManageController {

    private final AuditManageService auditManageService;
    private final ExportExcelService exportExcelService;
    private final StatisticsClient statisticsClient;
    private final OrderClient orderClient;
    private final CommonService commonService;

    /**
     * 商务审核管理列表
     *
     * @param manageReq 查询条件
     * @return 商务审核管理列表分页
     */
    @ApiOperation("获取商务审核管理列表")
    @PostMapping("auditManage")
    public PageResult<List<OrderAuditManageResp>> getAuditManageList(
            @ApiParam @RequestBody(required = false) @Validated AuditManageReq manageReq) {
        return auditManageService.getOrderManageList(manageReq);
    }

    /**
     * 分配商务
     *
     * @param auditorId
     * @param orderNumbers
     * @return
     */
    @ApiOperation("分配商务")
    @PatchMapping("assign")
    public BaseResult<Boolean> swAssign(
            @RequestParam @ApiParam("商务审核人ID") Integer auditorId,
            @RequestParam @ApiParam("订单编号") List<String> orderNumbers) {
        List<Integer> orderIds = commonService.getBatchOrderIdByNumber(orderNumbers);
        return auditManageService.swAssign(AuthContextHolder.getUserId(), auditorId, orderIds);
    }

    /**
     * 根据订单id获取订单流程信息
     *
     * @param orderNumber 订单编号
     * @return 流程信息
     */
    @ApiOperation("流程信息")
    @GetMapping("flow/{orderNumber}")
    public BaseResult<OrderFlowStepsResp> getOrderFlow(@PathVariable @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return auditManageService.getOrderFlow(orderId);
    }

    @ApiOperation("查询审核流程记录")
    @GetMapping("auditFlow/{orderNumber}")
    public BaseResult<List<OrderAuditFlowResp>> findByOrderId(@PathVariable @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return auditManageService.getOrderAuditFlow(orderId);
    }

    @ApiOperation("查询合规审核流程记录")
    @GetMapping("hg/auditFlow/{orderNumber}")
    public BaseResult<List<OrderAuditFlowResp>> findHgByOrderId(@PathVariable String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        List<OrderAuditFlowResp> flowRespList = auditManageService.getOrderAuditFlow(orderId).getData();
        List<Integer> hgFlow = Arrays.asList(OrderFlowOperateTypeConstant.HG_ASSIGN.getValue(), OrderFlowOperateTypeConstant.HG_AUDIT.getValue(), OrderFlowOperateTypeConstant.CLOSE.getValue());
        List<OrderAuditFlowResp> result = flowRespList.stream().filter(e -> hgFlow.contains(e.getOperateType())).collect(Collectors.toList());
        return BaseResult.success(result);
    }

    /**
     * 根据订单id获取退款信息
     *
     * @param orderNumber 订单编号
     * @return 退款信息
     */
    @ApiOperation("退款信息")
    @GetMapping("refund/{orderNumber}")
    public BaseResult<List<RsCourseRefundOrder>> getRefundList(@PathVariable @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return auditManageService.getRefundList(orderId);
    }

    /**
     * 根据订单id获取相关子订单信息
     *
     * @param orderNumber 订单编号
     * @return 相关子订单信息
     */
    @ApiOperation("相关子订单信息")
    @GetMapping("sub/{orderNumber}")
    public BaseResult<List<CourseSubOrder>> getSubOrderList(@PathVariable @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return auditManageService.getSubOrderList(orderId);
    }

    @ApiOperation("获取订单相关信息")
    @GetMapping("info/{orderNumber}")
    public BaseResult<OrderInfoResp> getOrderInfo(@PathVariable @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return auditManageService.getOrderInfo(orderId);
    }

    @ApiOperation("修改订单备注")
    @PutMapping("order/updateRemark")
    public BaseResult<ErrorConstant> updateRemarkStatus(
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("订单备注") String remark){
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        BaseResult<OsErrorCode> res = orderClient.updateRemarkStatus(orderId, remark);
        if (res.isSuccess()){
            return BaseResult.of(ErrorConstant.SUCCESS);
        } else {
            return BaseResult.of(ErrorConstant.FAILURE);
        }
    }

    @ApiOperation("导出审核管理列表")
    @PostMapping("export")
    public BaseResult<RsFileExportRecord> exportAuditManage(@RequestParam @ApiParam("文件名") String fileName,
                                                            @RequestBody ExportBusinessAuditReq manageReq) {
        RsFileExportRecord record = exportExcelService.genExportRecord(fileName);
        ExportFileInfo<Object> info = ExportFileInfo.builder()
                .recordId(record.getId())
                .fileEnum(ExportFileEnum.EXPORT_AUDIT_MANAGE)
                .t(manageReq)
                .build();
        BaseResult<String> sendRes = statisticsClient.exportExcel(info);
        if (sendRes.isFail()) {
            exportExcelService.markExportFile(info.getRecordId());
        }
        return BaseResult.success(record);
    }

    @ApiOperation("商务审核")
    @PostMapping("audit/{orderId}")
    public BaseResult<OrderErrorCode> swAudit(@PathVariable Integer orderId) {
        Integer userId = AuthContextHolder.getUserId();
        return auditManageService.swAudit(userId, orderId);
    }

    @ApiOperation("关闭商务审核")
    @PostMapping("close/audit/{orderId}")
    public BaseResult<OrderErrorCode> closeSwAudit(@PathVariable Integer orderId) {
        Integer userId = AuthContextHolder.getUserId();
        return auditManageService.closeSwAudit(userId, orderId);
    }
}

package cn.shrise.radium.adminapi.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.content.EvaluationIdRecordResp;
import cn.shrise.radium.adminapi.resp.content.EvaluationOperateRecordResp;
import cn.shrise.radium.adminapi.resp.user.CustomerEvaluationResp;
import cn.shrise.radium.adminapi.service.AuthService;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.EvaluationService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.req.IdVerifyReq;
import cn.shrise.radium.contentservice.resp.EvaluationInfoResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@Slf4j
@RestController
@RequestMapping("evaluation")
@RequiredArgsConstructor
public class EvaluationController {

    private final EvaluationService evaluationService;

    private final AuthService authService;
    private final CommonService commonService;

    @ApiOperation("用户测评信息")
    @GetMapping("info")
    public BaseResult<EvaluationInfoResp> getEvaluationInfo(
            @RequestParam @ApiParam("用户编号") String userNumber) {
        Integer userId = commonService.getUserIdByNumber(AuthContextHolder.getCompanyType(), userNumber, null);
        EvaluationInfoResp infoResp = evaluationService.getEvaluationInfo(userId, "您的年龄：");
        return BaseResult.success(infoResp);
    }

    @ApiOperation("获取客户测评操作记录")
    @GetMapping("evaluationOperateRecord")
    public PageResult<List<EvaluationOperateRecordResp>> getEvaluationRecord(
            @RequestParam @ApiParam("用户编号") String userNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer userId = commonService.getUserIdByNumber(AuthContextHolder.getCompanyType(), userNumber);
        return evaluationService.getEvaluationRecord(userId, current, size);
    }

    @ApiOperation("获取客户认证操作记录")
    @GetMapping("evaluationOperateRecordId")
    public PageResult<List<EvaluationOperateRecordResp>> getEvaluationIdRecord(
            @RequestParam @ApiParam("用户编号") String userNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer userId = commonService.getUserIdByNumber(AuthContextHolder.getCompanyType(), userNumber);
        return evaluationService.getEvaluationIdRecord(userId, current, size);
    }

    @GetMapping("redoEvaluation")
    @ApiOperation("重新测评")
    public BaseResult<CustomerEvaluationResp> redoEvaluation(
            @RequestParam @ApiParam("用户编号") String userNumber,
            @RequestParam @ApiParam("测评编号") String evaluationNumber) {
        Integer evaluationId = commonService.getEvaluationIdByNumber(evaluationNumber);
        Integer userId = commonService.getUserIdByNumber(AuthContextHolder.getCompanyType(), userNumber);
        Integer operator = AuthContextHolder.getUserId();
        return evaluationService.evaluationRedo(evaluationId, userId, operator);
    }

    @GetMapping("evaluationIdRedo")
    @ApiOperation("重新认证")
    public BaseResult<String> evaluationIdRedo(
            @RequestParam @ApiParam("测评编号") String evaluationNumber) {
        Integer evaluationId = commonService.getEvaluationIdByNumber(evaluationNumber);
        Integer userId = AuthContextHolder.getUserId();
        return evaluationService.evaluationIdRedo(evaluationId, userId);
    }

    @ApiOperation("获取客户测评记录")
    @GetMapping("evaluationRecord")
    public PageResult<List<CustomerEvaluationResp>> evaluationRecord(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("用户编号") String userNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer userId = commonService.getUserIdByNumber(companyType, userNumber);
        return evaluationService.evaluationRecord(userId, current, size);
    }

    @ApiOperation("获取客户认证记录")
    @GetMapping("evaluationIdRecord")
    public PageResult<List<EvaluationIdRecordResp>> evaluationIdRecord(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("用户编号") String userNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer userId = commonService.getUserIdByNumber(companyType, userNumber);
        return evaluationService.evaluationIdRecord(userId, current, size);
    }

    @ApiOperation("认证审核")
    @PostMapping("evaluationIdVerify")
    public BaseResult<String> evaluationIdVerify(
            @RequestBody IdVerifyReq req) {
        req.setUserId(AuthContextHolder.getUserId());
        evaluationService.evaluationIdVerify(req);
        return BaseResult.success();
    }

    @GetMapping("generateEvaluationPdf")
    @ApiOperation("生成测评pdf")
    ResponseEntity<Void> evaluationPdf(
            @ApiIgnore HttpServletResponse response,
            @RequestParam @ApiParam("测评number") String number,
            @RequestParam @ApiParam("token") String token) throws IOException {
        Integer userId = (Integer) authService.getTokenPayload(token).get(USER_ID);
        if (ObjectUtil.isEmpty(userId)) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().write("权限不足");
            response.getWriter().flush();
            return ResponseEntity.ok().build();
        }
        evaluationService.generateEvaluationPdf(response, number);
        return ResponseEntity.ok().build();
    }

    @GetMapping("getEvaluationPdf")
    @ApiOperation("查看测评结果pdf")
    ResponseEntity<Void> getEvaluationPdf(
            @ApiIgnore HttpServletResponse response,
            @RequestParam @ApiParam("测评number") String number,
            @RequestParam @ApiParam("token") String token) throws IOException {
        Integer userId = (Integer) authService.getTokenPayload(token).get(USER_ID);
        if (ObjectUtil.isEmpty(userId)) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().write("权限不足");
            response.getWriter().flush();
            return ResponseEntity.ok().build();
        }
        evaluationService.getEvaluationPdf(response, number);
        return ResponseEntity.ok().build();
    }

}

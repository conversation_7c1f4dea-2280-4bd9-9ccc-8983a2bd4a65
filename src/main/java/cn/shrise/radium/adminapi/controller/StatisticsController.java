package cn.shrise.radium.adminapi.controller;

import cn.shrise.radium.adminapi.resp.*;
import cn.shrise.radium.adminapi.service.StatisticsService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.req.DepartmentStatisticsReq;
import cn.shrise.radium.statisticsservice.req.DeptSalesReq;
import cn.shrise.radium.statisticsservice.req.SalesStatisticsReq;
import cn.shrise.radium.statisticsservice.resp.SalesTraceExploitResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api
@Slf4j
@RestController
@RequestMapping("statistics")
@RequiredArgsConstructor
public class StatisticsController {

    private final StatisticsService statisticsService;
    private final StatisticsClient statisticsClient;

    /**
     * 获取员工报表列表
     *
     * @param statisticsReq 查询条件
     * @return 报表列表分页
     */
    @PostMapping("salesPerformance")
    @ApiOperation("获取员工报表列表")
    PageResult<List<SalesPerformanceStatisticsResp>> getStatisticsList(
            @RequestBody @Validated SalesStatisticsReq statisticsReq) {
        return statisticsService.getSalesPerformanceList(statisticsReq);
    }

    /**
     * 获取部门的报表列表
     *
     * @param req 查询条件
     * @return 报表列表分页
     */
    @PostMapping("deptPerformance")
    @ApiOperation("获取部门报表列表")
    PageResult<List<DeptPerformanceResp>> getDeptPerformanceList(
            @RequestBody @Validated DepartmentStatisticsReq req) {
        return statisticsService.getDeptPerformanceList(req);
    }

    @ApiOperation("获取部门排行分析")
    @PostMapping("dept/ranking")
    PageResult<List<DeptRankingResp>> getDeptRankingResp(
            @RequestBody @Validated DepartmentStatisticsReq statisticsReq) {
        return statisticsService.getDeptRankingResp(statisticsReq);
    }

    @ApiOperation("获取员工排行分析")
    @PostMapping("sales/ranking")
    PageResult<List<SalesRankingResp>> getSalesRankingResp(
            @RequestBody @Validated DeptSalesReq deptSalesReq) {
        return statisticsService.getSalesRankingResp(deptSalesReq);
    }

    @ApiOperation("获取部门渠道明细")
    @GetMapping("dz/detail")
    BaseResult<List<DeptDzDetailResp>> getDeptDzDetailList(
            @RequestParam @ApiParam("部门ID") Integer deptId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("日期类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("渠道ID") List<Long> dzIds,
            @RequestParam @ApiParam("订单属性") Integer orderLevel
    ) {
        return statisticsService.getDeptDzDetailList(deptId, periodType, flagTime, dzIds, orderLevel);
    }

    @PostMapping("sales_trace/exploit")
    @ApiOperation("销售开发率追踪报表")
    public PageResult<List<SalesTraceExploitResp>> getSalesTraceExploit(
            @RequestParam @ApiParam("销售id") List<Integer> salesIdList,
            @RequestParam(value = "startTime") String startTime,
            @RequestParam(value = "endTime") String endTime,
            @RequestParam @ApiParam("1/2/3") Integer levelType,
            @RequestParam Integer current,
            @RequestParam Integer size
    ) {
        return statisticsClient.getSalesTraceExploit(salesIdList, startTime, endTime, levelType, current, size);
    }

}

package cn.shrise.radium.adminapi.controller.compliance;

import cn.shrise.radium.adminapi.service.ExportExcelService;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.service.PerformancePenaltyNoticeService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.complianceservice.ComplianceClient;
import cn.shrise.radium.complianceservice.req.*;
import cn.shrise.radium.complianceservice.resp.PerformancePenaltyNoticeDetailResp;
import cn.shrise.radium.complianceservice.resp.PerformancePenaltyNoticeRecordResp;
import cn.shrise.radium.complianceservice.resp.PerformancePenaltyNoticeResp;
import cn.shrise.radium.orderservice.entity.RsFileExportRecord;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.bean.ExportFileInfo;
import cn.shrise.radium.statisticsservice.constant.ExportFileEnum;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.UserDTO;
import cn.shrise.radium.userservice.entity.UcDepartment;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * @Author: tangjiajun
 * @Date: 2025/5/27 9:42
 * @Desc:
 **/
@RestController
@RequestMapping("performance-penalty-notice")
@RequiredArgsConstructor
public class PerformancePenaltyNoticeController {

    private final ComplianceClient complianceClient;
    private final PerformancePenaltyNoticeService performancePenaltyNoticeService;
    private final ExportExcelService exportExcelService;
    private final StatisticsClient statisticsClient;
    private final UserClient userClient;

    @PostMapping("list")
    @ApiOperation("业绩处罚单列表")
    public PageResult<List<PerformancePenaltyNoticeResp>> performancePenaltyNoticeList(
            @RequestBody PerformancePenaltyNoticeReq req,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size) {
        return performancePenaltyNoticeService.performancePenaltyNoticeList(null, null, req, current, size);
    }

    @PostMapping("relation-list")
    @ApiOperation("相关业绩处罚单列表")
    public PageResult<List<PerformancePenaltyNoticeResp>> performancePenaltyNoticeRelationList(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer salesId,
            @RequestBody PerformancePenaltyNoticeReq req,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size) {
        if (ObjectUtil.isEmpty(req.getSalesList())) {
            UcDepartment department = userClient.getDepartmentByUserId(salesId).orElseThrow();
            List<UserDTO> userDTOS = userClient.getAllUsersByDepartmentId(department.getId(), null, companyType).orElseThrow();
            List<Integer> salesList = userDTOS.stream().map(UserDTO::getId).collect(Collectors.toList());
            req.setSalesList(salesList);
        }
        return performancePenaltyNoticeService.performancePenaltyNoticeList(null, null, req, current, size);
    }

    @PostMapping("own-list")
    @ApiOperation("我的业绩处罚单列表")
    public PageResult<List<PerformancePenaltyNoticeResp>> performancePenaltyNoticeOwnList(
            @RequestHeader(USER_ID) @ApiIgnore Integer salesId,
            @RequestBody PerformancePenaltyNoticeReq req,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size) {
        return performancePenaltyNoticeService.performancePenaltyNoticeList(salesId, null, req, current, size);
    }

    @PostMapping("create-list")
    @ApiOperation("我创建的业绩处罚单列表")
    public PageResult<List<PerformancePenaltyNoticeResp>> performancePenaltyNoticeCreateLis(
            @RequestHeader(USER_ID) @ApiIgnore Integer creatorId,
            @RequestBody PerformancePenaltyNoticeReq req,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size) {
        return performancePenaltyNoticeService.performancePenaltyNoticeList(null, creatorId, req, current, size);
    }

    @GetMapping("detail")
    @ApiOperation("业绩处罚单详情")
    public BaseResult<PerformancePenaltyNoticeDetailResp> performancePenaltyNoticeDetail(
            @RequestParam @ApiParam("处罚单id") Long id) {
        return performancePenaltyNoticeService.performancePenaltyNoticeDetail(id);
    }

    @PostMapping("create")
    @ApiOperation("创建业绩处罚单")
    public BaseResult<Void> createPerformancePenaltyNotice(@RequestHeader(USER_ID) @ApiIgnore Integer creatorId,
                                                           @RequestBody PerformancePenaltyNoticeDetailReq performancePenaltyNoticeDetailReq) {
        performancePenaltyNoticeDetailReq.setCreatorId(creatorId);
        return performancePenaltyNoticeService.createNotice(performancePenaltyNoticeDetailReq);
    }

    @PostMapping("audit")
    @ApiOperation("业绩处罚单审核")
    public BaseResult<Void> auditPerformancePenaltyNotice(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                                          @RequestBody @Valid PerformancePenaltyNoticeAuditReq penaltyNoticeAuditReq) {
        penaltyNoticeAuditReq.setUserId(userId);
        return complianceClient.auditPerformancePenaltyNotice(penaltyNoticeAuditReq);
    }

    @PostMapping("appeal")
    @ApiOperation("业绩处罚单申诉")
    public BaseResult<Void> appealPerformancePenaltyNotice(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                                           @RequestBody PerformancePenaltyNoticeAppealReq req) {
        req.setUserId(userId);
        return complianceClient.appealPerformancePenaltyNotice(req);
    }

    @PostMapping("appeal-audit")
    @ApiOperation("业绩处罚单申诉审核")
    public BaseResult<Void> auditAppealPerformancePenaltyNotice(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                                       @RequestBody @Valid PerformancePenaltyNoticeAppealAuditReq appealAuditReq) {
        appealAuditReq.setUserId(userId);
        return complianceClient.auditAppealPerformancePenaltyNotice(appealAuditReq);
    }

    @GetMapping("record/list")
    @ApiOperation("业绩处罚单操作记录列表")
    public BaseResult<List<PerformancePenaltyNoticeRecordResp>> performancePenaltyNoticeOperateRecordList(
            @RequestParam @ApiParam("处罚单id") Long noticeId) {
        return performancePenaltyNoticeService.noticeOperateRecordList(noticeId);
    }

    @PostMapping("export")
    @ApiOperation("业绩处罚单导出")
    public BaseResult<Integer> performancePenaltyNoticeExport(
            @RequestParam @ApiParam("文件名") String fileName,
            @RequestBody PerformancePenaltyNoticeReq req) {
        RsFileExportRecord record = exportExcelService.genExportRecord(fileName, "zip", "zip");
        ExportFileInfo<PerformancePenaltyNoticeReq> info = new ExportFileInfo<>();
        info.setRecordId(record.getId());
        info.setFileEnum(ExportFileEnum.EXPORT_PERFORMANCE_PENALTY_NOTICE);
        info.setT(req);
        BaseResult<String> sendRes = statisticsClient.exportExcel(info);
        if (sendRes.isFail()) {
            exportExcelService.markExportFile(info.getRecordId());
        }
        return BaseResult.success(record.getId());
    }


}

package cn.shrise.radium.adminapi.controller;

import cn.shrise.radium.adminapi.resp.GetWholeOrderInfo;
import cn.shrise.radium.adminapi.service.OrderService;
import cn.shrise.radium.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;

@Api
@Slf4j
@RestController
@RequestMapping("wholeSelectOrder")
@RequiredArgsConstructor
public class WholeSelectOrderController {
    private final OrderService orderService;

    @ApiOperation("获取主订单号")
    @GetMapping("{searchNumber}")
    public BaseResult<Integer> getOrderId(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @PathVariable String searchNumber){
        final Integer orderId = orderService.getOrderId(companyType, searchNumber);
        return BaseResult.success(orderId);
    }

    @ApiOperation("全局搜索获取订单信息")
    @GetMapping({"search/{pkId}"})
    public BaseResult<GetWholeOrderInfo> getWholeOrderInfo(@PathVariable Integer pkId){
        final GetWholeOrderInfo wholeOrderInfo = orderService.getWholeOrder(pkId);
        return BaseResult.success(wholeOrderInfo);
    }
}

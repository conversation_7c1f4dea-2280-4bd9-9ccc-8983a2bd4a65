package cn.shrise.radium.adminapi.controller;

import cn.shrise.radium.adminapi.service.SoftwarePackageService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.req.SoftwarePackageReq;
import cn.shrise.radium.contentservice.resp.SoftwarePackageResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * @Author: tangjiajun
 * @Date: 2024/6/24 10:20
 * @Desc:
 **/
@Api
@RestController
@RequestMapping("software-package")
@RequiredArgsConstructor
public class SoftwarePackageController {

    private final ContentClient contentClient;
    private final SoftwarePackageService softwarePackageService;

    @GetMapping("list")
    @ApiOperation("软件包管理")
    public PageResult<List<SoftwarePackageResp>> packageList(
            @RequestParam @ApiParam("软件包类型：10-国诚智投,20-决策家PC,30-决策家Android") Integer packageType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return contentClient.packageList(packageType,current,size);
    }

    @PostMapping("add")
    @ApiOperation("新增软件包")
    public BaseResult<Void> addPackage(@RequestBody @Valid SoftwarePackageReq softwarePackageReq) {
        return softwarePackageService.addPackage(softwarePackageReq);
    }

    @PostMapping("update-enable")
    @ApiOperation("更新软件包状态")
    public BaseResult<Void> updatePackageEnable(
            @RequestParam Long id,
            @RequestParam @ApiParam("上架/下架") Boolean enabled) {
        return contentClient.updatePackageEnable(id,enabled);
    }

    @GetMapping("info")
    @ApiOperation("软件包详情")
    public BaseResult<SoftwarePackageResp> packageInfo(@RequestParam @ApiParam("id") Long id){
        return contentClient.packageInfo(id);
    }
}

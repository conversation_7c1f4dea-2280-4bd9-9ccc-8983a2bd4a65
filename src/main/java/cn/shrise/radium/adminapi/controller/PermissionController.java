package cn.shrise.radium.adminapi.controller;

import cn.shrise.radium.adminapi.annotation.CheckServerPermission;
import cn.shrise.radium.adminapi.properties.PermissionProperties;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.BaseConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api
@RestController
@RequestMapping("permissions")
@RequiredArgsConstructor
public class PermissionController {

    private final PermissionProperties permissionProperties;

    @ApiOperation("获取权限点")
    @GetMapping()
    public BaseResult<List<BaseConfig>> getPermission() {
        List<BaseConfig> permissionList = permissionProperties.getConfigs();
        return BaseResult.success(permissionList);
    }

    @ApiOperation("校验通话记录权限")
    @PostMapping("call_log/decrypt")
    @CheckServerPermission(type = 70)
    public BaseResult<Void> checkCallLog() {
        return BaseResult.successful();
    }

    @ApiOperation("校验用户查看电子券使用明细权限")
    @PostMapping("customer_gift_coupon_detail/decrypt")
    @CheckServerPermission(type = 80)
    public BaseResult<Void> checkCustomerGiftCouponDetail() {
        return BaseResult.successful();
    }

}

package cn.shrise.radium.adminapi.controller;

import cn.shrise.radium.adminapi.service.AdminAppVersionService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.req.AdminAppVersionReq;
import cn.shrise.radium.contentservice.resp.AdminAppVersionInfoResp;
import cn.shrise.radium.contentservice.resp.AdminAppVersionResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api
@RestController
@RequestMapping("admin-app-version")
@RequiredArgsConstructor
public class AdminAppVersionController {

    private final ContentClient contentClient;
    private final AdminAppVersionService adminAppVersionService;

    @GetMapping("list")
    @ApiOperation("版本管理列表")
    public BaseResult<List<AdminAppVersionResp>> adminAppVersionList(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type) {
        return contentClient.adminAppVersionList(type);
    }

    @PostMapping("create")
    @ApiOperation("新增版本")
    public BaseResult<Void> addAdminAppVersion(@RequestBody AdminAppVersionReq req) {
        return contentClient.addAdminAppVersion(req);
    }

    @PostMapping("update")
    @ApiOperation("编辑版本")
    public BaseResult<Void> updateAdminAppVersion(@RequestBody AdminAppVersionReq req) {
        return contentClient.updateAdminAppVersion(req);
    }

    @ApiOperation("版本上架/下架")
    @PostMapping("set-status")
    public BaseResult<Void> updateAdminAppVersionStatus(@RequestParam @ApiParam("false:下架，true：上架") Boolean enabled,
                                                        @RequestParam @ApiParam("id") Long id) {
        return contentClient.updateAdminAppVersionStatus(enabled, id);
    }

    @ApiOperation("检查最新版本")
    @GetMapping("check")
    public BaseResult<AdminAppVersionInfoResp> checkAdminAppVersion(@RequestParam @ApiParam("应用类型（10：营销助手云控版）") Integer type,
                                                                    @RequestParam @ApiParam("版本号") String version) {
        return adminAppVersionService.checkAdminAppVersion(type, version);
    }

}

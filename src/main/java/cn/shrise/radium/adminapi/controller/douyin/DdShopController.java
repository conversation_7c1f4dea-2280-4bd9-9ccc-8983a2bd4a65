package cn.shrise.radium.adminapi.controller.douyin;

import cn.shrise.radium.adminapi.resp.douyin.DdShopOrderResp;
import cn.shrise.radium.adminapi.service.douyin.DdShopService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.douyinservice.DouYinClient;
import cn.shrise.radium.douyinservice.resp.DouDianShopInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("dd/shop")
@RequiredArgsConstructor
public class DdShopController {

    private final DdShopService ddShopService;
    private final DouYinClient douYinClient;

    @GetMapping("order/list")
    @ApiOperation("获取抖店店铺订单列表")
    public PageResult<List<DdShopOrderResp>> getDdShopOrderList(
            @RequestParam(required = false) @ApiParam("店铺id") Long shopId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer status,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("订单搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return ddShopService.getDdShopOrderList(shopId, status, startTime, endTime, searchContent, current, size);
    }

    @GetMapping("checkAuth")
    @ApiOperation("校验抖店店铺授权")
    public BaseResult<Boolean> checkDdShopAuth(
            @RequestParam @ApiParam("店铺id") Long shopId) {
        return douYinClient.checkDdShopAuth(shopId);
    }

    @GetMapping("list")
    @ApiOperation("获取抖店店铺列表")
    public BaseResult<List<DouDianShopInfo>> getDdShopList() {
        return douYinClient.getDdShopList();
    }
}

package cn.shrise.radium.adminapi.controller.workwx;

import cn.shrise.radium.adminapi.resp.workWx.*;
import cn.shrise.radium.adminapi.service.workwx.WorkWxFriendTransferService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.constant.WwxErrorCode;
import cn.shrise.radium.workwxservice.entity.NpWwxFriendTransferActionRecord;
import cn.shrise.radium.workwxservice.req.CreateFriendTransferReq;
import cn.shrise.radium.workwxservice.req.FilterFriendTransferReq;
import cn.shrise.radium.workwxservice.req.FilterTransferFriendReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("work_wx/friend/transfer")
public class WorkWxFriendTransferController {

    private final WorkWxFriendTransferService wxFriendTransferService;
    private final WorkwxClient workwxClient;

    @ApiOperation("筛选企业微信好友资源继承列表")
    @PostMapping("list")
    public PageResult<List<WwxFriendTransferResp>> filterFriendTransfer(
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @RequestBody @Validated FilterFriendTransferReq req) {
        req.setCompanyType(companyType);
        req.setUserId(userId);
        return wxFriendTransferService.list(req);
    }

    @ApiOperation("根据id获取企业微信好友资源继承详情")
    @GetMapping("info/{id}")
    public BaseResult<WwxFriendTransferInfoResp> getFriendTransferInfo(@PathVariable Integer id) {
        return wxFriendTransferService.getFriendTransferInfo(id);
    }

    @ApiOperation("创建客户资源继承")
    @PostMapping("createFriendTransfer")
    public BaseResult<String> createFriendTransfer(@RequestBody @Validated CreateFriendTransferReq req,
                                                   @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
                                                   @ApiIgnore @RequestHeader(USER_ID) Integer userId) {
        req.setCompanyType(companyType);
        req.setOperatorId(userId);
        return workwxClient.createFriendTransfer(req);
    }

    @ApiOperation("筛选企业微信好友资源")
    @PostMapping("filterFriend")
    public PageResult<List<FriendTransferResp>> filterFriend(@RequestBody @Validated FilterTransferFriendReq req,
                                                             @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType) {
        req.setCompanyType(companyType);
        return wxFriendTransferService.filterFriend(req);
    }

    @ApiOperation("更新申请状态")
    @PostMapping("updateStatus")
    public BaseResult<String> updateStatus(@RequestParam @ApiParam("批次id") Integer batchId,
                                           @RequestParam @ApiParam("状态") Integer status,
                                           @ApiIgnore @RequestHeader(USER_ID) Integer auditorId,
                                           @RequestParam(required = false) @ApiParam("拒绝原因") String refuseReason) {
        return workwxClient.updateStatus(batchId, status, auditorId, refuseReason);
    }

    @ApiOperation("根据批次id获取资源继承客户明细")
    @GetMapping("record/{batchId}")
    public PageResult<List<WwxFriendTransferRecordResp>> getFriendTransferRecordByBatchId(@PathVariable Integer batchId,
                                                                                          @RequestParam(required = false) @ApiParam("页码") Integer current,
                                                                                          @RequestParam(required = false) @ApiParam("分页数量") Integer size) {
        return wxFriendTransferService.getFriendTransferRecordByBatchId(batchId, current, size);
    }

    @ApiOperation("获取资源继承客户列表")
    @GetMapping("customer/list")
    public BaseResult<List<WwxFriendTransferCustomerResp>> getFriendTransferCustomerList(@RequestParam Integer batchId) {
        return wxFriendTransferService.getFriendTransferCustomerList(batchId);
    }

    @ApiOperation("获取批次处理动态")
    @GetMapping("actionRecord")
    public PageResult<List<NpWwxFriendTransferActionRecord>> getActionRecord(@RequestParam @ApiParam("批次id") Integer batchId,
                                                                             @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
                                                                             @RequestParam(defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return workwxClient.getActionRecord(batchId, current, size);
    }

    @ApiOperation("是否上线资源")
    @GetMapping("findOnlineUser")
    public BaseResult<WwxErrorCode> findOnlineUser(@RequestParam @ApiParam("企微账号") String wxAccount,
                                                   @RequestParam @ApiParam("企微公司类型") Integer accountType) {
        return workwxClient.findOnlineUser(wxAccount, accountType);
    }
}

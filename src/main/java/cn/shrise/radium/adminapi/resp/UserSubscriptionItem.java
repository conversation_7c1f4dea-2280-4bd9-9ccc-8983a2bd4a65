package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSubscriptionItem {

    @ApiModelProperty("订阅id")
    private Long subscriptionId;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("服务包等级")
    private Integer level;

    @ApiModelProperty("开通时间")
    private Instant openTime;

    @ApiModelProperty("过期时间")
    private Instant expireTime;
}

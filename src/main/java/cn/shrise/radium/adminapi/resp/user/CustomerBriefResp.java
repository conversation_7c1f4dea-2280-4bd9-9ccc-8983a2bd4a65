package cn.shrise.radium.adminapi.resp.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBriefResp {

    @ApiModelProperty("用户信息")
    private BriefCustomerInfo briefCustomerInfo;

    @ApiModelProperty("认证信息")
    private BriefVerifyInfo briefVerifyInfo;

    @ApiModelProperty("测评信息")
    private BriefEvaluationInfo briefEvaluationInfo;

    @ApiModelProperty("用户订单信息")
    private BriefOrderInfo briefOrderInfo;

    @ApiModelProperty("用户标签信息")
    private BriefTagInfo briefTagInfo;

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BriefCustomerInfo {
        @ApiModelProperty("微信id")
        private String wxId;

        @ApiModelProperty(value = "unionId")
        private String unionId;

        @ApiModelProperty("头像")
        private String avatarUrl;

        @ApiModelProperty("昵称")
        private String nickName;

    }

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BriefTagInfo {
        @ApiModelProperty("客户标签列表")
        private List<String> wwxUnionTagList;
        @ApiModelProperty("服务标签列表")
        private List<String> customerTagList;
        @ApiModelProperty("企微标签列表")
        private List<String> wwxTagList;
    }

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BriefOrderInfo {
        @ApiModelProperty("已完成订单数量")
        private Integer completedOrderCount;
        @ApiModelProperty("已退款订单数量")
        private Integer refundOrderCount;
    }

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BriefEvaluationInfo {

        @ApiModelProperty("等级")
        private String level;

        @ApiModelProperty("过期时间")
        private Instant expireTime;

        @ApiModelProperty("可投资资产数额")
        private String investmentAmount;

    }

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BriefVerifyInfo {

        //todo 打星
        @ApiModelProperty("姓名")
        private String name;

        @ApiModelProperty("性别(1：男 0：女)")
        private Integer sex;

        @ApiModelProperty("身份证所在地")
        private String region;

        @ApiModelProperty("年龄")
        private Integer age;

    }


}

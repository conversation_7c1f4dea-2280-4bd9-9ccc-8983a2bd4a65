package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DeptPerformanceResp {

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID", name ="deptId")
    private Integer deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", name ="deptName")
    private String deptName;

    /**
     * 新增资源
     */
    @ApiModelProperty(value = "新增资源", name ="resourceCount")
    private Long resourceCount;

    /**
     * 新增订单量
     */
    @ApiModelProperty(value = "新增订单量", name ="orderCount")
    private Long orderCount;

    /**
     * 新增订单金额
     */
    @ApiModelProperty(value = "新增订单金额", name ="orderAmount")
    private Long orderAmount;

    /**
     * 新增退款订单量
     */
    @ApiModelProperty(value = "新增退款订单量", name ="refundCount")
    private Long refundCount;

    /**
     * 新增退款金额
     */
    @ApiModelProperty(value = "新增退款金额", name ="refundAmount")
    private Long refundAmount;

    /**
     * 产出率(按订单量)
     */
    @ApiModelProperty(value = "产出率(按订单量)",name = "outputRateOrder")
    private String outputRateOrder;

    /**
     * 产出率(按订单金额)
     */
    @ApiModelProperty(value = "产出率(按订单金额)",name = "outputRateAmount")
    private String outputRateAmount;

    /**
     * 退款率(按订单量)
     */
    @ApiModelProperty(value = "退款率(按订单量)",name = "returnRateOrder")
    private String returnRateOrder;

    /**
     * 退款率(按订单金额)
     */
    @ApiModelProperty(value = "退款率(按订单金额)",name = "returnRateAmount")
    private String returnRateAmount;

}

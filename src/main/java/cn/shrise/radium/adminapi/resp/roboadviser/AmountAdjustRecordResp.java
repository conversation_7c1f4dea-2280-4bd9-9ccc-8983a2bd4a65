package cn.shrise.radium.adminapi.resp.roboadviser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AmountAdjustRecordResp {

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("策略名称")
    private String strategyName;

    @ApiModelProperty("金额(分)")
    private Long adjustAmount;

    @ApiModelProperty("类型")
    private Integer adjustType;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("操作人id")
    private Integer operatorId;

    @ApiModelProperty("操作人")
    private String operatorName;
}

package cn.shrise.radium.adminapi.resp.im;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ImRoomScenePlanResp {

    @ApiModelProperty(value = "计划Id")
    private Long id;

    @ApiModelProperty(value = "直播室Id")
    private Long roomId;

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "循环日期")
    private List<Integer> weekday;

    @ApiModelProperty(value = "直播主题")
    private String title;

    @ApiModelProperty(value = "场次模式(1-横屏, 2-竖屏)")
    private Integer modeType;

    @ApiModelProperty(value = "直播封面")
    private String thumbUrl;

    @ApiModelProperty(value = "预告时间")
    private String preheatTime;

    @ApiModelProperty(value = "预开播时间")
    private String preStartTime;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

    @ApiModelProperty(value = "老师id列表")
    private List<Integer> analystIds;

    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
}

package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class TrackEventStatResp {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

    @ApiModelProperty(value = "统计时间")
    private Long flagTime;

    @ApiModelProperty(value = "应用ID")
    private String appId;

    @ApiModelProperty(value = "页面ID")
    private String pageId;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @ApiModelProperty(value = "pv")
    private Long pv;

    @ApiModelProperty(value = "uv")
    private Long uv;

}

package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkWxContactTagResp {

    private List<String> relationTagList;

    private List<CustomerTagGroup> customTagGroupList;

    private List<String> unionTagList;
}

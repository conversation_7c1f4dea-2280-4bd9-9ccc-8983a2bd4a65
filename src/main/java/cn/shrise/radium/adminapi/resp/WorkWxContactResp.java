package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.entity.WxInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkWxContactResp {

    @ApiModelProperty("好友ID")
    private Integer friendId;

    private Integer companyType;

    private Integer accountType;

    private String wxAccount;

    private String externalUserId;

    @ApiModelProperty("客户昵称")
    private String name;

    @ApiModelProperty("好友备注")
    private String remark;

    @ApiModelProperty("客户头像")
    private String avatar;

    @ApiModelProperty("客户性别 0-未知 1-男性 2-女性")
    private Integer gender;

    private String unionId;

    @ApiModelProperty("产品订单数量")
    private Long orderCount;

    @ApiModelProperty("退款订单数量")
    private Long refundCount;

    @ApiModelProperty("可投资资产数额")
    private String investmentAmount;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("到期时间")
    private Instant expireTime;

    @ApiModelProperty("身份证年龄")
    private Integer idCardAge;

    @ApiModelProperty("身份证地区")
    private String idCardRegion;

    private Integer userId;

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("用户编号")
    private String userNumber;

    private String userRealName;

    private Integer wxId;
}

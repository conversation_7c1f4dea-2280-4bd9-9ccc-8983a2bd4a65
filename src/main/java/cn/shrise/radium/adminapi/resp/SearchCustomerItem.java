package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.entity.WxInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchCustomerItem {

    @ApiModelProperty("用户信息")
    private UserInfo userInfo;
    @ApiModelProperty("用户微信信息")
    private WxInfo wxInfo;
    @ApiModelProperty("unionId")
    private String unionId;
    @ApiModelProperty("已添加的好友关系")
    private List<SearchWorkWxRelationItem> relationList;
}

package cn.shrise.radium.adminapi.resp.roboadviser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CustomerTradeAuthRecordResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("是否交易授权")
    private Boolean isAuth;

    @ApiModelProperty("操作人id")
    private Integer operatorId;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("操作人昵称")
    private String operatorNickName;
}

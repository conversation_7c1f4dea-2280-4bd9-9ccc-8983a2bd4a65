package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class WxTrackInfo {

    @ApiModelProperty("用户id")
    private Integer userId;//用户id

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("头像")
    private String headImg;//头像

    @ApiModelProperty("昵称")
    private String nickName;//昵称

    @ApiModelProperty("产品名称")
    private String action;//产品名称

    @ApiModelProperty("最后访问行为")
    private String label;//最后访问行为

    @ApiModelProperty("最后访问时间")
    private Instant accessTime;//访问时间

    @ApiModelProperty("微信id是查找访问明细的请求参数，页面不展示")
    private Integer wxId;//微信id

    @ApiModelProperty("订单数量")
    private Long orderCount;//订单数量

    @ApiModelProperty("退款数量")
    private Long refundCount;//退款数量

    @ApiModelProperty("归属销售")
    private String saleName;//归属销售
}

package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.UserInfo;
import lombok.*;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferOrderFlowItem {

    private Long id;

    private Instant gmtCreate;

    private Instant gmtModified;

    private Integer companyType;

    private Integer userId;

    private UserInfo userInfo;

    private Integer salesId;

    private UserInfo salesInfo;

    private Integer skuId;

    private String skuName;

    private Integer orderId;

    private String orderNumber;

    private Integer subOrderId;

    private String subOrderNumber;

    private Integer subOrderPayType;

    private Integer subOrderAmount;

    private Integer creatorId;

    private UserInfo creatorInfo;

    private Integer auditorId;

    private UserInfo auditorInfo;

    private Instant auditTime;

    private Integer flowStatus;

}
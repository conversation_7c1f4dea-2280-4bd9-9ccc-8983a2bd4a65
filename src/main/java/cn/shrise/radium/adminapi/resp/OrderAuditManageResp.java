package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.resp.AuditManageResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class OrderAuditManageResp {

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    /**
     * sku等级
     */
    @ApiModelProperty(value = "sku等级")
    private Integer productLevel;

    /**
     * sku名称
     */
    @ApiModelProperty(value = "sku名称")
    private String skuName;

    /**
     * sku价格
     */
    @ApiModelProperty(value = "sku价格")
    private Integer skuPrice;

    /**
     * 订单创建时间
     */
    @ApiModelProperty(value = "订单创建时间")
    private Instant createTime;

    /**
     * 订单支付时间
     */
    @ApiModelProperty(value = "订单支付时间")
    private Instant payTime;

    /**
     * 相关用户id
     */
    @ApiModelProperty(value = "相关用户id")
    private Integer userId;

    @ApiModelProperty(value = "相关用户code")
    private String userCode;

    /**
     * 相关用户编号
     */
    @ApiModelProperty(value = "相关用户编号")
    private String userNumber;

    /**
     * 相关用户名称
     */
    @ApiModelProperty(value = "相关用户名称")
    private String customerNickName;

    /**
     * 相关用户真实姓名
     */
    @ApiModelProperty(value = "相关用户名称")
    private String customerRealName;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private Integer amount;

    /**
     * 销售id
     */
    @ApiModelProperty(value = "销售id")
    private Integer salesId;

    /**
     * 销售名称
     */
    @ApiModelProperty(value = "销售名称")
    private String saleName;

    /**
     * 销售所在部门
     */
    @ApiModelProperty
    private String department;

    /**
     * 订单外呼id
     */
    @ApiModelProperty(value = "订单外呼id")
    private Long callId;

    /**
     * 订单审核状态
     */
    @ApiModelProperty(value = "订单审核状态")
    private Integer auditStatus;

    /**
     * AI任务状态
     */
    @ApiModelProperty(value = "AI任务状态")
    private String taskStatus;

    /**
     * 呼叫结果
     */
    @ApiModelProperty(value = "呼叫结果")
    private String callResult;

    /**
     * 是否线上
     */
    @ApiModelProperty(value = "是否线上")
    private Boolean isOnline;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private Integer payType;

    @ApiModelProperty(value = "回访状态")
    private Boolean feedbackStatus;

    @ApiModelProperty(value = "签字状态")
    private Integer signStatus;

    public OrderAuditManageResp(AuditManageResp manageResp) {
        this.orderId = manageResp.getOrderId();
        this.orderNumber = manageResp.getOrderNumber();
        this.productLevel = manageResp.getProductLevel();
        this.skuName = manageResp.getSkuName();
        this.skuPrice = manageResp.getSkuPrice();
        this.createTime = manageResp.getCreateTime();
        this.payTime = manageResp.getPayTime();
        this.userId = manageResp.getUserId();
        this.userCode = DesensitizeUtil.idToMask(manageResp.getUserId());
        this.customerNickName = null;
        this.amount = manageResp.getAmount();
        this.salesId = manageResp.getSalesId();
        this.saleName = null;
        this.department = null;
        this.callId = manageResp.getCallId();
        this.auditStatus = manageResp.getAuditStatus();
        this.taskStatus = manageResp.getTaskStatus();
        this.callResult = manageResp.getCallResult();
        this.isOnline = manageResp.getIsOnline();
        this.payType = manageResp.getPayType();
        this.feedbackStatus = manageResp.getFeedbackStatus();
        this.signStatus = manageResp.getSignStatus();
    }
}

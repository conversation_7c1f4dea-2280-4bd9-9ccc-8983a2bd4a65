package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.notificationservice.constant.OutCallTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * @titel: OutCallRecordsResp
 * @description: 外呼对话记录
 * @author: 毕国强
 */

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class OutCallRecordsResp {

    @ApiModelProperty("任务Id")
    private Long robotCallJobId;

    @ApiModelProperty("电话号码")
    private String calledPhoneNumber;

    @ApiModelProperty("意向等级")
    private String intentLevel;

    @ApiModelProperty("用户关注点")
    private String customerConcern;

    @ApiModelProperty("用户和AI合成语音")
    private String fullAudioUrl;

    @ApiModelProperty("用户录音")
    private String customerAudioUrl;

    @ApiModelProperty("通话时长")
    private Integer chatDuration;

    @ApiModelProperty("用户属性")
    private String attributes;

    @ApiModelProperty("用户意向分析判断依据")
    private String analysisBasis;

    @ApiModelProperty("对话详情")
    private List<CallDetail> callDetailList;

    @Data
    @ApiModel
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CallDetail implements Serializable {

        @ApiModelProperty("具体对话内容")
        private String text;

        @ApiModelProperty("说话者")
        private String type;

        @ApiModelProperty("对话详情Id")
        private Long callDetailId;
    }
}

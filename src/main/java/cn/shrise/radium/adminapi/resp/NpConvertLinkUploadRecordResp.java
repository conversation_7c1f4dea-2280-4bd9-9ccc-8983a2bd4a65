package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.BaseInfo;
import cn.shrise.radium.adminapi.entity.ContactWayInfo;
import cn.shrise.radium.marketingservice.entity.NpConvertLinkConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NpConvertLinkUploadRecordResp extends BaseInfo {

    @ApiModelProperty("好友id")
    private Integer wwxFriendId;

    @ApiModelProperty("链路详情")
    private NpConvertLinkConfig linkInfo;

    @ApiModelProperty("渠道信息")
    private ContactWayInfo wayInfo;

    @ApiModelProperty("行为")
    private Integer actionType;

    @ApiModelProperty("上报详情")
    private String content;

    @ApiModelProperty("返回结果")
    private String resultCode;
}

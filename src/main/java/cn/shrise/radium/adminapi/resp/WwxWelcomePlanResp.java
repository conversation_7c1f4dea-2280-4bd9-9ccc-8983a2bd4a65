package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WwxWelcomePlanResp {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("应用DZID")
    private List<Long> dzIdList;

    @ApiModelProperty("删除时间")
    private Instant deleteTime;

    @ApiModelProperty("操作人")
    private String operator;
}

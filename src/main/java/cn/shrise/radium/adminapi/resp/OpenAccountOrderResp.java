package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class OpenAccountOrderResp {
    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "wxId")
    private Integer wxId;
    @ApiModelProperty(value = "头像")
    private String headImgUrl;
    @ApiModelProperty(value = "昵称")
    private String nickName;
    @ApiModelProperty(value = "客户手机号")
    private String mobileExt;
    @ApiModelProperty(value = "客户姓名")
    private String name;
    @ApiModelProperty(value = "注册时间")
    private Instant gmtCreate;
    @ApiModelProperty(value = "开户日期")
    private Instant openTime;
    @ApiModelProperty(value = "首次入金日期")
    private Instant depositTime;
    @ApiModelProperty(value = "销户日期")
    private Instant closeTime;
    @ApiModelProperty(value = "当前资产")
    private Long amount;
    @ApiModelProperty(value = "状态")
    private Integer eventType;
    @ApiModelProperty(value = "是否为有效户")
    private Boolean isValid;
    @ApiModelProperty(value = "有效户时间")
    private Instant validTime;
}

package cn.shrise.radium.adminapi.resp.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;


@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class TeamContentAuditResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("内容主id")
    private Long teamId;

    @ApiModelProperty("编号")
    private String teamNumber;

    @ApiModelProperty("创建人Id")
    private Integer creatorId;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty("创建时间")
    private Instant createTime;

    @ApiModelProperty("审核状态")
    private Integer status;

    @ApiModelProperty("审核人Id")
    private Integer auditorId;

    @ApiModelProperty("审核人")
    private String auditorName;

    @ApiModelProperty("审核时间")
    private Instant auditTime;

    @ApiModelProperty("审核原因")
    private String auditRemark;

    @ApiModelProperty("文章详情")
    private ArticleInfo articleInfo;

    @ApiModelProperty("视频详情")
    private VideoInfo videoInfo;

    @ApiModelProperty("观点详情")
    private StreamInfo streamInfo;


    @Data
    @Builder
    @ApiModel
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ArticleInfo {

        @ApiModelProperty(value = "标题")
        private String title;

        @ApiModelProperty(value = "内容")
        private String content;

        private String subtitle;

        private String brief;

        private String titleImageUrl;

        private String titleThumbUrl;

        private String origin;

        private String originUrl;

        private String audioUrl;

        private String videoUrl;

        private String videoThumbUrl;

        private String linkUrl;

        private Integer viewCount;

        private Instant preReleaseTime;

        private Instant releaseTime;

        private String videoId;

        private String wxUrlNumber;

    }


    @Data
    @Builder
    @ApiModel
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VideoInfo {

        @ApiModelProperty(value = "标题")
        private String title;

        @ApiModelProperty(value = "VideoId")
        private String sourceId;

        @ApiModelProperty(value = "视频时长（单位：秒）")
        private String videoDuration;

        @ApiModelProperty(value = "预发布时间")
        private Instant preReleaseTime;

        @ApiModelProperty(value = "发布时间")
        private Instant releaseTime;
    }

    @Data
    @Builder
    @ApiModel
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StreamInfo {
        private Integer messageType;

        @ApiModelProperty("内容")
        private String content;

        @ApiModelProperty("图片地址")
        private String imageUrl;

        @ApiModelProperty("语音地址")
        private String audioUrl;

        @ApiModelProperty("语音时长")
        private Double audioDuration;

        @ApiModelProperty("多图片地址")
        private String imageList;
    }
}

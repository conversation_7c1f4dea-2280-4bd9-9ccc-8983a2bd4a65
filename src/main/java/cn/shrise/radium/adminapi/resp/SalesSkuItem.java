package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.SkuActivityInfo;
import cn.shrise.radium.adminapi.entity.SkuInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesSkuItem {

    private SkuInfo skuInfo;
    private SkuActivityInfo activityInfo;

}

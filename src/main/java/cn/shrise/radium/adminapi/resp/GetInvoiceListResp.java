package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetInvoiceListResp {

    private Integer orderId;

    private String orderNumber;

    private Integer skuId;

    private String skuName;

    private Integer productLevel;

    private Integer amount;

    private Integer userId;

    @ApiModelProperty("用户code")
    private String userCode;

    private String userName;

    private Integer wxId;

    private Integer orderStatus;

    private Instant payTime;

    private Integer salesId;

    private String salesName;

    private Integer payType;

    private Long invoiceId;

    private Integer invoiceStatus;

    private String invoiceRemark;

    private Instant gmtCreate;

    private Boolean isRefund;
}

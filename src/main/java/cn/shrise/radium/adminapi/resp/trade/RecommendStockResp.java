package cn.shrise.radium.adminapi.resp.trade;

import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 * desc: 案例别表
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendStockResp {
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("股票代码")
    private String code;

    @ApiModelProperty("股票名称")
    private String name;

    @ApiModelProperty("成交价格")
    private Double costPrice;

    @ApiModelProperty("最高涨幅")
    private Double maxProfitRatio;

    @ApiModelProperty("是否置顶")
    private Boolean isTop;

    @ApiModelProperty("创建时间")
    private Instant createTime;

    @ApiModelProperty("更新时间")
    private Instant updateTime;

    @ApiModelProperty("买入时间")
    private Instant buyTime;

    @ApiModelProperty("成交时间")
    private Instant costTime;

    @ApiModelProperty("是否清仓")
    private Boolean isClosed;

    @ApiModelProperty("当前仓位")
    private Integer count;

    @ApiModelProperty("价格")
    private Double price;

    @ApiModelProperty("防守位")
    private Double lossPrice;

    @ApiModelProperty("目标位")
    private Double targetPrice;

    @ApiModelProperty("频道ID")
    private Integer typeId;

    @ApiModelProperty("频道名称")
    private String typeName;

    @ApiModelProperty("创建人")
    private UserBaseInfoResp creatorInfo;

    @ApiModelProperty("审核人")
    private UserBaseInfoResp auditorInfo;

    @ApiModelProperty("分析师Id")
    private Integer analystId;

    @ApiModelProperty("分析师")
    private String analystName;

    @ApiModelProperty("简介")
    private String brief;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("审核状态")
    private Integer auditStatus;

    @ApiModelProperty("审核时间")
    private Instant auditTime;

    @ApiModelProperty("审核内容")
    private String auditRemark;

}

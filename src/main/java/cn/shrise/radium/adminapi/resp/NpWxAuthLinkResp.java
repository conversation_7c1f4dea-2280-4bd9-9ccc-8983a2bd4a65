package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NpWxAuthLinkResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("公众号类型")
    private Integer accountType;

    @ApiModelProperty("链接url")
    private String linkUrl;

    @ApiModelProperty("公司类型")
    private Integer companyType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("短链接")
    private String shortUrl;

}

package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.WayTag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WayTagGroupResp {

    @ApiModelProperty("标签组id")
    private Integer id;

    @ApiModelProperty("标签组名称")
    private String groupName;

    @ApiModelProperty("公司类型")
    private Integer companyType;

    @ApiModelProperty("是否可用: 1 可用 0 不可用")
    private Boolean enabled;

    @ApiModelProperty("是否共通")
    private Boolean isPublic;

    @ApiModelProperty("标签对象")
    private List<WayTag> wayTags;
}

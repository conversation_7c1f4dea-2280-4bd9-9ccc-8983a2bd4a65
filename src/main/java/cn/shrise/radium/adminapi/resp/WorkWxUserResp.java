package cn.shrise.radium.adminapi.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkWxUserResp {

    private Long id;

    private Integer accountType;

    private Integer companyType;

    private String wxAccount;

    private String name;

    private String avatar;

    private Boolean enable;

    private String alias;

    private String qrCode;

    private Instant createTime;

    private Instant updateTime;

    private Boolean deleted;

    private Integer belongId;

    private Integer statusId;

    private String inviteUrl;

    private Long roleId;

    private String roleName;

    private String belongName;

    private String corpId;

    private String corpName;

}

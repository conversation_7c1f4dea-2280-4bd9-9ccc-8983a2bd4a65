package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.orderservice.entity.RelevantRefundInfo;
import cn.shrise.radium.orderservice.entity.SubOrderInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class GetWholeOrderInfo {

    @ApiModelProperty("订单号")
    private String orderNumber;//订单号

    @ApiModelProperty("属性")
    private Integer productLevel;//属性（一档、二档。。。）

    @ApiModelProperty("订单类型")
    private Boolean isOnline;//订单类型（是否上线）

    @ApiModelProperty("是否续费")
    private Boolean isRenew;//是否续费

    @ApiModelProperty("SKU名称")
    private String skuName;//SKU名称

    @ApiModelProperty("SKU价格")
    private Integer skuPrice;//SKU价格

    @ApiModelProperty("订单渠道")
    private String orderState;//订单渠道

    @ApiModelProperty("订单状态")
    private Integer orderStatus;//订单状态

    @ApiModelProperty("创建时间")
    private Instant createTime;//创建时间

    @ApiModelProperty("支付时间")
    private Instant payTime;//支付时间

    @ApiModelProperty("支付方式")
    private Integer payType;//支付方式

    @ApiModelProperty("支付金额")
    private Integer amount;//支付金额

    @ApiModelProperty("用户名称")
    private String userName;//用户名称

    @ApiModelProperty("销售名称")
    private String salesName;//销售名称

    @ApiModelProperty("优惠金额")
    private Integer discountAmount;//优惠金额

    @ApiModelProperty("子订单信息")
    private List<SubOrderInfo> subOrderInfoList;//子订单信息

    @ApiModelProperty("相关退款信息")
    private List<RelevantRefundInfo> relevantRefundInfoList;//相关退款信息

    @ApiModelProperty("用戶id")
    private Integer userId;

    @ApiModelProperty("用戶code")
    private String userCode;
}

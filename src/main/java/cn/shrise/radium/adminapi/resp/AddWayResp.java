package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AddWayResp implements Comparable<AddWayResp>{
    private Integer code;
    private String msg;

    @Override
    public int compareTo(@NotNull AddWayResp o) {
        if(this.code>o.code) return 1;
        if(this.code.equals(o.code)) return 0;
        return -1;
    }
}

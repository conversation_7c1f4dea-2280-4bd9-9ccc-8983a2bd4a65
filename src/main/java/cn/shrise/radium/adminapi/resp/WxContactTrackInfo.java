package cn.shrise.radium.adminapi.resp;


import cn.shrise.radium.adminapi.entity.WorkWxUserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class WxContactTrackInfo extends WxTrackInfo{

    @ApiModelProperty("访问次数")
    private Long accessCount;//访问次数

    @ApiModelProperty("pkId")
    private Long pkId;

    @ApiModelProperty("添加企业微信账号")
    private List<WorkWxUserInfo> addAccountList;//添加企业微信账号
}

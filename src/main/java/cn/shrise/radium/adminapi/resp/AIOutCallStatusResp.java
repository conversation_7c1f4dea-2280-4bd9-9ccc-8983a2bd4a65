package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * @titel: AIOutCallStatusResp
 * @description: AI外呼状态 <br>
 * @author: 毕国强 <br>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AIOutCallStatusResp {

    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "状态时间")
    private Instant gmtCreate;
}

package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.entity.WxInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchCustomerAggregateItem {

    private Long id;
    private Integer companyType;
    private Integer userId;
    private String userCode;
    @ApiModelProperty("用户信息")
    private UserInfo userInfo;
    private Integer wxId;
    @ApiModelProperty("微信用户信息")
    private WxInfo wxInfo;
    private String unionId;
    @ApiModelProperty("已添加的好友关系")
    private List<SearchWorkWxRelationItem> relationList;
}

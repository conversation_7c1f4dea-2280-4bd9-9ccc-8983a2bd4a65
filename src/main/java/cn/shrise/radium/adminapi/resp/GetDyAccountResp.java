package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.DouyinConvertTool;
import cn.shrise.radium.adminapi.entity.DouyinUserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Collections;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class GetDyAccountResp {

    @ApiModelProperty("账户ID")
    private Long id;

    @ApiModelProperty("开放平台用户ID")
    private Long dyId;

    @ApiModelProperty("开放平台用户信息")
    private DouyinUserInfo userInfo;

    @ApiModelProperty("应用类型")
    private Integer clientType;

    @ApiModelProperty("用户标识")
    private String openId;

    @ApiModelProperty("refresh_token刷新时间")
    private Instant refreshTime;

    @ApiModelProperty("refresh_token过期时间")
    private Instant expiredTime;

    @ApiModelProperty("refresh_token刷新次数")
    private Integer refreshCount;

    @ApiModelProperty("是否授权")
    private Boolean isActive;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled;

    @ApiModelProperty("转化工具")
    private List<DouyinConvertTool> convertTools = Collections.emptyList();

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("更新时间")
    private Instant gmtModified;
}

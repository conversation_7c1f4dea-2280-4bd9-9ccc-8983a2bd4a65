package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VipPackageItem {

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String number;

    /**
     * 服务包名称
     */
    @ApiModelProperty("服务包名称")
    private String name;

    /**
     * 服务包等级
     */
    @ApiModelProperty("服务包等级")
    private Integer level;

    @ApiModelProperty("服务包时长")
    private Integer period;
}

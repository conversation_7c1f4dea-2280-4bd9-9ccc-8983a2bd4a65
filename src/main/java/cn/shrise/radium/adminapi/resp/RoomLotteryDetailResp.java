package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class RoomLotteryDetailResp {
    @ApiModelProperty("ID")
    private Long id;
    @ApiModelProperty("客户ID")
    private Integer customerId;
    @ApiModelProperty("客户code")
    private String customerCode;
    @ApiModelProperty("客户姓名")
    private String customerName;
    @ApiModelProperty("客户昵称")
    private String nickname;
    @ApiModelProperty("活动ID")
    private Long lotteryId;
    @ApiModelProperty("活动名称")
    private String lotteryName;
    @ApiModelProperty("奖品ID")
    private Long prizeId;
    @ApiModelProperty("奖品名称")
    private String prizeName;
    @ApiModelProperty("直播室ID")
    private Long roomId;
    @ApiModelProperty("直播室名称")
    private String roomName;
    @ApiModelProperty("直播场次id")
    private Long sceneId;
    @ApiModelProperty("直播场次名称")
    private String sceneName;
    @ApiModelProperty("创建时间")
    private Instant gmtCreate;
    @ApiModelProperty("参与时间")
    private Instant joinTime;
    @ApiModelProperty("是否中奖")
    private Boolean isWin;
    @ApiModelProperty("是否已处理")
    private Boolean isHandled;
    @ApiModelProperty("客户地址区划")
    private String region;
    @ApiModelProperty("客户详细地址")
    private String address;
    @ApiModelProperty("抽奖类型")
    private Integer lotteryType;
}

package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.orderservice.entity.RsCourseCatalog;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RsCourseSeriesResp {

    private Integer id;
    private String number;
    private String name;
    private Integer teamId;
    private Integer companyType;
    private String content;
    private String bannerUrl;
    private Boolean isEnabled;
    private Instant createTime;
    private Instant updateTime;
    private Integer categoryType;
    private Integer level;
    private String catalogList;
    private List<RsCourseCatalog> courseCatalogList;
    private String bannerTag;
    private String bannerBrief;
    private String tagColor;
    private String wxUrlNumber;
    private String shortUrl;
}

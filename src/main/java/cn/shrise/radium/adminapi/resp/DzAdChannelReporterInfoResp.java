package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.workwxservice.resp.DzAdTagResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/9/12 16:29
 * @Desc:
 **/
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DzAdChannelReporterInfoResp {

    @ApiModelProperty("dzId")
    private Long dzId;

    @ApiModelProperty("渠道名称")
    private String name;

    @ApiModelProperty("tagList")
    private List<DzAdTagResp> tagList;

    @ApiModelProperty("链路负责人")
    private String managerName;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("最新消费时间")
    private Instant latestCostTime;

    @ApiModelProperty("消费填报人")
    private List<String> reporterList;

}

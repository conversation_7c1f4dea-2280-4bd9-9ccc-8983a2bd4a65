package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * @auther 张涛淼
 */

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WwxUserResp {
    @ApiModelProperty("pkid")
    private Long id;

    @ApiModelProperty("企业微信员工")
    private String name;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("企业微信公司类型")
    private Integer accountType;

    @ApiModelProperty("成员UserID")
    private String wxAccount;

    @ApiModelProperty("头像url")
    private String avatar;

    @ApiModelProperty("部门")
    private String deptName;

    @ApiModelProperty("邀请链接")
    private String inviteUrl;

    @ApiModelProperty("归属销售id")
    private Integer belongId;

    @ApiModelProperty("归属销售")
    private String belongName;

    @ApiModelProperty("是否在职")
    private Boolean enable;

    @ApiModelProperty("删除状态")
    private Boolean deleted;

    @ApiModelProperty("账号状态")
    private Integer status;

    @ApiModelProperty("员工自定义状态")
    private Integer userStatus;

    @ApiModelProperty(value = "离职时间")
    private Instant leaveTime;

    @ApiModelProperty(value = "是否封号")
    private Boolean isBlock;

    @ApiModelProperty(value = "封号类型")
    private Integer blockType;

    @ApiModelProperty(value = "封号时长")
    private Integer duration;

    @ApiModelProperty(value = "封号时长类型")
    private Integer durationType;

    @ApiModelProperty(value = "封号日期")
    private Instant blockTime;
}

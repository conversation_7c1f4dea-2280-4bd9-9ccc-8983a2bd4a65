package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.authservice.entity.AccessToken;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RefreshTokenResp {

    @ApiModelProperty("访问凭证")
    private AccessToken accessToken;

    @ApiModelProperty("老后台token")
    private String token;
}

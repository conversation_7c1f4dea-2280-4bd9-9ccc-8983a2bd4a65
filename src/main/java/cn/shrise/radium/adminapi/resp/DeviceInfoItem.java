package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfoItem {

    private Long id;

    private Instant gmtCreate;

    private Instant gmtModified;

    private Integer companyType;

    private Integer productType;

    private Integer userId;

    private UserInfo userInfo;

    private Integer platform;

    private Integer channel;

    private String deviceToken;

    private String alias;

    private String tag;
}

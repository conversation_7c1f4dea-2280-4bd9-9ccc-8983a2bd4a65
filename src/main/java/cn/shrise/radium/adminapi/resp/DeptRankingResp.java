package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DeptRankingResp {

    @ApiModelProperty(value = "部门ID")
    private Integer departmentId;
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    @ApiModelProperty(value = "人数")
    private Integer number;
    @ApiModelProperty(value = "资源")
    private Long resourceCount;
    @ApiModelProperty(value = "订单量")
    private Long orderCount;
    @ApiModelProperty(value = "订单金额")
    private Long orderAmount;
    @ApiModelProperty(value = "人均订单量")
    private Double averageCount;
    @ApiModelProperty(value = "人均订单金额")
    private Double averageAmount;
    @ApiModelProperty(value = "产出率")
    private String outputRateOrder;
    @ApiModelProperty(value = "产出率")
    private String outputRateAmount;
    @ApiModelProperty(value = "退款订单量")
    private Long refundCount;
    @ApiModelProperty(value = "退款订单金额")
    private Long refundAmount;
    @ApiModelProperty(value = "退款率")
    private String returnRateOrder;
    @ApiModelProperty(value = "退款率")
    private String returnRateAmount;

    public DeptRankingResp(Integer departmentId, Long resourceCount, Long orderCount, Long orderAmount, String outputRateOrder, String outputRateAmount, Long refundCount, Long refundAmount, String returnRateOrder, String returnRateAmount) {
        this.departmentId = departmentId;
        this.resourceCount = resourceCount;
        this.orderCount = orderCount;
        this.orderAmount = orderAmount;
        this.outputRateOrder = outputRateOrder;
        this.outputRateAmount = outputRateAmount;
        this.refundCount = refundCount;
        this.refundAmount = refundAmount;
        this.returnRateOrder = returnRateOrder;
        this.returnRateAmount = returnRateAmount;
    }
}

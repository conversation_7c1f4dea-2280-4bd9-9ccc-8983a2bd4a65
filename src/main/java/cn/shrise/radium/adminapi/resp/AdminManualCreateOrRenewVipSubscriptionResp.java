package cn.shrise.radium.adminapi.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminManualCreateOrRenewVipSubscriptionResp {

    private Long id;

    private Integer userId;

    private String userCode;

    private String number;

    private Integer level;

    private Instant openTime;

    private Instant expireTime;
}

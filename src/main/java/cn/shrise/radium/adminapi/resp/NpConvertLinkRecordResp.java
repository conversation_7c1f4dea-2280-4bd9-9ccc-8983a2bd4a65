package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.BaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NpConvertLinkRecordResp extends BaseInfo {

    @ApiModelProperty("操作人id")
    private Integer operatorId;

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("操作人头像url")
    private String operatorAvatarUrl;

    @ApiModelProperty("操作类型")
    private Integer operateType;

    @ApiModelProperty("链路id")
    private Long linkId;

    @ApiModelProperty("行为类型")
    private Integer actionType;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("渠道id")
    private Integer wayId;

    @ApiModelProperty("渠道名称")
    private String wayName;
}

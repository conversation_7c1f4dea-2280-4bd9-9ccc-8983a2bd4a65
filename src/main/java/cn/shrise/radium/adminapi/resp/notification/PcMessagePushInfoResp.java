package cn.shrise.radium.adminapi.resp.notification;

import cn.shrise.radium.adminapi.resp.VipPackageItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PcMessagePushInfoResp {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "创建时间")
    private Instant gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private Instant gmtModified;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "链接")
    private String linkUrl;

    @ApiModelProperty(value = "附件")
    private String attachment;

    @ApiModelProperty(value = "推送类型")
    private Integer pushType;

    @ApiModelProperty(value = "推送时间")
    private Instant pushTime;

    @ApiModelProperty(value = "推送对象")
    private Integer pushTarget;

    @ApiModelProperty(value = "服务包")
    private List<VipPackageItem> packageList;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private Integer creatorId;

    @ApiModelProperty(value = "创建名称")
    private String creatorName;

    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;
}

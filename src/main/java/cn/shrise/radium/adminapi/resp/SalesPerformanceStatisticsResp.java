package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.statisticsservice.entity.StSalesPerformanceStatistics;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SalesPerformanceStatisticsResp {

    /**
     * 记录id
     */
    @ApiModelProperty(value = "记录id", name = "id")
    private Long id;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id", name = "salesId")
    private Integer salesId;

    /**
     * 员工名
     */
    @ApiModelProperty(value = "员工名", name = "salesName")
    private String salesName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", name = "deptName")
    private String deptName;

    /**
     * 新增资源
     */
    @ApiModelProperty(value = "新增资源", name = "resourceCount")
    private Long resourceCount;

    /**
     * 新增订单量
     */
    @ApiModelProperty(value = "新增订单量", name = "orderCount")
    private Long orderCount;

    /**
     * 新增订单金额
     */
    @ApiModelProperty(value = "新增订单金额", name = "orderAmount")
    private Long orderAmount;

    /**
     * 新增退款订单量
     */
    @ApiModelProperty(value = "新增退款订单量", name = "refundCount")
    private Long refundCount;

    /**
     * 新增退款金额
     */
    @ApiModelProperty(value = "新增退款金额", name = "refundAmount")
    private Long refundAmount;

    /**
     * 是否在职
     */
    @ApiModelProperty("是否在职")
    private Boolean enabled;

    /**
     * 产出率(按订单量)
     */
    @ApiModelProperty(value = "产出率(按订单量)", name = "outputRateOrder")
    private String outputRateOrder;

    /**
     * 产出率(按订单金额)
     */
    @ApiModelProperty(value = "产出率(按订单金额)", name = "outputRateAmount")
    private String outputRateAmount;

    /**
     * 退款率(按订单量)
     */
    @ApiModelProperty(value = "退款率(按订单量)", name = "returnRateOrder")
    private String returnRateOrder;

    /**
     * 退款率(按订单金额)
     */
    @ApiModelProperty(value = "退款率(按订单金额)", name = "returnRateAmount")
    private String returnRateAmount;

    @ApiModelProperty(value = "发送消息数", name = "sendMsgCount")
    private Long sendMsgCount;

    @ApiModelProperty(value = "接收消息数", name = "receiveMsgCount")
    private Long receiveMsgCount;

    @ApiModelProperty(value = "微信语音聊天数量", name = "wxChatCount")
    private Integer wxChatCount;

    @ApiModelProperty(value = "拨打电话数量", name = "callCount")
    private Integer callCount;

    @ApiModelProperty(value = "拨打电话时长", name = "callDuration")
    private Long callDuration;

    public static SalesPerformanceStatisticsResp of(StSalesPerformanceStatistics salesPerformanceStatistics) {
        return new SalesPerformanceStatisticsResp(salesPerformanceStatistics.getId(), salesPerformanceStatistics.getSalesId(), null,
                null,
                salesPerformanceStatistics.getResourceCount(), salesPerformanceStatistics.getOrderCount(),
                salesPerformanceStatistics.getOrderAmount(), salesPerformanceStatistics.getRefundCount(),
                salesPerformanceStatistics.getRefundAmount(), null,
                salesPerformanceStatistics.getOutputRateOrder(), salesPerformanceStatistics.getOutputRateAmount(),
                salesPerformanceStatistics.getReturnRateOrder(), salesPerformanceStatistics.getReturnRateAmount(),
                salesPerformanceStatistics.getSendMsgCount(),
                salesPerformanceStatistics.getReceiveMsgCount(), salesPerformanceStatistics.getWxChatCount(),
                salesPerformanceStatistics.getCallCount(), salesPerformanceStatistics.getCallDuration()
        );
    }
}

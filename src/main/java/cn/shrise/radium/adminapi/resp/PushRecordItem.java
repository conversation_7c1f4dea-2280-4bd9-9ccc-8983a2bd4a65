package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.UserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class PushRecordItem {

    private Long gmtCreate;

    private Long gmtModified;

    private Integer companyType;

    private Integer productType;

    private Integer pushType;

    private Integer msgType;

    private String msgBody;

    private String receiver;

    private Integer userId;

    private UserInfo userInfo;

    private String result;

    private Long batchId;

    @ApiModelProperty("流水号")
    private Long handleId;
}

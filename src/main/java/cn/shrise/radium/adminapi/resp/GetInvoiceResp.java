package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.OrderInfo;
import cn.shrise.radium.adminapi.entity.SkuInfo;
import cn.shrise.radium.adminapi.entity.UserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class GetInvoiceResp {

    private Integer id;

    @ApiModelProperty("开票状态")
    private Integer status;

    @ApiModelProperty("发票抬头")
    private String title;

    @ApiModelProperty("发票代码")
    private String code;

    @ApiModelProperty("发票号码")
    private String number;

    @ApiModelProperty("发票创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("发票开票时间")
    private Instant invoiceTime;

    @ApiModelProperty("发票pdf")
    private String pdfUrl;

    @ApiModelProperty("发票备注")
    private String remark;

    @ApiModelProperty("用户信息")
    private UserInfo userInfo;

    @ApiModelProperty("销售信息")
    private UserInfo salesInfo;

    @ApiModelProperty("sku信息")
    private SkuInfo skuInfo;

    @ApiModelProperty("订单信息")
    private OrderInfo orderInfo;

}

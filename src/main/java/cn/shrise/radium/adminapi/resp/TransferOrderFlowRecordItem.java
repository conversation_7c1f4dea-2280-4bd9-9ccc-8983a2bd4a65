package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferOrderFlowRecordItem {

    private Long id;

    private Instant gmtCreate;

    private Instant gmtModified;

    private Integer subOrderId;

    private Integer operateType;

    private Integer operatorId;

    private UserInfo operatorInfo;

}

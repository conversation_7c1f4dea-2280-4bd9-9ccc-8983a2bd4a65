package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ChatOperateRecordResp {

    @ApiModelProperty("时间")
    private Instant gmtCreate;

    @ApiModelProperty("聊天室id")
    private Long chatId;

    @ApiModelProperty("聊天室")
    private String chatName;

    @ApiModelProperty("操作人id")
    private Integer operatorId;

    @ApiModelProperty("操作账号")
    private String operator;

    @ApiModelProperty("是否在职")
    private Boolean enable;

    @ApiModelProperty("操作内容")
    private String content;

    @ApiModelProperty("操作")
    private Integer operateType;
}

package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.adminapi.entity.BaseInfo;
import cn.shrise.radium.workwxservice.resp.WwxContactWayInfoResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ConvertLinkContactWayRelationResp extends BaseInfo {
    @ApiModelProperty("渠道信息")
    private WwxContactWayInfoResp wayInfo;
}

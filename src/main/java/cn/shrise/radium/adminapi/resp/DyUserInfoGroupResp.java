package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.douyinservice.resp.DyConvertToolResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DyUserInfoGroupResp {

    private Long id;
    private Long accountId;
    @ApiModelProperty("用户昵称")
    private String nickName;
    @ApiModelProperty("头像")
    private String avatar;
    @ApiModelProperty("账号类型")
    private String accountRole;
    @ApiModelProperty("模板ID")
    private Long templateId;
    @ApiModelProperty("营销转化工具")
    private List<DyConvertToolResp> dyConvertToolRespList;
}

package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class LoginVerificationCodeResp {

    @ApiModelProperty("手机号")
    private String mobile;
    private String batchId;
    private String requestId;
    private String requestContext;
}

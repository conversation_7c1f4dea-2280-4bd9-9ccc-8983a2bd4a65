package cn.shrise.radium.adminapi.resp.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SsSalesVideoRecordResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("视频名称")
    private String title;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("视频id")
    private String videoId;

    @ApiModelProperty("上传状态")
    private Boolean enabled;
}

package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminCreateTeamShortVideoResp {

    @ApiModelProperty(value = "内容主id")
    @NotNull
    private Long teamId;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty("封面url")
    private String bannerUrl;
    @ApiModelProperty("阿里云videoId")
    private String sourceId;
}

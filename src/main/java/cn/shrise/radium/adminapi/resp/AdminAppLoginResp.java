package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.entity.RefreshToken;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AdminAppLoginResp {

    @ApiModelProperty("访问凭证")
    private AccessToken accessToken;

    @ApiModelProperty("刷新凭证")
    private RefreshToken refreshToken;

    @ApiModelProperty("用户信息")
    private UserBaseInfoResp userBaseInfo;

    @ApiModelProperty("老后台token")
    private String token;
}

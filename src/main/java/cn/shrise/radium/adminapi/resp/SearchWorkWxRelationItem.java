package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.workwxservice.bean.WorkWxFullContactUserRelation;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContact;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactUserRelation;
import cn.shrise.radium.workwxservice.entity.NpWorkWxUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchWorkWxRelationItem {

    @ApiModelProperty("关系id")
    private Integer id;
    @ApiModelProperty("企业微信员工账号")
    private String wxAccount;
    @ApiModelProperty("企业微信员工名称")
    private String wxAccountName;
    @ApiModelProperty("外部联系人ID")
    private String externalUserId;
    @ApiModelProperty("外部联系人名称")
    private String contactName;
    @ApiModelProperty("企业微信ID")
    private Integer accountType;
    @ApiModelProperty("企业微信名称")
    private String cropName;
    @ApiModelProperty("好友备注")
    private String remark;
    @ApiModelProperty("添加时间")
    private Instant addTime;
    @ApiModelProperty("关系状态")
    private Boolean enabled;
    @ApiModelProperty("聊天计数")
    private Integer chatCount;
    @ApiModelProperty("归属部门")
    private String deptName;
    @ApiModelProperty("归属销售")
    private String salesName;

    public static SearchWorkWxRelationItem of(WorkWxFullContactUserRelation fullContactUserRelation) {
        if (fullContactUserRelation == null) {
            return null;
        }
        SearchWorkWxRelationItemBuilder builder = SearchWorkWxRelationItem.builder();
        if (fullContactUserRelation.getContact() != null) {
            NpWorkWxContact contact = fullContactUserRelation.getContact();
            builder.contactName(contact.getName());
        }
        if (fullContactUserRelation.getWorkWxUser() != null) {
            NpWorkWxUser wxUser = fullContactUserRelation.getWorkWxUser();
            builder.wxAccountName(wxUser.getName())
                    .accountType(wxUser.getAccountType());
        }
        if (fullContactUserRelation.getRelation() != null) {
            NpWorkWxContactUserRelation relation = fullContactUserRelation.getRelation();
            builder.id(relation.getPkId())
                    .wxAccount(relation.getWxAccount())
                    .externalUserId(relation.getExternalUserId())
                    .remark(relation.getRemark())
                    .addTime(relation.getAddTime())
                    .enabled(relation.getContactEnabled() != null && relation.getUserEnabled() != null && (relation.getContactEnabled() && relation.getUserEnabled()));
        }
        builder.cropName(fullContactUserRelation.getCorpName());
        builder.chatCount(fullContactUserRelation.getChatCount());
        return builder.build();
    }
}

package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoomLotteryOperateRecordResp {

    @ApiModelProperty("活动ID")
    private Long lotteryId;
    @ApiModelProperty("操作人ID")
    private Integer operatorId;
    @ApiModelProperty("操作人姓名")
    private String operatorName;
    @ApiModelProperty("操作类型")
    private Integer operateType;
    @ApiModelProperty("活动名称")
    private String lotteryName;
    @ApiModelProperty("操作时间")
    private Instant operateTime;
    @ApiModelProperty("活动状态")
    private Integer lotteryStatus;
    @ApiModelProperty("头像地址")
    private String avatarUrl;
    @ApiModelProperty("活动编号")
    private String number;
    @ApiModelProperty("抽奖类型")
    private Integer lotteryType;
}

package cn.shrise.radium.adminapi.resp;

import cn.shrise.radium.workwxservice.resp.DzAdTagResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NpDzAdChannelResp {

    @ApiModelProperty("DZID")
    private Long id;

    @ApiModelProperty("Way_ID")
    private String dzNumber;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("备注名")
    private String remark;

    @ApiModelProperty("tagList")
    private List<DzAdTagResp> tagList;

    @ApiModelProperty("链路负责人id")
    private Integer managerId;

    @ApiModelProperty("链路负责人")
    private String managerName;

    @ApiModelProperty("链路类型")
    private Integer channelType;

    @ApiModelProperty("去重方案:10-方案一，20-方案二，30-方案三，40-方案四，50-方案五")
    private Integer qrCodeType;

    @ApiModelProperty("状态")
    private Boolean status;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("是否回传")
    private Integer isConvert;

    @ApiModelProperty("回传类型")
    private Integer linkType;

    @ApiModelProperty("wayIdList")
    private List<Integer> wayIdList;

    @ApiModelProperty("linkIdSet")
    private Set<Long> linkIdSet;

    @ApiModelProperty("欢迎语方案ID")
    private Integer welcomePlanId;

    @ApiModelProperty("欢迎语方案名称")
    private String welcomePlanName;

    @ApiModelProperty("welcomeId")
    private Integer welcomeId;
}

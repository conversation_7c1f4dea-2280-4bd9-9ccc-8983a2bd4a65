package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AssignBatchRecordResp {

    @ApiModelProperty("批次ID")
    private Long id;

    @ApiModelProperty("操作时间")
    private Instant gmtCreate;

    @ApiModelProperty("操作人")
    private String operatorName;

    @ApiModelProperty("状态")
    private Integer status;
}

package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DingDingUserInfoResp {

    @ApiModelProperty("钉钉id")
    private Long ddId;

    @ApiModelProperty("钉钉员工的userId")
    private String ddUserId;

    @ApiModelProperty("钉钉员工姓名")
    private String name;

    @ApiModelProperty("钉钉员工工号")
    private String jobNumber;

    @ApiModelProperty("员工账号")
    private String staffUserName;
}

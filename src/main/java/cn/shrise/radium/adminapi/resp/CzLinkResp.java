package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CzLinkResp {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("获客组件id")
    private String instanceId;

    @ApiModelProperty("获客链路名称")
    private String name;

    @ApiModelProperty("获客链路url")
    private String url;

    @ApiModelProperty("销售列表")
    private List<String> userList;

    @ApiModelProperty("企微主体")
    private Integer accountType;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;
}

package cn.shrise.radium.adminapi.config;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 依据Nacos metadata做负载均衡
 */
@Slf4j
public class NacosMetadataDiscoveryClient extends NacosDiscoveryClient {

    private final NacosServiceDiscovery nacosServiceDiscovery;
    private final NacosDiscoveryProperties nacosDiscoveryProperties;
    private final static String VERSION = "version";

    public NacosMetadataDiscoveryClient(NacosServiceDiscovery nacosServiceDiscovery,
                                        NacosDiscoveryProperties nacosDiscoveryProperties) {
        super(nacosServiceDiscovery);
        this.nacosServiceDiscovery = nacosServiceDiscovery;
        this.nacosDiscoveryProperties = nacosDiscoveryProperties;
    }

    @Override
    public List<ServiceInstance> getInstances(String serviceId) {
        try {
            final List<ServiceInstance> originInstances = nacosServiceDiscovery.getInstances(serviceId);
            return filter(originInstances);
        }
        catch (Exception e) {
            throw new RuntimeException(
                    "Can not get hosts from nacos server. serviceId: " + serviceId, e);
        }
    }

    private List<ServiceInstance> filter(List<ServiceInstance> instances) {
        final String version = getVersion();

        if (ObjectUtils.isEmpty(version)) {
            return instances;
        }

        final List<ServiceInstance> filterInstances = instances.stream().filter(serviceInstance -> {
            final String instanceVersion = serviceInstance.getMetadata().get(VERSION);
            if (!ObjectUtils.isEmpty(instanceVersion)) {
                return instanceVersion.equals(version);
            }
            return false;
        }).collect(Collectors.toList());
        return ObjectUtils.isEmpty(filterInstances)? instances: filterInstances;
    }

    private String getVersion() {
        final Map<String, String> metadata = nacosDiscoveryProperties.getMetadata();
        return metadata.get(VERSION);
    }

}

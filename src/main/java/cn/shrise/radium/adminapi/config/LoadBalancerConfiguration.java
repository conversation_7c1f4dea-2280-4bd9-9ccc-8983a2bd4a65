package cn.shrise.radium.adminapi.config;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LoadBalancerConfiguration {

    @Bean
    public NacosDiscoveryClient nacosMetadataDiscoveryClient(
            NacosServiceDiscovery nacosServiceDiscovery, NacosDiscoveryProperties nacosDiscoveryProperties) {
        return new NacosMetadataDiscoveryClient(nacosServiceDiscovery, nacosDiscoveryProperties);
    }

}


package cn.shrise.radium.adminapi.config;

import cn.shrise.radium.adminapi.filter.TokenFilter;
import cn.shrise.radium.adminapi.service.AuthService;
import cn.shrise.radium.common.filter.WebTraceResponseFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
@RequiredArgsConstructor
public class WebMvcConfiguration implements WebMvcConfigurer {

    private final AuthService authService;
    private final Tracer tracer;

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer.defaultContentType(MediaType.ALL);
    }

    //跨域
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();

        config.setAllowCredentials(false);
        config.addAllowedOrigin("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        return new CorsFilter(source);
    }

    @Bean
    public FilterRegistrationBean<TokenFilter> tokenFilterRegistration() {
        FilterRegistrationBean<TokenFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(tokenFilter());
        registration.addUrlPatterns("/*");
        registration.setName("tokenFilter");
        registration.setOrder(1);
        return registration;
    }

    @Bean
    public TokenFilter tokenFilter() {
        return new TokenFilter(authService);
    }

    @Bean
    public WebTraceResponseFilter webTraceResponseFilter() {
        return new WebTraceResponseFilter(tracer);
    }

    @Bean(name = "adminExecutor")
    public Executor adminExecutor() {
        int max = 10000;
        int processors = Runtime.getRuntime().availableProcessors();
        return new ThreadPoolExecutor(processors * 10, processors * max,
                60L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(processors * max));
    }
}

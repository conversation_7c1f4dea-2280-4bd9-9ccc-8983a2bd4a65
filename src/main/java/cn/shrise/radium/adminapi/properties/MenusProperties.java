package cn.shrise.radium.adminapi.properties;

import cn.shrise.radium.adminapi.entity.MenusConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 对应菜单配置属性
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "menus")
@EnableConfigurationProperties
public class MenusProperties {

    @NestedConfigurationProperty
    private Map<Integer, List<MenusConfig>> configs;
}

package cn.shrise.radium.adminapi.properties;

import cn.shrise.radium.adminapi.entity.PageConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 对应权限点配置属性
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "page")
@EnableConfigurationProperties
public class PageProperties {

    @NestedConfigurationProperty
    private List<PageConfig> configs;
}

package cn.shrise.radium.adminapi.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "wwxrobot")
@EnableConfigurationProperties
public class WwxRobotLimitProperties {

    private Boolean forceOut;
    private LimitVersion limitVersion;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LimitVersion {
        private String min;
        private String max;
    }
}

package cn.shrise.radium.adminapi.properties;

import cn.shrise.radium.common.entity.BaseConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "workwx")
@EnableConfigurationProperties
public class WorkWxVerifyProperties {

    private List<VerifyFile> verifyFiles;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerifyFile {
        private String name;
        private String value;
    }
}

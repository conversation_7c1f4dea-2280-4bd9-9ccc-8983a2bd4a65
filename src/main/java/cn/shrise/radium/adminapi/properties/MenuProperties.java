package cn.shrise.radium.adminapi.properties;

import cn.shrise.radium.adminapi.entity.MenuConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 对应菜单配置属性
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "menu")
@EnableConfigurationProperties
public class MenuProperties {

    @NestedConfigurationProperty
    private Map<Integer, List<MenuConfig>> configs;
}

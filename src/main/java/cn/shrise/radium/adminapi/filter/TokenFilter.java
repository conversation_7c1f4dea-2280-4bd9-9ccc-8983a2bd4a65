package cn.shrise.radium.adminapi.filter;

import cn.shrise.radium.adminapi.http.AuthRequestWrapper;
import cn.shrise.radium.adminapi.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public class TokenFilter extends OncePerRequestFilter {

    private final static String AUTHORIZATION_PREFIX = "Bearer ";
    private final AuthService authService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String accessToken = getJwtToken(request);
        if (ObjectUtils.isEmpty(accessToken)) {
            filterChain.doFilter(request, response);
            return;
        }

        Map<String, Object> payload = authService.getTokenPayload(accessToken);
        AuthRequestWrapper wrapperRequest = new AuthRequestWrapper(request, payload);
        filterChain.doFilter(wrapperRequest, response);
    }

    private String getJwtToken(HttpServletRequest request) {
        String authorization = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (ObjectUtils.isEmpty(authorization) || !authorization.startsWith(AUTHORIZATION_PREFIX)) {
            return null;
        }
        return authorization.substring(AUTHORIZATION_PREFIX.length());
    }

}

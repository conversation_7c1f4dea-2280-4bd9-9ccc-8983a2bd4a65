package cn.shrise.radium.adminapi;

import cn.shrise.radium.common.constant.ServiceConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableFeignClients(basePackages = ServiceConstant.BASE_PACKAGE)
@SpringBootApplication(scanBasePackages = ServiceConstant.BASE_PACKAGE)
public class AdminApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminApiApplication.class, args);
    }

}

package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Objects;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AnalystInfo {

    @ApiModelProperty("分析师ID")
    private Integer id;
    @ApiModelProperty("分析师名字")
    private String name;
    @ApiModelProperty("证书号")
    private String certificateNo;
    @ApiModelProperty("职称")
    private String title;
    @ApiModelProperty("简介")
    private String introduce;
    @ApiModelProperty("头像Url")
    private String avatarUrl;

    public static AnalystInfo of(SsAnalystInfo info) {
        if (Objects.isNull(info)) {
            return null;
        }
        return AnalystInfo.builder()
                .id(info.getId())
                .name(info.getName())
                .certificateNo(info.getCertificateNo())
                .title(info.getTitle())
                .introduce(info.getIntroduce())
                .avatarUrl(info.getAvatarUrl())
                .build();
    }
}

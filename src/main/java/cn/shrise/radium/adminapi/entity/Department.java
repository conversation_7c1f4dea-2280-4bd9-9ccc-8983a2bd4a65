package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Department {

    private Integer id;
    private String name;
    private Integer parentId;
    private Boolean enabled;
    private List<Department> children;

    public Department(Integer id, String name, Integer parentId, Boolean enabled) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.enabled = enabled;
    }

}

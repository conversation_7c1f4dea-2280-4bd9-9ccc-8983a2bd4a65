package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.orderservice.entity.RsCourseOrderExt;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.workwxservice.entity.NpDzAdChannel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetail {

    private Integer id;

    private String orderNumber;

    private Integer amount;

    private Integer paidAmount;

    private Integer discountAmount;

    private Integer couponId;

    private Integer skuPrice;

    private Integer userId;

    private String userCode;

    private Integer wxId;

    private Integer orderStatus;

    private Instant payTime;

    private Instant createTime;

    private Boolean isSplit;

    private Integer salesId;

    private Integer payType;

    private SkuInfo skuInfo;

    private UserInfo userInfo;

    private UserInfo salesInfo;

    private RsCourseOrderExt orderExt;

    private NpDzAdChannel dzInfo;

    private String deptName;

    public List<SubOrderInfo> subOrderList;

    @ApiModelProperty("订单回访id")
    public Long feedbackId;

    @ApiModelProperty("订单回访完成时间")
    public Instant feedbackTime;

    @ApiModelProperty("订单回访Pdf地址")
    public String feedbackUrl;

    @ApiModelProperty("订单回访状态0未回访，1已回访，2无需回访，10-标记人工回访，11-人工回访完成，12-人工回访不通过")
    // ArtificialFeedbackConstant
    public Integer feedbackStatus;

    @ApiModelProperty("人工回访信息")
    public ArtificialFeedbackInfo artificialFeedbackInfo;

    @ApiModelProperty("订单渠道")
    public String stateRemark;

    @ApiModelProperty("支付主体名称")
    private String payCompany;

    @ApiModelProperty("支付主体ID")
    private Long payCompanyId;

    @Data
    @Builder
    @ApiModel
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ArtificialFeedbackInfo {

        @ApiModelProperty("id")
        public Long id;

        @ApiModelProperty("回访操作人")
        public String creatorName;

        @ApiModelProperty("回访时间")
        public Instant createTime;

        @ApiModelProperty("是否通过")
        public Boolean isPass;

        @ApiModelProperty("状态操作人")
        public String dealName;

        @ApiModelProperty("状态操作时间")
        public Instant dealTime;

        @ApiModelProperty("备注")
        public String remark;

        @ApiModelProperty("视频链接")
        public String videoUrl;
    }

    public static OrderDetail of(RsCourseOrder order, RsSku sku) {
        if (order == null) return null;
        return OrderDetail.builder()
                .id(order.getId())
                .orderNumber(order.getOrderNumber())
                .amount(order.getAmount())
                .discountAmount(order.getDiscountAmount())
                .skuPrice(order.getSkuPrice())
                .userId(order.getUserId())
                .userCode(DesensitizeUtil.idToMask(order.getUserId()))
                .wxId(order.getWxId())
                .orderStatus(order.getOrderStatus())
                .payTime(order.getPayTime())
                .createTime(order.getCreateTime())
                .isSplit(order.getIsSplit())
                .salesId(order.getSalesId())
                .payType(order.getPayType())
                .skuInfo(SkuInfo.of(sku))
                .payCompanyId(order.getPayCompanyId())
                .build();
    }
}

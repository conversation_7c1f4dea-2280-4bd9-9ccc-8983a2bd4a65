package cn.shrise.radium.adminapi.entity;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxInfo {

    @ApiModelProperty("微信id")
    private Long wxId;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("unionId")
    private String unionId;

    @ApiModelProperty("头像")
    private String headImgUrl;

    @ApiModelProperty("授权账号类型")
    private Integer accountType;

    @ApiModelProperty("账号")
    private String openId;


    public static WxInfo of(UcWxExt wxExt) {
        if (wxExt == null) {
            return null;
        }
        return WxInfo.builder()
                .wxId(ObjectUtil.isNotEmpty(wxExt.getId()) ? Long.valueOf(wxExt.getId()) : null)
                .userId(wxExt.getUserId())
                .userCode(DesensitizeUtil.idToMask(wxExt.getUserId()))
                .nickname(wxExt.getNickname())
                .unionId(wxExt.getUnionId())
                .headImgUrl(wxExt.getHeadImgUrl())
                .build();
    }

    public static WxInfo of(cn.shrise.radium.userservice.entity.UcWxExt wxExt) {
        if (wxExt == null) {
            return null;
        }
        return WxInfo.builder()
                .wxId(ObjectUtil.isNotEmpty(wxExt.getId()) ? Long.valueOf(wxExt.getId()) : null)
                .userId(wxExt.getUserId())
                .userCode(DesensitizeUtil.idToMask(wxExt.getUserId()))
                .nickname(wxExt.getNickname())
                .unionId(wxExt.getUnionId())
                .headImgUrl(wxExt.getHeadImgUrl())
                .build();
    }
}

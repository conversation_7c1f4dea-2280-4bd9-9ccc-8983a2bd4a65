package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.orderservice.entity.CourseSubOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubOrderInfo {

    private Integer id;
    private Integer orderId;
    private Integer amount;
    private String number;
    private Integer status;
    private Integer mchType;
    private String bankType;
    private Integer payType;
    private Instant payTime;
    private Instant createTime;
    private Instant updateTime;
    private Long merchantId;

    public static SubOrderInfo of(CourseSubOrder subOrder) {
        if (subOrder == null) {
            return null;
        }
        return SubOrderInfo.builder()
                .id(subOrder.getId())
                .orderId(subOrder.getOrderId())
                .amount(subOrder.getAmount())
                .number(subOrder.getNumber())
                .status(subOrder.getOrderStatus())
                .mchType(subOrder.getMchType())
                .bankType(subOrder.getBankType())
                .payTime(subOrder.getPayTime())
                .payType(subOrder.getPayType())
                .createTime(subOrder.getCreateTime())
                .updateTime(subOrder.getUpdateTime())
                .merchantId(subOrder.getMerchantId())
                .build();
    }
}

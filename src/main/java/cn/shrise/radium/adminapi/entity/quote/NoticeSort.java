package cn.shrise.radium.adminapi.entity.quote;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NoticeSort {

    private String id;
    private String name;
    private String parentId;
    private Boolean enabled;
    private List<NoticeSort> children;

    public NoticeSort(String id, String name, String parentId, Boolean enabled) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.enabled = enabled;
    }

}

package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DouyinUserInfo {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("Union ID")
    private String unionId;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("账号类型")
    private String accountRole;
}

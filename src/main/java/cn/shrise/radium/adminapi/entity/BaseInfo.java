package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class BaseInfo {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("公司类型")
    private Integer companyType;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("更新时间")
    private Instant gmtModified;
}

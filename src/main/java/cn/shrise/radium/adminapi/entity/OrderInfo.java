package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class OrderInfo {

    private Integer id;

    private String orderNumber;

    private Integer amount;

    private Integer discountAmount;

    private Integer couponId;

    private Integer skuPrice;

    private Integer userId;

    private Integer wxId;

    private Integer orderStatus;

    private Instant payTime;

    private Instant createTime;

    private Boolean isSplit;

    private Integer salesId;

    private Integer payType;

    public static OrderInfo of(RsCourseOrder item) {
        if (item == null) return null;
        return OrderInfo.builder()
                .id(item.getId())
                .orderNumber(item.getOrderNumber())
                .amount(item.getAmount())
                .discountAmount(item.getDiscountAmount())
                .skuPrice(item.getSkuPrice())
                .userId(item.getUserId())
                .wxId(item.getWxId())
                .orderStatus(item.getOrderStatus())
                .payTime(item.getPayTime())
                .createTime(item.getCreateTime())
                .isSplit(item.getIsSplit())
                .salesId(item.getSalesId())
                .payType(item.getPayType())
                .build();
    }
}

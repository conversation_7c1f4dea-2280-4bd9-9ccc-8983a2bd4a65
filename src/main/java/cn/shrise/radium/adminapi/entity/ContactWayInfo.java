package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ContactWayInfo extends BaseInfo{

    @ApiModelProperty("企业微信类型")
    private Integer accountType;

    @ApiModelProperty("企业微信名称")
    private String accountName;

    @ApiModelProperty("渠道名称")
    private String name;

    @ApiModelProperty("渠道备注")
    private String remark;

    @ApiModelProperty("渠道标签")
    private List<String> tags;
}

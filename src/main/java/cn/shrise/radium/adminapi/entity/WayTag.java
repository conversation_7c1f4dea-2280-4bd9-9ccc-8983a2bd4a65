package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WayTag {

    @ApiModelProperty("标签id")
    private Integer id;

    @ApiModelProperty("标签名称")
    private String name;

    @ApiModelProperty("标签组id")
    private Integer groupID;

    @ApiModelProperty("公司类型")
    private Integer companyType;

    @ApiModelProperty("是否可用: 1 可用 0 不可用")
    private Integer enabled;
}

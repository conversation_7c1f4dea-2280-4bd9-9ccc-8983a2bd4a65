package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.orderservice.entity.RsSkuActivity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SkuActivityInfo {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("折扣后价格（单位：分）")
    private Integer discountPrice;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("开始时间")
    private Instant startTime;

    @ApiModelProperty("结束时间")
    private Instant endTime;

    @Nullable
    public static SkuActivityInfo of(@Nullable RsSkuActivity activity) {
        if (activity == null) return null;
        return SkuActivityInfo.builder()
                .name(activity.getName())
                .discountPrice(activity.getDiscountPrice())
                .status(activity.getStatus())
                .startTime(activity.getStartTime())
                .endTime(activity.getEndTime())
                .build();
    }
}

package cn.shrise.radium.adminapi.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MenuConfig {

    private Integer id;

    private String desc;

    private String icon;

    private String type;

    private String name;

    private String url;

    private List<PageConfig> permList;

    private List<MenuConfig> nodeList;
}

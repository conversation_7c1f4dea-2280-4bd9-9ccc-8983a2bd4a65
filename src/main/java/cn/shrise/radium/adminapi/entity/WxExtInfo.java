package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class WxExtInfo {

    @ApiModelProperty("wxId")
    private Integer id;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("用户code")
    private String userCode;;

    @ApiModelProperty("微信昵称")
    private String nickname;

    @ApiModelProperty("微信头像")
    private String headImgUrl;

    @ApiModelProperty("unionId")
    private String unionId;

    @ApiModelProperty("公司")
    private Integer companyType;
}

package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class WorkWxUserInfo {

    private Long id;

    private Integer accountType;

    private String cropName;

    private Integer companyType;

    private String wxAccount;

    private String name;

    private String mobile;

    private Integer gender;

    private String avatar;

    private Boolean enable;

    private Integer status;

    private Boolean deleted;

    private Integer belongId;

    private Integer statusId;

    @ApiModelProperty("当前封号状态")
    private Boolean isBlock;

    @ApiModelProperty("封号报备id")
    private Long blockFlowId;

    @ApiModelProperty("封号报备信息")
    private WwxUserBlockFlowInfo blockFlowInfo;
}

package cn.shrise.radium.adminapi.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@ApiModel("评测信息")
@NoArgsConstructor
@AllArgsConstructor
public class CustomerEvaluation {
    @ApiModelProperty("评测id")
    private Integer id;

    @ApiModelProperty("评测编号")
    private String number;

    @ApiModelProperty("客户id")
    private Integer userId;

    @ApiModelProperty("客户code")
    private String userCode;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private Integer sex;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("测评分数")
    private Integer surveyScore;

    @ApiModelProperty("风险等级")
    private String riskLevel;
}

package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class WwxUserBlockFlowInfo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("提交时间")
    private Instant gmtCreate;

    @ApiModelProperty("企业微信账号id")
    private Long wwxUserId;

    @ApiModelProperty("类型")
    private Integer flowType;

    @ApiModelProperty("封号类型")
    private Integer blockType;

    @ApiModelProperty("封号日期")
    private Instant blockTime;

    @ApiModelProperty("当前承接状态")
    private Integer acceptStatus;

    @ApiModelProperty("封号后状态")
    private Integer blockedStatus;

    @ApiModelProperty("封号时长类型")
    private Integer durationType;

    @ApiModelProperty("封号时长")
    private Integer duration;

    @ApiModelProperty("截图")
    private String imageUrl;

    @ApiModelProperty("解封时间")
    private Instant unblockTime;

    @ApiModelProperty("创建人id")
    private Integer creatorId;

    @ApiModelProperty("创建人姓名")
    private String creatorName;

    @ApiModelProperty("审核状态")
    private Integer auditStatus;

    @ApiModelProperty("审核时间")
    private Instant auditTime;

    @ApiModelProperty("确认状态")
    private Boolean confirmStatus;

    @ApiModelProperty("确认时间")
    private Instant confirmTime;

    @ApiModelProperty("企微账号姓名")
    private String accountName;

    @ApiModelProperty("企微账号")
    private String wxAccount;

    @ApiModelProperty("企微类型")
    private Integer accountType;

    @ApiModelProperty("企微名称")
    private String cropName;

    @ApiModelProperty("企微账号手机号")
    private String mobile;

    @ApiModelProperty("审核人id")
    private Integer auditorId;

    @ApiModelProperty("审核人姓名")
    private String auditorName;

    @ApiModelProperty("商务确认人id")
    private Integer confirmId;

    @ApiModelProperty("商务确认人姓名")
    private String confirmName;
}

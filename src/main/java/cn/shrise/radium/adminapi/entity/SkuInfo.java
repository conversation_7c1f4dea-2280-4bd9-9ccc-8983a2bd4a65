package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.orderservice.entity.RsSku;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SkuInfo {

    @ApiModelProperty("skuId")
    private Integer id;

    @ApiModelProperty("sku编号")
    private String number;

    @ApiModelProperty("后台展示名称")
    private String name;

    @ApiModelProperty("产品级别")
    private Integer productLevel;

    @ApiModelProperty("价格（单位：分）")
    private Integer price;

    @ApiModelProperty("是否线上")
    private Boolean isOnline;

    @ApiModelProperty("是否续费")
    private Boolean isRenew;

    @ApiModelProperty("sku分类")
    private Integer category;

    @ApiModelProperty("上下架状态")
    private Integer status;

    @ApiModelProperty("开始时间")
    private Instant startTime;

    @ApiModelProperty("结束时间")
    private Instant endTime;

    @Nullable
    public static SkuInfo of(@Nullable RsSku sku) {
        if (sku == null) return null;
        return SkuInfo.builder()
                .id(sku.getId())
                .number(sku.getNumber())
                .name(sku.getName())
                .productLevel(sku.getProductLevel())
                .price(sku.getPrice())
                .isOnline(sku.getIsOnline())
                .isRenew(sku.getIsRenew())
                .category(sku.getCategory())
                .status(sku.getStatus())
                .startTime(sku.getStartTime())
                .endTime(sku.getEndTime())
                .build();
    }
}

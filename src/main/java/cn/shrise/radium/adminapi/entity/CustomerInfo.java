package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.adminapi.util.DesensitizedUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.entity.UcUsers;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerInfo {

    @ApiModelProperty("用户id")
    private Integer id;

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("用户账号")
    private String userName;

    @ApiModelProperty("用户编号")
    private String number;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("真实姓名")
    private String realName;

    @ApiModelProperty("头像")
    private String avatarUrl;

    @ApiModelProperty("手机号")
    private String mobile;

    public static CustomerInfo of(UcUsers user) {
        if (user == null) {
            return null;
        }
        return CustomerInfo.builder()
                .id(user.getId())
                .userCode(DesensitizeUtil.idToMask(user.getId()))
                .number(user.getNumber())
                .userName(user.getUserName())
                .nickName(user.getNickName())
                .realName(user.getRealName())
                .avatarUrl(user.getAvatarUrl())
                .mobile(user.getMaskMobile())
                .build();
    }
}

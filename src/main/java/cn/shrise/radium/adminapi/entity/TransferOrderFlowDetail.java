package cn.shrise.radium.adminapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.Instant;
import java.util.List;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferOrderFlowDetail {

    private Long id;

    private Instant gmtCreate;

    private Instant gmtModified;

    private Integer companyType;

    private Integer subOrderId;

    @ApiModelProperty("子订单信息")
    private SubOrderInfo subOrderInfo;

    private List<String> images;

    private Integer creatorId;

    @ApiModelProperty("创建人消息")
    private UserInfo creatorInfo;

    private Integer auditorId;

    @ApiModelProperty("审核人信息")
    private UserInfo auditorInfo;

    private Instant auditTime;

    private Integer flowStatus;

    private Integer mchType;

    private String mchName;

    private String remark;

    @ApiModelProperty("平台类型")
    private Integer platform;

    @ApiModelProperty("渠道类型")
    private Integer channelType;

    @ApiModelProperty("商户名称")
    private String merchantName;
}
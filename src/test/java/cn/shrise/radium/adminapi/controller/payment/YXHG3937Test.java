package cn.shrise.radium.adminapi.controller.payment;

import cn.hutool.core.lang.Assert;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.orderservice.resp.complaint.OrderComplaintWxResp;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.lettuce.core.ScriptOutputType;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class YXHG3937Test {

    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
    }

    @Autowired
    MockMvc mockMvc;

    //商户投诉列表—微信—（投诉时间：2024-11-07 17:51:52、投诉状态：待处理、商户名称：原生微信-上分（可用）（商户ID：69））
    @Test
    public void testOrderComplaintList() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/order-complaint/wx/list")
                        .header("Authorization", token)
                        .param("startTime", "2024-11-07 00:00:00")//投诉时间筛选
                        .param("endTime", "2024-11-07 23:59:59")
                        .param("complaintStatus", "PROCESSED")//投诉状态筛选(PENDING-待处理，PROCESSING-处理中，PROCESSED-已处理完成)
                        .param("merchantId", "78"))//商户名称筛选

                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<OrderComplaintWxResp>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<PageResult<List<OrderComplaintWxResp>>>() {
        });
        //断言接口返回0成功、投诉时间：2024-11-06 11:20:39、投诉状态：待处理、商户名称：通联-总部—【可用】
        Assert.equals(result.getData().get(0).getComplaintState(), "PROCESSED");
        Assert.equals(result.getData().get(0).getMerchantName(), "通联-总部—【可用】");
    }
    //后台登录
    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(128301)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }


}

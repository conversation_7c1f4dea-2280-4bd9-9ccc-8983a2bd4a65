package cn.shrise.radium.adminapi.controller.doudianorder;

import cn.hutool.core.lang.Assert;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.adminapi.resp.douyin.DdShopOrderResp;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.List;


import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class CRM241119Test {

    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
    }

    @Autowired
    MockMvc mockMvc;

    //抖店订单管理列表
    @Test
    public void testDdShopList() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/dd/shop/order/list")
                        .header("Authorization", token)
                        .param("shopId", "22277625")
                        .param("status", "5")//5:已完成
                        .param("startTime", "2024-11-23")
                        .param("endTime", "2024-11-23"))

                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        //断言接口返回0成功
        PageResult<List<DdShopOrderResp>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<PageResult<List<DdShopOrderResp>>>() {
        });
        DdShopOrderResp courseCouponResp = Optional.ofNullable(result)
                .map(PageResult::getData)
                .flatMap(data -> data.stream()
                        .filter(o -> o != null && "6936675418456397437".equals(o.getOrderNumber()))
                        .findFirst()
                ).orElse(new DdShopOrderResp());
        Assert.equals(courseCouponResp.getOrderNumber(), "6936675418456397437");
        Assert.equals(courseCouponResp.getProductName(), "代金卷全国电影通用T【守兔兰生典】");
        Assert.equals(courseCouponResp.getPayAmount(), 20);
        Assert.equals(courseCouponResp.getOrderStatus(), 5);
        Assert.equals(courseCouponResp.getShopName(), "卡券测试店铺");
        Assert.equals(courseCouponResp.getOrderTime().toEpochMilli(), 1732355649000000L);
    }

    //后台登录
    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(128301)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }


}

package cn.shrise.radium.adminapi.controller.order;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.entity.WxInfo;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.adminapi.resp.SearchCustomerItem;
import cn.shrise.radium.adminapi.resp.SearchWorkWxRelationItem;
import cn.shrise.radium.adminapi.resp.order.DeptVisibleOrderResp;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DesensitizeUtil;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class YXHG4095Test {
    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
    }

    @Autowired
    MockMvc mockMvc;

    //订单号搜索
    @Test
    public void testSearchByOrderNumber() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/orders/list/sales/visible")
                        .header("Authorization", token)
                        .param("searchContent", "m1859871685881368576")
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<DeptVisibleOrderResp>> result = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<DeptVisibleOrderResp>>>() {
        });

        Assert.equals(result.getData().get(0).getOrderNumber(), "m1859871685881368576");

    }



    //用户ID搜索
    @Test
    public void testSearchByUserId() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/orders/list/sales/visible")
                        .header("Authorization", token)
                        .param("searchContent", "8YT888ZV")
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<DeptVisibleOrderResp>> result = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<DeptVisibleOrderResp>>>() {
        });
        Assert.equals(result.getData().get(0).getUserCode(), "8YT888ZV");

    }


    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(240)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }

}
package cn.shrise.radium.adminapi.controller.user;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.entity.WxInfo;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.adminapi.resp.SearchCustomerItem;
import cn.shrise.radium.adminapi.resp.SearchWorkWxRelationItem;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DesensitizeUtil;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class YXHG3820Test {

    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
    }

    @Autowired
    MockMvc mockMvc;

    //用户ID搜索
    @Test
    public void testGlobalSearchByUeserId() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        .param("searchType", "10")
                        .param("content", "128902"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });
        Integer userId = Optional.ofNullable(result)
                .map(PageResult::getData)
                .flatMap(data -> data.stream()
                        .filter(Objects::nonNull)
                        .map(SearchCustomerItem::getUserInfo)
                        .map(UserInfo::getId)
                        .findFirst())
                .orElse(null);
        Assert.equals(userId, 128902);
    }

    //关系ID搜索
    @Test
    public void testGlobalSearchByRelationId() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        .param("searchType", "20")
                        .param("content", "106499122"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });
        Set<Integer> set = Optional.ofNullable(result.getData())
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(Objects::nonNull)
                .flatMap(datum -> Optional.ofNullable(datum.getRelationList())
                        .orElseGet(Collections::emptyList)
                        .stream()
                        .filter(Objects::nonNull)
                        .map(SearchWorkWxRelationItem::getId))
                .collect(Collectors.toSet());
        boolean contains = set.contains(106499122);
        Assert.isTrue(contains);
    }

    //真实姓名搜索
    @Test
    public void testGlobalSearchByRealName() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        .param("searchType", "30")
                        .param("content", "何鑫"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });
        String realName = Optional.ofNullable(result)
                .map(PageResult::getData)
                .flatMap(data -> data.stream()
                        .filter(Objects::nonNull)
                        .map(SearchCustomerItem::getUserInfo)
                        .map(UserInfo::getRealName)
                        .findFirst())
                .orElse(null);
        Assert.equals(realName, "何鑫");
    }

    //手机号搜索
    @Test
    public void testGlobalSearchByMaskMobile() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        //.param("companyType", "45")
                        .param("searchType", "40")
                        .param("content", "***********"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });
        String maskMobile = Optional.ofNullable(result)
                .map(PageResult::getData)
                .flatMap(data -> data.stream()
                        .filter(Objects::nonNull)
                        .map(SearchCustomerItem::getUserInfo)
                        .map(UserInfo::getMaskMobile)
                        .findFirst())
                .orElse(null);
        Assert.equals(maskMobile, DesensitizeUtil.mobilePhone("***********"));
    }

    //添加账号搜索
    @Test
    public void testGlobalSearchByWxAccountName() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        //.param("companyType", "45")
                        .param("searchType", "50")
                        .param("content", "吴杰"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });

        Set<String> set = Optional.ofNullable(result)
                .map(PageResult::getData)
                .map(data -> data.stream()
                        .filter(datum -> datum != null && ObjectUtil.isNotEmpty(datum.getRelationList()))
                        .flatMap(datum -> datum.getRelationList().stream())
                        .filter(Objects::nonNull)
                        .map(SearchWorkWxRelationItem::getWxAccountName)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
        boolean allContain = set.stream()
                .filter(StringUtils::isNotBlank)
                .anyMatch(s -> s.contains("吴杰"));
        Assert.isTrue(allContain);
    }

    //备注名搜索
    @Test
    public void testGlobalSearchByRemark() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        .param("searchType", "60")
                        .param("content", "241014#241014#H."))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });
        Set<String> set = Optional.ofNullable(result)
                .map(PageResult::getData)
                .map(data -> data.stream()
                        .filter(datum -> datum != null && ObjectUtil.isNotEmpty(datum.getRelationList()))
                        .flatMap(datum -> datum.getRelationList().stream())
                        .filter(Objects::nonNull)
                        .map(SearchWorkWxRelationItem::getRemark)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
        boolean allContain = set.stream()
                .filter(StringUtils::isNotBlank)
                .anyMatch(s -> s.contains("241014#241014#H."));
        Assert.isTrue(allContain);
    }

    //用户昵称搜索
    @Test
    public void testGlobalSearchByNickName() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        //.param("companyType", "45")
                        .param("searchType", "70")
                        .param("content", "H."))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });
        String nickName = Optional.ofNullable(result)
                .map(PageResult::getData)
                .flatMap(data -> data.stream()
                        .filter(datum -> datum != null)
                        .map(o -> o.getUserInfo())
                        .map(o -> o.getNickName())
                        .findFirst())
                .orElse(null);
        Assert.equals(nickName, "H.");
    }

    //微信昵称搜索
    @Test
    public void testGlobalSearchByWxNickName() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/customer/global-search")
                        .header("Authorization", token)
                        //.param("companyType", "45")
                        .param("searchType", "80")
                        .param("content", "H."))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<SearchCustomerItem>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<PageResult<List<SearchCustomerItem>>>() {
        });
        String nickName = Optional.ofNullable(result)
                .map(PageResult::getData)
                .flatMap(data -> data.stream()
                        .filter(datum -> datum != null)
                        .map(o -> o.getWxInfo())
                        .map(o -> o.getNickname())
                        .findFirst())
                .orElse(null);
        Assert.equals(nickName, "H.");
    }

    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(128301)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }


}

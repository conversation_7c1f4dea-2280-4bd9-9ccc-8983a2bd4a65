package cn.shrise.radium.adminapi.controller.payment;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.orderservice.entity.RsMerchantInfo;
import cn.shrise.radium.orderservice.resp.payment.UnionPayConfigInfo;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;
import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class Crm240903Test {

    private static ObjectMapper mapper = new ObjectMapper();
    private static final Long MERCHANTID = 46L;

    static {
        mapper.registerModule(new JavaTimeModule());
    }

    @Autowired
    MockMvc mockMvc;

    @Test
    public void testSetMerchantInfoName() throws Exception {
        String token = getAuthorization();
        RsMerchantInfo merchantInfoBefore = getMerchantInfo(token);
        String name = "T" + merchantInfoBefore.getName();
        setPaymentMerchantName(token, merchantInfoBefore.getId(), name);
        RsMerchantInfo merchantInfoAfter = getMerchantInfo(token);
        Assert.equals(name, merchantInfoAfter.getName());
    }

    @Test
    public void testSetMerchantInfoWeight() throws Exception {
        String token = getAuthorization();
        RsMerchantInfo merchantInfoBefore = getMerchantInfo(token);
        Integer weight = 1 + merchantInfoBefore.getWeight();
        setPaymentMerchantWeight(token, merchantInfoBefore.getId(), weight);
        RsMerchantInfo merchantInfoAfter = getMerchantInfo(token);
        Assert.equals(weight, merchantInfoAfter.getWeight());
    }

    @Test
    public void testSetMerchantInfoEnabled() throws Exception {
        String token = getAuthorization();
        RsMerchantInfo merchantInfoBefore = getMerchantInfo(token);
        Boolean enabled = !merchantInfoBefore.getEnabled();
        setPaymentMerchantEnabled(token, merchantInfoBefore.getId(), enabled);
        RsMerchantInfo merchantInfoAfter = getMerchantInfo(token);
        Assert.equals(enabled, merchantInfoAfter.getEnabled());
    }

    @Test
    public void testSetMerchantInfoConfig() throws Exception {
        String token = getAuthorization();
        RsMerchantInfo merchantInfoBefore = getMerchantInfo(token);
        if (StringUtils.isBlank(merchantInfoBefore.getConfigInfo())) {
            merchantInfoBefore.setConfigInfo("{\"cusId\":\"56216106211BW32\",\"appId\":\"00255029\",\"signType\":\"SM2\",\"validTime\":\"60\",\"privateKey\":\"MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCsmTdW2eRsCDVMiZCtgyAH2L3ZLTziDweahLUdqFKIwbK5wG9e2u7nLNARkRS4i/1Qa4xqC6gD0d0jL1P2pNvGL5Qz/VvxftGsnBnxoR8urSIQl7n/8MCHm7ZigFk5apj6mjnEbhk3xPsnOLmLB2UNbqsEHMAHzWxmbCTWAhBg4pVnnAdERJY4xSPEcd0QrppgiiP4iWuwVHG0pZsQw/evLAeWUEJQwGEm8C7E55Dg85VYi3BWXt4mP/iF4m7RM/62FdKd077me15hn6Xc59PFO9wpXqVB4RPhSKXAxUwvdtFpbiXitceT6Mjq/k1+4q4VTaHRGUvtYMfweb8lyxxfAgMBAAECggEAH7YS1dE4x6SGbmaD/20tEGQBFYrbOcl7Iyeowycu6tNzy+8QR0RASpcQL3oRbYrC4uivprg3iWQglhtW+OT3QCVzvE2be/ZJ/cW8eomY2hGTRasq0Fxic0fnrZdZTVRJgRme7XkxBu8ZGe6xIFsedOZVY28I1SYn+XV7GxrL+ZC50JwyJunR9bwx6M0cFu9m7P08WW24BKSGitPjV+FaehtTVUQCNd9NGAYMnJyBZsbLYoZ1lJHu8y2RZpRUSpKMrwphvto+E3y/yreqZQEo3YpxPTNFzwiUjKpzL4jfDB2NepiOh1kDPOUtaxxnLY1dRKpDOJz/vYZaPPf0LGfZKQKBgQDyIxZO3rqb08kowpatQYGjlnE38/dOZsGp6vo2/+3CuT09ra5EsJffQDgmFpdUEmj1tBLW00TnxZSjFaoLm72IRkpIENfVUFA4ti7j301boOAvqK2Svq2zko2yVGS363ub7+WLrvrmTthICN8j/l0E/shZLgDSXnS6HWIMKovn9QKBgQC2eu2gWTVXjAlyMdzIm+Jn3eM7Q3rNnatKQdNmXenHxvjbX1hgRxuKBBOs3PpoaA+QM03B0l8iNvy/+PmLLlgDCtkRS8caNy3yl3IYRgbb7ME9qDRV7HRAXmZAXbo5HwnonTrDqZZ4HcmB4FxjA17B2yZFqcm9LWsnzF6/1iWCgwKBgBWpa+p8ZIdiSAG8fsxbKuTepZxS2BIMgVDZM69N8BBWkBL/gbKldcDENwG5Tap4xykMBg9v8R8m/ugkHQVS1n7lgum1kAmGWNbp/YHnTT1gRA4fcf/JzTJebwzAHg6SI4nMyWVYrxuBfDndiVDoBZxXysanpe0sBBdOX6IlAEUtAoGAUDtmZX0zreV9db8ksvOPLVnrAfCeeu9bE9AzcavTESqT2mhmhBZfrmQyfmu+kc9HIIDeLF0hxPpmUMXYenCK8/N+E1O9G/Ks+h/KY4/Ojj3fjQt6z1iimHapAJ31Ng5GngcbglH7PZ7jLX6HQGWH8TY48qI/eDUTCzGZa0Ftxp8CgYEAnxlulfQn1o2UBejWAuqlZFBhaiyC42H8WsxHxGcF+6HvFCfMeGQ7DLhs6WoJ+K8LTRmlLm3uGVOsBuXamZx8iTHz54RC3mNieDPt5e6hnMxYq2RBx5hRITGQyTJDVTiY/EP33CfpqwAW2XSueuR8fLhMJNq2noiEMewKjKMjE2g=\",\"publicKey\":\"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCm9OV6zH5DYH/ZnAVYHscEELdCNfNTHGuBv1nYYEY9FrOzE0/4kLl9f7Y9dkWHlc2ocDwbrFSm0Vqz0q2rJPxXUYBCQl5yW3jzuKSXif7q1yOwkFVtJXvuhf5WRy+1X5FOFoMvS7538No0RpnLzmNi3ktmiqmhpcY/1pmt20FHQQIDAQAB\",\"sm2PrivateKey\":\"MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgWAjVrh5/3PTebbecnI0amP0z26EgqjyFI7AJxuH8kQOgCgYIKoEcz1UBgi2hRANCAATz3PUc1UFjIzAJQkvCVkhdx4BAaGcwG6cIm2bComwqWNwd7SFz40WF/6Pjewwnlm+eO3dDT4TRVE8jfr7V0NVd\",\"tlPublicKey\":\"MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEBQicgWm0KAMqhO3bdqMUEDrKQvYg8cCXHhdGwq7CGE6oJDzJ1P/94HpuVdBf1KidmPxr7HOH+0DAnpeCcx9TcQ==\",\"url\":\"https://syb.allinpay.com/apiweb\"}");
        }
        UnionPayConfigInfo unionPayConfigInfoBefore = mapper.readValue(merchantInfoBefore.getConfigInfo(), UnionPayConfigInfo.class);
        unionPayConfigInfoBefore.setAppId(unionPayConfigInfoBefore.getAppId() + "1");
        String configInfo = mapper.writeValueAsString(unionPayConfigInfoBefore);
        setPaymentMerchantConfig(token, merchantInfoBefore.getId(), configInfo);
        RsMerchantInfo merchantInfoAfter = getMerchantInfo(token);
        UnionPayConfigInfo unionPayConfigInfoAfter = mapper.readValue(merchantInfoAfter.getConfigInfo(), UnionPayConfigInfo.class);
        Assert.equals(unionPayConfigInfoBefore.getAppId(), unionPayConfigInfoAfter.getAppId());
    }

    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(128702)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }

    public RsMerchantInfo getMerchantInfo(String token) throws Exception {
        RsMerchantInfo merchantInfo = null;
        PageResult<List<RsMerchantInfo>> pageResult;
        String authorization = "Bearer " + token;
        int current = 1;
        do {
            MockHttpServletResponse response = mockMvc.perform(get("/payment/merchant/list")
                            .header("Authorization", authorization)
                            .param("payCompanyId", "1000")
                            .param("platform", "40")
                            .param("current", Integer.toString(current))
                            .param("size", "100"))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                    .andReturn()
                    .getResponse();
            current++;
            pageResult = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<RsMerchantInfo>>>() {
            });
            merchantInfo = Optional.ofNullable(pageResult)
                    .map(PageResult::getData)
                    .flatMap(data -> data.stream()
                            .filter(datum -> datum != null && MERCHANTID.equals(datum.getId()))
                            .findFirst())
                    .orElse(null);
        } while (merchantInfo == null && pageResult != null && ObjectUtil.isNotEmpty(pageResult.getData()));

        if (merchantInfo == null) {
            throw new BusinessException("数据不存在");
        }
        return merchantInfo;
    }

    public void setPaymentMerchantName(String token, Long merchantInfoId, String name) throws Exception {
        String authorization = "Bearer " + token;
        mockMvc.perform(post("/payment/merchant/set-name")
                        .header("Authorization", authorization)
                        .param("id", String.valueOf(merchantInfoId))
                        .param("name", name))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    public void setPaymentMerchantWeight(String token, Long merchantInfoId, Integer weight) throws Exception {
        String authorization = "Bearer " + token;
        mockMvc.perform(post("/payment/merchant/set-weight")
                        .header("Authorization", authorization)
                        .param("id", String.valueOf(merchantInfoId))
                        .param("weight", String.valueOf(weight)))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    public void setPaymentMerchantEnabled(String token, Long merchantInfoId, Boolean enabled) throws Exception {
        String authorization = "Bearer " + token;
        mockMvc.perform(post("/payment/merchant/set-enabled")
                        .header("Authorization", authorization)
                        .param("id", String.valueOf(merchantInfoId))
                        .param("enabled", String.valueOf(enabled)))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void setPaymentMerchantConfig(String token, Long merchantInfoId, String configInfo) throws Exception {
        String authorization = "Bearer " + token;
        mockMvc.perform(post("/payment/merchant/set-config")
                        .header("Authorization", authorization)
                        .param("id", String.valueOf(merchantInfoId))
                        .param("config", configInfo))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());

    }
}

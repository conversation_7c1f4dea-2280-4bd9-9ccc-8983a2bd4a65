package cn.shrise.radium.adminapi.controller.workwx;

import cn.hutool.core.lang.Assert;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.workwxservice.resp.WorkWxRobotWorkWxInfoResp;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc

public class HTML2380Test {
    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
    }
    @Autowired
    MockMvc mockMvc;

//企业筛选默认全部

    @Test
    public void testaccountType_all() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/work-wx-robot/my-account-list")
                        .header("Authorization", token)
                        .param("isAvailable","true")
                        .param("accountType", "")
                                .characterEncoding(StandardCharsets.UTF_8.name())
                                .accept(MediaType.APPLICATION_JSON)
                        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<List<WorkWxRobotWorkWxInfoResp>> result = mapper.readValue(response.getContentAsString(),new TypeReference<BaseResult<List<WorkWxRobotWorkWxInfoResp>>>(){
        });
        List<WorkWxRobotWorkWxInfoResp> respList = result.getData();

        for (WorkWxRobotWorkWxInfoResp e:respList) {
            Assert.equals(e.getSaleName(),"胡测试");
            Assert.equals(e.getIsAvailable(),true);
        }
    }

    //企业筛选切换为前路咨询

    @Test
    public void testaccountType_224() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/work-wx-robot/my-account-list")
                        .header("Authorization", token)
                        .param("isAvailable","true")
                        .param("accountType", "224")
                        .characterEncoding(StandardCharsets.UTF_8.name())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<List<WorkWxRobotWorkWxInfoResp>> result = mapper.readValue(response.getContentAsString(),new TypeReference<BaseResult<List<WorkWxRobotWorkWxInfoResp>>>(){
        });
        List<WorkWxRobotWorkWxInfoResp> respList = result.getData();
        for (WorkWxRobotWorkWxInfoResp e:respList) {
            Assert.equals(e.getSaleName(),"胡测试");
            Assert.equals(e.getAccountType(),224);
            Assert.equals(e.getIsAvailable(),true);
        }

    }

    //企业筛选切换为-前路咨询管理
    @Test
    public void testaccountType_225() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/work-wx-robot/my-account-list")
                        .header("Authorization", token)
                        .param("isAvailable","true")
                        .param("accountType", "225"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<List<WorkWxRobotWorkWxInfoResp>> result = mapper.readValue(response.getContentAsString(),new TypeReference<BaseResult<List<WorkWxRobotWorkWxInfoResp>>>(){
        });
        List<WorkWxRobotWorkWxInfoResp> respList = result.getData();
        for (WorkWxRobotWorkWxInfoResp e:respList) {
            Assert.equals(e.getSaleName(),"胡测试");
            Assert.equals(e.getAccountType(),225);
            Assert.equals(e.getIsAvailable(),true);
        }

    }

    //获取token
    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(128258)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }
}

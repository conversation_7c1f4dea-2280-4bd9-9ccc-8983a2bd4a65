package cn.shrise.radium.adminapi.controller.trade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.adminapi.resp.douyin.DdShopOrderResp;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.tradeservice.entity.TdStockDiagnoseInfo;
import cn.shrise.radium.tradeservice.resp.StockDiagnoseResp;
import cn.shrise.radium.workwxservice.resp.WorkWxRobotWorkWxInfoResp;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.aliyun.openapiutil.Client.getAuthorization;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc

public class HTML2639Test {
    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
    }

    @Autowired
    MockMvc mockMvc;

    //时间筛选-当天
    @Test
    public void time() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/stock-diagnose/list")
                        .header("Authorization", token)
                        .param("startTime", "2024-12-09")
                        .param("endTime", "2024-12-09"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<StockDiagnoseResp>> result = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<StockDiagnoseResp>>>() {
        });
        List<StockDiagnoseResp> respList = result.getData();
        for (StockDiagnoseResp e : respList) {
            Assert.isTrue(e.getGmtCreate().toEpochMilli() >= 1733673600000000L);
            Assert.isTrue(e.getGmtCreate().toEpochMilli() <= 1733759999000000l);
        }
    }
    //搜索-股票代码精确搜索
    @Test
    public void searchCode() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/stock-diagnose/list")
                        .header("Authorization", token)
                        .param("searchContent", "600000.SH"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        PageResult<List<StockDiagnoseResp>> result = mapper.readValue(response.getContentAsString(), new TypeReference<PageResult<List<StockDiagnoseResp>>>() {
        });
        List<StockDiagnoseResp> respList = result.getData();
        for (StockDiagnoseResp e : respList) {
            Assert.equals(e.getCode(),"600000.SH");
        }
    }



    //获取token
        public String getAuthorization() throws Exception {
            GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                    .companyType(45)
                    .userId(128258)
                    .build();
            MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(mapper.writeValueAsString(req)))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                    .andReturn()
                    .getResponse();
            BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
            });
            String token = Optional.ofNullable(baseResult)
                    .map(BaseResult::getData)
                    .map(GenerateTokenResp::getAccessToken)
                    .map(AccessToken::getValue)
                    .orElse(null);
            Assert.notBlank(token);
            return token;
        }
    }
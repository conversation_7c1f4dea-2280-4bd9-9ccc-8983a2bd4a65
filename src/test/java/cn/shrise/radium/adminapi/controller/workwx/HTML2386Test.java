package cn.shrise.radium.adminapi.controller.workwx;

import cn.hutool.core.lang.Assert;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc

public class HTML2386Test {
    private static ObjectMapper mapper = new ObjectMapper();
    static {
      mapper.registerModule(new JavaTimeModule());
    }
    @Autowired
    MockMvc mockMvc;
    //创建文字类型群发
    @Test
    public void testCreatTextMsg() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(post("/work-wx-robot/message-send")
                .content("{\"wxAcountList\": [{\"accountType\": 224, \"tagIdList\": [\"1679312\"], \"wxAccount\": \"ZangHuZhongXinZaiChuFangXiaGu\"}], \"messageList\": [{ \"type\":\"0\",\"content\": \"Niatest文本消息\"}], \"startTime\": \"2024-01-01\", \"endTime\": \"2024-10-31\"}")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<Void> result = mapper.readValue(response.getContentAsString(),new TypeReference<BaseResult<Void>>(){
        });
        Assert.equals(result.getCode(),0);
    }

    //创建图片类型群发
    @Test
    public void testCreatPicMsg() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(post("/work-wx-robot/message-send")
                .content("{\"wxAcountList\": [{\"accountType\": 224, \"tagIdList\": [\"1679312\"], \"wxAccount\": \"ZangHuZhongXinZaiChuFangXiaGu\"}], \"messageList\": [{ \"type\":\"14\",\"content\": \"upload/image/2024/10/29/*************.jpg\"}], \"startTime\": \"2024-01-01\", \"endTime\": \"2024-10-31\"}")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<Void> result = mapper.readValue(response.getContentAsString(),new TypeReference<BaseResult<Void>>(){
        });
        Assert.equals(result.getCode(),0);
    }

    //获取token
    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(128263)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                .contentType(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }
}

package cn.shrise.radium.adminapi.controller.index;

import cn.hutool.core.lang.Assert;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.adminapi.resp.douyin.DdShopOrderResp;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.resp.index.QuoteIndexResp;
import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class CRM241217Test {

    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
    }

    @Autowired
    MockMvc mockMvc;

    //分时线主图列表列表
    @Test
    public void testDdShopList() throws Exception {
        String token = "Bearer " + getAuthorization();
        MockHttpServletResponse response = mockMvc.perform(get("/quote-index/list")
                        .header("Authorization", token)
                        .param("type", "10") //10-分时线图
                        .param("position", "1")// 1-主图
                        .param("enabled", "")) //上下架

                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        //断言接口返回0成功
        BaseResult<List<QuoteIndexResp>> result = mapper.readValue(response.getContentAsString(StandardCharsets.UTF_8), new TypeReference<BaseResult<List<QuoteIndexResp>>>() {
        });
        QuoteIndexResp quoteIndexResp = Optional.ofNullable(result)
                .map(BaseResult::getData)
                .flatMap(data -> data.stream()
                        .filter(o -> o != null && "fen001".equals(o.getNumber()))
                        .findFirst()
                ).orElse(new QuoteIndexResp());
        Assert.equals(quoteIndexResp.getName(), "六脉神剑");
    }

    //后台登录
    public String getAuthorization() throws Exception {
        GenerateAdminTokenReq req = GenerateAdminTokenReq.builder()
                .companyType(45)
                .userId(128988)
                .build();
        MockHttpServletResponse response = mockMvc.perform(post("/auth/token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(result -> StringUtils.isNotBlank(result.getResponse().getContentAsString()))
                .andReturn()
                .getResponse();
        BaseResult<GenerateTokenResp> baseResult = mapper.readValue(response.getContentAsString(), new TypeReference<BaseResult<GenerateTokenResp>>() {
        });
        String token = Optional.ofNullable(baseResult)
                .map(BaseResult::getData)
                .map(GenerateTokenResp::getAccessToken)
                .map(AccessToken::getValue)
                .orElse(null);
        Assert.notBlank(token);
        return token;
    }


}

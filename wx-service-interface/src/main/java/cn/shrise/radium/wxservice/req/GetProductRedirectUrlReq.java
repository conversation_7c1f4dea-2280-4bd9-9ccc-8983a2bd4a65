package cn.shrise.radium.wxservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class GetProductRedirectUrlReq {

    @NotNull
    @ApiModelProperty("产品类型")
    private Integer productType;

    @NotNull
    @ApiModelProperty("账号类型")
    private Integer accountType;

    @ApiModelProperty("跳转路径")
    private String path;

    @ApiModelProperty("跳转参数")
    private Map<String, Object> params;
}

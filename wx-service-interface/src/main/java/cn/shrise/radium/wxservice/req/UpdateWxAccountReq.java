package cn.shrise.radium.wxservice.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateWxAccountReq {

    private Integer wxId;

    private String accessToken;

    private String refreshToken;

}

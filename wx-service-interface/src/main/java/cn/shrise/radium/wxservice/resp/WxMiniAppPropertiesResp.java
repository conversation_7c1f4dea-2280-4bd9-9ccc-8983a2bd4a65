package cn.shrise.radium.wxservice.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tan<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/4 10:46
 * @Desc:
 **/
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class WxMiniAppPropertiesResp {

    @ApiModelProperty("小程序类型")
    private Integer accountType;

    @ApiModelProperty("小程序名称")
    private String name;

}

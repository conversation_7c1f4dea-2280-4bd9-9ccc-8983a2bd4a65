package cn.shrise.radium.complianceservice.repository;

import cn.shrise.radium.complianceservice.entity.RsOrderSensitiveRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RsOrderSensitiveRecordRepository extends JpaRepository<RsOrderSensitiveRecord, Long> , QuerydslPredicateExecutor<RsOrderSensitiveRecord> {
    RsOrderSensitiveRecord findByUserIdAndOrderId(Integer userId, Integer orderId);
}
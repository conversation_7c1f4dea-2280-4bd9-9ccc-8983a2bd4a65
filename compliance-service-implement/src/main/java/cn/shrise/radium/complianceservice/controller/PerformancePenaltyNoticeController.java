package cn.shrise.radium.complianceservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.complianceservice.dao.PerformancePenaltyNoticeDao;
import cn.shrise.radium.complianceservice.dao.PerformancePenaltyNoticeRecordDao;
import cn.shrise.radium.complianceservice.entity.RsPerformancePenaltyNotice;
import cn.shrise.radium.complianceservice.entity.RsPerformancePenaltyNoticeRecord;
import cn.shrise.radium.complianceservice.req.*;
import cn.shrise.radium.complianceservice.resp.PerformancePenaltyNoticeResp;
import cn.shrise.radium.complianceservice.service.PerformancePenaltyNoticeService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: tangjiajun
 * @Date: 2025/5/28 19:11
 * @Desc:
 **/
@RestController
@RequestMapping("performance-penalty-notice")
@RequiredArgsConstructor
public class PerformancePenaltyNoticeController {

    private final PerformancePenaltyNoticeService performancePenaltyNoticeService;
    private final PerformancePenaltyNoticeDao performancePenaltyNoticeDao;
    private final PerformancePenaltyNoticeRecordDao performancePenaltyNoticeRecordDao;

    @PostMapping("list")
    @ApiOperation("业绩处罚单列表")
    public PageResult<List<PerformancePenaltyNoticeResp>> performancePenaltyNoticeList(
            @RequestBody @Valid PerformancePenaltyNoticeReq req,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return performancePenaltyNoticeDao.performancePenaltyNoticeList(req.getNoticeId(), req.getSalesId(), req.getCreatorId(), req.getStatusList(),
                req.getSalesList(), req.getCreatorList(), req.getStartDate(), req.getEndDate(), current, size);
    }

    @GetMapping("detail")
    @ApiOperation("业绩处罚单详情")
    public BaseResult<RsPerformancePenaltyNotice> performancePenaltyNoticeDetail(@RequestParam @ApiParam("处罚单id") Long id) {
        RsPerformancePenaltyNotice notice = performancePenaltyNoticeDao.findPerformancePenaltyNoticeDetail(id);
        return BaseResult.success(notice);
    }

    @PostMapping("create")
    @ApiOperation("创建业绩处罚单")
    public BaseResult<Void> createPerformancePenaltyNotice(
            @RequestBody @Valid PerformancePenaltyNoticeDetailReq req) {
        return performancePenaltyNoticeService.createPerformancePenaltyNotice(req);
    }

    @PostMapping("audit")
    @ApiOperation("业绩处罚单审核")
    public BaseResult<Void> auditPerformancePenaltyNotice(
            @RequestBody @Valid PerformancePenaltyNoticeAuditReq penaltyNoticeAuditReq) {
        return performancePenaltyNoticeService.auditPerformancePenaltyNotice(penaltyNoticeAuditReq);
    }

    @PostMapping("appeal")
    @ApiOperation("业绩处罚单发起申诉")
    public BaseResult<Void> appealPerformancePenaltyNotice(@RequestBody @Valid PerformancePenaltyNoticeAppealReq req) {
        return performancePenaltyNoticeService.appealPerformancePenaltyNotice(req.getUserId(), req.getId(), req.getAppealReason(), req.getStatus());
    }

    @PostMapping("appeal-audit")
    @ApiOperation("业绩处罚单申诉审核")
    public BaseResult<Void> auditAppealPerformancePenaltyNotice(
            @RequestBody @Valid PerformancePenaltyNoticeAppealAuditReq appealAuditReq) {
        return performancePenaltyNoticeService.auditAppealPerformancePenaltyNotice(appealAuditReq);
    }

    @GetMapping("record/list")
    @ApiOperation("业绩处罚单操作记录列表")
    public BaseResult<List<RsPerformancePenaltyNoticeRecord>> performancePenaltyNoticeOperateRecordList(
            @RequestParam @ApiParam("处罚单id") Long noticeId) {
        return performancePenaltyNoticeRecordDao.performancePenaltyNoticeOperateRecordList(noticeId);
    }

}

package cn.shrise.radium.complianceservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeStatusConstant;
import cn.shrise.radium.complianceservice.entity.QRsPerformancePenaltyNotice;
import cn.shrise.radium.complianceservice.req.PerformancePenaltyNoticeAppealAuditReq;
import com.alibaba.fastjson.JSON;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: tangjiajun
 * @Date: 2025/5/30 10:18
 * @Desc:
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformancePenaltyNoticeAppealDao {

    private final JPAQueryFactory queryFactory;
    private final QRsPerformancePenaltyNotice performancePenaltyNotice = QRsPerformancePenaltyNotice.rsPerformancePenaltyNotice;

    @Transactional
    public void updateAppeal(Long id, Integer reason, Integer status) {
        queryFactory.update(performancePenaltyNotice)
                .set(performancePenaltyNotice.status, status)
                .set(performancePenaltyNotice.appealReason, reason)
                .where(performancePenaltyNotice.id.eq(id)).execute();
    }

    @Transactional
    public void auditAppealPerformancePenaltyNotice(PerformancePenaltyNoticeAppealAuditReq appealAuditReq) {
        if (ObjectUtil.equals(appealAuditReq.getStatus(), PerformancePenaltyNoticeStatusConstant.PENALTY_MODIFIED)) {
            JPAUpdateClause updateClause = queryFactory.update(performancePenaltyNotice)
                    .set(performancePenaltyNotice.status, appealAuditReq.getStatus())
                    .set(performancePenaltyNotice.adjustReason, appealAuditReq.getAdjustReason())
                    .set(performancePenaltyNotice.judgeResult, appealAuditReq.getJudgeResult());
            if (ObjectUtil.isNotEmpty(appealAuditReq.getImageUrls())) {
                updateClause.set(performancePenaltyNotice.attachments, JSON.toJSONString(appealAuditReq.getImageUrls()));
            } else {
                updateClause.set(performancePenaltyNotice.attachments, "");
            }
            updateClause.where(performancePenaltyNotice.id.eq(appealAuditReq.getId())).execute();
        } else {
            JPAUpdateClause updateClause = queryFactory.update(performancePenaltyNotice)
                    .set(performancePenaltyNotice.status, appealAuditReq.getStatus())
                    .set(performancePenaltyNotice.appealAuditRemark, appealAuditReq.getRemark());
            if (ObjectUtil.isNotEmpty(appealAuditReq.getImageUrls())) {
                updateClause.set(performancePenaltyNotice.appealAuditImages, JSON.toJSONString(appealAuditReq.getImageUrls()));
            }
            updateClause.where(performancePenaltyNotice.id.eq(appealAuditReq.getId())).execute();
        }
    }


}

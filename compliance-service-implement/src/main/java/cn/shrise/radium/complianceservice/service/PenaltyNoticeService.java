package cn.shrise.radium.complianceservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.complianceservice.constant.PenaltyNoticeAppealEnum;
import cn.shrise.radium.complianceservice.constant.PenaltyNoticeChangeStatusMsgEnum;
import cn.shrise.radium.complianceservice.constant.PenaltyNoticeStatusConstant;
import cn.shrise.radium.complianceservice.constant.PenaltyNoticeTypeEnum;
import cn.shrise.radium.complianceservice.dao.PenaltyClauseDao;
import cn.shrise.radium.complianceservice.dao.RsPenaltyNoticeClauseRelationDao;
import cn.shrise.radium.complianceservice.dao.RsPenaltyNoticeDao;
import cn.shrise.radium.complianceservice.dao.RsPenaltyNoticeRecordDao;
import cn.shrise.radium.complianceservice.entity.RsPenaltyNotice;
import cn.shrise.radium.complianceservice.entity.RsPenaltyNoticeClauseRelation;
import cn.shrise.radium.complianceservice.entity.RsPenaltyNoticeRecord;
import cn.shrise.radium.complianceservice.req.*;
import cn.shrise.radium.complianceservice.resp.PenaltyClauseDetailResp;
import cn.shrise.radium.complianceservice.resp.PenaltyNoticeRecordResp;
import cn.shrise.radium.complianceservice.resp.PenaltyNoticeResp;
import cn.shrise.radium.dingdingservice.req.PenaltyNoticeSendReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.RocketMQConstant.TAG_DING_TALK_PENALTY_NOTICE;
import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_DING_TALK_MQ5;
import static cn.shrise.radium.complianceservice.constant.ComplianceErrorCode.PENALTY_STATUS_CHANGED;
import static cn.shrise.radium.complianceservice.constant.PenaltyNoticeStatusConstant.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PenaltyNoticeService {

    private final RsPenaltyNoticeDao penaltyNoticeDao;
    private final RsPenaltyNoticeRecordDao penaltyNoticeRecordDao;
    private final RsPenaltyNoticeClauseRelationDao penaltyNoticeClauseRelationDao;
    private final PenaltyClauseDao penaltyClauseDao;
    private final UserClient userClient;
    private final OrderClient orderClient;
    private final RocketMqUtils rocketMqUtils;
    private static final List<Integer> terminalStatusList = Arrays.asList(CONFIRMED, PRELIMINARY_REJECTED, SECONDARY_REJECTED, FINAL_REJECTED, NOTICE_REVOKED, PENALTY_MODIFIED, ORIGINAL_JUDGMENT_MAINTAINED);


    private String getNoticeInfoContent(CreateOrUpdatePenaltyNoticeReq req) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("类型：").append(PenaltyNoticeTypeEnum.getMsgByCode(req.getType())).append("\n");
        UcUsers salesInfo = userClient.getUser(req.getSalesId()).getData();
        stringBuilder.append("被通知人姓名：").append(salesInfo.getRealName()).append("    ");
        stringBuilder.append("用户ID：").append(ObjectUtil.isNotEmpty(req.getCustomerCode()) ? req.getCustomerCode() : "--").append("    ");
        stringBuilder.append("订单号：").append(ObjectUtil.isNotEmpty(req.getOrderNumber()) ? req.getOrderNumber() : "--").append("\n");
        Map<Long, PenaltyClauseDetailResp> clauseMap = penaltyClauseDao.getClauseInfo(req.getClauses().stream().map(CreateOrUpdatePenaltyNoticeReq.ClauseInfo::getDetailId)
                .collect(Collectors.toSet())).stream().collect(Collectors.toMap(PenaltyClauseDetailResp::getDetailId, Function.identity()));
        for (int i = 0; i < req.getClauses().size(); i++) {
            CreateOrUpdatePenaltyNoticeReq.ClauseInfo clauseInfo = req.getClauses().get(i);
            stringBuilder.append("违规点").append(i + 1).append("\n");
            stringBuilder.append("违规类别：").append(clauseMap.get(clauseInfo.getDetailId()).getCategoryName()).append("\n");
            stringBuilder.append("违规条例：").append(clauseMap.get(clauseInfo.getDetailId()).getRuleName()).append("\n");
            stringBuilder.append("详细条款：").append(clauseMap.get(clauseInfo.getDetailId()).getDetailContent()).append("\n");
            stringBuilder.append("事件缘由：").append(clauseInfo.getBackground()).append("\n");
            stringBuilder.append("整体问题：").append(clauseInfo.getProblem()).append("\n");
            stringBuilder.append("违规员工扣分：").append(clauseInfo.getSalesPoint()).append("    ");
            stringBuilder.append("主任扣分：").append(clauseInfo.getHeadPoint()).append("    ");
            stringBuilder.append("经理扣分：").append(clauseInfo.getManagerPoint()).append("    ");
            stringBuilder.append("总监扣分：").append(clauseInfo.getDirectorPoint()).append("\n");
        }
        stringBuilder.append("开立日期：").append(DateUtils.formatterLocalDateTimeToString(req.getIssueTime(), "yyyy/MM/dd")).append("\n");
        stringBuilder.append("是否扣除业绩：").append(ObjectUtil.equals(true, req.getIsReduce()) ?  "是" : "否").append("\n");
        stringBuilder.append("是否多位员工同时惩处：").append(ObjectUtil.equals(true, req.getIsMultiple()) ?  "是" : "否").append("；");
        if (ObjectUtil.isNotEmpty(req.getMultipleList())) {
            List<UcUsers> salesList = userClient.batchGetUserList(BatchReq.of(req.getMultipleList())).getData();
            String salesNames = salesList.stream().map(UcUsers::getRealName).collect(Collectors.joining("、"));
            stringBuilder.append(salesNames);
        }
        stringBuilder.append("\n");
        stringBuilder.append("扣除项对象：").append("\n");
        stringBuilder.append("减少发放绩效-违规员工：").append(req.getSalesAmount()).append("\n");
        stringBuilder.append("减少发放绩效-主任：").append(req.getHeadAmount()).append("\n");
        stringBuilder.append("减少发放绩效-经理：").append(req.getManagerAmount()).append("\n");
        stringBuilder.append("减少发放绩效-总监：").append(req.getDirectorAmount()).append("\n");
        stringBuilder.append("扣分总分值-违规员工：").append(req.getSalesPoint()).append("\n");
        stringBuilder.append("扣分总分值-主任：").append(req.getHeadPoint()).append("\n");
        stringBuilder.append("扣分总分值-经理：").append(req.getManagerPoint()).append("\n");
        stringBuilder.append("扣分总分值-总监：").append(req.getDirectorPoint()).append("\n");
        return stringBuilder.toString();
    }

    public void createOne(CreateOrUpdatePenaltyNoticeReq req) {
        RsPenaltyNotice notice = penaltyNoticeDao.createOne(req);
        penaltyNoticeClauseRelationDao.createList(notice.getId(), req.getClauses());
        String content = getNoticeInfoContent(req);
        penaltyNoticeRecordDao.createOne(notice.getId(), req.getCreatorId(),
                PenaltyNoticeChangeStatusMsgEnum.getMsgByCode(PenaltyNoticeStatusConstant.PRELIMINARY_REVIEWING), content, req.getAttachments());
    }

    public PageResult<List<PenaltyNoticeResp>> findPenaltyNoticeList(PenaltyNoticeListReq req, Integer current, Integer size) {
        PageResult<List<RsPenaltyNotice>> pageResult = penaltyNoticeDao.findPenaltyNoticeList(req, current, size);
        List<RsPenaltyNotice> noticeList = pageResult.getData();
        List<Long> noticeIdList = noticeList.stream().map(RsPenaltyNotice::getId).collect(Collectors.toList());
        List<RsPenaltyNoticeClauseRelation> relationList = penaltyNoticeClauseRelationDao.findByNoticeIdList(noticeIdList);
        Set<Long> detailIdSet = relationList.stream().map(RsPenaltyNoticeClauseRelation::getClauseId).collect(Collectors.toSet());
        Map<Long, PenaltyClauseDetailResp> clauseMap = penaltyClauseDao.getClauseInfo(detailIdSet).stream()
                .collect(Collectors.toMap(PenaltyClauseDetailResp::getDetailId, Function.identity()));
        Map<Long, List<RsPenaltyNoticeClauseRelation>> relationMap = relationList.stream().collect(Collectors.groupingBy(RsPenaltyNoticeClauseRelation::getNoticeId));
        Set<Integer> userIdSet = new HashSet<>();
        Set<Integer> salesIdSet = new HashSet<>();
        Set<Integer> orderIdSet = new HashSet<>();
        noticeList.forEach(notice -> {
            salesIdSet.add(notice.getSalesId());
            userIdSet.add(notice.getCreatorId());
            userIdSet.add(notice.getCustomerId());
            if (ObjectUtil.isNotEmpty(notice.getMultipleList())) {
                userIdSet.addAll(JSON.parseArray(notice.getMultipleList(), Integer.class));
            }
            if (ObjectUtil.isNotNull(notice.getOrderId())) {
                orderIdSet.add(notice.getOrderId());
            }
        });
        userIdSet.addAll(salesIdSet);
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).getData();
        Map<Integer, String> deptMap = userClient.getDeptListByUsers(salesIdSet).getData();
        Map<Integer, RsCourseOrder> orderMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(orderIdSet)) {
            orderMap = orderClient.batchGetOrderListById(new ArrayList<>(orderIdSet)).getData().stream()
                    .collect(Collectors.toMap(RsCourseOrder::getId, Function.identity()));
        }
        Map<Integer, RsCourseOrder> finalOrderMap = orderMap;
        List<PenaltyNoticeResp> records = noticeList.stream().map(notice -> {
            PenaltyNoticeResp resp = PenaltyNoticeResp.builder()
                    .id(notice.getId())
                    .gmtCreate(notice.getGmtCreate())
                    .origin(notice.getOrigin())
                    .type(notice.getType())
                    .salesId(notice.getSalesId())
                    .salesName(userMap.get(notice.getSalesId()).getRealName())
                    .deptName(deptMap.get(notice.getSalesId()))
                    .creatorId(notice.getCreatorId())
                    .creatorName(userMap.get(notice.getCreatorId()).getRealName())
                    .status(notice.getStatus())
                    .appealReason(notice.getAppealReason())
                    .customerCode(DesensitizeUtil.idToMask(notice.getCustomerId()))
                    .customerNickName(userMap.getOrDefault(notice.getCustomerId(), new UcUsers()).getNickName())
                    .orderId(notice.getOrderId())
                    .attachments(JSON.parseArray(notice.getAttachments(), String.class))
                    .issueTime(notice.getIssueTime())
                    .isReduce(notice.getIsReduce())
                    .isMultiple(notice.getIsMultiple())
                    .salesAmount(notice.getSalesAmount())
                    .headAmount(notice.getHeadAmount())
                    .managerAmount(notice.getManagerAmount())
                    .directorAmount(notice.getDirectorAmount())
                    .salesPoint(notice.getSalesPoint())
                    .headPoint(notice.getHeadPoint())
                    .managerPoint(notice.getManagerPoint())
                    .directorPoint(notice.getDirectorPoint())
                    .build();
            if (ObjectUtil.isNotNull(notice.getOrderId())) {
                resp.setOrderNumber(finalOrderMap.get(notice.getOrderId()).getOrderNumber());
            }
            List<RsPenaltyNoticeClauseRelation> relations = relationMap.get(notice.getId());
            List<PenaltyNoticeResp.ClauseInfoResp> clauseResp = relations.stream().map(relation -> {
                PenaltyClauseDetailResp detailResp = clauseMap.get(relation.getClauseId());
                return PenaltyNoticeResp.ClauseInfoResp.builder()
                        .id(relation.getId())
                        .categoryId(detailResp.getCategoryId())
                        .categoryName(detailResp.getCategoryName())
                        .ruleId(detailResp.getRuleId())
                        .ruleName(detailResp.getRuleName())
                        .detailId(relation.getClauseId())
                        .detailContent(detailResp.getDetailContent())
                        .background(relation.getBackground())
                        .problem(relation.getProblem())
                        .salesPoint(relation.getSalesPoint())
                        .headPoint(relation.getHeadPoint())
                        .managerPoint(relation.getManagerPoint())
                        .directorPoint(relation.getDirectorPoint())
                        .build();
            }).collect(Collectors.toList());
            resp.setClauses(clauseResp);
            if (ObjectUtil.isNotEmpty(notice.getMultipleList())) {
                List<Integer> multipleList = JSON.parseArray(notice.getMultipleList(), Integer.class);
                List<PenaltyNoticeResp.MultipleInfoResp> multipleInfos = multipleList.stream().map(userId ->
                        PenaltyNoticeResp.MultipleInfoResp.builder()
                        .salesId(userId)
                        .salesName(userMap.get(userId).getRealName())
                        .build()).collect(Collectors.toList());
                resp.setMultipleInfos(multipleInfos);
            }
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(records, pageResult.getPagination());
    }

    public void checkStatus(Long noticeId, Integer status) {
        RsPenaltyNotice notice = penaltyNoticeDao.findOne(noticeId);
        if (notice.getStatus() >= status || terminalStatusList.contains(notice.getStatus())) {
            throw new BusinessException(PENALTY_STATUS_CHANGED);
        }
    }

    public void auditPenaltyNotice(PenaltyNoticeAuditReq req) {
        checkStatus(req.getId(), req.getStatus());
        penaltyNoticeDao.updateAudit(req);
        String content = null;
        if (ObjectUtil.isNotEmpty(req.getRemark())) {
            content = "原因：" + req.getRemark();
        }
        penaltyNoticeRecordDao.createOne(req.getId(), req.getUserId(), PenaltyNoticeChangeStatusMsgEnum.getMsgByCode(req.getStatus()),
                content, req.getImageUrls());
        if (ObjectUtil.equals(req.getStatus(), FINAL_REVIEWING)) {
            PenaltyNoticeSendReq sendReq = PenaltyNoticeSendReq.builder()
                    .penaltyId(req.getId())
                    .configNumber("PROCESSWORK")
                    .build();
            rocketMqUtils.convertAndSend(TOPIC_DING_TALK_MQ5, TAG_DING_TALK_PENALTY_NOTICE, sendReq);
        }
    }

    public void appealPenaltyNotice(PenaltyNoticeAppealReq req) {
        checkStatus(req.getId(), req.getStatus());
        penaltyNoticeDao.updateAppeal(req);
        String content = "原因：" + PenaltyNoticeAppealEnum.getMsgByCode(req.getAppealReason());
        penaltyNoticeRecordDao.createOne(req.getId(), req.getUserId(), PenaltyNoticeChangeStatusMsgEnum.getMsgByCode(req.getStatus()),
                content, null);
    }

    public void auditAppealPenaltyNotice(PenaltyNoticeAppealAuditReq req) {
        checkStatus(req.getId(), req.getStatus());
        String content = null;
        List<String> imageUrls = req.getImageUrls();
        if (ObjectUtil.equals(req.getStatus(), PENALTY_MODIFIED)) {
            penaltyNoticeDao.updateOne(req.getNoticeInfo());
            penaltyNoticeClauseRelationDao.updateList(req.getId(), req.getNoticeInfo().getClauses());
            penaltyNoticeClauseRelationDao.disableList(req.getId(),
                    req.getNoticeInfo().getClauses().stream().map(CreateOrUpdatePenaltyNoticeReq.ClauseInfo::getDetailId).collect(Collectors.toList()));
            content = getNoticeInfoContent(req.getNoticeInfo());
            imageUrls = req.getNoticeInfo().getAttachments();
        }
        penaltyNoticeDao.updateAppealAudit(req);
        if (ObjectUtil.isNotEmpty(req.getRemark())) {
            content = "原因：" + req.getRemark();
        }
        penaltyNoticeRecordDao.createOne(req.getId(), req.getUserId(), PenaltyNoticeChangeStatusMsgEnum.getMsgByCode(req.getStatus()),
                content, imageUrls);
    }

    public PageResult<List<PenaltyNoticeRecordResp>> findPenaltyNoticeRecordList(Long noticeId, Integer current, Integer size) {
        PageResult<List<RsPenaltyNoticeRecord>> pageResult = penaltyNoticeRecordDao.getList(noticeId, current, size);
        List<RsPenaltyNoticeRecord> recordList = pageResult.getData();
        Set<Integer> userIdSet = recordList.stream().map(RsPenaltyNoticeRecord::getOperatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userIdSet)).getData();
        List<PenaltyNoticeRecordResp> recordRespList = recordList.stream()
                .map(record -> PenaltyNoticeRecordResp.builder()
                        .gmtCreate(record.getGmtCreate())
                        .operatorName(userMap.get(record.getOperatorId()).getRealName())
                        .avatarUrl(userMap.get(record.getOperatorId()).getAvatarUrl())
                        .title(record.getTitle())
                        .content(record.getContent())
                        .imageUrls(ObjectUtil.isNotEmpty(record.getImageUrls()) ? JSON.parseArray(record.getImageUrls(), String.class) : null)
                        .build()
                ).collect(Collectors.toList());
        return PageResult.success(recordRespList, pageResult.getPagination());
    }
}

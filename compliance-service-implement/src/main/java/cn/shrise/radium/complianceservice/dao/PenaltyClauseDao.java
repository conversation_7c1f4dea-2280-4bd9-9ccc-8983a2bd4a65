package cn.shrise.radium.complianceservice.dao;

import cn.shrise.radium.complianceservice.entity.*;
import cn.shrise.radium.complianceservice.resp.PenaltyClauseDetailResp;
import cn.shrise.radium.complianceservice.resp.PenaltyClauseResp;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/5/29, 星期四
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class PenaltyClauseDao {
    private final JPAQueryFactory jpaQueryPrimary;
    private final static QRsPenaltyClauseCategory rsPenaltyClauseCategory = QRsPenaltyClauseCategory.rsPenaltyClauseCategory;
    private final static QRsPenaltyClauseRule rsPenaltyClauseRule = QRsPenaltyClauseRule.rsPenaltyClauseRule;
    private final static QRsPenaltyClauseDetail rsPenaltyClauseDetail = QRsPenaltyClauseDetail.rsPenaltyClauseDetail;

    public PenaltyClauseResp getAllPenaltyClause() {
        // 1. 查询所有分类
        List<RsPenaltyClauseCategory> categoryEntities = jpaQueryPrimary
                .selectFrom(rsPenaltyClauseCategory)
                .orderBy(rsPenaltyClauseCategory.id.asc())
                .fetch();

        // 2. 查询所有规则
        List<RsPenaltyClauseRule> rules = jpaQueryPrimary
                .selectFrom(rsPenaltyClauseRule)
                .orderBy(rsPenaltyClauseRule.id.asc())
                .fetch();

        // 3. 查询所有详情
        List<RsPenaltyClauseDetail> details = jpaQueryPrimary
                .selectFrom(rsPenaltyClauseDetail)
                .orderBy(rsPenaltyClauseDetail.id.asc())
                .fetch();

        // 4. 组装数据
        Map<Long, List<RsPenaltyClauseRule>> ruleMap = rules.stream()
                .collect(Collectors.groupingBy(RsPenaltyClauseRule::getCategoryId));

        Map<Long, List<RsPenaltyClauseDetail>> detailMap = details.stream()
                .collect(Collectors.groupingBy(RsPenaltyClauseDetail::getRuleId));

        // 5. 组装规则和详情
        List<PenaltyClauseResp.Category> categories = categoryEntities.stream()
                .map(category -> {
                    List<RsPenaltyClauseRule> categoryRules = ruleMap.getOrDefault(category.getId(), Collections.emptyList());
                    List<PenaltyClauseResp.Rule> ruleList = categoryRules.stream()
                            .map(rule -> {
                                List<RsPenaltyClauseDetail> ruleDetails = detailMap.getOrDefault(rule.getId(), Collections.emptyList());
                                List<PenaltyClauseResp.Detail> detailList = ruleDetails.stream()
                                        .map(detail -> new PenaltyClauseResp.Detail(detail.getId(), detail.getContent()))
                                        .collect(Collectors.toList());
                                return new PenaltyClauseResp.Rule(rule.getId(), rule.getName(), detailList);
                            }).collect(Collectors.toList());
                    return new PenaltyClauseResp.Category(category.getId(), category.getName(), category.getGmtCreate(), ruleList);
                }).collect(Collectors.toList());

        return PenaltyClauseResp.builder().categories(categories).build();
    }

    public List<PenaltyClauseDetailResp> getClauseInfo(Collection<Long> detailIds) {
        return jpaQueryPrimary.select(Projections.bean(PenaltyClauseDetailResp.class,
                        rsPenaltyClauseDetail.id.as("detailId"),
                        rsPenaltyClauseDetail.content.as("detailContent"),
                        rsPenaltyClauseRule.id.as("ruleId"),
                        rsPenaltyClauseRule.name.as("ruleName"),
                        rsPenaltyClauseCategory.id.as("categoryId"),
                        rsPenaltyClauseCategory.name.as("categoryName")))
                .from(rsPenaltyClauseDetail)
                .leftJoin(rsPenaltyClauseRule).on(rsPenaltyClauseDetail.ruleId.eq(rsPenaltyClauseRule.id))
                .leftJoin(rsPenaltyClauseCategory).on(rsPenaltyClauseRule.categoryId.eq(rsPenaltyClauseCategory.id))
                .where(rsPenaltyClauseDetail.id.in(detailIds))
                .fetch();
    }
}

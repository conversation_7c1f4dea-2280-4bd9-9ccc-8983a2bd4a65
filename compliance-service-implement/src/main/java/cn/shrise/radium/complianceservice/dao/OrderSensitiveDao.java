package cn.shrise.radium.complianceservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.complianceservice.entity.QRsOrderSensitiveRecord;
import cn.shrise.radium.complianceservice.entity.RsOrderSensitiveRecord;
import cn.shrise.radium.complianceservice.repository.RsOrderSensitiveRecordRepository;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/5/29, 星期四
 **/

@Service
@RequiredArgsConstructor
@Slf4j
public class OrderSensitiveDao {
    private final JPAQueryFactory jpaQueryPrimary;
    private final RsOrderSensitiveRecordRepository sensitiveRecordRepository;
    private final QRsOrderSensitiveRecord rsOrderSensitiveRecord = QRsOrderSensitiveRecord.rsOrderSensitiveRecord;

    public RsOrderSensitiveRecord getByUserIdAndOrderId(Integer userId, Integer orderId) {
        return jpaQueryPrimary.selectFrom(rsOrderSensitiveRecord)
                .where(rsOrderSensitiveRecord.userId.eq(userId)
                        .and(rsOrderSensitiveRecord.orderId.eq(orderId))).fetchOne();
    }

    public List<RsOrderSensitiveRecord> getOrderSensititiveRecords(Integer userId, Set<Integer> orderIdList) {
        return jpaQueryPrimary.selectFrom(rsOrderSensitiveRecord)
                .where(rsOrderSensitiveRecord.userId.eq(userId).and(rsOrderSensitiveRecord.orderId.in(orderIdList))).fetch();
    }

    public PageResult<List<RsOrderSensitiveRecord>> listWithPage(LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        JPAQuery<RsOrderSensitiveRecord> query = jpaQueryPrimary.selectFrom(rsOrderSensitiveRecord);
        if (ObjectUtil.isNotEmpty(startTime)) {
            Instant minTime = DateUtils.getDayOfStart(startTime);
            query.where(rsOrderSensitiveRecord.gmtCreate.goe(minTime));
        }
        if (ObjectUtil.isNotEmpty(endTime)) {
            Instant maxTime = DateUtils.getDayOfEnd(endTime);
            query.where(rsOrderSensitiveRecord.gmtCreate.loe(maxTime));
        }
        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();
        query.orderBy(rsOrderSensitiveRecord.gmtCreate.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

}

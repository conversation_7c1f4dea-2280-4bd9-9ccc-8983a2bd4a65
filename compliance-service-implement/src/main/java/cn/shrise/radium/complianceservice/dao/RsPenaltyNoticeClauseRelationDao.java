package cn.shrise.radium.complianceservice.dao;

import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.complianceservice.entity.QRsPenaltyNoticeClauseRelation;
import cn.shrise.radium.complianceservice.entity.RsPenaltyNoticeClauseRelation;
import cn.shrise.radium.complianceservice.repository.RsPenaltyNoticeClauseRelationRepository;
import cn.shrise.radium.complianceservice.req.CreateOrUpdatePenaltyNoticeReq;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RsPenaltyNoticeClauseRelationDao {

    private final JPAQueryFactory jpaQueryPrimary;
    private final QRsPenaltyNoticeClauseRelation qRsPenaltyNoticeClauseRelation = QRsPenaltyNoticeClauseRelation.rsPenaltyNoticeClauseRelation;
    private final RsPenaltyNoticeClauseRelationRepository rsPenaltyNoticeClauseRelationRepository;
    private final JdbcTemplate primaryJdbcTemplate;

    public void createList(Long noticeId, List<CreateOrUpdatePenaltyNoticeReq.ClauseInfo> clauses) {
        List<RsPenaltyNoticeClauseRelation> relations = clauses.stream().map(clause -> RsPenaltyNoticeClauseRelation.builder()
                .noticeId(noticeId)
                .clauseId(clause.getDetailId())
                .background(clause.getBackground())
                .problem(clause.getProblem())
                .salesPoint(clause.getSalesPoint())
                .headPoint(clause.getHeadPoint())
                .managerPoint(clause.getManagerPoint())
                .directorPoint(clause.getDirectorPoint())
                .build()).collect(Collectors.toList());
        rsPenaltyNoticeClauseRelationRepository.saveAll(relations);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateList(Long noticeId, List<CreateOrUpdatePenaltyNoticeReq.ClauseInfo> clauses) {
        List<RsPenaltyNoticeClauseRelation> relations = clauses.stream().map(clause -> RsPenaltyNoticeClauseRelation.builder()
                .noticeId(noticeId)
                .clauseId(clause.getDetailId())
                .background(clause.getBackground())
                .problem(clause.getProblem())
                .salesPoint(clause.getSalesPoint())
                .headPoint(clause.getHeadPoint())
                .managerPoint(clause.getManagerPoint())
                .directorPoint(clause.getDirectorPoint())
                .enabled(true)
                .build()).collect(Collectors.toList());
        String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        primaryJdbcTemplate.execute(sql);
    }

    @Transactional(rollbackFor = Exception.class)
    public void disableList(Long noticeId, List<Long> clauseIds) {
        jpaQueryPrimary.update(qRsPenaltyNoticeClauseRelation)
                .where(qRsPenaltyNoticeClauseRelation.noticeId.eq(noticeId))
                .where(qRsPenaltyNoticeClauseRelation.clauseId.notIn(clauseIds))
                .set(qRsPenaltyNoticeClauseRelation.enabled, false)
                .execute();
    }

    public List<RsPenaltyNoticeClauseRelation> findByNoticeId(Long noticeId) {
        return jpaQueryPrimary.selectFrom(qRsPenaltyNoticeClauseRelation)
                .where(qRsPenaltyNoticeClauseRelation.noticeId.eq(noticeId))
                .where(qRsPenaltyNoticeClauseRelation.enabled.eq(true))
                .fetch();
    }

    public List<RsPenaltyNoticeClauseRelation> findByNoticeIdList(List<Long> noticeIds) {
        return jpaQueryPrimary.selectFrom(qRsPenaltyNoticeClauseRelation)
                .where(qRsPenaltyNoticeClauseRelation.noticeId.in(noticeIds))
                .where(qRsPenaltyNoticeClauseRelation.enabled.eq(true))
                .fetch();
    }
}

package cn.shrise.radium.complianceservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.complianceservice.dao.RsPenaltyNoticeDao;
import cn.shrise.radium.complianceservice.entity.RsPenaltyNotice;
import cn.shrise.radium.complianceservice.req.*;
import cn.shrise.radium.complianceservice.resp.PenaltyNoticeRecordResp;
import cn.shrise.radium.complianceservice.resp.PenaltyNoticeResp;
import cn.shrise.radium.complianceservice.service.PenaltyNoticeService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("penalty-notice")
@RequiredArgsConstructor
public class PenaltyNoticeController {

    private final PenaltyNoticeService penaltyNoticeService;
    private final RsPenaltyNoticeDao penaltyNoticeDao;

    @ApiOperation("创建惩处单")
    @PostMapping("create")
    public BaseResult<Void> createPenaltyNotice(
            @RequestBody @Valid CreateOrUpdatePenaltyNoticeReq req) {
        penaltyNoticeService.createOne(req);
        return BaseResult.successful();
    }

    @ApiOperation("惩处单列表")
    @PostMapping("list")
    public PageResult<List<PenaltyNoticeResp>> getPenaltyNoticeList(
            @RequestBody @Valid PenaltyNoticeListReq req,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return penaltyNoticeService.findPenaltyNoticeList(req, current, size);
    }

    @ApiOperation("惩处单审核")
    @PostMapping("audit")
    public BaseResult<Void> auditPenaltyNotice(
            @RequestBody @Valid PenaltyNoticeAuditReq req) {
        penaltyNoticeService.auditPenaltyNotice(req);
        return BaseResult.successful();
    }

    @ApiOperation("惩处单申诉")
    @PostMapping("appeal")
    public BaseResult<Void> appealPenaltyNotice(
            @RequestBody @Valid PenaltyNoticeAppealReq req) {
        penaltyNoticeService.appealPenaltyNotice(req);
        return BaseResult.successful();
    }

    @ApiOperation("惩处单申诉审核")
    @PostMapping("appeal-audit")
    public BaseResult<Void> auditAppealPenaltyNotice(
            @RequestBody @Valid PenaltyNoticeAppealAuditReq req) {
        penaltyNoticeService.auditAppealPenaltyNotice(req);
        return BaseResult.successful();
    }

    @ApiOperation("惩处单操作记录列表")
    @GetMapping("record/list")
    public PageResult<List<PenaltyNoticeRecordResp>> getPenaltyNoticeRecordList(
            @RequestParam @ApiParam("惩处单id") Long noticeId,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size) {
        return penaltyNoticeService.findPenaltyNoticeRecordList(noticeId, current, size);
    }

    @ApiOperation("获取惩处单信息")
    @GetMapping("info")
    public BaseResult<RsPenaltyNotice> getPenaltyNoticeInfo(
            @RequestParam @ApiParam("惩处单id") Long noticeId) {
        RsPenaltyNotice notice = penaltyNoticeDao.findOne(noticeId);
        return BaseResult.success(notice);
    }

    @ApiOperation("惩处工单导出")
    @PostMapping("export")
    public BaseResult<Long> exportWorkOrders(
            @RequestParam @ApiParam("文件名称") String fileName,
            @RequestBody @Valid PenaltyNoticeListReq req) {
        return BaseResult.success(0l);
    }

}

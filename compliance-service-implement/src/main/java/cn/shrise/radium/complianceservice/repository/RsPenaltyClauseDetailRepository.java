package cn.shrise.radium.complianceservice.repository;

import cn.shrise.radium.complianceservice.entity.RsPenaltyClauseDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RsPenaltyClauseDetailRepository extends JpaRepository<RsPenaltyClauseDetail, Long>, QuerydslPredicateExecutor<RsPenaltyClauseDetail> {
    List<RsPenaltyClauseDetail> findByRuleId(Long ruleId);
} 
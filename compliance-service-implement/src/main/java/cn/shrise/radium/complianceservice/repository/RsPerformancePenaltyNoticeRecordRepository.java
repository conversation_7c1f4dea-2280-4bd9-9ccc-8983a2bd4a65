package cn.shrise.radium.complianceservice.repository;

import cn.shrise.radium.complianceservice.entity.RsPerformancePenaltyNoticeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2025/5/29 15:57
 * @Desc:
 **/
public interface RsPerformancePenaltyNoticeRecordRepository extends JpaRepository<RsPerformancePenaltyNoticeRecord, Long>, QuerydslPredicateExecutor<RsPerformancePenaltyNoticeRecord> {
}

package cn.shrise.radium.complianceservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.complianceservice.entity.QRsPenaltyNoticeRecord;
import cn.shrise.radium.complianceservice.entity.RsPenaltyNoticeRecord;
import cn.shrise.radium.complianceservice.repository.RsPenaltyNoticeRecordRepository;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class RsPenaltyNoticeRecordDao {

    private final JPAQueryFactory jpaQueryPrimary;
    private final QRsPenaltyNoticeRecord qRsPenaltyNoticeRecord  = QRsPenaltyNoticeRecord.rsPenaltyNoticeRecord;
    private final RsPenaltyNoticeRecordRepository rsPenaltyNoticeRecordRepository;

    public void createOne(Long noticeId, Integer operatorId, String title, String content, List<String> imageUrls) {
        RsPenaltyNoticeRecord record = RsPenaltyNoticeRecord.builder()
                .noticeId(noticeId)
                .operatorId(operatorId)
                .title(title)
                .content(content)
                .build();
        if (ObjectUtil.isNotEmpty(imageUrls)) {
            record.setImageUrls(JSON.toJSONString(imageUrls));
        }
        rsPenaltyNoticeRecordRepository.save(record);
    }

    public PageResult<List<RsPenaltyNoticeRecord>> getList(Long noticeId, Integer current, Integer size) {
        JPAQuery<RsPenaltyNoticeRecord> query = jpaQueryPrimary.selectFrom(qRsPenaltyNoticeRecord)
                .where(qRsPenaltyNoticeRecord.noticeId.eq(noticeId));
        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();
        query.orderBy(qRsPenaltyNoticeRecord.id.asc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }
}

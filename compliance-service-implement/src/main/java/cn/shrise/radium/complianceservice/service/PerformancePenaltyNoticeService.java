package cn.shrise.radium.complianceservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeChangeStatusMsgEnum;
import cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeJudgeTypeEnum;
import cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeStatusConstant;
import cn.shrise.radium.complianceservice.dao.PerformancePenaltyNoticeAppealDao;
import cn.shrise.radium.complianceservice.dao.PerformancePenaltyNoticeDao;
import cn.shrise.radium.complianceservice.dao.PerformancePenaltyNoticeRecordDao;
import cn.shrise.radium.complianceservice.entity.RsPerformancePenaltyNotice;
import cn.shrise.radium.complianceservice.req.PerformancePenaltyNoticeAppealAuditReq;
import cn.shrise.radium.complianceservice.req.PerformancePenaltyNoticeAuditReq;
import cn.shrise.radium.complianceservice.req.PerformancePenaltyNoticeDetailReq;
import cn.shrise.radium.dingdingservice.req.PenaltyNoticeSendReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.ProductLevelEnum;
import cn.shrise.radium.orderservice.resp.FullOrder;
import cn.shrise.radium.orderservice.resp.OrderInfoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.shrise.radium.common.constant.RocketMQConstant.TAG_DING_TALK_PENALTY_NOTICE;
import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_DING_TALK_MQ5;
import static cn.shrise.radium.complianceservice.constant.ComplianceErrorCode.PERFORMANCE_PENALTY_STATUS_CHANGED;
import static cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeJudgeTypeConstant.PERFORMANCE_BELONG_ADJUST;
import static cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeJudgeTypeConstant.PERK_BELONG_JUDGE;
import static cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeStatusConstant.*;

/**
 * @Author: tangjiajun
 * @Date: 2025/5/28 20:42
 * @Desc:
 **/
@Service
@RequiredArgsConstructor
public class PerformancePenaltyNoticeService {

    private final UserClient userClient;
    private final PerformancePenaltyNoticeDao performancePenaltyNoticeDao;
    private final PerformancePenaltyNoticeRecordDao performancePenaltyNoticeRecordDao;
    private final PerformancePenaltyNoticeAppealDao performancePenaltyNoticeAppealDao;
    private final OrderClient orderClient;
    private final RocketMqUtils rocketMqUtils;
    private static final List<Integer> terminalStatusList = Arrays.asList(CONFIRMED, PRELIMINARY_REJECTED, SECONDARY_REJECTED, FINAL_REJECTED, NOTICE_REVOKED, PENALTY_MODIFIED, ORIGINAL_JUDGMENT_MAINTAINED);
    private static final List<Integer> belongJudgeType = Arrays.asList(PERFORMANCE_BELONG_ADJUST, PERK_BELONG_JUDGE);


    public BaseResult<Void> createPerformancePenaltyNotice(PerformancePenaltyNoticeDetailReq req) {
        if (ObjectUtil.isNotEmpty(req.getAttachments())) {
            req.setAttachmentsJson(JSON.toJSONString(req.getAttachments()));
        }
        RsPerformancePenaltyNotice save = performancePenaltyNoticeDao.createPerformancePenaltyNotice(req).getData();
        String content = getNoticeInfoContent(req);
        performancePenaltyNoticeRecordDao.createPerformancePenaltyNoticeRecord(save.getId(),
                PerformancePenaltyNoticeChangeStatusMsgEnum.getEnum(PRELIMINARY_REVIEWING), content, save.getCreatorId(), save.getAttachments());
        return BaseResult.successful();
    }


    public BaseResult<Void> auditPerformancePenaltyNotice(PerformancePenaltyNoticeAuditReq req) {
        String title = "";
        String content = "";
        checkStatus(req.getId(), req.getStatus());
        if (ObjectUtil.isNotEmpty(req.getRemark())) {
            content = "原因：" + req.getRemark();
        }
        if (ObjectUtil.isNotEmpty(PerformancePenaltyNoticeChangeStatusMsgEnum.getEnum(req.getStatus()))) {
            title = PerformancePenaltyNoticeChangeStatusMsgEnum.getEnum(req.getStatus());
        }
        performancePenaltyNoticeDao.auditPerformancePenaltyNotice(req);
        performancePenaltyNoticeRecordDao.createPerformancePenaltyNoticeRecord(req.getId(), title, content,
                req.getUserId(), req.getImageUrlsJson());
        //复审通过，钉钉通知
        if (ObjectUtil.equals(req.getStatus(), FINAL_REVIEWING)) {
            PenaltyNoticeSendReq sendReq = PenaltyNoticeSendReq.builder()
                    .performancePenaltyId(req.getId())
                    .configNumber("PROCESSPERF")
                    .build();
            rocketMqUtils.convertAndSend(TOPIC_DING_TALK_MQ5, TAG_DING_TALK_PENALTY_NOTICE, sendReq);
        }
        return BaseResult.successful();
    }

    public BaseResult<Void> appealPerformancePenaltyNotice(Integer userId, Long id, Integer reason, Integer status) {
        checkStatus(id, status);
        performancePenaltyNoticeAppealDao.updateAppeal(id, reason, PerformancePenaltyNoticeStatusConstant.APPEALING);
        String title = PerformancePenaltyNoticeChangeStatusMsgEnum.getEnum(PerformancePenaltyNoticeStatusConstant.APPEALING);
        performancePenaltyNoticeRecordDao.createPerformancePenaltyNoticeRecord(id, title, null, userId, null);
        return BaseResult.successful();
    }

    public BaseResult<Void> auditAppealPerformancePenaltyNotice(PerformancePenaltyNoticeAppealAuditReq appealAuditReq) {
        String title = "";
        String content = "";
        String imageUrls = "";
        if (ObjectUtil.isNotEmpty(appealAuditReq.getImageUrls())) {
            imageUrls = JSON.toJSONString(appealAuditReq.getImageUrls());
        }
        checkStatus(appealAuditReq.getId(), appealAuditReq.getStatus());
        //申诉审核
        performancePenaltyNoticeAppealDao.auditAppealPerformancePenaltyNotice(appealAuditReq);
        //获取审核后的工单信息
        RsPerformancePenaltyNotice detail = performancePenaltyNoticeDao.findPerformancePenaltyNoticeDetail(appealAuditReq.getId());
        OrderInfoResp orderInfo = orderClient.getOrderInfo(detail.getOrderId()).getData();
        //查询订单详情
        FullOrder order = orderClient.getFullOrder(orderInfo.getOrderNumber()).getData();
        if (ObjectUtil.isNotEmpty(PerformancePenaltyNoticeChangeStatusMsgEnum.getEnum(appealAuditReq.getStatus()))) {
            title = PerformancePenaltyNoticeChangeStatusMsgEnum.getEnum(appealAuditReq.getStatus());
            if (ObjectUtil.equals(appealAuditReq.getStatus(), PerformancePenaltyNoticeStatusConstant.PENALTY_MODIFIED)) {
                PerformancePenaltyNoticeDetailReq build = PerformancePenaltyNoticeDetailReq.builder()
                        .salesId(detail.getSalesId())
                        .creatorId(detail.getCreatorId())
                        .belongId(detail.getBelongId())
                        .oldBelongId(orderInfo.getSalesId())
                        .judgeType(detail.getJudgeType())
                        .productLevel(order.getSku().getProductLevel())
                        .customerCode(DesensitizeUtil.idToMask(detail.getCustomerId()))
                        .orderNumber(orderInfo.getOrderNumber())
                        .adjustReason(detail.getAdjustReason())
                        .judgeResult(detail.getJudgeResult())
                        .build();
                content = getNoticeInfoContent(build);
                imageUrls = detail.getAttachments();
            }
        }
        if (ObjectUtil.isNotEmpty(appealAuditReq.getRemark())) {
            content = "原因：" + appealAuditReq.getRemark();
        }
        performancePenaltyNoticeRecordDao.createPerformancePenaltyNoticeRecord(appealAuditReq.getId(), title,
                content, appealAuditReq.getUserId(), imageUrls);
        return BaseResult.successful();
    }

    public void checkStatus(Long noticeId, Integer status) {
        RsPerformancePenaltyNotice notice = performancePenaltyNoticeDao.findPerformancePenaltyNoticeDetail(noticeId);
        if (notice.getStatus() >= status || terminalStatusList.contains(notice.getStatus())) {
            throw new BusinessException(PERFORMANCE_PENALTY_STATUS_CHANGED);
        }
    }

    private String getNoticeInfoContent(PerformancePenaltyNoticeDetailReq req) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("类型：").append("业绩确认单").append("\n");
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(Arrays.asList(req.getSalesId(), req.getBelongId(), req.getOldBelongId()))).getData();
        stringBuilder.append("被通知人姓名：").append(usersMap.getOrDefault(req.getSalesId(), new UcUsers()).getRealName()).append("\n");
        stringBuilder.append("业绩判定描述：").append("\n");
        stringBuilder.append("判定类型：").append(ObjectUtil.isNotEmpty(req.getJudgeType()) ? PerformancePenaltyNoticeJudgeTypeEnum.getEnum(req.getJudgeType()) : "--").append("\n");
        stringBuilder.append("用户ID：").append(ObjectUtil.isNotEmpty(req.getCustomerCode()) ? req.getCustomerCode() : "--").append("    ");
        stringBuilder.append("订单号：").append(ObjectUtil.isNotEmpty(req.getOrderNumber()) ? req.getOrderNumber() : "--").append("    ");
        stringBuilder.append("产品类型：").append(ObjectUtil.isNotEmpty(req.getProductLevel()) ? ProductLevelEnum.getEnum(req.getProductLevel()).getProductLevel() : "--").append("\n");
        if (belongJudgeType.contains(req.getJudgeType())) {
            stringBuilder.append("原归属人姓名：").append(ObjectUtil.isNotEmpty(req.getOldBelongId()) ? usersMap.getOrDefault(req.getOldBelongId(), new UcUsers()).getRealName() : "--").append("    ");
            stringBuilder.append("新归属人姓名：").append(ObjectUtil.isNotEmpty(req.getBelongId()) ? usersMap.getOrDefault(req.getBelongId(), new UcUsers()).getRealName() : "--").append("\n");
        }
        stringBuilder.append("充公或调整原因：").append(req.getAdjustReason()).append("\n");
        stringBuilder.append("判定结果：").append(req.getJudgeResult()).append("\n");
        return stringBuilder.toString();
    }
}

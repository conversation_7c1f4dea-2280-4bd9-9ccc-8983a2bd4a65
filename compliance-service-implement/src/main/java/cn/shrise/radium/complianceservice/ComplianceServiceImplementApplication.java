package cn.shrise.radium.complianceservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

import static cn.shrise.radium.common.constant.ServiceConstant.BASE_PACKAGE;


@EnableFeignClients(basePackages = BASE_PACKAGE)
@SpringBootApplication(scanBasePackages = BASE_PACKAGE)
public class ComplianceServiceImplementApplication {

	public static void main(String[] args) {
		SpringApplication.run(ComplianceServiceImplementApplication.class, args);
	}

}

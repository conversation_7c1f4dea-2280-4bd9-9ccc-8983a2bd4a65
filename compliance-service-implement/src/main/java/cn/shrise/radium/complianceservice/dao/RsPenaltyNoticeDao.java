package cn.shrise.radium.complianceservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.complianceservice.constant.PenaltyNoticeStatusConstant;
import cn.shrise.radium.complianceservice.entity.QRsPenaltyNotice;
import cn.shrise.radium.complianceservice.entity.RsPenaltyNotice;
import cn.shrise.radium.complianceservice.repository.RsPenaltyNoticeRepository;
import cn.shrise.radium.complianceservice.req.*;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class RsPenaltyNoticeDao {

    private final JPAQueryFactory jpaQueryPrimary;
    private final QRsPenaltyNotice qRsPenaltyNotice  = QRsPenaltyNotice.rsPenaltyNotice;
    private final RsPenaltyNoticeRepository rsPenaltyNoticeRepository;

    public RsPenaltyNotice createOne(CreateOrUpdatePenaltyNoticeReq req) {
        RsPenaltyNotice penaltyNotice = RsPenaltyNotice.builder()
                .origin(req.getOrigin())
                .type(req.getType())
                .salesId(req.getSalesId())
                .customerId(req.getCustomerId())
                .orderId(req.getOrderId())
                .creatorId(req.getCreatorId())
                .attachments(JSON.toJSONString(req.getAttachments()))
                .issueTime(DateUtils.localDateTimeToInstant(req.getIssueTime()))
                .isReduce(req.getIsReduce())
                .isMultiple(req.getIsMultiple())
                .salesAmount(req.getSalesAmount())
                .headAmount(req.getHeadAmount())
                .managerAmount(req.getManagerAmount())
                .directorAmount(req.getDirectorAmount())
                .salesPoint(req.getSalesPoint())
                .headPoint(req.getHeadPoint())
                .managerPoint(req.getManagerPoint())
                .directorPoint(req.getDirectorPoint())
                .status(PenaltyNoticeStatusConstant.PRELIMINARY_REVIEWING)
                .build();
        if (ObjectUtil.isNotNull(req.getMultipleList())) {
            penaltyNotice.setMultipleList(JSON.toJSONString(req.getMultipleList()));
        }
        return rsPenaltyNoticeRepository.save(penaltyNotice);
    }

    @Transactional
    public void updateOne(CreateOrUpdatePenaltyNoticeReq req) {
        JPAUpdateClause update = jpaQueryPrimary.update(qRsPenaltyNotice)
                .where(qRsPenaltyNotice.id.eq(req.getId()))
                .set(qRsPenaltyNotice.type, req.getType())
                .set(qRsPenaltyNotice.attachments, JSON.toJSONString(req.getAttachments()))
                .set(qRsPenaltyNotice.issueTime, DateUtils.localDateTimeToInstant(req.getIssueTime()))
                .set(qRsPenaltyNotice.isReduce, req.getIsReduce())
                .set(qRsPenaltyNotice.isMultiple, req.getIsMultiple())
                .set(qRsPenaltyNotice.salesAmount, req.getSalesAmount())
                .set(qRsPenaltyNotice.headAmount, req.getHeadAmount())
                .set(qRsPenaltyNotice.managerAmount, req.getManagerAmount())
                .set(qRsPenaltyNotice.directorAmount, req.getDirectorAmount())
                .set(qRsPenaltyNotice.salesPoint, req.getSalesPoint())
                .set(qRsPenaltyNotice.headPoint, req.getHeadPoint())
                .set(qRsPenaltyNotice.managerPoint, req.getManagerPoint())
                .set(qRsPenaltyNotice.directorPoint, req.getDirectorPoint());
        if (ObjectUtil.isNotNull(req.getMultipleList())) {
            update.set(qRsPenaltyNotice.multipleList, JSON.toJSONString(req.getMultipleList()));
        }
        update.execute();
    }

    @Transactional
    public void updateStatus(Long id, Integer status) {
        jpaQueryPrimary.update(qRsPenaltyNotice)
                .where(qRsPenaltyNotice.id.eq(id))
                .set(qRsPenaltyNotice.status, status)
                .execute();
    }

    @Transactional
    public void updateAudit(PenaltyNoticeAuditReq req) {
        JPAUpdateClause update = jpaQueryPrimary.update(qRsPenaltyNotice)
                .where(qRsPenaltyNotice.id.eq(req.getId()))
                .set(qRsPenaltyNotice.status, req.getStatus())
                .set(qRsPenaltyNotice.auditRemark, req.getRemark());
        if (ObjectUtil.isNotEmpty(req.getImageUrls())) {
            update.set(qRsPenaltyNotice.auditImages, JSON.toJSONString(req.getImageUrls()));
        }
        update.execute();
    }

    @Transactional
    public void updateAppealAudit(PenaltyNoticeAppealAuditReq req) {
        JPAUpdateClause update = jpaQueryPrimary.update(qRsPenaltyNotice)
                .where(qRsPenaltyNotice.id.eq(req.getId()))
                .set(qRsPenaltyNotice.status, req.getStatus())
                .set(qRsPenaltyNotice.appealAuditRemark, req.getRemark());
        if (ObjectUtil.isNotEmpty(req.getImageUrls())) {
            update.set(qRsPenaltyNotice.appealAuditImages, JSON.toJSONString(req.getImageUrls()));
        }
        update.execute();
    }

    @Transactional
    public void updateAppeal(PenaltyNoticeAppealReq req) {
        jpaQueryPrimary.update(qRsPenaltyNotice)
                .where(qRsPenaltyNotice.id.eq(req.getId()))
                .set(qRsPenaltyNotice.status, req.getStatus())
                .set(qRsPenaltyNotice.appealReason, req.getAppealReason())
                .execute();
    }

    public PageResult<List<RsPenaltyNotice>> findPenaltyNoticeList(PenaltyNoticeListReq req, Integer current, Integer size) {
        JPAQuery<RsPenaltyNotice> query = jpaQueryPrimary.selectFrom(qRsPenaltyNotice);
        if (ObjectUtil.isNotNull(req.getStatusList())) {
            query.where(qRsPenaltyNotice.status.in(req.getStatusList()));
        }
        if (ObjectUtil.isNotEmpty(req.getStartTime())) {
            query.where(qRsPenaltyNotice.gmtCreate.goe(DateUtils.localDateTimeToInstant(req.getStartTime())));
        }
        if (ObjectUtil.isNotEmpty(req.getEndTime())) {
            query.where(qRsPenaltyNotice.gmtCreate.loe(DateUtils.localDateTimeToInstant(req.getEndTime())));
        }
        if (ObjectUtil.isNotNull(req.getOwnerList())) {
            query.where(qRsPenaltyNotice.salesId.in(req.getOwnerList()));
        }
        if (ObjectUtil.isNotNull(req.getCreatorList())) {
            query.where(qRsPenaltyNotice.creatorId.in(req.getCreatorList()));
        }
        if (ObjectUtil.isNotEmpty(req.getSearchContent())) {
            try {
                Long noticeId = Long.valueOf(req.getSearchContent());
                query.where(qRsPenaltyNotice.id.eq(noticeId));
            } catch (Exception e) {
                return PageResult.empty();
            }
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(qRsPenaltyNotice.id.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public RsPenaltyNotice findOne(Long id) {
        return jpaQueryPrimary.selectFrom(qRsPenaltyNotice).where(qRsPenaltyNotice.id.eq(id)).fetchOne();
    }
}

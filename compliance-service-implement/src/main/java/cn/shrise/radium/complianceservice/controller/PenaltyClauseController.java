package cn.shrise.radium.complianceservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.complianceservice.resp.PenaltyClauseResp;
import cn.shrise.radium.complianceservice.service.PenaltyClauseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


@Api
@Slf4j
@RestController
@RequestMapping("penalty-clause")
@RequiredArgsConstructor
public class PenaltyClauseController {

    private final PenaltyClauseService penaltyClauseService;

    @GetMapping("list")
    @ApiOperation("列出所有违规条款")
    BaseResult<PenaltyClauseResp> listPenaltyClause() {
        return BaseResult.success(penaltyClauseService.listPenaltyClause());
    }

    @PostMapping("category/add")
    @ApiOperation("创建违规类别")
    BaseResult<String> addPenaltyClauseCategory(@RequestParam @ApiParam("违规类名") String name) {
        return penaltyClauseService.addPenaltyClauseCategory(name);
    }

    @PostMapping("rule/add")
    @ApiOperation("创建违规条例")
    BaseResult<Void> addPenaltyClauseRule(@RequestParam @ApiParam("违规类id") Long categoryId,
                                          @RequestParam @ApiParam("违规条例名") String name) {
        penaltyClauseService.addPenaltyClauseRule(categoryId, name);
        return BaseResult.successful();
    }

    @PostMapping("detail/add")
    @ApiOperation("创建违规条款")
    BaseResult<Void> addPenaltyClauseDetail(@RequestParam @ApiParam("违规条例id") Long ruleId,
                                            @RequestParam @ApiParam("违规条款内容") String content) {
        penaltyClauseService.addPenaltyClauseDetail(ruleId, content);
        return BaseResult.successful();
    }

}
package cn.shrise.radium.complianceservice.repository;

import cn.shrise.radium.complianceservice.entity.RsPenaltyClauseRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RsPenaltyClauseRuleRepository extends JpaRepository<RsPenaltyClauseRule, Long>, QuerydslPredicateExecutor<RsPenaltyClauseRule> {
    List<RsPenaltyClauseRule> findByCategoryId(Long categoryId);
} 
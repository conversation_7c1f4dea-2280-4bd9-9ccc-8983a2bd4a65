package cn.shrise.radium.complianceservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.complianceservice.entity.RsOrderSensitiveRecord;
import cn.shrise.radium.complianceservice.req.OrderSensititiveApplyReq;
import cn.shrise.radium.complianceservice.resp.OrderSensitiveCheckResp;
import cn.shrise.radium.complianceservice.service.OrderSensitiveService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("order-sensitive")
@RequiredArgsConstructor
@Slf4j
public class OrderSensitiveController {

    private final OrderSensitiveService orderSensitiveService;

    @PostMapping("apply")
    @ApiOperation("查看敏感信息")
    BaseResult<String> applyOrderSensitive(@RequestBody OrderSensititiveApplyReq req) {
        return orderSensitiveService.applyOrderSensitive(req);
    }

    @PostMapping("check")
    @ApiOperation("查看敏感信息检查")
    BaseResult<List<OrderSensitiveCheckResp>> orderSensitiveCheck(@RequestParam @ApiParam("用户id") Integer userId,
                                                                  @RequestParam @ApiParam("订单id集合") Set<Integer> orderIdList) {
        return BaseResult.success(orderSensitiveService.orderSensitiveCheck(userId, orderIdList));
    }

    @GetMapping("list")
    @ApiOperation("敏感信息查看记录")
    PageResult<List<RsOrderSensitiveRecord>> orderSensitiveRecordList(@RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
                                                                      @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
                                                                      @RequestParam @ApiParam("当前页") Integer current,
                                                                      @RequestParam @ApiParam("每页条数") Integer size) {
        return orderSensitiveService.orderSensitiveRecordList(startTime, endTime, current, size);
    }


}
package cn.shrise.radium.complianceservice.repository;

import cn.shrise.radium.complianceservice.entity.RsPerformancePenaltyNotice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2025/5/29 14:28
 * @Desc:
 **/
public interface RsPerformancePenaltyNoticeRepository extends JpaRepository<RsPerformancePenaltyNotice, Long>, QuerydslPredicateExecutor<RsPerformancePenaltyNotice> {
}

package cn.shrise.radium.complianceservice.conf;

import com.blazebit.persistence.Criteria;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceContext;

/**
 * <AUTHOR>
 */
@Configuration
public class QuerydslAutoConfiguration {

    @PersistenceContext(unitName = "primaryPersistenceUnit")
    private EntityManager primaryEntityManager;

    @Primary
    @Bean(name = "primaryQueryFactory")
    public JPAQueryFactory primaryQueryFactory() {
        return new JPAQueryFactory(primaryEntityManager);
    }

    @Primary
    @Bean(name = "primaryCriteriaBuilderFactory")
    public CriteriaBuilderFactory primaryCriteriaBuilderFactory(
            @Qualifier("primaryEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return Criteria.getDefault().createCriteriaBuilderFactory(entityManagerFactory);
    }

}

package cn.shrise.radium.complianceservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.complianceservice.constant.ComplianceErrorCode;
import cn.shrise.radium.complianceservice.dao.PenaltyClauseDao;
import cn.shrise.radium.complianceservice.entity.QRsPenaltyClauseCategory;
import cn.shrise.radium.complianceservice.entity.RsPenaltyClauseCategory;
import cn.shrise.radium.complianceservice.entity.RsPenaltyClauseDetail;
import cn.shrise.radium.complianceservice.entity.RsPenaltyClauseRule;
import cn.shrise.radium.complianceservice.repository.RsPenaltyClauseCategoryRepository;
import cn.shrise.radium.complianceservice.repository.RsPenaltyClauseDetailRepository;
import cn.shrise.radium.complianceservice.repository.RsPenaltyClauseRuleRepository;
import cn.shrise.radium.complianceservice.resp.PenaltyClauseResp;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> zhangjianwu
 * @created : 2025/5/28, 星期三
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PenaltyClauseService {

    private final JPAQueryFactory jpaQueryPrimary;
    private final RsPenaltyClauseRuleRepository ruleRepository;
    private final RsPenaltyClauseCategoryRepository categoryRepository;
    private final RsPenaltyClauseDetailRepository detailRepository;
    private final static QRsPenaltyClauseCategory rsPenaltyClauseCategory = QRsPenaltyClauseCategory.rsPenaltyClauseCategory;
    private final PenaltyClauseDao penaltyClauseDao;

    public PenaltyClauseResp listPenaltyClause() {
        return penaltyClauseDao.getAllPenaltyClause();
    }

    public BaseResult<String> addPenaltyClauseCategory(String name) {
        List<RsPenaltyClauseCategory> categories = jpaQueryPrimary.selectFrom(rsPenaltyClauseCategory).where(rsPenaltyClauseCategory.name.eq(name)).fetch();
        if (ObjectUtil.isNotEmpty(categories)) {
            return BaseResult.create(ComplianceErrorCode.PANELTY_CATEGORY_EXIST);
        }
        RsPenaltyClauseCategory build = RsPenaltyClauseCategory.builder()
                .name(name)
                .build();
        categoryRepository.save(build);
        return BaseResult.success();
    }

    public void addPenaltyClauseRule(Long categoryId, String name) {
        RsPenaltyClauseRule build = RsPenaltyClauseRule.builder()
                .categoryId(categoryId)
                .name(name)
                .build();
        ruleRepository.save(build);
    }

    public void addPenaltyClauseDetail(Long ruleId, String content) {
        RsPenaltyClauseDetail build = RsPenaltyClauseDetail.builder()
                .ruleId(ruleId)
                .content(content)
                .build();
        detailRepository.save(build);
    }
}

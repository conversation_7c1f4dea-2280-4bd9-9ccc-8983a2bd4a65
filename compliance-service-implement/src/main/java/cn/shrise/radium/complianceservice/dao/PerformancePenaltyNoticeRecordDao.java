package cn.shrise.radium.complianceservice.dao;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.complianceservice.entity.QRsPerformancePenaltyNoticeRecord;
import cn.shrise.radium.complianceservice.entity.RsPerformancePenaltyNoticeRecord;
import cn.shrise.radium.complianceservice.repository.RsPerformancePenaltyNoticeRecordRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: tangjiajun
 * @Date: 2025/5/30 9:28
 * @Desc:
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformancePenaltyNoticeRecordDao {

    private final JPAQueryFactory queryFactory;
    private final QRsPerformancePenaltyNoticeRecord performancePenaltyNoticeRecord = QRsPerformancePenaltyNoticeRecord.rsPerformancePenaltyNoticeRecord;
    private final RsPerformancePenaltyNoticeRecordRepository performancePenaltyNoticeRecordRepository;


    @Transactional
    public void createPerformancePenaltyNoticeRecord(Long noticeId, String title, String content, Integer operateId, String imageUrls) {
        RsPerformancePenaltyNoticeRecord build = RsPerformancePenaltyNoticeRecord.builder()
                .title(title)
                .content(content)
                .noticeId(noticeId)
                .operatorId(operateId)
                .imageUrls(imageUrls)
                .build();
        performancePenaltyNoticeRecordRepository.save(build);
    }

    public BaseResult<List<RsPerformancePenaltyNoticeRecord>> performancePenaltyNoticeOperateRecordList(Long noticeId) {
        List<RsPerformancePenaltyNoticeRecord> fetch = queryFactory.selectFrom(performancePenaltyNoticeRecord)
                .where(performancePenaltyNoticeRecord.noticeId.eq(noticeId))
                .orderBy(performancePenaltyNoticeRecord.gmtCreate.asc()).fetch();
        return BaseResult.success(fetch);
    }

}

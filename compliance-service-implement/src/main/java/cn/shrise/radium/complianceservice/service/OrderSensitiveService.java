package cn.shrise.radium.complianceservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.complianceservice.dao.OrderSensitiveDao;
import cn.shrise.radium.complianceservice.entity.RsOrderSensitiveRecord;
import cn.shrise.radium.complianceservice.repository.RsOrderSensitiveRecordRepository;
import cn.shrise.radium.complianceservice.req.OrderSensititiveApplyReq;
import cn.shrise.radium.complianceservice.resp.OrderSensitiveCheckResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.shrise.radium.complianceservice.constant.ComplianceErrorCode.ORDER_SENSITIVE_RECORD_ALREADY_EXIST;

/**
 * <AUTHOR> zhangjianwu
 * @created : 2025/5/28, 星期三
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderSensitiveService {

    private final OrderSensitiveDao orderSensitiveDao;
    private final RsOrderSensitiveRecordRepository sensitiveRecordRepository;

    public BaseResult<String> applyOrderSensitive(OrderSensititiveApplyReq req) {
        RsOrderSensitiveRecord orderSensitiveRecord = orderSensitiveDao.getByUserIdAndOrderId(req.getUserId(), req.getOrderId());
        if (ObjectUtil.isNotEmpty(orderSensitiveRecord)) {
            return BaseResult.create(ORDER_SENSITIVE_RECORD_ALREADY_EXIST);
        }
        RsOrderSensitiveRecord build = RsOrderSensitiveRecord.builder()
                .userId(req.getUserId())
                .orderId(req.getOrderId())
                .reason(req.getReason())
                .build();
        sensitiveRecordRepository.save(build);
        return BaseResult.success();
    }

    public List<OrderSensitiveCheckResp> orderSensitiveCheck(Integer userId, Set<Integer> orderIdList) {
        if (ObjectUtil.isEmpty(orderIdList)) {
            return Collections.emptyList();
        }
        List<RsOrderSensitiveRecord> fetch = orderSensitiveDao.getOrderSensititiveRecords(userId, orderIdList);
        // 将查询结果转换为Map，方便后续查找
        Map<Integer, RsOrderSensitiveRecord> recordMap = fetch.stream()
                .collect(Collectors.toMap(RsOrderSensitiveRecord::getOrderId, record -> record));
        // 构建返回结果
        return orderIdList.stream().map(orderId -> OrderSensitiveCheckResp.builder()
                .orderId(orderId)
                .isApplied(recordMap.containsKey(orderId))
                .build()).collect(Collectors.toList());
    }

    public PageResult<List<RsOrderSensitiveRecord>> orderSensitiveRecordList(LocalDate startTime, LocalDate endTime, Integer current, Integer size) {
        return orderSensitiveDao.listWithPage(startTime, endTime, current, size);
    }

}

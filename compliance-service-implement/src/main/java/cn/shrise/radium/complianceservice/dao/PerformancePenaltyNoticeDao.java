package cn.shrise.radium.complianceservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.complianceservice.constant.PerformancePenaltyNoticeStatusConstant;
import cn.shrise.radium.complianceservice.entity.QRsPerformancePenaltyNotice;
import cn.shrise.radium.complianceservice.entity.RsPerformancePenaltyNotice;
import cn.shrise.radium.complianceservice.repository.RsPerformancePenaltyNoticeRepository;
import cn.shrise.radium.complianceservice.req.PerformancePenaltyNoticeAuditReq;
import cn.shrise.radium.complianceservice.req.PerformancePenaltyNoticeDetailReq;
import cn.shrise.radium.complianceservice.resp.PerformancePenaltyNoticeResp;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * @Author: tangjiajun
 * @Date: 2025/5/30 9:20
 * @Desc:
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformancePenaltyNoticeDao {

    private final JPAQueryFactory queryFactory;
    private final QRsPerformancePenaltyNotice performancePenaltyNotice = QRsPerformancePenaltyNotice.rsPerformancePenaltyNotice;
    private final RsPerformancePenaltyNoticeRepository performancePenaltyNoticeRepository;

    public PageResult<List<PerformancePenaltyNoticeResp>> performancePenaltyNoticeList(
            Long noticeId, Integer salesId, Integer creatorId, List<Integer> statusList, List<Integer> salesList, List<Integer> creatorList, LocalDate startDate,
            LocalDate endDate, Integer current, Integer size) {
        JPAQuery<PerformancePenaltyNoticeResp> query = queryFactory.select(Projections.bean(PerformancePenaltyNoticeResp.class, performancePenaltyNotice.id,
                        performancePenaltyNotice.orderId, performancePenaltyNotice.customerId, performancePenaltyNotice.judgeResult, performancePenaltyNotice.adjustReason,
                        performancePenaltyNotice.gmtCreate, performancePenaltyNotice.salesId, performancePenaltyNotice.creatorId,
                        performancePenaltyNotice.status, performancePenaltyNotice.appealReason))
                .from(performancePenaltyNotice);
        if (ObjectUtil.isNotEmpty(noticeId)) {
            query.where(performancePenaltyNotice.id.eq(noticeId));
        }
        if (ObjectUtil.isNotEmpty(salesId)) {
            query.where(performancePenaltyNotice.salesId.eq(salesId));
        }
        if (ObjectUtil.isNotEmpty(creatorId)) {
            query.where(performancePenaltyNotice.creatorId.eq(creatorId));
        }
        if (ObjectUtil.isNotEmpty(statusList)) {
            query.where(performancePenaltyNotice.status.in(statusList));
        }
        if (ObjectUtil.isNotEmpty(salesList)) {
            query.where(performancePenaltyNotice.salesId.in(salesList));
        }
        if (ObjectUtil.isNotEmpty(creatorList)) {
            query.where(performancePenaltyNotice.creatorId.in(creatorList));
        }
        if (ObjectUtil.isAllNotEmpty(startDate, endDate)) {
            query.where(performancePenaltyNotice.gmtCreate.goe(DateUtils.getDayOfStart(startDate))
                    .and(performancePenaltyNotice.gmtCreate.lt(DateUtils.getDayOfEnd(endDate))));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();
        query.orderBy(performancePenaltyNotice.id.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public RsPerformancePenaltyNotice findPerformancePenaltyNoticeDetail(Long id) {
        return queryFactory.selectFrom(performancePenaltyNotice).where(performancePenaltyNotice.id.eq(id)).fetchOne();
    }

    @Transactional
    public BaseResult<RsPerformancePenaltyNotice> createPerformancePenaltyNotice(PerformancePenaltyNoticeDetailReq req) {
        RsPerformancePenaltyNotice build = RsPerformancePenaltyNotice.builder()
                .salesId(req.getSalesId())
                .creatorId(req.getCreatorId())
                .judgeType(req.getJudgeType())
                .customerId(req.getCustomerId())
                .orderId(req.getOrderId())
                .adjustReason(req.getAdjustReason())
                .judgeResult(req.getJudgeResult())
                .belongId(req.getBelongId())
                .attachments(req.getAttachmentsJson())
                .status(PerformancePenaltyNoticeStatusConstant.PRELIMINARY_REVIEWING)
                .build();
        RsPerformancePenaltyNotice save = performancePenaltyNoticeRepository.save(build);
        return BaseResult.success(save);
    }

    @Transactional
    public void auditPerformancePenaltyNotice(PerformancePenaltyNoticeAuditReq req) {
        JPAUpdateClause updateClause = queryFactory.update(performancePenaltyNotice)
                .set(performancePenaltyNotice.status, req.getStatus());
        if (ObjectUtil.isNotEmpty(req.getRemark())) {
            updateClause.set(performancePenaltyNotice.auditRemark, req.getRemark());
        }
        if (ObjectUtil.isNotEmpty(req.getImageUrls())) {
            req.setImageUrlsJson(JSON.toJSONString(req.getImageUrls()));
            updateClause.set(performancePenaltyNotice.auditImages, req.getImageUrlsJson());
        }
        updateClause.where(performancePenaltyNotice.id.eq(req.getId())).execute();
    }

}

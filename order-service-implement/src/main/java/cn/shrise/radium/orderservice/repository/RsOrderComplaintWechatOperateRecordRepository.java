package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsComplaintReplyTemplateRecord;
import cn.shrise.radium.orderservice.entity.RsOrderComplaintWechatOperateRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RsOrderComplaintWechatOperateRecordRepository extends JpaRepository<RsOrderComplaintWechatOperateRecord, Long>,
        QuerydslPredicateExecutor<RsOrderComplaintWechatOperateRecord> {
}

package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsArticleStockRelation;
import cn.shrise.radium.orderservice.entity.RsArticleStockRelationId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;


@Repository
public interface ArticleStockRelationRepository extends JpaRepository<RsArticleStockRelation, RsArticleStockRelationId>,
        QuerydslPredicateExecutor<RsArticleStockRelation> {
}

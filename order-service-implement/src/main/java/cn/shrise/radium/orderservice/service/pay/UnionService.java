package cn.shrise.radium.orderservice.service.pay;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.constant.RocketMQConstant;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.util.GsonUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.orderservice.allinpay.TrxstatusCodeEnum;
import cn.shrise.radium.orderservice.conf.bean.MultiUnionPayService;
import cn.shrise.radium.orderservice.constant.*;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.req.*;
import cn.shrise.radium.orderservice.resp.CreateSubOrderResp;
import cn.shrise.radium.orderservice.resp.CreateUnionPaySubOrderResp;
import cn.shrise.radium.orderservice.resp.PaidOrderInfoResp;
import cn.shrise.radium.orderservice.resp.UnionPayInfoResp;
import cn.shrise.radium.orderservice.service.refund.RefundFlowRecordService;
import cn.shrise.radium.orderservice.service.refund.RefundOrderSubService;
import cn.shrise.radium.orderservice.service.refund.RefundService;
import cn.shrise.radium.orderservice.service.order.OrderService;
import cn.shrise.radium.orderservice.service.order.SubOrderService;
import cn.shrise.radium.orderservice.union.config.UnionPayConfig;
import cn.shrise.radium.orderservice.union.util.SybUtil;
import cn.shrise.radium.userservice.UserClient;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnionService {

    private final OrderService orderService;

    private final RefundService refundService;

    private final SubOrderService subOrderService;
    private final CommonProperties commonProperties;

    private final NotifyOrderProcess notifyOrderProcessService;

    private final RefundOrderSubService refundOrderSubService;

    private final UserClient userClient;

    private final RocketMqUtils rocketMqUtils;

    private final MultiUnionPayService multiUnionPayService;
    private final RefundFlowRecordService refundFlowRecordService;

    public CreateUnionPaySubOrderResp createUnionPaySubOrder(CreateUnionPaySubOrderReq req) {
        Integer companyType = req.getCompanyType();
        Integer payType = PayTypeConstant.UNION.getValue();
        //获取mchType
        Integer mchType = req.getMchType() != null ? req.getMchType() :
                orderService.getDefaultMchType(companyType, payType);
        Integer amount = req.getAmount();

        // 创建子订单
        CreateSubOrderReq createSubOrderReq = CreateSubOrderReq.builder()
                .payType(payType)
                .mchType(mchType)
                .orderId(req.getOrderId())
                .amount(amount)
                .build();
        CreateSubOrderResp resp = subOrderService.createSubOrder(createSubOrderReq);
        CourseSubOrder subOrder = resp.getSubOrder();
        //支付回调地址
        String notifyUrl = getUnionPayNotifyUrl(companyType, mchType);
        //h5页面回调地址

        String page = multiUnionPayService.switchoverTo(companyType, mchType).pay(notifyUrl, getUnionPayReturnUrl(resp.getOrder().getOrderNumber()), req.getGateId(), req.getSkuName(), subOrder.getNumber(), subOrder.getAmount());

        UnionPayInfoResp payInfoResp = UnionPayInfoResp.builder()
                .page(page)
                .build();

        return CreateUnionPaySubOrderResp.builder()
                .subOrder(subOrder)
                .order(resp.getOrder())
                .payInfo(payInfoResp)
                .build();
    }

    public void refundOrder(RsCourseRefundOrder refundOrder) {
        Integer refundOrderId = refundOrder.getId();
        List<RsCourseRefundOrderSub> refundSubOrderList = refundOrderSubService.getRefundSubOrderList(refundOrderId, null, RefundOrderStatusEnum.REFUNDING.getValue());
        if (ObjectUtil.isEmpty(refundSubOrderList)) {
            log.info("不存在需要退款的订单: {}", refundOrder);
            return;
        }

        RsCourseOrder mainOrder = orderService.getOrder(refundOrder.getOrderId())
                .orElseThrow(RecordNotExistedException::new);
        boolean refundAll = true;
        List<Long> failedList = new ArrayList<>();

        Integer companyType = mainOrder.getCompanyType();
        for (RsCourseRefundOrderSub refundSubOrder : refundSubOrderList) {
            Integer subOrderId = refundSubOrder.getSubOrderId();
            CourseSubOrder subOrder = subOrderService.getSubOrder(subOrderId).orElse(null);
            if (subOrder == null) {
                failedList.add(refundSubOrder.getId());
                refundAll = false;
                continue;
            }
            Integer mchType = subOrder.getMchType();
            String refundNumber = refundSubOrder.getRefundNumber();
            UnionPayConfig unionPayConfig = multiUnionPayService.switchoverTo(companyType, mchType).getUnionPayConfig();
            refundOrderSubService.updateRefundStatus(refundSubOrder.getId(), RefundOrderStatusEnum.IN_PROCESS, null);
            String refund = multiUnionPayService.switchoverTo(companyType, mchType).refund(refundSubOrder.getRefundFee(), refundNumber, subOrder.getNumber());
            Map<String, String> map = JSON.parseObject(refund, Map.class);
            if (map == null) {
                throw new BusinessException("返回数据错误");
            }
            Boolean result = Boolean.FALSE;
            if ("SUCCESS".equals(map.get("retcode"))) {
                TreeMap<String, String> tmap = new TreeMap<>(map);
                String publicKey = unionPayConfig.getTlPublicKey();
                try {
                    if (SybUtil.validSign(tmap, publicKey, unionPayConfig.getSignType())) {
                        if (ObjectUtil.isNotEmpty(map.get("trxstatus")) && "0000".equals(map.get("trxstatus"))) {
                            result = Boolean.TRUE;
                        } else {
                            refundSubOrder.setRefundResult(String.format("code:%s，msg:%s", map.get("trxstatus"), map.get("errmsg")));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                refundSubOrder.setRefundResult(map.get("retmsg"));
            }
            if (result) {
                refundSubOrder.setGmtRefund(Instant.now());
                refundSubOrder.setRefundStatus(RefundOrderStatusEnum.REFUNDED.getValue());
                log.info("银联支付退款：子退款单 {}-{} 退款成功，resp：{}", refundSubOrder.getId(), refundNumber, GsonUtils.toJson(map));
            } else {
                failedList.add(refundSubOrder.getId());
                refundAll = false;
                refundSubOrder.setRefundStatus(RefundOrderStatusEnum.FAILED.getValue());
                log.error("银联支付退款：子退款单 {}-{} 退款失败，resp：{}", refundSubOrder.getId(), refundNumber, GsonUtils.toJson(map));
            }
            refundOrderSubService.saveOne(refundSubOrder);
            if (Objects.equals(refundSubOrder.getRefundStatus(), RefundOrderStatusEnum.REFUNDED.getValue())) {
                RefundCompletedReq refundCompletedReq = RefundCompletedReq.of(refundSubOrder.getRefundOrderId());
                rocketMqUtils.send(RocketMQConstant.TOPIC_COMMON_DELAY, RocketMQConstant.TAG_ORDER_REFUND_COMPLETE, 3000l, refundCompletedReq);
            }
        }
    }

    /**
     * 银联支付子订单退款
     *
     * @param refundList 子订单列表
     */
    public long refundOrder(List<RsCourseRefundOrderSub> refundList) {
        long refundCount = 0L;
        if (ObjectUtil.isEmpty(refundList)) {
            log.info("不存在需要退款的订单");
            return 0L;
        }

        for (RsCourseRefundOrderSub refundOrder : refundList) {
            boolean isRefund = refundOrder(refundOrder);
            if (isRefund) {
                refundCount += 1;
            }
        }
        return refundCount;
    }

    public boolean refundOrder(RsCourseRefundOrderSub refundOrder) {
        Integer subOrderId = refundOrder.getSubOrderId();
        // 获取支付时的订单号
        CourseSubOrder subOrder = subOrderService.getSubOrder(subOrderId).orElseThrow(RecordNotExistedException::new);
        String refundNumber = refundOrder.getRefundNumber();
        String subOrderNumber = subOrder.getNumber();
        //发起银联支付退款
        boolean isRefund = false;
        Integer companyType = refundOrder.getCompanyType();
        Integer mchType = subOrder.getMchType();
        String refundResult;
        UnionPayConfig unionPayConfig = multiUnionPayService.switchoverTo(companyType, mchType).getUnionPayConfig();
        refundOrderSubService.updateRefundStatus(refundOrder.getId(), RefundOrderStatusEnum.IN_PROCESS, null);
        String refund = multiUnionPayService.switchoverTo(companyType, mchType).refund(refundOrder.getRefundFee(), refundNumber, subOrderNumber);
        Map<String, String> map = JSON.parseObject(refund, Map.class);
        refundResult = map.get("retmsg");
        if ("SUCCESS".equals(map.get("retcode"))) {
            TreeMap<String, String> tmap = new TreeMap<>(map);
            String publicKey = unionPayConfig.getTlPublicKey();
            try {
                if (SybUtil.validSign(tmap, publicKey, unionPayConfig.getSignType())) {
                    if (ObjectUtil.isNotEmpty(map.get("trxstatus")) && "0000".equals(map.get("trxstatus"))) {
                        //退款处理
                        isRefund = true;
                    } else {
                        refundResult = String.format("code:%s，msg:%s", map.get("trxstatus"), map.get("errmsg"));
                    }
                } else {
                    log.error("验签失败");
                    refundResult = "验签失败";
                }
            } catch (Exception e) {
                log.error("验签失败");
                refundResult = "验签失败";
            }
        }
        RefundOrderStatusEnum refundStatus = isRefund ? RefundOrderStatusEnum.REFUNDED : RefundOrderStatusEnum.FAILED;
        refundOrderSubService.updateRefundStatus(refundOrder.getId(), refundStatus, refundResult);
        return isRefund;
    }

    private String getUnionPayNotifyUrl(Integer companyType, Integer mchType) {
        String callbackUrl = commonProperties.getCallbackUrl();
        String url = callbackUrl.endsWith("/") ? callbackUrl.substring(0, callbackUrl.length() - 1) : callbackUrl;
        return String.format("%s/unionpay/notify/order/companyType/%s/mchType/%s", url, companyType, mchType);
    }

    private String getUnionPayReturnUrl(String orderNumber) {
        String callbackUrl = commonProperties.getCallbackUrl();
        String url = callbackUrl.endsWith("/") ? callbackUrl.substring(0, callbackUrl.length() - 1) : callbackUrl;
        return String.format("%s/unionpay/return/order/orderNumber/%s", url, orderNumber);
    }

    /**
     * 银联支付订单回调处理
     *
     * @param req
     */
    public void handleUnionPayOrderNotify(UnionPayOrderNotifyReq req) {
        Integer companyType = req.getCompanyType();
        Integer mchType = req.getMchType();
        String notifyBody = req.getNotifyBody();
        Map<String, String> queryMap = multiUnionPayService.switchoverTo(companyType, mchType).handleResult(notifyBody);

        String tradeStatus = queryMap.get("trxstatus");
        if (Objects.equals(tradeStatus, "0000")) {
            String tradeNo = queryMap.get("trxid");
            String orderNumber = queryMap.get("cusorderid");
            String paymentAccount = queryMap.get("acct");
            String buyerPayAmount = queryMap.get("trxamt");
            Integer amount = Integer.valueOf(buyerPayAmount);

            //获取支付订单信息
            PaidOrderInfoResp paidOrderInfo = orderService.getPayableOrderInfo(orderNumber);

            OrderNotifyReq resp = OrderNotifyReq.builder()
                    .companyType(companyType)
                    .mchType(mchType)
                    .transactionId(tradeNo)
                    .orderNumber(orderNumber)
                    .paymentAccount(paymentAccount)
                    .amount(amount)
                    .isSub(paidOrderInfo.getIsSub())
                    .order(paidOrderInfo.getOrder())
                    .subOrder(paidOrderInfo.getSubOrder())
                    .payTime(Instant.now())
                    .build();
            //订单处理
            log.info("银联支付订单交易成功，OrderNotifyReq：{}", resp);
            rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY,
                    OrderRocketMqNameConst.TagType.TAG_PAY_ORDER_NOTIFY, resp);
        } else {
            String msg = TrxstatusCodeEnum.getMsgByCode(tradeStatus);
            log.error("银联支付订单交易失败，code: {}, msg: {}", tradeStatus, msg);
        }
    }

}

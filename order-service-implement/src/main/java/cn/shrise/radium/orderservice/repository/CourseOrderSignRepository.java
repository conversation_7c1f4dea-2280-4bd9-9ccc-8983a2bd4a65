package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsCourseOrderSign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseOrderSignRepository extends JpaRepository<RsCourseOrderSign, Integer>, QuerydslPredicateExecutor<RsCourseOrderSign> {
}

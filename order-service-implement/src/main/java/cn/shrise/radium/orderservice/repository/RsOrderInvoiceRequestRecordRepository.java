package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsOrderInvoiceRequestRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface RsOrderInvoiceRequestRecordRepository extends JpaRepository<RsOrderInvoiceRequestRecord, Long>, QuerydslPredicateExecutor<RsOrderInvoiceRequestRecord> {
}

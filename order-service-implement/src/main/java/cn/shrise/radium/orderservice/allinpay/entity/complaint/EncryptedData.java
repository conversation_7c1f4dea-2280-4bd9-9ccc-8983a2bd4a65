package cn.shrise.radium.orderservice.allinpay.entity.complaint;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "STSPackage")
@XmlAccessorType(XmlAccessType.FIELD)
public class EncryptedData {

    @XmlElement(name = "EncryptedText")
    private String encryptedText;

    private KeyInfo keyInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlRootElement(name = "KeyInfo")
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class KeyInfo {
        @XmlElement(name = "ReceiverX509CertSN")
        private String receiverX509CertSN;

        @XmlElement(name = "EncryptedKey")
        private String encryptedKey;
    }
}

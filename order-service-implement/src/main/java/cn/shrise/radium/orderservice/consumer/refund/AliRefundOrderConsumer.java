package cn.shrise.radium.orderservice.consumer.refund;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.orderservice.constant.OrderRocketMqNameConst;
import cn.shrise.radium.orderservice.entity.RsCourseRefundOrder;
import cn.shrise.radium.orderservice.service.pay.AlipayService;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = OrderRocketMqNameConst.TOPIC_PAY_NOTIFY,
        consumerGroup = OrderRocketMqNameConst.MqGroupType.GID_REFUND_ALI_ORDER,
        selectorExpression = OrderRocketMqNameConst.TagType.ALI_ORDER_REFUND)
public class AliRefundOrderConsumer implements MessageListener {

    private final AlipayService alipayService;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        RsCourseRefundOrder value = JSON.parseObject(message.getBody(), RsCourseRefundOrder.class);
        log.info("收到支付宝订单退款请求消息，退款订单号 refund_pkID: {}",value.getId());
        alipayService.refundOrder(value);
        return Action.CommitMessage;
    }
}

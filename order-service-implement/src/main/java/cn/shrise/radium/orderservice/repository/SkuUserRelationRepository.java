package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsSkuUserRelation;
import cn.shrise.radium.orderservice.entity.RsSkuUserRelationId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface SkuUserRelationRepository extends JpaRepository<RsSkuUserRelation, RsSkuUserRelationId>,
        QuerydslPredicateExecutor<RsSkuUserRelation> {
}

package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.ArticleSeries;
import cn.shrise.radium.orderservice.entity.RsArticleCourse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RsArticleCourseRepository extends JpaRepository<RsArticleCourse, Long>,
        QuerydslPredicateExecutor<RsArticleCourse> {
}

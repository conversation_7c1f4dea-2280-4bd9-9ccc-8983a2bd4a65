package cn.shrise.radium.orderservice.allinpay.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AllInPayConfig {

    private String serialNumber;
    private String instId;
    private String version;
    private String sepAccount;
    private String sepBankId;

    /**
     * 商户证书路径
     */
    private String certificatePath;

    /**
     * 商户证书密码
     */
    private String certificatePassword;

    /**
     * 通联公钥路径
     */
    private String publicKeyPath;
}

package cn.shrise.radium.orderservice.conf.bean;

import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.ServerProperties;
import cn.shrise.radium.orderservice.allinpay.config.AllInPayConfig;
import cn.shrise.radium.orderservice.allinpay.http.AllInPayFeignHttpClient;
import cn.shrise.radium.orderservice.allinpay.service.AllInPayServiceClient;
import cn.shrise.radium.orderservice.allinpay.service.impl.AllInPayServiceClientImpl;
import lombok.ToString;

import java.util.Map;

/**
 * <AUTHOR>
 */
@ToString
public class MultiAllinPayService {
    private final Map<String, AllInPayConfig> payServiceMap;

    private final AllInPayFeignHttpClient allInPayFeignHttpClient;

    public MultiAllinPayService(Map<String, AllInPayConfig> payServiceMap, AllInPayFeignHttpClient allInPayFeignHttpClient) {
        this.payServiceMap = payServiceMap;
        this.allInPayFeignHttpClient = allInPayFeignHttpClient;
    }


    /**
     * 切换到对应的通联支付支付服务
     *
     * @param mchType 商户类型
     * @return service
     */
    public AllInPayServiceClient switchoverTo(Integer companyType, Integer mchType) {
        AllInPayConfig allInPayConfig = payServiceMap.get(String.format("%s_%s", companyType, mchType));
        if (allInPayConfig == null) {
            throw new RecordNotExistedException("未找到对应公司的通联支付配置");
        }
        AllInPayServiceClient service = new AllInPayServiceClientImpl(allInPayFeignHttpClient);
        service.setMultiConfig(payServiceMap);
        return service;
    }
}

package cn.shrise.radium.orderservice.ext.wechatpay.bean;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintDetailResultExt {

    /**
     * <pre>
     * 字段名：投诉单号
     * 是否必填：是
     * 描述：投诉单对应的投诉单号
     * </pre>
     */
    @JsonProperty("complaint_id")
    private String complaintId;

    /**
     * <pre>
     * 字段名：投诉时间
     * 是否必填：是
     * 描述：投诉时间，遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss.sss+TIMEZONE，yyyy-MM-DD表示年月日，
     * T出现在字符串中，表示time元素的开头，HH:mm:ss.sss表示时分秒毫秒，TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35.120+08:00表示北京时间2015年05月20日13点29分35秒
     * 示例值：2015-05-20T13:29:35.120+08:00
     * </pre>
     */
    @JsonProperty("complaint_time")
    private String complaintTime;

    /**
     * <pre>
     * 字段名：投诉详情
     * 是否必填：是
     * 投诉的具体描述
     * </pre>
     */
    @JsonProperty("complaint_detail")
    private String complaintDetail;

    /**
     * <pre>
     * 字段名：投诉单状态
     * 是否必填：是
     * 标识当前投诉单所处的处理阶段，具体状态如下所示：
     * PENDING：待处理
     * PROCESSING：处理中
     * PROCESSED：已处理完成
     * </pre>
     */
    @JsonProperty("complaint_state")
    private String complaintState;

    /**
     * <pre>
     * 字段名：投诉人联系方式
     * 是否必填：否
     * 投诉人联系方式。该字段已做加密处理，具体解密方法详见敏感信息加密说明。
     * </pre>
     */
    @JsonProperty("payer_phone")
    private String payerPhone;

    /**
     * <pre>
     * 字段名：投诉人openid
     * 是否必填：是
     * 投诉人在商户appid下的唯一标识
     * </pre>
     */
    @JsonProperty("payer_openid")
    private String payerOpenid;


    /**
     * <pre>
     * 字段名：投诉资料列表
     * 是否必填：是
     * 用户上传的投诉相关资料，包括图片凭证等
     * </pre>
     */
    @JsonProperty("complaint_media_list")
    private List<ComplaintMedia> complaintMediaList;

    @Data
    public static class ComplaintMedia {
        private static final long serialVersionUID = 4240983048700956803L;

        /**
         * <pre>
         * 字段名：媒体文件业务类型
         * 是否必填：是
         * 描述：
         * 媒体文件对应的业务类型
         * USER_COMPLAINT_IMAGE：用户投诉图片，用户提交投诉时上传的图片凭证
         * OPERATION_IMAGE：操作流水图片，用户、商户、微信支付客服在协商解决投诉时，上传的图片凭证
         * 注：用户上传的图片凭证会以白名单的形式提供给商户，若希望查看用户图片，联系微信支付客服
         * 示例值：USER_COMPLAINT_IMAGE
         * </pre>
         */
        @JsonProperty("media_type")
        private String mediaType;

        /**
         * <pre>
         * 字段名：媒体文件请求url
         * 是否必填：是
         * 描述：
         * 微信返回的媒体文件请求url
         * </pre>
         */
        @JsonProperty("media_url")
        private List<String> mediaUrl;

    }

    /**
     * <pre>
     * 字段名：投诉单关联订单信息
     * 是否必填：是
     * 投诉单关联订单信息
     * 注：投诉单和订单目前是一对一关系，array是预留未来一对多的扩展
     * </pre>
     */
    @JsonProperty("complaint_order_info")
    private List<ComplaintOrder> complaintOrderInfo;

    @Data
    public static class ComplaintOrder {
        private static final long serialVersionUID = 4240983048700956804L;

        /**
         * <pre>
         * 字段名：微信订单号
         * 是否必填：是
         * 描述：
         * 投诉单关联的微信订单号
         * </pre>
         */
        @JsonProperty("transaction_id")
        private String transactionId;

        /**
         * <pre>
         * 字段名：商户订单号
         * 是否必填：是
         * 描述：
         * 投诉单关联的商户订单号
         * </pre>
         */
        @JsonProperty("out_trade_no")
        private String outTradeNo;

        /**
         * <pre>
         * 字段名：订单金额
         * 是否必填：是
         * 描述：
         * 订单金额，单位（分）
         * </pre>
         */
        @JsonProperty("amount")
        private Integer amount;

    }

    /**
     * <pre>
     * 字段名：投诉单是否已全额退款
     * 是否必填：是
     * 描述：
     * 投诉单下所有订单是否已全部全额退款
     * </pre>
     */
    @JsonProperty("complaint_full_refunded")
    private Boolean complaintFullRefunded;

    /**
     * <pre>
     * 字段名：是否有待回复的用户留言
     * 是否必填：是
     * 描述：
     * 投诉单是否有待回复的用户留言
     * </pre>
     */
    @JsonProperty("incoming_user_response")
    private Boolean incomingUserResponse;

    /**
     * <pre>
     * 字段名：问题描述
     * 是否必填：是
     * 描述：
     * 用户发起投诉前选择的faq标题（2021年7月15日之后的投诉单均包含此信息）
     * </pre>
     */
    @JsonProperty("problem_description")
    private String problemDescription;

    /**
     * <pre>
     * 字段名：用户投诉次数
     * 是否必填：是
     * 描述：
     * 用户投诉次数。用户首次发起投诉记为1次，用户每有一次继续投诉就加1
     * </pre>
     */
    @JsonProperty("user_complaint_times")
    private Integer userComplaintTimes;

    //问题类型
    @JsonProperty("problem_type")
    private String problemType;

    //【申请退款金额】 仅当问题类型为申请退款时, 有值, (单位:分)
    @JsonProperty("apply_refund_amount")
    private Integer applyRefundAmount;

    //用户标签列表
    @JsonProperty("user_tag_list")
    private List<String> userTagList;

    //投诉单关联服务单信息
    @JsonProperty("service_order_info")
    private List<ServiceOrderInfo> serviceOrderInfo;

    //补充信息
    @JsonProperty("additional_info")
    private AdditionalInfo additionalInfo;

    //是否在平台协助中
    @JsonProperty("in_platform_service")
    private Boolean inPlatformService;

    //是否需即时服务用户
    @JsonProperty("need_immediate_service")
    private Boolean needImmediateService;

    @Data
    public static class ServiceOrderInfo {

        //微信支付服务订单号
        @JsonProperty("order_id")
        private String orderId;

        //商户服务订单号
        @JsonProperty("out_order_no")
        private String outOrderNo;

        //支付分服务单状态
        @JsonProperty("state")
        private String state;
    }

    @Data
    public static class AdditionalInfo {

        //补充信息类型
        @JsonProperty("type")
        private String type;

        //充电宝投诉相关信息
        @JsonProperty("share_power_info")
        private SharePowerInfo sharePowerInfo;

    }

    @Data
    public static class SharePowerInfo {

        //归还时间
        @JsonProperty("return_time")
        private String returnTime;

        //归还地点信息
        @JsonProperty("return_address_info")
        private ReturnAddressInfo returnAddressInfo;

        //是否归还同一柜机
        @JsonProperty("is_returned_to_same_machine")
        private Boolean isReturnedToSameMachine;

    }

    @Data
    public static class ReturnAddressInfo {

        //归还地点
        @JsonProperty("return_address")
        private String returnAddress;

        //归还地点经度
        @JsonProperty("longitude")
        private String longitude;

        //归还地点纬度
        @JsonProperty("latitude")
        private String latitude;
    }
}

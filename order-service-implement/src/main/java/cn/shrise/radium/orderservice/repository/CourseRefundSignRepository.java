package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsCourseRefundOrder;
import cn.shrise.radium.orderservice.entity.RsCourseRefundSign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface CourseRefundSignRepository extends JpaRepository<RsCourseRefundSign, Integer>, QuerydslPredicateExecutor<RsCourseRefundSign> {

}

package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.TransferOrderFlowRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface TransferOrderFlowRecordRepository extends JpaRepository<TransferOrderFlowRecord, Long>,
        QuerydslPredicateExecutor<TransferOrderFlowRecord> {
}

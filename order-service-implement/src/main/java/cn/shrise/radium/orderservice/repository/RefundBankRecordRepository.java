package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsRefundBankRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/4/25, 星期五
 **/
@Repository
public interface RefundBankRecordRepository extends JpaRepository<RsRefundBankRecord, Integer>, QuerydslPredicateExecutor<RsRefundBankRecord> {

}

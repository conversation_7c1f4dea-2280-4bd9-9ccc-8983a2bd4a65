package cn.shrise.radium.orderservice.util.esign;

import cn.shrise.radium.common.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GenSignNumber {

    private final RedisUtil redisUtil;

    public String getSignNumber(Integer company, String date) {
        String key = String.format("sign_number:%d:%s", company, date);
        Long number = redisUtil.incr(key);
        redisUtil.setExpire(key, 24 * 60 * 60L);
        String n = "000" + number.toString();
        return "sh" + date + n.substring(n.length() - 4);
    }

    public String getRefundSignNumber(Integer company, String date) {
        String key = String.format("refund_sign_number:%d:%s", company, date);
        Long number = redisUtil.incr(key);
        redisUtil.setExpire(key, 24 * 60 * 60L);
        String n = "000" + number.toString();
        return "rf" + date + n.substring(n.length() - 4);
    }
}

package cn.shrise.radium.orderservice.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.constant.RocketMQConstant;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.marketingservice.resp.OrderDealtResp;
import cn.shrise.radium.orderservice.conf.bean.MultiUnionPayService;
import cn.shrise.radium.orderservice.constant.*;
import cn.shrise.radium.orderservice.constant.payment.MerchantCategoryEnum;
import cn.shrise.radium.orderservice.constant.payment.PaymentChannelTypeEnum;
import cn.shrise.radium.orderservice.constant.payment.PaymentPlatformEnum;
import cn.shrise.radium.orderservice.dao.RsMerchantDao;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.req.*;
import cn.shrise.radium.orderservice.service.WebOrderService;
import cn.shrise.radium.orderservice.service.pay.AlipayService;
import cn.shrise.radium.orderservice.service.pay.AllinPayService;
import cn.shrise.radium.orderservice.service.pay.LklPayService;
import cn.shrise.radium.orderservice.service.pay.WechatPayService;
import cn.shrise.radium.orderservice.service.payment.LLianPaymentService;
import cn.shrise.radium.orderservice.service.payment.MerchantService;
import cn.shrise.radium.orderservice.service.sku.SkuService;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.entity.UcWxAccount;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import cn.shrise.radium.wxservice.entity.WxAccountDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.RocketMQConstant.TAG_PAYMENT_PROMOTION_ORDER_REFUND;
import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_PAYMENT;
import static cn.shrise.radium.common.util.LockUtils.getOrderLockKey;
import static cn.shrise.radium.common.util.LockUtils.locked;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderNotifyService {

    private final OrderService orderService;
    private final WebOrderService webOrderService;
    private final SubOrderService subOrderService;
    private final OrderFlowService orderFlowService;
    private final RedissonClient redissonClient;
    private final RocketMqUtils rocketMqUtils;
    private final SkuService skuService;
    private final WxClient wxClient;
    private final WechatPayService wechatPayService;
    private final AlipayService alipayService;
    private final TransferOrderFlowService transferOrderFlowService;
    private final MultiUnionPayService multiUnionPayService;
    private final UserClient userClient;
    private final AllinPayService allinPayService;
    private final LklPayService lklPayService;
    private final RsMerchantDao rsMerchantDao;
    private final LLianPaymentService lLianPaymentService;
    private final MerchantService merchantService;

    /**
     * 已支付订单回调处理
     */
    public void handleOrderNotify(OrderNotifyReq notifyReq) {
        final RsCourseOrder mainOrder = notifyReq.getOrder();
        String lockKey = getOrderLockKey(mainOrder.getId());
        RLock lock = redissonClient.getLock(lockKey);
        // lock by orderId
        locked(lock, () -> {
            handleOrderNotifyWithoutLock(notifyReq);
            return null;
        });

    }

    public void handleOrderNotifyWithoutLock(OrderNotifyReq notifyReq) {
        final String orderNumber = notifyReq.getOrderNumber();
        final boolean isSub = notifyReq.getIsSub();
        final RsCourseOrder mainOrder = notifyReq.getOrder();
        final CourseSubOrder subOrder = notifyReq.getSubOrder();
        final Integer notifyAmount = notifyReq.getAmount();
        Instant payTime = notifyReq.getPayTime() != null ? notifyReq.getPayTime() : Instant.now();
        final Integer payAmount = isSub ? subOrder.getAmount() : mainOrder.getAmount();

        if (!Objects.equals(notifyAmount, payAmount)) {
            log.error("支付通知的金额与订单上的金额不一致, orderNumber: {}, payAmount: {}, notifyAmount: {}",
                    orderNumber, payAmount, notifyAmount);
            return;
        }

        //更新订单状态
        boolean updated = updateOrderStatusToPass(notifyReq);
        if (!updated) {
            log.error("支付通知订单上与数据库的状态不一致, orderNumber: {}", orderNumber);
            return;
        }

        // 主订单是否完成
        Integer wxAccountId = null;
        Integer wxId = null;
        Integer userId = null;
        boolean isMainOrderFinish = !isSub;
        if (isSub) {
            int currentOrderAmount = mainOrder.getAmount();

            List<CourseSubOrder> paidSubOrderList = subOrderService.findAllByOrderId(subOrder.getOrderId(),
                    Collections.singletonList(OrderStatusConstant.PASSED.getValue()));
            int paidAmount = paidSubOrderList.stream()
                    .filter(e -> !Objects.equals(e.getId(), subOrder.getId()) && Objects.nonNull(e.getAmount()))
                    .mapToInt(CourseSubOrder::getAmount)
                    .sum();

            // 子订单支付总金额==主订单金额，将主订单状态置为已完成
            int totalPayAmount = paidAmount + notifyAmount;
            if (totalPayAmount == currentOrderAmount) {
                if (ObjectUtil.isAllEmpty(mainOrder.getUserId(), mainOrder.getWxId())) {
                    // 微信授权无用户信息特殊处理
                    WxAccountDTO wxAccountDTO = wxClient.getWxAccount(notifyReq.getAccountType(), notifyReq.getPaymentAccount()).orElse(new WxAccountDTO());
                    UcWxAccount wxAccount = ObjectUtil.isNotEmpty(wxAccountDTO.getWxAccount()) ? wxAccountDTO.getWxAccount() : new UcWxAccount();
                    UcWxExt ucWxExt = ObjectUtil.isNotEmpty(wxAccountDTO.getUcWxExt()) ? wxAccountDTO.getUcWxExt() : new UcWxExt();
                    wxAccountId = wxAccount.getId();
                    wxId = wxAccount.getWxId();
                    userId = ucWxExt.getUserId();
                }
                isMainOrderFinish = true;
            } else if (totalPayAmount > currentOrderAmount) {
                log.error("已支付金额超过订单金额");
            } else {
                orderService.updateExpireTime(mainOrder.getId(), null);
            }
        }

        if (isMainOrderFinish) {
            wxAccountId = ObjectUtil.isEmpty(mainOrder.getWxAccountId()) ? wxAccountId : mainOrder.getWxAccountId();
            wxId = ObjectUtil.isEmpty(mainOrder.getWxId()) ? wxId : mainOrder.getWxId();
            userId = ObjectUtil.isEmpty(mainOrder.getUserId()) ? userId : mainOrder.getUserId();
            // 订单后续处理
            mainOrder.setPayTime(payTime);
            mainOrder.setWxAccountId(wxAccountId);
            mainOrder.setWxId(wxId);
            mainOrder.setUserId(userId);
            handleOrderFinished(mainOrder);

            // 非营销sku,置用户订单标志位
            Optional<RsSku> skuOptional = skuService.getSku(mainOrder.getSkuId());
            if (skuOptional.isPresent() && Objects.equals(skuOptional.get().getIsPromotion(), false)) {
                userClient.updateCustomerExtTraded(userId);
            }

            if (isSub && skuOptional.isPresent() && Objects.equals(skuOptional.get().getIsPromotion(), true)) {
                //todo 待商户号开通h5支付产品后继续
//                if (StringUtils.equals(notifyReq.getTradeType(), WechatPayTradeTypeEnum.MWEB.name())) {
//                    promotionBindUnionNotify(notifyReq, subOrder, mainOrder);
//                }
                // 营销单支付成功回传
                promotionConvertNotify(mainOrder);
            }

            //支付订单退款
            RsMerchantInfo merchantInfo = null;
            if (ObjectUtil.isNotNull(subOrder.getMerchantId())) {
                merchantInfo = merchantService.getMerchantInfoById(subOrder.getMerchantId());
            }
            if (isSub && Objects.equals(mainOrder.getAmount(), subOrder.getAmount()) && ObjectUtil.isNotNull(merchantInfo) &&
                    Objects.equals(merchantInfo.getCategory(), MerchantCategoryEnum.MC_ADVERTISEMENT.getCode())) {
                Optional<RsSku> skuRes = skuService.getSku(mainOrder.getSkuId());
                if (skuRes.isPresent() && Objects.equals(skuRes.get().getIsPromotion(), true) && ObjectUtil.isNotEmpty(skuRes.get().getPrice()) && skuRes.get().getPrice() <= 300) {
                    // 获取当前时间
                    LocalTime now = LocalTime.now();

                    // 定义时间区间
                    LocalTime start = LocalTime.of(5, 0); // 早上5点
                    LocalTime end = LocalTime.of(23, 59); // 晚上23点59分

                    //早八到晚九顺延5分钟退款，其余时间顺延8小时退款
                    if (now.isAfter(start) && now.isBefore(end)) {
                        rocketMqUtils.send(TOPIC_PAYMENT, TAG_PAYMENT_PROMOTION_ORDER_REFUND, 5 * 60 * 1000L, subOrder);
                    } else {
                        rocketMqUtils.send(TOPIC_PAYMENT, TAG_PAYMENT_PROMOTION_ORDER_REFUND, 8 * 60 * 60 * 1000L, subOrder);
                    }
                }
            }
            // 订单支付成功,开通营销服务
            rocketMqUtils.send(RocketMQConstant.TOPIC_MARKETING, RocketMQConstant.TAG_PROMOTION_SERVICE_SUBSCRIPTION, mainOrder);
            // 关闭子订单
            rocketMqUtils.send(RocketMQConstant.TOPIC_PAYMENT, "payment_close_sub_order", mainOrder);
        }
        /* 初始化商务审核状态 */
        orderFlowService.createOrderFlow(mainOrder.getId());

    }

    private void promotionConvertNotify(RsCourseOrder mainOrder) {
        PaidPromotionOrderConvertReq convertReq = PaidPromotionOrderConvertReq.builder()
                .companyType(mainOrder.getCompanyType())
                .actionType(80)
                .orderId(mainOrder.getId())
                .wxId(mainOrder.getWxId())
                .build();
        rocketMqUtils.send(RocketMQConstant.TOPIC_PAY, RocketMQConstant.TAG_PAID_PROMOTION_ORDER_ACTION, convertReq);
    }

    private void promotionBindUnionNotify(OrderNotifyReq notifyReq, CourseSubOrder subOrder, RsCourseOrder mainOrder) {
        QueryOrderUnionIdReq orderUnionIdReq = QueryOrderUnionIdReq.builder()
                .openId(notifyReq.getOpenId())
                .mainOrderId(mainOrder.getId())
                .subOrderNumber(subOrder.getNumber())
                .mchType(notifyReq.getMchType())
                .transactionId(notifyReq.getTransactionId())
                .build();
        rocketMqUtils.send(RocketMQConstant.TOPIC_PAY, RocketMQConstant.TAG_WECHAT_H5_PAY_ORDER_UNION_BIND_NOTIFY, orderUnionIdReq);
    }

    public void handleOrderFinished(RsCourseOrder mainOrder) {
        mainOrder.setOrderStatus(OrderStatusConstant.PASSED.getValue());
        orderService.updateById(
                mainOrder.getId(),
                OrderStatusConstant.PASSED.getValue(),
                mainOrder.getPayTime(),
                mainOrder.getWxAccountId(),
                mainOrder.getWxId(),
                mainOrder.getUserId()
        );
        log.info("订单完成, orderNumber: {}", mainOrder.getOrderNumber());

        // 创建订单扩展信息
        rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY,
                OrderRocketMqNameConst.TagType.TAG_ORDER_INFO_EXT, mainOrder);
        // 开通服务
//        rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY,
//                OrderRocketMqNameConst.TagType.TAG_OPEN_ORDER_SERVICE, mainOrder);
        // 发站内信
        rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY,
                OrderRocketMqNameConst.TagType.TAG_SEND_PAY_AGENT, mainOrder);
        // 发送模板消息
//        notifyReq.setOrder(mainOrder);
//        rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY,
//                OrderRocketMqNameConst.TagType.TAG_SEND_PAY_SUCCESS_MSG, notifyReq);
        // 微信公众号支付回调
        rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY, OrderRocketMqNameConst.TagType.WX_ACCOUNT_ORDER_CONVERT, mainOrder);

        Optional<RsSku> skuRes = skuService.getSku(mainOrder.getSkuId());
        if (skuRes.isPresent()) {
            Integer actionType;
            if (SkuProductLevelConstant.getLevelOne().contains(skuRes.get().getProductLevel())) {
                //兴趣标签 暂停使用
//            rocketMqUtils.send(RocketMQConstant.TOPIC_USER,
//                    RocketMQConstant.TAG_INTEREST_TAG_HANDLE, mainOrder.getUserId());
                //一档成交订单
                actionType = 41;
            } else if (SkuProductLevelConstant.getLargeLevel().contains(skuRes.get().getProductLevel())) {
                //大单成交
                actionType = 43;
            } else {
                return;
            }
            OrderDealtResp orderDealtResp = OrderDealtResp.builder()
                    .companyType(mainOrder.getCompanyType())
                    .orderId(mainOrder.getId())
                    .payTime(mainOrder.getPayTime())
                    .userId(mainOrder.getUserId())
                    .actionType(actionType)
                    .build();
            rocketMqUtils.send(RocketMQConstant.TOPIC_WORK_WX, RocketMQConstant.TAG_DEALT_ORDER_ACTION, orderDealtResp);
        }

    }

    private boolean updateOrderStatusToPass(OrderNotifyReq orderNotifyReq) {
        Boolean isSub = orderNotifyReq.getIsSub();
        RsCourseOrder mainOrder = orderNotifyReq.getOrder();
        CourseSubOrder subOrder = orderNotifyReq.getSubOrder();
        String transactionId = orderNotifyReq.getTransactionId();
        Instant payTime = orderNotifyReq.getPayTime() != null ? orderNotifyReq.getPayTime() : Instant.now();
        String bankType = orderNotifyReq.getBankType();
        String paymentAccount = orderNotifyReq.getPaymentAccount();
        if (!isSub) {
            // 未分单
            UpdateOrderStatusToPassReq updateOrderStatusToPassReq = UpdateOrderStatusToPassReq.builder()
                    .id(mainOrder.getId())
                    .payTime(payTime)
                    .build();
            return orderService.updateOrder(updateOrderStatusToPassReq);
        } else {
            // 分单
            UpdateSubOrderStatusToPassReq updateSubOrderStatusToPassReq = UpdateSubOrderStatusToPassReq.builder()
                    .id(subOrder.getId())
                    .serialNumber(transactionId)
                    .bankType(bankType)
                    .paymentAccount(paymentAccount)
                    .payTime(payTime)
                    .build();
            return subOrderService.updateSubOrder(updateSubOrderStatusToPassReq);
        }
    }

    /**
     * 微信h5支付回调处理
     * 仅处理营销H5支付
     */
    public void handleWebOrderNotify(OrderNotifyReq notifyReq) {
        String orderNumber = notifyReq.getOrderNumber();
        String transactionId = notifyReq.getTransactionId();
        String openid = notifyReq.getPaymentAccount();

        // h5支付，仅更新订单状态和open id
        Optional<RsWxWebOrder> optional = webOrderService.getWebOrder(orderNumber);
        if (optional.isPresent()) {
            RsWxWebOrder order = optional.get();
            if (ObjectUtil.isNotEmpty(order) && Objects.equals(order.getOrderStatus(), OrderStatusConstant.PAYING.getValue())) {
                webOrderService.updateById(order.getId(), OrderStatusConstant.PASSED.getValue(), transactionId,
                        Instant.now(), openid);
                // h5支付订单回传
                rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY, OrderRocketMqNameConst.TagType.H5_ORDER_CONVERT, order);
            } else {
                log.error("WxPayNotifyHandler【h5支付】服务端数据库不存在该订单或已支付成功");
            }
        }
    }

    //同步订单状态
    public BaseResult<Void> syncOrder(Integer orderId) {
        String lockKey = getOrderLockKey(orderId);
        RLock lock = redissonClient.getLock(lockKey);
        return locked(lock, () -> {
            RsCourseOrder order = orderService.getOrder(orderId).orElseThrow(RecordNotExistedException::new);
            if (!Objects.equals(order.getOrderStatus(), OrderStatusEnum.PAYING.getValue())) {
                return BaseResult.failed("订单状态无法同步");
            }

            List<CourseSubOrder> subOrderList = subOrderService.getSubOrderList(orderId);
            List<CourseSubOrder> unpaidSubOrderList = subOrderList.stream()
                    .filter(subOrder -> Objects.equals(subOrder.getOrderStatus(), OrderStatusEnum.PAYING.getValue()))
                    .collect(Collectors.toList());
            int paidAmount = subOrderList.stream()
                    .filter(subOrder -> Objects.equals(subOrder.getOrderStatus(), OrderStatusEnum.PASSED.getValue()))
                    .mapToInt(CourseSubOrder::getAmount)
                    .sum();

            if (paidAmount >= order.getAmount()) {
                // only sync main order
                order.setPayTime(Instant.now());
                handleOrderFinished(order);
            } else {
                unpaidSubOrderList.forEach(subOrder -> {
                    //query order status from wechat/alipay/unionpay/allinPay
                    boolean isPaid = isPaidOrder(order.getCompanyType(), subOrder, order.getUserId(), order.getWxId());
                    log.info("syncOrder: isPaid={}, subOrderNumber={}", isPaid, subOrder.getNumber());
                    if (isPaid) {
                        OrderNotifyReq orderNotifyReq = OrderNotifyReq.builder()
                                .companyType(order.getCompanyType())
                                .paymentAccount(subOrder.getPaymentAccount())
                                .orderNumber(subOrder.getNumber())
                                .isSub(true)
                                .order(order)
                                .subOrder(subOrder)
                                .payTime(Instant.now())
                                .mchType(subOrder.getMchType())
                                .transactionId(subOrder.getSerialNumber())
                                .bankType(subOrder.getBankType())
                                .amount(subOrder.getAmount())
                                .build();
                        //fix status
                        handleOrderNotifyWithoutLock(orderNotifyReq);
                    }
                });
            }

            return BaseResult.successful();
        });

    }


    public boolean isPaidOrder(Integer companyType, CourseSubOrder subOrder, Integer userId, Integer wxId) {
        String orderNumber = subOrder.getNumber();
        Integer payType = subOrder.getPayType();
        Integer mchType = subOrder.getMchType();
        Long merchantId = subOrder.getMerchantId();
        RsMerchantInfo merchantInfo = rsMerchantDao.getMerchantInfoById(merchantId);

        if (Objects.equals(payType, PayTypeConstant.WX.getValue())) {
            //微信支付
            return wechatPayService.isPaidOrder(companyType, mchType, wxId, orderNumber);
        } else if (Objects.equals(payType, PayTypeConstant.ALI.getValue())) {
            //支付宝支付
            return alipayService.isPaidOrder(companyType, mchType, orderNumber);
        } else if (Objects.equals(payType, PayTypeConstant.UNION.getValue())) {
            //unionpay
            return multiUnionPayService.switchoverTo(companyType, mchType).isPaidOrder(orderNumber);
        } else if (Objects.equals(payType, PayTypeConstant.BANK.getValue())) {
            //transfer
            return transferOrderFlowService.isPaidOrder(subOrder.getId());
        } else if (Objects.equals(payType, PayTypeConstant.Allin.getValue())) {
            //通联allinPay
            return allinPayService.isPaidOrder(companyType, mchType, subOrder);
        } else if (Objects.equals(payType, PayTypeConstant.LKL.getValue())) {
            //拉卡拉微信
            return lklPayService.isPaidOrder(orderNumber);
        } else if (Objects.equals(payType, PayTypeConstant.LKL_ALI.getValue())) {
            //拉卡拉支付宝
            return lklPayService.isPaidOrder(orderNumber);
        }

        if (Objects.equals(merchantInfo.getPlatform(), PaymentPlatformEnum.PP_Wx.getCode())) {
            // 连连微信
            if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_LLian.getCode())) {
                return lLianPaymentService.isPaidOrder(merchantId, orderNumber);
            }
        } else if (Objects.equals(merchantInfo.getPlatform(), PaymentPlatformEnum.PP_Ali.getCode())) {
            // 连连支付宝
            if (Objects.equals(merchantInfo.getChannelType(), PaymentChannelTypeEnum.PCT_LLian.getCode())) {
                return lLianPaymentService.isPaidOrder(merchantId, orderNumber);
            }
        }
        return false;
    }
}

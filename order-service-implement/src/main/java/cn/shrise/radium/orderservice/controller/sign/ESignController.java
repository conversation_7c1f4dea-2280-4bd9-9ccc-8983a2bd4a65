package cn.shrise.radium.orderservice.controller.sign;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.orderservice.conf.ESignConfiguration;
import cn.shrise.radium.orderservice.properties.esign.CompaniesConfig;
import cn.shrise.radium.orderservice.properties.esign.SignConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("e-sign")
public class ESignController {

    private final ESignConfiguration esignConfiguration;

    @GetMapping("config")
    @ApiOperation("获取签字业务配置")
    public BaseResult<List<SignConfig>> getSignConfig() {
        List<SignConfig> signList = esignConfiguration.getSignList();
        return BaseResult.success(signList);
    }

    @GetMapping("companies/config")
    @ApiOperation("获取主体配置")
    public BaseResult<List<CompaniesConfig>> getCompaniesConfig() {
        List<CompaniesConfig> companiesConfig =  esignConfiguration.getCompaniesList();
        return BaseResult.success(companiesConfig);
    }
}

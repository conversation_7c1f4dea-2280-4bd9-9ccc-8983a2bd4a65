package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsEsignFaceAuthRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RsEsignFaceAuthRecordRepository extends JpaRepository<RsEsignFaceAuthRecord, Long>,
        QuerydslPredicateExecutor<RsEsignFaceAuthRecord> {
}

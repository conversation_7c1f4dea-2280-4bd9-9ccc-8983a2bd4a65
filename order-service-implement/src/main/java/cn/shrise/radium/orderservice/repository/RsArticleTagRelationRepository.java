package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.ArticleTagRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RsArticleTagRelationRepository extends JpaRepository<ArticleTagRelation, Long>, QuerydslPredicateExecutor<ArticleTagRelation> {
}

package cn.shrise.radium.orderservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.resp.LiveRoomNameResp;
import cn.shrise.radium.orderservice.constant.ServiceOperateTypeEnum;
import cn.shrise.radium.orderservice.constant.ServiceTypeConstant;
import cn.shrise.radium.orderservice.dto.CustomerServiceDto;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.repository.*;
import cn.shrise.radium.orderservice.req.ServiceKey;
import cn.shrise.radium.orderservice.service.serving.ArticleSeriesService;
import cn.shrise.radium.orderservice.service.serving.CommonSeriesService;
import cn.shrise.radium.orderservice.service.serving.CourseSeriesService;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.*;

@Service
@RequiredArgsConstructor
public class CourseServService {

    private final JPAQueryFactory jpaQueryPrimary;
    private final ContentClient contentClient;

    private final RsCourseServiceRepository rsCourseServiceRepository;
    private final RsCourseSeriesRepository rsCourseSeriesRepository;
    private final RsCommonServiceRepository rsCommonServiceRepository;
    private final RsCourseServiceRecordRepository rsCourseServiceRecordRepository;
    private final RsCourseServiceOptionRepository rsCourseServiceOptionRepository;

    private final QRsCourseService qCourseService = QRsCourseService.rsCourseService;
    private final QRsCourseServiceOption qRsCourseServiceOption = QRsCourseServiceOption.rsCourseServiceOption;
    private final QRsCommonService qRsCommonService = QRsCommonService.rsCommonService;
    private final CommonSeriesService commonSeriesService;
    private final CourseSeriesService courseSeriesService;
    private final ArticleSeriesService articleSeriesService;

    public RsCourseService findOneByFilter(Integer userId, Integer seriesId, Integer serviceType, Boolean isEnabled) {
        QRsCourseService courseService = QRsCourseService.rsCourseService;
        JPAQuery<RsCourseService> query = jpaQueryPrimary.select(courseService).from(courseService)
                .where(courseService.customerId.eq(userId))
                .where(courseService.seriesId.eq(seriesId))
                .where(courseService.serviceType.eq(serviceType));
        if (isEnabled != null) {
            query = query.where(courseService.enabled.eq(isEnabled));
        }
        return query.fetchFirst();
    }

    @Transactional
    public void createOrUpdateOne(Integer userId, Integer seriesId, Integer serviceType, Integer periodDay) {
        QRsCourseService courseService = QRsCourseService.rsCourseService;
        RsCourseService service = this.findOneByFilter(userId, seriesId, serviceType, null);
        Instant nowTime = Instant.now();
        if (service != null) {
            Instant expireTime;
            Instant openTime;
            if (nowTime.isAfter(service.getExpireTime())) {
                expireTime = nowTime.plus(periodDay, ChronoUnit.DAYS);
                openTime = nowTime;
            } else {
                expireTime = service.getExpireTime().plus(periodDay, ChronoUnit.DAYS);
                openTime = service.getOpenTime();
            }
            expireTime = LocalDateTime.ofInstant(expireTime, ZoneId.systemDefault())
                    .with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
            jpaQueryPrimary.update(courseService).where(courseService.id.eq(service.getId()))
                    .set(courseService.expireTime, expireTime.truncatedTo(ChronoUnit.SECONDS))
                    .set(courseService.openTime, openTime.truncatedTo(ChronoUnit.SECONDS))
                    .set(courseService.enabled, true).execute();
        } else {
            Instant expireTime = LocalDateTime.ofInstant(nowTime.plus(periodDay, ChronoUnit.DAYS), ZoneId.systemDefault())
                    .with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
            RsCourseService record = RsCourseService.builder()
                    .customerId(userId)
                    .seriesId(seriesId)
                    .serviceType(serviceType)
                    .openTime(nowTime.truncatedTo(ChronoUnit.SECONDS))
                    .expireTime(expireTime.truncatedTo(ChronoUnit.SECONDS))
                    .enabled(true)
                    .build();
            rsCourseServiceRepository.save(record);
//            queryFactory.insert(courseService).columns(courseService.customerId, courseService.seriesId,
//                    courseService.serviceType, courseService.openTime, courseService.expireTime)
//                    .values(userId, seriesId, serviceType, nowTime, expireTime).execute();
        }
    }

    /**
     * 查找符合条件的方案 分页查询
     *
     * @param customerId  客户id
     * @param hideExpired 隐藏已过期的
     * @param serviceType 服务类型
     * @param current     当前页
     * @param size        每页条数
     * @return
     */
    public PageResult<List<RsCourseService>> findServiceByFilter(@NonNull Integer customerId, Boolean hideExpired, Integer serviceType, Integer current, Integer size) {
        JPAQuery<RsCourseService> query = jpaQueryPrimary.select(qCourseService).from(qCourseService)
                .where(qCourseService.customerId.eq(customerId));

        if (ObjectUtil.isNotEmpty(serviceType)) {
            query.where(qCourseService.serviceType.eq(serviceType));
        }

        if (ObjectUtil.isNotEmpty(hideExpired) && hideExpired) {
            query.where(qCourseService.expireTime.goe(Instant.now()));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qCourseService.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    /**
     * @param customerId
     * @param hideExpired
     * @param serviceType
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<CustomerServiceDto>> findServiceDetailByFilter(@NonNull Integer customerId, Boolean hideExpired, Integer serviceType, Integer current, Integer size) {

        JPAQuery<Tuple> query = jpaQueryPrimary.select(qCourseService, qRsCourseServiceOption.name)
                .from(qCourseService)
                .leftJoin(qRsCourseServiceOption)
                .on(qCourseService.seriesId.eq(qRsCourseServiceOption.serviceId), qCourseService.serviceType.eq(qRsCourseServiceOption.serviceType))
                .where(qCourseService.customerId.eq(customerId));

        if (ObjectUtil.isNotEmpty(serviceType)) {
            query.where(qCourseService.serviceType.eq(serviceType));
        }

        if (ObjectUtil.isNotEmpty(hideExpired) && hideExpired) {
            query.where(qCourseService.expireTime.goe(Instant.now()));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qCourseService.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        Map<Integer, String> courseMap = new HashMap<>();
        Map<Integer, String> commonMap = new HashMap<>();
        Map<Long, String> roomMap = new HashMap<>();
        List<CustomerServiceDto> resultList = new ArrayList<>();
        for (Tuple t : query.fetch()) {
            RsCourseService service = t.get(qCourseService);
            assert service != null;
            CustomerServiceDto dto = CustomerServiceDto.builder()
                    .service(service)
                    .alias(t.get(qRsCourseServiceOption.name))
                    .build();
            resultList.add(dto);
            if (ServiceTypeConstant.CST_Video.equals(service.getServiceType())) {
                courseMap.put(service.getSeriesId(), "");
            } else if (ServiceTypeConstant.CST_Common.equals(service.getServiceType())) {
                commonMap.put(service.getSeriesId(), "");
            } else if (ServiceTypeConstant.CST_PayRoom.equals(service.getServiceType())) {
                roomMap.put(Long.valueOf(service.getSeriesId()), "");
            }
        }

        List<RsCourseSeries> courseInfo = rsCourseSeriesRepository.findAllById(courseMap.keySet());
        courseMap = courseInfo.stream().collect(Collectors.toMap(RsCourseSeries::getId, RsCourseSeries::getName));

        List<RsCommonService> commonServiceInfo = rsCommonServiceRepository.findAllById(commonMap.keySet());
        commonMap = commonServiceInfo.stream().collect(Collectors.toMap(RsCommonService::getId, RsCommonService::getName));

        List<LiveRoomNameResp> roomInfo = contentClient.getLiveRoomName(roomMap.keySet(), null, null, null, null, null).getData();
        roomMap = roomInfo.stream().collect(Collectors.toMap(LiveRoomNameResp::getPkId, LiveRoomNameResp::getRoomName));

        Map<Integer, String> finalCourseMap = courseMap;
        Map<Integer, String> finalCommonMap = commonMap;
        Map<Long, String> finalRoomMap = roomMap;
        resultList.forEach(r -> {
            Integer st = r.getService().getServiceType();
            if (ServiceTypeConstant.CST_Video.equals(st)) {
                r.setServiceName(finalCourseMap.get(r.getService().getSeriesId()));
            } else if (ServiceTypeConstant.CST_Common.equals(st)) {
                r.setServiceName(finalCommonMap.get(r.getService().getSeriesId()));
            } else if (ServiceTypeConstant.CST_PayRoom.equals(st)) {
                r.setServiceName(finalRoomMap.get(Long.valueOf(r.getService().getSeriesId())));
            }
        });
        return PageResult.success(resultList, Pagination.of(current, size, total));
    }

    /**
     * 关闭服务
     *
     * @param id
     * @param operatorId
     * @param reason
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeService(@NonNull Integer id, @NonNull Integer operatorId, String reason) {
        RsCourseService service = rsCourseServiceRepository.findById(id).orElseThrow(RecordNotExistedException::new);
        service.setExpireTime(Instant.now());
        service.setUpdateTime(Instant.now());
        rsCourseServiceRepository.save(service);

        RsCourseServiceRecord record = RsCourseServiceRecord.builder()
                .seriesId(service.getSeriesId())
                .serviceType(service.getServiceType())
                .customerId(service.getCustomerId())
                .operatorId(operatorId)
                .operateType(ServiceOperateTypeEnum.SOT_Close.getValue())
                .remark(reason)
                .createTime(Instant.now())
                .build();
        rsCourseServiceRecordRepository.save(record);
    }

    public List<RsCourseServiceOption> getServiceOptionList(Integer serviceType, Collection<Integer> serviceIds) {
        if (ObjectUtils.isEmpty(serviceIds))
            return Collections.emptyList();

        List<Integer> distinctIds = serviceIds.stream().distinct().collect(Collectors.toList());
        return jpaQueryPrimary.selectFrom(qRsCourseServiceOption)
                .where(qRsCourseServiceOption.serviceType.eq(serviceType))
                .where(qRsCourseServiceOption.serviceId.in(distinctIds))
                .fetch();
    }

    @Transactional
    public void createServiceRecord(Integer seriesId, Integer serviceType, Integer customerId, Integer operatorId,
                                    Integer operateType, String remark) {
        RsCourseServiceRecord record = RsCourseServiceRecord.builder()
                .seriesId(seriesId)
                .serviceType(serviceType)
                .customerId(customerId)
                .operatorId(operatorId)
                .operateType(operateType)
                .remark(remark)
                .createTime(Instant.now())
                .build();
        rsCourseServiceRecordRepository.save(record);
    }

    public List<ServiceIntroduction> getServiceIntroductionList(Collection<ServiceKey> serviceKeyList) {
        if (ObjectUtils.isEmpty(serviceKeyList)) {
            return Collections.emptyList();
        }
        Map<Integer, List<ServiceKey>> groupMap = serviceKeyList.stream()
                .collect(Collectors.groupingBy(ServiceKey::getServiceType));

        List<ServiceIntroduction> serviceIntroductionList = new ArrayList<>(serviceKeyList.size());
        groupMap.forEach((serviceType, params) -> {
            List<Integer> serviceIds = params.stream()
                    .map(ServiceKey::getServiceId)
                    .map(Long::intValue)
                    .distinct()
                    .collect(Collectors.toList());
            if (Objects.equals(serviceType, CST_Common)) {
                List<ServiceIntroduction> commonServiceIntroduction = commonSeriesService.getCommonServiceIntroductionList(serviceIds);
                serviceIntroductionList.addAll(commonServiceIntroduction);
            } else if (Objects.equals(serviceType, CST_Video)) {
                List<ServiceIntroduction> courseServiceIntroduction = courseSeriesService.getCourseServiceIntroductionList(serviceIds);
                serviceIntroductionList.addAll(courseServiceIntroduction);
            } else if (Objects.equals(serviceType, CST_Article)) {
                List<ServiceIntroduction> articleServiceIntroduction = articleSeriesService.getArticleServiceIntroductionList(serviceIds);
                serviceIntroductionList.addAll(articleServiceIntroduction);
            } else if (Objects.equals(serviceType, CST_PayRoom)) {
                BaseResult<List<ServiceIntroduction>> result = contentClient.getLiveRoomServiceIntroductionList(BatchReq.create(serviceIds));
                if (result.isSuccess()) {
                    serviceIntroductionList.addAll(result.getData());
                }
            }
        });
        return serviceIntroductionList;
    }

    public List<RsCourseService> filterService(Integer seriesId, Integer serviceType, Set<Integer> customerIds, Boolean isExpired) {
        JPAQuery<RsCourseService> query = jpaQueryPrimary.select(qCourseService)
                .from(qCourseService)
                .where(qCourseService.serviceType.eq(serviceType).and(qCourseService.seriesId.eq(seriesId))
                        .and(qCourseService.customerId.in(customerIds)));
        if (ObjectUtil.isNotEmpty(isExpired)) {
            if (isExpired) {
                query = query.where(qCourseService.expireTime.lt(Instant.now()));
            }else {
                query = query.where(qCourseService.expireTime.gt(Instant.now()));
            }
        }
        return query.fetch();
    }
}

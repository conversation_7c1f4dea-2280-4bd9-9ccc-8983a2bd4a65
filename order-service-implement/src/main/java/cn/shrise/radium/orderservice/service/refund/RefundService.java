package cn.shrise.radium.orderservice.service.refund;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.LockUtils;
import cn.shrise.radium.common.util.RedisUtil;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.orderservice.constant.*;
import cn.shrise.radium.orderservice.dto.OrderExtInfoDto;
import cn.shrise.radium.orderservice.dto.RefundOrderDto;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import cn.shrise.radium.orderservice.repository.CourseRefundSignRepository;
import cn.shrise.radium.orderservice.repository.RefundBankInfoRepository;
import cn.shrise.radium.orderservice.repository.RefundBankRecordRepository;
import cn.shrise.radium.orderservice.repository.RefundRepository;
import cn.shrise.radium.orderservice.req.CreateBankReq;
import cn.shrise.radium.orderservice.req.RefundBankUpdateReq;
import cn.shrise.radium.orderservice.req.RefundCompletedReq;
import cn.shrise.radium.orderservice.resp.*;
import cn.shrise.radium.orderservice.service.order.OrderService;
import cn.shrise.radium.orderservice.service.pay.NotifyOrderProcess;
import cn.shrise.radium.orderservice.service.payment.MerchantService;
import cn.shrise.radium.orderservice.util.DateDetailUtils;
import cn.shrise.radium.orderservice.util.GenNumberUtils;
import cn.shrise.radium.orderservice.util.esign.GenSignNumber;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.payment.PaymentPlatformEnum.PP_Transfer;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefundService {

    private final JPAQueryFactory jpaQueryPrimary;
    private final UserClient userClient;
    private final RefundRepository refundRepository;
    private final CourseRefundSignRepository signRepository;
    private final RefundBankInfoRepository refundBankInfoRepository;
    private final RefundBankRecordRepository refundBankRecordRepository;
    private final QRsCourseRefundOrder qRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
    private final QRsCourseOrder courseOrder = QRsCourseOrder.rsCourseOrder;
    private final QRsSku rsSku = QRsSku.rsSku;
    private final QRsCourseOrderSign qRsCourseOrderSign = QRsCourseOrderSign.rsCourseOrderSign;
    private final QRsCourseRefundSign qRsCourseRefundSign = QRsCourseRefundSign.rsCourseRefundSign;
    private final QRsCourseRefundOrderSub qRsCourseRefundOrderSub = QRsCourseRefundOrderSub.rsCourseRefundOrderSub;
    private final QCourseSubOrder qRsCourseSubOrder = QCourseSubOrder.courseSubOrder;
    private final QRsMerchantInfo qRsMerchantInfo = QRsMerchantInfo.rsMerchantInfo;
    private final QRsRefundBankInfo qRsRefundBankInfo = QRsRefundBankInfo.rsRefundBankInfo;
    private final RefundFlowRecordService flowRecordService;
    private final RefundOrderSubService subService;
    private final JdbcTemplate jdbcTemplate;
    private final OrderService orderService;

    private final ContentClient contentClient;

    private static final Integer COMPANY = 45;
    private final GenSignNumber genSignNumber;
    private final RefundOrderSubService refundOrderSubService;
    private final NotifyOrderProcess notifyOrderProcessService;
    private final RedisUtil redisUtil;
    private final MerchantService merchantService;

    @PersistenceContext(unitName = "primaryPersistenceUnit")
    private EntityManager primaryEntityManager;
    private final CriteriaBuilderFactory primaryCriteriaBuilderFactory;
    private final QRsCourseOrder qRsCourseOrder = QRsCourseOrder.rsCourseOrder;
    private final QRsSku qRsSku = QRsSku.rsSku;
    private final QRsRefundBankRecord qRsRefundBankRecord= QRsRefundBankRecord.rsRefundBankRecord;

    public List<SalesRefundInfo> getSalesRefundInfoList(LocalDateTime startTime, LocalDateTime endTime, Integer orderLevel) {

        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        QRsCourseOrderExt rsCourseOrderExt = QRsCourseOrderExt.rsCourseOrderExt;
        BooleanBuilder builder = getBuilder(startTime, endTime, orderLevel, rsCourseRefundOrder, rsCourseOrder, rsSku);
        log.info("validRefund-startTime:" + System.currentTimeMillis());
        List<Tuple> fetch = jpaQueryPrimary.select(rsCourseOrder.salesId, rsCourseRefundOrder.refundAmount.sum().longValue(),
                        rsCourseRefundOrder.id.count(), rsCourseOrderExt.dzId)
                .from(rsCourseRefundOrder)
                .leftJoin(rsCourseOrder)
                .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                .leftJoin(rsCourseOrderExt)
                .on(rsCourseOrder.id.eq(rsCourseOrderExt.orderId))
                .leftJoin(rsSku)
                .on(rsCourseOrder.skuId.eq(rsSku.id))
                .where(builder)
                .groupBy(rsCourseOrder.salesId, rsCourseOrderExt.dzId)
                .fetch();
        List<SalesRefundInfo> salesRefundInfoList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundInfo salesRefundInfo = new SalesRefundInfo(
                        tuple.get(rsCourseOrder.salesId) == null ? 0 : tuple.get(rsCourseOrder.salesId),
                        tuple.get(rsCourseRefundOrder.id.count()),
                        tuple.get(rsCourseRefundOrder.refundAmount.sum().longValue()),
                        tuple.get(rsCourseOrderExt.dzId) == null ? 0L : tuple.get(rsCourseOrderExt.dzId));
                salesRefundInfoList.add(salesRefundInfo);
            }
        }
        log.info("validRefund-endTime:" + System.currentTimeMillis());
        return salesRefundInfoList;
    }

    public List<SalesRefundInfo> getSalesValidRefundInfoList(LocalDateTime startTime, LocalDateTime endTime, Integer orderLevel) {

        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        QRsCourseOrderExt rsCourseOrderExt = QRsCourseOrderExt.rsCourseOrderExt;
        BooleanBuilder builder = getBuilder(startTime, endTime, orderLevel, rsCourseRefundOrder, rsCourseOrder, rsSku);
        final NumberTemplate<Integer> template = Expressions.numberTemplate(
                Integer.class, "function('datediff', {0}, {1})", rsCourseRefundOrder.refundTime, rsCourseOrder.payTime);
        builder.and(template.loe(90));
        log.info("refund-startTime:" + System.currentTimeMillis());
        List<Tuple> fetch = jpaQueryPrimary.select(rsCourseOrder.salesId, rsCourseRefundOrder.refundAmount.sum().longValue(),
                        rsCourseRefundOrder.id.count(), rsCourseOrderExt.dzId)
                .from(rsCourseRefundOrder)
                .leftJoin(rsCourseOrder)
                .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                .leftJoin(rsCourseOrderExt)
                .on(rsCourseOrder.id.eq(rsCourseOrderExt.orderId))
                .leftJoin(rsSku)
                .on(rsCourseOrder.skuId.eq(rsSku.id))
                .where(builder)
                .groupBy(rsCourseOrder.salesId, rsCourseOrderExt.dzId)
                .fetch();
        List<SalesRefundInfo> salesRefundInfoList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundInfo salesRefundInfo = new SalesRefundInfo(
                        tuple.get(rsCourseOrder.salesId) == null ? 0 : tuple.get(rsCourseOrder.salesId),
                        tuple.get(rsCourseRefundOrder.id.count()),
                        tuple.get(rsCourseRefundOrder.refundAmount.sum().longValue()),
                        tuple.get(rsCourseOrderExt.dzId) == null ? 0L : tuple.get(rsCourseOrderExt.dzId));
                salesRefundInfoList.add(salesRefundInfo);
            }
        }
        log.info("refund-endTime:" + System.currentTimeMillis());
        return salesRefundInfoList;
    }

    public List<SalesRefundInfo> getSalesRefundInfoTotal(LocalDateTime startTime, LocalDateTime endTime, Integer orderLevel) {

        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        BooleanBuilder builder = getBuilder(startTime, endTime, orderLevel, rsCourseRefundOrder, rsCourseOrder, rsSku);
        List<Tuple> fetch = jpaQueryPrimary.select(rsCourseOrder.salesId, rsCourseRefundOrder.refundAmount.sum().longValue(), rsCourseRefundOrder.id.count())
                .from(rsCourseRefundOrder)
                .leftJoin(rsCourseOrder)
                .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                .leftJoin(rsSku)
                .on(rsCourseOrder.skuId.eq(rsSku.id))
                .where(builder)
                .groupBy(rsCourseOrder.salesId)
                .fetch();
        List<SalesRefundInfo> salesRefundInfoList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundInfo salesRefundInfo = new SalesRefundInfo(tuple.get(rsCourseOrder.salesId) == null ? 0 : tuple.get(rsCourseOrder.salesId), tuple.get(rsCourseRefundOrder.id.count()), tuple.get(rsCourseRefundOrder.refundAmount.sum().longValue()), null);
                salesRefundInfoList.add(salesRefundInfo);
            }
        }
        return salesRefundInfoList;
    }

    public List<SalesRefundInfo> getSalesValidRefundInfoTotal(LocalDateTime startTime, LocalDateTime endTime, Integer orderLevel) {

        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        BooleanBuilder builder = getBuilder(startTime, endTime, orderLevel, rsCourseRefundOrder, rsCourseOrder, rsSku);
        final NumberTemplate<Integer> template = Expressions.numberTemplate(
                Integer.class, "function('datediff', {0}, {1})", rsCourseRefundOrder.refundTime, rsCourseOrder.payTime);
        builder.and(template.loe(90));
        List<Tuple> fetch = jpaQueryPrimary.select(rsCourseOrder.salesId, rsCourseRefundOrder.refundAmount.sum().longValue(), rsCourseRefundOrder.id.count())
                .from(rsCourseRefundOrder)
                .leftJoin(rsCourseOrder)
                .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                .leftJoin(rsSku)
                .on(rsCourseOrder.skuId.eq(rsSku.id))
                .where(builder)
                .groupBy(rsCourseOrder.salesId)
                .fetch();
        List<SalesRefundInfo> salesRefundInfoList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundInfo salesRefundInfo = new SalesRefundInfo(tuple.get(rsCourseOrder.salesId) == null ? 0 : tuple.get(rsCourseOrder.salesId), tuple.get(rsCourseRefundOrder.id.count()), tuple.get(rsCourseRefundOrder.refundAmount.sum().longValue()), null);
                salesRefundInfoList.add(salesRefundInfo);
            }
        }
        return salesRefundInfoList;
    }

    private BooleanBuilder getBuilder(LocalDateTime startTime, LocalDateTime endTime, Integer orderLevel, QRsCourseRefundOrder rsCourseRefundOrder, QRsCourseOrder rsCourseOrder, QRsSku rsSku) {
        BooleanBuilder builder = new BooleanBuilder();

        if (startTime != null) {
            builder.and(rsCourseRefundOrder.refundTime.goe(startTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        if (endTime != null) {
            builder.and(rsCourseRefundOrder.refundTime.lt(endTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        builder.and(rsCourseRefundOrder.status.eq(OrderRefundStatusConstant.PASSED.getValue()))
                .and(rsCourseOrder.orderStatus.eq(OrderStatusConstant.PASSED.getValue()))
                .and(rsCourseOrder.companyType.eq(COMPANY));

        if (orderLevel != null) {
            builder.and(rsSku.category.eq(orderLevel));
        }
        return builder;
    }

    public Optional<RsCourseRefundOrder> getRefundOrder(Integer id) {
        return refundRepository.findById(id);
    }

    public List<RsCourseRefundOrder> getRefundList(Collection<Integer> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        final Set<Integer> idSet = ids.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        final QRsCourseRefundOrder refundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        BooleanExpression expression = refundOrder.orderId.in(idSet);
        Iterable<RsCourseRefundOrder> iterable = refundRepository.findAll(expression);
        List<RsCourseRefundOrder> refundList = new ArrayList<>();
        iterable.forEach(refundList::add);
        return refundList;
    }

    public List<RsCourseRefundOrder> getRefundListByRefundId(Collection<Integer> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        final Set<Integer> idSet = ids.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        final QRsCourseRefundOrder refundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        BooleanExpression expression = refundOrder.id.in(idSet);
        Iterable<RsCourseRefundOrder> iterable = refundRepository.findAll(expression);
        List<RsCourseRefundOrder> refundList = new ArrayList<>();
        iterable.forEach(refundList::add);
        return refundList;
    }

    public Map<Integer, RsCourseRefundOrder> getRefundMap(Collection<Integer> ids) {
        return getRefundList(ids).stream()
                .collect(Collectors.toMap(RsCourseRefundOrder::getOrderId, Function.identity()));
    }

    public List<RsCourseRefundOrder> getRefundList(Integer orderId, Integer status) {
        JPAQuery<RsCourseRefundOrder> query = jpaQueryPrimary.selectFrom(qRefundOrder)
                .where(qRefundOrder.orderId.eq(orderId))
                .where(qRefundOrder.enabled.eq(true));

        if (!ObjectUtils.isEmpty(status)) {
            query.where(qRefundOrder.status.eq(status));
        }
        return query.fetch();
    }

    public List<RsCourseRefundOrder> getRefundList(Integer cursor, Integer status, Integer limit) {
        JPAQuery<RsCourseRefundOrder> query = jpaQueryPrimary.selectFrom(qRefundOrder)
                .where(qRefundOrder.id.gt(cursor))
                .orderBy(qRefundOrder.id.asc())
                .limit(limit);

        if (!ObjectUtils.isEmpty(status)) {
            query.where(qRefundOrder.status.eq(status));
        }
        return query.fetch();
    }

    /**
     * 根据订单Id获取退款列表
     *
     * @param orderIds
     * @param status
     * @return
     */
    public List<RsCourseRefundOrder> getRefundList(Collection<Integer> orderIds, Integer status) {
        if (ObjectUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        Set<Integer> orderIdSet = new HashSet<>(orderIds);

        JPAQuery<RsCourseRefundOrder> query = jpaQueryPrimary.selectFrom(qRefundOrder)
                .where(qRefundOrder.orderId.in(orderIdSet))
                .where(qRefundOrder.enabled.eq(true));

        if (!ObjectUtils.isEmpty(status)) {
            query.where(qRefundOrder.status.eq(status));
        }
        return query.fetch();
    }

    public RsCourseRefundOrder addOne(@NonNull RsCourseRefundOrder order) {
        order.setCreateTime(Instant.now());
        order.setUpdateTime(Instant.now());
        return refundRepository.save(order);
    }

    @Transactional
    public RsCourseRefundOrder updateOne(@NonNull RsCourseRefundOrder order) {
        Optional<RsCourseRefundOrder> opInfo = refundRepository.findById(order.getId());
        if (opInfo.isPresent()) {
            RsCourseRefundOrder dbOrder = opInfo.get();
            order.setUpdateTime(Instant.now());
            BeanUtil.copyProperties(order, dbOrder, CopyOptions.create().setIgnoreNullValue(true));
            return refundRepository.save(dbOrder);
        } else {
            throw new BusinessException(OsErrorCode.NOT_FOUND);
        }
    }

    public RsCourseRefundOrder findOneByFilter(Integer orderId, Integer status, List<Integer> statusList) {
        JPAQuery<RsCourseRefundOrder> query = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder);
        if (ObjectUtil.isNotEmpty(orderId)) {
            query.where(qRefundOrder.orderId.eq(orderId));
        }

        if (ObjectUtil.isNotEmpty(status)) {
            query.where(qRefundOrder.status.eq(status));
        }

        if (ObjectUtil.isNotEmpty(statusList)) {
            query.where(qRefundOrder.status.in(statusList));
        }
        return query.fetchFirst();
    }

    /**
     * 添加退款单、退款详情、退款流程
     *
     * @param order
     * @return
     */
    @Transactional
    public RsCourseRefundOrder addRefundInfo(@NonNull RsCourseRefundOrder order, @NonNull List<OrderExtInfoDto> extInfos,
                                             @NonNull Integer userId, @NonNull Integer companyType) {
        order = addOne(order);
        // 存审核记录
        flowRecordService.addOne(RsRefundFlowRecord.builder()
                .refundId(order.getId())
                .operatorId(userId)
                .operateType(RefundFlowOperateTypeEnum.RFOT_Create.getValue())
                .createTime(Instant.now()).build());
        // 存退款单
        @NonNull RsCourseRefundOrder finalOrder = order;
        List<RsCourseRefundOrderSub> infoList = extInfos.stream().map(i ->
                RsCourseRefundOrderSub.builder()
                        .companyType(companyType)
                        .refundOrderId(finalOrder.getId())
                        .mainOrderId(i.getMainOrderId())
                        .subOrderId(i.getSubOrderId())
//                        .payType(i.getPayType())
                        .refundNumber(GenNumberUtils.createRefundOrderNumber())
                        .refundFee(i.getPreRefundAmount())
                        .refundStatus(RefundOrderStatusEnum.REFUNDING.getValue())
                        .build()).collect(Collectors.toList());
        subService.addAll(infoList);
        return order;
    }

    public Page<SalesRefundDetailResp> getSalesRefundDetail(Integer salesId, LocalDate flagTime, Integer periodType,
                                                            Integer orderLevel, Long dzId, PageRequest pageRequest) {
        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        QRsCourseOrderExt rsCourseOrderExt = QRsCourseOrderExt.rsCourseOrderExt;
        List<LocalDateTime> date = DateDetailUtils.getLocalDateTimeList(DateUtils.strToDate(flagTime + " 00:00:00", null).toInstant(), periodType);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(rsCourseRefundOrder.refundTime.goe(date.get(0).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.refundTime.lt(date.get(1).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.status.eq(OrderRefundStatusConstant.PASSED.getValue()));
        if (salesId != null) {
            builder.and(rsCourseOrder.salesId.eq(salesId));
        }
        if (orderLevel != null) {
            builder.and(rsSku.category.eq(orderLevel));
        }
        JPAQuery<Tuple> query;
        if (dzId == -1L) {
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder).on(rsCourseOrder.id.eq(rsCourseRefundOrder.orderId))
                    .leftJoin(rsSku).on(rsSku.id.eq(rsCourseOrder.skuId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        } else {
            builder.and(rsCourseOrderExt.dzId.eq(dzId));
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder).on(rsCourseOrder.id.eq(rsCourseRefundOrder.orderId))
                    .leftJoin(rsCourseOrderExt)
                    .on(rsCourseOrder.id.eq(rsCourseOrderExt.orderId))
                    .leftJoin(rsSku).on(rsSku.id.eq(rsCourseOrder.skuId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        }
        long total = query.fetchCount();
        List<Tuple> fetch = query.offset(pageRequest.getOffset()).limit(pageRequest.getPageSize()).fetch();
        List<SalesRefundDetailResp> salesRefundDetailList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundDetailResp salesRefundDetail = new SalesRefundDetailResp(
                        tuple.get(rsCourseRefundOrder.refundNumber),
                        tuple.get(rsCourseRefundOrder.createTime),
                        tuple.get(rsCourseRefundOrder.refundTime),
                        tuple.get(rsCourseRefundOrder.refundAmount),
                        tuple.get(rsCourseRefundOrder.status)
                );
                salesRefundDetailList.add(salesRefundDetail);
            }
        }
        return new PageImpl<>(salesRefundDetailList, pageRequest, total);
    }

    public Page<SalesRefundDetailResp> getSalesValidRefundDetail(Integer salesId, LocalDate flagTime, Integer periodType, Integer orderLevel, Long dzId, PageRequest pageRequest) {
        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        QRsCourseOrderExt rsCourseOrderExt = QRsCourseOrderExt.rsCourseOrderExt;
        List<LocalDateTime> date = DateDetailUtils.getLocalDateTimeList(DateUtils.strToDate(flagTime + " 00:00:00", null).toInstant(), periodType);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(rsCourseRefundOrder.refundTime.goe(date.get(0).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.refundTime.lt(date.get(1).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.status.eq(OrderRefundStatusConstant.PASSED.getValue()));
        final NumberTemplate<Integer> template = Expressions.numberTemplate(
                Integer.class, "function('datediff', {0}, {1})", rsCourseRefundOrder.refundTime, rsCourseOrder.payTime);
        builder.and(template.loe(90));
        if (salesId != null) {
            builder.and(rsCourseOrder.salesId.eq(salesId));
        }
        if (orderLevel != null) {
            builder.and(rsSku.category.eq(orderLevel));
        }
        JPAQuery<Tuple> query;
        if (dzId == -1) {
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder).on(rsCourseOrder.id.eq(rsCourseRefundOrder.orderId))
                    .leftJoin(rsSku).on(rsSku.id.eq(rsCourseOrder.skuId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        } else {
            builder.and(rsCourseOrderExt.dzId.eq(dzId));
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder).on(rsCourseOrder.id.eq(rsCourseRefundOrder.orderId))
                    .leftJoin(rsSku).on(rsSku.id.eq(rsCourseOrder.skuId))
                    .leftJoin(rsCourseOrderExt)
                    .on(rsCourseOrder.id.eq(rsCourseOrderExt.orderId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        }
        long total = query.fetchCount();
        List<Tuple> fetch = query.offset(pageRequest.getOffset()).limit(pageRequest.getPageSize()).fetch();
        List<SalesRefundDetailResp> salesRefundDetailList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundDetailResp salesRefundDetail = new SalesRefundDetailResp(
                        tuple.get(rsCourseRefundOrder.refundNumber),
                        tuple.get(rsCourseRefundOrder.createTime),
                        tuple.get(rsCourseRefundOrder.refundTime),
                        tuple.get(rsCourseRefundOrder.refundAmount),
                        tuple.get(rsCourseRefundOrder.status)
                );
                salesRefundDetailList.add(salesRefundDetail);
            }
        }
        return new PageImpl<>(salesRefundDetailList, pageRequest, total);
    }

    public Page<SalesRefundDetailResp> getDeptRefundDetail(Integer deptId, LocalDate flagTime, Integer periodType, Integer orderLevel, Long dzId, PageRequest pageRequest) {
        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QCourseOrderDeptRelation courseOrderDeptRelation = QCourseOrderDeptRelation.courseOrderDeptRelation;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        QRsCourseOrderExt rsCourseOrderExt = QRsCourseOrderExt.rsCourseOrderExt;
        List<LocalDateTime> date = DateDetailUtils.getLocalDateTimeList(DateUtils.strToDate(flagTime + " 00:00:00", null).toInstant(), periodType);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(rsCourseRefundOrder.refundTime.goe(date.get(0).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.refundTime.lt(date.get(1).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.status.eq(OrderRefundStatusConstant.PASSED.getValue())).and(rsCourseOrder.companyType.eq(COMPANY));
        JPAQuery<Tuple> query;
        if (deptId != null) {
            builder.and(courseOrderDeptRelation.departmentId.eq(deptId));
        }
        if (orderLevel != null) {
            builder.and(rsSku.category.eq(orderLevel));
        }
        if (dzId == -1L) {
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder)
                    .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                    .leftJoin(rsSku)
                    .on(rsCourseOrder.skuId.eq(rsSku.id))
                    .leftJoin(courseOrderDeptRelation)
                    .on(rsCourseOrder.id.eq(courseOrderDeptRelation.orderId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        } else {
            builder.and(rsCourseOrderExt.dzId.eq(dzId));
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder)
                    .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                    .leftJoin(rsSku)
                    .on(rsCourseOrder.skuId.eq(rsSku.id))
                    .leftJoin(courseOrderDeptRelation)
                    .on(rsCourseOrder.id.eq(courseOrderDeptRelation.orderId))
                    .leftJoin(rsCourseOrderExt)
                    .on(rsCourseOrder.id.eq(rsCourseOrderExt.orderId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        }
        long total = query.fetchCount();
        List<Tuple> fetch = query.offset(pageRequest.getOffset()).limit(pageRequest.getPageSize()).fetch();
        List<SalesRefundDetailResp> salesRefundDetailList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundDetailResp salesRefundDetail = new SalesRefundDetailResp(
                        tuple.get(rsCourseRefundOrder.refundNumber),
                        tuple.get(rsCourseRefundOrder.createTime),
                        tuple.get(rsCourseRefundOrder.refundTime),
                        tuple.get(rsCourseRefundOrder.refundAmount),
                        tuple.get(rsCourseRefundOrder.status)
                );
                salesRefundDetailList.add(salesRefundDetail);
            }
        }
        return new PageImpl<>(salesRefundDetailList, pageRequest, total);
    }

    public Page<SalesRefundDetailResp> getDeptValidRefundDetail(Integer deptId, LocalDate flagTime, Integer periodType, Integer orderLevel, Long dzId, PageRequest pageRequest) {
        QRsCourseRefundOrder rsCourseRefundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        QCourseOrderDeptRelation courseOrderDeptRelation = QCourseOrderDeptRelation.courseOrderDeptRelation;
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsSku rsSku = QRsSku.rsSku;
        QRsCourseOrderExt rsCourseOrderExt = QRsCourseOrderExt.rsCourseOrderExt;
        List<LocalDateTime> date = DateDetailUtils.getLocalDateTimeList(DateUtils.strToDate(flagTime + " 00:00:00", null).toInstant(), periodType);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(rsCourseRefundOrder.refundTime.goe(date.get(0).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.refundTime.lt(date.get(1).atZone(ZoneId.systemDefault()).toInstant()));
        builder.and(rsCourseRefundOrder.status.eq(OrderRefundStatusConstant.PASSED.getValue())).and(rsCourseOrder.companyType.eq(COMPANY));
        final NumberTemplate<Integer> template = Expressions.numberTemplate(
                Integer.class, "function('datediff', {0}, {1})", rsCourseRefundOrder.refundTime, rsCourseOrder.payTime);
        builder.and(template.loe(90));
        JPAQuery<Tuple> query;
        if (deptId != null) {
            builder.and(courseOrderDeptRelation.departmentId.eq(deptId));
        }
        if (orderLevel != null) {
            builder.and(rsSku.category.eq(orderLevel));
        }
        if (dzId == -1L) {
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder)
                    .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                    .leftJoin(rsSku)
                    .on(rsCourseOrder.skuId.eq(rsSku.id))
                    .leftJoin(courseOrderDeptRelation)
                    .on(rsCourseOrder.id.eq(courseOrderDeptRelation.orderId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        } else {
            builder.and(rsCourseOrderExt.dzId.eq(dzId));
            query = jpaQueryPrimary.select(rsCourseRefundOrder.refundNumber, rsCourseRefundOrder.createTime, rsCourseRefundOrder.refundAmount, rsCourseRefundOrder.status)
                    .from(rsCourseRefundOrder)
                    .leftJoin(rsCourseOrder)
                    .on(rsCourseRefundOrder.orderId.eq(rsCourseOrder.id))
                    .leftJoin(rsSku)
                    .on(rsCourseOrder.skuId.eq(rsSku.id))
                    .leftJoin(courseOrderDeptRelation)
                    .on(rsCourseOrder.id.eq(courseOrderDeptRelation.orderId))
                    .leftJoin(rsCourseOrderExt)
                    .on(rsCourseOrder.id.eq(rsCourseOrderExt.orderId))
                    .where(builder)
                    .orderBy(rsCourseRefundOrder.refundTime.desc());
        }
        long total = query.fetchCount();
        List<Tuple> fetch = query.offset(pageRequest.getOffset()).limit(pageRequest.getPageSize()).fetch();
        List<SalesRefundDetailResp> salesRefundDetailList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            for (Tuple tuple : fetch) {
                SalesRefundDetailResp salesRefundDetail = new SalesRefundDetailResp(
                        tuple.get(rsCourseRefundOrder.refundNumber),
                        tuple.get(rsCourseRefundOrder.createTime),
                        tuple.get(rsCourseRefundOrder.refundTime),
                        tuple.get(rsCourseRefundOrder.refundAmount),
                        tuple.get(rsCourseRefundOrder.status)
                );
                salesRefundDetailList.add(salesRefundDetail);
            }
        }
        return new PageImpl<>(salesRefundDetailList, pageRequest, total);
    }

    public UserStatisticsCount getRefundOrderCount(Integer userId, Integer refundStatus, Boolean isPromotion) {
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsCourseRefundOrder refundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        BooleanBuilder predicate = new BooleanBuilder();
        predicate.and(rsCourseOrder.userId.eq(userId));

        if (!ObjectUtils.isEmpty(refundStatus)) {
            predicate.and(refundOrder.status.eq(refundStatus));
        }

        if (!ObjectUtils.isEmpty(isPromotion)) {
            predicate.and(rsSku.isPromotion.eq(isPromotion));
        }

        final UserStatisticsCount statisticsCount = jpaQueryPrimary.select(Projections.bean(
                        UserStatisticsCount.class,
                        refundOrder.id.count().as("count"),
                        rsCourseOrder.userId.as("userId")))
                .from(refundOrder)
                .leftJoin(rsCourseOrder)
                .on(rsCourseOrder.id.eq(refundOrder.orderId))
                .leftJoin(rsSku)
                .on(rsCourseOrder.skuId.eq(rsSku.id))
                .where(predicate)
                .fetchOne();
        // 补齐
        if (statisticsCount != null && ObjectUtils.isEmpty(statisticsCount.getUserId())) {
            statisticsCount.setUserId(userId);
        }
        return statisticsCount;
    }

    public UserStatisticsCount getCompanyRefundOrderCount(Integer companyType, Integer userId, Integer refundStatus, Boolean isPromotion) {
        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsCourseRefundOrder refundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        BooleanBuilder predicate = new BooleanBuilder();
        predicate.and(rsCourseOrder.userId.eq(userId)).and(rsCourseOrder.companyType.eq(companyType));

        if (!ObjectUtils.isEmpty(refundStatus)) {
            predicate.and(refundOrder.status.eq(refundStatus));
        }

        if (!ObjectUtils.isEmpty(isPromotion)) {
            predicate.and(rsSku.isPromotion.eq(isPromotion));
        }

        final UserStatisticsCount statisticsCount = jpaQueryPrimary.select(Projections.bean(
                        UserStatisticsCount.class,
                        refundOrder.id.count().as("count"),
                        rsCourseOrder.userId.as("userId")))
                .from(refundOrder)
                .leftJoin(rsCourseOrder)
                .on(rsCourseOrder.id.eq(refundOrder.orderId))
                .leftJoin(rsSku)
                .on(rsCourseOrder.skuId.eq(rsSku.id))
                .where(predicate)
                .fetchOne();
        // 补齐
        if (statisticsCount != null && ObjectUtils.isEmpty(statisticsCount.getUserId())) {
            statisticsCount.setUserId(userId);
        }
        return statisticsCount;
    }

    public List<UserStatisticsCount> getRefundOrderCount(Collection<Integer> userIds, Integer refundStatus, Boolean isPromotion) {

        final Set<Integer> userIdSet = userIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (ObjectUtils.isEmpty(userIdSet)) {
            return Collections.emptyList();
        }

        QRsCourseOrder rsCourseOrder = QRsCourseOrder.rsCourseOrder;
        QRsCourseRefundOrder refundOrder = QRsCourseRefundOrder.rsCourseRefundOrder;
        BooleanBuilder predicate = new BooleanBuilder();
        predicate.and(rsCourseOrder.userId.in(userIdSet));

        if (!ObjectUtils.isEmpty(refundStatus)) {
            predicate.and(refundOrder.status.eq(refundStatus));
        }

        if (!ObjectUtils.isEmpty(isPromotion)) {
            predicate.and(rsSku.isPromotion.eq(isPromotion));
        }

        final List<UserStatisticsCount> countList = jpaQueryPrimary.select(Projections.bean(
                        UserStatisticsCount.class,
                        refundOrder.id.count().as("count"),
                        rsCourseOrder.userId.as("userId")))
                .from(refundOrder)
                .leftJoin(rsCourseOrder)
                .on(rsCourseOrder.id.eq(refundOrder.orderId))
                .leftJoin(rsSku)
                .on(rsCourseOrder.skuId.eq(rsSku.id))
                .where(predicate)
                .groupBy(rsCourseOrder.userId)
                .fetch();

        // 补齐
        final Set<Integer> existSet = new HashSet<>();
        final List<UserStatisticsCount> data = new ArrayList<>();
        countList.forEach(e -> {
            if (!ObjectUtils.isEmpty(e.getUserId())) {
                existSet.add(e.getUserId());
                data.add(e);
            }
        });

        final Sets.SetView<Integer> diffSet = Sets.difference(userIdSet, existSet);
        final List<UserStatisticsCount> collect = diffSet.stream().map(e -> new UserStatisticsCount(e, 0L)).collect(Collectors.toList());
        List<UserStatisticsCount> result = new ArrayList<>(countList.size() + diffSet.size());
        result.addAll(data);
        result.addAll(collect);
        return result;
    }

    /*查询归属于该销售或商务节点下的所有退款订单*/

    /**
     * @param salesIds
     * @param creatorIds
     * @param companyType
     * @param refundStatus
     * @param refundNumber
     * @param orderNumber
     * @param createStartTime
     * @param createEndTime
     * @param refundStartTime
     * @param refundEndTime
     * @param productLevel
     * @param refundStatusSet
     * @param auditorId
     * @param readStatus      确认书阅读状态
     * @param refundStatusSet 退款合同签字状态
     * @param pageRequest
     * @return
     */
    public Page<SalesRelateRefundResp> getSalesRelateRefundList(
            List<Integer> salesIds,
            List<Integer> creatorIds,
            Integer companyType,
            Integer refundStatus,
            String refundNumber,
            String orderNumber,
            LocalDate createStartTime,
            LocalDate createEndTime,
            LocalDate refundStartTime,
            LocalDate refundEndTime,
            Integer productLevel,
            String refundStatusSet,
            Integer auditorId,
            Integer readStatus,
            RefundSignStatusEnum refundSignStatus,
            PageRequest pageRequest
    ) {

        JPAQuery<Tuple> query = jpaQueryPrimary.select(qRefundOrder.refundNumber, qRefundOrder.createTime, qRefundOrder.refundAmount,
                        qRefundOrder.status, courseOrder.orderNumber, courseOrder.userId, courseOrder.createTime, courseOrder.payTime, rsSku.name, qRefundOrder.refundTime, qRefundOrder.id,
                        qRefundOrder.auditorId, qRefundOrder.confirmId, qRefundOrder.financeId, courseOrder.id, qRefundOrder.creatorId)
                .from(qRefundOrder)
                .join(courseOrder)
                .on(qRefundOrder.orderId.eq(courseOrder.id))
                .join(rsSku)
                .on(courseOrder.skuId.eq(rsSku.id))
                .where(courseOrder.companyType.eq(companyType));
        if (!ObjectUtils.isEmpty(salesIds)) {
            query.where(courseOrder.salesId.in(salesIds));
        }
        if (!ObjectUtils.isEmpty(creatorIds)) {
            query.where(qRefundOrder.creatorId.in(creatorIds));
        }
        if (refundStatus != null) {
            query.where(qRefundOrder.status.eq(refundStatus));
        } else {
            List<Integer> refundStatusList = new ArrayList<>();
            refundStatusList.add(OrderRefundStatusConstant.REFUND.getValue());
            refundStatusList.add(OrderRefundStatusConstant.PASSED.getValue());
            refundStatusList.add(OrderRefundStatusConstant.FAILED.getValue());
            refundStatusList.add(OrderRefundStatusConstant.CLOSED.getValue());
            refundStatusList.add(OrderRefundStatusConstant.LOADING.getValue());
            if (Objects.equals(refundStatusSet, "refundOrderAudit")) {
                refundStatusList.add(OrderRefundStatusConstant.AUDITING.getValue());
                refundStatusList.add(OrderRefundStatusConstant.CONFIRMING.getValue());
            } else if (Objects.equals(refundStatusSet, "refundOrderConfirm")) {
                refundStatusList.add(OrderRefundStatusConstant.CONFIRMING.getValue());
            } else if (Objects.equals(refundStatusSet, "financeRefundOrder")) {

            } else {
                refundStatusList.add(OrderRefundStatusConstant.ASSIGNING.getValue());
                refundStatusList.add(OrderRefundStatusConstant.AUDITING.getValue());
                refundStatusList.add(OrderRefundStatusConstant.CONFIRMING.getValue());
            }
            query.where(qRefundOrder.status.in(refundStatusList));
        }
        if (!ObjectUtils.isEmpty(refundNumber)) {
            query.where(qRefundOrder.refundNumber.eq(refundNumber));
        }
        if (!ObjectUtils.isEmpty(orderNumber)) {
            query.where(courseOrder.orderNumber.eq(orderNumber));
        }
        if (productLevel != null) {
            query.where(rsSku.productLevel.eq(productLevel));
        }

        if (!ObjectUtils.isEmpty(refundSignStatus)) {
            query.leftJoin(qRsCourseRefundSign)
                    .on(qRsCourseRefundSign.refundId.eq(qRefundOrder.id)
                            .and(qRsCourseRefundSign.auditStatus.ne(SignAuditStatusEnum.AUDIT_DELETE.getValue())));

            if (Objects.equals(refundSignStatus, RefundSignStatusEnum.NOT_START)) {
                query.where(qRsCourseRefundSign.isSigned.isNull());
            } else if (Objects.equals(refundSignStatus, RefundSignStatusEnum.NOT_SIGNED)) {
                query.where(qRsCourseRefundSign.isSigned.eq(false));
            } else if (Objects.equals(refundSignStatus, RefundSignStatusEnum.SIGNED)) {
                query.where(qRsCourseRefundSign.isSigned.eq(true));
            }
        }

        if (readStatus != null) {
            query.where(qRefundOrder.readStatus.eq(readStatus));
        }
        if (createStartTime != null) {
            query.where(qRefundOrder.createTime.goe(createStartTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (createEndTime != null) {
            query.where(qRefundOrder.createTime.lt(createEndTime.atStartOfDay().plusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (refundStartTime != null) {
            query.where(qRefundOrder.refundTime.goe(refundStartTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (refundEndTime != null) {
            query.where(qRefundOrder.refundTime.lt(refundEndTime.atStartOfDay().plusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (auditorId != null) {
            query.where(qRefundOrder.auditorId.eq(auditorId));
        }
        long total = query.fetchCount();
        List<Tuple> fetch = query.orderBy(qRefundOrder.createTime.desc())
                .offset(pageRequest.getOffset())
                .limit(pageRequest.getPageSize())
                .fetch();
        List<SalesRelateRefundResp> salesRelateRefundList = new ArrayList<>();
        if (null != fetch && !fetch.isEmpty()) {
            fetch.forEach(e -> {
                SalesRelateRefundResp salesRelateRefundResp = new SalesRelateRefundResp(
                        e.get(qRefundOrder.refundNumber),
                        e.get(courseOrder.orderNumber),
                        e.get(qRefundOrder.createTime),
                        e.get(courseOrder.createTime),
                        e.get(courseOrder.payTime),
                        e.get(rsSku.name),
                        e.get(courseOrder.userId),
                        null,
                        e.get(qRefundOrder.refundAmount),
                        e.get(qRefundOrder.status),
                        null,
                        e.get(qRefundOrder.refundTime),
                        e.get(qRefundOrder.id),
                        null,
                        null,
                        null,
                        e.get(courseOrder.id),
                        e.get(qRefundOrder.creatorId),
                        null
                );
                if (Objects.equals(e.get(qRefundOrder.status), OrderRefundStatusConstant.AUDITING.getValue())) {
                    salesRelateRefundResp.setHandler(e.get(qRefundOrder.auditorId));
                } else if (Objects.equals(e.get(qRefundOrder.status), OrderRefundStatusConstant.CONFIRMING.getValue())) {
                    salesRelateRefundResp.setHandler(e.get(qRefundOrder.confirmId));
                } else if (Objects.equals(e.get(qRefundOrder.status), OrderRefundStatusConstant.REFUND.getValue())) {
                    salesRelateRefundResp.setHandler(e.get(qRefundOrder.financeId));
                }
                salesRelateRefundList.add(salesRelateRefundResp);
            });
        }
        return new PageImpl<>(salesRelateRefundList, pageRequest, total);
    }

    /*关闭退款订单*/
    public RsRefundFlowRecord closeRefundOrder(Integer userId, Integer refundId) {
        Optional<RsCourseRefundOrder> refundOrder = refundRepository.findById(refundId);
        RsCourseRefundOrder rsCourseRefundOrder = refundOrder.get();
        if (Arrays.asList(OrderRefundStatusConstant.CLOSED, OrderRefundStatusConstant.PASSED,
                OrderRefundStatusConstant.LOADING).contains(rsCourseRefundOrder.getStatus())) {
            throw new BusinessException("当前退款单状态下,不可关闭退款单");
        }
        if (OrderServiceConst.CLOSE_REFUND_STATUS.contains(rsCourseRefundOrder.getStatus())) {
            throw new BusinessException("退款状态已更新,请刷新重试");
        }
        rsCourseRefundOrder.setUpdateTime(Instant.now());
        rsCourseRefundOrder.setStatus(OrderRefundStatusConstant.CLOSED.getValue());
        rsCourseRefundOrder.setCloseId(userId);
        rsCourseRefundOrder.setCloseTime(Instant.now());
        refundRepository.save(rsCourseRefundOrder);
        List<RefundOrderDto> refundingList = subService.getRefundInfo(refundId, Lists.newArrayList(RefundOrderStatusEnum.REFUNDING, RefundOrderStatusEnum.IN_PROCESS));
        if (ObjectUtil.isNotEmpty(refundingList)) {
            for (RefundOrderDto dto : refundingList) {
                RsCourseRefundOrderSub subRefundOrder = dto.getSubRefundOrder();
                if (ObjectUtil.isNotNull(subRefundOrder)) {
                    subRefundOrder.setRefundStatus(RefundOrderStatusEnum.CLOSED.getValue());
                    subService.saveOne(subRefundOrder);
                }
            }
        }
        return flowRecordService.addOne(RsRefundFlowRecord.builder()
                .refundId(refundId)
                .operatorId(userId)
                .operateType(RefundFlowOperateTypeEnum.RFOT_Close.getValue())
                .createTime(Instant.now()).build());
    }

    public OrderDetailResp getOrderDetailRespByRefundId(Integer refundId, Integer orderId) {
        if (ObjectUtil.isAllEmpty(refundId, orderId)) {
            throw new BusinessException(OsErrorCode.PARAM_INVALID);
        }
        Tuple tuple = null;
        if (refundId != null) {
            tuple = jpaQueryPrimary.select(
                            courseOrder.id,
                            courseOrder.orderNumber,
                            rsSku.name,
                            courseOrder.skuPrice,
                            courseOrder.userId,
                            courseOrder.payTime,
                            courseOrder.payType,
                            courseOrder.salesId,
                            courseOrder.orderStatus,
                            courseOrder.amount,
                            courseOrder.isAvoidSign,
                            courseOrder.createTime,
                            rsSku.isOnline,
                            rsSku.productLevel
                    )
                    .from(qRefundOrder)
                    .leftJoin(courseOrder).on(courseOrder.id.eq(qRefundOrder.orderId))
                    .leftJoin(rsSku).on(rsSku.id.eq(courseOrder.skuId))
                    .where(qRefundOrder.id.eq(refundId))
                    .fetchFirst();
        } else {
            tuple = jpaQueryPrimary.select(
                            courseOrder.id,
                            courseOrder.orderNumber,
                            rsSku.name,
                            courseOrder.skuPrice,
                            courseOrder.userId,
                            courseOrder.payTime,
                            courseOrder.payType,
                            courseOrder.salesId,
                            courseOrder.orderStatus,
                            courseOrder.amount,
                            courseOrder.isAvoidSign,
                            courseOrder.createTime,
                            rsSku.isOnline,
                            rsSku.productLevel
                    )
                    .from(courseOrder)
                    .leftJoin(rsSku).on(rsSku.id.eq(courseOrder.skuId))
                    .where(courseOrder.id.eq(orderId))
                    .fetchFirst();
        }
        if (ObjectUtil.isEmpty(tuple)) {
            return null;
        }
        OrderDetailResp orderDetailResp = new OrderDetailResp(
                tuple.get(courseOrder.id),
                tuple.get(courseOrder.orderNumber),
                tuple.get(rsSku.name),
                NumberUtil.div(Long.valueOf(tuple.get(courseOrder.skuPrice)), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue(),
                tuple.get(courseOrder.userId) == null ? null : tuple.get(courseOrder.userId),
                tuple.get(courseOrder.payTime),
                tuple.get(courseOrder.payType),
                tuple.get(courseOrder.salesId) == null ? null : tuple.get(courseOrder.salesId),
                tuple.get(courseOrder.orderStatus),
                NumberUtil.div(Long.valueOf(tuple.get(courseOrder.amount)), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue(),
                null,
                tuple.get(rsSku.productLevel) == null ? null : tuple.get(rsSku.productLevel),
                tuple.get(rsSku.isOnline),
                tuple.get(courseOrder.isAvoidSign),
                tuple.get(courseOrder.createTime)
        );
        // 签字审核
        RsCourseOrderSign orderSignInfo = jpaQueryPrimary.select(qRsCourseOrderSign).from(qRsCourseOrderSign)
                .where(qRsCourseOrderSign.orderID.eq(tuple.get(courseOrder.id)))
                .where(qRsCourseOrderSign.auditStatus.ne(SignAuditStatusEnum.AUDIT_DELETE.getValue()))
                .fetchFirst();
        if (ObjectUtil.isNotEmpty(orderSignInfo)) {
            orderDetailResp.setOrderSignInfo(orderSignInfo);
        }
        return orderDetailResp;
    }

    public Integer getRefundAmountByOrderId(Integer orderId) {
        return jpaQueryPrimary.select(qRefundOrder.refundAmount.sum())
                .from(qRefundOrder)
                .where(qRefundOrder.orderId.eq(orderId).and(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Passed.getValue()))).fetchOne();
    }

    public RefundOrderResp getRefundOrderRespByRefundId(Integer refundId) {
        Tuple tuple = jpaQueryPrimary.select(
                        qRefundOrder.id,
                        qRefundOrder.refundNumber,
                        qRefundOrder.remark,
                        qRefundOrder.status,
                        qRefundOrder.imageList,
                        qRefundOrder.createTime,
                        qRefundOrder.readStatus,
                        qRsCourseRefundSign.isSigned,
                        qRefundOrder.creatorId,
                        qRefundOrder.refundAmount,
                        qRsCourseOrderSign.isSigned,
                        qRsCourseOrderSign.auditStatus,
                        courseOrder.orderNumber,
                        qRsCourseRefundSign.createTime,
                        qRsCourseRefundSign.auditStatus,
                        qRsCourseRefundSign.id,
                        qRsCourseRefundSign.signId,
                        qRefundOrder.isCloseService
                ).from(qRefundOrder)
                .leftJoin(qRsCourseRefundSign).on(qRsCourseRefundSign.refundId.eq(qRefundOrder.id))
                .leftJoin(qRsCourseOrderSign).on(qRsCourseOrderSign.orderID.eq(qRefundOrder.orderId))
                .leftJoin(courseOrder).on(courseOrder.id.eq(qRefundOrder.orderId))
                .where(qRefundOrder.id.eq(refundId))
                .orderBy(qRsCourseRefundSign.createTime.desc())
                .fetchFirst();
        if (ObjectUtil.isEmpty(tuple)) {
            return null;
        }
        RefundOrderResp refundOrderResp = new RefundOrderResp(
                tuple.get(qRefundOrder.id),
                tuple.get(qRefundOrder.refundNumber),
                null,
                tuple.get(qRefundOrder.remark),
                tuple.get(qRefundOrder.status),
                null,
                tuple.get(qRefundOrder.imageList),
                tuple.get(qRefundOrder.createTime),
                tuple.get(qRefundOrder.readStatus),
                tuple.get(qRsCourseRefundSign.isSigned) == null ? null : tuple.get(qRsCourseRefundSign.isSigned),
                tuple.get(qRsCourseOrderSign.isSigned) == null ? null : tuple.get(qRsCourseOrderSign.isSigned),
                tuple.get(courseOrder.orderNumber),
                SignAuditStatusEnum.AUDIT_PASS.getValue().equals(tuple.get(qRsCourseOrderSign.auditStatus)) ? true : false,
                SignAuditStatusEnum.AUDIT_EXECUTING.getValue().equals(tuple.get(qRsCourseRefundSign.auditStatus)) ? true : false,
                tuple.get(qRsCourseRefundSign.id),
                tuple.get(qRsCourseRefundSign.signId),
                tuple.get(qRefundOrder.isCloseService)
        );
        String salesName = null;
        if (ObjectUtil.isNotEmpty(tuple.get(qRefundOrder.creatorId))) {
            salesName = userClient.getUser(tuple.get(qRefundOrder.creatorId)).getData().getRealName();
        }
        refundOrderResp.setSalesName(salesName);
        if (ObjectUtil.isNotEmpty(tuple.get(qRefundOrder.refundAmount))) {
            refundOrderResp.setRefundAmount(NumberUtil.div(Long.valueOf(tuple.get(qRefundOrder.refundAmount)), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue());
        }
        return refundOrderResp;
    }

    public List<RefundOrderResp> getRefundOrderRecord(Integer orderId, Integer refundStatus) {
        BooleanBuilder builder = new BooleanBuilder();
        if (ObjectUtil.isNotEmpty(refundStatus)) {
            builder.and(qRefundOrder.status.eq(refundStatus));
        }
        builder.and(qRefundOrder.orderId.eq(orderId));
        List<RsCourseRefundOrder> rsCourseRefundOrderList = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder)
                .where(builder).orderBy(qRefundOrder.createTime.desc()).fetch();
        List<RefundOrderResp> refundOrderRespList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(rsCourseRefundOrderList)) {
            rsCourseRefundOrderList.forEach(e -> {
                RefundOrderResp refundOrderResp = new RefundOrderResp();
                BeanUtil.copyProperties(e, refundOrderResp);
                if (ObjectUtil.isNotEmpty(e.getRefundAmount())) {
                    refundOrderResp.setRefundAmount(NumberUtil.div(Long.valueOf(e.getRefundAmount()), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue());
                } else {
                    refundOrderResp.setRefundAmount(null);
                }
                refundOrderRespList.add(refundOrderResp);
            });
        }
        return refundOrderRespList;
    }

    /**
     * @param refundId  退款单ID
     * @param auditorId 分配对象ID
     * @param assignId  分配人ID
     * @return
     */
    public OrderErrorCode distributeRefundOrder(Integer refundId, Integer auditorId, Integer assignId) {
        RsCourseRefundOrder rsCourseRefundOrder = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder).where(qRefundOrder.id.eq(refundId).and(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Assigning.getValue()))).fetchOne();
        if (ObjectUtil.isEmpty(rsCourseRefundOrder)) {
            return OrderErrorCode.RECORD_NOT_FOUND;
        }
        // 状态置为待审核
        rsCourseRefundOrder.setStatus(RefundOrderAuditStatusEnum.ORS_Auditing.getValue());
        rsCourseRefundOrder.setAssignId(assignId);
        rsCourseRefundOrder.setAssignTime(Instant.now());
        rsCourseRefundOrder.setAuditorId(auditorId);
        rsCourseRefundOrder.setUpdateTime(Instant.now());
        RsCourseRefundOrder refundOrder = refundRepository.save(rsCourseRefundOrder);
        // 存审核记录
        flowRecordService.addOne(RsRefundFlowRecord.builder()
                .refundId(refundOrder.getId())
                .operatorId(assignId)
                .operateType(RefundFlowOperateTypeEnum.RFOT_Assign.getValue())
                .createTime(Instant.now()).build());
        return OrderErrorCode.SUCCESS;
    }

    /**
     * @param newAuditorId 新的分配对象ID
     * @param oldAuditorId 老的分配对象ID
     * @param assignId     分配人ID
     * @return
     */
    public OrderErrorCode distributeRefundOrderAgain(Integer newAuditorId, Integer oldAuditorId, Integer assignId) {
        List<RsCourseRefundOrder> rsCourseRefundOrder = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder).where(qRefundOrder.auditorId.eq(oldAuditorId).and(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Auditing.getValue()))).fetch();
        if (ObjectUtil.isEmpty(rsCourseRefundOrder)) {
            return OrderErrorCode.RECORD_NOT_FOUND;
        }
        List<RsCourseRefundOrder> rsCourseRefundOrders = new ArrayList<>();
        rsCourseRefundOrder.forEach(e -> {
            e.setAssignId(assignId);
            e.setAssignTime(Instant.now());
            e.setAuditorId(newAuditorId);
            e.setUpdateTime(Instant.now());
            rsCourseRefundOrders.add(e);
        });
        final String rsCourseRefundOrdersSql = SqlUtil.onDuplicateKeyUpdateSql(rsCourseRefundOrders);
        jdbcTemplate.execute(rsCourseRefundOrdersSql);
        List<RsRefundFlowRecord> rsRefundFlowRecordList = new ArrayList<>();
        rsCourseRefundOrders.forEach(e -> {
            RsRefundFlowRecord rsRefundFlowRecord = RsRefundFlowRecord.builder()
                    .refundId(e.getId())
                    .operatorId(assignId)
                    .operateType(RefundFlowOperateTypeEnum.RFOT_Assign.getValue())
                    .createTime(Instant.now()).build();
            rsRefundFlowRecordList.add(rsRefundFlowRecord);
        });
        // 存审核记录
        final String recordSql = SqlUtil.onDuplicateKeyUpdateSql(rsRefundFlowRecordList);
        jdbcTemplate.execute(recordSql);
        return OrderErrorCode.SUCCESS;
    }

    /**
     * @param refundId  退款单ID
     * @param auditorId 审核人ID
     * @return
     */
    @Transactional
    public OrderErrorCode auditRefundOrder(Integer refundId, Integer auditorId, Boolean isCloseService) {
        String lockKey = LockUtils.getRefundOrderFlowLockKey(refundId);
        if (!redisUtil.getLock(lockKey, 30L)) {
            log.warn("当前订单有操作正在进行，refundId:{}", refundId);
            throw new BusinessException("状态已更新，请刷新重试！");
        }
        try {
            checkRefundFlowStatus(refundId, RefundOrderAuditStatusEnum.ORS_Auditing);
            RsCourseRefundOrder rsCourseRefundOrder = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder).where(qRefundOrder.id.eq(refundId).and(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Auditing.getValue()))).fetchOne();
            if (ObjectUtil.isEmpty(rsCourseRefundOrder)) {
                return OrderErrorCode.RECORD_NOT_FOUND;
            }
            // 状态置为待退款
            jpaQueryPrimary.update(qRefundOrder)
                    .where(qRefundOrder.id.eq(refundId))
                    .set(qRefundOrder.status, RefundOrderAuditStatusEnum.ORS_Confirmed.getValue())
                    .set(qRefundOrder.auditTime, Instant.now())
                    .set(qRefundOrder.isCloseService, isCloseService)
                    .execute();
            // 存审核记录
            flowRecordService.addOne(RsRefundFlowRecord.builder()
                    .refundId(refundId)
                    .operatorId(auditorId)
                    .operateType(RefundFlowOperateTypeEnum.RFOT_Audit.getValue())
                    .createTime(Instant.now()).build());
            return OrderErrorCode.SUCCESS;
        } finally {
            redisUtil.releaseLock(lockKey);
        }
    }

    /**
     * @param refundId  退款单ID
     * @param confirmId 确认人ID
     * @return
     */
    @Transactional
    public OrderErrorCode confirmRefundOrder(Integer refundId, Integer confirmId) {
        String lockKey = LockUtils.getRefundOrderFlowLockKey(refundId);
        if (!redisUtil.getLock(lockKey, 30L)) {
            log.warn("当前订单有操作正在进行，refundId:{}", refundId);
            throw new BusinessException("状态已更新，请刷新重试！");
        }
        try {
            checkRefundFlowStatus(refundId, RefundOrderAuditStatusEnum.ORS_Confirming);
            RsCourseRefundOrder rsCourseRefundOrder = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder).where(qRefundOrder.id.eq(refundId).and(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Confirming.getValue()))).fetchOne();
            if (ObjectUtil.isEmpty(rsCourseRefundOrder)) {
                return OrderErrorCode.RECORD_NOT_FOUND;
            }
            // 状态置为待退款
            jpaQueryPrimary.update(qRefundOrder)
                    .where(qRefundOrder.id.eq(refundId))
                    .set(qRefundOrder.status, RefundOrderAuditStatusEnum.ORS_Confirmed.getValue())
                    .set(qRefundOrder.confirmTime, Instant.now())
                    .set(qRefundOrder.confirmId, confirmId)
                    .execute();
            // 存审核记录
            flowRecordService.addOne(RsRefundFlowRecord.builder()
                    .refundId(refundId)
                    .operatorId(confirmId)
                    .operateType(RefundFlowOperateTypeEnum.RFOT_Confirm.getValue())
                    .createTime(Instant.now()).build());
            return OrderErrorCode.SUCCESS;
        } finally {
            redisUtil.releaseLock(lockKey);
        }
    }


    /**
     * @param refundOrderId    退款单id
     * @param expectStatusEnum 当前退款单预期状态
     */
    public void checkRefundFlowStatus(Integer refundOrderId, RefundOrderAuditStatusEnum expectStatusEnum) {
        RsCourseRefundOrder refundOrder = getRefundOrder(refundOrderId).orElseThrow(RecordNotExistedException::new);
        if (!ObjectUtil.equals(refundOrder.getStatus(), expectStatusEnum.getValue())) {
            throw new BusinessException("状态已更新，请刷新重试！");
        }
    }

    @Transactional
    public OrderErrorCode startConfirmSignRefundOrder(Integer refundId) {
        RsCourseRefundOrder rsCourseRefundOrder = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder).where(qRefundOrder.id.eq(refundId).and(qRefundOrder.readStatus.eq(ReadStatusConstant.NOT_START.getValue()))).fetchOne();
        if (ObjectUtil.isEmpty(rsCourseRefundOrder)) {
            return OrderErrorCode.RECORD_NOT_FOUND;
        }
        // 签字确认状态置为已开始
        jpaQueryPrimary.update(qRefundOrder)
                .where(qRefundOrder.id.eq(refundId))
                .set(qRefundOrder.readStatus, ReadStatusConstant.START.getValue())
                .set(qRefundOrder.readInitTime, Instant.now())
                .execute();
        return OrderErrorCode.SUCCESS;
    }

    public List<RefundPlanResp> getRefundPlan(Integer orderId, Integer refundId, Integer refundStatus) {
        if (ObjectUtil.isAllEmpty(refundId, orderId)) {
            throw new BusinessException(OsErrorCode.PARAM_INVALID);
        }
        BooleanBuilder builder = new BooleanBuilder();
        if (refundId != null) {
            builder.and(qRsCourseRefundOrderSub.refundOrderId.eq(refundId));
        } else {
            builder.and(qRsCourseRefundOrderSub.mainOrderId.eq(orderId));
        }
        if (ObjectUtil.isNotEmpty(refundStatus)) {
            builder.and(qRsCourseRefundOrderSub.refundStatus.eq(refundStatus));
        }
        List<Tuple> fetch = jpaQueryPrimary.select(
                        courseOrder.orderNumber,
                        qRsCourseSubOrder.number,
                        courseOrder.createTime,
                        qRsCourseSubOrder.createTime,
                        courseOrder.payTime,
                        qRsCourseSubOrder.payTime,
                        courseOrder.amount,
                        qRsCourseSubOrder.amount,
                        qRsCourseRefundOrderSub.refundFee,
                        qRsCourseRefundOrderSub.refundStatus,
                        qRsCourseSubOrder.paymentAccount,
                        qRsCourseRefundOrderSub.payType,
                        qRsCourseRefundOrderSub.refundResult,
                        qRsCourseRefundOrderSub.refundNumber,
                        qRsCourseSubOrder.mchType,
                        qRsCourseSubOrder.merchantId,
                        qRsCourseRefundOrderSub.id,
                        qRsCourseRefundOrderSub.isForce,
                        qRsCourseRefundOrderSub.refundType,
                        qRsCourseRefundOrderSub.remark,
                        qRsCourseRefundOrderSub.forceId,
                        qRsCourseRefundOrderSub.gmtRefund,
                        qRsCourseRefundOrderSub.forceRefundDate
                )
                .from(qRsCourseRefundOrderSub)
                .leftJoin(courseOrder).on(courseOrder.id.eq(qRsCourseRefundOrderSub.mainOrderId))
                .leftJoin(qRsCourseSubOrder).on(qRsCourseSubOrder.id.eq(qRsCourseRefundOrderSub.subOrderId))
                .where(builder)
                .orderBy(qRsCourseSubOrder.createTime.desc())
                .fetch();
        if (ObjectUtil.isEmpty(fetch)) {
            return null;
        }
        if (Objects.equals(fetch.get(0).get(courseOrder.payType), PayTypeConstant.BANK.getValue())) {
            return null;
        }
        Set<Integer> userIds = new HashSet<>();
        List<Long> merchantIdList = new ArrayList<>();
        for (Tuple tuple : fetch) {
            if (tuple.get(qRsCourseRefundOrderSub.forceId) != null) {
                userIds.add(tuple.get(qRsCourseRefundOrderSub.forceId));
            }
            if (tuple.get(qRsCourseSubOrder.merchantId) != null) {
                merchantIdList.add(tuple.get(qRsCourseSubOrder.merchantId));
            }
        }
        List<RsMerchantInfo> merchantInfoList = merchantService.getMerchantInfoList(merchantIdList).orElse(new ArrayList<>());
        Map<Long, RsMerchantInfo> merchantInfoMap = merchantInfoList.stream().collect(Collectors.toMap(RsMerchantInfo::getId, Function.identity()));
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIds)).orElse(new HashMap<>());
        List<RefundPlanResp> refundPlanRespList = new ArrayList<>();
        fetch.forEach(e -> {
            RefundPlanResp refundPlanResp = RefundPlanResp.builder()
                    .refundOrderSubId(e.get(qRsCourseRefundOrderSub.id))
                    .orderNumber(e.get(qRsCourseSubOrder.number) == null ? e.get(courseOrder.orderNumber) : e.get(qRsCourseSubOrder.number))
                    .gmtCreate(e.get(qRsCourseSubOrder.createTime) == null ? e.get(courseOrder.createTime) : e.get(qRsCourseSubOrder.createTime))
                    .gmtPay(e.get(qRsCourseSubOrder.payTime) == null ? e.get(courseOrder.payTime) : e.get(qRsCourseSubOrder.payTime))
                    .amount(NumberUtil.div(Long.valueOf(e.get(qRsCourseSubOrder.amount) == null ? e.get(courseOrder.amount) : e.get(qRsCourseSubOrder.amount)), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue())
                    .refundAmount(NumberUtil.div(Long.valueOf(e.get(qRsCourseRefundOrderSub.refundFee)), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue())
                    .refundStatus(e.get(qRsCourseRefundOrderSub.refundStatus))
                    .refundNumber(e.get(qRsCourseRefundOrderSub.refundNumber))
                    .paymentAccount(e.get(qRsCourseSubOrder.paymentAccount))
                    .payType(e.get(qRsCourseRefundOrderSub.payType))
                    .platform(merchantInfoMap.getOrDefault(e.get(qRsCourseSubOrder.merchantId), new RsMerchantInfo()).getPlatform())
                    .channelType(merchantInfoMap.getOrDefault(e.get(qRsCourseSubOrder.merchantId), new RsMerchantInfo()).getChannelType())
                    .refundResult(e.get(qRsCourseRefundOrderSub.refundResult))
                    .isForce(e.get(qRsCourseRefundOrderSub.isForce))
                    .refundType(e.get(qRsCourseRefundOrderSub.refundType))
                    .remark(e.get(qRsCourseRefundOrderSub.remark))
                    .image(JSONUtil.parseArray(e.get(qRsCourseRefundOrderSub.image)))
                    .force(usersMap.getOrDefault(e.get(qRsCourseRefundOrderSub.forceId), new UcUsers()).getRealName() + "-" + e.get(qRsCourseRefundOrderSub.forceId))
                    .refundTime(e.get(qRsCourseRefundOrderSub.gmtRefund))
                    .forceRefundTime(ObjectUtil.isNotEmpty(e.get(qRsCourseRefundOrderSub.forceRefundDate)) ? DateUtils.getDayOfStart(e.get(qRsCourseRefundOrderSub.forceRefundDate)) : null)
                    .build();
            if (e.get(qRsCourseSubOrder.merchantId) != null) {
                RsMerchantInfo merchantInfo = merchantService.getMerchantInfoById(e.get(qRsCourseSubOrder.merchantId));
                if (merchantInfo != null) {
                    refundPlanResp.setMchName(merchantInfo.getName());
                }
            }
            refundPlanRespList.add(refundPlanResp);
        });
        return refundPlanRespList;
    }

    @Transactional
    public OrderErrorCode signAgain(Integer refundOrderSignId, Integer signId, Integer refundId) {
        // 签字审核删除
        jpaQueryPrimary.update(qRsCourseRefundSign)
                .where(qRsCourseRefundSign.id.eq(refundOrderSignId))
                .set(qRsCourseRefundSign.auditStatus, SignAuditStatusEnum.AUDIT_DELETE.getValue())
                .set(qRsCourseRefundSign.auditTime, Instant.now())
                .execute();
        BaseResult<String> stringBaseResult = contentClient.abandonSignById(signId);
        if (stringBaseResult.isFail()) {
            throw new BusinessException(stringBaseResult);
        }
        if (stringBaseResult.isSuccess()) {
            return startSign(refundId);
        } else {
            return OrderErrorCode.FAILURE;
        }

    }

    public OrderErrorCode auditSign(Integer refundId) {
        RsCourseRefundSign rsCourseRefundSign = jpaQueryPrimary.select(qRsCourseRefundSign).from(qRsCourseRefundSign).where(qRsCourseRefundSign.refundId.eq(refundId).and(qRsCourseRefundSign.isSigned.eq(true)).and(qRsCourseRefundSign.auditStatus.eq(SignAuditStatusEnum.AUDIT_EXECUTING.getValue()))).fetchOne();
        if (ObjectUtil.isEmpty(rsCourseRefundSign)) {
            return OrderErrorCode.RECORD_NOT_FOUND;
        }
        // 签字审核通过
        rsCourseRefundSign.setAuditStatus(SignAuditStatusEnum.AUDIT_PASS.getValue());
        rsCourseRefundSign.setAuditTime(Instant.now());
        rsCourseRefundSign.setUpdateTime(Instant.now());
        RsCourseRefundSign refundSign = signRepository.save(rsCourseRefundSign);
        if (ObjectUtil.isEmpty(refundSign)) {
            return OrderErrorCode.FAILURE;
        }
        return OrderErrorCode.SUCCESS;
    }

    public OrderErrorCode startSign(Integer refundId) {
        RsCourseRefundSign existedOne = jpaQueryPrimary.selectFrom(qRsCourseRefundSign).where(qRsCourseRefundSign.refundId.eq(refundId)).fetchOne();
        if (ObjectUtil.isNotEmpty(existedOne)) {
            return OrderErrorCode.SIGN_RECORD_EXISTED;
        }
        RsCourseRefundOrder rsCourseRefundOrder = jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder).where(qRefundOrder.id.eq(refundId)).fetchOne();
        RsCourseOrder order = orderService.findOneByFilter(rsCourseRefundOrder.getOrderId());
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String signNumber = genSignNumber.getRefundSignNumber(order.getCompanyType(), date);
        RsCourseRefundSign rsCourseRefundSign = new RsCourseRefundSign();
        rsCourseRefundSign.setRefundId(refundId);
        rsCourseRefundSign.setNumber(signNumber);
        rsCourseRefundSign.setIsSigned(SignStatusEnum.SIGNING.getValue());
        rsCourseRefundSign.setAuditStatus(SignAuditStatusEnum.AUDIT_EXECUTING.getValue());
        RsCourseRefundSign refundSign = signRepository.save(rsCourseRefundSign);
        if (ObjectUtil.isEmpty(refundSign)) {
            return OrderErrorCode.FAILURE;
        }
        return OrderErrorCode.SUCCESS;
    }

    @Transactional
    public boolean updateRefundOrderStatus(Integer id, Integer refundStatus) {
        JPAUpdateClause updateClause = jpaQueryPrimary.update(qRefundOrder)
                .set(qRefundOrder.status, refundStatus)
                .where(qRefundOrder.id.eq(id))
                .where(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Refunding.getValue()));
        if (Objects.equals(refundStatus, RefundOrderAuditStatusEnum.ORS_Passed.getValue())) {
            updateClause.set(qRefundOrder.refundTime, Instant.now());
        }
        long effectRows = updateClause.execute();
        return effectRows > 0;
    }

    @Transactional
    public boolean updateRefundOrderStatus(Integer id, Integer refundStatus, Instant refundTime) {
        JPAUpdateClause updateClause = jpaQueryPrimary.update(qRefundOrder)
                .set(qRefundOrder.status, refundStatus)
                .where(qRefundOrder.id.eq(id));
        if (Objects.nonNull(refundTime)) {
            updateClause.set(qRefundOrder.refundTime, refundTime);
        }
        long effectRows = updateClause.execute();
        return effectRows > 0;
    }

    public List<RefundOrderSkuInfo> getRefundOrderSkuList(Integer companyType, Integer userId, String orderNumber) {
        BlazeJPAQuery<RefundOrderSkuInfo> blazeJpaQuery = new BlazeJPAQuery<>(primaryEntityManager, primaryCriteriaBuilderFactory);
        BlazeJPAQuery<RefundOrderSkuInfo> query = blazeJpaQuery.select(Projections.bean(RefundOrderSkuInfo.class,
                        qRsCourseOrder,
                        qRsSku,
                        qRefundOrder))
                .from(qRsCourseOrder)
                .leftJoin(qRsSku)
                .on(qRsCourseOrder.skuId.eq(qRsSku.id))
                .leftJoin(qRefundOrder)
                .on(qRefundOrder.orderId.eq(qRsCourseOrder.id))
                .where(qRsCourseOrder.companyType.eq(companyType))
                .where(qRsCourseOrder.enabled.eq(true))
                .where(qRefundOrder.enabled.eq(true));

        if (ObjectUtil.isNotEmpty(userId)) {
            query.where(qRsCourseOrder.userId.eq(userId));
        }
        if (ObjectUtil.isNotEmpty(orderNumber)) {
            query.where(qRsCourseOrder.orderNumber.eq(orderNumber));
        }

        return query.orderBy(qRefundOrder.createTime.desc(), qRefundOrder.id.desc()).fetch();
    }

    public Integer getOrderIdByRefundId(Integer refundId) {
        return refundRepository.findById(refundId).orElse(new RsCourseRefundOrder()).getOrderId();
    }

    public RsCourseRefundOrder getOrderIdByRefundNumber(String refundNumber) {
        return jpaQueryPrimary.select(qRefundOrder).from(qRefundOrder).where(qRefundOrder.refundNumber.eq(refundNumber)).fetchOne();
    }

    @Transactional
    public OsErrorCode readRefundPdf(Integer refundId) {
        jpaQueryPrimary.update(qRefundOrder).where(qRefundOrder.id.eq(refundId))
                .set(qRefundOrder.readStatus, ReadStatusConstant.CONFIRM.getValue())
                .execute();
        return OsErrorCode.SUCCESS;
    }

    @Transactional
    public void refundingCompletedIfAllSubordersRefunded(RefundCompletedReq req) {
        Integer refundOrderId = req.getRefundOrderId();
        RsCourseRefundOrder refundOrder = refundRepository.findById(refundOrderId).orElseThrow(RecordNotExistedException::new);
        if (!ObjectUtil.equals(refundOrder.getStatus(), RefundOrderAuditStatusEnum.ORS_Refunding.getValue())) {
            log.warn("退款主订单状态未在退款中,refundOrder={}", JSONUtil.toJsonStr(refundOrder));
            return;
        }
        List<RsCourseRefundOrderSub> refundSubOrderList = refundOrderSubService.getRefundSubOrderList(refundOrderId, null, null);
        if (CollUtil.isEmpty(refundSubOrderList)
                || refundSubOrderList.stream().anyMatch(refundOrderSub -> !Objects.equals(refundOrderSub.getRefundStatus(), RefundOrderStatusEnum.REFUNDED.getValue()))) {
            long refundedCnt = refundSubOrderList.stream()
                    .filter(refundOrderSub -> Objects.equals(refundOrderSub.getRefundStatus(), RefundOrderStatusEnum.REFUNDED.getValue())).count();
            long refundingCnt = refundSubOrderList.size() - refundedCnt;
            log.warn("无退款子订单或子订单状态不全为已退款,refundOrder={},refundedCnt={},refundingCnt={}", JSONUtil.toJsonStr(refundOrder), refundedCnt, refundingCnt);
            return;
        }
        Instant refundTime = Instant.now();
        JPAUpdateClause updateClause = jpaQueryPrimary.update(qRefundOrder)
                .set(qRefundOrder.status, RefundOrderAuditStatusEnum.ORS_Passed.getValue())
                .set(qRefundOrder.refundTime, refundTime)
                .where(
                        qRefundOrder.id.eq(refundOrderId)
                                .and(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Refunding.getValue()))
                );
        long executeSuccess = updateClause.execute();
        flowRecordService.addOne(RsRefundFlowRecord.builder()
                .refundId(refundOrderId)
                .operateType(RefundFlowOperateTypeEnum.RFOT_COMPLETED.getValue())
                .createTime(Instant.now()).build());
        log.info("退款订单[{}]已完成", refundOrderId);
        Optional<RsCourseOrder> order = orderService.getOrder(refundOrder.getOrderId());
        if (order.isPresent() && ObjectUtil.isNotNull(order.get().getUserId())) {
            userClient.updateCustomerExtRefund(order.get().getUserId());
        }
        notifyOrderProcessService.refundSendNotify(refundOrderId, RefundOrderAuditStatusEnum.ORS_Passed.getValue(), refundTime);
    }

    @Transactional(rollbackFor = Exception.class)
    public void createOrderBankInfo(CreateBankReq req, Integer orderId, String name) {
        RsRefundBankInfo info = RsRefundBankInfo.builder()
                .name(name)
                .accountBank(req.getAccountBank())
                .cardNumber(req.getCardNumber())
                .orderId(orderId)
                .build();
        RsRefundBankInfo save = refundBankInfoRepository.save(info);
        RsRefundBankRecord record = RsRefundBankRecord.builder()
                .orderId(orderId)
                .operatorId(req.getOperatorId())
                .content(buildBankRecordContent(save))
                .build();
        refundBankRecordRepository.save(record);
    }

    public void updateRefundBankInfo(RefundBankUpdateReq req) {
        RsRefundBankInfo refundBankInfo = jpaQueryPrimary.selectFrom(qRsRefundBankInfo)
                .where(qRsRefundBankInfo.orderId.eq(req.getOrderId())).fetchOne();
        if (ObjectUtil.isEmpty(refundBankInfo)) {
            refundBankInfo = RsRefundBankInfo.builder().orderId(req.getOrderId()).build();
        }
        if (ObjectUtil.isNotEmpty(req.getName())) {
            refundBankInfo.setName(req.getName());
        }
        if (ObjectUtil.isNotEmpty(req.getAccountBank())) {
            refundBankInfo.setAccountBank(req.getAccountBank());
        }
        if (ObjectUtil.isNotEmpty(req.getCardNumber())) {
            refundBankInfo.setCardNumber(req.getCardNumber());
        }
        RsRefundBankInfo save = refundBankInfoRepository.save(refundBankInfo);
        RsRefundBankRecord record = RsRefundBankRecord.builder()
                .orderId(req.getOrderId())
                .operatorId(req.getOperatorId())
                .content(buildBankRecordContent(save))
                .build();
        refundBankRecordRepository.save(record);
    }

    private static String buildBankRecordContent(RsRefundBankInfo bankInfo) {
        StringBuilder sb = new StringBuilder();
        sb.append("姓名：")
                .append(Optional.ofNullable(bankInfo.getName()).orElse(""))
                .append("\n")
                .append("开户行：")
                .append(Optional.ofNullable(bankInfo.getAccountBank()).orElse(""))
                .append("\n")
                .append("银行卡号：")
                .append(Optional.ofNullable(bankInfo.getCardNumber()).orElse(""));
        return sb.toString();
    }


    public Optional<RsRefundBankInfo> orderRefundBankInfo(Integer orderId) {
        RsRefundBankInfo entity = jpaQueryPrimary.selectFrom(qRsRefundBankInfo)
                .where(qRsRefundBankInfo.orderId.eq(orderId))
                .fetchOne();
        return Optional.ofNullable(entity);
    }

    public PageResult<List<RefundPlanResp>> getRefundManualList(LocalDateTime createStartTime, LocalDateTime createEndTime, Long payCompanyId, Integer current, Integer size) {
        JPAQuery<RefundPlanResp> query = jpaQueryPrimary.select(Projections.bean(RefundPlanResp.class,
                        qRsCourseRefundOrderSub.refundNumber,
                        qRsCourseRefundOrderSub.gmtCreate,
                        qRsCourseRefundOrderSub.refundFee,
                        qRsCourseRefundOrderSub.mainOrderId,
                        qRsCourseRefundOrderSub.refundOrderId,
                        qRsCourseRefundOrderSub.subOrderId,
                        qRsCourseRefundOrderSub.refundStatus,
                        qRsCourseSubOrder.amount.as("orderAmount"),
                        qRsCourseSubOrder.payTime.as("gmtPay"),
                        qRsCourseSubOrder.payType,
                        qRsCourseSubOrder.number.as("subOrderNumber"),
                        qRsCourseSubOrder.merchantId
                ))
                .from(qRsCourseRefundOrderSub)
                .leftJoin(courseOrder).on(courseOrder.id.eq(qRsCourseRefundOrderSub.mainOrderId))
                .leftJoin(qRsCourseSubOrder).on(qRsCourseSubOrder.id.eq(qRsCourseRefundOrderSub.subOrderId))
                .leftJoin(qRefundOrder).on(qRefundOrder.id.eq(qRsCourseRefundOrderSub.refundOrderId))
                .leftJoin(qRsMerchantInfo).on(qRsCourseSubOrder.merchantId.eq(qRsMerchantInfo.id))
                .where(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Refunding.getValue()))
                .where(qRsMerchantInfo.platform.eq(PP_Transfer.getCode()).or(qRsMerchantInfo.platform.ne(PP_Transfer.getCode()).and(qRsCourseRefundOrderSub.refundStatus.eq(RefundOrderStatusEnum.FAILED.getValue()))))
                .where(courseOrder.payCompanyId.eq(payCompanyId));
        if (createStartTime != null) {
            query.where(qRsCourseRefundOrderSub.gmtCreate.goe(createStartTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (createEndTime != null) {
            query.where(qRsCourseRefundOrderSub.gmtCreate.lt(createEndTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qRsCourseRefundOrderSub.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        List<RefundPlanResp> fetch = query.fetch();
        Set<Integer> orderIds = fetch.stream().map(RefundPlanResp::getMainOrderId).collect(Collectors.toSet());
        List<RsRefundBankInfo> refundBankInfos = jpaQueryPrimary.selectFrom(qRsRefundBankInfo)
                .where(qRsRefundBankInfo.orderId.in(orderIds))
                .fetch();
        Map<Integer, RsRefundBankInfo> bankInfoMap = refundBankInfos.stream().collect(Collectors.toMap(RsRefundBankInfo::getOrderId, Function.identity()));
        fetch.forEach(e -> {
            e.setName(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getName());
            e.setAccountBank(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getAccountBank());
            e.setCardNumber(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getCardNumber());
            e.setCardCreateTime(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getGmtCreate());
        });

        return PageResult.success(fetch, Pagination.of(current, size, total));
    }

    public BaseResult<List<RefundPlanResp>> getRefundManualListExport(LocalDateTime createStartTime, LocalDateTime createEndTime, Long payCompanyId) {
        JPAQuery<RefundPlanResp> query = jpaQueryPrimary.select(Projections.bean(RefundPlanResp.class,
                        qRsCourseRefundOrderSub.refundNumber,
                        qRsCourseRefundOrderSub.gmtCreate,
                        qRsCourseRefundOrderSub.refundFee,
                        qRsCourseRefundOrderSub.mainOrderId,
                        qRsCourseRefundOrderSub.refundOrderId,
                        qRsCourseRefundOrderSub.subOrderId,
                        qRsCourseRefundOrderSub.refundStatus,
                        qRsCourseSubOrder.amount.as("orderAmount"),
                        qRsCourseSubOrder.payTime.as("gmtPay"),
                        qRsCourseSubOrder.payType,
                        qRsCourseSubOrder.number.as("subOrderNumber"),
                        qRsCourseSubOrder.merchantId
                ))
                .from(qRsCourseRefundOrderSub)
                .leftJoin(courseOrder).on(courseOrder.id.eq(qRsCourseRefundOrderSub.mainOrderId))
                .leftJoin(qRsCourseSubOrder).on(qRsCourseSubOrder.id.eq(qRsCourseRefundOrderSub.subOrderId))
                .leftJoin(qRefundOrder).on(qRefundOrder.id.eq(qRsCourseRefundOrderSub.refundOrderId))
                .leftJoin(qRsMerchantInfo).on(qRsCourseSubOrder.merchantId.eq(qRsMerchantInfo.id))
                .where(qRefundOrder.status.eq(RefundOrderAuditStatusEnum.ORS_Refunding.getValue()))
                .where(qRsMerchantInfo.platform.eq(PP_Transfer.getCode()).or(qRsMerchantInfo.platform.ne(PP_Transfer.getCode()).and(qRsCourseRefundOrderSub.refundStatus.eq(RefundOrderStatusEnum.FAILED.getValue()))))
                .where(courseOrder.payCompanyId.eq(payCompanyId));
        if (createStartTime != null) {
            query.where(qRsCourseRefundOrderSub.gmtCreate.goe(createStartTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (createEndTime != null) {
            query.where(qRsCourseRefundOrderSub.gmtCreate.lt(createEndTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        List<RefundPlanResp> fetch = query.fetch();
        Set<Integer> orderIds = fetch.stream().map(RefundPlanResp::getMainOrderId).collect(Collectors.toSet());
        List<RsRefundBankInfo> refundBankInfos = jpaQueryPrimary.selectFrom(qRsRefundBankInfo)
                .where(qRsRefundBankInfo.orderId.in(orderIds))
                .fetch();
        Map<Integer, RsRefundBankInfo> bankInfoMap = refundBankInfos.stream().collect(Collectors.toMap(RsRefundBankInfo::getOrderId, Function.identity()));
        fetch.forEach(e -> {
            e.setName(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getName());
            e.setAccountBank(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getAccountBank());
            e.setCardNumber(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getCardNumber());
            e.setCardCreateTime(bankInfoMap.getOrDefault(e.getMainOrderId(), new RsRefundBankInfo()).getGmtCreate());
        });

        return BaseResult.success(fetch);
    }

    public PageResult<List<RefundBankRecordResp>> orderRefundBankRecord(Integer orderId, Integer current, Integer size) {
        JPAQuery<RsRefundBankRecord> query = jpaQueryPrimary.selectFrom(qRsRefundBankRecord)
                .where(qRsRefundBankRecord.orderId.eq(orderId));
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qRsRefundBankRecord.gmtCreate.desc());
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        List<RsRefundBankRecord> fetch = query.fetch();
        if (ObjectUtil.isEmpty(fetch)) {
            return PageResult.success(Collections.emptyList(), Pagination.of(current, size, total));
        }
        return PageResult.success(BeanUtil.copyToList(fetch, RefundBankRecordResp.class), Pagination.of(current, size, total));
    }

}

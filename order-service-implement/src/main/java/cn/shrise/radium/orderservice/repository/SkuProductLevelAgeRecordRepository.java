package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.SkuProductLevelAgeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/6/19, 星期四
 **/
@Repository
public interface SkuProductLevelAgeRecordRepository extends JpaRepository<SkuProductLevelAgeRecord, Long>,
        QuerydslPredicateExecutor<SkuProductLevelAgeRecord> {
}

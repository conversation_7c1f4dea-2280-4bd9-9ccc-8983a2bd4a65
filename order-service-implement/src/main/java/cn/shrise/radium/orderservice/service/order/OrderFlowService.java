package cn.shrise.radium.orderservice.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.orderservice.constant.*;
import cn.shrise.radium.orderservice.dto.OrderInfoDto;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import cn.shrise.radium.orderservice.repository.OrderFlowRecordRepository;
import cn.shrise.radium.orderservice.repository.OrderFlowRepository;
import cn.shrise.radium.orderservice.req.BatchAssignReq;
import cn.shrise.radium.orderservice.resp.AssignDetailResp;
import cn.shrise.radium.orderservice.resp.HgBatchAssignRecordResp;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderFlowService {

    private final JPAQueryFactory jpaQueryPrimary;
    private final OrderFlowRecordRepository orderFlowRecordRepository;
    private final QOrderFlowRecord qOrderFlowRecord = QOrderFlowRecord.orderFlowRecord;
    private final OrderFlowRepository orderFlowRepository;
    private final QOrderFlow orderFlow = QOrderFlow.orderFlow;
    private final QRsCourseOrder qRsCourseOrder = QRsCourseOrder.rsCourseOrder;
    private final QRsSku qRsSku = QRsSku.rsSku;
    private final JdbcTemplate jdbcTemplate;
    private final QRsOrderAssignBatchRecord qRsOrderAssignBatchRecord = QRsOrderAssignBatchRecord.rsOrderAssignBatchRecord;
    private final QRsCourseOrderSign qRsCourseOrderSign = QRsCourseOrderSign.rsCourseOrderSign;

    @Transactional
    public void createOne(Integer orderId, Integer operateType, Integer operatorID) {
        OrderFlowRecord record = OrderFlowRecord.builder()
                .orderId(orderId)
                .operateType(operateType)
                .operatorId(operatorID)
                .build();
        orderFlowRecordRepository.save(record);
    }

    @Transactional
    public void createOne(Integer orderId, Integer operateType, Integer operatorID, String content) {
        OrderFlowRecord.OrderFlowRecordBuilder builder = OrderFlowRecord.builder()
                .orderId(orderId)
                .operateType(operateType)
                .operatorId(operatorID);
        if (content != null) {
            builder.content(content);
        }
        orderFlowRecordRepository.save(builder.build());
    }

    public OrderFlowRecord createOrderFlowRecord(OrderFlowRecord orderFlowRecord) {
        return orderFlowRecordRepository.save(orderFlowRecord);
    }

    public List<OrderFlowRecord> findByOrderId(Integer orderId) {
        return jpaQueryPrimary.select(qOrderFlowRecord).from(qOrderFlowRecord).where(qOrderFlowRecord.orderId.eq(orderId)).fetch();
    }

    public OrderFlow findFlowByOrderId(Integer orderId) {
        return jpaQueryPrimary.select(orderFlow).from(orderFlow).where(orderFlow.orderId.eq(orderId)).fetchOne();
    }

    public void createOrderFlow(Integer id) {
        List<OrderFlow> orderFlows = jpaQueryPrimary.select(orderFlow).from(orderFlow).where(orderFlow.orderId.eq(id)).fetch();
        if (ObjectUtil.isEmpty(orderFlows)) {
            OrderFlow orderFlow = OrderFlow.builder().orderId(id).auditStatus(OrderAuditStatusConstant.SWASSIGNING.getValue()).build();
            orderFlowRepository.save(orderFlow);
        }
    }

    @Transactional
    public void updateOrderFlowAuditStatus(Integer orderId, Integer autoAuditStatus, Integer auditStatus, String reason) {
        JPAUpdateClause clause = jpaQueryPrimary.update(orderFlow)
                .where(orderFlow.orderId.eq(orderId));
        if (autoAuditStatus != null) {
            clause.set(orderFlow.autoAuditStatus, autoAuditStatus);
        }
        if (auditStatus != null) {
            clause.set(orderFlow.auditStatus, auditStatus);
        }
        if (reason != null) {
            clause.set(orderFlow.reason, reason);
        }
        clause.execute();
    }

    @Transactional
    public void updateOrderFlowStatus(Integer orderId, Integer operatorId, Integer hgAssignId, Integer auditStatus) {
        JPAUpdateClause clause = jpaQueryPrimary.update(orderFlow)
                .set(orderFlow.auditStatus, auditStatus)
                .where(orderFlow.orderId.eq(orderId));
        if (operatorId != null) {
            clause.set(orderFlow.hgAssignId, operatorId);
        }
        if (hgAssignId != null) {
            clause.set(orderFlow.hgAuditorId, hgAssignId);
        }
        if (auditStatus.equals(OrderAuditStatusConstant.HGAUDITING.getValue())) {
            clause.set(orderFlow.hgAssignTime, Instant.now());
        } else if (auditStatus.equals(OrderAuditStatusConstant.PASSED.getValue())) {
            clause.set(orderFlow.hgAuditTime, Instant.now());
        }
        clause.execute();
    }

    public List<HgBatchAssignRecordResp> getHgBatchAssignRecord(Long batchId) {
        List<Tuple> fetch = jpaQueryPrimary.select(qOrderFlowRecord, qRsCourseOrder, qRsSku, orderFlow).from(qOrderFlowRecord)
                .leftJoin(qRsCourseOrder).on(qRsCourseOrder.id.eq(qOrderFlowRecord.orderId))
                .leftJoin(qRsSku).on(qRsSku.id.eq(qRsCourseOrder.skuId))
                .leftJoin(orderFlow).on(orderFlow.orderId.eq(qOrderFlowRecord.orderId))
                .where(qOrderFlowRecord.batchId.eq(batchId))
                .fetch();
        if (ObjectUtil.isEmpty(fetch)) {
            return Collections.emptyList();
        }
        return fetch.stream().map(e -> HgBatchAssignRecordResp.builder()
                .orderNumber(e.get(qRsCourseOrder).getOrderNumber())
                .payTime(e.get(qRsCourseOrder).getPayTime())
                .amount(e.get(qRsCourseOrder).getAmount())
                .createTime(e.get(qRsCourseOrder).getCreateTime())
                .salesId(e.get(qRsCourseOrder).getSalesId())
                .productLevel(e.get(qRsSku).getProductLevel())
                .userId(e.get(qRsCourseOrder).getUserId())
                .hgAuditorId(e.get(orderFlow).getHgAuditorId())
                .build()).collect(Collectors.toList());
    }

    @Transactional
    public void batchCreateOrderFlowRecord(List<OrderFlowRecord> records) {
        String insertSql = SqlUtil.batchInsertSql(records);
        jdbcTemplate.execute(insertSql);
    }

    @Transactional
    public void updateAssignBatchRecordStatus(Long batchId) {
        jpaQueryPrimary.update(qRsOrderAssignBatchRecord)
                .set(qRsOrderAssignBatchRecord.status, BatchAssignStatusConstant.Completed)
                .where(qRsOrderAssignBatchRecord.id.eq(batchId))
                .execute();
    }

    public Page<RsOrderAssignBatchRecord> getRsOrderAssignBatchRecord(Pageable pageable) {
        JPAQuery<RsOrderAssignBatchRecord> query = jpaQueryPrimary.select(qRsOrderAssignBatchRecord).from(qRsOrderAssignBatchRecord)
                .orderBy(qRsOrderAssignBatchRecord.gmtCreate.desc());

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        List<RsOrderAssignBatchRecord> result = query.offset(pageable.getOffset()).limit(pageable.getPageSize()).fetch();

        return new PageImpl<>(result, pageable, total);
    }

    public List<AssignDetailResp> getExtractDetail(Long batchId) {
        NumberExpression<Long> count = qRsCourseOrder.salesId.count().as("count");
        List<Tuple> tuples = jpaQueryPrimary.select(Projections.tuple(qRsCourseOrder.salesId,
                        count))
                .from(qOrderFlowRecord)
                .leftJoin(qRsCourseOrder).on(qOrderFlowRecord.orderId.eq(qRsCourseOrder.id))
                .where(qOrderFlowRecord.batchId.eq(batchId))
                .groupBy(qRsCourseOrder.salesId)
                .fetch();
        return tuples.stream().map(tuple -> AssignDetailResp.builder()
                        .userId(tuple.get(qRsCourseOrder.salesId))
                        .count(tuple.get(count))
                        .build())
                .collect(Collectors.toList());
    }

    public List<AssignDetailResp> getAssignDetail(Long batchId) {
        NumberExpression<Long> count = orderFlow.hgAuditorId.count().as("count");
        List<Tuple> tuples = jpaQueryPrimary.select(Projections.tuple(orderFlow.hgAuditorId,
                        count))
                .from(qOrderFlowRecord)
                .leftJoin(orderFlow).on(orderFlow.orderId.eq(qOrderFlowRecord.orderId))
                .where(qOrderFlowRecord.batchId.eq(batchId))
                .groupBy(orderFlow.hgAuditorId)
                .fetch();
        return tuples.stream().map(tuple -> AssignDetailResp.builder()
                        .userId(tuple.get(orderFlow.hgAuditorId))
                        .count(tuple.get(count))
                        .build())
                .collect(Collectors.toList());
    }

    @Transactional
    public void assignHg(String orderNumber, Integer hgAuditorId, Integer hgAssignId) {
        RsCourseOrder order = jpaQueryPrimary.select(qRsCourseOrder).from(qRsCourseOrder)
                .where(qRsCourseOrder.orderNumber.eq(orderNumber))
                .fetchOne();
        if (ObjectUtil.isEmpty(order)) {
            throw new BusinessException(OsErrorCode.NOT_FOUND);
        }
        updateOrderFlowStatus(order.getId(), hgAssignId, hgAuditorId, OrderAuditStatusConstant.HGAUDITING.getValue());
        createOrderFlowRecord(OrderFlowRecord.builder()
                .operateType(OrderFlowOperateTypeConstant.HG_ASSIGN.getValue())
                .orderId(order.getId())
                .operatorId(hgAssignId)
                .build());
    }

    @Transactional
    public void reallocateHg(BatchAssignReq req) {
        List<RsCourseOrder> rsCourseOrders = jpaQueryPrimary.select(qRsCourseOrder).from(qRsCourseOrder)
                .where(qRsCourseOrder.orderNumber.in(req.getOrderNumberList()))
                .fetch();
        List<Integer> orderIds = rsCourseOrders.stream().map(RsCourseOrder::getId).collect(Collectors.toList());
        List<OrderFlowRecord> records = new ArrayList<>();
        for (Integer orderId : orderIds) {
            updateOrderFlowStatus(orderId, req.getOperatorId(), req.getHgIdList().get(0), OrderAuditStatusConstant.HGAUDITING.getValue());
            records.add(OrderFlowRecord.builder()
                    .operateType(OrderFlowOperateTypeConstant.HG_ASSIGN.getValue())
                    .orderId(orderId)
                    .operatorId(req.getOperatorId())
                    .build());
        }
        batchCreateOrderFlowRecord(records);
    }

    public Page<OrderInfoDto> auditingHg(Integer auditorId, Pageable pageable) {
        JPAQuery<Tuple> query = jpaQueryPrimary.select(qRsCourseOrder, orderFlow, qRsSku)
                .from(orderFlow)
                .leftJoin(qRsCourseOrder).on(qRsCourseOrder.id.eq(orderFlow.orderId))
                .leftJoin(qRsSku).on(qRsSku.id.eq(qRsCourseOrder.skuId))
                .where(orderFlow.auditStatus.eq(OrderAuditStatusConstant.HGAUDITING.getValue()))
                .orderBy(qRsCourseOrder.payTime.desc());

        if (auditorId != null) {
            query.where(orderFlow.hgAuditorId.eq(auditorId));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        List<Tuple> tuples = query
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();
        if (ObjectUtil.isEmpty(tuples)) {
            return Page.empty(pageable);
        }
        List<OrderInfoDto> infoDtoList = tuples.stream().map(e ->
                OrderInfoDto.builder()
                        .orderInfo(e.get(qRsCourseOrder))
                        .orderFlow(e.get(orderFlow))
                        .skuInfo(e.get(qRsSku))
                        .build()
        ).collect(Collectors.toList());
        return new PageImpl<>(infoDtoList, pageable, total);
    }

    @Transactional
    public void auditing(Integer auditorId, String orderNumber, Integer auditStatus, String content) {
        OrderFlow orderFlow = jpaQueryPrimary.select(this.orderFlow).from(this.orderFlow)
                .leftJoin(qRsCourseOrder).on(qRsCourseOrder.id.eq(this.orderFlow.orderId))
                .where(this.qRsCourseOrder.orderNumber.eq(orderNumber))
                .fetchOne();
        if (ObjectUtil.isEmpty(orderFlow)) {
            throw new BusinessException(OrderErrorCode.RECORD_NOT_FOUND);
        }
        if (!ObjectUtil.equals(OrderAuditStatusConstant.HGAUDITING.getValue(), orderFlow.getAuditStatus())) {
            throw new BusinessException(OrderErrorCode.AUDIT_STATUS_ERROR);
        }
        updateOrderFlowStatus(orderFlow.getOrderId(), auditorId, null, auditStatus);
        Integer operateType = null;
        if (OrderAuditStatusConstant.PASSED.getValue().equals(auditStatus)) {
            operateType = OrderFlowOperateTypeConstant.HG_AUDIT.getValue();
        } else if (OrderAuditStatusConstant.CLOSED.getValue().equals(auditStatus)) {
            operateType = OrderFlowOperateTypeConstant.CLOSE.getValue();
        }
        createOne(orderFlow.getOrderId(), operateType, auditorId, content);
    }

    @Transactional
    public void createAssignOrderData(Integer operatorId, List<OrderFlow> updateOrderFlowList, List<OrderFlowRecord> flowRecordList) {
        if (ObjectUtil.isNotEmpty(updateOrderFlowList)) {
            this.batchUpdateOrderFlow(OrderAuditStatusConstant.HGAUDITING.getValue(), operatorId, updateOrderFlowList);
        }
        if (ObjectUtil.isNotEmpty(flowRecordList)) {
            this.batchCreateOrderFlowRecord2(flowRecordList);
        }
    }

    @Transactional
    public void batchUpdateOrderFlow(Integer auditStatus, Integer operatorId, List<OrderFlow> updateList) {
        Lists.partition(updateList, 500).forEach(batch -> {
            CaseBuilder.Cases<Integer, NumberExpression<Integer>> hgAuditorBuilder = Expressions.cases().when(orderFlow.orderId.eq(-1)).then(1);
            List<Integer> orderIds = new ArrayList<>();
            for (OrderFlow flow: batch) {
                hgAuditorBuilder.when(orderFlow.orderId.eq(flow.getOrderId())).then(flow.getHgAuditorId());
                orderIds.add(flow.getOrderId());
            }
            NumberExpression<Integer> hgAuditorExpression = hgAuditorBuilder.otherwise((Integer) null);
            jpaQueryPrimary.update(orderFlow)
                    .set(orderFlow.hgAssignId, operatorId)
                    .set(orderFlow.auditStatus, auditStatus)
                    .set(orderFlow.hgAssignTime, Instant.now())
                    .set(orderFlow.hgAuditorId, hgAuditorExpression)
                    .where(orderFlow.orderId.in(orderIds))
                    .execute();
        });
    }

    @Transactional
    public void batchCreateOrderFlowRecord2(List<OrderFlowRecord> records) {
        Lists.partition(records, 2000).forEach(batch -> {
            String insertSql = SqlUtil.batchInsertSql(records);
            jdbcTemplate.execute(insertSql);
        });
    }
}

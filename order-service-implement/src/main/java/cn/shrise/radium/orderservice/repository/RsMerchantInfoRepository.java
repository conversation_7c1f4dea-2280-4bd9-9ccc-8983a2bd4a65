package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsMerchantInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RsMerchantInfoRepository extends JpaRepository<RsMerchantInfo, Long>, QuerydslPredicateExecutor<RsMerchantInfo> {

}

package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.CourseSubOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface CourseSubOrderRepository extends JpaRepository<CourseSubOrder, Integer>, QuerydslPredicateExecutor<CourseSubOrder> {

    /**
     * 根据订单id获取相关子订单
     * @param orderId 订单id
     * @return 相关子订单
     */
    List<CourseSubOrder> findByOrderId(Integer orderId);

    /**
     * 根据订单号获取子订单信息
     * @param orderNumber 订单号
     * @return 子订单信息
     */
    Optional<CourseSubOrder> findByNumber(String orderNumber);

}

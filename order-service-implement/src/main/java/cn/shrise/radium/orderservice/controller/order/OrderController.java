package cn.shrise.radium.orderservice.controller.order;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.orderservice.constant.*;
import cn.shrise.radium.orderservice.dao.RsCourseOrderLakehouseDao;
import cn.shrise.radium.orderservice.dao.RsStrategyOrderLinkDao;
import cn.shrise.radium.orderservice.dto.OrderBelongDto;
import cn.shrise.radium.orderservice.dto.OrderExtInfoDto;
import cn.shrise.radium.orderservice.dto.OrderInfoDto;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.error.OsErrorCode;
import cn.shrise.radium.orderservice.properties.merchant.Merchant;
import cn.shrise.radium.orderservice.req.*;
import cn.shrise.radium.orderservice.resp.*;
import cn.shrise.radium.orderservice.service.AuditManageService;
import cn.shrise.radium.orderservice.service.CourseServService;
import cn.shrise.radium.orderservice.service.DepartmentOrderService;
import cn.shrise.radium.orderservice.service.analyst.DeptOrderAnalyst;
import cn.shrise.radium.orderservice.service.analyst.OrderAnalyst;
import cn.shrise.radium.orderservice.service.common.CommonService;
import cn.shrise.radium.orderservice.service.order.*;
import cn.shrise.radium.orderservice.service.pay.AlipayService;
import cn.shrise.radium.orderservice.service.pay.AllinPayService;
import cn.shrise.radium.orderservice.service.pay.LklPayService;
import cn.shrise.radium.orderservice.service.pay.WechatPayService;
import cn.shrise.radium.orderservice.service.payment.PaymentService;
import cn.shrise.radium.orderservice.service.refund.OrderRefundService;
import cn.shrise.radium.orderservice.service.refund.RefundService;
import cn.shrise.radium.orderservice.service.sku.SkuService;
import cn.shrise.radium.orderservice.util.esign.GenSignNumber;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.util.DateUtils.DEFAULT_PATTERN_DATE;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("orders")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;
    private final TransferOrderFlowService transferOrderFlowService;

    private final OrderAnalyst orderAnalyst;

    private final AuditManageService manageService;

    private final DepartmentOrderService departmentOrderService;

    private final DeptOrderAnalyst deptOrderAnalyst;

    private final SkuService skuService;

    private final OrderRefundService orderRefundService;

    private final CourseOrderSignService courseOrderSignService;
    private final CourseServService courseServService;

    private final GenSignNumber genSignNumber;
    private final OrderFeedbackService orderFeedbackService;
    private final OrderNotifyService orderNotifyService;
    private final SubOrderService subOrderService;
    private final WechatPayService wechatPayService;
    private final AlipayService alipayService;
    private final RefundService refundService;
    private final CommonService commonService;
    private final UserClient userClient;
    private final LklPayService lklPayService;
    private final AllinPayService allinPayService;
    private final OrderDeliveryService deliveryService;
    private final PaymentService paymentService;
    private final RsCourseOrderLakehouseDao rsCourseOrderLakehouseDao;
    private final RsStrategyOrderLinkDao strategyOrderLinkDao;

    @ApiOperation("获取订单归属信息操作列表")
    @GetMapping("getOrderBelongOperateRecordList")
    public BaseResult<List<RsOrderBelongOperateRecord>> getOrderBelongOperateRecordList(@RequestParam @ApiParam("订单id") Integer orderId) {
        List<RsOrderBelongOperateRecord> recordList = orderService.getOrderBelongOperateRecordList(orderId);
        return BaseResult.success(recordList);
    }

    @ApiOperation("获取订单归属信息列表")
    @GetMapping("getOrderBelongList")
    public PageResult<List<OrderBelongDto>> getOrderBelongList(
            @RequestParam(required = false) Instant startCreateTime,
            @RequestParam(required = false) Instant endCreateTime,
            @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("订单类型") Integer payType,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size
    ) {
        Page<OrderBelongDto> page = orderService.getOrderBelongList(startCreateTime, endCreateTime, orderNumber, payType,
                PageRequest.of(current - 1, size));
        return PageResult.success(page.getContent(), Pagination.of(current, size, page.getTotalElements()));
    }

    @PostMapping("edit")
    @ApiOperation("订单归属信息表修改")
    public BaseResult<Void> updateOrderBelong(
            @RequestParam Integer orderId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("操作人id") Integer operateId,
            @RequestParam(required = false) @ApiParam("归属主任") Integer headId,
            @RequestParam(required = false) @ApiParam("归属经理") Integer managerId,
            @RequestParam(required = false) @ApiParam("归属总监") Integer directorId
    ) {
        return orderService.updateOrderBelong(orderId, companyType, operateId, headId, managerId, directorId);
    }


    @ApiOperation("获取主订单列表")
    @GetMapping
    public PageResult<List<RsCourseOrder>> getOrderList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("公司类型") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("skuID") Integer skuId,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size,
            @SortDefault(sort = "id", direction = Sort.Direction.DESC) @ApiParam("排序字段") Sort sort) {
        PageRequest pageRequest = PageRequest.of(current - 1, size, sort);
        Page<RsCourseOrder> orderPage = orderService.getOrderList(companyType, userId, orderStatus, enabled, skuId, pageRequest);
        return PageResult.success(orderPage.getContent(), Pagination.of(orderPage));
    }

    @ApiOperation("通过主订单ID获取订单")
    @GetMapping("{id}")
    public BaseResult<RsCourseOrder> getOrder(@PathVariable Integer id) {
        RsCourseOrder order = orderService.getOrder(id).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

    @GetMapping("{orderId}/transfer/flow")
    @ApiOperation("根据订单ID获取转账单审核流程")
    public PageResult<List<TransferOrder>> getOrderTransferFlowList(
            @PathVariable Integer orderId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        Page<TransferOrder> page = transferOrderFlowService.getOrderTransferFlowList(orderId, PageRequest.of(current - 1, size));
        return PageResult.success(page.getContent(), Pagination.of(current, size, page.getTotalElements()));
    }

    @ApiOperation("通过主订单ID获取订单(全部属性)")
    @GetMapping("{id}/full")
    public BaseResult<FullOrder> getFullOrder(@PathVariable Integer id) {
        FullOrder order = orderService.getFullOrder(id).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

    @ApiOperation("按订单id获取订单回访")
    @GetMapping("{id}/feedback")
    public BaseResult<RsOrderFeedback> getOrderFeedback(@PathVariable Integer id) {
        final RsOrderFeedback feedback = orderFeedbackService.getOrderFeedback(id)
                .orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(feedback);
    }

    //sync order
    @ApiOperation("同步订单状态")
    @PostMapping("{id}/sync")
    public BaseResult<Void> syncOrder(@PathVariable Integer id) {
        return orderNotifyService.syncOrder(id);
    }

    @ApiOperation("通过主订单号获取订单")
    @GetMapping("number/{orderNumber}")
    public BaseResult<RsCourseOrder> getOrder(@PathVariable String orderNumber) {
        RsCourseOrder order = orderService.getOrder(orderNumber).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

    @ApiOperation("通过主订单号获取订单")
    @GetMapping("company/number")
    public BaseResult<RsCourseOrder> getOrder(@RequestParam Integer companyType,
                                              @RequestParam String orderNumber) {
        RsCourseOrder order = orderService.getOrder(companyType, orderNumber).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

    @ApiOperation("通过订单号获取订单(批量)")
    @GetMapping("number/batch")
    public BaseResult<List<RsCourseOrder>> getOrderList(@RequestParam List<String> orderNumbers) {
        return BaseResult.success(orderService.getOrderList(orderNumbers));
    }

    @ApiOperation("通过订单Id获取订单(批量)")
    @GetMapping("id/batch")
    public BaseResult<List<RsCourseOrder>> batchGetOrderListById(@RequestParam List<Integer> orderIdList) {
        return BaseResult.success(orderService.batchGetOrderListById(orderIdList));
    }

    @ApiOperation("通过主订单号获取订单(全部属性)")
    @GetMapping("number/{orderNumber}/full")
    public BaseResult<FullOrder> getFullOrder(@PathVariable String orderNumber) {
        FullOrder order = orderService.getFullOrder(orderNumber).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

    @ApiOperation("通过主订单号获取订单(全部属性)")
    @GetMapping("company/number/full")
    public BaseResult<FullOrder> getFullOrderByCompanyNumber(@RequestParam Integer companyType,
                                                             @RequestParam String orderNumber) {
        FullOrder order = orderService.getFullOrderByCompanyNumber(companyType, orderNumber).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

    @ApiOperation("通过子订单流水号获取订单(全部属性)")
    @GetMapping("sub/serialNumber/full")
    public BaseResult<FullOrder> getFullOrderBySerialNumber(@RequestParam String serialNumber) {
        FullOrder order = orderService.getFullOrderBySerialNumber(serialNumber).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

    @PostMapping
    @ApiOperation("创建主订单")
    public BaseResult<RsCourseOrder> createOrder(@RequestBody @Valid CreateOrderReq req) {
        RsCourseOrder order = orderService.createOrder(req);
        return BaseResult.success(order);
    }

    @PostMapping("noAuth")
    @ApiOperation("创建主订单(无需授权)")
    public BaseResult<RsCourseOrder> createOrderNoAuth(@RequestBody @Valid CreateOrderReq req) {
        RsCourseOrder order = orderService.createOrderNoAuth(req);
        return BaseResult.success(order);
    }

    @ApiOperation("获取订单")
    @GetMapping("byFilter")
    public BaseResult<OrderInfoDto> findOrderByFilter(@RequestParam @ApiParam("公司类型") Integer companyType,
                                                      @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
                                                      @RequestParam(required = false) @ApiParam("订单ID") Integer orderId,
                                                      @RequestParam(required = false) @ApiParam("订单状态") OrderStatusEnum status) {
        RsCourseOrder order = orderService.findOneByFilter(companyType, orderId, orderNumber, status);
        OrderInfoDto dto = OrderInfoDto.builder().orderInfo(order).build();
        if (order != null) {
            Optional<RsSku> opSku = skuService.getSku(order.getSkuId());
            opSku.ifPresent(dto::setSkuInfo);
        }
        return BaseResult.success(dto);
    }

    @ApiOperation("获取用户未完成的订单")
    @GetMapping("unfinished")
    public BaseResult<UnFinishedOrderResp> getUserUnFinishedOrderList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("SkuID") Integer skuId,
            @RequestParam @ApiParam("销售Id") Integer salesId) {
        UnFinishedOrderResp orderList = orderService.getUserUnFinishedOrderList(companyType, userId, skuId, salesId);
        return BaseResult.success(orderList);
    }


    /**
     * 根据查询获取相关订单信息
     *
     * @param companyType 公司类型
     * @param userId      用户id
     * @return 订单信息
     */
    @ApiOperation("获取订单信息")
    @GetMapping("findAllByUser")
    public BaseResult<List<RsCourseOrder>> findAllByUser(@RequestParam Integer companyType, @RequestParam Integer userId) {
        List<RsCourseOrder> orderInfo = orderService.findAllByUser(companyType, userId);
        return BaseResult.success(orderInfo);
    }

    /**
     * 根据查询获取相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @ApiOperation("获取员工订单信息")
    @GetMapping("sales")
    public BaseResult<List<SalesOrderInfo>> getSalesOrderInfoList(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam("startTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam("endTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel) {
        if (isAnalyze) {
            return BaseResult.success(orderAnalyst.getSalesOrderInfoList(startTime, endTime, orderLevel));
        } else {
            return BaseResult.success(orderService.getSalesOrderInfoList(startTime, endTime, orderLevel));
        }
    }

    /**
     * 根据查询获取相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @ApiOperation("获取员工订单信息")
    @GetMapping("sales/total")
    public BaseResult<List<SalesOrderInfo>> getSalesOrderInfoTotal(@RequestParam(defaultValue = "false") Boolean isAnalyze, @RequestParam("startTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam("endTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel) {
        if (isAnalyze) {
            return BaseResult.success(orderAnalyst.getSalesOrderInfoTotal(startTime, endTime, orderLevel));
        } else {
            return BaseResult.success(orderService.getSalesOrderInfoTotal(startTime, endTime, orderLevel));
        }
    }

    /**
     * 根据查询获取部门相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @return 订单信息
     */
    @ApiOperation("获取部门订单信息")
    @GetMapping("dept")
    public BaseResult<List<DepartmentOrderInfo>> getDeptOrderInfoList(@RequestParam("startTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam("endTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam(defaultValue = "false") Boolean isAnalyze) {
        List<DepartmentOrderInfo> deptOrderInfoList;
        if (isAnalyze) {
            deptOrderInfoList = departmentOrderService.getDeptOrderInfoList(startTime, endTime, orderLevel);
        } else {
            deptOrderInfoList = deptOrderAnalyst.getDeptOrderInfoList(startTime, endTime, orderLevel);
        }
        return BaseResult.success(deptOrderInfoList);
    }

    /**
     * 根据查询获取部门相关订单信息
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param orderLevel 订单级别
     * @param isAnalyst  是否使用分析型数据库
     * @return 订单信息
     */
    @ApiOperation("获取部门订单信息")
    @GetMapping("dept/total")
    public BaseResult<List<DepartmentOrderInfo>> getDeptOrderInfoTotal(@RequestParam("startTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam("endTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime, @RequestParam Integer orderLevel, @RequestParam Boolean isAnalyst) {
        List<DepartmentOrderInfo> deptOrderInfoTotal;
        if (isAnalyst) {
            deptOrderInfoTotal = deptOrderAnalyst.getDeptOrderInfoTotal(startTime, endTime, orderLevel);
        } else {
            deptOrderInfoTotal = departmentOrderService.getDeptOrderInfoTotal(startTime, endTime, orderLevel);
        }
        return BaseResult.success(deptOrderInfoTotal);
    }

    @ApiOperation("获取员工订单排行榜")
    @GetMapping("user/volume")
    public BaseResult<List<UserOrderVolumeResp>> getUserOrderVolume(
            @RequestBody List<Integer> userIds,
            @RequestParam @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "10") @ApiParam("显示数量") Integer size,
            @RequestParam(defaultValue = "1") @ApiParam("前端后端") Integer category) {
        List<UserOrderVolumeResp> userOrderVolume = orderService.getUserOrderVolume(userIds, startTime, endTime, size, category);
        return BaseResult.success(userOrderVolume);
    }

    /**
     * 商务审核管理列表
     *
     * @param manageReq 查询条件
     * @return 商务审核管理列表分页
     */
    @ApiOperation("获取商务审核管理列表")
    @PostMapping("auditManage")
    public PageResult<List<AuditManageResp>> getAuditManageList(
            @ApiParam @RequestBody(required = false) @Validated AuditManageReq manageReq) {
        Sort.Direction direction = manageReq.getAsc() ? Sort.Direction.ASC : Sort.Direction.DESC;
        if (Objects.isNull(manageReq.getField())) {
            manageReq.setField("payTime");
        }
        Page<AuditManageResp> page = manageService.getAuditManageList(manageReq,
                PageRequest.of(manageReq.getPage(), manageReq.getSize(), Sort.by(direction, manageReq.getField(), "id")));
        Pagination pagination = Pagination.of(manageReq.getCurrent(), page.getSize(), page.getTotalElements());
        return PageResult.success(page.getContent(), pagination);
    }

    @ApiOperation("导出商务审核管理列表")
    @PostMapping("export/auditManage")
    public BaseResult<List<AuditManageResp>> getAuditManageList(
            @ApiParam @RequestBody(required = false) @Validated ExportBusinessAuditReq manageReq) {
        List<AuditManageResp> auditManageList = manageService.getAuditManageList(manageReq);
        return BaseResult.success(auditManageList);
    }

    /**
     * 分配商务
     *
     * @param operatorId
     * @param auditorId
     * @param orderIds
     * @return
     */
    @ApiOperation("分配商务")
    @PatchMapping("assign")
    public BaseResult<Boolean> swAssign(@RequestParam @ApiParam("分配人ID") Integer operatorId,
                                        @RequestParam @ApiParam("商务审核人ID") Integer auditorId,
                                        @RequestParam @ApiParam("订单ID") List<Integer> orderIds) {
        return BaseResult.success(manageService.swAssign(operatorId, auditorId, orderIds));
    }

    @ApiOperation("获取订单相关信息")
    @GetMapping("info/{id}")
    public BaseResult<OrderInfoResp> getOrderInfo(@PathVariable Integer id) {
        OrderInfoResp orderInfo = orderService.getOrderInfo(id);
        return BaseResult.success(orderInfo);
    }

    @ApiOperation("修改订单开票状态")
    @PutMapping("invoice/status")
    public void updateInvoiceStatus(
            @RequestParam(required = false) Integer orderId,
            @RequestParam(required = false) Integer invoiceStatus
    ) {
        orderService.updateInvoiceStatus(orderId, invoiceStatus);
    }

    @ApiOperation("修改订单备注")
    @PutMapping("update/remark")
    public BaseResult<OsErrorCode> updateRemarkStatus(
            @RequestParam Integer orderId,
            @RequestParam String remark) {
        orderService.updateRemark(orderId, remark);
        return BaseResult.success(OsErrorCode.SUCCESS);
    }

    @ApiOperation("修改订单免签")
    @PostMapping("update/avoidSign")
    public BaseResult<OsErrorCode> updateAvoidSign(
            @RequestParam Integer orderId,
            @RequestParam Boolean isAvoidSign) {
        orderService.updateAvoidSign(orderId, isAvoidSign, null);
        return BaseResult.success(OsErrorCode.SUCCESS);
    }

    @ApiOperation("关闭订单")
    @PostMapping("close")
    public BaseResult<OsErrorCode> closeOrder(
            @RequestParam @ApiParam("订单ID") Integer orderId,
            @RequestParam @ApiParam("操作人id") Integer operatorId) {
        Boolean res = orderService.closeOrder(orderId, operatorId);
        if (res) {
            return BaseResult.success(OsErrorCode.SUCCESS);
        } else {
            return BaseResult.success(OsErrorCode.FAILURE);
        }
    }

    @ApiOperation("获取销售订单明细")
    @GetMapping("sales/order/detail")
    public PageResult<List<SalesOrderDetailResp>> getSalesOrderDetail(
            @RequestParam @ApiParam("销售ID") Integer salesId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<SalesOrderDetailResp> page = orderService.getSalesOrderDetail(salesId, flagTime, periodType, orderLevel, dzId, pageRequest);
        return PageResult.success(page.getContent(), Pagination.of(current, size, page.getTotalElements()));
    }

    @ApiOperation("获取部门订单明细")
    @GetMapping("dept/order/detail")
    public PageResult<List<SalesOrderDetailResp>> getDeptOrderDetail(
            @RequestParam @ApiParam("部门ID") Integer deptId,
            @RequestParam(value = "flagTime") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate flagTime,
            @RequestParam @ApiParam("时间类型") Integer periodType,
            @RequestParam(required = false) @ApiParam("订单属性") Integer orderLevel,
            @RequestParam(required = false) @ApiParam("dzid") Long dzId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<SalesOrderDetailResp> page = orderService.getDeptOrderDetail(deptId, flagTime, periodType, orderLevel,
                dzId, pageRequest);
        return PageResult.success(page.getContent(), Pagination.of(current, size, page.getTotalElements()));
    }

    @ApiOperation("统计用户订单数量")
    @GetMapping("{userId}/count")
    public BaseResult<UserStatisticsCount> getOrderCount(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion) {
        UserStatisticsCount statistics = orderService.getOrderCount(userId, orderStatus, isPromotion);
        return BaseResult.success(statistics);
    }

    @ApiOperation("统计用户订单数量")
    @GetMapping("userId/count")
    public BaseResult<UserStatisticsCount> getCompanyOrderCount(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion) {
        UserStatisticsCount statistics = orderService.getCompanyOrderCount(companyType, userId, orderStatus, isPromotion);
        return BaseResult.success(statistics);
    }

    @ApiOperation("统计用户订单数量")
    @PostMapping("count")
    public BaseResult<List<UserStatisticsCount>> getOrderCount(
            @RequestBody @Valid @ApiParam("用户id列表") BatchReq<Integer> req,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isPromotion) {
        List<UserStatisticsCount> statisticsList = orderService.getOrderCount(req.getValues(), orderStatus, isPromotion);
        return BaseResult.success(statisticsList);
    }

    @ApiOperation("商户订单详情")
    @GetMapping("detail")
    public BaseResult<List<OrderExtInfoDto>> getOrderExtInfoDto(
            @RequestParam @ApiParam("订单ID") Integer orderId,
            @RequestParam @ApiParam("退款金额") Integer refundAmount
    ) {
        final Optional<RsCourseOrder> optional = orderService.getOrder(orderId);
        final RsCourseOrder order = optional.orElseThrow(RecordNotExistedException::new);
        if (Objects.equals(order.getPayType(), PayTypeConstant.BANK.getValue())) {
            throw new RecordNotExistedException();
        }
        if (ObjectUtil.isEmpty(order.getIsSplit())) {
            order.setIsSplit(false);
        }
        return orderRefundService.genRefundPolicy(order, refundAmount);
    }

    @ApiOperation("更新订单签字审核状态")
    @PostMapping("sign/audit/status")
    public BaseResult<OsErrorCode> updateSignAuditStatus(
            @RequestParam @ApiParam("订单签字记录ID") Integer orderSignAuditId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus) {
        courseOrderSignService.updateAudit(orderSignAuditId, auditStatus);
        return BaseResult.success(OsErrorCode.SUCCESS);
    }

    @ApiOperation("创建订单签字审核记录")
    @PostMapping("sign/audit/create")
    public BaseResult<OsErrorCode> createSignAudit(
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        RsCourseOrder order = orderService.findOneByFilter(orderId);
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String signNumber = genSignNumber.getSignNumber(order.getCompanyType(), date);
        courseOrderSignService.createOne(orderId, signNumber);
        return BaseResult.success(OsErrorCode.SUCCESS);
    }

    @ApiOperation("根据订单id获取签字信息")
    @PostMapping("getSignInfo")
    public BaseResult<List<RsCourseOrderSign>> getSignByOrderId(
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        List<RsCourseOrderSign> infoList = courseOrderSignService.findOrderSign(orderId);
        return BaseResult.success(infoList);
    }

    @ApiOperation("获取订单和标签关系")
    @PostMapping("getTagRelation")
    public BaseResult<List<RsCourseOrderTagRelation>> findTagByOrder(
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        List<RsCourseOrderTagRelation> resultList = orderService.findTagByOrder(orderId);
        return BaseResult.success(resultList);
    }

    @ApiOperation("商务审核")
    @PostMapping("sw/audit")
    public BaseResult<OrderErrorCode> swAudit(
            @RequestParam @ApiParam("审核人ID") Integer userId,
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        OrderErrorCode orderErrorCode = manageService.swAudit(userId, orderId);
        return BaseResult.of(orderErrorCode);
    }

    @ApiOperation("关闭商务审核")
    @PostMapping("sw/close")
    public BaseResult<OrderErrorCode> closeSwAudit(
            @RequestParam @ApiParam("操作人ID") Integer userId,
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        OrderErrorCode orderErrorCode = manageService.closeSwAudit(userId, orderId);
        return BaseResult.of(orderErrorCode);
    }

    @ApiOperation("订单签字审核信息")
    @GetMapping("sign/audit_info")
    public BaseResult<RsCourseOrderSign> getOrderSignAuditInfo(
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        RsCourseOrderSign result = courseOrderSignService.getOrderSignByOrderId(orderId);
        return BaseResult.success(result);
    }

    @ApiOperation("拉取服务对应用户")
    @PostMapping("course/service")
    public Set<Integer> filterService(@RequestBody @Valid FilterOrderServiceReq req) {
        List<RsCourseService> results = courseServService.filterService(req.getSeriesId(), req.getServiceType(), req.getCustomerIds(), req.getIsExpired());
        return results.stream().map(RsCourseService::getCustomerId).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    @ApiOperation("获取所有订单签字审核信息")
    @GetMapping("sign/all_audit_info")
    public BaseResult<List<RsCourseOrderSign>> getAllOrderSignByOrderId(
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        List<RsCourseOrderSign> result = courseOrderSignService.getAllOrderSignByOrderId(orderId);
        return BaseResult.success(result);
    }

    @ApiOperation("查询主订单列表")
    @GetMapping("list")
    public PageResult<List<OrderInfoDto>> getOrderByFilter(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer orderStatus,
            @RequestParam(required = false) Integer payType,
            @RequestParam(required = false) List<Integer> salesIds,
            @RequestParam(required = false) Boolean isPromotion,
            @RequestParam(required = false) Instant startCreateTime,
            @RequestParam(required = false) Instant endCreateTime,
            @RequestParam(required = false) Instant startPayTime,
            @RequestParam(required = false) Instant endPayTime,
            @RequestParam(required = false) String searchContent,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Integer productLevel,
            @RequestParam(required = false) @ApiParam("sku列表") List<Integer> skuList,
            @RequestParam(required = false) @ApiParam("近60天是否添加高级助教") Boolean isMarkWxUser,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<OrderInfoDto> page = orderService.findAllByFilter(companyType, orderStatus, payType, salesIds,
                isPromotion, startCreateTime, endCreateTime, startPayTime, endPayTime, searchContent, userId, productLevel, skuList,
                isMarkWxUser, pageRequest);
        Pagination pagination = Pagination.of(page.getNumber(), page.getSize(), page.getTotalElements());
        return PageResult.success(page.getContent(), pagination);
    }

    @ApiOperation("查询销售主订单列表")
    @GetMapping("list/sales")
    public PageResult<List<RsCourseOrder>> getOrderBySales(
            @RequestParam Integer companyType,
            @RequestBody @Valid @ApiParam("salesIds") BatchReq<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("是否有相关退款") Boolean isRefund,
            @RequestParam(required = false) Instant startPayTime,
            @RequestParam(required = false) Instant endPayTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<RsCourseOrder> page = orderService.findAllByFilter(companyType, salesIds.getValues(), userId, orderNumber, isRefund, startPayTime, endPayTime,
                pageRequest);
        Pagination pagination = Pagination.of(page.getNumber(), page.getSize(), page.getTotalElements());
        return PageResult.success(page.getContent(), pagination);
    }

    @GetMapping("merchants")
    @ApiOperation("获取商户列表")
    public BaseResult<List<Merchant>> getMerchantList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer payType) {
        List<Merchant> merchants = orderService.getMerchants(companyType, payType);
        return BaseResult.success(merchants);
    }

    @GetMapping("merchants/{id}")
    @ApiOperation("根据MchType获取商户信息")
    public BaseResult<Merchant> getMerchant(@PathVariable Integer id) {
        Merchant merchant = orderService.getMerchant(id).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(merchant);
    }

    @GetMapping("getByWxSku")
    @ApiOperation("根据wxId,skuId查询订单")
    public BaseResult<RsCourseOrder> getOrderByWxSku(
            @RequestParam Integer companyType,
            @RequestParam Integer wxId,
            @RequestParam Integer skuId,
            @RequestParam Integer orderStatus) {
        RsCourseOrder order = orderService.findOneByFilter(companyType, wxId, skuId, orderStatus);
        return BaseResult.success(order);
    }

    @GetMapping("get-by-wx-sku-number")
    @ApiOperation("根据wxId,skuNumber查询订单")
    public BaseResult<RsCourseOrder> getOrderByWxSkuNumber(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Integer wxId,
            @RequestParam(required = false) Integer wxAccountId,
            @RequestParam String skuNumber,
            @RequestParam Integer status,
            @RequestParam Integer days) {
        RsCourseOrder order = orderService.findOneBySkuNumberFilter(companyType, userId, wxId, wxAccountId, skuNumber, status, days);
        return BaseResult.success(order);
    }

    @ApiOperation("获取用户未签字的订单")
    @GetMapping("unsigned")
    public BaseResult<List<RsCourseOrder>> getUserUnSignedOrderList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("sku分类") Integer category) {
        List<RsCourseOrder> orderList = orderService.getUserUnSignedOrderList(companyType, userId, category);
        return BaseResult.success(orderList);
    }

    @ApiOperation("查询已成交的订单")
    @GetMapping("getDealtOrder")
    public RsCourseOrder getDealtOrder(
            @RequestParam @ApiParam("微信ID") Integer wxId) {
        return orderService.getDealtOrder(wxId);
    }

    @ApiOperation("查询订单扩展信息")
    @GetMapping("getOrderExt")
    public RsCourseOrderExt getOrderExt(
            @RequestParam @ApiParam("订单ID") Integer orderId) {
        return orderService.getOrderExt(orderId);
    }

    @ApiOperation("查询用户五分钟内的相同挡位的订单")
    @GetMapping("getUserFinishedOrder")
    public BaseResult<RsCourseOrder> getUserFinishedOrder(
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("SkuID") Integer skuId) {
        RsCourseOrder userFinishedOrder = orderService.getUserFinishedOrder(userId, skuId);
        return BaseResult.success(userFinishedOrder);
    }

    @ApiOperation("按id范围获取订单列表")
    @PostMapping("range")
    public BaseResult<List<FullOrder>> getRangeFullOrderList(
            @RequestParam Integer startId,
            @RequestParam Integer endId,
            @RequestParam(required = false) Integer companyType,
            @RequestParam(required = false) Integer orderStatus) {
        final List<FullOrder> orderList = orderService.getRangeFullOrderList(startId, endId, companyType, orderStatus);
        return BaseResult.success(orderList);
    }

    @ApiOperation("获取第一条订单")
    @GetMapping("first")
    public BaseResult<RsCourseOrder> getFirstOrder() {
        final RsCourseOrder order = orderService.getFirstOrder();
        return BaseResult.success(order);
    }

    @ApiOperation("获取最后一条订单")
    @GetMapping("last")
    public BaseResult<RsCourseOrder> getLastOrder() {
        final RsCourseOrder order = orderService.getLastOrder();
        return BaseResult.success(order);
    }

    @ApiOperation("按用户id批量获取订单")
    @PostMapping("batch/users")
    public BaseResult<List<FullOrder>> getUserOrderList(
            @RequestBody @Valid BatchReq<Integer> req,
            @RequestParam(required = false) Integer orderStatus,
            @RequestParam(required = false) Boolean isPromotion) {
        List<FullOrder> orderList = orderService.getUserOrderList(req.getValues(), orderStatus, isPromotion);
        return BaseResult.success(orderList);
    }

    @ApiOperation("获取用户二档已完成订单")
    @GetMapping("finish")
    public BaseResult<List<FeedbackResp>> getFullOrderListByUserId(
            @RequestParam Integer companyType,
            @RequestParam Integer userId) {
        List<FeedbackResp> fullOrderList = orderService.getFullOrderListByUserId(companyType, userId);
        return BaseResult.success(fullOrderList);
    }

    @ApiOperation("通过sku等级获取订单")
    @GetMapping("by/sku/level")
    public BaseResult<List<RsCourseOrder>> getRsCourseOrderListBySkuLevel(
            @RequestParam @ApiParam("前端后端") Integer category,
            @RequestParam @ApiParam("flagTime") Instant flagTime
    ) {
        List<RsCourseOrder> rsCourseOrderList = orderService.getRsCourseOrderListBySkuLevel(category, flagTime);
        return BaseResult.success(rsCourseOrderList);
    }

    @ApiOperation("获取订单sku列表")
    @GetMapping("orderSku")
    public PageResult<List<OrderSkuInfo>> getOrderSkuPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("是否广告单") Boolean isPromotion,
            @RequestParam(required = false) @ApiParam("sku产品类型") Integer productLevel,
            @RequestParam(required = false) @ApiParam("订单支付开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startDate,
            @RequestParam(required = false) @ApiParam("订单支付结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endDate,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size) {
        PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<OrderSkuInfo> orderPage = orderService.getOrderSkuPage(companyType, userId, orderStatus, isPromotion, productLevel, startDate, endDate, pageRequest);
        return PageResult.success(orderPage.getContent(), Pagination.of(orderPage));
    }

    @ApiOperation("获取订单sku列表不分页")
    @GetMapping("orderSkuList")
    public List<OrderSkuInfo> getOrderSkuList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("是否广告单") Boolean isPromotion) {
        return orderService.getOrderSkuList(companyType, userId, orderStatus, isPromotion);
    }

    @ApiOperation("按订单id批量获取签字信息")
    @PostMapping("batch/orders")
    public BaseResult<List<RsCourseOrderSign>> getSignList(
            @RequestBody @Valid BatchReq<Integer> req) {
        List<RsCourseOrderSign> orderList = courseOrderSignService.getSignInfo(req.getValues());
        return BaseResult.success(orderList);
    }

    @ApiOperation("按订单id获取签字信息")
    @GetMapping("orderSign/orderId")
    public BaseResult<RsCourseOrderSign> getSignInfoByOrderId(
            @RequestParam @ApiParam("订单id") Integer orderId) {
        RsCourseOrderSign courseOrderSign = courseOrderSignService.getSignInfoByOrderId(orderId);
        return BaseResult.success(courseOrderSign);
    }

    @ApiOperation("查询已成交的wx")
    @GetMapping("dealtWx")
    public List<Integer> getDealtWx() {
        return null;
    }

    @GetMapping("feedbackData")
    @ApiOperation("获取回访pdf信息")
    public BaseResult<Map<String, Object>> feedbackData(
            @RequestParam @ApiParam("订单id") Integer orderId) {
        Map<String, Object> dataMap = orderService.getFeedbackData(orderId);
        return BaseResult.success(dataMap);
    }

    @GetMapping("get-feedback-data")
    @ApiOperation("获取回访pdf信息")
    public BaseResult<Map<String, Object>> getFeedbackData(
            @RequestParam @ApiParam("订单id") Integer orderId) {
        Map<String, Object> dataMap = orderService.getFeedbackData2(orderId);
        return BaseResult.success(dataMap);
    }

    @GetMapping("sku/detail")
    @ApiOperation("前端回访问卷-获取订单详情")
    public BaseResult<OrderSkuDetailResp> getOrderSkuDetail(
            @RequestParam @ApiParam("userId") Integer userId,
            @RequestParam @ApiParam("订单编号") String orderNumber
    ) {
        OrderSkuDetailResp orderSkuDetail = orderService.getOrderSkuDetail(userId, orderNumber);
        return BaseResult.success(orderSkuDetail);
    }

    @GetMapping("sku-detail")
    @ApiOperation("前端回访问卷-获取订单详情(新测评)")
    BaseResult<OrderSkuDetailResp> newGetOrderSkuDetail(
            @RequestParam @ApiParam("userId") Integer userId,
            @RequestParam @ApiParam("订单编号") String orderNumber) {
        OrderSkuDetailResp orderSkuDetail = orderService.getNewOrderSkuDetail(userId, orderNumber);
        return BaseResult.success(orderSkuDetail);
    }

    @GetMapping("hg/auditManage")
    @ApiOperation("订单合规审核管理新")
    public PageResult<List<OrderInfoDto>> getOrderHgAuditManage(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Integer auditorId,
            @RequestParam(required = false) List<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startPayTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endPayTime,
            @RequestParam(required = false) Integer feedbackStatus,
            @RequestParam(required = false) Integer productLevel,
            @RequestParam(required = false) List<Integer> auditStatus,
            @RequestParam(required = false) @ApiParam("机审状态") Integer autoAuditStatus,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        final PageRequest pageRequest = PageRequest.of(current - 1, size);
        Page<OrderInfoDto> page = orderService.findAllByFilter(companyType, auditorId, salesIds, startPayTime, endPayTime, feedbackStatus,
                productLevel, auditStatus, autoAuditStatus, searchContent, pageRequest);
        Pagination pagination = Pagination.of(page.getNumber(), page.getSize(), page.getTotalElements());
        return PageResult.success(page.getContent(), pagination);
    }

    @GetMapping("hg/auto-audit-info")
    @ApiOperation("获取自动审核信息")
    public BaseResult<List<RsOrderAutoAuditInfo>> getOrderHgAutoAuditInfo(
            @RequestParam @ApiParam("订单id") Integer orderId) {
        return orderService.getOrderHgAutoAuditInfo(orderId);
    }

    @PostMapping("freeze")
    @ApiOperation("冻结订单")
    public BaseResult<Void> freezeOrder(
            @RequestParam @ApiParam("订单号") String orderNumber,
            @RequestParam @ApiParam("操作人id") Integer operatorId) {
        orderService.checkFreezeOrder(orderNumber);
        RsCourseOrder order = orderService.getOrder(orderNumber).orElseThrow(RecordNotExistedException::new);
        List<CourseSubOrder> subOrders = subOrderService.getSubOrderList(order.getId(), OrderStatusConstant.PAYING.getValue());
        boolean isCloseOrder = paymentService.isCloseOrder(order.getId(), subOrders);
        if (!isCloseOrder) {
            throw new BusinessException(StrUtil.format("冻结订单失败，orderNumber: {}", orderNumber));
        }
        orderService.freezeOrder(orderNumber, operatorId);
        RsSku sku = skuService.getSku(order.getSkuId()).get();
        if (ObjectUtil.equals(sku.getProductLevel(), ProductLevelEnum.STRATEGY.getValue())) {
            strategyOrderLinkDao.updateEnabled(order.getId());
        }
        return BaseResult.successful();
    }

    @PostMapping("sign/no/need")
    @ApiOperation("订单标记无需签字")
    public BaseResult<Void> noNeedSign(
            @RequestParam @ApiParam("订单id") Integer orderId,
            @RequestParam @ApiParam("用户id") Integer userId
    ) {
        RsCourseRefundOrder refunding = refundService.findOneByFilter(orderId, null,
                OrderServiceConst.ORDER_NO_SIGN_REFUND_ORDER_AUDITING_STATUS);
        if (ObjectUtil.isEmpty(refunding)) {
            throw new BusinessException("该订单未发起退款，无法标记");
        }
        RsCourseOrderSign orderSign = courseOrderSignService.getOrderSignByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(orderSign) && Objects.equals(orderSign.getIsSigned(), true)) {
            throw new BusinessException("当前订单已签字");
        }
        orderService.updateAvoidSign(orderId, true, userId);
        RsCourseOrder order = orderService.getOrder(orderId).get();
        RsSku sku = skuService.getSku(order.getSkuId()).get();
        if (ObjectUtil.equals(sku.getProductLevel(), ProductLevelEnum.STRATEGY.getValue())) {
            strategyOrderLinkDao.updateEnabled(orderId);
        }
        return BaseResult.successful();
    }

    @GetMapping("getContractOrderInfo")
    @ApiOperation("获取合同的订单信息")
    public BaseResult<OrderContractDetail> getContractOrderInfo(
            @RequestParam @ApiParam("订单号") String orderNumber
    ) {
        FullOrder fullOrder = orderService.getFullOrder(orderNumber).orElseThrow(RecordNotExistedException::new);
        RsCourseOrder order = fullOrder.getOrder();
        RsSku sku = fullOrder.getSku();
        Set<Integer> userIdSet = new HashSet<>(Collections.singletonList(order.getUserId()));
        BaseResult<Map<Integer, UcUsers>> getUserResult = userClient.batchGetUserMap(BatchReq.create(userIdSet));
        Map<Integer, UcUsers> userMap = getUserResult.isSuccess() ? getUserResult.getData() : Collections.emptyMap();
        Integer amount = commonService.getSignOrderAmount(order.getCouponId(), order.getAmount());
        OrderContractDetail resp = OrderContractDetail.builder()
                .id(order.getId())
                .orderNumber(order.getOrderNumber())
                .userName(userMap.get(order.getUserId()).getUserName())
                .skuName(sku.getShowName())
                .payTime(order.getPayTime())
                .amount(amount)
                .build();
        return BaseResult.success(resp);
    }

    @PostMapping("delivery/create")
    @ApiOperation("创建订单物流信息")
    public BaseResult<Void> createDelivery(@RequestBody @Valid CreateOrderDeliveryReq req) {
        deliveryService.createOrderDelivery(req);
        return BaseResult.successful();
    }

    @PostMapping("delivery/batch")
    @ApiOperation("批量查询订单物流信息")
    public BaseResult<List<RsOrderDeliveryInfo>> batchGetDelivery(@RequestBody @Valid @ApiParam("订单id列表") BatchReq<Integer> req) {
        return deliveryService.batchGetDelivery(req.getValues());
    }

    @GetMapping("sub-order-amount-sum")
    @ApiOperation("获取订单已支付金额")
    BaseResult<Map<Integer, Integer>> geSubOrderAmountSumMap(
            @RequestParam @ApiParam("订单id") List<Integer> orderIds
    ) {
        return BaseResult.success(orderService.geSubOrderAmountSumMap(orderIds));
    }

    @GetMapping("get-order-by-wx-user")
    @ApiOperation("根据wxId,userId查询订单")
    public BaseResult<List<RsCourseOrder>> getOrderByWxIdAndUserId(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId,
            @RequestParam(required = false) @ApiParam("订单状态") List<Integer> status) {
        return orderService.getOrderByWxIdAndUserId(companyType, userId, wxId, status);
    }


    @GetMapping("mark-record-list")
    @ApiOperation("查询标记2.0销售订单列表")
    public PageResult<List<RsCourseOrderExt>> getMarkRecordList(
            @RequestParam(required = false) @ApiParam("标记开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startMarkDate,
            @RequestParam(required = false) @ApiParam("标记结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endMarkDate,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchText,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size) {
        return orderService.getMarkRecordList(startMarkDate, endMarkDate, searchText, current, size);
    }

    @ApiOperation("湖仓查询销售主订单列表")
    @PostMapping("sales-order-list")
    public PageResult<List<RsCourseOrder>> getCourseOrderSalesByFilter(
            @RequestParam Integer companyType,
            @RequestBody @Valid @ApiParam("salesIds") BatchReq<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("是否有相关退款") Boolean isRefund,
            @RequestParam(required = false) @ApiParam("支付开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startPayTime,
            @RequestParam(required = false) @ApiParam("支付结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endPayTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return rsCourseOrderLakehouseDao.getCourseOrderByFilter(companyType, salesIds.getValues(), userId, orderNumber, isRefund, startPayTime, endPayTime, current, size);
    }

}
package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.TransferOrderFlow;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TransferOrderFlowRepository extends JpaRepository<TransferOrderFlow, Long>,
        QuerydslPredicateExecutor<TransferOrderFlow> {

    Optional<TransferOrderFlow> findBySubOrderId(Integer subOrderId);
}

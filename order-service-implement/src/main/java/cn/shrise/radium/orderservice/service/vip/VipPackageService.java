package cn.shrise.radium.orderservice.service.vip;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import cn.shrise.radium.imservice.entity.ImLiveRoom;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.properties.vip.*;
import cn.shrise.radium.orderservice.service.serving.ArticleSeriesService;
import cn.shrise.radium.orderservice.service.serving.CourseSeriesService;
import cn.shrise.radium.tradeservice.TradeClient;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class VipPackageService {

    private final MultiVipProperties multiVipProperties;
    private final VipSubscriptionService vipSubscriptionService;
    private final ArticleSeriesService articleSeriesService;
    private final CourseSeriesService courseSeriesService;
    private final ContentClient contentClient;
    private final TradeClient tradeClient;
    private final ImClient imClient;

    public Optional<VipProperties> getVipProperties(Integer companyType) {
        return multiVipProperties.getMultiConfigs().stream()
                .filter(e -> Objects.equals(companyType, e.getCompanyType()))
                .findFirst();
    }

    public Optional<VipPackage> getVipPackage(Integer companyType, String number) {
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);
        List<VipPackage> packageList = vipProperties.getPackages();
        if (ObjectUtils.isEmpty(packageList)) {
            return Optional.empty();
        }
        return packageList.stream()
                .filter(e -> Objects.equals(e.getNumber(), number))
                .findFirst();
    }

    public List<VipPackage> getVipPackage(Integer companyType, Collection<String> numberList) {
        if (ObjectUtils.isEmpty(numberList)) {
            return Collections.emptyList();
        }

        List<VipPackage> packageList = getVipPackageList(companyType);
        Set<String> numberSet = new HashSet<>(numberList);
        return packageList.stream()
                .filter(e -> numberSet.contains(e.getNumber()))
                .collect(Collectors.toList());
    }

    public List<VipPackage> getVipPackageList(Integer companyType) {
        VipProperties vipProperties = getVipProperties(companyType)
                .orElseThrow(RecordNotExistedException::new);
        return vipProperties.getPackages();
    }

    public List<VipPackage> getVipPackageList(Integer companyType, Integer level) {
        List<VipPackage> packageList = getVipPackageList(companyType);
        if (ObjectUtils.isEmpty(level)) {
            return packageList;
        }

        if (ObjectUtils.isEmpty(packageList)) {
            return Collections.emptyList();
        }
        return packageList.stream()
                .filter(e -> Objects.equals(e.getLevel(), level))
                .collect(Collectors.toList());
    }

    public List<VipPackage> getVipPackageByNumbers(Integer companyType, Collection<String> numbers) {
        List<VipPackage> packageList = getVipPackageList(companyType);

        if (ObjectUtils.isEmpty(packageList)) {
            return Collections.emptyList();
        }
        return packageList.stream()
                .filter(e -> numbers.contains(e.getNumber()))
                .collect(Collectors.toList());
    }

    public Map<String, Map<String, Integer>> getVipPackageNoPermissionConfig(Integer companyType) {
        VipProperties vipProperties = getVipProperties(companyType)
                .orElseThrow(RecordNotExistedException::new);
        return vipProperties.getNoPermission();
    }

    public List<String> getVipPackageServiceNumberList(Integer companyType, String number, String type) {
        VipPackage vipPackage = getVipPackage(companyType, number).orElse(null);
        if (ObjectUtils.isEmpty(vipPackage)) {
            return Collections.emptyList();
        }

        List<String> serviceNumbers = new ArrayList<>();
        VipPackageServiceConfig config = vipPackage.getServices();
        if (Objects.equals(type, ARTICLE)) {
            //付费文章
            serviceNumbers.addAll(config.getArticle());
        } else if (Objects.equals(type, COURSE)) {
            //付费课程
            serviceNumbers.addAll(config.getCourse());
        } else if (Objects.equals(type, ROOM)) {
            //直播室
            serviceNumbers.addAll(config.getRoom());
        } else if (Objects.equals(type, STREAM)) {
            //直播流
            serviceNumbers.addAll(config.getStream());
        } else if (Objects.equals(type, PORTFILIO)) {
            //实战跟投
            serviceNumbers.addAll(config.getPortfolio());
        } else if (Objects.equals(type, STOCK_POOL)) {
            //股票池
            serviceNumbers.addAll(config.getStockPool());
        }
        return serviceNumbers;
    }

    public Optional<VipPackageServiceConfig> getVipPackageServiceConfig(Integer companyType, String number) {
        VipPackage vipPackage = getVipPackage(companyType, number).orElse(null);
        if (vipPackage == null) {
            return Optional.empty();
        }
        return Optional.of(vipPackage.getServices());
    }

    /**
     * 获取用户的服务包
     */
    public UserVipPackageInfo getUserVipPackage(Integer companyType, Integer userId, String number) {
        VipSubscription subscription = vipSubscriptionService.getUserVipSubscription(userId, number, false)
                .orElseThrow(RecordNotExistedException::new);

        VipProperties vipProperties = getVipProperties(companyType)
                .orElseThrow(RecordNotExistedException::new);

        VipPackage vipPackage = vipProperties.getPackages().stream()
                .filter(e -> Objects.equals(number, e.getNumber()))
                .findFirst()
                .orElseThrow(RecordNotExistedException::new);

        final VipPackageServiceConfig services = vipPackage.getServices();
        List<VipPackageStyle> styles = vipPackage.getStyles();
        if (ObjectUtils.isEmpty(styles)) {
            styles = new ArrayList<>();
        }
        List<VipPackageBanner> banners = vipPackage.getBanners();

        List<ServiceIntroduction> serviceIntroductionList = getServiceIntroductionList(companyType, services);

        List<VipPackageServiceItem> items = serviceIntroductionList.stream()
                .map(VipPackageServiceItem::of)
                .collect(Collectors.toList());

        List<VipPackageServiceItem> serviceList = decorateStyle(companyType, items, styles);

        return UserVipPackageInfo.builder()
                .subscriptionId(subscription.getId())
                .name(vipPackage.getName())
                .level(subscription.getLevel())
                .number(subscription.getNumber())
                .openTime(subscription.getOpenTime())
                .expireTime(subscription.getExpireTime())
                .services(serviceList)
                .banners(banners)
                .build();
    }

    /**
     * 获取用户所有的服务包
     */
    public BaseResult<List<UserVipPackageInfo>> getSubscriptionsInfoList(Integer companyType, Integer userId) {
        List<VipSubscription> subscriptionList = vipSubscriptionService.getUserAllVipSubscription(userId)
                .orElseThrow(RecordNotExistedException::new);

        List<UserVipPackageInfo> resultList = subscriptionList.stream().map(
                subscription -> {
                    VipProperties vipProperties = getVipProperties(companyType)
                            .orElseThrow(RecordNotExistedException::new);

                    VipPackage vipPackage = vipProperties.getPackages().stream()
                            .filter(e -> Objects.equals(subscription.getNumber(), e.getNumber()))
                            .findFirst()
                            .orElse(null);
                    if(vipPackage == null) {
                        return null;
                    }

                    final VipPackageServiceConfig services = vipPackage.getServices();

                    List<ServiceIntroduction> serviceIntroductionList = getServiceIntroductionList(companyType, services);

                    List<VipPackageServiceItem> items = serviceIntroductionList.stream()
                            .filter(e -> !"stockPool".equals(e.getType())) // 过滤掉type为stockPool
                            .map(e -> {
                                VipPackageServiceItem item = VipPackageServiceItem.of(e);
                                if ("index".equals(e.getType())) {
                                    item.setName(e.getServiceName()); // type为index,取ServiceIntroduction的serviceName
                                }
                                return item;
                            })
                            // 按 type 分组
                            .collect(Collectors.groupingBy(VipPackageServiceItem::getType))
                            .entrySet()
                            .stream()
                            .map(entry -> {
                                String type = entry.getKey();
                                List<VipPackageServiceItem> groupItems = entry.getValue();
                                String mergedName = groupItems.stream()
                                        .map(VipPackageServiceItem::getName)
                                        .filter(StrUtil::isNotBlank)
                                        .distinct()
                                        .collect(Collectors.joining("，"));
                                // 只保留合并后的 name 和 type
                                VipPackageServiceItem merged = new VipPackageServiceItem();
                                merged.setType(type);
                                merged.setName(mergedName);
                                return merged;
                            })
                            .collect(Collectors.toList());
                    return UserVipPackageInfo.builder()
                            .subscriptionId(subscription.getId())
                            .name(vipPackage.getName())
                            .level(subscription.getLevel())
                            .number(subscription.getNumber())
                            .openTime(subscription.getOpenTime())
                            .expireTime(subscription.getExpireTime())
                            .services(items)
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return BaseResult.success(resultList);
    }

    public List<ServiceIntroduction> getServiceIntroductionList(Integer companyType, VipPackageServiceConfig configService) {

        if (Objects.isNull(configService)) {
            return Collections.emptyList();
        }

        List<ServiceIntroduction> serviceIntroductionList = new ArrayList<>();
        //付费文章
        List<String> articleServiceNumbers = configService.getArticle();
        List<ServiceIntroduction> articleServiceIntroductionList = articleSeriesService.getArticleServiceIntroductionList(companyType, articleServiceNumbers, true);
        serviceIntroductionList.addAll(articleServiceIntroductionList);

        //付费课程
        List<String> courseServiceNumbers = configService.getCourse();
        List<ServiceIntroduction> courseServiceIntroductionList = courseSeriesService.getCourseServiceIntroductionList(companyType, courseServiceNumbers);
        serviceIntroductionList.addAll(courseServiceIntroductionList);

        //直播室
        List<String> roomNumbers = configService.getRoom();
//        List<ServiceIntroduction> roomServiceIntroductionList = contentClient.getLiveRoomServiceIntroductionList(companyType, BatchReq.of(roomNumbers), true)
//                .orElse(Collections.emptyList());
        List<ServiceIntroduction> newRoomServiceIntroductionList = imClient.getLiveRoomServiceIntroductionList(companyType, BatchReq.of(roomNumbers), true)
                .orElse(Collections.emptyList());
//        serviceIntroductionList.addAll(roomServiceIntroductionList);
        serviceIntroductionList.addAll(newRoomServiceIntroductionList);

        //直播流
        List<String> streamNumbers = configService.getStream();
        List<ServiceIntroduction> streamServiceIntroductionList = contentClient.getStreamChannelServiceIntroductionList(companyType, BatchReq.of(streamNumbers))
                .orElse(Collections.emptyList());
        serviceIntroductionList.addAll(streamServiceIntroductionList);

        //实战跟投
        List<String> portfolioNumbers = configService.getPortfolio();
        List<ServiceIntroduction> portfolioServiceIntroductionList = tradeClient.getPortfolioServiceIntroductionList(companyType, BatchReq.of(portfolioNumbers))
                .orElse(Collections.emptyList());
        serviceIntroductionList.addAll(portfolioServiceIntroductionList);

        //股票池
        List<String> stockPool = configService.getStockPool();
        List<ServiceIntroduction> stockPoolServiceIntroductionList = tradeClient.getRecommendStockServiceIntroductionList(companyType, BatchReq.of(stockPool))
                .orElse(Collections.emptyList());
        serviceIntroductionList.addAll(stockPoolServiceIntroductionList);

        //聊天室
        List<String> chatroom = configService.getChatroom();
        List<ServiceIntroduction> chatRoomServiceIntroductionList = imClient.getChatRoomServiceIntroductionList(companyType, BatchReq.of(chatroom), true).orElse(Collections.emptyList());
        serviceIntroductionList.addAll(chatRoomServiceIntroductionList);

        //指标
        List<String> index = configService.getIndex();
        List<ServiceIntroduction> indexServiceIntroductionList = getIndexServiceIntroductionList(companyType, index);
        serviceIntroductionList.addAll(indexServiceIntroductionList);

        //股池
        List<String> poolCase = configService.getPoolCase();
        List<ServiceIntroduction> poolCaseServiceIntroductionList = tradeClient.getPoolCaseServiceIntroductionList(companyType, BatchReq.of(poolCase)).orElse(Collections.emptyList());
        serviceIntroductionList.addAll(poolCaseServiceIntroductionList);

        return serviceIntroductionList;
    }

    public List<ServiceIntroduction> getVipPackageServiceIntroductionList(Integer companyType, String number, String type, Boolean isFree) {
        VipProperties vipProperties = getVipProperties(companyType)
                .orElseThrow(RecordNotExistedException::new);

        VipPackageServiceConfig configService = null;

        if (Objects.equals(isFree, false)) {
            VipPackage vipPackage = vipProperties.getPackages().stream()
                    .filter(e -> Objects.equals(number, e.getNumber()))
                    .findFirst()
                    .orElse(null);

            if (Objects.isNull(vipPackage)) {
                return Collections.emptyList();
            }

            configService = vipPackage.getServices();

        } else if (Objects.equals(isFree, true)) {
            configService = vipProperties.getFree();
        }

        if (Objects.isNull(configService)) {
            return Collections.emptyList();
        }

        List<ServiceIntroduction> serviceIntroductionList = new ArrayList<>();
        if (Objects.equals(type, ARTICLE)) {
            //付费文章
            List<String> articleServiceNumbers = configService.getArticle();
            List<ServiceIntroduction> articleServiceIntroductionList = articleSeriesService.getArticleServiceIntroductionList(companyType, articleServiceNumbers, true);
            serviceIntroductionList.addAll(articleServiceIntroductionList);
        } else if (Objects.equals(type, COURSE)) {
            //付费课程
            List<String> courseServiceNumbers = configService.getCourse();
            List<ServiceIntroduction> courseServiceIntroductionList = courseSeriesService.getCourseServiceIntroductionList(companyType, courseServiceNumbers);
            serviceIntroductionList.addAll(courseServiceIntroductionList);
        } else if (Objects.equals(type, ROOM)) {
            //直播室
            List<String> roomNumbers = configService.getRoom();
            List<ServiceIntroduction> roomServiceIntroductionList = contentClient.getLiveRoomServiceIntroductionList(companyType, BatchReq.of(roomNumbers), true)
                    .orElse(Collections.emptyList());
            List<ServiceIntroduction> imServiceIntroductionList = imClient.getLiveRoomServiceIntroductionList(companyType, BatchReq.of(roomNumbers), true)
                    .orElse(Collections.emptyList());
            serviceIntroductionList.addAll(roomServiceIntroductionList);
            serviceIntroductionList.addAll(imServiceIntroductionList);
        } else if (Objects.equals(type, STREAM)) {
            //直播流
            List<String> streamNumbers = configService.getStream();
            List<ServiceIntroduction> streamServiceIntroductionList = contentClient.getStreamChannelServiceIntroductionList(companyType, BatchReq.of(streamNumbers)).orElse(Collections.emptyList());
            serviceIntroductionList.addAll(streamServiceIntroductionList);
        } else if (Objects.equals(type, PORTFILIO)) {
            //实战跟投
            List<String> portfolioNumbers = configService.getPortfolio();
            List<ServiceIntroduction> portfolioServiceIntroductionList = tradeClient.getPortfolioServiceIntroductionList(companyType, BatchReq.of(portfolioNumbers)).orElse(Collections.emptyList());
            serviceIntroductionList.addAll(portfolioServiceIntroductionList);
        } else if (Objects.equals(type, STOCK_POOL)) {
            //股票池
            List<String> stockPool = configService.getStockPool();
            List<ServiceIntroduction> stockPoolServiceIntroductionList = tradeClient.getRecommendStockServiceIntroductionList(companyType, BatchReq.of(stockPool)).orElse(Collections.emptyList());
            serviceIntroductionList.addAll(stockPoolServiceIntroductionList);
        } else if (Objects.equals(type, CHAT_ROOM)) {
            //聊天室
            List<String> chatroom = configService.getChatroom();
            List<ServiceIntroduction> chatRoomServiceIntroductionList = imClient.getChatRoomServiceIntroductionList(companyType, BatchReq.of(chatroom), true).orElse(Collections.emptyList());
            serviceIntroductionList.addAll(chatRoomServiceIntroductionList);
        } else if (Objects.equals(type, INDEX)) {
            //指标
            List<String> index = configService.getIndex();
            List<ServiceIntroduction> indexServiceIntroductionList = getIndexServiceIntroductionList(companyType, index);
            serviceIntroductionList.addAll(indexServiceIntroductionList);
        } else if (Objects.equals(type, POOL_CASE)) {
            //股池
            List<String> poolCase = configService.getPoolCase();
            List<ServiceIntroduction> poolCaseServiceIntroductionList = tradeClient.getPoolCaseServiceIntroductionList(companyType, BatchReq.of(poolCase)).orElse(Collections.emptyList());
            serviceIntroductionList.addAll(poolCaseServiceIntroductionList);
        }

        return serviceIntroductionList;
    }

    private List<VipPackageServiceItem> decorateStyle(Integer companyType, List<VipPackageServiceItem> items, List<VipPackageStyle> styles) {
        if (ObjectUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        List<ImLiveRoom> liveRoomList = imClient.filterLiveRoomList(companyType, null, null, null, true).orElse(Collections.emptyList());
        Map<Long, ImLiveRoom> newLiveRoomMap = liveRoomList.stream().collect(Collectors.toMap(ImLiveRoom::getId, r -> r));

        List<ImChatRoom> chatRoomList = imClient.filterChatRoomList(companyType, null, null, null, null).orElse(Collections.emptyList());
        Map<Long, ImChatRoom> chatRoomMap = chatRoomList.stream().collect(Collectors.toMap(ImChatRoom::getId, r -> r));
        String keyTemplate = "%s_%s";
        Map<String, VipPackageStyle> styleMap = styles.stream()
                .collect(Collectors.toMap(e -> String.format(keyTemplate, e.getType(), e.getNumber()), Function.identity()));

        return items.stream().map(e -> {
            String key = String.format(keyTemplate, e.getType(), e.getNumber());
            VipPackageServiceItem newItem = new VipPackageServiceItem();
            BeanUtils.copyProperties(e, newItem);
            if (!Objects.equals(e.getType(), COURSE)) {
                VipPackageStyle packageStyle = styleMap.get(key);
                newItem.setStyle(packageStyle);
            }
            if (Objects.equals(e.getType(), CHAT_ROOM) && chatRoomMap.containsKey(e.getServiceId())) {
                ImChatRoom chatRoom = chatRoomMap.get(e.getServiceId());
                VipPackageStyle packageStyle = VipPackageStyle.builder()
                        .number(e.getNumber())
                        .type(e.getType())
                        .tags(Collections.singletonList(chatRoom.getVipTag()))
                        .title(chatRoom.getVipBrief())
                        .cover(chatRoom.getVipBannerUrl())
                        .tagColor(chatRoom.getVipTagColor())
                        .build();
                if (ObjectUtil.isEmpty(chatRoom.getVipTag())) {
                    packageStyle.setTags(Collections.emptyList());
                }
                newItem.setStyle(packageStyle);
            }
            if (Objects.equals(e.getType(), ROOM) && newLiveRoomMap.containsKey(e.getServiceId())) {
                ImLiveRoom liveRoom = newLiveRoomMap.get(e.getServiceId());
                VipPackageStyle packageStyle = VipPackageStyle.builder()
                        .number(e.getNumber())
                        .type(e.getType())
                        .tags(Collections.singletonList(liveRoom.getVipTag()))
                        .title(liveRoom.getVipBrief())
                        .cover(liveRoom.getVipBannerUrl())
                        .tagColor(liveRoom.getVipTagColor())
                        .pcVipTitleImgUrl(liveRoom.getPcVipTitleImgUrl())
                        .build();
                if (ObjectUtil.isNotEmpty(styleMap.get(key))) {
                    packageStyle.setHot(styleMap.get(key).getHot());
                }
                if (ObjectUtil.isEmpty(liveRoom.getVipTag())) {
                    packageStyle.setTags(Collections.emptyList());
                }
                newItem.setStyle(packageStyle);
            }
            return newItem;
        }).collect(Collectors.toList());
    }

    public List<UserSubscriptionServiceItem> getUserVipSubscriptionServiceList(Integer companyType, Integer userId, String type) {
        List<VipSubscription> subscriptionList = vipSubscriptionService.getUserVipSubscriptionList(userId);
        return subscriptionList.stream().map(e -> {
                    List<ServiceIntroduction> services = getVipPackageServiceIntroductionList(companyType, e.getNumber(), type, false);
                    return services.stream().map(s -> UserSubscriptionServiceItem.builder()
                                    .id(e.getId())
                                    .number(e.getNumber())
                                    .level(e.getLevel())
                                    .expireTime(e.getExpireTime())
                                    .openTime(e.getOpenTime())
                                    .service(s)
                                    .build())
                            .collect(Collectors.toList());
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    public List<ServiceIntroduction> getUserVipSubscriptionColumnList(Integer companyType, Integer userId, String type) {
        List<VipSubscription> subscriptionList = vipSubscriptionService.getUserVipSubscriptionList(userId);
        return subscriptionList.stream().map(e -> getVipPackageServiceIntroductionList(companyType, e.getNumber(), type, false))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取vip包中频道编号对应的服务包编号
     *
     * @param companyType     公司类型
     * @param portfolioNumber 频道编号
     * @return 服务包编号
     */
    public Set<String> getVipPackageNumber(Integer companyType, String portfolioNumber) {
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);
        List<VipPackage> packageList = vipProperties.getPackages();
        if (ObjectUtils.isEmpty(packageList)) {
            return null;
        }
        return packageList.stream()
                .filter(x -> ObjectUtil.isNotNull(x.getServices()))
                .filter(x -> ObjectUtil.isNotNull(x.getServices().getPortfolio()))
                .filter(x -> x.getServices().getPortfolio().contains(portfolioNumber))
                .map(VipPackage::getNumber)
                .collect(Collectors.toSet());
    }

    public Set<String> getChatVipPackageNumber(Integer companyType, String chatRoomNumber) {
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);
        List<VipPackage> packageList = vipProperties.getPackages();
        if (ObjectUtils.isEmpty(packageList)) {
            return null;
        }
        return packageList.stream()
                .filter(x -> ObjectUtil.isNotNull(x.getServices()))
                .filter(x -> ObjectUtil.isNotNull(x.getServices().getChatroom()))
                .filter(x -> x.getServices().getChatroom().contains(chatRoomNumber))
                .map(VipPackage::getNumber)
                .collect(Collectors.toSet());
    }

    public List<String> getRoomVipPackageNumber(Integer companyType, String roomNumber) {
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);
        List<VipPackage> packageList = vipProperties.getPackages();
        if (ObjectUtils.isEmpty(packageList)) {
            return null;
        }
        return packageList.stream()
                .filter(x -> ObjectUtil.isNotNull(x.getServices()))
                .filter(x -> ObjectUtil.isNotNull(x.getServices().getRoom()))
                .filter(x -> x.getServices().getRoom().contains(roomNumber))
                .map(VipPackage::getNumber)
                .collect(Collectors.toList());
    }

    public List<String> getPoolCaseVipPackageNumber(Integer companyType, String poolCaseNumber) {
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);
        List<VipPackage> packageList = vipProperties.getPackages();
        if (ObjectUtils.isEmpty(packageList)) {
            return null;
        }
        return packageList.stream()
                .filter(x -> ObjectUtil.isNotNull(x.getServices()))
                .filter(x -> ObjectUtil.isNotNull(x.getServices().getPoolCase()))
                .filter(x -> x.getServices().getPoolCase().contains(poolCaseNumber))
                .map(VipPackage::getNumber)
                .collect(Collectors.toList());
    }

    public List<VipPackageBanner> getVipPackageBannerList(Integer companyType, String number) {
        final VipPackage vipPackage = getVipPackage(companyType, number).orElseThrow(RecordNotExistedException::new);
        return vipPackage.getBanners();
    }

    /**
     * 获取免费服务
     */
    public List<UserSubscriptionServiceItem> getFreeSubscriptionServiceList(Integer companyType, String type) {
        List<ServiceIntroduction> services = getVipPackageServiceIntroductionList(companyType, null, type, true);
        return services.stream().map(s -> UserSubscriptionServiceItem.builder()
                        .service(s)
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 获取用户拥有的服务包配置
     */
    public List<VipPackage> getUserVipPackageConfig(Integer companyType, Integer userId) {
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);
        List<VipPackage> packageList = vipProperties.getPackages();
        if (ObjectUtils.isEmpty(packageList)) {
            return Collections.emptyList();
        }
        List<VipSubscription> subscriptionList = vipSubscriptionService.getUserVipSubscriptionList(userId, false);
        List<String> numberList = subscriptionList.stream().map(VipSubscription::getNumber).collect(Collectors.toList());
        List<VipPackage> resp = packageList.stream().filter(e -> numberList.contains(e.getNumber())).collect(Collectors.toList());
        return resp;
    }

    public List<ServiceIntroduction> getIndexServiceIntroductionList(Integer companyType, Collection<String> numbers) {
        if (ObjectUtils.isEmpty(numbers)) {
            return Collections.emptyList();
        }
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);
        Map<String, VipColumn> indexMap = vipProperties.getColumns().stream()
                .filter(e -> Objects.equals(e.getType(), INDEX))
                .collect(Collectors.toMap(VipColumn::getNumber, Function.identity()));
        return numbers.stream().map(e -> {
            VipColumn vipColumn = indexMap.get(e);
            if (vipColumn == null) {
                return null;
            }
            return ServiceIntroduction.builder()
                    .serviceName(vipColumn.getName())
                    .number(vipColumn.getNumber())
                    .type(vipColumn.getType())
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<VipColumn> getVipColumnList(Integer companyType, String type) {
        VipProperties vipProperties = getVipProperties(companyType).orElseThrow(RecordNotExistedException::new);

        if (ObjectUtils.isEmpty(type)) {
            return vipProperties.getColumns();
        }
        return vipProperties.getColumns().stream()
                .filter(e -> Objects.equals(e.getType(), type))
                .collect(Collectors.toList());
    }

}

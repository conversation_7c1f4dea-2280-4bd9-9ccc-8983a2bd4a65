package cn.shrise.radium.orderservice.consumer.order;

import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.orderservice.req.OrderNotifyReq;
import cn.shrise.radium.orderservice.service.order.OrderNotifyService;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static cn.shrise.radium.orderservice.constant.OrderRocketMqNameConst.*;
import static cn.shrise.radium.orderservice.constant.OrderRocketMqNameConst.MqGroupType.GROUP_PAY_ORDER_NOTIFY;
import static cn.shrise.radium.orderservice.constant.OrderRocketMqNameConst.TagType.TAG_PAY_ORDER_NOTIFY;

/**
 * 已支付订单回调
 */
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = TOPIC_PAY_NOTIFY, consumerGroup = GROUP_PAY_ORDER_NOTIFY, selectorExpression = TAG_PAY_ORDER_NOTIFY)
public class OrderNotifyConsumer implements MessageListener {

    private final OrderNotifyService orderNotifyService;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        OrderNotifyReq notifyReq = JSON.parseObject(message.getBody(), OrderNotifyReq.class);
        log.info("OrderNotifyReq: {}", notifyReq);
        orderNotifyService.handleOrderNotify(notifyReq);
        return Action.CommitMessage;
    }
}

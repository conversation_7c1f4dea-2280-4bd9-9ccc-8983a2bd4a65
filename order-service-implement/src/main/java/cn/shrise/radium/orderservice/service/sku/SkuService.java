package cn.shrise.radium.orderservice.service.sku;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.orderservice.constant.BuyLimitConstant;
import cn.shrise.radium.orderservice.constant.ResultConstant;
import cn.shrise.radium.orderservice.constant.SkuActivityStatusConstant;
import cn.shrise.radium.orderservice.constant.SkuProductLevelConstant;
import cn.shrise.radium.orderservice.dto.SkuDto;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.properties.SkuProperties;
import cn.shrise.radium.orderservice.repository.RsSkuRepository;
import cn.shrise.radium.orderservice.repository.SkuChangeRecordRepository;
import cn.shrise.radium.orderservice.repository.SkuMaterialRelationRepository;
import cn.shrise.radium.orderservice.repository.SkuStrategyRepository;
import cn.shrise.radium.orderservice.req.*;
import cn.shrise.radium.orderservice.resp.SkuAndMaterialResp;
import cn.shrise.radium.orderservice.resp.SkuMaterialItem;
import cn.shrise.radium.orderservice.resp.SkuStrategy;
import cn.shrise.radium.orderservice.service.CourseServService;
import cn.shrise.radium.orderservice.service.vip.VipSubscriptionService;
import cn.shrise.radium.orderservice.util.ProjectionUtils;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import com.alibaba.fastjson.JSONObject;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.PagedList;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.BuyLimitConstant.NONE_LIMIT;
import static cn.shrise.radium.orderservice.constant.OrderErrorCode.SKU_TIMEOUT;
import static cn.shrise.radium.orderservice.constant.ResultConstant.*;
import static cn.shrise.radium.orderservice.constant.SkuChangeTypeConstant.*;
import static cn.shrise.radium.orderservice.constant.SkuMaterialPositionConstant.*;
import static cn.shrise.radium.orderservice.constant.SkuStatusConstant.*;

/**
 * service for entity Sku
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SkuService {

    private final RsSkuRepository skuRepository;
    private final JdbcTemplate jdbcTemplate;
    private final QRsSku qRsSku = QRsSku.rsSku;
    private final QRsSkuUserRelation qRsSkuUserRelation = QRsSkuUserRelation.rsSkuUserRelation;

    private final QSkuDeptRelation skuDeptRelation = QSkuDeptRelation.skuDeptRelation;

    private final QRsSkuActivity skuActivity = QRsSkuActivity.rsSkuActivity;

    private final QRsSkuStrategy skuStrategy = QRsSkuStrategy.rsSkuStrategy;

    private final SkuMaterialRelationRepository skuMaterialRelationRepository;

    private final SkuChangeRecordRepository skuChangeRecordRepository;

    private final JPAQueryFactory jpaQueryPrimary;

    private final SkuMaterialService skuMaterialService;

    private final RocketMqUtils rocketMqUtils;

    private final SkuStrategyRepository skuStrategyRepository;

    @PersistenceContext(unitName = "primaryPersistenceUnit")
    private EntityManager primaryEntityManager;
    private final CriteriaBuilderFactory primaryCriteriaBuilderFactory;
    private final CourseServService courseServService;
    private final SkuProperties skuProperties;
    private final UserClient userClient;
    private final VipSubscriptionService vipSubscriptionService;
    private final SkuVipSubscriptionService skuVipSubscriptionService;

    private final QRsSkuStrategy qRsSkuStrategy = QRsSkuStrategy.rsSkuStrategy;

    public Optional<RsSku> getSku(Integer id) {
        return skuRepository.findById(id);
    }

    public Optional<RsSku> getSku(Integer companyType, String skuNumber) {
        return skuRepository.findByCompanyTypeAndNumber(companyType, skuNumber);
    }

    @Transactional
    public RsSku createSku(CreateSkuReq req) {
        RsSku sku = RsSku.builder()
                .companyType(req.getCompanyType())
                .number(req.getNumber())
                .name(req.getName())
                .showName(req.getShowName())
                .showTitle(req.getShowTitle())
                .description(req.getDescription())
                .productLevel(req.getProductLevel())
                .price(req.getPrice())
                .creatorId(req.getCreatorId())
                .enabled(req.getEnabled())
                .isOnline(req.getIsOnline())
                .isRenew(req.getIsRenew())
                .startTime(req.getStartTime())
                .endTime(req.getEndTime())
                .category(req.getCategory())
                .build();
        return skuRepository.save(sku);
    }

    @Transactional
    public void deleteSku(Integer id) {
        jpaQueryPrimary.update(qRsSku)
                .set(qRsSku.enabled, false)
                .where(qRsSku.id.eq(id))
                .execute();
    }

    public List<RsSku> getSkuList(Collection<Integer> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        final Set<Integer> idSet = ids.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        final QRsSku sku = QRsSku.rsSku;
        BooleanExpression expression = sku.id.in(idSet);
        Iterable<RsSku> iterable = skuRepository.findAll(expression);
        List<RsSku> skuList = new ArrayList<>();
        iterable.forEach(skuList::add);
        return skuList;
    }

    public Map<Integer, RsSku> getSkuMap(Collection<Integer> ids) {
        return getSkuList(ids).stream()
                .collect(Collectors.toMap(RsSku::getId, Function.identity()));
    }

    public Page<RsSku> getSkuList(SkuManageReq req, Pageable pageable) {
        final QRsSku sku = QRsSku.rsSku;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(sku.companyType.eq(req.getCompanyType()))
                .and(sku.enabled.eq(true));
        if (req.getCategory() != null) {
            builder.and(sku.category.eq(req.getCategory()));
        }
        if (req.getIsPromotion() != null) {
            builder.and(sku.isPromotion.eq(req.getIsPromotion()));
        } else {
            builder.and(sku.isPromotion.eq(false));
        }
        if (req.getIsOnline() != null) {
            builder.and(sku.isOnline.eq(req.getIsOnline()));
        }
        if (req.getProductLevel() != null) {
            builder.and(sku.productLevel.eq(req.getProductLevel()));
        }

        if (!ObjectUtils.isEmpty(req.getStatus())) {
            builder.and(qRsSku.status.eq(req.getStatus()));
        }

        if (!ObjectUtils.isEmpty(req.getContent())) {
            builder.and(qRsSku.name.like("%" + req.getContent() + "%"))
                    .or(qRsSku.number.eq(req.getContent()));
        }
        Page<RsSku> all = skuRepository.findAll(builder, pageable);
        List<RsSku> content = all.getContent();
        for (RsSku rsSku : content) {
            if (rsSku.getBenefit() != null) {
                String benefit = JSONObject.parseArray(rsSku.getBenefit())
                        .toString()
                        .replace("\"", "")
                        .replace("[", "")
                        .replace("]", "")
                        .replace(",", "，");
                rsSku.setBenefit(benefit);
            }
        }
        return new PageImpl<>(content, pageable, all.getTotalElements());
    }

    public Page<RsSku> getSkuList(Integer companyType, Integer status, Integer productLevel, String content, Boolean isFilterStrategy, Pageable pageable) {
        final QRsSku sku = QRsSku.rsSku;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(sku.companyType.eq(companyType))
                .and(sku.status.ne(WAIT_SALE))
                .and(sku.isPromotion.eq(false))
                .and(sku.enabled.eq(true));

        if (ObjectUtil.isNotEmpty(isFilterStrategy) && isFilterStrategy) {
            // 智投sku
            builder.and(qRsSku.productLevel.ne(SkuProductLevelConstant.STRATEGY.getValue()));
        }

        if (ObjectUtil.isNotEmpty(productLevel)) {
            builder.and(qRsSku.productLevel.eq(productLevel));
        }

        if (ObjectUtil.isNotEmpty(status)) {
            builder.and(qRsSku.status.eq(status));
        }

        if (ObjectUtil.isNotEmpty(content)) {
            builder.and(qRsSku.number.eq(content).or(qRsSku.name.like("%" + content + "%")));
        }

        return skuRepository.findAll(builder, pageable);
    }

    public RsSku verifySku(Integer skuId, Integer userId) {
        if (skuId == null) {
            throw new RecordNotExistedException("skuId不存在");
        }
        Instant now = Instant.now();
        RsSku sku = getSku(skuId).orElseThrow(() -> new RecordNotExistedException("sku不存在"));
        Integer status = sku.getStatus();
        Instant startTime = sku.getStartTime();
        Instant endTime = sku.getEndTime();
        if (!Objects.equals(sku.getEnabled(), true)) {
            throw new RecordNotExistedException("sku不存在");
        }
        if (!Objects.equals(status, ON_SALE)) {
            throw new BusinessException(SKU_NOT_ON_SALE);
        }
        if (startTime != null && now.isBefore(startTime)) {
            throw new BusinessException(SKU_LT_SALE_TIME);
        }
        if (endTime != null && now.isAfter(endTime)) {
            throw new BusinessException(SKU_GT_SALE_TIME);
        }
        ResultConstant checkResult = checkSkuBuyLimit(sku, userId);
        if (Objects.nonNull(checkResult)) {
            throw new BusinessException(checkResult);
        }
        return sku;
    }

    /**
     * 校验sku的购买限制
     *
     * @param sku
     * @param userId
     */
    public ResultConstant checkSkuBuyLimit(RsSku sku, Integer userId) {
        if (userId == null) {
            return null;
        }
        Integer skuId = sku.getId();
        Integer buyLimit = sku.getBuyLimit();
        Integer productLevel = sku.getProductLevel();
        Integer vipLevel = productLevelToVipLevel(productLevel);

        if (Objects.isNull(vipLevel)) {
            return null;
        }

        //判断sku的服务期限制
        List<VipSubscription> userVipSubscriptionList = vipSubscriptionService.getUserVipSubscriptionList(userId, vipLevel, false);
        Instant expireTime = ObjectUtils.isEmpty(userVipSubscriptionList) ? null : userVipSubscriptionList.get(0).getExpireTime();

        if (Objects.equals(buyLimit, BuyLimitConstant.SAME_USER_LIMIT)) {
            // 已在服务期内限购
            return Objects.nonNull(expireTime) ? SKU_USER_MAX_LIMIT : null;
        } else if (Objects.equals(buyLimit, BuyLimitConstant.DAY_LIMIT)) {
            //没开服务不能购买
            if (Objects.isNull(expireTime)) {
                return SKU_USER_SERVICE_PERIOD_LIMIT;
            }
            //已有服务期的60天内限购
            boolean canRenew = Instant.now().isAfter(expireTime.minus(61, ChronoUnit.DAYS));
            if (!canRenew) {
                return SKU_USER_SERVICE_PERIOD_LIMIT;
            }
        }
        return null;
    }

    private Integer productLevelToVipLevel(Integer productLevel) {
        List<SkuProperties.Config> config = skuProperties.getConfig();
        if (ObjectUtils.isEmpty(config)) {
            return null;
        }
        SkuProperties.Config skuConfig = config.stream()
                .filter(e -> Objects.equals(e.getProductLevel(), productLevel))
                .findFirst()
                .orElse(null);
        return skuConfig != null ? skuConfig.getVipLevel() : null;
    }

    public Page<SkuDto> getUserSkuList(Integer userId, Boolean isOnline, Integer status, String skuName, Pageable pageable) {

        BlazeJPAQuery<RsSku> blazeJPAQuery = new BlazeJPAQuery<>(primaryEntityManager, primaryCriteriaBuilderFactory);

        BaseResult<List<UcDepartment>> deptList = userClient.getDepartmentFullPath(userId, null);
        if (deptList.isFail()) {
            throw new BusinessException(deptList);
        }

        Set<Integer> skuIdSet = new HashSet<>();
        if (deptList.isPresent()) {
            Set<Integer> deptIdSet = deptList.getData().stream().map(UcDepartment::getId).collect(Collectors.toSet());
            Set<Integer> skuDeptSet = jpaQueryPrimary.selectFrom(skuDeptRelation)
                    .where(skuDeptRelation.department.in(deptIdSet))
                    .fetch()
                    .stream()
                    .map(SkuDeptRelation::getSku)
                    .collect(Collectors.toSet());
            skuIdSet.addAll(skuDeptSet);
        }
        Set<Integer> skuUserSet = jpaQueryPrimary.selectFrom(qRsSkuUserRelation)
                .where(qRsSkuUserRelation.userId.eq(userId))
                .fetch()
                .stream()
                .map(RsSkuUserRelation::getSkuId)
                .collect(Collectors.toSet());
        skuIdSet.addAll(skuUserSet);
        // 排除智投sku
        Set<Integer> strategySkuSet = getStrategySkuIdSet();
        skuIdSet.removeAll(strategySkuSet);
        BlazeJPAQuery<Tuple> query = blazeJPAQuery.select(qRsSku, skuActivity)
                .from(qRsSku)
                .leftJoin(skuActivity)
                .on(qRsSku.id.eq(skuActivity.skuId)
                        .and(skuActivity.status.in(Arrays.asList(SkuActivityStatusConstant.PREHEATING,
                                SkuActivityStatusConstant.WAITING, SkuActivityStatusConstant.PROCESS))))
                .where(qRsSku.id.in(skuIdSet))
                .where(qRsSku.enabled.eq(true));
        if (!ObjectUtils.isEmpty(status)) {
            query.where(qRsSku.status.eq(status));
        }

        if (!ObjectUtils.isEmpty(isOnline)) {
            query.where(qRsSku.isOnline.eq(isOnline));
        }

        if (!ObjectUtils.isEmpty(skuName)) {
            query.where(qRsSku.name.like("%" + skuName + "%"));
        }

        PagedList<Tuple> pagedList = query
                .orderBy(qRsSku.id.desc())
                .fetchPage((int) pageable.getOffset(), pageable.getPageSize());

        List<SkuDto> skuList = pagedList.stream().map(p -> SkuDto.builder()
                        .sku(p.get(qRsSku))
                        .skuActivity(p.get(skuActivity)).build())
                .collect(Collectors.toList());

        return new PageImpl<>(skuList, pageable, pagedList.getTotalSize());
    }

    @Transactional(rollbackFor = Exception.class)
    public RsSku createSku(CreateSkuAndSkuMaterialReq req) {
//        Instant startTime = req.getStartTime() == null ? null : req.getStartTime().atZone(ZoneId.systemDefault()).toInstant();
//        Instant endTime = req.getEndTime() == null ? null : req.getEndTime().atZone(ZoneId.systemDefault()).toInstant();
        RsSku sku = RsSku.builder()
                .companyType(req.getCompanyType())
                .number(req.getNumber())
                .name(req.getName())
                .showName(req.getShowName())
                .showTitle(req.getShowTitle())
                .description(req.getDescription())
                .productLevel(req.getProductLevel())
                .price(req.getPrice())
                .creatorId(req.getCreatorId())
                .isOnline(req.getIsOnline())
//                .startTime(startTime)
//                .endTime(endTime)
                .category(req.getCategory())
                .buyLimit(req.getBuyLimit())
                .enabled(true)
                .isPromotion(false)
                .status(ON_SALE)
                .build();
        if (req.getBenefit() != null) {
            String jsonString = JSONObject.toJSONString(req.getBenefit().split("，"));
            sku.setBenefit(jsonString);
        }
//        if (req.getIsLimitTime()) {
//            sku.setStatus(WAIT_SALE);
//        } else {
//            sku.setStatus(ON_SALE);
//        }
        RsSku rsSku;
        try {
            rsSku = skuRepository.save(sku);
        } catch (DataIntegrityViolationException e) {
            throw new BusinessException(SKU_RECORD_EXISTED);
        }
//        if (req.getIsLimitTime()) {
//            //自动上架
//            Instant now = Instant.now();
//            long onTime = ChronoUnit.MILLIS.between(now, startTime);
//            SkuStatusReq onSale = SkuStatusReq.builder()
//                    .skuId(rsSku.getId())
//                    .status(ON_SALE)
//                    .startTime(startTime)
//                    .build();
//            rocketMqUtils.send(TOPIC_COMMON_DELAY, SKU_STATUS_EDIT, onTime, onSale);
//
//            //自动下架
//            Instant instant = now.plusMillis(TimeUnit.DAYS.toMillis(30));
//            if (ChronoUnit.MILLIS.between(instant, endTime) >= 0) {
//                throw new BusinessException(SKU_TIME_OVER);
//            }
//            long offTime = ChronoUnit.MILLIS.between(now, endTime);
//            SkuStatusReq offSale = SkuStatusReq.builder()
//                    .skuId(rsSku.getId())
//                    .endTime(endTime)
//                    .status(OFF_SALE)
//                    .build();
//            rocketMqUtils.send(TOPIC_COMMON_DELAY, SKU_STATUS_EDIT, offTime, offSale);
//        }
        if (req.getMainPicture() != null) {
            SkuMaterialRelation main = SkuMaterialRelation.builder()
                    .skuId(rsSku.getId())
                    .materialId(req.getMainPicture())
                    .position(POSITION_COVER)
                    .enabled(true)
                    .build();
            skuMaterialRelationRepository.save(main);
        }
        if (req.getCarouselPicture() != null) {
            for (Long carouselId : req.getCarouselPicture()) {
                SkuMaterialRelation carousel = SkuMaterialRelation.builder()
                        .skuId(rsSku.getId())
                        .materialId(carouselId)
                        .position(POSITION_CAROUSEL)
                        .enabled(true)
                        .build();
                skuMaterialRelationRepository.save(carousel);
            }
        }
        if (req.getProductDetail() != null) {
            SkuMaterialRelation description = SkuMaterialRelation.builder()
                    .skuId(rsSku.getId())
                    .materialId(req.getProductDetail())
                    .position(POSITION_DESCRIPTION)
                    .enabled(true)
                    .build();
            skuMaterialRelationRepository.save(description);
        }
        if (SkuProductLevelConstant.STRATEGY.getValue().equals(req.getProductLevel())) {
            // 智投
            RsSkuStrategy skuStrategy = RsSkuStrategy.builder()
                    .strategyId(req.getStrategyId())
                    .amount(req.getAmount())
                    .rate(req.getRate())
                    .price(req.getRatePrice())
                    .skuId(sku.getId())
                    .period(365)
                    .enabled(true)
                    .build();
            if (ObjectUtil.isNotEmpty(req.getServicePeriod())) {
                skuStrategy.setPeriod(req.getServicePeriod());
            }
            skuStrategyRepository.save(skuStrategy);
        }
        SkuChangeRecord changeRecord = SkuChangeRecord.builder()
                .changeType(CREATE)
                .skuId(rsSku.getId())
                .createTime(Instant.now())
                .operatorId(req.getCreatorId())
                .build();
        skuChangeRecordRepository.save(changeRecord);
        return rsSku;
    }

    public List<SkuChangeRecord> getSkuChangeRecord(Integer skuId) {
        QSkuChangeRecord skuChangeRecord = QSkuChangeRecord.skuChangeRecord;
        return jpaQueryPrimary.selectFrom(skuChangeRecord)
                .where(skuChangeRecord.skuId.eq(skuId))
                .orderBy(skuChangeRecord.createTime.asc(), skuChangeRecord.id.asc())
                .fetch();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSku(UpdateSkuReq req) {
//        Instant now = Instant.now();
//        RsSku sku = skuRepository.findById(req.getSkuId()).orElseThrow(() -> new BusinessException(RECORD_NOT_FOUND));
        JPAUpdateClause update = jpaQueryPrimary.update(qRsSku);
//        Instant startTime = req.getStartTime() == null ? null : req.getStartTime().atZone(ZoneId.systemDefault()).toInstant();
//        Instant endTime = req.getEndTime() == null ? null : req.getEndTime().atZone(ZoneId.systemDefault()).toInstant();
        if (req.getName() != null) {
            update.set(qRsSku.name, req.getName());
        }
        if (req.getShowName() != null) {
            update.set(qRsSku.showName, req.getShowName());
        }
        if (req.getDescription() != null) {
            update.set(qRsSku.description, req.getDescription());
        }
//        if (req.getStartTime() != null) {
//            if (sku.getStartTime().minusMillis(TimeUnit.MINUTES.toMillis(5)).isBefore(now) && sku.getStartTime().isAfter(now)) {
//                throw new BusinessException(UPDATE_TIME_UNSUPPORTED);
//            }
//            if (sku.getStartTime().isBefore(now) || sku.getStatus().equals(ON_SALE)) {
//                throw new BusinessException(SKU_STATUS_UPDATED);
//            } else {
//                update.set(qRsSku.startTime, startTime);
//                //自动上架
//                long onTime = ChronoUnit.MILLIS.between(now, startTime);
//                SkuStatusReq onSale = SkuStatusReq.builder()
//                        .skuId(sku.getId())
//                        .status(ON_SALE)
//                        .startTime(startTime)
//                        .build();
//                rocketMqUtils.send(TOPIC_COMMON_DELAY, SKU_STATUS_EDIT, onTime, onSale);
//            }
//        }
//        if (req.getEndTime() != null) {
//            update.set(qRsSku.endTime, endTime);
//            //自动下架
//            Instant instant = now.plusMillis(TimeUnit.DAYS.toMillis(30));
//            if (ChronoUnit.MILLIS.between(instant, endTime) >= 0) {
//                throw new BusinessException(SKU_TIME_OVER);
//            }
//            if (sku.getEndTime().isBefore(now) || sku.getStatus().equals(OFF_SALE)) {
//                throw new BusinessException(SKU_STATUS_UPDATED);
//            }
//            long offTime = ChronoUnit.MILLIS.between(now, endTime);
//            SkuStatusReq offSale = SkuStatusReq.builder()
//                    .skuId(sku.getId())
//                    .status(OFF_SALE)
//                    .endTime(endTime)
//                    .build();
//            rocketMqUtils.send(TOPIC_COMMON_DELAY, SKU_STATUS_EDIT, offTime, offSale);
//        }
        if (req.getBenefit() != null) {
            String jsonString = JSONObject.toJSONString(req.getBenefit().split("，"));
            update.set(qRsSku.benefit, jsonString);
        }
        update.where(qRsSku.id.eq(req.getSkuId()))
                .execute();
        deleteSkuMaterialRelation(req.getSkuId());
        if (req.getMainPicture() != null) {
            SkuMaterialRelation carousel = SkuMaterialRelation.builder()
                    .skuId(req.getSkuId())
                    .materialId(req.getMainPicture())
                    .enabled(true)
                    .position(POSITION_COVER)
                    .build();
            String sql = SqlUtil.onDuplicateKeyUpdateSql(carousel);
            if (sql != null) {
                jdbcTemplate.execute(sql);
            }
        }
        if (req.getCarouselPicture() != null && req.getCarouselPicture().size() > 0) {
            List<SkuMaterialRelation> skuMaterialRelationList = new ArrayList<>();
            for (Long carouselId : req.getCarouselPicture()) {
                SkuMaterialRelation carousel = SkuMaterialRelation.builder()
                        .skuId(req.getSkuId())
                        .materialId(carouselId)
                        .position(POSITION_CAROUSEL)
                        .enabled(true)
                        .build();
                skuMaterialRelationList.add(carousel);
            }
            String sql = SqlUtil.onDuplicateKeyUpdateSql(skuMaterialRelationList);
            if (sql != null) {
                jdbcTemplate.execute(sql);
            }
        }
        if (req.getProductDetail() != null) {
            SkuMaterialRelation carousel = SkuMaterialRelation.builder()
                    .skuId(req.getSkuId())
                    .materialId(req.getProductDetail())
                    .position(POSITION_DESCRIPTION)
                    .enabled(true)
                    .build();
            String sql = SqlUtil.onDuplicateKeyUpdateSql(carousel);
            if (sql != null) {
                jdbcTemplate.execute(sql);
            }
        }
        // 操作记录
        SkuChangeRecord changeRecord = SkuChangeRecord.builder()
                .changeType(EDIT)
                .skuId(req.getSkuId())
                .createTime(Instant.now())
                .operatorId(req.getOperatorId())
                .build();
        skuChangeRecordRepository.save(changeRecord);
    }

    @Transactional
    public void updatePromotionSku(UpdatePromotionSkuReq req) {
        JPAUpdateClause update = jpaQueryPrimary.update(qRsSku);
        if (req.getName() != null) {
            update.set(qRsSku.name, req.getName());
        }
        if (req.getShowName() != null) {
            update.set(qRsSku.showName, req.getShowName());
        }
        if (req.getPrice() != null) {
            update.set(qRsSku.price, req.getPrice());
        }
        update.where(qRsSku.id.eq(req.getSkuId()))
                .execute();
    }

    @Transactional
    public RsSku createPromotionSku(CreatePromotionSkuReq req) {
        RsSku sku = RsSku.builder()
                .number(req.getNumber())
                .name(req.getName())
                .showName(req.getShowName())
                .price(req.getPrice())
                .isPromotion(true)
                .isOnline(true)
                .companyType(req.getCompanyType())
                .enabled(true)
                .creatorId(req.getCreatorId())
                .status(ON_SALE)
                .buyLimit(NONE_LIMIT)
                .build();
        RsSku rsSku;
        try {
            rsSku = skuRepository.save(sku);
        } catch (DataIntegrityViolationException e) {
            throw new BusinessException(SKU_RECORD_EXISTED);
        }
        SkuChangeRecord changeRecord = SkuChangeRecord.builder()
                .changeType(CREATE)
                .skuId(rsSku.getId())
                .createTime(Instant.now())
                .operatorId(req.getCreatorId())
                .build();
        skuChangeRecordRepository.save(changeRecord);
        return rsSku;
    }

    public SkuAndMaterialResp getSkuAndMaterial(Integer skuId) {
        Optional<RsSku> byId = skuRepository.findById(skuId);
        SkuAndMaterialResp.SkuAndMaterialRespBuilder builder = SkuAndMaterialResp.builder();
        if (byId.isPresent()) {
            RsSku sku = byId.get();
            if (sku.getBenefit() != null) {
                String benefit = JSONObject.parseArray(sku.getBenefit())
                        .toString()
                        .replace("\"", "")
                        .replace("[", "")
                        .replace("]", "")
                        .replace(",", "，");
                sku.setBenefit(benefit);
            }
            builder.rsSku(sku);
        }
        byId.ifPresent(builder::rsSku);
        List<SkuMaterialItem> skuMaterialList = skuMaterialService.getSkuMaterialList(skuId, null, null, true);
        List<SkuMaterialItem> carouselList = new ArrayList<>();
        for (SkuMaterialItem skuMaterialItem : skuMaterialList) {
            if (skuMaterialItem.getPosition().equals(POSITION_COVER)) {
                builder.mainPosition(skuMaterialItem);
            } else if (skuMaterialItem.getPosition().equals(POSITION_CAROUSEL)) {
                carouselList.add(skuMaterialItem);
            } else if (skuMaterialItem.getPosition().equals(POSITION_DESCRIPTION)) {
                builder.descriptionPosition(skuMaterialItem);
            }
        }
        SkuStrategy skuStrategyBySku = getSkuStrategyBySku(skuId);
        return builder.carouselPosition(carouselList).skuStrategy(skuStrategyBySku).build();
    }

    @Transactional(rollbackFor = Exception.class)
    public void onSale(Integer skuId, Integer operatorId) {
        Optional<RsSku> byId = skuRepository.findById(skuId);
        if (byId.isPresent()) {
            if (byId.get().getEndTime() != null && byId.get().getEndTime().isBefore(Instant.now())) {
                throw new BusinessException(SKU_TIMEOUT);
            }
            RsSku rsSku = byId.get();
            rsSku.setStatus(ON_SALE);
            skuRepository.save(rsSku);
            SkuChangeRecord changeRecord = SkuChangeRecord.builder()
                    .changeType(ENABLE)
                    .skuId(skuId)
                    .createTime(Instant.now())
                    .operatorId(operatorId)
                    .build();
            skuChangeRecordRepository.save(changeRecord);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void offSale(Integer skuId, Integer operatorId) {
        Optional<RsSku> result = skuRepository.findById(skuId);
        if (result.isPresent()) {
            RsSku rsSku = result.get();
            rsSku.setStatus(OFF_SALE);
            skuRepository.save(rsSku);
            //操作记录
            SkuChangeRecord changeRecord = SkuChangeRecord.builder()
                    .skuId(skuId)
                    .operatorId(operatorId)
                    .createTime(Instant.now())
                    .changeType(DISABLE)
                    .build();
            skuChangeRecordRepository.save(changeRecord);
        }
    }

    private void deleteSkuMaterialRelation(Integer skuId) {
        QSkuMaterialRelation relation = QSkuMaterialRelation.skuMaterialRelation;
        jpaQueryPrimary.delete(relation)
                .where(relation.skuId.eq(skuId))
                .execute();
    }

    private Set<Integer> getStrategySkuIdSet() {
        return jpaQueryPrimary.selectFrom(skuStrategy)
                .where(skuStrategy.enabled.eq(true))
                .fetch()
                .stream()
                .map(e -> e.getSkuId().intValue())
                .collect(Collectors.toSet());

    }

    public List<SkuStrategy> getSkuStrategy(Long strategyId) {
        BlazeJPAQuery<SkuStrategy> blazeJPAQuery = new BlazeJPAQuery<>(primaryEntityManager, primaryCriteriaBuilderFactory);
        return blazeJPAQuery.select(ProjectionUtils.skuStrategyBean())
                .from(qRsSkuStrategy)
                .leftJoin(qRsSku)
                .on(qRsSkuStrategy.skuId.intValue().eq(qRsSku.id))
                .where(qRsSkuStrategy.strategyId.eq(strategyId))
                .where(qRsSkuStrategy.enabled.eq(true))
                .where(qRsSku.status.eq(ON_SALE))
                .fetch();
    }

    public List<SkuStrategy> getSkuStrategy(Long strategyId, Integer status) {
        BlazeJPAQuery<SkuStrategy> blazeJPAQuery = new BlazeJPAQuery<>(primaryEntityManager, primaryCriteriaBuilderFactory);
        BlazeJPAQuery<SkuStrategy> query = blazeJPAQuery.select(ProjectionUtils.skuStrategyBean())
                .from(qRsSkuStrategy)
                .leftJoin(qRsSku)
                .on(qRsSkuStrategy.skuId.intValue().eq(qRsSku.id))
                .where(qRsSkuStrategy.strategyId.eq(strategyId))
                .where(qRsSkuStrategy.enabled.eq(true));
        if (ObjectUtil.isNotEmpty(status)) {
            query.where(qRsSku.status.eq(status));
        }
        return query.fetch();
    }

    public SkuStrategy getSkuStrategyBySku(Integer skuId) {
        return jpaQueryPrimary.select(ProjectionUtils.skuStrategyBean())
                .from(qRsSkuStrategy)
                .where(qRsSkuStrategy.skuId.eq(skuId))
                .where(qRsSkuStrategy.enabled.eq(true))
                .fetchOne();
    }


    public BaseResult<List<SkuStrategy>> batchSkuStrategyBySku(Collection<Integer> values) {
        List<SkuStrategy> fetch = jpaQueryPrimary.select(ProjectionUtils.skuStrategyBean())
                .from(qRsSkuStrategy)
                .where(qRsSkuStrategy.skuId.in(values))
                .where(qRsSkuStrategy.enabled.eq(true))
                .fetch();
        return BaseResult.success(fetch);
    }

    public List<RsSku> batchGetSkuList(Collection<String> numbers, Integer companyType, Integer status, Boolean isPromotion) {
        JPAQuery<RsSku> query = jpaQueryPrimary.selectFrom(qRsSku)
                .where(qRsSku.companyType.eq(companyType))
                .where(qRsSku.number.in(numbers));
        if (ObjectUtil.isNotEmpty(status)) {
            query.where(qRsSku.status.eq(status));
        }
        if (ObjectUtil.isNotEmpty(isPromotion)) {
            query.where(qRsSku.isPromotion.eq(isPromotion));
        }
        query.orderBy(qRsSku.createTime.desc());
        return query.fetch();
    }
}

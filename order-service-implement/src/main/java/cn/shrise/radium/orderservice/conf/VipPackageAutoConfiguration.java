package cn.shrise.radium.orderservice.conf;

import cn.shrise.radium.orderservice.properties.vip.MultiVipProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties(value = MultiVipProperties.class)
public class VipPackageAutoConfiguration {

}

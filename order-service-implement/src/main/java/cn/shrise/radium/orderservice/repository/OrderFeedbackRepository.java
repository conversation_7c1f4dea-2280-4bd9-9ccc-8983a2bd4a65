package cn.shrise.radium.orderservice.repository;

import cn.shrise.radium.orderservice.entity.RsOrderFeedback;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface OrderFeedbackRepository extends JpaRepository<RsOrderFeedback, Long>, QuerydslPredicateExecutor<RsOrderFeedback> {

    Optional<RsOrderFeedback> findByOrderId(Integer orderId);
}

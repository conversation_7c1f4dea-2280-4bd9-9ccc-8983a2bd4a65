spring:
  datasource:
    username: ydl058
    password: ydl058058
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************
  jpa:
    show-sql: true
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  schedulerx2:
    enabled: true
    endpoint: acm.aliyun.com
    namespace: 67f08a28-546f-426c-a8dc-030b5927b377
    groupId: test-groupId
    appKey: mfOuxiKxQGMObZjBsLpT+g==
  cloud:
    function:
      definition: receiptObtain;
    stream:
      rocketmq:
        binder:
          access-key: LTAI5tRpmoqAfxDpYY4LFB5K
          secret-key: ******************************
          name-server: http://MQ_INST_1952650058542185_BXzWA1JT.mq-internet-access.mq-internet.aliyuncs.com:80
      bindings:
        receiptObtain-in-0:
          destination: wxReceiptNotify
          group: GID_wxReceiptNotify
          content-type: application/json
invoice:
  kind-invoicing: HTJS_DZFPKJ
  kind-issue: COMMON_FPKJ_FPT
  number-x: *****************
  name-x: 上海百旺测试开票
  address-and-phone-x: 上海市普陀区云岭西路689弄230号E02-E03 021-********
  address-of-bank-x: 中信银行上海普陀支行7311310182600065566
  call-method-x: 021-********
  number-g:
  name-g: 个人
  address-and-phone-g:
  address-of-bank-g:
  call-method-g:
  issuer: 点涨开票人
  payee: 点涨收款人
  reviewer: 点涨复核人
  deduction:
  kind-projects: COMMON_FPKJ_XMXX
  size: 1
  nature: 0
  commodity-code: 3040201990000000000
  logo: 0
  project-amount: 1
  tariff: 0.06
  lxdm: 用户类型
  tccdzfp-zpdzfp: http://tccdzfp.shfapiao.cn/zpdzfp
  fpserver-servlet: http://sc.bwfapiao.com/fpserver/FpServlet
  app-key: 07DB492A6CB8861200FEF3F0AC5FB3FA079B6E616826A63A074D4BEC1DC936F9252413F27486936750ED70D1EF9A45E4066F5DC16077FB436B9E3F00EB47EF68BE15FDF32FB6936F7AC5D0BDD3812154
  bucket-name: gs-file-src
  pay-type: {10: 支付方式：微信支付,20: 支付方式：支付宝,30: 转账}
  order-id: 订单号：
  pay-time: 支付时间：
  target-folder: invoice/

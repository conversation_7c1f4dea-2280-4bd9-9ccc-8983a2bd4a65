package cn.shrise.radium.orderservice.service.delaycoupon;

import cn.shrise.radium.orderservice.entity.DelayCouponApply;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class DelayCouponApplyServiceTest {

    @Autowired
    DelayCouponApplyService delayCouponApplyService;

    @Test
    void findAuditingDelayChange() {
        DelayCouponApply auditingDelayChange = delayCouponApplyService.findAuditingDelayChange(128155);
        System.out.println(auditingDelayChange);
    }
}
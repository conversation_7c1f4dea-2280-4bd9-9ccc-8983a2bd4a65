package cn.shrise.radium.orderservice.allinpay.entity;

import cn.hutool.core.util.IdUtil;
import cn.shrise.radium.orderservice.allinpay.config.AllInPayConfig;
import cn.shrise.radium.orderservice.allinpay.exception.AllInPayException;
import cn.shrise.radium.orderservice.allinpay.http.AllInPayFeignHttpClient;
import cn.shrise.radium.orderservice.allinpay.req.AllinPayOrderRequest;
import cn.shrise.radium.orderservice.resp.allinpay.AllinPayOrderResponse;
import cn.shrise.radium.orderservice.allinpay.service.AllInPayServiceClient;
import cn.shrise.radium.orderservice.allinpay.service.impl.AllInPayServiceClientImpl;
import cn.shrise.radium.orderservice.allinpay.util.CertificateUtils;
import cn.shrise.radium.orderservice.conf.bean.MultiAllinPayService;
import cn.shrise.radium.orderservice.constant.AllinPayClientTypeConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bouncycastle.util.encoders.Base64;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
class TransactionTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AllInPayServiceClientImpl allInPayService;

    @Autowired
    private MappingJackson2XmlHttpMessageConverter xmlConverter;

    @Autowired
    private AllInPayFeignHttpClient allInPayClient;

    @Autowired
    private MultiAllinPayService multiAllinPayService;

    @Test
    void test() throws IOException, UnrecoverableKeyException, CertificateException, KeyStoreException, NoSuchAlgorithmException, NoSuchPaddingException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException {
        TransactionRequest<Map<String, String>> transaction = new TransactionRequest<>();
        TransactionHead head = new TransactionHead();
        head.setProcessingCode("1020");
        head.setInstId("********");
        head.setTransDate("20121028");
        head.setTransTime("103029");
        head.setVerNum("0000");
        transaction.setHead(head);

        Map<String, String> map = new HashMap<>();
        map.put("req_trace_num","20121028103029123456");
        map.put("brc_id","0000");
        map.put("product_num","01");
        map.put("bnk_id","03020000");
        map.put("acct_type","");
        map.put("acct_num","9555500210021111 ");
        map.put("sign_num","123123123123");
        map.put("org_req_trace_num","156");
        map.put("org_trans_date","300");
        map.put("cur_type","200");
        map.put("amt_tran","");
        map.put("resp_url","");
        map.put("addtnl_data1","**************");
        transaction.setRequest(map);

        String xml = xmlConverter.getObjectMapper().writeValueAsString(transaction);
        System.out.println(xml);
        Object[] objects = CertificateUtils.readPfxFromFileWithDefaultProvider("allinpay/********/wts.pfx", "111111");
        X509Certificate x509Certificate = (X509Certificate) objects[0];
        PrivateKey privateKey = (PrivateKey) objects[1];
        //签名所有属性
        String signCode = CertificateUtils.signMsgPriKey(xml, privateKey);
        System.out.println(signCode);

        head.setSignCode(signCode);
        String signXml = xmlConverter.getObjectMapper().writeValueAsString(transaction);

        //生成对称密钥 加密xml
        Key key = CertificateUtils.generateSymmetricKey();
        byte[] encryptedText = CertificateUtils.encryptSymmetry(signXml, key);

        //通联公钥加密对称密钥
        PublicKey publicKey = CertificateUtils.readCerFile("allinpay/********/beta/publicKey.cer");
        byte[] encryptedKey = CertificateUtils.encryptSymmetricKey(key, publicKey);
        EncryptedBody body = EncryptedBody.builder()
                .encryptedText(Base64.toBase64String(encryptedText))
                .keyInfo(new EncryptedBody.KeyInfo(x509Certificate.getSerialNumber().toString(), Base64.toBase64String(encryptedKey)))
                .build();
        String message = xmlConverter.getObjectMapper().writeValueAsString(body);
        String signformat = Base64.toBase64String(message.getBytes(StandardCharsets.UTF_8));
        System.out.println(signformat);
        String response = allInPayClient.httpRequest(signformat);
        System.out.println("resp:"+ response);


        byte[] decode = Base64.decode(response);
        EncryptedBody encryptedBody = xmlConverter.getObjectMapper().readValue(decode, EncryptedBody.class);
        String responseEncryptedText = encryptedBody.getEncryptedText();
        String receiverX509CertSN = encryptedBody.getKeyInfo().getReceiverX509CertSN();
        String responseEncryptedKey = encryptedBody.getKeyInfo().getEncryptedKey();

        //解密对称密钥
        final Key symmetricKey = CertificateUtils.decryptSymmetricKey(Base64.decode(responseEncryptedKey), privateKey);
        //解密xml
        final String responsePlainText = CertificateUtils.decryptSymmetry(Base64.decode(responseEncryptedText), symmetricKey);
        System.out.println(responsePlainText);


    }

    @Test
    void testAllInPayService() throws AllInPayException {
        TransactionRequest<Map<String, String>> transaction = new TransactionRequest<>();
        TransactionHead head = new TransactionHead();
        head.setProcessingCode("1020");
        head.setInstId("********");
        head.setTransDate("20121028");
        head.setTransTime("103029");
        head.setVerNum("0000");
        transaction.setHead(head);

        Map<String, String> map = new HashMap<>();
        map.put("req_trace_num","20121028103029123456");
        map.put("brc_id","0000");
        map.put("product_num","01");
        map.put("bnk_id","03020000");
        map.put("acct_type","");
        map.put("acct_num","9555500210021111 ");
        map.put("sign_num","123123123123");
        map.put("org_req_trace_num","156");
        map.put("org_trans_date","300");
        map.put("cur_type","200");
        map.put("amt_tran","");
        map.put("resp_url","");
        map.put("addtnl_data1","**************");
        transaction.setRequest(map);

        final TransactionResponse<Object> response = allInPayService.request(transaction, Object.class);
        System.out.println(response);
    }

    @Test
    void testAllinPayWithAlipay() throws AllInPayException, JsonProcessingException {

        SeqDetail seqDetail = SeqDetail.builder()
                .sepNo("000")
                .sepAcctType(1)
                .sepMerchCode("********")
                .sepBankId("********")
                .sepAcctNum("****************")
                .sepPayAmount(1)
                .build();
        List<SeqDetail> seqDetailList = Collections.singletonList(seqDetail);

        OrderDetail orderDetail = OrderDetail.builder()
                .subAppId("****************")
                .build();
        String orderDetailStr = objectMapper.writeValueAsString(orderDetail);
        String seqDetailStr = objectMapper.writeValueAsString(seqDetailList);
        AllinPayOrderRequest allinPayOrderRequest = AllinPayOrderRequest.builder()
                .reqTraceNum(IdUtil.objectId())
                .purchaseType(4)
                .payMode("L")
                .bnkId("A")
                .payAmount(1)
                .curType(156)
                .orderDetail(orderDetailStr)
                .sepDetail(seqDetailStr)
                .prodImportFlag(0)
                .orderNum("ordernum123123774")
                .respUrl("https://callback-api-beta2.shrise.cn")
                .ipAddr("*************")
                .build();
        // todo
        AllinPayOrderResponse response = allInPayService.request("********", "2085", "1.00", allinPayOrderRequest, AllinPayOrderResponse.class);
        System.out.println(response);
    }

    @Test
    void testCreatePay() throws AllInPayException, JsonProcessingException {

        SeqDetail seqDetail = SeqDetail.builder()
                .sepNo("000")
                .sepAcctType(1)
                .sepMerchCode("********")
                .sepBankId("********")
                .sepAcctNum("****************")
                .sepPayAmount(1)
                .build();
        List<SeqDetail> seqDetailList = Collections.singletonList(seqDetail);

        OrderDetail orderDetail = OrderDetail.builder()
                .subAppId("wx047c729e91070b37")
                .build();
        String orderDetailStr = objectMapper.writeValueAsString(orderDetail);
        String seqDetailStr = objectMapper.writeValueAsString(seqDetailList);
        AllinPayOrderRequest allinPayOrderRequest = AllinPayOrderRequest.builder()
                .reqTraceNum(IdUtil.objectId())
                .purchaseType(4)
                .payMode("H")
                .bnkId("W")
                .payAmount(1)
                .curType(156)
                .orderDetail(orderDetailStr)
                .sepDetail(seqDetailStr)
                .prodImportFlag(0)
                .orderNum("ordernum123123774")
                .respUrl("http://ztm.shrise.cn:8108/")
                .build();
        // todo
        AllInPayServiceClient allInPayServiceClient = multiAllinPayService.switchoverTo(45, 600001);
        AllInPayConfig allInPayConfig = allInPayServiceClient.getConfig();
        AllinPayOrderResponse response = allInPayServiceClient.request(AllinPayClientTypeConstant.PAY, allInPayConfig.getInstId(), allInPayConfig.getVersion(), allinPayOrderRequest, AllinPayOrderResponse.class);
        System.out.println(response);
    }

    @Test
    void testBetaCreatePay() throws AllInPayException, JsonProcessingException {

        SeqDetail seqDetail = SeqDetail.builder()
                .sepNo("000")
                .sepAcctType(1)
                .sepMerchCode("********")
                .sepBankId("********")
                .sepAcctNum("********")
                .sepPayAmount(1)
                .build();
        List<SeqDetail> seqDetailList = Collections.singletonList(seqDetail);

        OrderDetail orderDetail = OrderDetail.builder()
                .subAppId("wx047c729e91070b37")
                .build();
        String orderDetailStr = objectMapper.writeValueAsString(orderDetail);
        String seqDetailStr = objectMapper.writeValueAsString(seqDetailList);
        AllinPayOrderRequest allinPayOrderRequest = AllinPayOrderRequest.builder()
                .reqTraceNum(IdUtil.objectId())
                .purchaseType(4)
                .payMode("H")
                .bnkId("W")
                .payAmount(1)
                .curType(156)
                .orderDetail(orderDetailStr)
                .sepDetail(seqDetailStr)
                .prodImportFlag(0)
                .orderNum("ordernum123123774")
                .respUrl("http://ztm.shrise.cn:8108/")
                .build();
        // todo
        AllInPayServiceClient allInPayServiceClient = multiAllinPayService.switchoverTo(45, 600001);
        AllInPayConfig allInPayConfig = allInPayServiceClient.getConfig();
        AllinPayOrderResponse response = allInPayServiceClient.request(AllinPayClientTypeConstant.PAY, allInPayConfig.getInstId(), allInPayConfig.getVersion(), allinPayOrderRequest, AllinPayOrderResponse.class);
        System.out.println(response);
    }

    @Test
    void testHttpClient() {
        String msg = "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";
        allInPayClient.httpRequest(msg);
    }

}
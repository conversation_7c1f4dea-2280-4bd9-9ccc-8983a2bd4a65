package cn.shrise.radium.orderservice;

import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.util.ResourceUtils;
import cn.shrise.radium.orderservice.properties.AlipayProperties;
import cn.shrise.radium.orderservice.resp.payment.*;
import cn.shrise.radium.orderservice.util.payment.ConfigInfoParserUtils;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.file.IOUtils;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

import static cn.shrise.radium.orderservice.constant.ResultConstant.KEY_NOT_FOUND;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Slf4j
public class AlipayTest {

    @Test
    public void queryDevOrder() throws AlipayApiException {
        AlipayClient alipayClient = getDevClient();
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", "ap20220214134419nbheli");
        //bizContent.put("trade_no", "2014112611001004680073956707");
        request.setBizContent(bizContent.toString());
        AlipayTradeQueryResponse response = alipayClient.certificateExecute(request);
        if(response.isSuccess()){
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

    @Test
    public void queryProdOrder() throws AlipayApiException {
        AlipayClient alipayClient = getProdClient();
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", "sub_ap202202150959169ywzn0");
        //bizContent.put("trade_no", "2014112611001004680073956707");
        request.setBizContent(bizContent.toString());
        AlipayTradeQueryResponse response = alipayClient.certificateExecute(request);
        if(response.isSuccess()){
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

    public AlipayClient getDevClient() throws AlipayApiException {
        AlipayProperties.Config c = new AlipayProperties.Config();
        c.setAppId("****************");
        c.setServerUrl("https://openapi.alipaydev.com/gateway.do");
        c.setAppPrivateKeyPath("pay/dev/appPrivateKey.txt");
        c.setAlipayPublicCertPath("pay/dev/alipayCertPublicKey_RSA2.cer");
        c.setAlipayRootCertPath("pay/dev/alipayRootCert.cer");
        c.setAppCertPublicKeyPath("pay/dev/appCertPublicKey.cer");
        return new DefaultAlipayClient(initConfig(c));
    };

    public AlipayClient getProdClient() throws AlipayApiException {
        AlipayProperties.Config c = new AlipayProperties.Config();
        c.setAppId("2021002129649689");
        c.setServerUrl("https://openapi.alipay.com/gateway.do");
        c.setAppPrivateKeyPath("pay/prod/app_private_key.txt");
        c.setAlipayPublicCertPath("pay/prod/alipay_cert_public_key_rsa2.crt");
        c.setAlipayRootCertPath("pay/prod/alipay_root_cert_sn.crt");
        c.setAppCertPublicKeyPath("pay/prod/app_cert_sn.crt");
        return new DefaultAlipayClient(initConfig(c));
    };

    public AlipayConfig initConfig(AlipayProperties.Config conf){
        AlipayConfig config = new AlipayConfig();
        config.setServerUrl(conf.getServerUrl());
        config.setAppId(conf.getAppId());
        // 设置私钥
        config.setPrivateKey(loadFile(conf.getAppPrivateKeyPath()));
        // 设置应用公钥证书路径
        config.setAppCertPath(ResourceUtils.getRealPath(conf.getAppCertPublicKeyPath()));
        // 设置支付宝公钥证书路径
        config.setAlipayPublicCertPath(ResourceUtils.getRealPath(conf.getAlipayPublicCertPath()));
        // 设置支付宝根证书路径
        config.setRootCertPath(ResourceUtils.getRealPath(conf.getAlipayRootCertPath()));
        return config;
    };

    private String loadFile(String classPath) {
        ClassPathResource resource = new ClassPathResource(classPath);

        try {
            InputStreamReader inputStreamReader = new InputStreamReader(resource.getInputStream());

            String var5;
            try {
                var5 = IOUtils.toString(inputStreamReader);
            } finally {
                inputStreamReader.close();

            }

            return var5;
        } catch (IOException var17) {
            log.error("ali pay app private key is required ,{}", var17.getMessage());
            throw new BusinessException(KEY_NOT_FOUND);
        }
    }

    @Test
    public void test() throws JsonProcessingException {
        WxPayConfigInfo info = WxPayConfigInfo.builder()
                .mchId("**********")
                .mchKey("pBR49QnFAxBGQUN5WrgHdexZkBmmz369")
                .keyPath("classpath:**********.p12")
                .build();
        ObjectMapper mapper = new ObjectMapper();
        String s = mapper.writeValueAsString(info);
        AllinPayConfigInfo allinPayConfigInfo = AllinPayConfigInfo.builder()
                .serialNumber("***********")
                .instId("********")
                .sepAccount("4000029619200231157")
                .sepBankId("********")
                .version("1.00")
                .certificatePath("allinpay/********/prod/privateKey.pfx")
                .certificatePassword("093093")
                .publicKeyPath("allinpay/********/prod/publicKey.cer")
                .build();
        String s1 = mapper.writeValueAsString(allinPayConfigInfo);
        LklPayConfigInfo lklPayConfigInfo = LklPayConfigInfo.builder()
                .appId("OP00002379")
                .serialNo("018d359c6b87")
                .merchantNo("8225840739900EH")
                .termNo("H1203603")
                .priKeyPath("/tmp/alipay/lakala/beta/api_private_key.pem")
                .lklCerPath("/tmp/alipay/lakala/beta/lkl-apigw-v1.cer")
                .lklNotifyCerPath("/tmp/alipay/lakala/beta/lkl-apigw-v1.cer")
                .sm4Key("PXiOgNmZcaP4OS7TOYUO8g==")
                .serverUrl("https://s2.lakala.com")
                .build();
        String s2 = mapper.writeValueAsString(lklPayConfigInfo);
        AliPayConfigInfo build = AliPayConfigInfo.builder()
                .appId("****************")
                .serverUrl("https://openapi.alipaydev.com/gateway.do")
                .appPrivateKeyPath("pay/dev/appPrivateKey.txt")
                .alipayPublicCertPath("pay/dev/alipayCertPublicKey_RSA2.cer")
                .alipayRootCertPath("pay/dev/alipayRootCert.cer")
                .appCertPublicKeyPath("pay/dev/appCertPublicKey.cer")
                .isLocal(true)
                .build();
        String s3 = mapper.writeValueAsString(build);
        UnionPayConfigInfo.BankInfoList bankInfoList = new UnionPayConfigInfo.BankInfoList();
        bankInfoList.setName("中国银行");
        bankInfoList.setNumber("0104");
        List<UnionPayConfigInfo.BankInfoList> list = new ArrayList<>();
        list.add(bankInfoList);
        UnionPayConfigInfo sm2 = UnionPayConfigInfo.builder()
                .cusId("56216106211BW32")
                .appId("********")
                .signType("SM2")
                .validTime("60")
                .privateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCsmTdW2eRsCDVMiZCtgyAH2L3ZLTziDweahLUdqFKIwbK5wG9e2u7nLNARkRS4i/1Qa4xqC6gD0d0jL1P2pNvGL5Qz/VvxftGsnBnxoR8urSIQl7n/8MCHm7ZigFk5apj6mjnEbhk3xPsnOLmLB2UNbqsEHMAHzWxmbCTWAhBg4pVnnAdERJY4xSPEcd0QrppgiiP4iWuwVHG0pZsQw/evLAeWUEJQwGEm8C7E55Dg85VYi3BWXt4mP/iF4m7RM/62FdKd077me15hn6Xc59PFO9wpXqVB4RPhSKXAxUwvdtFpbiXitceT6Mjq/k1+4q4VTaHRGUvtYMfweb8lyxxfAgMBAAECggEAH7YS1dE4x6SGbmaD/20tEGQBFYrbOcl7Iyeowycu6tNzy+8QR0RASpcQL3oRbYrC4uivprg3iWQglhtW+OT3QCVzvE2be/ZJ/cW8eomY2hGTRasq0Fxic0fnrZdZTVRJgRme7XkxBu8ZGe6xIFsedOZVY28I1SYn+XV7GxrL+ZC50JwyJunR9bwx6M0cFu9m7P08WW24BKSGitPjV+FaehtTVUQCNd9NGAYMnJyBZsbLYoZ1lJHu8y2RZpRUSpKMrwphvto+E3y/yreqZQEo3YpxPTNFzwiUjKpzL4jfDB2NepiOh1kDPOUtaxxnLY1dRKpDOJz/vYZaPPf0LGfZKQKBgQDyIxZO3rqb08kowpatQYGjlnE38/dOZsGp6vo2/+3CuT09ra5EsJffQDgmFpdUEmj1tBLW00TnxZSjFaoLm72IRkpIENfVUFA4ti7j301boOAvqK2Svq2zko2yVGS363ub7+WLrvrmTthICN8j/l0E/shZLgDSXnS6HWIMKovn9QKBgQC2eu2gWTVXjAlyMdzIm+Jn3eM7Q3rNnatKQdNmXenHxvjbX1hgRxuKBBOs3PpoaA+QM03B0l8iNvy/+PmLLlgDCtkRS8caNy3yl3IYRgbb7ME9qDRV7HRAXmZAXbo5HwnonTrDqZZ4HcmB4FxjA17B2yZFqcm9LWsnzF6/1iWCgwKBgBWpa+p8ZIdiSAG8fsxbKuTepZxS2BIMgVDZM69N8BBWkBL/gbKldcDENwG5Tap4xykMBg9v8R8m/ugkHQVS1n7lgum1kAmGWNbp/YHnTT1gRA4fcf/JzTJebwzAHg6SI4nMyWVYrxuBfDndiVDoBZxXysanpe0sBBdOX6IlAEUtAoGAUDtmZX0zreV9db8ksvOPLVnrAfCeeu9bE9AzcavTESqT2mhmhBZfrmQyfmu+kc9HIIDeLF0hxPpmUMXYenCK8/N+E1O9G/Ks+h/KY4/Ojj3fjQt6z1iimHapAJ31Ng5GngcbglH7PZ7jLX6HQGWH8TY48qI/eDUTCzGZa0Ftxp8CgYEAnxlulfQn1o2UBejWAuqlZFBhaiyC42H8WsxHxGcF+6HvFCfMeGQ7DLhs6WoJ+K8LTRmlLm3uGVOsBuXamZx8iTHz54RC3mNieDPt5e6hnMxYq2RBx5hRITGQyTJDVTiY/EP33CfpqwAW2XSueuR8fLhMJNq2noiEMewKjKMjE2g=")
                .publicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCm9OV6zH5DYH/ZnAVYHscEELdCNfNTHGuBv1nYYEY9FrOzE0/4kLl9f7Y9dkWHlc2ocDwbrFSm0Vqz0q2rJPxXUYBCQl5yW3jzuKSXif7q1yOwkFVtJXvuhf5WRy+1X5FOFoMvS7538No0RpnLzmNi3ktmiqmhpcY/1pmt20FHQQIDAQAB")
                .sm2PrivateKey("MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgWAjVrh5/3PTebbecnI0amP0z26EgqjyFI7AJxuH8kQOgCgYIKoEcz1UBgi2hRANCAATz3PUc1UFjIzAJQkvCVkhdx4BAaGcwG6cIm2bComwqWNwd7SFz40WF/6Pjewwnlm+eO3dDT4TRVE8jfr7V0NVd")
                .tlPublicKey("MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEBQicgWm0KAMqhO3bdqMUEDrKQvYg8cCXHhdGwq7CGE6oJDzJ1P/94HpuVdBf1KidmPxr7HOH+0DAnpeCcx9TcQ==")
                .url("https://syb.allinpay.com/apiweb")
                .bankList(list)
                .build();
        String s4 = mapper.writeValueAsString(sm2);
        System.out.println(s4);
    }
}

package cn.shrise.radium.orderservice.util;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@SpringBootTest
class InvoiceReqTest {

    @Autowired
    InvoiceReq invoiceReq;

    @Test
    void getInvoiceResult() {
        String result = invoiceReq.getInvoiceResult("aBWLKUpYeHe3KJJ").getRequestResult();
        JSONObject jsonObject = JSON.parseObject(result);
        byte[] decoded = Base64.getDecoder().decode(jsonObject.getString("data"));
        String decodedStr = new String(decoded, StandardCharsets.UTF_8);
        System.out.println(result);
        System.out.println(decodedStr);
    }

    @Test
    void doPostInvoice() {
        double amount = NumberUtil.div(Long.valueOf(2), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        double total = NumberUtil.div(Long.valueOf(2), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        double tax = NumberUtil.div(Long.valueOf(0), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        String result = invoiceReq.doPostInvoice("刘琪", "GxU_9lKnVbGnokc", amount, total, tax, "m1726528779392585728", "2023-11-20 17:12:03", "*现代服务*服务费6%").getRequestResult();
        System.out.println(result);
    }
}
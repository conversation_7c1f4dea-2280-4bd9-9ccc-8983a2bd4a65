package cn.shrise.radium.orderservice.allinpay.entity;

import cn.hutool.core.util.IdUtil;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.orderservice.allinpay.config.AllInPayConfig;
import cn.shrise.radium.orderservice.allinpay.exception.AllInPayException;
import cn.shrise.radium.orderservice.allinpay.req.AllinCloseOrderRequest;
import cn.shrise.radium.orderservice.allinpay.req.AllinQueryOrderRequest;
import cn.shrise.radium.orderservice.allinpay.resp.AllinCloseOrderResponse;
import cn.shrise.radium.orderservice.allinpay.resp.AllinQueryOrderResponse;
import cn.shrise.radium.orderservice.allinpay.service.AllInPayServiceClient;
import cn.shrise.radium.orderservice.conf.bean.MultiAllinPayService;
import cn.shrise.radium.orderservice.constant.AllinPayClientTypeConstant;
import cn.shrise.radium.orderservice.constant.OrderRocketMqNameConst;
import cn.shrise.radium.orderservice.entity.RsCourseRefundOrderSub;
import cn.shrise.radium.orderservice.req.allinpay.AllinPayOrderNotifyReq;
import cn.shrise.radium.orderservice.req.allinpay.AllinRefundOrderNotifyReq;
import cn.shrise.radium.orderservice.req.allinpay.CreateAllinPaySubOrderReq;
import cn.shrise.radium.orderservice.service.pay.AllinPayService;
import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Objects;

import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_PAY;

/**
 * <AUTHOR>
 */
@SpringBootTest
class AllinPayServiceTest {

    @Autowired
    private RocketMqUtils rocketMqUtils;
    @Autowired
    private AllinPayService allinPayService;
    @Autowired
    private MultiAllinPayService multiAllinPayService;

    @Test
    void testCreateAllinWxPay() {
        CreateAllinPaySubOrderReq req = CreateAllinPaySubOrderReq.builder()
                .companyType(45)
                .bnkId("W")
                .payMode("H")
                .payType(60)
                .mchType(600001)
                .amount(1)
                .skuId(482)
                .wxId(1103)
                .orderId(651963)
                .build();
        allinPayService.createAllinWxPaySubOrder(req);
    }

    @Test
    void testQueryOrder() {
        AllInPayServiceClient allInPayServiceClient = multiAllinPayService.switchoverTo(45, 600001);
        AllInPayConfig allInPayConfig = allInPayServiceClient.getConfig();
        AllinQueryOrderRequest queryOrderRequest = AllinQueryOrderRequest.builder()
                .reqTraceNum(IdUtil.objectId())
                .orgReqTraceNum("1743186488079228928")
                .orgRespTraceNum("2024010516234038910704")
                .orgTransDate("20240105")
                .build();
        try {
            AllinQueryOrderResponse response = allInPayServiceClient.request(AllinPayClientTypeConstant.QUERY, allInPayConfig.getInstId(), allInPayConfig.getVersion(), queryOrderRequest, AllinQueryOrderResponse.class);
            boolean b = Objects.equals(response.getCode(), "0000") && Objects.equals(response.getQurRst(), "0000");
            System.out.println(b);
            System.out.println(response);
        } catch (AllInPayException e) {
            e.printStackTrace();
        }
    }

    @Test
    void testOrderClose() {
        AllInPayServiceClient allInPayServiceClient = multiAllinPayService.switchoverTo(45, 600001);
        AllInPayConfig allInPayConfig = allInPayServiceClient.getConfig();
        AllinCloseOrderRequest closeOrderRequest = AllinCloseOrderRequest.builder()
                .reqTraceNum(IdUtil.objectId())
                .orgRespTraceNum("sub_1753346944782688256")
                .build();
        try {
            AllinCloseOrderResponse response = allInPayServiceClient.request(AllinPayClientTypeConstant.CLOSE, allInPayConfig.getInstId(), allInPayConfig.getVersion(), closeOrderRequest, AllinCloseOrderResponse.class);
            boolean b = Objects.equals(response.getCode(), "0000");
            System.out.println(b);
            System.out.println(response);
        } catch (AllInPayException e) {
            e.printStackTrace();
        }
    }

    @Test
    void testPayNotify() {
        String message = "{\"companyType\":45,\"mchType\":600001,\"notifyBody\":\"repMsg=PFNUU1BhY2thZ2U%2BPEVuY3J5cHRlZFRleHQ%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%2BPEVuY3J5cHRlZEtleT5JTmdNNGpnWGUzZHFnM2p1S3VGbmE5RFJweUJMSFpaZ2dLYmpkdnY1VlJ0MU1JK3lVTDdoR3M2SmRCbmtqZDRiUlF3Vk41WkpCd0czV2tpWDlvT0FvSTNJenNaMnNneFBJa25MWTRtcnBJVGUwWlFwQlRyN1FGdEpSbVlzb2luYnhCWFVEdGpuNmlDWGM3R1hXbDBVeUY1eEJ0Rjc2ZG95dUdPVWJBbmcyeWd5dWdwOHh1VGpKbW5TTU5tK2JoSnVHQk1IbkNKdzNTTHNCVmFEQ1RYTEIzL0M3NFFZUmhpQVJvWEhsTFNPVm1Jb1RkTnl4NjVkWE1xc1R0SGliMWlkc1VIeUVBVDBEZzUxaC9mTTZUUmg3eVhDNW5rN2pLdW1ZOXRxZzRxK1diYlRZUFRWR1lJYnYzYjhrTENnc1hESERUa2VxQjB1c1NRV2hKcFVrUXlPM0E9PTwvRW5jcnlwdGVkS2V5PjwvS2V5SW5mbz48L1NUU1BhY2thZ2U%2B\"}";
        AllinPayOrderNotifyReq req = JSON.parseObject(message, AllinPayOrderNotifyReq.class);
        rocketMqUtils.send(TOPIC_PAY, OrderRocketMqNameConst.TagType.TAG_ALLIN_PAY_ORDER_NOTIFY, req);
    }

    @Test
    void testRefund() {
        RsCourseRefundOrderSub refundOrderSub = RsCourseRefundOrderSub.builder()
                .id(824L).companyType(45).refundNumber("tk1717408498436526080").subOrderId(9276)
                .build();
        allinPayService.refundOrder(refundOrderSub);
    }

    @Test
    void testRefundNotify() {
        String message = "{\"companyType\":45,\"mchType\":600001,\"notifyBody\":\"repMsg=PFNUU1BhY2thZ2U%2BPEVuY3J5cHRlZFRleHQ%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%2BPEtleUluZm8%2BPFJlY2VpdmVyWDUwOUNlcnRTTj44NjczMTQ5MzY1MTwvUmVjZWl2ZXJYNTA5Q2VydFNOPjxFbmNyeXB0ZWRLZXk%2BRGV1disxK2pEZXU4d0U5WWdTSHJ5dXVPTHRaYlpLK2d4U08vRzBPVWdqblFXWmVCODhXNnpHbHB1TGpoSmJuN2JmNlo5blg3QTN1N3pNaGpnMThTaW5LQjgxYm53YUJrNFhsRDI2K2g5cXZRTUpROHVLbVRVRlMvdFNlL0pybjYxR3oxREV3bDc2dmRzYUVnZEo4TDk1blR6bVhockJ4QXVoeE83UnRGVkk4alJQS1JjQ3d4dSticmROQmVsQ2piVHlLc0FpTkpHV2dJL1JvcGY1TXlrUmxQbm5wWE8wMlJQQ3ZyNkJXL1dWM0N5UzFtSjVwSVR5MUNXbVFiamdSd29qaWswMkYybGZLODdWN0Q3RHZnWGcwcjBmbWM3Q3FXc2J4SS9GU1R6Wk9jNVpnTkVRYk4rYzN4QmkzN3BJaGdZK2k1YS9CejljREd5MHRuZjJFL2tnPT08L0VuY3J5cHRlZEtleT48L0tleUluZm8%2BPC9TVFNQYWNrYWdlPg%3D%3D\"}";
        AllinRefundOrderNotifyReq req = JSON.parseObject(message, AllinRefundOrderNotifyReq.class);
        rocketMqUtils.send(TOPIC_PAY, OrderRocketMqNameConst.TagType.TAG_ALLIN_REFUND_NOTIFY, req);
    }
}

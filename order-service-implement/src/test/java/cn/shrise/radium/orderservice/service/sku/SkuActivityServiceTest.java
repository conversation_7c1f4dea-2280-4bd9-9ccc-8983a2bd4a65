package cn.shrise.radium.orderservice.service.sku;

import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.orderservice.req.SkuActivityStatusReq;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

import static cn.shrise.radium.common.constant.RocketMQConstant.SKU_ACTIVITY_STATUS_EDIT;
import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_COMMON_DELAY;
import static cn.shrise.radium.orderservice.constant.SkuActivityStatusConstant.END;

@SpringBootTest
class SkuActivityServiceTest {

    @Autowired
    RocketMqUtils rocketMqUtils;

    @Test
    void test() {
        Instant now = Instant.now();
        SkuActivityStatusReq offSale = SkuActivityStatusReq.builder()
                .activityId(1)
                .endTimeVer(1)
                .status(END).build();
        rocketMqUtils.send(TOPIC_COMMON_DELAY, SKU_ACTIVITY_STATUS_EDIT, ChronoUnit.MILLIS.between(now, now.plusMillis(TimeUnit.DAYS.toMillis(50))), offSale);//Caused by: com.aliyun.openservices.shade.com.alibaba.rocketmq.client.exception.MQBrokerException: CODE: 13  DESC: msg delay time more than 40 day
    }
}
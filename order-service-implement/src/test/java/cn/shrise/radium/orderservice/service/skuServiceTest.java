package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.orderservice.entity.QRsSku;
import cn.shrise.radium.orderservice.entity.RsSku;
import cn.shrise.radium.orderservice.properties.esign.EvaluationPdfTemplate;
import cn.shrise.radium.orderservice.service.sku.SkuService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.QueryFactory;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Optional;

@SpringBootTest
public class skuServiceTest {
    @Autowired
    private SkuService skuService;

    @Autowired
    private JPAQueryFactory jpaQueryPrimary;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private EvaluationPdfTemplate evaluationPdfTemplate;

    @Test
    void jsonTest(){
        String benefit = "dsada,fafasf,asdas,idqjwd,dnasu,dasd";
        String[] split = benefit.split(",");
        String s = JSONObject.toJSONString(split);
        System.out.println(s);
        Object object = JSONObject.parseArray(s);
        System.out.println(object.toString());
        String replace = object.toString().replace("\"", "").replace("[", "").replace("]", "");
        System.out.println(replace);
    }

    @Test
    void sql(){
        QRsSku qRsSku = QRsSku.rsSku;
        List<RsSku> rsSkuList = jpaQueryPrimary.selectFrom(qRsSku)
                .where(qRsSku.benefit.isNotNull())
                .fetch();
        for (RsSku rsSku : rsSkuList){
            String replace = rsSku.getBenefit().replace("\"", "").replace("[", "").replace("]", "");
            String[] benefit = replace.split("，");
            rsSku.setBenefit(JSONObject.toJSONString(benefit));
        }

        String sql = SqlUtil.onDuplicateKeyUpdateSql(rsSkuList);
        jdbcTemplate.execute(sql);
    }

    @Test
    void test(){
        String templateId = evaluationPdfTemplate.getTemplateId();
        System.out.println(templateId);
    }
}

package cn.shrise.radium.orderservice.service;

import lombok.val;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class FileExportRecordServiceTest {

    @Autowired
    FileExportRecordService service;

    @Test
    void findUnfinishedByUserId() {
        val r = service.findUnfinishedByUserId(14739);
        System.out.println(r);
    }
}
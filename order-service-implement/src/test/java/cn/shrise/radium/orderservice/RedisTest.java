package cn.shrise.radium.orderservice;

import cn.shrise.radium.common.util.RedisUtil;
import cn.shrise.radium.orderservice.service.push.PushSubscriptionService;
import cn.shrise.radium.orderservice.service.vip.VipPackageService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@SpringBootTest
public class RedisTest {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private PushSubscriptionService pushSubscriptionService;

    @Test
    public void test() {
        Boolean result = redisUtil.getLock("test001", 300L);
        System.out.println(result);
        Boolean result1 = redisUtil.releaseLock("test001");
        System.out.println(result1);
    }

    @Test
    void test2() {
//        pushSubscriptionService.getServiceTypeList(45, new HashSet<>(Arrays.asList("l1a")));
        Set<String> result = pushSubscriptionService.filterVipPackageNumber(45, "quantize");
        System.out.println(result);
    }
}

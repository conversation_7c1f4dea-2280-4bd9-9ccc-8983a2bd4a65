package cn.shrise.radium.orderservice.service.pay;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class PayTypeServiceTest {

    @Autowired
    private PayTypeService payTypeService;

    @Test
    void getPayType() {
        for (int i = 0; i < 100; i++) {
            payTypeService.getPayType(false);
        }
    }

}
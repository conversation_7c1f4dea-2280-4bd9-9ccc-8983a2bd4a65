package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.orderservice.constant.OrderErrorCode;
import cn.shrise.radium.orderservice.entity.RsCourseRefundComment;
import cn.shrise.radium.orderservice.req.CreateCourseRefundCommentReq;
import cn.shrise.radium.orderservice.service.refund.RefundCommentService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class RefundCommentServiceTest {

    @Autowired
    private RefundCommentService refundCommentService;

    @Test
    void getRsCourseRefundCommentByRefundId() {
        List<RsCourseRefundComment> rsCourseRefundCommentByRefundId = refundCommentService.getRsCourseRefundCommentByRefundId(535);
        System.out.println(rsCourseRefundCommentByRefundId);
    }

    @Test
    void createCourseRefundComment() {
        CreateCourseRefundCommentReq req = new CreateCourseRefundCommentReq();
        req.setRefundID(480);
        req.setUserID(240);
        req.setMsgType("10");
        req.setContent(" ");
        OrderErrorCode courseRefundComment = refundCommentService.createCourseRefundComment(req);
        System.out.println(courseRefundComment);
    }
}

package cn.shrise.radium.orderservice.util.esign;

import cn.shrise.radium.orderservice.service.order.OrderSignProcess;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class ESignFlowAbstractTest {

    @Autowired
    OrderSignProcess orderSignProcess;

    @Test
    void orderSignProcessTest() {
        orderSignProcess.startSignFlow(665020);
    }

}
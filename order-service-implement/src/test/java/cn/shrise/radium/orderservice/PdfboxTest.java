package cn.shrise.radium.orderservice;

import cn.hutool.core.io.resource.ClassPathResource;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class PdfboxTest {
    @Test
    public void test() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("templates/order/test.pdf");
        PDDocument pdfDocument = PDDocument.load(classPathResource.getFile());
        PDDocumentCatalog docCatalog = pdfDocument.getDocumentCatalog();
        PDAcroForm acroForm = docCatalog.getAcroForm();
        if (acroForm != null)
        {
            PDField field = acroForm.getField( "name" );
            field.setValue("test");
            field.setReadOnly(true);
            PDField field1 = acroForm.getField( "textarea" );
            field1.setValue("testtesttesttesttesttesttesttest");
            field1.setReadOnly(true);
            PDField c1 = acroForm.getField( "c1" );
            c1.setValue("Yes");
            c1.setReadOnly(true);
            PDField c2 = acroForm.getField( "c2" );
            c2.setValue("Yes");
            c2.setReadOnly(true);
            PDField c3 = acroForm.getField( "c3" );
            c3.setReadOnly(true);
            PDField c4 = acroForm.getField( "c4" );
            c4.setReadOnly(true);

            pdfDocument.save("C:\\test.pdf");
            pdfDocument.close();
        }
    }

    @Test
    void pdfTest(){

        ClassPathResource classPathResource = new ClassPathResource("templates/order/测评.pdf");
        //生成的文件路径
        String outputUrl = "D:\\test.pdf";

        PdfStamper ps = null;
        PdfReader reader = null;

        try {
            OutputStream outputStream = Files.newOutputStream(Paths.get(outputUrl));

            //读取pdf表单
            reader = new PdfReader(classPathResource.getStream());
            //根据表单生成一个新的pdf文件
            ps = new PdfStamper(reader, outputStream);
            //获取pdf表单
            AcroFields form = ps.getAcroFields();

            //给表单中添加中文字体
            BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            form.addSubstitutionFont(bf);
            Map<String,Object> data = new HashMap<>();
            data.put("a20","E");
            data.put("sign","姓名");
            data.put("date1","2022年8月23日");
            data.put("name3","姓名");
            data.put("date2","2022-8-23");
            data.put("name1","姓名");
            data.put("job1","Yes");
            data.put("job2","Yes");

            //遍历map集合，对应的填进去
            for (String key:data.keySet()) {
                form.setField(key, String.valueOf(data.get(key)), true);
            }
            ps.setFormFlattening(true);
            System.out.println(outputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                assert ps != null;
                ps.close();
                reader.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }
}
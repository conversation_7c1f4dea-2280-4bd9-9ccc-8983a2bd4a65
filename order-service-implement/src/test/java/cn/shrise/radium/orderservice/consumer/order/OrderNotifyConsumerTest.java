package cn.shrise.radium.orderservice.consumer.order;

import cn.shrise.radium.orderservice.req.OrderNotifyReq;
import cn.shrise.radium.orderservice.service.order.OrderNotifyService;
import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class OrderNotifyConsumerTest {

    @Autowired
    private OrderNotifyService orderNotifyService;

    @Test
    void testHandleOrderNotify() {
        OrderNotifyReq orderNotifyReq = JSON.parseObject("{\"accountType\":1003,\"amount\":1,\"bankType\":\"OTHERS\",\"companyType\":45,\"isSub\":true,\"mchType\":1000,\"order\":{\"amount\":1,\"companyType\":45,\"createTime\":\"2022-11-25T06:01:34Z\",\"discountAmount\":0,\"enabled\":true,\"expireTime\":\"2022-11-25T06:06:35Z\",\"id\":648662,\"isSplit\":true,\"orderNumber\":\"m1596021256280936448\",\"orderStatus\":2,\"skuId\":1356,\"skuPrice\":1,\"updateTime\":\"2022-11-25T06:01:34Z\"},\"orderNumber\":\"sub_1596021256675201024\",\"payTime\":\"2022-11-25T06:01:40.784674Z\",\"paymentAccount\":\"ot1KA6tzZ0Df5KGCTG7a-scJAjh4\",\"subOrder\":{\"amount\":1,\"createTime\":\"2022-11-25T06:01:34Z\",\"id\":6138,\"mchType\":1000,\"number\":\"sub_1596021256675201024\",\"orderId\":648662,\"orderStatus\":2,\"payType\":10,\"tradeType\":\"JSAPI\",\"updateTime\":\"2022-11-25T06:01:34Z\"},\"tradeType\":\"JSAPI\",\"transactionId\":\"4200001625202211252594279684\"}", OrderNotifyReq.class);
        orderNotifyService.handleOrderNotify(orderNotifyReq);
    }

}
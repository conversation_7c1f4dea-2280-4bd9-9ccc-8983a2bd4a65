package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.orderservice.entity.DepartmentOrderInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.List;

@SpringBootTest
class DepartmentOrderServiceTest {

    @Autowired
    private DepartmentOrderService departmentOrderService;

    @Test
    void getDeptOrderInfoList() {
        long start = System.currentTimeMillis();
        List<DepartmentOrderInfo> deptOrderInfoList = departmentOrderService.getDeptOrderInfoList(LocalDateTime.now().minusMonths(2), LocalDateTime.now(), 1);
        long end = System.currentTimeMillis();
        System.out.println("时间差"+(end-start)+",列表总数："+deptOrderInfoList.size());
    }
}
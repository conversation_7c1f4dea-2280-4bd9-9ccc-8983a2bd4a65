package cn.shrise.radium.orderservice.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class GenNumberUtilsTest {

    @Test
    void createNumber() {
        String mo = GenNumberUtils.createNumber("mo");
        System.out.println(mo);
    }

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Test
    public void test() throws JsonProcessingException {
        String str = "{\"name\":\"test\",\"age\":11,\"address\":\"tttt\"}";
        Person person1 = OBJECT_MAPPER.readValue(str, Person.class);
        String str1 = "{\"age\":11,\"address\":\"tttt\"}";
        Person person2 = OBJECT_MAPPER.readValue(str1, Person.class);
        String str2 = "{\"name\":\"test\",\"age\":11,\"address\":\"tttt\",\"mobile\":\"1111\"}";
        Person person3 = OBJECT_MAPPER.readValue(str2, Person.class);
        System.out.println();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Person {
        private String name;
        private int age;
        private String address;
    }
}
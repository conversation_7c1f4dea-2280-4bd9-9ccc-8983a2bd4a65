package cn.shrise.radium.orderservice.properties;

import cn.shrise.radium.common.util.GsonUtils;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class WxPayPropertiesTest {

    @Autowired
    WxPayProperties properties;

    @Test
    void getPayPropertiesByAt() {
        val r = properties.getPayPropertiesByAt(1099);
        System.out.println(GsonUtils.toJson(r));
    }

    @Test
    void getPayPropertiesByMchType() {
        val r = properties.getPayPropertiesByMchType(1002);
        System.out.println(GsonUtils.toJson(r));
        val r1 = properties.getPayPropertiesByMchType(1000);
        System.out.println(GsonUtils.toJson(r1));
    }
}
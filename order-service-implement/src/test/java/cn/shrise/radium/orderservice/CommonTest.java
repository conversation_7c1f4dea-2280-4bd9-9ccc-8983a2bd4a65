package cn.shrise.radium.orderservice;

import lombok.NonNull;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
public class CommonTest {
    @Test
    void test(){
        System.out.println(123 / 100);
        System.out.println(String.format("%.2f sdfjk", 123F /100));
        System.out.printf("%.2f sdfjk%n", 123 /100f);
    }

    @Test
    void nonNullTest(){
        out(Arrays.asList(1,2,3));
        out(new ArrayList());
    }

    void out(@NonNull List<Integer> abc){
        System.out.println(abc);
    }

}

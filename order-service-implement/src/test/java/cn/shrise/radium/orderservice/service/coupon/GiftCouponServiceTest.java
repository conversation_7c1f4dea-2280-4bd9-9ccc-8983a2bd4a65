package cn.shrise.radium.orderservice.service.coupon;

import cn.shrise.radium.orderservice.service.delaycoupon.ESignDelayCouponService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class GiftCouponServiceTest {

    @Autowired
    GiftCouponService giftCouponService;
    @Autowired
    ESignDelayCouponService delayCouponService;

    @Test
    void applyGiftCouponTest() {
        giftCouponService.applyGiftCoupon("ddd", 128014, 45);
    }

    @Test
    void delayCouponSign() {
        delayCouponService.startDelayCouponSign(306L, 128093);
    }
}

package cn.shrise.radium.orderservice.conf.bean;

import cn.shrise.radium.orderservice.union.service.UnionPayService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class MultiUnionServiceTest {

    @Autowired
    private MultiUnionPayService multiUnionPayService;

    @Test
    void switchoverTo() {

        UnionPayService service = multiUnionPayService.switchoverTo(45, 400001);
        String pay = service.query("sub_1590612036660809728");
        System.out.println(pay);
    }

    @Test
    void pay() {
        UnionPayService service = multiUnionPayService.switchoverTo(45, 400001);
        String pay = service.pay("https://callback-api-beta.shrise.cn/allinpay/notify/order/companyType/45/mchType/400001", "https://callback-api-beta2.shrise.cn/unionpay/notify/order", "0104", "银联支付支付测试", "sub_10000008", 1);
        System.out.println(pay);
    }

    @Test
    void refund() {
        UnionPayService service = multiUnionPayService.switchoverTo(45, 400001);
        String pay = service.refund(1, "refund1234", "sub_7777777");
        System.out.println(pay);
    }

    @Test
    void query() {
        UnionPayService service = multiUnionPayService.switchoverTo(45, 400001);
        String pay = service.query("sub_7777777");
        System.out.println(pay);
    }
}
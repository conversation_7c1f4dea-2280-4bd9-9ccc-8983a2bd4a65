package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.dto.CustomerServiceDto;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class CourseServServiceTest {

    @Autowired
    CourseServService service;

    @Autowired
    OrderClient orderClient;

    @Test
    void findServiceDetailByFilter() {
        val res = service.findServiceDetailByFilter(255, false, null, null, null);
        System.out.println(res);
    }

    @Test
    void readWriteTest() {
        val res = service.findServiceDetailByFilter(358, true, 999, 1, 10).getData();
        System.out.println(res.size());
        res.forEach(r -> System.out.println(r.getService().getId()));
        service.closeService(res.get(0).getService().getId(), 240, "测试");
        val res2 = service.findServiceDetailByFilter(358, true, 999, 1, 10).getData();
        System.out.println(res2.size());
        res2.forEach(r -> System.out.println(r.getService().getId()));
    }

    @Test
    void readWriteTest2(){
        List<CustomerServiceDto> res = orderClient.getCustomerService(374, true, 999, 1, 10).getData();
        System.out.println(res.size());

        for (int i = 0; i < res.size(); i++) {
            orderClient.closeCustomerService(res.get(i).getService().getId(), 240, "");
            List<CustomerServiceDto> res2 = orderClient.getCustomerService(374, true, 999, 1, 10).getData();
            System.out.println(i + ":" + res2.size());
        }
//        res.forEach(r -> System.out.println(r.getService().getId()));
//        orderClient.closeCustomerService(res.get(0).getService().getId(), 240, "");
//
//        List<CustomerServiceDto> res2 = orderClient.getCustomerService(374, true, 999, 1, 10).getData();
//        System.out.println(res2.size());
//        res2.forEach(r -> System.out.println(r.getService().getId()));
    }

}
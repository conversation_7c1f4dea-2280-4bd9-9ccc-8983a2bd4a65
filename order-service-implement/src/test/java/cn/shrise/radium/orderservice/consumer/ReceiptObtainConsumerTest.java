package cn.shrise.radium.orderservice.consumer;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.shrise.radium.orderservice.entity.QRsCourseOrder;
import cn.shrise.radium.orderservice.entity.QRsInvoice;
import cn.shrise.radium.orderservice.entity.RsInvoice;
import cn.shrise.radium.orderservice.repository.InvoiceRepository;
import cn.shrise.radium.orderservice.service.InvoiceService;
import cn.shrise.radium.orderservice.util.SignUtil;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.RoundingMode;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class ReceiptObtainConsumerTest {

    @Autowired
    InvoiceRepository invoiceRepository;
    @Autowired
    JPAQueryFactory jpaQueryPrimary;
    @Autowired
    InvoiceService invoiceService;

    @Test
    void test() {
        String serialNumber = "2KmhNAO5m1tKToj";//开票流水号
        String title = "丁万平";//开票抬头
        double amount = NumberUtil.div(Long.valueOf(58302 + 3498), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        double total = NumberUtil.div(Long.valueOf(58302), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        double tax = NumberUtil.div(Long.valueOf(3498), Long.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        String orderNumber = "m1729754591949017088";
        String payTime = "2023-11-29 14:50:26";
        String projects = "*现代服务*服务费6%";

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("appid", "ba0c673e5c5c4b5fa84c2c2587c8451c");
        jsonObject.set("serviceid", "QDP-FP-10001");
        jsonObject.set("signType", SignUtil.SIGN_TYPE_1);

        JSONObject content = new JSONObject();
        content.set("djbh", serialNumber);
        content.set("kpzddm", "91310115MA1K3HRJ7B_QD01");
        content.set("fplxdm", "02");
        content.set("kplx", "0");
        content.set("zsfs", "0");
        content.set("xfdzdh", "中国（上海）自由贸易试验区滨江大道257弄10号第9、10层 021-68981650");
        content.set("xfyhzh", "中信工商银行上海市证券大厦支行 1001184209000064891");
        content.set("khmc", title);
        content.set("hsje", amount);
        content.set("hjje", total);
        content.set("hjse", tax);
        content.set("bz", "订单号：" + orderNumber + "支付时间：" + payTime);
        content.set("gsdm", "91310115MA1K3HRJ7B");
        content.set("yhdm", "f9712eaf1d6e4589a856ba8567893768");
        content.set("kpr", "张敏");
        content.set("skr", "葛晓丽");
        content.set("fhr", "葛晓丽");

        JSONArray mxxx = new JSONArray();
        JSONObject mxxx1 = new JSONObject();
        mxxx1.set("djhh", 1);
        mxxx1.set("fphxz", 0);
        mxxx1.set("spmc", projects);
        mxxx1.set("ssbm", "3049900000000000000");
        mxxx1.set("hsje", amount);
        mxxx1.set("bhsje", total);
        mxxx1.set("tax", "0.06");
        mxxx1.set("se", tax);
        mxxx.add(mxxx1);

        content.set("mxxx", mxxx);

        byte[] bytes = content.toString().getBytes();
        String encoded = Base64.getEncoder().encodeToString(bytes);

        Map<String, String> querys = new HashMap<>();
        querys.put("appid", "ba0c673e5c5c4b5fa84c2c2587c8451c");
        querys.put("content", encoded);
        querys.put("serviceid", "QDP-FP-10001");
        jsonObject.set("signature", SignUtil.sign(SignUtil.SIGN_TYPE_1, "c99e0ebaa8164458a17129e58104ed82", querys));

        jsonObject.set("content", encoded);

        System.out.println(jsonObject);

        Map<String, String> heads = new HashMap<>();
        heads.put("Content-Type", "application/json");
//        String body = HttpRequest.post("https://swgx.baiwangjs.com/api/xxgl/v2/tbkp.do")
//                .headerMap(heads, false)
//                .body(String.valueOf(jsonObject))
//                .execute().body();
//        invoiceService.updateRsInvoice(invoice.getSerialNumber(), body);
    }
}

package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.orderservice.resp.RefundFlowRecordResp;
import cn.shrise.radium.orderservice.service.refund.RefundFlowRecordService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class RefundFlowRecordServiceTest {
    @Autowired
    private RefundFlowRecordService refundFlowRecordService;

    @Test
    void getRsRefundFlowRecordListByRefundId() {
        List<RefundFlowRecordResp> rsRefundFlowRecordListByRefundId = refundFlowRecordService.getRsRefundFlowRecordListByRefundId(4005);
        System.out.println(rsRefundFlowRecordListByRefundId);
    }
}

package cn.shrise.radium.orderservice.payment;

import cn.shrise.radium.orderservice.req.lkl.LklAliTransPreOrderReq;
import cn.shrise.radium.orderservice.resp.payment.PaymentTypeResp;
import cn.shrise.radium.orderservice.service.payment.LklPaymentService;
import cn.shrise.radium.orderservice.service.payment.PaymentService;
import cn.shrise.radium.orderservice.service.payment.WxPaymentService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class PaymentServiceTest {

    @Autowired
    PaymentService paymentService;
    @Autowired
    WxPaymentService wxPaymentService;
    @Autowired
    LklPaymentService lklPaymentService;

    @Test
    void testGetPayType() {
        List<PaymentTypeResp> resplList = paymentService.getPaymentType(1000L, true);
        System.out.println(resplList);
    }

    @Test
    void testWxPayCloseOrder() {
        wxPaymentService.closeOrder(77L, "sub_1838475506236313600");
    }

    @Test
    void testLklSdk() {
        LklAliTransPreOrderReq lklAliTransPreOrderReq1 = LklAliTransPreOrderReq.builder()
                .skuId(2446)
                .skuName("ppppp11")
                .accountType("ALIPAY")
                .transType("41")
                .payAmount(1)
                .orderNum("sub_1838489770636398592test1")
                .requestIp("*************")
                .build();
        String response1 = lklPaymentService.alipayTransPreOrder(lklAliTransPreOrderReq1, 87L);
        LklAliTransPreOrderReq lklAliTransPreOrderReq2 = LklAliTransPreOrderReq.builder()
                .skuId(2446)
                .skuName("ppppp11")
                .accountType("ALIPAY")
                .transType("41")
                .payAmount(1)
                .orderNum("sub_1838489770636398592test2")
                .requestIp("*************")
                .build();
        String response2 = lklPaymentService.alipayTransPreOrder(lklAliTransPreOrderReq2, 81L);
        System.out.println("finished");
    }
}

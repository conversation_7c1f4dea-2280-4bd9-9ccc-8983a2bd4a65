package cn.shrise.radium.orderservice;

import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.orderservice.constant.OrderRocketMqNameConst;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.orderservice.req.OrderNotifyReq;
import cn.shrise.radium.orderservice.service.pay.NotifyOrderProcess;
import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;

@SpringBootTest
public class OrderProcessTest {

    @Autowired
    private NotifyOrderProcess notifyOrderProcess;
    @Autowired
    private RocketMqUtils rocketMqUtils;

    @Test
    public void handle() {
        RsCourseOrder order = RsCourseOrder.builder()
                .id(7007)
                .amount(68800)
                .payTime(Instant.now())
                .companyType(45)
                .userId(311)
                .salesId(311)
                .skuId(17)
                .build();

        OrderNotifyReq value = OrderNotifyReq.builder()
                .order(order)
                .accountType(1000)
                .orderNumber("wx20211101094457i5bzod")
                .transactionId("4200001298202112104292569598")
                .paymentAccount("oqSKF0p2vZP6VDX0nviL2bCHio1I")
                .build();

//        notifyOrderProcess.orderHandle(value);
//        notifyOrderProcess.orderExtProcess(order);
//        notifyOrderProcess.openOrderServiceProcess(order);
//        notifyOrderProcess.wxH5OrderHandle(value);
        notifyOrderProcess.paySuccessMsgProcess(value);

    }

    @Test
    void testNotifyOrderProcess() {
        String msg = "{\"amount\":1,\"companyType\":45,\"createTime\":\"2022-08-16T08:27:10Z\",\"discountAmount\":0,\"enabled\":true,\"expireTime\":\"2022-08-16T08:32:11Z\",\"id\":646896,\"isSplit\":true,\"orderNumber\":\"m1559456722168381440\",\"orderStatus\":4,\"payTime\":\"2022-08-16T08:27:17.891617Z\",\"salesId\":127247,\"skuId\":1187,\"skuPrice\":1,\"updateTime\":\"2022-08-16T08:27:10Z\",\"userId\":127541,\"wxId\":663}";
        RsCourseOrder data = JSON.parseObject(msg, RsCourseOrder.class);
        rocketMqUtils.send(OrderRocketMqNameConst.TOPIC_PAY_NOTIFY, OrderRocketMqNameConst.TagType.TAG_ORDER_INFO_EXT, data);
    }
}

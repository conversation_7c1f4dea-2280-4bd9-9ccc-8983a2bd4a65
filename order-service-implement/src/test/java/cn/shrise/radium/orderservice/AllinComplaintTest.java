package cn.shrise.radium.orderservice;

import cn.shrise.radium.orderservice.req.complaint.WechatOrderComplaintReq;
import cn.shrise.radium.orderservice.service.complaint.AllinComplaintService;
import cn.shrise.radium.orderservice.service.complaint.WechatOrderComplaintService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class AllinComplaintTest {

    @Autowired
    private AllinComplaintService allinComplaintService;
    @Autowired
    private WechatOrderComplaintService wechatOrderComplaintService;

    @Test
    public void testAllinWx() throws JsonProcessingException {
        String str = "{\n" +
                "    \"merchantId\": 78,\n" +
                "    \"notifyBody\": \"merchantId=78&repMsg=PFNUU1BhY2thZ2U%2BPEVuY3J5cHRlZFRleHQ%2BRjhFOUEwQUNybDAyYlhUaVlvZEdRNWtoYnl0M2RhcFFXUnBBM2EzZ1VWNS8zSkNUd2RBTU1BcTVOWG15a3F6ZVJ4anVDckRhdHkrNXE4clVuS1BQZy9RM0tYUXVuR0JLbllyOG54SEIzcjExTkppbXN2ZWV3RzljVW9LNEFaTU1sRXV1L2dacDg3Q0FYMm1kR2J3cmZ5dUsyTDl6cHg4OWwwREtYdGhpOXVqTjJ2ZVpsVEpUTnUwTm0rclFnREd1RnhPbXBmWG1KWnBRZjNZdDJLbmpKcXFYZmMvZmtnT2srMnhRL2FJai9pZWdBZThldDNacWQ3U1p2eFg2ZGdDbFcvWCtLdmdiNDdSYXlXVVdmVTlIcldDa2lzTVlNMHk5MVdiMDFqa0d5Q1ZpYUdHQjdJVmFOVHdzUENaM2RSajRnbFg0YThqNHNzRDR3YzgvbnorZFgyWWFoVHBkS2szTE1CbXVINmFoc21BZHpRMlRyYnJiSWlvTDlYRmpXN0lhY0g1RTJHZytlazZicVoyNVFlRkxKaWhoN1NSUG4zOXFmK3lFTE0zQTRIYzRnTWZyOWhHZW9iK1lSS29WMW1qNC9uU0xwb1FFRVQ0R21oZ3hvUGJ3V2dBdjF1OG5NZWVabEpBRFgvbnpQNEYzc1lDWHZvSWdFQnI5cVhBcngyckJtOTk2aElucWZFYmcxOFZyL3g0Q3FYb05HNHVlemVDZ3pQZThxdkZleHh5a29hM2JlVlI5ZTlMb3AyOEJzMkdkM25OcWFJaVkzRVVEeXcyQ2ltM2FnZFZFUGVsRTIyTlMyR3ZhdUhFbWpJNzRyblRHdHB2Ujh1dUM1aXRrbGlzYWJ1eEU4ZU1Ob1ZFQS81ckhNRFlsR2pCL0pqTjNUZU1IYXpxOVZxakRiaVJPM3RXZmI5QmlKdzN3ZEp6bEw1bm5JMHFZbDMxQ01KOWNrdUk0VFZNVjBpd3l2c29JZTFjRlRFY2lPcTdCQUNFWnZDZUNsRzZYQ2NWcWY2aUs1Q2t0OGRxSUdBMXJQdVdyVU0rTE9IeDNPQUhvOFV3TVhPZUZOckE4Y0ZNTDA4SlpodE9Zb1lEVktGNlJlZmU4UjBKSmJOU1RzN2FDaE5jVnl4KzZ4VjRhL2phNjdyQTZndmhzMWtIdTFlRTQvZFhMY1lVTzVUdkxpUHBuSHdCMUw4cmhwNTNkelhLbGpqc1BVSWlIckZBMEY0MjdRYjZjamhRWmlRdlNIenBUZ21KMGV0TjRCa092d2xRNmZHV3kxS2Q1d3VPMU9ibFU3Z29IbHdzVFBqb0syK3BuQlcwN0V6bjlHZXNMK1ovcDB5dzArSTFTNnJxMy8yYTErSGxDeHBlMkc1bXJJWWZtWnhqSmZMQWZMc3ltdS80Tzl4M2liZWJKQmxDclUyZHdWUmZ0ODRtTmV1M3FoQjBhTmFvWjMyNTVzcFFRS0tOLzN1SHZhWUV3ZldUNGlsZG1tUldBajVXNERnZnNkRFprU2x2VWpGN09Na1Q5VklaSFlUZFQyQjNDaS9jNTNnL1E1QXNHdVBlaXJKMXZmSCtOMXRVN0hjclgrWUcxbjhEaEQ5QnpsaEpGeGNhTVFiNHBWaG16VGcxbytZRzFuOERoRDlEbkZvdnRzeGNYdlFnWmhiNFFzOGdrNkxkZ3NEUkJqL2lWTm1tMVJLWDQ3czRvQjJJemhKVWhCYTRDODIrNUFQMEt1VFY1c3BLczN2bERoSUxVdS91Nzg0ZE40ckR1TWNjRDBhQnB5VDNzdjBSamN4a3RoaEsrTlFTNVJLMkdqTHIwb295dURCQkpxOVZYc2svWXBpcHVQT01qYlRzTGY3WnlCTVpBNG5hNjEyeHd1SnJBSzNGVHdUaE5YOG5CV2lUalFCbkxoNEJTb0tMNTlPZlhnZFFBeFdLMmptSmE5NVQvcXIyc2RwWjFjRE1tcnVBS1BBLzVicTRrRTVERG83MURhUHc5NUludjhoWXJNSklnaDdQSXBha3VDM3EvVGhRNXB4Y1RINTh6Qm15QjBUa05WN3dybmp2eVJBa1hCL3gzQzZFRGp3UTlqMGNIelFFek9DY3hXWFZyaDdqZ2gwQUxSa1N4bmpJeEV0bFJyL252S25DNXdsTXBDcFBpZVQ4PTwvRW5jcnlwdGVkVGV4dD48S2V5SW5mbz48UmVjZWl2ZXJYNTA5Q2VydFNOPjkwMjE5ODI4MjQ0PC9SZWNlaXZlclg1MDlDZXJ0U04%2BPEVuY3J5cHRlZEtleT5HU242a0l0TlBnOTNibU5sOFYvajVrR1FDZkpGaXg4d1BWbXNoaDREQUo5blY4VUtVbU52dDQ3VjFad1pHaTJSSHM3SnJzdUt6UUVFSmtzYVJZcmxIbjRncWdta05RZTViZjRKVWxCUGtmOUgzeHMxNUp5ZlJqV3VxbGlmOU5Nc3l2djRxU2IrcXN2SkIxUTNuVnRKbmR3RzBBTW15dGdac25oVmI1VEk3L1psZkloSVQ0L3RUSUpvUm1TdmtVY0ZyWEJDK3FsUndoQmZNb0NEU2NtclVnUms2RnUybWlWNFkxbHBZVnhNUkp1c1BuODJuU0dLRVNKQjFqTEgrNmdOQ3c2MmhzWmxVd096eExWbkc3YTRKUUVRMHl0dDVKMkdNWUNIN29PbHZjcXZFdDkvU2xybTU1WGtmY0QrWWlTV2RMQlpKNmgyRkNxQmRrNG80TFVmbnc9PTwvRW5jcnlwdGVkS2V5PjwvS2V5SW5mbz48L1NUU1BhY2thZ2U%2B\"\n" +
                "}";
        ObjectMapper OBJECT_MAPPER = new ObjectMapper();
        WechatOrderComplaintReq wechatOrderComplaintReq = OBJECT_MAPPER.readValue(str, WechatOrderComplaintReq.class);
        allinComplaintService.processAllinWxComplaint(wechatOrderComplaintReq);
    }

    @Test
    public void testWx() {
        wechatOrderComplaintService.processOrderComplaintNotify("200000020241108150224830119", "客户回复", "销售回复客户", 69L);
    }
}

package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.orderservice.req.SwAssignReq;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;

@SpringBootTest
class AuditManageServiceTest {
    @Autowired
    AuditManageService auditManageService;

    @Test
    void test() {
        ArrayList<SwAssignReq> swAssignReqList = new ArrayList<>();
        SwAssignReq build1 = SwAssignReq.builder().orderId(1).operatorId(1).auditorId(1).build();
        SwAssignReq build2 = SwAssignReq.builder().orderId(2).operatorId(2).auditorId(2).build();
        swAssignReqList.add(build1);
        swAssignReqList.add(build2);
    }

    @Test
    void swAudit(){
        auditManageService.swAudit(240,5184);
    }

}

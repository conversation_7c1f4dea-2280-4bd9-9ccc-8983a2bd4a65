package cn.shrise.radium.orderservice.payment;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.orderservice.ext.lklpay.FunctionCodeExtEnum;
import cn.shrise.radium.orderservice.req.lkl.LklWxTransPreOrderReq;
import cn.shrise.radium.orderservice.resp.payment.LklPayConfigInfo;
import cn.shrise.radium.orderservice.util.payment.LklPaySdk;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.lkl.laop.sdk.exception.SDKException;
import com.lkl.laop.sdk.request.V3LabsTransPreorderRequest;
import com.lkl.laop.sdk.request.V3LabsTransRelationRefundRequest;
import com.lkl.laop.sdk.request.model.V3LabsTradeLocationInfo;
import com.lkl.laop.sdk.request.model.V3LabsTradePreorderWechatBus;
import org.junit.jupiter.api.Test;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 */
public class LklPayTest {

//    LklPayConfigInfo initSdk() {
//        String appId = "wxc5c78b8350f6829f";
//        WxPayService service = new WxPayServiceImpl();
//        WxPayConfig payConfig = new WxPayConfig();
//        payConfig.setAppId(appId);
//        payConfig.setMchId("1684006627");
////        payConfig.setMchKey("LaK2sWgX6ONSYn5yGMjd0vt8OWOrKosn");
////        payConfig.setKeyPath("classpath:1684006627.p12");
////        payConfig.setUseSandboxEnv(false);
//        payConfig.setCertSerialNo("7004AFBCB15ECF3994435C856E2638CB045E1D6B");
//        payConfig.setApiV3Key("LaK2sWgX6ONSYn5yGMjd0vt8OWOrKosn");
//        payConfig.setPrivateKeyPath("classpath:1684006627_key.pem");
//        payConfig.setPrivateCertPath("classpath:1684006627_cert.pem");
//        service.setConfig(payConfig);
//    }

    @Test
    void testRefund() throws SDKException {
        try {
            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4093460\",\"priKeyPath\":\"lkl/beta/api_private_key.pem\",\"lklCerPath\":\"lkl/beta/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"lkl/beta/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
            LklPayConfigInfo lklPayConfigInfo = JSON.parseObject(configInfo, LklPayConfigInfo.class);
            V3LabsTransRelationRefundRequest refundRequest = new V3LabsTransRelationRefundRequest();
            refundRequest.setMerchantNo(lklPayConfigInfo.getMerchantNo());
            refundRequest.setTermNo(lklPayConfigInfo.getTermNo());
            refundRequest.setOutTradeNo("tk1854765969801744384");
            refundRequest.setRefundAmount("1");
            refundRequest.setOriginOutTradeNo("sub_1854735310722961408");
            //地址位置信息
            V3LabsTradeLocationInfo locationInfo = new V3LabsTradeLocationInfo("101.80.136.68");
            refundRequest.setLocationInfo(locationInfo);
            LklPaySdk lklPaySdk = new LklPaySdk();
            lklPaySdk.initLklSdk(lklPayConfigInfo);
            lklPaySdk.httpPost(refundRequest, FunctionCodeExtEnum.API_V3_REFUND_FRONT);
        }catch (SDKException sdkException) {
            throw new SDKException(sdkException.getCode(), sdkException.getMessage());
        }catch (Exception e) {
            throw new BusinessException(ExceptionUtil.getStackTrace(e));
        }

    }

    @Test
    void testNormalPay() {
        try {
            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4093460\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
            LklPayConfigInfo lklPayConfigInfo = JSON.parseObject(configInfo, LklPayConfigInfo.class);
            LklWxTransPreOrderReq req = LklWxTransPreOrderReq.builder()
                    .transType("51")
                    .payAmount(1)
                    .orderNum("sub_1889859180048662528X")
                    .requestIp("***************")
                    .skuId(2483)
                    .appId("wxc5c78b8350f6829f")
                    .openId("o7Juk6ubfMHer9AB61Dcoj9TKWg0")
                    .accountType("WECHAT")
                    .build();
            V3LabsTransPreorderRequest v3LabsTransPreorderWechatReq = new V3LabsTransPreorderRequest();
            v3LabsTransPreorderWechatReq.setMerchantNo(lklPayConfigInfo.getMerchantNo());
            v3LabsTransPreorderWechatReq.setTermNo(lklPayConfigInfo.getTermNo());
            v3LabsTransPreorderWechatReq.setOutTradeNo(req.getOrderNum());
            v3LabsTransPreorderWechatReq.setAccountType(req.getAccountType());
            v3LabsTransPreorderWechatReq.setTransType(req.getTransType());
            v3LabsTransPreorderWechatReq.setTotalAmount(req.getPayAmount().toString());
            v3LabsTransPreorderWechatReq.setNotifyUrl("https://callback-api-beta1.shrise.cn/lkl-notify/pay/79");

            //地址位置信息
            V3LabsTradeLocationInfo v3LabsTradeLocationInfo = new V3LabsTradeLocationInfo(req.getRequestIp());
            v3LabsTransPreorderWechatReq.setLocationInfo(v3LabsTradeLocationInfo);

            //微信主扫场景下acc_busi_fields域内容
            V3LabsTradePreorderWechatBus wechatBus = new V3LabsTradePreorderWechatBus();
            wechatBus.setSubAppid(req.getAppId());
            wechatBus.setUserId(req.getOpenId());

            V3LabsTradePreorderWechatBus.AccBusiDetail accBusiDetail = new V3LabsTradePreorderWechatBus.AccBusiDetail();
            List<V3LabsTradePreorderWechatBus.WechatGoodsDetail> wechatGoodsDetails = new ArrayList<>();
            V3LabsTradePreorderWechatBus.WechatGoodsDetail wechatGoodsDetail = new V3LabsTradePreorderWechatBus.WechatGoodsDetail();
            wechatGoodsDetail.setGoodsId(req.getSkuId().toString());
            wechatGoodsDetail.setPrice(req.getPayAmount());
            wechatGoodsDetail.setQuantity(1);
            wechatGoodsDetails.add(wechatGoodsDetail);
            accBusiDetail.setGoodsDetail(wechatGoodsDetails);
            wechatBus.setDetail(accBusiDetail);
            v3LabsTransPreorderWechatReq.setAccBusiFields(wechatBus);

            LklPaySdk lklPaySdk = new LklPaySdk();
            lklPaySdk.initLklSdk(lklPayConfigInfo);
            //聚合主扫
            lklPaySdk.baseRequest(v3LabsTransPreorderWechatReq);
        }catch (Exception e) {
            throw new BusinessException(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void testGenerateDynamicAccount() {
        try {
            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4092647\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
//            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4093460\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
            LklPayConfigInfo lklPayConfigInfo = JSON.parseObject(configInfo, LklPayConfigInfo.class);
            LklPaySdk lklPaySdk = new LklPaySdk();
            lklPaySdk.initLklSdk(lklPayConfigInfo);
            //大额转账
            Map<String, Object> param = new HashMap<>();
            Map<String, Object> reqDataMap = new HashMap<>();
            reqDataMap.put("outBsId", "228");
            reqDataMap.put("mercNo", lklPayConfigInfo.getMerchantNo());
            reqDataMap.put("termNo", lklPayConfigInfo.getTermNo());
            reqDataMap.put("amt", "1");
            reqDataMap.put("effectiveTime", "60");
            reqDataMap.put("notifyUrl", "http://ztm.shrise.cn:8108/lkl-notify/dynamic-account");
//            reqDataMap.put("losNo", "sub_1234567");
            param.put("reqData", reqDataMap);
            param.put("termExtInfo", "{}");
            String response = lklPaySdk.httpPost(FunctionCodeExtEnum.API_APPLY_DYNAMIC_ACCOUNT, JSON.toJSONString(param));
            System.out.println(response);
        }catch (Exception e) {
            throw new BusinessException(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void testQueryDynamicAccount() {
        try {
            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4092647\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
//            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4093460\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
            LklPayConfigInfo lklPayConfigInfo = JSON.parseObject(configInfo, LklPayConfigInfo.class);
            LklPaySdk lklPaySdk = new LklPaySdk();
            lklPaySdk.initLklSdk(lklPayConfigInfo);
            //动态转账账号交易查询
            Map<String, Object> param = new HashMap<>();
            Map<String, Object> reqDataMap = new HashMap<>();
            reqDataMap.put("mercNo", lklPayConfigInfo.getMerchantNo());
            reqDataMap.put("termNo", lklPayConfigInfo.getTermNo());
            //对应动态转账账号（收款账号：accInNo)
            reqDataMap.put("accInNo", "3062025032131853427");
            param.put("reqData", reqDataMap);
            param.put("termExtInfo", "{}");
            String response = lklPaySdk.httpPost(FunctionCodeExtEnum.API_QUERY_DYNAMIC_ACCOUNT, JSON.toJSONString(param));
            System.out.println(response);
        }catch (Exception e) {
            throw new BusinessException(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void testCloseDynamicAccount() {
        try {
            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4092647\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
//            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4093460\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
            LklPayConfigInfo lklPayConfigInfo = JSON.parseObject(configInfo, LklPayConfigInfo.class);
            LklPaySdk lklPaySdk = new LklPaySdk();
            lklPaySdk.initLklSdk(lklPayConfigInfo);
            //动态转账账号交易查询
            Map<String, Object> param = new HashMap<>();
            Map<String, Object> reqDataMap = new HashMap<>();
            reqDataMap.put("merchant_no", lklPayConfigInfo.getMerchantNo());
            reqDataMap.put("term_no", lklPayConfigInfo.getTermNo());
            //对应动态转账账号（收款账号：accInNo)
            reqDataMap.put("acc_in_no", "3062025032431760769");
            reqDataMap.put("out_trade_no", "sub_1234567");
            param.put("req_data", reqDataMap);
            param.put("req_id", "1111");
            param.put("version", "3.0");
            param.put("req_time", "**************");
            String response = lklPaySdk.httpPost(FunctionCodeExtEnum.API_DYNAMIC_ACCOUNT_CLOSE, JSON.toJSONString(param));
            System.out.println(response);
        }catch (Exception e) {
            throw new BusinessException(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void testDynamicAccountRefund() {
        try {
            String configInfo = "{\"appId\":\"OP00002379\",\"serialNo\":\"018d359c6b87\",\"merchantNo\":\"8222900739900XX\",\"termNo\":\"H4093460\",\"priKeyPath\":\"/tmp/pay/wxpay/beta/lakala/api_private_key.pem\",\"lklCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"lklNotifyCerPath\":\"/tmp/pay/wxpay/beta/lakala/lkl-apigw-v1.cer\",\"sm4Key\":\"PXiOgNmZcaP4OS7TOYUO8g\\u003d\\u003d\",\"serverUrl\":\"https://s2.lakala.com\"}";
            LklPayConfigInfo lklPayConfigInfo = JSON.parseObject(configInfo, LklPayConfigInfo.class);
            LklPaySdk lklPaySdk = new LklPaySdk();
            lklPaySdk.initLklSdk(lklPayConfigInfo);

            V3LabsTransRelationRefundRequest refundRequest = new V3LabsTransRelationRefundRequest();
            refundRequest.setMerchantNo(lklPayConfigInfo.getMerchantNo());
            refundRequest.setTermNo(lklPayConfigInfo.getTermNo());
            // 对应动态转账（acpNo：拉卡拉交易流水号）
//            refundRequest.setOutTradeNo("51210209747812");
            // （商户请求流水号：表示唯一）
            refundRequest.setOutTradeNo("222222");
            // 对应动态转账（outBsId：原商户请求流水）
            refundRequest.setOriginOutTradeNo("115");
            refundRequest.setRefundAmount("1");
            //地址位置信息
            V3LabsTradeLocationInfo locationInfo = new V3LabsTradeLocationInfo("101.80.136.68");
            refundRequest.setLocationInfo(locationInfo);
            lklPaySdk.httpPost(refundRequest, FunctionCodeExtEnum.API_V3_REFUND_FRONT);
        }catch (Exception e) {
            throw new BusinessException(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void test1() {
        String response = "{\"code\":\"000000\",\"msg\":\"通讯成功\",\"resp_data\":{\"trade_state\":\"FAIL\",\"refund_type\":\"ALL\",\"merchant_no\":\"8222900739900D9\",\"out_trade_no\":\"tk1894684799697149952\",\"trade_no\":\"202502271101100012**************\",\"log_no\":\"**************\",\"acc_trade_no\":\"\",\"account_type\":\"WECHAT\",\"total_amount\":\"69900\",\"refund_amount\":\"69900\",\"payer_amount\":\"69900\",\"trade_time\":\"**************\",\"origin_trade_no\":\"202402281101131302**************\",\"origin_out_trade_no\":\"sub_1762650499602247680\",\"origin_log_no\":\"**************\",\"up_coupon_info\":\"\",\"channel_ret_desc\":\"RFD11103#超期订单不允许退款\"},\"resp_time\":\"**************\"}";
//        String response = "{\"code\":\"000000\",\"msg\":\"通讯成功\",\"resp_time\":\"**************\"}";
        JSONObject res = JSON.parseObject(response);
        if (Objects.equals(res.getString("code"), "BBS00000") || Objects.equals(res.getString("code"), "000000")) {
            JSONObject resData = JSON.parseObject(res.getString("resp_data"));
            if (ObjectUtil.isNotEmpty(resData) && Objects.equals(resData.getString("trade_state"), "SUCCESS")) {
                System.out.println("成功-----" + res);
            }else {
                System.out.println("失败-----" + res);
            }
        }
    }
}

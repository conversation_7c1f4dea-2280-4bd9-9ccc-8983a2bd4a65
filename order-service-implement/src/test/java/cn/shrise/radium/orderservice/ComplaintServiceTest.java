package cn.shrise.radium.orderservice;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.shrise.radium.common.util.HttpUtils;
import cn.shrise.radium.orderservice.ext.wechatpay.bean.ComplaintDetailResultExt;
import cn.shrise.radium.orderservice.ext.wechatpay.bean.ImageMediaResult;
import cn.shrise.radium.orderservice.ext.wechatpay.bean.NegotiationHistoryResultExt;
import cn.shrise.radium.orderservice.ext.wechatpay.service.ComplaintServiceExtImpl;
import cn.shrise.radium.orderservice.req.complaint.WechatOrderComplaintReq;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.aliyun.oss.OSS;
import com.github.binarywang.wxpay.bean.complaint.ComplaintDetailRequest;
import com.github.binarywang.wxpay.bean.complaint.ComplaintNotifyUrlRequest;
import com.github.binarywang.wxpay.bean.complaint.NegotiationHistoryRequest;
import com.github.binarywang.wxpay.bean.complaint.ResponseRequest;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
//@SpringBootTest
public class ComplaintServiceTest {

    @Autowired
    OSS ossClient;

    WxPayService initWxPayService() {
        String appId = "wxc5c78b8350f6829f";
        WxPayService service = new WxPayServiceImpl();
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setAppId(appId);
        payConfig.setMchId("1684006627");
//        payConfig.setMchKey("LaK2sWgX6ONSYn5yGMjd0vt8OWOrKosn");
//        payConfig.setKeyPath("classpath:1684006627.p12");
//        payConfig.setUseSandboxEnv(false);
        payConfig.setCertSerialNo("7004AFBCB15ECF3994435C856E2638CB045E1D6B");
        payConfig.setApiV3Key("LaK2sWgX6ONSYn5yGMjd0vt8OWOrKosn");
        payConfig.setPrivateKeyPath("classpath:1684006627_key.pem");
        payConfig.setPrivateCertPath("classpath:1684006627_cert.pem");
        service.setConfig(payConfig);
        return service;
    }

    @Test
    void testWechatComplaint() throws WxPayException {
        WxPayService service = initWxPayService();
        ComplaintNotifyUrlRequest request = ComplaintNotifyUrlRequest.newBuilder()
                .url("")
                .build();
//        service.getComplaintsService().updateComplaintNotifyUrl(request);
        service.getComplaintsService().getComplaintNotifyUrl();
        service.queryOrder(null, "sub_1848258044643909632");
    }

    @Test
    void testDecrypt() throws WxPayException {
        String msg = "{\"merchantId\":69,\"notifyBody\":\"{\\\"id\\\":\\\"f13f152f-f96c-522d-8815-1cd01db2bac9\\\",\\\"create_time\\\":\\\"2024-11-06T11:20:39+08:00\\\",\\\"resource_type\\\":\\\"encrypt-resource\\\",\\\"event_type\\\":\\\"COMPLAINT.CREATE\\\",\\\"summary\\\":\\\"新投诉生成\\\",\\\"resource\\\":{\\\"original_type\\\":\\\"complaint\\\",\\\"algorithm\\\":\\\"AEAD_AES_256_GCM\\\",\\\"ciphertext\\\":\\\"sLaPYyHGqIYp4uxrvQXZOr1ArtoycM6e8RmokZ5/BNgd+1LHDn+1UcTP9ApiyQV/I6JHSfj24GuPxdCeg4ZiloLdsoLz3T9DIr+tx9wNrYM2ghDpu68elyfs5Cpq2X07TA2OVhek4mne2v3Y+FBje7UcgNDh5UC4xwprUhIW3DOyFdt2wfmq39h1dh5oPVJsBOgoaeG9KR3adwJX0+O2FX8qSsucJgVF55feyw55VEnaJEtmONLDF7gZaqz53hwOyGMAV7SLR1OwYEFFI4059J1GUdLkgergRu1mAfGLOc7DxHiuIULsMHni8daoso8GFmHOpVOeDrFmRZ1UiiKicWqF2qJ6jJNLiJa9AjHfCS4VbIF3XlXR9Ix52h9/lwEXDVu1AdwScqgyIY2KRntBuMpgFyrPDpBhFhB5Me6RzdrLjvMWT0lYaMusNB/9D6TVLpzc9Eu4umxuP9J3bYgfth++kf6BGXs5Z/YTDR9ilMGeS4szb+3LiHvnBAGMyYqe1nIGQB5KyiSh2czMaKoBCQXCqGEdLrbEh5Fd\\\",\\\"associated_data\\\":\\\"complaint\\\",\\\"nonce\\\":\\\"aM1f4dG06pkT\\\"}}\"}";
        WechatOrderComplaintReq req = JSON.parseObject(msg, WechatOrderComplaintReq.class);
        WxPayService wxPayService = initWxPayService();
        wxPayService.parseComplaintNotifyResult(req.getNotifyBody(), null);
    }

    @Test
    void testComplaintInfo() {
        WxPayService wxPayService = initWxPayService();
        try {
//            ComplaintServiceExtImpl complaintServiceExt = (ComplaintServiceExtImpl) wxPayService.getComplaintsService();
            ComplaintServiceExtImpl complaintServiceExt = new ComplaintServiceExtImpl(wxPayService);
            ComplaintDetailRequest request = ComplaintDetailRequest.newBuilder()
                    .complaintId("200000020241106110224249967")
                    .build();
            ComplaintDetailResultExt complaintInfo = complaintServiceExt.getComplaintExt(request);
            if (ObjectUtil.isEmpty(complaintInfo)) {
                return;
            }
        } catch (Exception e) {
            System.out.println(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void testComplaintNegotiationHistory() {
        int current = 1;
        int size = 50;
        NegotiationHistoryRequest request = NegotiationHistoryRequest.newBuilder()
                .complaintId("200000020241106110224249967")
                .offset((current - 1) * size)
                .limit(size)
                .build();
        try {
            WxPayService wxPayService = initWxPayService();
            ComplaintServiceExtImpl complaintServiceExt = new ComplaintServiceExtImpl(wxPayService);
            NegotiationHistoryResultExt historyResult = complaintServiceExt.queryNegotiationHistorysExt(request);
            if (ObjectUtil.isEmpty(historyResult)) {
                if (ObjectUtil.isEmpty(historyResult.getData())) {
                    return;
                }
            }
        } catch (Exception e) {
            System.out.println(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void testCustomerReply() {
        try {
            WxPayService wxPayService = initWxPayService();
            ComplaintServiceExtImpl complaintServiceExt = new ComplaintServiceExtImpl(wxPayService);
            ResponseRequest request = ResponseRequest.newBuilder()
                    .complaintId("200000020241106110224249967")
                    .complaintedMchid(wxPayService.getConfig().getMchId())
                    .responseContent("稍微回复一下意思意思")
                    .build();
            complaintServiceExt.submitResponse(request);
        } catch (Exception e) {
            System.out.println(ExceptionUtil.getStackTrace(e));
        }
    }

    @Test
    void testJson() {
        String response = "{\"complaint_detail\":\"测试上分原生微信，测试\",\"complaint_full_refunded\":false,\"complaint_id\":\"200000020241108150224830119\",\"complaint_media_list\":[{\"media_type\":\"USER_COMPLAINT_IMAGE\",\"media_url\":[\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYACCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYASCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYAiCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYAyCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYBCCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYBSCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYBiCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYByCq%2BLa5BigBMAE4AQ%3D%3D\",\"https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNTAyMjQ4MzAxMTkYCCCq%2BLa5BigBMAE4AQ%3D%3D\"]}],\"complaint_order_info\":[{\"amount\":1,\"out_trade_no\":\"sub_1854785874110451712\",\"transaction_id\":\"4200002435202411083053557831\"}],\"complaint_state\":\"PENDING\",\"complaint_time\":\"2024-11-08T15:22:14+08:00\",\"in_platform_service\":false,\"incoming_user_response\":false,\"need_immediate_service\":false,\"payer_openid\":\"o7Juk6o4xGXnGv_lbnLUuD-zE8WM\",\"payer_phone\":\"IH0whJ2Dk2uOIbvwgxaofXvII/UNenrN3Ij6WnkN9j/Kdrnw2RCQC8c9EGM4ZIf0fbnvVuxbCHr0eHcUeGryvJM1rB/m48gK99OEhqzJz6LjCcB3xg7TD5wO1VQAaNDV93A3+ND2QPjv5dlglLHw5blUwZOa+mGMpktEPc3gG+9Rcz6rUN30Aete4C+jDF1EduRffSTUsy1B9bdSVbw++BBVrNcqsmv3jx7snPdQAuRXiwYg1fSr1r6hmR/2cdUn4tQ88jq85otifWaDWCvm7EfQY3em+kUf6OBP9c7qGrFSmj5KyZ350Pq6AiLrVcr0Db4b2MNGIhqlbHrpdrU7mA\\u003d\\u003d\",\"problem_description\":\"账户被莫名扣费\",\"problem_type\":\"OTHERS\",\"service_order_info\":[],\"user_complaint_times\":1,\"user_tag_list\":[]}";
        ComplaintDetailResultExt result = JSON.parseObject(response, ComplaintDetailResultExt.class);
    }

    public byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    @Test
    void testMediaUrl() throws UnsupportedEncodingException {
        WxPayService wxPayService = initWxPayService();
        String mediaUrl = "https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyNDExMDgxNzAyMjQ4NDU1NjkYACCTs8G5BigBMAE4AQ%3D%3D";
        String[] mediaUrls = URLDecoder.decode(mediaUrl, StandardCharsets.UTF_8.name()).split("/");
        String mediaId = mediaUrls[mediaUrls.length-1];
        ComplaintServiceExtImpl complaintServiceExt = new ComplaintServiceExtImpl(wxPayService);
        try {
            InputStream inputStream = complaintServiceExt.getImage(mediaId);
//            File file = new File(response);
//            FileInputStream inputStream = new FileInputStream(file);
//            URL url = new URL(mediaUrl);
//            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
//            conn.setConnectTimeout(3 * 1000);
//            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
//            InputStream inputStream = conn.getInputStream();
//            byte[] getData = readInputStream(inputStream);
//            String id = IdUtil.nanoId(8);
            ossClient.putObject("gs-file-src", "test/aaa" + ".jpg", inputStream);
        } catch (Exception e) {
            System.out.println(ExceptionUtil.getStackTrace(e));
        }
    }

}

package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.orderservice.resp.Result;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class IdCardVerifyServiceTest {

    @Autowired
    private IdCardVerifyService idCardVerifyService;

    @Test
    void verifyTest() {
        int index = 0;
        while (true) {
            Result result = idCardVerifyService.verify("522325200301020813", "万志华", "18208653452");
            index++;
            if (index == 10) {
                return;
            }
            System.out.println(result.getCode() + "code" + result.getMessage());
        }
    }

}
package cn.shrise.radium.orderservice.service.pay;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class NotifyOrderProcessTest {
    @Autowired
    private NotifyOrderProcess notifyOrderProcess;

    @Test
    void refundSendNotify() {
        /*List<RefundContactInfoResp> refundContactInfoResps = notifyOrderProcess.refundSendNotify(353);
        System.out.println(refundContactInfoResps);*/
    }
}
package cn.shrise.radium.orderservice.union.service;

import cn.shrise.radium.orderservice.conf.bean.MultiUnionPayService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

@SpringBootTest
class UnionServiceTest {

    @Autowired
    private UnionPayService unionPayService;

    @Autowired
    private MultiUnionPayService multiUnionPayService;

    @Test
    void pay() {
        String pay = unionPayService.pay("https://callback-api-beta.shrise.cn/allinpay/notify/order/companyType/45/mchType/400001", "https://www.baidu.com", "0104", "银联支付支付测试", "sub_7777772", 1);
        System.out.println(pay);
    }

    @Test
    void refund() {
        String pay = unionPayService.refund(1, "refund1234", "sub_7777777");
        System.out.println(pay);
    }

    @Test
    void query() {
        String pay = unionPayService.query("sub_7777777");
        System.out.println(pay);
    }

    @Test
    public void allinPay() {
        String s = "acct=6217880800014124780&acctname=%E5%B4%94%E6%99%94&accttype=00&appid=00254327&cusid=56216107372AZC6&cusorderid=sub_7777775&fee=1&initamt=132&outtrxid=sub_7777775&paytime=20221107101833&sign=hn0brOGM1RKc9oRR6YX5DKd%2FlHXxyO5LC5y2bw8BXD%2B1SBtbWfNlbkUQv%2BOKUxyjmnfbvCfCZ8uVlxND%2B2FnirdyQ4dtszOlp%2BlL7yX8E0B7HQOJhVbm%2FdocPz9hJpVDstl4%2FIdLTJ2IJsFp4WsyZTMNdt3b1gGAtlwpZBlWPts%3D&signtype=RSA&termauthno=2022110703048218299262220310508&termrefnum=2211071001010104821829926222&termtraceno=0&trxamt=132&trxcode=VSP535&trxdate=20221107&trxid=221107121829926222&trxstatus=0000";
//        String str = "acct=6217560800041957670&acctname=%E4%B8%87%E5%BF%97%E5%8D%8E&accttype=00&appid=00254327&cusid=56216107372AZC6&cusorderid=sub_test123&fee=0&initamt=1&outtrxid=sub_test123&paytime=20221104153932&sign=TfH9YtSDXQxHwmDZ33ly7BfRBMb8kflB7Xzdo8y1TXdPQp5rOdCyiTuMzzTlbB3%2B2m9UoXRQC4vXJK83Z7QBDdXkHic8mOZ7VzRYE0HfvEh5%2FO0fxibij5XqmQxOt2w679KzrW9HhNwH1ILoQDtxl9ifoNPSjPutffkw5OKkpi0%3D&signtype=RSA&termauthno=2022110403065254297204520110708&termrefnum=2211041501010106525429720452&termtraceno=0&trxamt=1&trxcode=VSP535&trxdate=20221104&trxid=221104125429720452&trxstatus=0000";
        unionPayService.handleResult(s);
    }

    @Test
    void verifySign() {
        String result = "acct=6217560800041957670&acctname=%E4%B8%87%E5%BF%97%E5%8D%8E&accttype=00&appid=00254327&cusid=56216107372AZC6&cusorderid=sub_7777772&fee=0&initamt=1&outtrxid=sub_7777772&paytime=20221107130921&sign=hTT6ND8YN%2FOOII6z%2BUCiGyex8GOZSHZStd0i4S1upJ7GWhfRM8g59KXxqLF8hdFiSOJDV3uZHdPSA02vaVuaXQ%3D%3D&signtype=SM2&termauthno=2022110703078211299240200200408&termrefnum=2211071301000107821129924020&termtraceno=0&trxamt=1&trxcode=VSP535&trxdate=20221107&trxid=221107121129924020&trxstatus=0000";
        Map<String, String> map = unionPayService.handleResult(result);
    }

    @Test
    void getConfig() {
        UnionPayService service = multiUnionPayService.switchoverTo(45, 400001);
        String pay = service.query("sub_7777777");
        System.out.println(pay);
    }
}
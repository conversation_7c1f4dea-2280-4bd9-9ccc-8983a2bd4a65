package cn.shrise.radium.orderservice.service.pay;

import cn.shrise.radium.common.util.GsonUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.orderservice.conf.AlipayConfiguration;
import cn.shrise.radium.orderservice.conf.bean.MultiAlipayService;
import cn.shrise.radium.orderservice.entity.RsCourseRefundOrder;
import cn.shrise.radium.orderservice.properties.AlipayProperties;
import cn.shrise.radium.orderservice.util.GenNumberUtils;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.*;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class AlipaymentServiceTest {


    @Autowired
    AlipayProperties properties;

    @Autowired
    AlipayConfiguration configuration;

    @Autowired
    RocketMqUtils rocketMqUtils;

    @Autowired
    AlipayService alipayService;

    AlipayClient alipayClient;

    AlipayConfig alipayConfig;

    @Autowired
    private MultiAlipayService multiAlipayService;

    @BeforeEach
    public void init(){
        alipayClient = configuration.getAlipayClientByCt(45);
        alipayConfig = configuration.getAlipayConfigByCt(45);
    }

    @Test
    public void test() throws AlipayApiException {

        System.out.println(GsonUtils.toJson(properties));
        System.out.println(GsonUtils.toJson(alipayConfig));

        System.out.println("app_cert_sn: " + AlipaySignature.getCertSN(alipayConfig.getAppCertPath()));
        System.out.println("alipay_root_cert_sn: " + AlipaySignature.getCertSN(alipayConfig.getRootCertPath()));
        System.out.println("私钥：\r\n" + alipayConfig.getPrivateKey());
        System.out.println("公钥：\r\n" + AlipaySignature.getAlipayPublicKey(alipayConfig.getAppCertPath()));
    }

    @Test
    public void sign() throws AlipayApiException {
        Map<String, String> param = new HashMap<>();
        param.put("alipay_root_cert_sn", AlipaySignature.getCertSN(alipayConfig.getRootCertPath()));
        param.put("app_cert_sn", AlipaySignature.getCertSN(alipayConfig.getAppCertPath()));
        param.put("app_id", alipayConfig.getAppId());
        param.put("charset", "utf-8");
        param.put("method", "alipay.mobile.public.menu.add");
        param.put("sign_type", "RSA2");
        param.put("timestamp", "2014-07-24 03:07:50");
        param.put("version", "1.0");

        String a = AlipaySignature.rsaSign(param, alipayConfig.getPrivateKey(), "utf8");
        System.out.println(alipayConfig.getPrivateKey());
        System.out.println(a);
    }

    /**
     * 电脑网站支付
     * @throws AlipayApiException
     */
    @Test
    public void createPcOrder() throws AlipayApiException {
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        request.setNotifyUrl("http://localhost:8108/alipay/notify/order/45");
        request.setReturnUrl("http://localhost:8108/alipay/return/order/45");
        JSONObject bizContent = new JSONObject();
        String number = GenNumberUtils.genOrderNumber(20);
        bizContent.put("out_trade_no", number);
        bizContent.put("total_amount", 1);
        bizContent.put("subject", "测试商品");
        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
        request.setBizContent(bizContent.toString());
        AlipayTradePagePayResponse response = alipayClient.pageExecute(request);
        if(response.isSuccess()){
            String head = "<html><head><meta http-equiv='Content-Type' content='text/html;charset=UTF-8'></head>";
            String bottom = "<body></body></html>";
            String content = head + response.getBody() + bottom;
            System.out.println(content);
            createFile("test.html", content);
            System.out.println("调用成功:" + number);
        } else {
            System.out.println("调用失败");
        }
    }
    /**
     * 手机网站支付
     * @throws AlipayApiException
     */
    @Test
    public void createWapOrder() throws AlipayApiException {
        AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
//        request.setNotifyUrl("http://localhost:8108/alipay/notify/order/45");
//        request.setReturnUrl("http://localhost:8108/alipay/return/order/45");
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", GenNumberUtils.genOrderNumber(20));
        bizContent.put("total_amount", 0.01);
        bizContent.put("subject", "测试商品");
        bizContent.put("product_code", "QUICK_WAP_WAY");
        request.setBizContent(bizContent.toString());
        AlipayTradeWapPayResponse response = alipayClient.pageExecute(request);
        if(response.isSuccess()){
            String head = "<html><head><meta http-equiv='Content-Type' content='text/html;charset=UTF-8'></head>";
            String bottom = "<body></body></html>";
            String content = head + response.getBody() + bottom;
            System.out.println(content);
            createFile("test.html", content);
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

    @Test
    public void createFile(String fileName, String content){
        try {
            BufferedWriter out = new BufferedWriter(new FileWriter(fileName));
            out.write(content);
            out.close();
            System.out.println("文件创建成功！");
        } catch (IOException e) {
            System.out.println(e);
        }
    }

    /**
     * 发起退款
     * @throws AlipayApiException
     */
    @Test
    public void refundOrder() throws AlipayApiException {
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("trade_no", "2022021422001497470502395553");
        bizContent.put("refund_amount", 0.01);
        bizContent.put("out_request_no", GenNumberUtils.genRefundOrderNumber(20));

        request.setBizContent(bizContent.toString());
        AlipayTradeRefundResponse response = alipayClient.certificateExecute(request);
        System.out.println(GsonUtils.toJson(response));
        if(response.isSuccess()){
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

    @Test
    public void queryOrder() throws AlipayApiException {
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", "ap20220214134419nbheli");
        //bizContent.put("trade_no", "2014112611001004680073956707");
        request.setBizContent(bizContent.toString());
        AlipayTradeQueryResponse response = alipayClient.certificateExecute(request);
        if(response.isSuccess()){
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

    @Test
    public void handleRefundOrderTest(){
        RsCourseRefundOrder r = new RsCourseRefundOrder();
        r.setId(1526);
        alipayService.refundOrder(r);
//        rocketMqUtils.producerFunction(OrderRocketMqNameConst.PAY_NOTIFY_TOPIC, r, OrderRocketMqNameConst.TagType.ALI_ORDER_REFUND);
    }

    @Test
    public void testCheckSign() throws UnsupportedEncodingException, AlipayApiException {
        String body = "gmt_create=2022-06-13+09%3A40%3A17&charset=utf-8&seller_email=cneewx4431%40sandbox.com&subject=aaa&sign=Eeti8bqJ0DMn48PaOsFSPtAD4cD4wVIr2knQb%2Bb1%2FiY1bYEMXfgq6WsXITiMaQfgPW7snhOtbsl%2Fl0Cm1FkkgHCfgOlwWgkLewnoaXwgBHFX73VC0PS%2Bthrm3m1CIxNAoZyFf7j6UkZxHt5NHGvaAlp5rlhxxti99Z6EVUv7yqaZViZcJdogDUTvgQ1YFxPPwihprbJjkqLsJY0RuYMJgloy9AkF0eCkvU3whbXzlofkWa0nn7hnCp7NgPWi2dLba0MBWTF9A2AcQH6fTouRHD9nQrjhklMeOsCETpMA5Tc8H15Xm5wgEfGzTHq2C81QXpFocj4UBYyQva1bvyZwag%3D%3D&buyer_id=****************&invoice_amount=0.01&notify_id=2022061300222094019097470523798783&fund_bill_list=%5B%7B%22amount%22%3A%220.01%22%2C%22fundChannel%22%3A%22ALIPAYACCOUNT%22%7D%5D&notify_type=trade_status_sync&trade_status=TRADE_SUCCESS&receipt_amount=0.01&buyer_pay_amount=0.01&app_id=****************&sign_type=RSA2&seller_id=****************&gmt_payment=2022-06-13+09%3A40%3A18&notify_time=2022-06-13+09%3A40%3A20&version=1.0&out_trade_no=sub_1536159127444992000&total_amount=0.01&trade_no=2022061322001497470502528732&auth_app_id=****************&buyer_logon_id=twv***%40sandbox.com&point_amount=0.00";
        final DefaultSignChecker signChecker = (DefaultSignChecker) multiAlipayService.switchoverTo(45, 200001).getSignChecker();

        String decodeBody = URLDecoder.decode(body, StandardCharsets.UTF_8.name());

        final List<NameValuePair> segments = URLEncodedUtils.parse(body, StandardCharsets.UTF_8);
        Map<String, String> map = new HashMap<>(segments.size());
        segments.forEach(pair -> {
            String name = pair.getName();
            String value = pair.getValue();
            map.put(name, value);
        });
        final DefaultAlipayClient defaultAlipayClient = multiAlipayService.switchoverTo(45,200001);
        final String alipayPublicKey = signChecker.getAlipayPublicKey();
        boolean flag = AlipaySignature.rsaCheckV1(map, alipayPublicKey, alipayConfig.getCharset(), alipayConfig.getSignType());
        assertTrue(flag);
    }

}
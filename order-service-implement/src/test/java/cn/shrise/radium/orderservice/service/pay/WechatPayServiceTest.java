package cn.shrise.radium.orderservice.service.pay;

import cn.shrise.radium.orderservice.conf.bean.MultiWxPayService;
import cn.shrise.radium.orderservice.properties.WxPayProperties;
import cn.shrise.radium.orderservice.req.WechatPayNotifyReq;
import com.alibaba.fastjson.JSON;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.UUID;

@SpringBootTest
class WechatPayServiceTest {
    @Autowired
    WechatPayService wechatPayService;

    @Autowired
    WxPayProperties properties;

    @Autowired
    WxPayService wxPayService;

    @Autowired
    MultiWxPayService multiWxPayService;

    @Test
    void getPayConfig(){
        WxPayProperties.Config config = properties.getPayPropertiesByMchType(1000);
        System.out.println(config);
    }

    @Test
    void orderNotifyHandle() {
        String xml = "<xml><appid><![CDATA[wxb4c93f7f76ac073d]]></appid>\n" +
                "<bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                "<cash_fee><![CDATA[1]]></cash_fee>\n" +
                "<fee_type><![CDATA[CNY]]></fee_type>\n" +
                "<is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "<mch_id><![CDATA[**********]]></mch_id>\n" +
                "<nonce_str><![CDATA[uwwdmqzu8vhhly62z8462kmrri7os00j]]></nonce_str>\n" +
                "<openid><![CDATA[oHbhR6C3-Eu79eBiPSxBNomp4u6M]]></openid>\n" +
                "<out_trade_no><![CDATA[wx20220315093412g5knm9]]></out_trade_no>\n" +
                "<result_code><![CDATA[SUCCESS]]></result_code>\n" +
                "<return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "<sign><![CDATA[F4173BE4617FD30A8B6E3FCBB6FC9F20]]></sign>\n" +
                "<time_end><![CDATA[**************]]></time_end>\n" +
                "<total_fee>1</total_fee>\n" +
                "<trade_type><![CDATA[JSAPI]]></trade_type>\n" +
                "<transaction_id><![CDATA[4200001386202203159867642920]]></transaction_id>\n" +
                "</xml>";
        wechatPayService.orderNotifyHandle(1099, xml);
    }

    @Test
    void prodTest(){
        String xml = "<xml><appid><![CDATA[wx73e85c0957bdab47]]></appid>\n" +
                "<bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                "<cash_fee><![CDATA[68800]]></cash_fee>\n" +
                "<fee_type><![CDATA[CNY]]></fee_type>\n" +
                "<is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "<mch_id><![CDATA[**********]]></mch_id>\n" +
                "<nonce_str><![CDATA[vn47e5ht6s2t3xgigfv26ut2216krwtn]]></nonce_str>\n" +
                "<openid><![CDATA[oOD1s5w6jLTg5ov6sXAniHSkACNE]]></openid>\n" +
                "<out_trade_no><![CDATA[wx20220314233157leiyp9]]></out_trade_no>\n" +
                "<result_code><![CDATA[SUCCESS]]></result_code>\n" +
                "<return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "<sign><![CDATA[A9E8E39A37CD9DC1B9AADEE288322CD4]]></sign>\n" +
                "<time_end><![CDATA[**************]]></time_end>\n" +
                "<total_fee>68800</total_fee>\n" +
                "<trade_type><![CDATA[JSAPI]]></trade_type>\n" +
                "<transaction_id><![CDATA[4200001304202203158083094823]]></transaction_id>\n" +
                "</xml>";
        WxPayProperties.Config config = properties.getPayPropertiesByAt(1099);
        wxPayService.switchoverTo(config.getAppId());
        try {
            final WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xml);
            System.out.println(notifyResult);
        }catch(Exception e){
            e.printStackTrace();
        }

    }

    @Test
    void refundAnotherOpenId() throws WxPayException {
        WxPayService payService = multiWxPayService.switchoverTo(45, 1000, "wx4f94c3b33fe9d35f");
        WxPayRefundRequest request = new WxPayRefundRequest();
        request.setOutTradeNo("sub_1739105639852531712");
        request.setOutRefundNo(UUID.randomUUID().toString());
        request.setTotalFee(1);
        request.setRefundFee(1);

        WxPayRefundResult refund = payService.refund(request);
        System.out.println(refund);
    }

    @Test
    void testRefundNotify() throws WxPayException {
        final WechatPayNotifyReq wechatPayNotifyReq = JSON.parseObject("{\"accountType\":1000,\"companyType\":45,\"mchType\":1000,\"xmlData\":\"<xml><return_code>SUCCESS</return_code><appid><![CDATA[wx047c729e91070b37]]></appid><mch_id><![CDATA[**********]]></mch_id><nonce_str><![CDATA[ca556ffcf9006854861234267dd4b3ef]]></nonce_str><req_info><![CDATA[bY+zNAO/1yVM/yXqQypTde+tN7Su8pT4TwAM/vUGSHa9zRe3qvuSctLtj/Md1T3VsMPXkREEQt0T5EKx3WawxwO/DRVGD39BOmREHO5jmK32xGeYts43q3yKEb7SBdgfzERj+psAtAgFOQNTt+O+TkkYXW83G+abt7NBqA1L8M057CeRu0FZVxPI1BY16TxCjTyoCGrwg074eq+KQwlekiyL1YKx7mgABzytJ4jp7YF8joaZu911M4T+/6OrTgMjUn3jmbb19e7YlnOGO79W5d3xdAMxoHENTqTBOEumBLPhbex43u9j1sjLPjnGo23fCPLOeYylHp5YnWDdE95ANk6zSuORBZQZNbAk0LxxDCK55QvbKJPZ/7lc15jSTgVPecqGub3JU3d+eHPVzzjsf+koZfXZDOyMVERLUfW3SvrJgHq5eBmp3D5ScZHyns0goC2MGk0B8+4C0UU1y+x9CFIk+53LMgiRDXeguUivgoTdNdNMFr+Pq2eZrbcc6iQmOFakjGIqD0xjG7dhFeHODUIWy5zv8K1pfif8oU9+6TlWUVX5I6EO1vH0So7PyrUn83WtaU/MmuQ2RIGErlkMCn1BatXuOOrDo+KepAhOXqQlBl7FcmF0lwcgshCB89d5NbgMjZhQjIuYrh00IvKbCgL2192RQyLrt7KLzDwuqE7nJ5zLxGu2hkAiJls9rm5W/KUk6DPbgxEk2myHud9+xrTnCktejyz5GY5dYfZQFagSoxo7omhp2dD+iukHJj95NbfcTGd9sEfcaRP1CbvhSmSuxlaOQZjjJgbfDQ3UoQKf3aOxH+TuY4hyxQjJ0j/0Y63yFuIIiiFuCw0ng275VMKRLo5vuQT6pALMym/EUAjttJDCxGiTqxs6GhaeW3lOPb3Hxe0c+hTtVRcI7gMxx2Raq8few90DtdwLjfuEPB+zXj4gwd9y5Cr9ojhj4hLIr8LAAKT8dDbLYto93MtEIpGlSboTjWYa2aACHf3UPKGPdIxl9GJNsUh4oaT4CrWxl8tBxaeI4waJ4HTHkO9KKb2wP5f3uKz+BjuMuriHsHxXLPlSzJBWLjaWL5eJxdAs]]></req_info></xml>\"}", WechatPayNotifyReq.class);
        wechatPayService.handleRefundNotify(wechatPayNotifyReq);
    }

    @Test
    void test22() throws WxPayException {
        WechatPayNotifyReq req = WechatPayNotifyReq.builder()
                .accountType(2099)
                .companyType(45)
                .mchType(900)
                .build();
        wechatPayService.handlePayNotify(req);
    }
}
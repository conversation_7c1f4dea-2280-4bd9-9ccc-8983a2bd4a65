package cn.shrise.radium.orderservice;

import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.exception.SDKException;
import com.lkl.laop.sdk.request.*;
import com.lkl.laop.sdk.request.model.V3LabsTradeLocationInfo;
import com.lkl.laop.sdk.request.model.V3LabsTradePreorderAlipayBus;
import com.lkl.laop.sdk.request.model.V3LabsTradePreorderWechatBus;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class LklTest {

    @Test
    void createOrder() throws SDKException {
        //2. 装配数据
        /*** 微信主扫场景示例 */
//        V3LabsTransPreorderRequest v3LabsTransPreorderWechatReq = new V3LabsTransPreorderRequest();
//        v3LabsTransPreorderWechatReq.setMerchantNo("8225840739900EH");
//        v3LabsTransPreorderWechatReq.setTermNo("H1203603");
//        v3LabsTransPreorderWechatReq.setOutTradeNo("sub_2024012610000028");
//        v3LabsTransPreorderWechatReq.setAccountType("WECHAT");
//        v3LabsTransPreorderWechatReq.setTransType("51");
//        v3LabsTransPreorderWechatReq.setTotalAmount("2");
//        v3LabsTransPreorderWechatReq.setNotifyUrl("https://callback-api-beta.shrise.cn/lkl/trade/notify");
//        v3LabsTransPreorderWechatReq.setRemark("备注");
//
//        //地址位置信息
//        V3LabsTradeLocationInfo v3LabsTradePreorderLocationInfo1 = new V3LabsTradeLocationInfo("**************");
//        v3LabsTransPreorderWechatReq.setLocationInfo(v3LabsTradePreorderLocationInfo1);
//
//        //微信主扫场景下acc_busi_fields域内容
//        V3LabsTradePreorderWechatBus wechatBus = new V3LabsTradePreorderWechatBus();
//        wechatBus.setSubAppid("wx2ec3de0ae4dffee4");
//        wechatBus.setUserId("ot1KA6j0xDELsI99JgidxDpZoZac");
//
//        V3LabsTradePreorderWechatBus.AccBusiDetail accBusiDetail = new V3LabsTradePreorderWechatBus.AccBusiDetail();
//        List<V3LabsTradePreorderWechatBus.WechatGoodsDetail> wechatGoodsDetails = new ArrayList<>();
//        V3LabsTradePreorderWechatBus.WechatGoodsDetail wechatGoodsDetail = new V3LabsTradePreorderWechatBus.WechatGoodsDetail();
//        wechatGoodsDetail.setGoodsId("100734033");
//        wechatGoodsDetail.setPrice(1);
//        wechatGoodsDetail.setQuantity(1);
//        wechatGoodsDetails.add(wechatGoodsDetail);
//        accBusiDetail.setGoodsDetail(wechatGoodsDetails);
//        wechatBus.setDetail(accBusiDetail);
//        v3LabsTransPreorderWechatReq.setAccBusiFields(wechatBus);


        /*** 支付宝主扫场景示例 */
        V3LabsTransPreorderRequest v3LabsTransPreorderAlipayReq = new V3LabsTransPreorderRequest();
        v3LabsTransPreorderAlipayReq.setMerchantNo("8225840739900EH");
        v3LabsTransPreorderAlipayReq.setTermNo("H1203603");
        v3LabsTransPreorderAlipayReq.setOutTradeNo("sub_2024030510000030");
        v3LabsTransPreorderAlipayReq.setAccountType("ALIPAY");
        v3LabsTransPreorderAlipayReq.setTransType("41");
        v3LabsTransPreorderAlipayReq.setTotalAmount("1");
        v3LabsTransPreorderAlipayReq.setNotifyUrl("https://callback-api-beta.shrise.cn/lkl/trade/notify");
        v3LabsTransPreorderAlipayReq.setRemark("备注2");

        //地址位置信息
        V3LabsTradeLocationInfo v3LabsTradePreorderLocationInfo2 = new V3LabsTradeLocationInfo("**************");
//        v3LabsTradePreorderLocationInfo2.setLocation("*************");
//        v3LabsTradePreorderLocationInfo2.setBaseStation("00+LAC:6361+CID:58130");
//        v3LabsTradePreorderLocationInfo2.setRequestIp("+37.*********/-121.*********");
        v3LabsTransPreorderAlipayReq.setLocationInfo(v3LabsTradePreorderLocationInfo2);


        //支付宝主扫场景下acc_busi_fields域内容
        V3LabsTradePreorderAlipayBus alipayBus = new V3LabsTradePreorderAlipayBus();
//        alipayBus.setStoreId("11111");
//        alipayBus.setUserId("o6q8U6BMCWI05e_jcO3dfyv9kvIQ");
//        alipayBus.setDisablePayChannels("credit_group");
//        alipayBus.setBusinessParams("{“enable_thirdpar ty_subsidy”:”N”}");
//        alipayBus.setQuitUrl("www.test.com");
//        alipayBus.setTimeoutExpress("10");
        //支付宝extend_params字段说明
//        V3LabsTradePreorderAlipayBus.AlipayExtendParamInfo extendParamInfo = new V3LabsTradePreorderAlipayBus.AlipayExtendParamInfo();
//        extendParamInfo.setHbFqNum("3");
//        extendParamInfo.setSysServiceProviderId("2088511833207846");
//        extendParamInfo.setFoodOrderType("qr_order");
//        extendParamInfo.setHbFqSellerPercent("100");
//        alipayBus.setExtendParams(extendParamInfo);

        //支付宝goods_detail字段说明
        List<V3LabsTradePreorderAlipayBus.AlipayGoodsDeTail> goodsDeTails = new ArrayList<>();
        V3LabsTradePreorderAlipayBus.AlipayGoodsDeTail alipayGoodsDeTail1 = new V3LabsTradePreorderAlipayBus.AlipayGoodsDeTail();
//        alipayGoodsDeTail1.setAlipayGoodsId("6666");
        alipayGoodsDeTail1.setGoodsId("123");
        alipayGoodsDeTail1.setGoodsName("测试商品");
        alipayGoodsDeTail1.setQuantity("1");
        alipayGoodsDeTail1.setPrice("1");
//        alipayGoodsDeTail1.setShowUrl("www.test.com");
        goodsDeTails.add(alipayGoodsDeTail1);
        alipayBus.setGoodsDetail(goodsDeTails);
        v3LabsTransPreorderAlipayReq.setAccBusiFields(alipayBus);
//
//
//        /*** 银联主扫场景示例 */
//        V3LabsTransPreorderRequest v3LabsTransPreorderUnionPayReq = new V3LabsTransPreorderRequest();
//        v3LabsTransPreorderUnionPayReq.setMerchantNo("8222900594309B8");
//        v3LabsTransPreorderUnionPayReq.setTermNo("A1135688");
//        v3LabsTransPreorderUnionPayReq.setOutTradeNo("7089279514841773301");
//        v3LabsTransPreorderUnionPayReq.setAccountType("UQRCODEPAY");
//        v3LabsTransPreorderUnionPayReq.setTransType("51");
//        v3LabsTransPreorderUnionPayReq.setTotalAmount("10");
//        v3LabsTransPreorderUnionPayReq.setNotifyUrl("http://www.test.com");
//        v3LabsTransPreorderUnionPayReq.setRemark("备注");
//
//        //地址位置信息
//        V3LabsTradeLocationInfo v3LabsTradePreorderLocationInfo3 = new V3LabsTradeLocationInfo();
//        v3LabsTradePreorderLocationInfo3.setRequestIp("*************");
//        v3LabsTradePreorderLocationInfo3.setLocation("+37.*********,-121.*********");
//        v3LabsTransPreorderUnionPayReq.setLocationInfo(v3LabsTradePreorderLocationInfo3);
//
//
//        //银联主扫场景下acc_busi_fields域内容
//        V3LabsTradePreorderUnionPayBus unionPayBus = new V3LabsTradePreorderUnionPayBus();
//        unionPayBus.setUserId("23425");
//        unionPayBus.setFrontUrl("www.front.com");
//        unionPayBus.setFrontFailUrl("www.fail.com");
//        v3LabsTransPreorderUnionPayReq.setAccBusiFields(unionPayBus);

        //3. 发送请求
        String response = LKLSDK.httpPost(v3LabsTransPreorderAlipayReq);
        //4. 响应
        System.out.println(response);
    }

    @Test
    void tradeQuery() throws SDKException {

        V3LabsTradeQueryRequest v3LabsTradeQueryRequest = new V3LabsTradeQueryRequest();
        v3LabsTradeQueryRequest.setMerchantNo("8225840739900EH");
        v3LabsTradeQueryRequest.setTermNo("H1203603");
        v3LabsTradeQueryRequest.setOutTradeNo("sub_2024012610000028");
        //3. 发送请求
        String response = LKLSDK.httpPost(v3LabsTradeQueryRequest);
        //4. 响应
        System.out.println(response);
    }

    @Test
    void relationClose() throws SDKException {

        V3LabsRelationCloseRequest v3LabsRelationCloseRequest = new V3LabsRelationCloseRequest();
        v3LabsRelationCloseRequest.setMerchantNo("8225840739900EH");
        v3LabsRelationCloseRequest.setTermNo("H1203603");
        v3LabsRelationCloseRequest.setOriginOutTradeNo("sub_2024030710000031");
        //地址位置信息
        V3LabsTradeLocationInfo v3LabsTradePreorderLocationInfo1 = new V3LabsTradeLocationInfo("**************");
        v3LabsRelationCloseRequest.setLocationInfo(v3LabsTradePreorderLocationInfo1);
        //3. 发送请求
        String response = LKLSDK.httpPost(v3LabsRelationCloseRequest);
        //4. 响应
        System.out.println(response);
    }

    @Test
    void relationRefund() throws SDKException {
        //2. 装配数据
        V3LabsTransRelationRefundRequest v3LabsTransRelationRefundRequest = new V3LabsTransRelationRefundRequest();
        v3LabsTransRelationRefundRequest.setMerchantNo("8225840739900EH");
        v3LabsTransRelationRefundRequest.setTermNo("H1203603");
        v3LabsTransRelationRefundRequest.setOutTradeNo("tk_2024030710000032");
        v3LabsTransRelationRefundRequest.setRefundAmount("1");
        v3LabsTransRelationRefundRequest.setRefundReason("测试退款");
        v3LabsTransRelationRefundRequest.setOriginOutTradeNo("sub_2024030710000030");
//        v3LabsTransRelationRefundRequest.setOriginLogNo("66210312600784");
//        v3LabsTransRelationRefundRequest.setOriginTradeNo("2024012466210312600784");

        V3LabsTradeLocationInfo v3LabsTradeLocationInfo = new V3LabsTradeLocationInfo("**************");
        v3LabsTransRelationRefundRequest.setLocationInfo(v3LabsTradeLocationInfo);

        //3. 发送请求
        String response = LKLSDK.httpPost(v3LabsTransRelationRefundRequest);
        //4. 响应
        System.out.println(response);
    }

    @Test
    void relationIdmrefund() throws SDKException {
        //2. 装配数据
        V3LabsRelationIdmrefundRequest v3LabsRelationIdmrefundRequest = new V3LabsRelationIdmrefundRequest();
        v3LabsRelationIdmrefundRequest.setMerchantNo("8225840739900EH");
        v3LabsRelationIdmrefundRequest.setTermNo("H1203603");
        v3LabsRelationIdmrefundRequest.setOutRefundOrderNo("R2024012410000004");
        v3LabsRelationIdmrefundRequest.setRefundAmount("1");
        v3LabsRelationIdmrefundRequest.setRefundReason("测试退款");
        v3LabsRelationIdmrefundRequest.setOriginOutTradeNo("2024012610000007");
        v3LabsRelationIdmrefundRequest.setOriginLogNo("66215896073151");
        v3LabsRelationIdmrefundRequest.setOriginTradeNo("20240126110113130266215896073151");

        V3LabsTradeLocationInfo v3LabsTradeLocationInfo = new V3LabsTradeLocationInfo("**************");
        v3LabsRelationIdmrefundRequest.setLocationInfo(v3LabsTradeLocationInfo);

        //3. 发送请求
        String response = LKLSDK.httpPost(v3LabsRelationIdmrefundRequest);
        //4. 响应
        System.out.println(response);
    }


    @Test
    void queryRelationIdmrefund() throws SDKException {
        //2. 装配数据
        V3LabsQueryIdmrefundqueryRequest v3LabsQueryIdmrefundqueryRequest = new V3LabsQueryIdmrefundqueryRequest();
        v3LabsQueryIdmrefundqueryRequest.setMerchantNo("8225840739900EH");
        v3LabsQueryIdmrefundqueryRequest.setTermNo("H1203603");
        v3LabsQueryIdmrefundqueryRequest.setOutRefundOrderNo("R2024012410000003");

        //3. 发送请求
        String response = LKLSDK.httpPost(v3LabsQueryIdmrefundqueryRequest);
        //4. 响应
        System.out.println(response);
    }

}

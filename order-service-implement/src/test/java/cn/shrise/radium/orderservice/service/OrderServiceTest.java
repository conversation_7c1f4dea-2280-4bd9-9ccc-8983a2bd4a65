package cn.shrise.radium.orderservice.service;

import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.orderservice.entity.RsInvoice;
import cn.shrise.radium.orderservice.req.AuditManageReq;
import cn.shrise.radium.orderservice.service.order.OrderService;
import cn.shrise.radium.orderservice.service.vip.VipPackageService;
import cn.shrise.radium.workwxservice.resp.OrderAutoAuditResultResp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class OrderServiceTest {

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    OrderService orderService;

    @Autowired
    InvoiceService invoiceService;

    @Autowired
    AuditManageService manageService;

    @Autowired
    private VipPackageService vipPackageService;


    @Test
    void repairOrderStatus() {
        orderService.repairOrderStatus();
    }

    @Test
    void batchUpdateTest() {
//        List<RsInvoice> list = invoiceService.getInvoice();
//        SqlUtil<RsInvoice> sqlUtil = new SqlUtil<>();
//        String sqlStr = sqlUtil.batchUpdate(list);
//        System.out.println(sqlStr);
//
//        String create = sqlUtil.batchCreate(list);
//        System.out.println(create);
    }

    @Test
    void getAuditManageResp() {
        AuditManageReq auditManageReq = new AuditManageReq();
        auditManageReq.setField("orderNumber");
        Sort.Direction direction = auditManageReq.getAsc() ? Sort.Direction.ASC : Sort.Direction.DESC;
        if (Objects.isNull(auditManageReq.getField())) {
            auditManageReq.setField("id");
        }
        manageService.getAuditManageList(auditManageReq, PageRequest.of(auditManageReq.getPage(), auditManageReq.getSize(), Sort.by(direction, auditManageReq.getField())));
    }

    @Test
    void batchAssignTest() {
        List<String> orderNumberList = new ArrayList<>();
        orderNumberList.add("m1671427726862127104");
        orderNumberList.add("m1671427500600397824");
        orderNumberList.add("m1671427104293195776");
        orderNumberList.add("m1671426824952549376");
        orderNumberList.add("m1671426259933663232");
        orderNumberList.add("m1671425831221268480");
        orderNumberList.add("m1671414747743457280");
        orderNumberList.add("m1671045830831570944");
        orderNumberList.add("m1671044335071461376");
        orderNumberList.add("m1670993040283926528");
        List<Integer> hgIdList = new ArrayList<>();
        hgIdList.add(1);
        hgIdList.add(2);
        hgIdList.add(3);
        hgIdList.add(4);
        hgIdList.add(5);
        orderService.batchAssign(orderNumberList, hgIdList, null, null, null, 1, 240);
    }

    @Test
    void checkOrder() {
        OrderAutoAuditResultResp resp = orderService.checkOrder(651143);
        System.out.println(resp);
    }


    @Test
    public void testIndex() {
        List<String> index = Arrays.asList("qstd", "dkxh", "ddxj", "zdld", "zldn", "zsjc");

        List<ServiceIntroduction> serviceIntroductionList = vipPackageService.getIndexServiceIntroductionList(45, index);

        System.out.println(serviceIntroductionList);
    }

}

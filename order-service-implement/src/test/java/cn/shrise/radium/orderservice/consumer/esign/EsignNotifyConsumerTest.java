package cn.shrise.radium.orderservice.consumer.esign;

import cn.shrise.radium.orderservice.constant.EsignAction;
import cn.shrise.radium.orderservice.req.EsignNotifyReq;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class EsignNotifyConsumerTest {

    @Autowired
    EsignNotifyConsumer esignNotifyConsumer;

    @Test
    void test(){
        EsignNotifyReq notifyReq = JSON.parseObject("{\n" +
                "    \"companyType\": 45,\n" +
                "    \"notifyBody\": \"{\\\"action\\\":\\\"SIGN_FLOW_FINISH\\\",\\\"flowId\\\":\\\"6d975535d6ea4932acca481c13376501\\\",\\\"businessScence\\\":\\\"智投增值包202410271630\\\",\\\"flowStatus\\\":\\\"2\\\",\\\"createTime\\\":\\\"2024-10-27 16:30:36\\\",\\\"endTime\\\":\\\"2024-10-27 16:31:01\\\",\\\"statusDescription\\\":\\\"完成\\\",\\\"timestamp\\\":1730017860919}\"\n" +
                "}", EsignNotifyReq.class);
        Integer companyType = notifyReq.getCompanyType();
        String notifyBody = notifyReq.getNotifyBody();
        JSONObject jsonObject = JSON.parseObject(notifyBody);
        String action = jsonObject.getString("action");
        if (Objects.equals(action, EsignAction.SIGN_FLOW_FINISH)) {
            esignNotifyConsumer.handleSignFlowFinish(companyType, jsonObject);
        }
    }

}
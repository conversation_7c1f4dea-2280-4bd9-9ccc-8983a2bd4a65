package cn.shrise.radium.orderservice.service.order;

import cn.shrise.radium.orderservice.req.GenerateOrderFeedbackPdfReq;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class OrderFeedbackServiceTest {

    @Autowired
    OrderFeedbackService orderFeedbackService;

    @Test
    void generateOrderFeedbackPdf() throws Exception {
        GenerateOrderFeedbackPdfReq generateOrderFeedbackPdfReq = new GenerateOrderFeedbackPdfReq();
        Map<String, Object> feedbackData = new HashMap<>();
        feedbackData.put("skuName", "二档测试-qd");
        feedbackData.put("period", "");
        feedbackData.put("option7", "Yes");
        feedbackData.put("payTime", "2024年09月04日");
        feedbackData.put("level", "C3级");
        feedbackData.put("option1", "Yes");
        feedbackData.put("name1", "孟伸");
        orderFeedbackService.generateOrderFeedbackPdf(generateOrderFeedbackPdfReq);
    }

}
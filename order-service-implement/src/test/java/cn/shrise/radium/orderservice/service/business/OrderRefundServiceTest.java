package cn.shrise.radium.orderservice.service.business;

import cn.shrise.radium.orderservice.service.refund.OrderRefundService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@SpringBootTest
class OrderRefundServiceTest {

    @Autowired
    OrderRefundService service;

    @Test
    void getOrderExtInfo() {

    }

    @Test
    void genRefundPolicy() {
        service.generateRefundPolicy(45, 644496, null, 3);
        service.generateRefundPolicy(45, 644496, null, 2);
        service.generateRefundPolicy(45, 644496, null, 1);
    }
}
package cn.shrise.radium.orderservice.payment;

import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.orderservice.entity.RsCourseRefundOrder;
import cn.shrise.radium.orderservice.req.payment.LLianNotifyReq;
import cn.shrise.radium.orderservice.req.payment.LklNotifyReq;
import cn.shrise.radium.orderservice.service.payment.LLianPaymentService;
import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static cn.shrise.radium.common.constant.RocketMQConstant.TAG_PAYMENT_REFUND_PLAN;
import static cn.shrise.radium.common.constant.RocketMQConstant.TOPIC_PAYMENT;

@SpringBootTest
public class LLianPayTest {

    @Autowired
    private LLianPaymentService lLianPaymentService;
    @Autowired
    private RocketMqUtils rocketMqUtils;
    
    @Test
    public void test() {
        String str = "{\"notifyBody\":\"{\\\"bank_code\\\":\\\"********\\\",\\\"dt_order\\\":\\\"********130856\\\",\\\"money_order\\\":\\\"0.01\\\",\\\"no_order\\\":\\\"sub_1866349355017891840\\\",\\\"oid_partner\\\":\\\"302409290000032826\\\",\\\"oid_paybill\\\":\\\"********08797572\\\",\\\"pay_type\\\":\\\"W\\\",\\\"result_pay\\\":\\\"SUCCESS\\\",\\\"settle_date\\\":\\\"********\\\",\\\"sign\\\":\\\"Us/GfC8dnqOqnCrm/IvV4AQHrmFMpaJMBkqu61VX++IFlR4S8FYs4rZf90mfAzBTeEReYta08hkgrY1kx/elRpRQ61loBRP6lIm0463aOR+OQEmhO3MrUYtcqodsisLafOLYJI7qgmt46RnrER8DJIJ5KoOZOn3+23LC5ILfgCU=\\\",\\\"sign_type\\\":\\\"RSA\\\"}\",\"merchantId\":99}";
        LLianNotifyReq req = JSON.parseObject(str, LLianNotifyReq.class);
        lLianPaymentService.handleOrderNotify(req);
        System.out.println();
    }

    @Test
    public void testRefund() {
        String str = "{\n" +
                "    \"auditTime\": \"2024-12-12T06:35:49Z\",\n" +
                "    \"confirmId\": 128702,\n" +
                "    \"confirmTime\": \"2024-12-12T06:36:40Z\",\n" +
                "    \"createTime\": \"2024-12-12T06:34:35Z\",\n" +
                "    \"creatorId\": 128702,\n" +
                "    \"enabled\": true,\n" +
                "    \"financeId\": 128702,\n" +
                "    \"id\": 3571,\n" +
                "    \"isCloseService\": true,\n" +
                "    \"orderId\": 667288,\n" +
                "    \"readInitTime\": \"2024-12-12T06:34:35Z\",\n" +
                "    \"readStatus\": 20,\n" +
                "    \"refundAmount\": 1,\n" +
                "    \"refundNumber\": \"tk1867095685464457216\",\n" +
                "    \"remark\": \"威胁投诉\",\n" +
                "    \"status\": 55,\n" +
                "    \"submitTime\": \"2024-12-12T06:34:35Z\",\n" +
                "    \"updateTime\": \"2024-12-12T06:37:19.751297Z\"\n" +
                "}";
        RsCourseRefundOrder rsCourseRefundOrder = JSON.parseObject(str, RsCourseRefundOrder.class);
        rocketMqUtils.send(TOPIC_PAYMENT, TAG_PAYMENT_REFUND_PLAN, rsCourseRefundOrder);
    }
}

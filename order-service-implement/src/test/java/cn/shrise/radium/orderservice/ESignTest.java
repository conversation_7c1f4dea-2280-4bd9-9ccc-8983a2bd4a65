package cn.shrise.radium.orderservice;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.RedisUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.req.UpdateSignInfoReq;
import cn.shrise.radium.orderservice.conf.ESignConfiguration;
import cn.shrise.radium.orderservice.entity.RsCourseOrderSign;
import cn.shrise.radium.orderservice.service.order.CourseOrderSignService;
import cn.shrise.radium.orderservice.service.order.OrderService;
import cn.shrise.radium.orderservice.service.sku.SkuService;
import cn.shrise.radium.orderservice.util.esign.ESignUtils;
import cn.shrise.radium.orderservice.util.esign.GenSignNumber;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static cn.shrise.radium.orderservice.constant.ESignConstant.SIGN_LOCK_KEY;
import static cn.shrise.radium.orderservice.constant.ESignConstant.SIGN_RESULT_KEY;

@Slf4j
@SpringBootTest
public class ESignTest {

    @Autowired
    private ESignConfiguration eSignConfiguration;
    @Autowired
    private CourseOrderSignService courseOrderSignService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ContentClient contentClient;
    @Autowired
    private GenSignNumber genSignNumber;

    @Test
    public void newTest() throws Exception {
        ESignUtils eSignUtils = eSignConfiguration.getESignUtils(45);
        JSONObject body = eSignUtils.getAccount("e8c04e0c124b4ae296ed7733ba78194f");
        System.out.println(body);
    }

    @Test
    public void test() throws Exception {
        ESignUtils eSignUtils = eSignConfiguration.getESignUtils(45);
        JSONObject body = eSignUtils.getAccount("e8c04e0c124b4ae296ed7733ba78194f");
    }

    @Test
    public void myTest() {
        UpdateSignInfoReq req = new UpdateSignInfoReq();
        req.setFileId("1234");
        contentClient.updateSignInfo(1482, req);
    }


    @Test
    void signTest2() {
        ESignUtils eSignUtils = eSignConfiguration.getESignUtils(45);
        String accountId = "a1ab24e414b44985a128519817d579e6";
        String templateId = "0e86576fea994c1b8a00a8163b2a8b5e";
        String evaluationName = "孟伸";
        String applySignNumber = "123456";
        String signTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.of("UTC+08:00")).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        Map<String, String> simpleFormFields = new HashMap<>();
        String cpmc = StrUtil.format("{}{}", "五股丰登策略", 100);
        simpleFormFields.put("name1", evaluationName);
        simpleFormFields.put("name2", evaluationName);
        simpleFormFields.put("name3", evaluationName);
        simpleFormFields.put("name4", evaluationName);
        simpleFormFields.put("name5", evaluationName);
        simpleFormFields.put("time1", signTime);
        simpleFormFields.put("time2", signTime);
        simpleFormFields.put("time3", signTime);
        simpleFormFields.put("time4", signTime);
        simpleFormFields.put("time5", signTime);
        simpleFormFields.put("htbh1", applySignNumber);
        simpleFormFields.put("htbh2", applySignNumber);
        simpleFormFields.put("sfzhm", AESUtil.decrypt("MTIzNDU2Nzg5MGFiY2RlZl8rNrDbFL0qrSZP6miC5Uv2x9fpLnyPf72wSGdQJ7aR"));
        simpleFormFields.put("name-gc1", "深圳市国诚投资咨询有限公司上海分公司");
        simpleFormFields.put("name-gc2", "深圳市国诚投资咨询有限公司上海分公司");
        simpleFormFields.put("ddbh", "1");
//        simpleFormFields.put("phone", "111111");
        simpleFormFields.put("cpmc", cpmc);
        simpleFormFields.put("fwq", String.valueOf(5));
        JSONObject res = eSignUtils.createFileByTemplate(accountId + ".pdf", templateId, simpleFormFields);
    }

    private RsCourseOrderSign getOrderSign(Integer company, Integer orderId) {
        RsCourseOrderSign orderSign = courseOrderSignService.getOrderSignByOrderId(orderId);
        if (ObjectUtil.isNull(orderSign)) {
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String signNumber = genSignNumber.getSignNumber(company, date);
            orderSign = courseOrderSignService.createOne(orderId, signNumber);
        }
        return orderSign;
    }

    private Boolean checkAge70(LocalDate birth) {
        LocalDate birth70;
        if (birth.getMonthValue() == 2 && birth.getDayOfMonth() == 29) {
            birth70 = birth.plusDays(1).plusYears(70).minusDays(1);
        } else {
            birth70 = birth.plusYears(70);
        }
        return LocalDate.now().isAfter(birth70);
    }

    private void releaseSignLock(Integer signType, Integer orderId, String result) {
        String key = String.format(SIGN_LOCK_KEY, signType, orderId);
        String res = redisUtil.getKey(key);
        if (ObjectUtil.isNotNull(res)) {
            redisUtil.setKey(String.format(SIGN_RESULT_KEY, res), result, 60 * 60 * 6L);
            redisUtil.releaseLock(key);
        }
    }
}

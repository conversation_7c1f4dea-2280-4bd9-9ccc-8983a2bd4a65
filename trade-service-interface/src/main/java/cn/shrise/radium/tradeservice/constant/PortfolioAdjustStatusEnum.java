package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Getter
public enum PortfolioAdjustStatusEnum {

    PAS_Unknown(0, "未知"),
    PAS_Dealt(1, "已成交"),
    PAS_Pending(2, "待成交"),
    PAS_Cancelled(3, "已取消");

    private final Integer code;
    private final String msg;

    PortfolioAdjustStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}

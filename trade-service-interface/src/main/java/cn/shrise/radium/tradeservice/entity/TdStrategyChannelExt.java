package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_strategy_channel_ext", indexes = {
        @Index(name = "uk_number_company", columnList = "company_type, number", unique = true)
})
public class TdStrategyChannelExt {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "number", nullable = false, length = 128)
    private String number;

    @Column(name = "back_name", nullable = false, length = 128)
    private String backName;

    @Column(name = "show_name", length = 128)
    private String showName;

    @Lob
    @Column(name = "description")
    private String description;

    @Column(name = "channel_id")
    private Long channelId;

    @Lob
    @Column(name = "tags")
    private String tags;

    @Column(name = "is_show_profit", nullable = false)
    private Boolean isShowProfit;

    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    @Column(name = "profit_ratio")
    private Double profitRatio;

    @Column(name = "profit_time")
    private Instant profitTime;

    @Column(name = "analyst_id")
    private Integer analystId;

}
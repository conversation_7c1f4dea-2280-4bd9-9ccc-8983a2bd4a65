package cn.shrise.radium.tradeservice.constant;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@UtilityClass
public class TradeServiceConst {

    /**
     * 抖音生产者topic
     */
    public static final String TRADE_TOPIC = "tradeTopic";

    @UtilityClass
    public static final class MqTagType {
        /**
         * 创建组合需通知的人
         */
        public static final String PORTFOLIO_OPEN_NOTIFY = "portfolio_open_notify";
        /**
         * 调仓需通知的人
         */
        public static final String PORTFOLIO_TRADE_NOTIFY = "portfolio_trade_notify";
        /**
         * 案例股票获取行情价格
         */
        public static final String STOCK_CASE_QUOTE = "stock_case_quote";

        /**
         * 创建案例股票池需通知的人
         */
        public static final String STOCK_CASE_OPEN_NOTIFY = "stock_case_open_notify";
        /**
         * 案例股票池调仓需通知的人
         */
        public static final String STOCK_CASE_TRADE_NOTIFY = "stock_case_trade_notify";
        /**
         * 废弃A股情报股票池
         */
        public static final String RECOMMEND_STOCK_ABANDON = "recommend_stock_abandon";
        /**
         * 更新股票评分
         */
        public static final String TAG_UPDATE_STOCK_CASE_SCORE = "tag_update_stock_case_score";
        /**
         * 诊股
         */
        public static final String TAG_STOCK_DIAGNOSE = "tag_stock_diagnose";

    }

    @UtilityClass
    public static final class MqGroupType {
        /**
         * 创建组合需通知的人
         */
        public static final String GID_PORTFOLIO_OPEN_NOTIFY = "GID_portfolio_open_notify";
        /**
         * 调仓需通知的人
         */
        public static final String GID_PORTFOLIO_TRADE_NOTIFY = "GID_portfolio_trade_notify";
        /**
         * 案例股票获取行情价格
         */
        public static final String GID_STOCK_CASE_QUOTE = "GID_stock_case_quote";

        /**
         * 案例股票池创建组合需通知的人
         */
        public static final String GID_STOCK_CASE_OPEN_NOTIFY = "GID_stock_case_open_notify";
        /**
         * 案例股票池调仓需通知的人
         */
        public static final String GID_STOCK_CASE_TRADE_NOTIFY = "GID_stock_case_trade_notify";
        /**
         * 废弃案例股票池
         */
        public static final String GID_STOCK_CASE_ABANDON = "GID_stock_case_abandon";
        /**
         * 废弃A股情报股票池
         */
        public static final String GID_RECOMMEND_STOCK_ABANDON = "GID_recommend_stock_abandon";
        /**
         * 更新股票评分
         */
        public static final String GID_UPDATE_STOCK_CASE_SCORE = "GID_update_stock_case_score";
        /**
         * 诊股
         */
        public static final String GID_STOCK_DIAGNOSE = "GID_stock_diagnose";
    }

}

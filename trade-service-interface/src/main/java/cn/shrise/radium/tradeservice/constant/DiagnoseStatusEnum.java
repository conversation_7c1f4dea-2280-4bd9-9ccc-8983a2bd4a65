package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/28 19:30
 * @Desc:
 **/
@Getter
public enum DiagnoseStatusEnum {

    DIAGNOSE_IN_PROCESS(10, "诊断中"),
    DIAGNOSE_FAIL(20, "诊断失败"),
    DIAGNOSE_SUCCESS(30, "诊断成功");
    private final Integer code;
    private final String msg;

    DiagnoseStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}

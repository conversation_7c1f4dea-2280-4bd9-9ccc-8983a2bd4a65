package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_recommend_stock_profit", indexes = {
        @Index(name = "MaxProfitRatio", columnList = "MaxProfitRatio")
})
public class TdRecommendStockProfit {

    @Id
    @Column(name = "StockID", nullable = false)
    private Integer id;

    @Column(name = "Cost")
    private Double cost;

    @Column(name = "CostTime")
    private Instant costTime;

    @Column(name = "ProfitRatio")
    private Double profitRatio;

    @Column(name = "ExPrice")
    private Double exPrice;

    @Column(name = "Price")
    private Double price;

    @Column(name = "SumFactor")
    private Double sumFactor;

    @Column(name = "ProfitTime")
    private Instant profitTime;

    @Column(name = "MaxPrice")
    private Double maxPrice;

    @Column(name = "MaxTime")
    private Instant maxTime;

    @Column(name = "MaxFactor")
    private Double maxFactor;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "MaxProfitRatio")
    private Double maxProfitRatio;

    @Column(name = "MaxProcessTime")
    private Instant maxProcessTime;

    @Column(name = "IsRefresh")
    private Boolean isRefresh;

}
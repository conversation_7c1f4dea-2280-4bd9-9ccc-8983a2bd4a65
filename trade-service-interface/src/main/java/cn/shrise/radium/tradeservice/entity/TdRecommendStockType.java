package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Table(name = "td_recommend_stock_type", indexes = {
        @Index(name = "unique_number_company", columnList = "Number, CompanyType", unique = true)
})
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TdRecommendStockType {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "Name", length = 64)
    private String name;

    @Column(name = "Number", nullable = false, length = 128)
    private String number;

    @Column(name = "CompanyType", nullable = false)
    private Integer companyType;

    @Column(name = "Enabled")
    private Boolean enabled;

    @Column(name = "SeriesID")
    private Integer seriesId;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "need_decision")
    private Boolean needDecision;
}
package cn.shrise.radium.tradeservice.constant;

import cn.shrise.radium.common.base.BaseError;

/**
 * <AUTHOR>
 */
public enum TradeErrorCode implements BaseError {

    /**
     * -1:失败
     * 0:成功
     */
    FAILURE(-1, "Failure"),
    SUCCESS(0,"Success"),
    CHANNEL_NAME_EXISTED(81161001, "案例频道名称已存在"),
    CHANNEL_NUMBER_EXISTED(81161002, "案例频道编号已存在"),
    ORDER_TYPE_NOT_SUPPORTED(81161003,"排序类型不支持"),
    LIVE_ROOM_NOT_EMPTY(81161004,"直播室Id不能为空"),
    SERIES_NOT_EMPTY(81161005,"栏目Id不能为空"),
    PARAM_INVALID(81161006,"参数不合法"),
    CHATROOM_NOT_EMPTY(81161007,"聊天室Id不能为空"),
    CHATROOM_EXISTED(81161008,"该聊天室已绑定频道"),
    MY_STOCK_100(81161009,"自选股数量超出限制"),
    MY_STOCK_REPEAT(81161010,"自选股重复添加"),
    MY_STOCK_NOT_EXISTED(81161011,"自选股不存在"),
    SUB_STOCK_CASE_COUNT_INSUFFICIENT(81161012,"已超过关注次数")
    ;

    private final Integer code;
    private final String msg;

    TradeErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}

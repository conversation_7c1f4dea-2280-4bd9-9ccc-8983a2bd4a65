package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2024/8/15 14:05
 * @Desc:
 **/
@Getter
public enum RecommendStockAuditStatusEnum {

    /**
     * 股票池审核状态枚举
     */

    PASS(1, "审核通过-合规"),
    REJECT(2, "审核拒绝-合规"),
    WAITING(4, "待审核-合规"),
    WAITING_DECISION(40, "待确认-决策小组"),
    PASS_DECISION(50, "已确认-决策小组"),
    REJECT_DECISION(60, "已驳回-决策小组"),
    ABANDON(70, "已作废");

    private final Integer value;
    private final String msg;

    RecommendStockAuditStatusEnum(Integer value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public static String getMsg(Integer value) {
        for (RecommendStockAuditStatusEnum typeEnum : RecommendStockAuditStatusEnum.values()) {
            if (typeEnum.value.equals(value)) {
                return typeEnum.msg;
            }
        }
        return null;
    }

}

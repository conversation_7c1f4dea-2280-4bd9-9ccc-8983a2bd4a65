package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_recommend_stock_notice", indexes = {
        @Index(name = "TypeID", columnList = "TypeID"),
        @Index(name = "AuthorID", columnList = "AuthorID")
})
public class TdRecommendStockNotice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "TypeID")
    private Integer typeID;

    @Column(name = "AuthorID")
    private Integer authorID;

    @Lob
    @Column(name = "Content")
    private String content;

    @Column(name = "CompanyType", nullable = false)
    private Integer companyType;

    @Column(name = "Enabled")
    private Boolean enabled;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "ChannelNumber")
    private String channelNumber;

}
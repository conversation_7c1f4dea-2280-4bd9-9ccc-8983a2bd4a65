package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;


@Table(name = "td_recommend_stock", indexes = {
        @Index(name = "TypeID", columnList = "TypeID"),
        @Index(name = "AuditComplianceID", columnList = "AuditComplianceID"),
        @Index(name = "AnalystID", columnList = "AnalystID"),
        @Index(name = "AuditorID", columnList = "AuditorID"),
        @Index(name = "CreatorID", columnList = "CreatorID"),
        @Index(name = "AuditManagerID", columnList = "AuditManagerID"),
        @Index(name = "ix_trade_db_td_recommend_stock_Code", columnList = "Code")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdRecommendStock {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "Name", length = 64)
    private String name;

    @Column(name = "Code", length = 64)
    private String code;

    @Column(name = "TypeID", nullable = false)
    private Integer typeId;

    @Column(name = "BuyTime")
    private Instant buyTime;

    @Column(name = "BuyPrice")
    private Double buyPrice;

    @Lob
    @Column(name = "Brief")
    private String brief;

    @Lob
    @Column(name = "Content")
    private String content;

    @Column(name = "AnalystID")
    private Integer analystId;

    @Column(name = "CreatorID")
    private Integer creatorId;

    @Column(name = "IsRank")
    private Boolean isRank;

    @Column(name = "IsTop")
    private Boolean isTop;

    @Column(name = "Enabled")
    private Boolean isEnabled;

    @Column(name = "CompanyType", nullable = false)
    private Integer companyType;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "AuditComplianceID")
    private Integer auditorComplianceId;

    @Column(name = "AuditManagerID")
    private Integer auditManagerId;

    @Column(name = "LossPrice")
    private Double lossPrice;

    @Column(name = "TargetPrice")
    private Double targetPrice;

    @Column(name = "AuditorID")
    private Integer auditorId;

    @Column(name = "AuditStatus")
    private Integer auditStatus;

    @Column(name = "AuditTime")
    private Instant auditTime;

    @Lob
    @Column(name = "AuditRemark")
    private String auditRemark;

}
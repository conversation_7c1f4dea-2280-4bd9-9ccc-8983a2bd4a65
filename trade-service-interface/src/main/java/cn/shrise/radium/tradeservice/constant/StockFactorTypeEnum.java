package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2025/4/3 15:19
 * @Desc:
 **/
@RequiredArgsConstructor
@Getter
public enum StockFactorTypeEnum {

    POSITION(10, "位置"),
    STYLE(20, "风格"),
    FORM(30, "形态"),
    TREND(40, "趋势"),
    TURING_POINT(50, "拐点");


    private final Integer code;
    private final String msg;
}

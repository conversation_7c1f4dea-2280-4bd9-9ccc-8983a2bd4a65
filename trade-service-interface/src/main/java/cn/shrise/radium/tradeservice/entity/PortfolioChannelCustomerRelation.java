package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Table(name = "td_portfolio_channel_customer_relation", indexes = {
        @Index(name = "unique_portfolio_user", columnList = "ChannelID, UserID", unique = true)
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortfolioChannelCustomerRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "UserID", nullable = false)
    private Integer userId;

    @Column(name = "ChannelID", nullable = false)
    private Integer channelId;

    @Column(name = "CreateTime")
    private Instant createTime;
}
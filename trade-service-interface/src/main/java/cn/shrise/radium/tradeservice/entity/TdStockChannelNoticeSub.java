package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_stock_channel_notice_sub", indexes = {
        @Index(name = "idx_channel_type", columnList = "channel_type"),
        @Index(name = "idx_user_id", columnList = "user_id"),
        @Index(name = "idx_device_type", columnList = "device_type"),
        @Index(name = "idx_is_sub", columnList = "is_subscribe")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdStockChannelNoticeSub {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "channel_type", nullable = false)
    private Integer channelType;

    @Column(name = "user_id", nullable = false)
    private Integer userId;

    @Column(name = "device_type")
    private Integer deviceType;

    @Column(name = "is_subscribe", nullable = false)
    private Boolean isSubscribe;

}
package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_portfolio_analyst_relation", indexes = {
        @Index(name = "uni_portfolio_analyst", columnList = "PortfolioID, AnalystID", unique = true),
        @Index(name = "AnalystID", columnList = "AnalystID")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdPortfolioAnalystRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "PortfolioID")
    private Integer portfolioId;

    @Column(name = "AnalystID")
    private Integer analystId;

    @Column(name = "CreateTime")
    private Instant createTime;
}
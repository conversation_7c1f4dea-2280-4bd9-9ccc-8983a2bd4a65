package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_portfolio_position", indexes = {
        @Index(name = "unique_portfolio_label", columnList = "PortfolioID, Label")
})
public class TdPortfolioPosition {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "PortfolioID")
    private Integer portfolioId;

    @Column(name = "Label", nullable = false, length = 64)
    private String label;

    @Column(name = "TotalCost")
    private Double totalCost;

    @Column(name = "Volume", precision = 20, scale = 7)
    private BigDecimal volume;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "MaxFactor")
    private Double maxFactor;

    @Column(name = "MaxPrice")
    private Double maxPrice;

    @Column(name = "MaxProcessTime")
    private Instant maxProcessTime;

    @Column(name = "MaxProfitRatio")
    private Double maxProfitRatio;

    @Column(name = "MaxTime")
    private Instant maxTime;

    @Column(name = "OpenTime")
    private Instant openTime;

    @Column(name = "OpenPrice")
    private Double openPrice;

    @Column(name = "IsRefresh")
    private Boolean isRefresh;

}
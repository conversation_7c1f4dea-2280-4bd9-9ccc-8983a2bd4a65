package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;

@Getter
public enum StockCaseAuditStatusEnum {

    WAITING(10, "已确认-决策小组"),
    PASS(20, "审核通过-合规"),
    REJECT(30, "审核拒绝-合规"),
    WAITING_DECISION(40, "待确认-决策小组"),
    PASS_DECISION(50, "已确认-决策小组"),
    REJECT_DECISION(60, "已驳回-决策小组"),
    ABANDON(70, "已作废"),
    ;
    private final Integer code;
    private final String msg;

    StockCaseAuditStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsg(Integer code) {
        for (StockCaseAuditStatusEnum typeEnum : StockCaseAuditStatusEnum.values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum.msg;
            }
        }
        return null;
    }
}

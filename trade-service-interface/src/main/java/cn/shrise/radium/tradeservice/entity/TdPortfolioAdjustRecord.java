package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_portfolio_adjust_record", indexes = {
        @Index(name = "PortfolioID", columnList = "PortfolioID")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdPortfolioAdjustRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "PortfolioID")
    private Integer portfolioId;

    @Lob
    @Column(name = "Remark")
    private String remark;

    @Column(name = "CreateTime")
    private Instant createTime;

    /**
     * 操作人
     */
    @Column(name = "OperatorID")
    private Integer operatorId;
}
package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "td_case_channel_manager_relation", indexes = {
        @Index(name = "manager_ibfk2_idx", columnList = "manager_id"),
        @Index(name = "uni_channel_manager", columnList = "channel_id, manager_id", unique = true),
        @Index(name = "channel_ibfk1_idx", columnList = "channel_id")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdCaseChannelManagerRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "channel_id", nullable = false)
    private Long channelId;

    @Column(name = "manager_id", nullable = false)
    private Integer managerId;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

}
package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_stock_case", indexes = {
        @Index(name = "channel_id_ibfk1_idx", columnList = "channel_id"),
        @Index(name = "auditor_ibfk3_idx", columnList = "auditor_id"),
        @Index(name = "index_lable", columnList = "label_code"),
        @Index(name = "creator_ibfk2_idx", columnList = "creator_id")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdStockCase {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "channel_id", nullable = false)
    private Long channelId;

    @Column(name = "company_type")
    private Integer companyType;

    @Column(name = "is_enabled")
    private Boolean isEnabled;

    @Column(name = "label_code", nullable = false, length = 128)
    private String labelCode;

    @Column(name = "category", length = 128)
    private String category;

    @Column(name = "count")
    private Integer count;

    @Column(name = "price")
    private Double price;

    @Column(name = "is_closed", nullable = false)
    private Boolean isClosed;

    @Column(name = "creator_id")
    private Integer creatorId;

    @Column(name = "auditor_id")
    private Integer auditorId;

    @Column(name = "analyst_id")
    private Integer analystId;

    @Column(name = "deal_time")
    private Instant dealTime;

    @Column(name = "gmt_create", insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "is_top", insertable = false)
    private Boolean isTop;

    @Column(name = "top_time")
    private Instant topTime;

    @Column(name = "source_type")
    private Integer sourceType;

    @Column(name = "close_time")
    private Instant closeTime;

    @Column(name = "close_price")
    private Double closePrice;

    @Column(name = "audit_time")
    private Instant auditTime;

    @Column(name = "audit_status")
    private Integer auditStatus;

    @Column(name = "reject_reason")
    private String rejectReason;

    @Column(name = "score")
    private Double score;

    @Column(name = "dz_stock_id")
    private String dzStockId;

    @Column(name = "is_research")
    private Boolean isResearch;


}
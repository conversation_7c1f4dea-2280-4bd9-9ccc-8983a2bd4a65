package cn.shrise.radium.tradeservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class EditStockCaseDealReq {

    @ApiModelProperty(value = "案例Id", required = true)
    private Long caseId;

    @ApiModelProperty("操作类型")
    private Integer dealType;

    @ApiModelProperty(value = "当前用户", hidden = true)
    private Integer operatorId;

    @ApiModelProperty(value = "公司类型", hidden = true)
    private Integer companyType;

    @ApiModelProperty(value = "关注价格")
    private Double price;

    @ApiModelProperty(value = "最高价")
    private Double priceUp;

    @ApiModelProperty(value = "最低价")
    private Double priceDown;

    @ApiModelProperty(value = "止盈区间最高价")
    private Double takeProfitUp;

    @ApiModelProperty(value = "止盈区间最低价")
    private Double takeProfitDown;

    @ApiModelProperty(value = "止损区间最高价")
    private Double stopLossUp;

    @ApiModelProperty(value = "止损区间最低价")
    private Double stopLossDown;

    @ApiModelProperty(value = "当前仓位")
    private Integer endCount;

    @ApiModelProperty(value = "跟踪理由", required = true)
    private String reason;

    @ApiModelProperty(value = "风险提示", required = true)
    private String risk;

    @ApiModelProperty(value = "温馨提示", required = true)
    private String tips;

    @ApiModelProperty(value = "是否APP推送")
    private Boolean appPush;

}

package cn.shrise.radium.tradeservice.dto;

import cn.shrise.radium.tradeservice.entity.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/3/15 14:41
 * @Desc:
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerStockCaseDto{
    private TdStockCase caseInfo;
    private TdStockCaseChannel channelInfo;
    private TdCaseSubInfo subInfo;
    private TdCaseProfit caseProfit;
    private TdStockCaseDeal latestCaseDeal;
}


package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_portfolio_customer_relation", indexes = {
        @Index(name = "unique_portfolio_user", columnList = "PortfolioID, UserID", unique = true),
        @Index(name = "UserID", columnList = "UserID")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdPortfolioCustomerRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "UserID")
    private Integer userID;

    @Column(name = "PortfolioID")
    private Integer portfolioID;

    @Column(name = "CreateTime", nullable = false)
    private Instant createTime;

}
package cn.shrise.radium.tradeservice.entity;

import cn.shrise.radium.tradeservice.constant.StockPortfolioAuditStatusConstant;
import cn.shrise.radium.tradeservice.constant.StockPortfolioPushStatusConstant;
import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_stock_portfolio", indexes = {
        @Index(name = "AuditComplianceID", columnList = "AuditComplianceID"),
        @Index(name = "AnalystID", columnList = "AnalystID"),
        @Index(name = "AuditorID", columnList = "AuditorID"),
        @Index(name = "UserID", columnList = "UserID"),
        @Index(name = "AnalystTeamID", columnList = "AnalystTeamID"),
        @Index(name = "ix_trade_db_td_stock_portfolio_PortfolioType", columnList = "PortfolioType"),
        @Index(name = "AuditManagerID", columnList = "AuditManagerID"),
        @Index(name = "ChannelID", columnList = "ChannelID")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdStockPortfolio {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "UserID")
    private Integer userId;

    @Column(name = "Name", length = 256)
    private String name;

    @Lob
    @Column(name = "Introduce")
    private String introduce;

    @Column(name = "AnalystID")
    private Integer analystId;

    @Column(name = "AnalystTeamID")
    private Integer analystTeamId;

    @Column(name = "OpenStatus")
    private Boolean openStatus;

    @Column(name = "Balance")
    private Double balance;

    @Column(name = "AvailableBalance")
    private Double availableBalance;

    @Column(name = "CompanyType")
    private Integer companyType;

    @Column(name = "ExpectedProfit")
    private Double expectedProfit;

    @Column(name = "Enabled")
    private Boolean isEnabled;

    @Column(name = "IsClosed")
    private Boolean isClosed;

    @Column(name = "IsRank")
    private Boolean isRank;

    @Column(name = "OpenTime")
    private Instant openTime;

    @Column(name = "CloseTime")
    private Instant closeTime;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "AuditComplianceID")
    private Integer auditComplianceId;

    @Column(name = "AuditManagerID")
    private Integer auditManagerId;

    @Column(name = "PortfolioType")
    private Integer portfolioType;

    @Column(name = "ChannelID")
    private Integer channelId;

    @Column(name = "AuditorID")
    private Integer auditorId;

    /**
     *  {@link StockPortfolioAuditStatusConstant}
     */
    @Column(name = "AuditStatus")
    private Integer auditStatus;

    @Column(name = "AuditTime")
    private Instant auditTime;

    @Column(name = "AuditReason")
    private String auditReason;

    /**
     *  {@link StockPortfolioPushStatusConstant}
     */
    @Column(name = "PushStatus")
    private Integer pushStatus;
}
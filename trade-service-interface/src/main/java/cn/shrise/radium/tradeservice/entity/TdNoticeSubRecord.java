package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_notice_sub_record")
public class TdNoticeSubRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "CustomerID")
    private Integer customerId;

    @Column(name = "NoticeType")
    private Integer noticeType;

    @Column(name = "IsSub", nullable = false)
    private Boolean isSub = false;

    @Column(name = "OperatorID")
    private Integer operatorId;

    @Column(name = "CompanyType", nullable = false)
    private Integer companyType;

    @Column(name = "CreateTime")
    private Instant createTime;

}
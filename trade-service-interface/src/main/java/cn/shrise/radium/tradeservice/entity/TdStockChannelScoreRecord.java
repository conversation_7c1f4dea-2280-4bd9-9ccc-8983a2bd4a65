package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * 股票池评分记录
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "td_stock_channel_score_record", indexes = {
        @Index(name = "uk_number_label_date", columnList = "channel_number, flag_date, label_code"),
        @Index(name = "idx_score", columnList = "score"),
})
public class TdStockChannelScoreRecord {

    /**
     * primary key
     */
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * create time
     */
    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    /**
     * modify time
     */
    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    /**
     * 频道编号
     */
    @Column(name = "channel_number", nullable = false)
    private String channelNumber;

    /**
     * 最新日期（天）
     */
    @Column(name = "flag_date", nullable = false)
    private Instant flagDate;

    /**
     * 股票代码
     */
    @Column(name = "label_code", nullable = false)
    private String labelCode;

    /**
     * 评分
     */
    @Column(name = "score")
    private Integer score;

}

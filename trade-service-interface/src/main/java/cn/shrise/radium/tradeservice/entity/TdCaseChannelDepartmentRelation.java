package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/3/27 10:35
 * @Desc:
 **/
@Entity
@Table(name = "td_case_channel_department_relation", indexes = {
        @Index(name = "uk_channel_department", columnList = "channel_id, department_id", unique = true),
        @Index(name = "idx_channel_id", columnList = "channel_id"),
        @Index(name = "idx_department_id", columnList = "department_id")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdCaseChannelDepartmentRelation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "channel_id", nullable = false)
    private Long channelId;

    @Column(name = "department_id", nullable = false)
    private Integer departmentId;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

}

package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;

@Table(name = "td_portfolio_adjust_detail", indexes = {
        @Index(name = "PortfolioID", columnList = "PortfolioID"),
        @Index(name = "RecordID", columnList = "RecordID")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdPortfolioAdjustDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "RecordID")
    private Integer recordId;

    @Column(name = "PortfolioID")
    private Integer portfolioId;

    @Column(name = "Label", nullable = false, length = 64)
    private String label;

    @Column(name = "BSFlag")
    private Integer bSFlag;

    @Column(name = "XdrFlag")
    private Boolean xdrFlag;

    @Column(name = "DealAmount")
    private Double dealAmount;

    @Column(name = "Volume", precision = 20, scale = 7)
    private BigDecimal volume;

    @Column(name = "StartPositionRatio")
    private Double startPositionRatio;

    @Column(name = "EndPositionRatio")
    private Double endPositionRatio;

    @Column(name = "Price")
    private Double price;

    @Column(name = "EndPosition", precision = 20, scale = 7)
    private BigDecimal endPosition;

    @Column(name = "Status")
    private Integer status;

    @Column(name = "DealTime")
    private Instant dealTime;

    @Column(name = "CreateTime")
    private Instant createTime;

}
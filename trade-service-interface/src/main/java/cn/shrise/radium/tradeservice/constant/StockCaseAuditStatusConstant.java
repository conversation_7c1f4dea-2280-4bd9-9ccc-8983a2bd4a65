package cn.shrise.radium.tradeservice.constant;

/**
 * <AUTHOR>
 */
public class StockCaseAuditStatusConstant {

    /**
     * 待审核-合规
     */
    public static final Integer WAITING = 10;

    /**
     * 审核通过-合规
     */
    public static final Integer PASS = 20;

    /**
     * 审核未通过-合规
     */
    public static final Integer REJECT = 30;

    /**
     * 待确认-决策小组
     */
    public static final Integer WAITING_DECISION = 40;

    /**
     * 已确认-决策小组
     */
    public static final Integer PASS_DECISION = 50;

    /**
     * 已驳回-决策小组
     */
    public static final Integer REJECT_DECISION = 60;

    /**
     * 已作废
     */
    public static final Integer ABANDON = 70;
}

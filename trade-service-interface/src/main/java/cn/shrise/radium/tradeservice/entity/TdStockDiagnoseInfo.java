package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/28 19:20
 * @Desc:
 **/

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_stock_diagnose_info", indexes = {
        @Index(name = "idx_user_id", columnList = "user_id"),
        @Index(name = "idx_code", columnList = "code"),
        @Index(name = "idx_gmt_create", columnList = "gmt_create")
})
public class TdStockDiagnoseInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "code", length = 128)
    private String code;

    @Column(name = "finish_time")
    private Instant finishTime;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "type")
    private Integer type;

    @Column(name = "status")
    private Integer status;

    @Lob
    @Column(name = "result")
    private String result;

}

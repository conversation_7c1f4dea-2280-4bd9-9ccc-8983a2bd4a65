package cn.shrise.radium.tradeservice.entity;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Table(name = "td_portfolio_channel_customer_record", indexes = {
        @Index(name = "UserID", columnList = "UserID"),
        @Index(name = "OperatorID", columnList = "OperatorID"),
        @Index(name = "ChannelID", columnList = "ChannelID")
})
@Entity
@Getter
@Setter
@Builder
public class PortfolioChannelCustomerRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "UserID")
    private Integer userId;

    @Column(name = "ChannelID")
    private Integer channelId;

    @Column(name = "IsSub")
    private Boolean isSub;

    @Column(name = "OperatorID")
    private Integer operatorId;

    @Column(name = "CreateTime")
    private Instant createTime;
}
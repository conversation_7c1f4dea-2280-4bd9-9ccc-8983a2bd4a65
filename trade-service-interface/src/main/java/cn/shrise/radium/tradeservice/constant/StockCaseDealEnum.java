package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@Getter
public enum StockCaseDealEnum {

    /**
     * 案例操作类型
     */
    DT_OPEN(1, "新增案例"),
    DT_BUY(2, "教学加仓"),
    DT_SELL(3, "教学减仓"),
    DT_CLOSE(4, "取消关注"),
    DT_FOLLOW(10, "案例跟踪");
    private final Integer code;
    private final String value;

    StockCaseDealEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getTypeValue(Integer dealType) {
        for (StockCaseDealEnum typeEnum : StockCaseDealEnum.values()) {
            if (typeEnum.code.equals(dealType)) {
                return typeEnum.value;
            }
        }
        return null;
    }
}

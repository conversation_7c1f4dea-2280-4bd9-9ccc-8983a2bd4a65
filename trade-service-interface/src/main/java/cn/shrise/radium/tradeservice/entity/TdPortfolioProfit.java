package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Table(name = "td_portfolio_profit", indexes = {
        @Index(name = "PortfolioID", columnList = "PortfolioID", unique = true)
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdPortfolioProfit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Basic(fetch = FetchType.LAZY)
    @Column(name = "PortfolioID")
    private Integer portfolioId;

    @Column(name = "DayProfit")
    private Double dayProfit;

    @Column(name = "WeekProfit")
    private Double weekProfit;

    @Column(name = "MonthProfit")
    private Double monthProfit;

    @Column(name = "TotalProfit")
    private Double totalProfit;

    @Column(name = "MaxProfit")
    private Double maxProfit;

    @Column(name = "MaxProfitTime")
    private Instant maxProfitTime;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;
}
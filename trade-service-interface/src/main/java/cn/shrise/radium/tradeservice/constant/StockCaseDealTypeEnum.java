package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Getter
public enum StockCaseDealTypeEnum {
    /**
     * 案例操作类型
     */
    DT_OPEN(1, "建仓"),
    DT_BUY(2, "加仓"),
    DT_SELL(3, "减仓"),
    DT_CLOSE(4, "清仓"),
    DT_FOLLOW(10, "跟踪");

    private final Integer value;
    private final String msg;

    StockCaseDealTypeEnum(Integer value, String msg) {
        this.value = value;
        this.msg = msg;
    }
}

package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/3/24, 星期一
 **/
@Getter
public enum MyStockOperateType {

    ADD_STOCK(10, "添加自选"),
    DELETE_STOCK(20, "删除自选");

    private final Integer code;
    private final String msg;

    MyStockOperateType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}

package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * @Author: tang<PERSON><PERSON>un
 * @Date: 2024/8/16 13:27
 * @Desc:
 **/
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_recommend_stock_type_record")
public class TdRecommendStockTypeRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "type_id", nullable = false)
    private Integer typeId;

    @Column(name = "operator_id", nullable = false)
    private Integer operatorId;

    @Lob
    @Column(name = "content", nullable = false)
    private String content;
}

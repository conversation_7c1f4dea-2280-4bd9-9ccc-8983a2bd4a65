package cn.shrise.radium.tradeservice.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockCaseQuoteReq {

    @ApiModelProperty(value = "案例ID")
    private Long caseId;
    @ApiModelProperty(value = "股票代码")
    private String labelCode;
    @ApiModelProperty(value = "操作类型")
    private Integer dealType;

}

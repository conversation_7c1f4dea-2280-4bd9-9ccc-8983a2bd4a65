package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_recommend_stock_modify_record")
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdRecommendStockModifyRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "stock_id", nullable = false)
    private Integer stockId;

    @Column(name = "operate_id")
    private Integer operateId;

    @Column(name = "operate_content", nullable = false, length = 128)
    private String operateContent;
}
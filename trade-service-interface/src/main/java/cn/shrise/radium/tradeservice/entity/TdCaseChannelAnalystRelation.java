package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "td_case_channel_analyst_relation", indexes = {
        @Index(name = "channel_id_ibfk1_idx", columnList = "channel_id"),
        @Index(name = "uni_channel_analyst", columnList = "channel_id, analyst_id", unique = true),
        @Index(name = "analyst_ibfk2_idx", columnList = "analyst_id")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdCaseChannelAnalystRelation{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "channel_id", nullable = false)
    private Long channelId;

    @Column(name = "analyst_id", nullable = false)
    private Integer analystId;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;


}
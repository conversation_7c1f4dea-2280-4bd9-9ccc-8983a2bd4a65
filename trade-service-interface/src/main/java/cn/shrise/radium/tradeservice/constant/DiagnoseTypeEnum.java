package cn.shrise.radium.tradeservice.constant;

import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/28 19:30
 * @Desc:
 **/
@Getter
public enum DiagnoseTypeEnum {
    INTELLIGENCE_DIAGNOSE(10, "智能诊股");

    private final Integer code;
    private final String msg;

    DiagnoseTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}

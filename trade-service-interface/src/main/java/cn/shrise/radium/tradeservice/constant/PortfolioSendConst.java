package cn.shrise.radium.tradeservice.constant;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class PortfolioSendConst {

    public static final String OPEN_SMS_TEMPLATE = "【国诚】您订阅的「{}」发布了最新的推荐，请前往查看！";

    public static final String ADJUST_SMS_TEMPLATE = "【国诚】您订阅的「{}」【我的关注】更新了最新操作建议，请前往查看！";

    public static final String STOCK_CASE_TEMP1 = "【案例股】：{} {}【{} 资格编号{}】";
    public static final String STOCK_CASE_TEMP2 = "【案例股】：{}【跟踪价格】：{} 【仓位管理】：建议【{}%】{}【{} 资格编号{}】";
    public static final String STOCK_CASE_TEMP3 = "【案例股】：{}【取关价格】：{} {} 【{} 资格编号{}】";

    // 模板消息配置
    public static final String PORTFOLIO_BUY_TEMP = "portfolio-buy";
    public static final String PORTFOLIO_ADJUST_TEMP = "portfolio-adjust";
    public static final String STOCK_CASE_TEMP = "stock-case";

}

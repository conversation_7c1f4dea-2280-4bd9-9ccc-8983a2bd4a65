package cn.shrise.radium.tradeservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/28 10:36
 * @Desc:
 **/
@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockDiagnoseCreateReq {

    @ApiModelProperty(value = "股票代码", required = true)
    private String code;

    @ApiModelProperty(value = "股票代码名称", required = true)
    private String stockName;

    @ApiModelProperty(value = "userId", hidden = true)
    private Integer userId;

}

package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "td_promotion_stock_record", schema = "trade_db", indexes = {
        @Index(name = "idx_trade_date", columnList = "trade_date")
})
public class TdPromotionStockRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "is_success")
    private Boolean isSuccess;

    @Column(name = "trade_date")
    private Instant tradeDate;

    @Column(name = "channel")
    private Integer channel;

    @Lob
    @Column(name = "result")
    private String result;

}
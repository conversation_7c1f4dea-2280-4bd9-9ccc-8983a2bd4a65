<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="411px" height="461px" viewBox="-0.5 -0.5 411 461" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-04-27T13:59:32.631Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36&quot; etag=&quot;UgA_Gp5lrs_HBi6rzZNT&quot; version=&quot;17.4.6&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;EmosH6iTZMWhTRG_RIQu&quot; name=&quot;Page-1&quot;&gt;5VtLc6M4EP41rto92AUSLx/Hdmb2MFubqtTW7hxlUIAZjLxC+DG/fiWQbINw7EkFCyeHEGg9kPrxqbtpj+B8tftC0Tr5k0Q4GwEr2o3gYgSAHwB+FYR9TXAsuybENI1q0gnhKf2JJdGS1DKNcNHoyAjJWLpuEkOS5zhkDRqilGyb3Z5J1nzrGsVYIzyFKNOp/6QRS2pqAPwj/Q+cxol6s+1N65YVUp3lTooERWR7QoIPIzinhLD6brWb40zwTvGlHvf5TOthYRTn7JoBRUwT8m223PzNxrHnfH98Wj6O5SwblJVywyPgZXy+WbFGuVg120tWeP+VYqmzkGSEjuAn3kjj5W8QjsSL5/wK/cOtY/8u7uGMX5co/BFTUubRuDUWuO5hROv+ODxLczxOJI/FSPu4Fn4Xy//Vop8J50XXokXDuKi0q5oCrHf6LDO+UJxHBe/wmS+TXykfVlPq+Tl361c0X8vJNbsUGTQWAarNYyEHsadtkjL8tEahaN1yq+G0hK0y/mQfRm8wZXh3VtT2QYG44WGywozueRc1QOmcNDpb8FY8b48q7Mkuyan2WpKIpNXEh6mPisVvpG79gp5BS2MKjrihyUdCWUJikqPs4UidNdl27POVkLVk1nfM2F6iBioZabIS71L2rxg+ceXTt5OWxU7OXD3s1UPO91sPsn1XEcS4sTWxPF9RjqOrp8bwR0xTzjdMJbHeu9jwywLl/CElDfELjJRwyhCNMXuhn9OtIBRniKWb5jreXNqgA1Va0udouK6EFjKBCDcwCuC7DaNwLM0mYIdNOH2ZBJjeoUk0DMK6YAtvqPbwHtQeDlHtod88C0yrvaMxKcxIGQlXDzG8RXvjp+dh7+r0BDrLwLSDZ7C309MxCxW23ztWFIySH3he+4eLCD+jsnKtxOQ9HqfOlbjiGsWV4A5PCtsftXwn61W+0znFeEMlcK9UgpOwzYAWuBpwomiV5mO0Ts1DptuCzKkOmcFNEVM/eodvMm8CmAbAMTAKju/ejb6BpIO7QMBAQ8AtXg4C/6DTjC3N4x80axUBaNiFSpx8NLdxatJcHMM68CpkDEDQkP/Est0LOmDKbZzeBWhONdAMUZaJnPwgkNNpB9umkRPcY7AVwHawJXhuNFFtq691g07ZqVUOK2fneMNKVdt60q4sMB3zv03K2WAaRNrh5wExTIGI3RGslywZDMPa/qp5hum+PaHRgFSsfU4Z55hKSr+EXL/EpOc0y5S/lJMc95NMB77Ot85k+rQ3xumQj1k4mUyM61gbxmDHh4fbRpFnco6cJPBfYxjfOmtyBWVpnPP7kLNAeBkzwaCUe6CfZMMqjaLahcJF+hMtq6kEu9ckzVm1IXc2chdiLu411SUc1dRND19qbE9K7LbCe10ufodcQG9y8TS5hGXB+OwfRybwsky6Slz6k4mvn2AsEQKx8Ebs6aMIRovorgOx3gTj6M5rq1xMVqUtTivCILCBK0o0zpWana2Pe225W/tFc7JakVy9bkkV/Ul5RO2Gs9VoZ2rXDJ927SI14JkuUnMH9ZldxfDn4/+OHOdr86id2QChLnLZNrhNTk3pxOWsgdEv8o7uGGmqo9IG4T5LuYZQeNmqlrUufV0eCAcs+atkohBWwbmUitshpZ5coHYZUQesOx3W6vUG6+/+u9+15td7yenVNukZtUndKX7nNgnbaULTNunaGsdv+9URXm+UH/rw9I0aqh4pvXNDdbyBHZ5ul/9yBzHRAjG0RMX7C35gRzq5K/h5RVaUPx5/ZFa1nfxSDz78Dw==&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="0" y="145" width="60" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 195px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(33 , 37 , 41) ; background-color: rgb(255 , 255 , 255) ; line-height: 1"><font style="font-size: 12px">Backends For Frontends</font></span></div></div></div></foreignObject><text x="30" y="199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Backends F...</text></switch></g><path d="M 170 70 L 170.62 101.62" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 170.73 106.87 L 167.09 99.94 L 170.62 101.62 L 174.09 99.8 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 155 70 C 155 54 155 46 170 46 C 160 46 160 30 170 30 C 180 30 180 46 170 46 C 185 46 185 54 185 70 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 265 70 L 265 103.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 265 108.88 L 261.5 101.88 L 265 103.63 L 268.5 101.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 250 70 C 250 54 250 46 265 46 C 255 46 255 30 265 30 C 275 30 275 46 265 46 C 280 46 280 54 280 70 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="120" y="110" width="290" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 288px; height: 1px; padding-top: 125px; margin-left: 121px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">cloud gateway</div></div></div></foreignObject><text x="265" y="129" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">cloud gateway</text></switch></g><path d="M 169.3 140 L 169.3 160 L 169.78 173.64" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 169.96 178.88 L 166.22 172.01 L 169.78 173.64 L 173.21 171.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 170 210 L 170 235 L 169.48 253" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 169.33 258.25 L 166.03 251.15 L 169.48 253 L 173.03 251.36 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="130" y="180" width="80" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 195px; margin-left: 131px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">admin-api</div></div></div></foreignObject><text x="170" y="199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">admin-api</text></switch></g><path d="M 265 140 L 265 173.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 265 178.88 L 261.5 171.88 L 265 173.63 L 268.5 171.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 265 210 L 265 253.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 265 258.88 L 261.5 251.88 L 265 253.63 L 268.5 251.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="225" y="180" width="80" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 195px; margin-left: 226px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">web-api</div></div></div></foreignObject><text x="265" y="199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">web-api</text></switch></g><path d="M 359.25 142.01 L 359.87 173.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 359.98 178.88 L 356.34 171.95 L 359.87 173.63 L 363.34 171.81 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 360 210 L 360 235 L 360.09 254.98" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 360.11 260.23 L 356.58 253.25 L 360.09 254.98 L 363.58 253.22 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="320" y="180" width="80" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 195px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">callback-api</div></div></div></foreignObject><text x="360" y="199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">callback-api</text></switch></g><path d="M 360 70 L 360.59 102.97" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 360.68 108.22 L 357.06 101.29 L 360.59 102.97 L 364.06 101.16 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 345 70 C 345 54 345 46 360 46 C 350 46 350 30 360 30 C 370 30 370 46 360 46 C 375 46 375 54 375 70 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="130" y="270" width="80" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 285px; margin-left: 131px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">user-service</div></div></div></foreignObject><text x="170" y="289" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">user-service</text></switch></g><rect x="225" y="270" width="80" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 285px; margin-left: 226px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">auth-service</div></div></div></foreignObject><text x="265" y="289" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">auth-service</text></switch></g><rect x="320" y="270" width="80" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 285px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-service</div></div></div></foreignObject><text x="360" y="289" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">order-service</text></switch></g><rect x="120" y="260" width="290" height="90" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="130" y="310" width="80" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 325px; margin-left: 131px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">etc...</div></div></div></foreignObject><text x="170" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">etc...</text></switch></g><rect x="135" y="0" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 10px; margin-left: 170px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">admin user</div></div></div></foreignObject><text x="170" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">admin user</text></switch></g><rect x="235" y="0" width="60" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 10px; margin-left: 265px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">customer</div></div></div></foreignObject><text x="265" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">customer</text></switch></g><rect x="320" y="0" width="80" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 10px; margin-left: 360px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">other event</div></div></div></foreignObject><text x="360" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">other event</text></switch></g><rect x="0" y="255" width="60" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 305px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#212529"><span style="background-color: rgb(255 , 255 , 255)">Common<br />Service<br /></span></font></div></div></div></foreignObject><text x="30" y="309" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Common...</text></switch></g><path d="M 169.3 351.98 L 169.3 375 L 169.82 393.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 169.97 398.88 L 166.27 391.98 L 169.82 393.63 L 173.27 391.79 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 150 415 C 150 406.72 158.95 400 170 400 C 175.3 400 180.39 401.58 184.14 404.39 C 187.89 407.21 190 411.02 190 415 L 190 445 C 190 453.28 181.05 460 170 460 C 158.95 460 150 453.28 150 445 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 415 C 190 423.28 181.05 430 170 430 C 158.95 430 150 423.28 150 415" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 265 350 L 265 393.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 265 398.88 L 261.5 391.88 L 265 393.63 L 268.5 391.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 245 415 C 245 406.72 253.95 400 265 400 C 270.3 400 275.39 401.58 279.14 404.39 C 282.89 407.21 285 411.02 285 415 L 285 445 C 285 453.28 276.05 460 265 460 C 253.95 460 245 453.28 245 445 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 285 415 C 285 423.28 276.05 430 265 430 C 253.95 430 245 423.28 245 415" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 360.7 350 L 360.7 375 L 360.18 393.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 360.03 398.88 L 356.73 391.79 L 360.18 393.63 L 363.73 391.98 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 340 415 C 340 406.72 348.95 400 360 400 C 365.3 400 370.39 401.58 374.14 404.39 C 377.89 407.21 380 411.02 380 415 L 380 445 C 380 453.28 371.05 460 360 460 C 348.95 460 340 453.28 340 445 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 380 415 C 380 423.28 371.05 430 360 430 C 348.95 430 340 423.28 340 415" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="370" width="60" height="90" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 415px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#212529"><span style="background-color: rgb(255 , 255 , 255)">Database<br /></span></font></div></div></div></foreignObject><text x="30" y="419" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Database&#xa;</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>
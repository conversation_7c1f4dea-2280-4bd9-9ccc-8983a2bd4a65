# RocketMQ商业库

## 用法

### 快速上手

发送消息

```java
//发送普通消息 使用自定义工具类
rocketmqUtils.send(topic, tag, key, delay, data);

//发送异步消息 使用自定义工具类
rocketmqUtils.sendAsync(topic, tag, key, delay, data);

//发送单向消息 使用自定义工具类
rocketmqUtils.sendOneway(topic, tag, data);

//发送顺序消息 使用自定义工具类
rocketmqUtils.sendOrderly(topic, tag, shardingKey, data);
```

接收消息

```java
//收消息 使用自定义注解并实现对应MessageListener/MessageOrderListener接口
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = TOPIC_PAY_NOTIFY, consumerGroup = GROUP_PAY_ORDER_NOTIFY, selectorExpression = TAG_PAY_ORDER_NOTIFY)
public class OrderNotifyConsumer implements MessageListener {

    private final OrderNotifyService orderNotifyService;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        OrderNotifyReq notifyReq = JSON.parseObject(message.getBody(), OrderNotifyReq.class);
        log.info("OrderNotifyReq: {}", notifyReq);
        orderNotifyService.handleOrderNotify(notifyReq);
        return Action.CommitMessage;
    }
}
```

### API

该库包含一个工具类和两个注解，工具类`RocketmqUtils`

|注解|需要实现的接口| 备注      |
|---|---|---------|
|OnsConsumer|MessageListener| 普通消息消费者 |
|OnsOrderConsumer|MessageOrderListener| 顺序消息消费者 |


## 支持的特性

### 占位符支持

注解上的参数支持传入动态变量，语法为`${}`

| 变量                 | 备注    |
|--------------------|-------|
| topic              | topic |
| consumerGroup      | 消费组   |
| selectorExpression |表达式|
| consumeThreadNums  |消费者线程数|


```java
@OnsConsumer(topic = "${topic}", consumerGroup = "${group}")
```

### 链路追踪

在消费者内部的日志输出会携带traceId，使用者无需额外配置

日志输出示例

```text
2022-02-20 23:01:28.662  INFO [a-service,d0a7a4be9e9f3ab4,cb4cb09a83e0c553] 20570 --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
```


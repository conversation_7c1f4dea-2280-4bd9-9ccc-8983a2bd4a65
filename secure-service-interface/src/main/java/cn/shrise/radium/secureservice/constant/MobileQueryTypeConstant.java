package cn.shrise.radium.secureservice.constant;

public class MobileQueryTypeConstant {

    // 查询微盟用户
    public static final Integer MQTC_WEI_MOB_USER = 10;
    // 拨打电话
    public static final Integer MQTC_CALL = 20;
    // 电话推送
    public static final Integer MQTC_CALL_PUSH = 30;
    // 短信推送
    public static final Integer MQTC_SMS_PUSH = 40;
    // 微盟用户发放积分
    public static final Integer MQTC_WEI_MOB_POINT = 50;
    // 三要素认证
    public static final Integer MQTC_VERIFY_ID_CARD = 60;
    // 短信验证码
    public static final Integer MQTC_VERIFICATION_CODE = 70;
    // 微信投诉详情页查看客户联系方式
    public static final Integer MQTC_COMPLAINT_DETAIL_WX = 80;
}

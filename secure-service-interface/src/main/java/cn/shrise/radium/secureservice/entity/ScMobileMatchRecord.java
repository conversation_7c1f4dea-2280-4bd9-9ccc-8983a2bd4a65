package cn.shrise.radium.secureservice.entity;

import cn.hutool.core.util.StrUtil;
import lombok.*;
import javax.persistence.*;
import java.time.Instant;

@Entity
@Table(
    name = "sc_mobile_match_record",
    indexes = {
        @Index(name = "pk_sc_mobile_match_record", columnList = "id", unique = true)
    }
)
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ScMobileMatchRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "user_type", nullable = false)
    private Integer userType;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "mask_mobile", length = 128)
    private String maskMobile;

    @Lob
    @Column(name = "mobile_md5")
    private String mobileMd5;

    @Column(name = "product_type")
    private Integer productType;

    @Column(name = "menu_name", length = 128)
    private String menuName;

    @Column(name = "ip", length = 128)
    private String ip;
}
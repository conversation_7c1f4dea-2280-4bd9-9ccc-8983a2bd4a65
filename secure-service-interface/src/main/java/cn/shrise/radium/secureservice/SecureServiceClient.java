package cn.shrise.radium.secureservice;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.secureservice.entity.ScMobile;
import cn.shrise.radium.secureservice.entity.ScMobileQueryRecord;
import cn.shrise.radium.secureservice.req.MobileQueryReq;
import cn.shrise.radium.secureservice.resp.MobileInfoResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.Instant;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

@FeignClient("secure-service")
public interface SecureServiceClient {

    @GetMapping("mobile/info")
    @ApiOperation("获取手机号信息")
    BaseResult<MobileInfoResp> getMobileInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("调用类型") Integer queryType,
            @RequestParam @ApiParam("手机号id") Long mobileId,
            @RequestParam(required = false) @ApiParam("调用人id") Integer queryId,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId);

//    @GetMapping("mobile/info/list")
//    @ApiOperation("获取手机号信息列表")
//    BaseResult<List<ScMobile>> getMobileInfoList(
//            @RequestParam @ApiParam("公司类型") Integer companyType,
//            @RequestParam @ApiParam("调用类型") Integer queryType,
//            @RequestParam(required = false) @ApiParam("调用人id") Integer queryId,
//            @RequestBody @ApiParam("手机号id") BatchReq<MobileQueryReq> req);

    @PostMapping("mobile/create")
    @ApiOperation("创建手机号信息")
    BaseResult<Long> createMobileInfo(
            @RequestParam @ApiParam("手机号") String mobile);

    @PostMapping("mobile/id")
    @ApiOperation("获取手机号id")
    BaseResult<Long> getMobileId(
            @RequestParam @ApiParam("手机号") String mobile);

    @GetMapping("mobile-query-record/list")
    @ApiOperation("查询手机号调用列表")
    PageResult<List<ScMobileQueryRecord>> getMobileQueryRecordList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("调用类型") Integer queryType,
            @RequestParam(required = false) @ApiParam("开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) Integer size);

    @PostMapping("mobile-match-record")
    @ApiOperation("手机号匹配记录")
    BaseResult<Void> mobileMatchRecord(
            @RequestParam @ApiParam("手机号") String mobile,
            @RequestParam @ApiParam("用户类型") Integer userType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("菜单") String menuName,
            @RequestParam(required = false) @ApiParam("产品类型") Integer productType,
            @RequestParam(required = false) @ApiParam("ip") String ip) ;

}

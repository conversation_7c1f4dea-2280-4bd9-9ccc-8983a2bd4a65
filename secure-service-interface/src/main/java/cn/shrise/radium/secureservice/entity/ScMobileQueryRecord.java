package cn.shrise.radium.secureservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Table(name = "sc_mobile_query_record", indexes = {
        @Index(name = "idx_gmt_create", columnList = "gmt_create"),
        @Index(name = "idx_query_type", columnList = "query_type")
})
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ScMobileQueryRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "query_type", nullable = false)
    private Integer queryType;

    @Column(name = "mobile_id", nullable = false)
    private Long mobileId;

    @Column(name = "mobile", nullable = false, length = 64)
    private String mobile;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "query_id")
    private Integer queryId;
}
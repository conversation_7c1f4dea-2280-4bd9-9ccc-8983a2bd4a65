package cn.shrise.radium.userservice.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "uc_department_operate_record")
public class UcDepartmentOperateRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "operator_id", nullable = false)
    private Integer operateId;

    @Column(name = "dept_id", nullable = false)
    private Integer deptId;

    @Column(name = "title", nullable = false, length = 256)
    private String title;

    @Column(name = "content")
    private String content;
}

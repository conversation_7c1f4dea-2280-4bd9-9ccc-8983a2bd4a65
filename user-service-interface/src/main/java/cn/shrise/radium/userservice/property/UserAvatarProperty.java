package cn.shrise.radium.userservice.property;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "avatar")
public class UserAvatarProperty {

    private String domain;

    private List<Image> imageList;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Image{
        private String img;
    }
}

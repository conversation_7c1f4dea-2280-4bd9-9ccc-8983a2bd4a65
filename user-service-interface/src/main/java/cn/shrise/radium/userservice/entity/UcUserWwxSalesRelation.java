package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "uc_user_wwx_sales_relation", indexes = {
        @Index(name = "uk_user_wx_sales", columnList = "user_id, wx_id, sales_id", unique = true)
})
public class UcUserWwxSalesRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "wx_id")
    private Integer wxId;

    @Column(name = "sales_id", nullable = false)
    private Integer salesId;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "enabled")
    private Boolean enabled;
}
package cn.shrise.radium.userservice.entity;

import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Embeddable
@EqualsAndHashCode
public class UcServerPermissionPackageId implements Serializable {
    private static final long serialVersionUID = -7430405143921337095L;
    @Column(name = "UserID", nullable = false)
    private Integer userId;
    @Column(name = "PackageID", nullable = false)
    private Integer packageId;

    public Integer getPackageId() {
        return packageId;
    }

    public void setPackageId(Integer packageID) {
        this.packageId = packageID;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userID) {
        this.userId = userID;
    }

}
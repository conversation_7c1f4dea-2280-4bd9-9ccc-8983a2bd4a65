package cn.shrise.radium.userservice.constant;

/**
 * <AUTHOR>
 */
public class IdentityTypeConstant {

    public static final int UNKNOWN = 0;
    /**
     * 身份证
     */
    public static final int ID_CARD = 100;
    /**
     * 中国护照
     */
    public static final int PASSPORT = 200;
    /**
     * 台湾来往大陆通行证
     */
    public static final int TW_CARD = 300;
    /**
     * 香港来往大陆通行证
     */
    public static final int HK_CARD = 310;
    /**
     * 澳门来往大陆通行证
     */
    public static final int MC_CARD = 320;
    /**
     * 中国永久居留证
     */
    public static final int GREEN_CARD = 390;
    /**
     * 外籍证件（国外护照）
     */
    public static final int FOREIGN_CARD = 400;
    /**
     * 其它
     */
    public static final int OTHER = 999;

}

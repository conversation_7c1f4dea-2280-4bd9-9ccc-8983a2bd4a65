package cn.shrise.radium.userservice.property;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "one-key-login")
public class OneKeyLoginProperty {

    private String publicKey;

    private String privateKey;

    private H5 h5;

    private App app;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class H5 {
        private String url;
        private String appId;
        private String appKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class App {
        private String url;
        private Ios ios;
        private Android android;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ios {
        private String appId;
        private String appKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Android {
        private String appId;
        private String appKey;
    }
}

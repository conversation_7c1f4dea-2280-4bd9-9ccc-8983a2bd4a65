package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "uc_sales_dept_chat_visiable", indexes = {
        @Index(name = "uk_sales_dept", columnList = "sales_id,dept_id", unique = true)
})
public class UcSalesDeptChatVisible {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "sales_id", nullable = false)
    private Integer salesId;

    @Column(name = "dept_id", nullable = false)
    private Integer deptId;
}
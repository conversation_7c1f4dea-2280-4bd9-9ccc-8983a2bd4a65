package cn.shrise.radium.userservice.property;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Builder
@Configuration
@NoArgsConstructor
@AllArgsConstructor
public class TopicPdfProperty {

    private Integer orderIndex;

    private String content;

    private String answerChoice;

    private List<ChoicePdfProperty> choiceList;

    @Data
    @Builder
    @Configuration
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChoicePdfProperty {

        private String choiceNumber;

        private String content;

    }
}

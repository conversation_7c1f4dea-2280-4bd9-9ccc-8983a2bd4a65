package cn.shrise.radium.userservice.property;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "verify")
public class VerifyProperty {

    private String appCode;
    private String host2;
    private String host3;
    private String path2;
    private String path3;

}

package cn.shrise.radium.userservice;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ServiceConstant;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.userservice.dto.*;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.error.UsErrorCode;
import cn.shrise.radium.userservice.lindorm.entity.LdCustomerTrackEvent;
import cn.shrise.radium.userservice.lindorm.entity.LdCustomerTrackEventStat;
import cn.shrise.radium.userservice.lindorm.entity.LdStaffTrackEvent;
import cn.shrise.radium.userservice.req.*;
import cn.shrise.radium.userservice.req.evaluation.*;
import cn.shrise.radium.userservice.resp.*;
import cn.shrise.radium.userservice.resp.call.*;
import cn.shrise.radium.userservice.resp.evaluation.*;
import cn.shrise.radium.userservice.resp.staffrole.StaffRoleResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

@FeignClient(name = ServiceConstant.USER_SERVICE)
public interface UserClient {

    @GetMapping("departments/director")
    @ApiOperation("获得部门领导信息")
    BaseResult<UcDepartmentDirector> getDepartmentDirector(@RequestParam(required = false) @ApiParam("部门id") Integer departmentId);

    @PostMapping("departments/batch/serverCount")
    @ApiOperation("批量获取部门在职员工人数")
    BaseResult<Map<Integer, Integer>> batchGetDepartmentServerCount(
            @RequestBody BatchReq<Integer> req);

    @PostMapping("departments/director/batch")
    @ApiOperation("根据部门ID批量获取部门领导信息")
    BaseResult<Map<Integer, UcDepartmentDirector>> batchGetDepDirectorList(@RequestBody BatchReq<Integer> req);

    @GetMapping("departments/{deptId}/users")
    @ApiOperation("根据部门ID获取用户信息")
    BaseResult<List<UserDTO>> getUsersByDepartmentId(
            @PathVariable("deptId") Integer deptId,
            @RequestParam(required = false) @ApiParam("用户状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("用户状态") Integer companyType);

    /**
     * 根据部门ID获取部门下的所有用户信息
     *
     * @param deptId  部门id
     * @param enabled 用户状态
     * @return 用户列表
     */
    @GetMapping("departments/{deptId}/allUsers")
    @ApiOperation("根据部门ID获取部门下的所有用户信息")
    BaseResult<List<UserDTO>> getAllUsersByDepartmentId(
            @PathVariable Integer deptId,
            @RequestParam(required = false) @ApiParam("用户状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("用户状态") Integer companyType
    );

    @GetMapping("departments/users")
    @ApiOperation("获取所有部门用户信息")
    BaseResult<List<UserDTO>> getUserListWithDepartment(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户状态") Boolean enabled);

    @GetMapping("departments")
    @ApiOperation("根据用户ID获取子部门结构信息")
    BaseResult<List<DepartmentDTO>> getDepartment(
            @RequestParam(required = false) @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam(value = "是否部门考评", defaultValue = "false") Boolean dept);

    @GetMapping("departments/{deptId}")
    @ApiOperation("根据部门ID获取部门结构信息")
    BaseResult<List<DepartmentDTO>> getDepartmentTree(
            @PathVariable("deptId") Integer deptId,
            @RequestParam(required = false) @ApiParam("用户状态") Boolean enabled);

    @GetMapping(value = "users/{userId}")
    @ApiOperation("根据用户ID查询用户")
    BaseResult<UcUsers> getUser(@PathVariable Integer userId);

    @GetMapping("users/number")
    @ApiOperation("根据number查询用户")
    BaseResult<UcUsers> getUser(@RequestParam Integer companyType,
                                @RequestParam String number,
                                @RequestParam(required = false) Boolean enabled);

    default BaseResult<UcUsers> getUser(Integer companyType, String number) {
        return getUser(companyType, number, null);
    }

    @GetMapping("users/update/customerExt")
    @ApiOperation("退款时，更新UcCustomerExt")
    BaseResult<UcCustomerExt> updateCustomerExtRefund(@RequestParam @ApiParam("用户ID") Integer userId);

    @GetMapping("users/update/customerExt/traded")
    @ApiOperation("支付成功时，更新UcCustomerExt标志位")
    BaseResult<UcCustomerExt> updateCustomerExtTraded(@RequestParam @ApiParam("用户ID") Integer userId);

    @GetMapping("users/ext")
    BaseResult<UcCustomerExt> getUserExt(@RequestParam @ApiParam("用户ID") Integer userId);

    @PostMapping("users/ext/batch")
    @ApiOperation("批量查询用户扩展信息")
    @Operation(summary = "根据用户ID查询用户扩展信息")
    BaseResult<List<UcCustomerExt>> batchUserExt(@RequestBody @Valid @ApiParam("用户ID列表") BatchReq<Integer> req);

    @PatchMapping("users/ext")
    BaseResult<String> updateUserExt(@RequestBody @Valid UpdateUserExtReq req);

    @GetMapping("users/byMobileId")
    @ApiOperation("根据用户手机号查询用户")
    BaseResult<UcUsers> getUserByMobileId(@RequestParam @ApiParam("手机号id") Long mobileId,
                                          @RequestParam @ApiParam("公司类型") Integer companyType,
                                          @RequestParam(required = false) @ApiParam("用户类型") Integer userType);

    @GetMapping("users")
    @ApiOperation("查询用户列表")
    PageResult<List<UcUsers>> getUserList(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户类型") Integer userType,
            @RequestParam(required = false) @ApiParam("用户状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("搜索条件") String searchText,
            @RequestParam(defaultValue = "true") @ApiParam("排序") Boolean asc,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("users/batch")
    @ApiOperation("批量查询用户")
    @Deprecated
    BaseResult<List<UcUsers>> batchGetUserList(
            @RequestParam @ApiParam("用户id列表") Collection<Integer> userIds);

    @PostMapping("users/server-ext/batch")
    @ApiOperation("根据用户ID批量查询用户扩展信息map")
    BaseResult<Map<Integer, UcServerExt>> batchGetServerExtMap(@RequestBody BatchReq<Integer> req);

    @PostMapping("users/batch")
    @ApiOperation("根据用户ID批量获取用户map")
    BaseResult<Map<Integer, UcUsers>> batchGetUserMap(@RequestBody BatchReq<Integer> req);

    @PostMapping("users/filterNoCancel")
    @ApiOperation("过滤未注销用户")
    BaseResult<List<Integer>> filterNoCancel(@RequestBody BatchReq<Integer> req);

    @PostMapping("users/batch/list")
    @ApiOperation("根据用户ID批量获取用户")
    BaseResult<List<UcUsers>> batchGetUserList(@RequestBody BatchReq<Integer> req);

    @PostMapping("users/batch/mobileId")
    @ApiOperation("根据手机号批量查询用户")
    BaseResult<List<UcUsers>> getUserListByMobileId(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestBody @ApiParam("手机号id列表") BatchReq<Long> req,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled);

    @PostMapping("users/cancel")
    @ApiOperation("注销用户")
    BaseResult<Void> cancelUser(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("产品类型") Integer productType);

    @GetMapping("users/base/batch")
    @ApiOperation("批量查询用户基础信息")
    BaseResult<Map<Integer, UserBaseInfoResp>> batchGetBaseUserMap(@RequestBody BatchReq<Integer> req);

    @GetMapping("users/address")
    @ApiOperation("批量获取用户地址map")
    BaseResult<Map<Integer, UcUserAddress>> getUserAddressMap(
            @RequestParam @ApiParam("用户id列表") Collection<Integer> userIds);

    @PostMapping("user/address/create")
    @ApiOperation("创建/更新用户收货地址信息")
    BaseResult<Void> createUserAddress(
            @RequestBody @Validated CreateUserAddressReq req);

    @GetMapping("user/address/{userId}")
    @ApiOperation("获取用户收货地址信息")
    BaseResult<UcUserAddress> findUserAddress(
            @PathVariable Integer userId);

    @GetMapping("users/first")
    @ApiOperation("获取首个用户")
    BaseResult<UcUsers> getFirstUser();

    @GetMapping("users/last")
    @ApiOperation("获取首个用户")
    BaseResult<UcUsers> getLastUser();

    @GetMapping("users/range")
    @ApiOperation("按id范围获取用户")
    BaseResult<List<UcUsers>> getRangeUser(@RequestParam Integer startId, @RequestParam Integer endId);

    @GetMapping("users/batchSearchUserByName")
    @ApiOperation("按姓名批量模糊搜索用户")
    BaseResult<List<UcUsers>> batchSearchUserByName(
            @RequestParam @ApiParam("姓名") String content,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @GetMapping("staff")
    @ApiOperation(value = "查询员工列表")
    PageResult<List<UcMainStaff>> getStaffList(
            @RequestParam(required = false) @ApiParam("主账号公司类型") Integer mainCompanyType,
            @RequestParam(required = false) @ApiParam("用户名") String userName,
            @RequestParam(required = false) @ApiParam("员工状态") Boolean enabled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("staff/{id}")
    @ApiOperation("根据id查询员工")
    BaseResult<UcMainStaff> getStaff(@PathVariable @ApiParam("员工id") Integer id);

    @GetMapping("staff/{mainCompanyType}/{userName}")
    @ApiOperation("查询当前公司下的员工")
    BaseResult<UcMainStaff> getStaff(
            @PathVariable @ApiParam("主账号公司类型") Integer mainCompanyType,
            @PathVariable @ApiParam("用户名") String userName,
            @RequestParam(required = false) @ApiParam("密码") String password,
            @RequestParam(required = false) @ApiParam("账户状态") Boolean enabled);

    @GetMapping("staff/mobile")
    @ApiOperation("查询员工")
    BaseResult<UcMainStaff> getStaff(
            @RequestParam @ApiParam("主账号公司类型") Integer mainCompanyType,
            @RequestParam @ApiParam("手机号") String mobile,
            @RequestParam(required = false) @ApiParam("账户状态") Boolean enabled);

    @GetMapping("staff/relation")
    @ApiOperation("查询员工子账号关系")
    PageResult<List<UcServerRelation>> getStaffRelation(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("员工id") Integer staffId,
            @RequestParam(required = false) @ApiParam("主账号公司类型") Integer mainCompanyType,
            @RequestParam(required = false) @ApiParam("主账号类型") Integer mainAccountType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("staff/{companyType}/relation")
    @ApiOperation("查询当前公司下员工子账号关系")
    BaseResult<UcServerRelation> getStaffRelationByCompanyType(
            @PathVariable @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("员工id") Integer staffId,
            @RequestParam @ApiParam("主账号公司类型") Integer mainCompanyType,
            @RequestParam @ApiParam("主账号类型") Integer mainAccountType);

    @PatchMapping("staff/{id}")
    @ApiOperation("修改员工信息")
    BaseResult<UcMainStaff> updateStaff(@PathVariable Integer id,
                                        @Valid @RequestBody UpdateStaffReq req);

    @GetMapping("staff/search")
    @ApiOperation(value = "搜索主账号")
    BaseResult<List<MainStaffResp>> searchStaff(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户名") String searchContent,
            @RequestParam(required = false) @ApiParam("员工状态") Boolean enabled);

    @GetMapping("permissions/packages")
    @ApiOperation("查询权限包列表")
    BaseResult<List<UcPermissionPackage>> getPermissionPackageList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled);

    @GetMapping("permissions/{userId}")
    @ApiOperation("查询用户权限点")
    BaseResult<List<Integer>> getPermissionList(@PathVariable Integer userId);

    @GetMapping("permissions/{userId}/packages")
    @ApiOperation("查询用户的权限包列表")
    BaseResult<List<UcPermissionPackage>> getUserPermissionPackageList(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "true") @ApiParam("是否启用") Boolean enabled);

    @GetMapping("permissions/{userId}/server")
    @ApiOperation("查询用户的通用权限")
    BaseResult<List<UcServerAdminPermission>> getUserServerPermissionList(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) Integer permissionType,
            @RequestParam(required = false) Boolean isExpired);

    @GetMapping("wx/info")
    @ApiOperation("根据（accountType，unionId）查询微信信息")
    BaseResult<WxDTO> getWxInfo(
            @RequestParam @ApiParam("微信类型") Integer accountType,
            @RequestParam @ApiParam("微信用户unionId") String unionId);

    @GetMapping("wx/ext/users")
    @ApiOperation("查询微信账号信息")
    BaseResult<List<UserWxExtDto>> filterWxExtByUserIds(
            @RequestParam @ApiParam("用户id") Set<Integer> userIds);

    @PostMapping("wx/ext/batchUnionId")
    @ApiOperation("批量查询用户扩展信息")
    BaseResult<List<UcWxExt>> batchWxExtByUnionId(@RequestParam @ApiParam("公司类型") Integer companyType,
                                                  @RequestBody @Valid @ApiParam("UnionId列表") BatchReq<String> req);

    @PostMapping("wx/ext/batchGetTradedUnionMap")
    @ApiOperation("过滤是否成交UnionMap")
    BaseResult<Map<String, Boolean>> batchGetTradedUnionMap(@RequestParam @ApiParam("公司类型") Integer companyType,
                                                            @RequestParam @ApiParam("是否成交") Boolean isTraded,
                                                            @RequestBody @Valid @ApiParam("UnionId列表") BatchReq<String> req);

    @PatchMapping("users/{userId}")
    @ApiOperation("修改用户信息")
    BaseResult<UcUsers> updateUser(@PathVariable Integer userId, @Valid @RequestBody UpdateUserReq req);

    @GetMapping("users/refresh_token")
    @ApiOperation("查询用户refresh token")
    BaseResult<UcLoginSession> getRefreshToken(
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam @ApiParam("用户类型") String refreshToken);

    @PostMapping("users/refresh_token")
    @ApiOperation("创建用户refresh token")
    BaseResult<UcLoginSession> createRefreshToken(@RequestBody @Valid CreateRefreshTokenReq req);

    @GetMapping("departments/full_path")
    @ApiOperation("获取某个销售的部门全路径")
    BaseResult<List<UcDepartment>> getDepartmentFullPath(
            @RequestParam @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("过滤的根节点深度（即过滤几层根节点）") Integer filterRootDepth);

    /**
     * 批量查询部门信息
     *
     * @param deptIds 部门id
     * @return 部门列表
     */
    @GetMapping("departments/batch")
    BaseResult<List<UcDepartment>> batchGetDeptList(@RequestParam @ApiParam("部门id列表") List<Integer> deptIds);

    @PostMapping("departments/add")
    @ApiOperation("创建部门")
    BaseResult<UsErrorCode> addDepartment(
            @RequestParam @ApiParam("父部门id") Integer superId,
            @RequestParam @ApiParam("部门名称") String name,
            @RequestParam(required = false) @ApiParam("部门主任名字") Integer headId,
            @RequestParam(required = false) @ApiParam("部门经理名字") Integer managerId,
            @RequestParam(required = false) @ApiParam("部门总监名字") Integer directorId,
            @RequestParam @ApiParam("操作人id") Integer userId);

    @PostMapping("departments/edit")
    @ApiOperation("编辑部门")
    BaseResult<UsErrorCode> editDepartment(
            @RequestParam @ApiParam("部门id") Integer departmentId,
            @RequestParam @ApiParam("操作人id") Integer userId,
            @RequestParam(required = false) @ApiParam("部门名称") String name,
            @RequestParam(required = false) @ApiParam("部门主任名字") Integer headId,
            @RequestParam(required = false) @ApiParam("部门经理名字") Integer managerId,
            @RequestParam(required = false) @ApiParam("部门总监名字") Integer directorId);

    @PostMapping("departments/modify")
    @ApiOperation("修改上级部门")
    BaseResult<UsErrorCode> modifyDepartment(
            @RequestParam @ApiParam("部门id") Integer departmentId,
            @RequestParam @ApiParam("操作人id") Integer userId,
            @RequestParam(required = false) @ApiParam("父部门id") Integer superId);

    @PostMapping("departments/disabled")
    @ApiOperation("禁用部门")
    BaseResult<UsErrorCode> disabledDepartment(
            @RequestParam @ApiParam("部门id") Integer departmentId,
            @RequestParam @ApiParam("操作人id") Integer userId);

    @PostMapping("departments/enabled")
    @ApiOperation("启用部门")
    BaseResult<UsErrorCode> enabledDepartment(
            @RequestParam @ApiParam("部门id") Integer departmentId,
            @RequestParam @ApiParam("操作人id") Integer userId);

    @GetMapping("departments/getOperateRecordList")
    @ApiOperation("获得部门操作记录信息")
    BaseResult<List<UcDepartmentOperateRecord>> getUcDepartmentOperateRecordList(@RequestParam @ApiParam("部门id") Integer departmentId);

    @GetMapping("departments/get-leaf-dept-list")
    @ApiOperation("获取某个部门下的叶子节点部门")
    BaseResult<List<UcDepartment>> getLeafDepartmentList(
            @RequestParam @ApiParam("部门id") Integer departmentId);


    @GetMapping("departments/has_user")
    @ApiOperation("部门及子部门是否有员工")
    BaseResult<Boolean> isDepartmentAndChildHasUser(
            @RequestParam @ApiParam("部门id") Integer departmentId,
            @RequestParam(required = false) @ApiParam("员工状态") Boolean userEnabled);

    @GetMapping("wx/findByUnionId")
    BaseResult<UcWxExt> findByUnionId(@RequestParam @ApiParam("公司类型") Integer companyType,
                                      @RequestParam @ApiParam("unionid") String unionId);

    @GetMapping({"departments/{deptId}/user_ids"})
    @ApiOperation("根据部门ID获取用户ID列表")
    BaseResult<List<Integer>> getUserIdsByDepartmentId(@PathVariable Integer deptId);

    @PostMapping("departments/batch/user_ids")
    @ApiOperation("根据部门ID批量获取用户ID列表")
    BaseResult<List<Integer>> batchUserIdsByDepartmentId(@RequestBody BatchReq<Integer> batchReq,
                                                         @RequestParam(required = false) @ApiParam("用户是否在职") Boolean isEnabled);

    @GetMapping("departments/user/department_list")
    @ApiOperation("根据用户ID获取用户部门和真实姓名")
    BaseResult<List<UserDepartmentResp>> getUserDepartmentListByUserIds(@RequestBody List<Integer> userIds);

    @GetMapping("departments/batch/user_ids")
    @ApiOperation("批量查询用户的所属部门")
    BaseResult<Map<Integer, String>> getDeptListByUsers(
            @RequestParam @ApiParam("用户id列表") Collection<Integer> userIds,
            @RequestParam @ApiParam("部门深度") Integer depth);


    default BaseResult<Map<Integer, String>> getDeptListByUsers(Collection<Integer> userIds) {
        return getDeptListByUsers(userIds, 2);
    }

    @GetMapping("departments/history")
    @ApiOperation("获取部门成员历史信息")
    BaseResult<List<DepartmentStaffHistoryDto>> getDepartmentStaffHistory(
            @RequestParam @ApiParam("部门id") List<Integer> deptIds, @RequestParam Instant flagDate);

    @GetMapping("wx/info/user_id")
    @ApiOperation("根据（companyType，userId）查询微信信息")
    BaseResult<UcWxExt> findByUserId(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId);

    /**
     * 根据用户id查询微信用户扩展信息
     *
     * @param userId 用户id
     * @return 微信用户扩展信息
     */
    @GetMapping("wx/{userId}")
    BaseResult<UcWxExt> getWxExtInfo(@PathVariable @ApiParam("用户Id") Integer userId);

    /**
     * 查询微信账号信息
     *
     * @param wxId        微信id
     * @param accountType 微信accountType
     * @return 微信账号信息
     */
    @ApiOperation("查询微信账号信息")
    @GetMapping("wx/account/account_type")
    BaseResult<List<UcWxAccount>> findAccountByWxId(
            @RequestParam @ApiParam("微信accountType") Integer accountType,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId);

    /**
     * 查询微信账号信息
     *
     * @param openId      openid
     * @param accountType 微信accountType
     * @return 微信账号信息
     */
    @ApiOperation("查询微信账号信息")
    @GetMapping("wx/account/open_id")
    BaseResult<List<UcWxAccount>> findAccountByOpenId(
            @RequestParam @ApiParam("微信accountType") Integer accountType,
            @RequestParam @ApiParam("openid") String openId);

    @GetMapping("wx/account/by_open_id")
    @ApiOperation("查询微信账号信息")
    BaseResult<UcWxAccount> findAccountInfoByOpenId(
            @RequestParam @ApiParam("微信accountType") Integer accountType,
            @RequestParam @ApiParam("openid") String openId);

    @GetMapping("wx/account/by-unionId")
    @ApiOperation("查询微信账号信息")
    BaseResult<UcWxAccount> findAccountInfoByUnionId(
            @RequestParam @ApiParam("unionId") String unionId);

    @PostMapping("wx/unbind")
    @ApiOperation("用户解绑微信")
    BaseResult<UsErrorCode> unbindWx(
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("微信id") Integer wxId,
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("产品类型") Integer productType);


    @GetMapping("rank")
    @ApiOperation("获取所有排行方案")
    PageResult<List<DepartmentRankPlanDto>> getRankList(
            @RequestParam Integer companyType, @RequestParam(required = false) Boolean isEnabled,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("rank/visible")
    @ApiOperation("获取可见范围内的排行方案")
    BaseResult<List<DepartmentRankPlanDto>> getDepartmentRankPlan(
            @RequestParam Integer companyType, @RequestParam(required = false) Boolean isEnabled,
            @RequestParam(required = false) Integer userId);

    @GetMapping("rank/{planId}")
    @ApiOperation("获取单条排行方案详情")
    BaseResult<DepartmentRankPlanDto> getRankInfo(@PathVariable Integer planId);

    @PostMapping("rank")
    @ApiOperation("新增排行方案")
    BaseResult<String> addDepartmentRank(@RequestBody UcDepartmentRankPlan planInfo);

    @PutMapping("rank/{planId}")
    @ApiOperation("修改排行方案")
    BaseResult<String> updateDepartmentRank(@PathVariable Integer planId, @RequestBody UcDepartmentRankPlan planInfo);

    @PutMapping("rank/{planId}/status")
    @ApiOperation("启用、禁用排行方案")
    BaseResult<String> updateDepartmentRankStatus(@PathVariable Integer planId, @RequestParam Boolean isEnabled);

    @PutMapping("rank/{planId}/visible")
    @ApiOperation("设置排行方案可见范围")
    BaseResult<String> setRankVisibleRange(@PathVariable Integer planId, @RequestBody List<Integer> userIdList);

    @GetMapping("users/server/base_info")
    @ApiOperation("获取后台用户基础信息")
    BaseResult<List<UserDTO>> getServerBaseInfo(@RequestParam @ApiParam("用户id列表") List<Integer> userIds,
                                                @RequestParam(required = false) @ApiParam("部门深度") Integer depth);

    @ApiOperation("分页获取用户信息")
    @GetMapping("users/by/deptId")
    PageResult<List<UcUsers>> getUsersByDeptId(
            @RequestParam @ApiParam("用户id列表") List<Integer> userIds,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    );

    @GetMapping("departments/getByFlagDate")
    @ApiOperation("根据flagDate获取部门成员历史信息")
    BaseResult<List<UcDepartmentStaffHistory>> getDepartmentByFlagDate(@RequestParam Instant flagDate);

    @GetMapping("users/all")
    @ApiOperation("获取所有后台员工")
    BaseResult<List<UcUsers>> getAllUcUsers();

    @GetMapping("users/ucwxext")
    @ApiOperation("根据wxId获取ucwxext")
    BaseResult<List<UcWxExt>> getUnionIds(@RequestParam @ApiParam("wxId") Set<Integer> wxId);

    @GetMapping("users/ucwxext/userId")
    @ApiOperation("根据userId获取ucwxext")
    BaseResult<List<UcWxExt>> getUnionIdsByUserId(@RequestParam @ApiParam("userIdList") List<Integer> userIdList);

    @GetMapping("users/{userId}/follow/employee")
    @ApiOperation("根据员工ID查询手下员工")
    BaseResult<List<Integer>> getFollowEmployeeIds(@PathVariable Integer userId);

    @GetMapping("users/userId/{userId}")
    @ApiOperation("根据用户ID获取下属的用户ID列表（包括自己）")
    BaseResult<List<Integer>> getUserIdByUserId(@PathVariable Integer userId);

    @GetMapping("users/account_activity/{userId}")
    @ApiOperation("根据用户ID分页查询登录记录")
    PageResult<List<UcAccountActivity>> getAccountActivityByUserId(
            @PathVariable @ApiParam("用户ID") Integer userId,
            @RequestParam(required = false) @ApiParam("产品列表") List<Integer> productTypeList,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("users/account_activity/batch_get")
    @ApiOperation("批量获取登录用户")
    BaseResult<List<Integer>> batchGetAccountActivityUser(
            @RequestParam(required = false) @ApiParam("开始userId") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("设备类型") Integer platform,
            @RequestParam(required = false) @ApiParam("开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam @ApiParam("数量") Integer size);

    @PostMapping("users/account_activity/list")
    @ApiOperation("查询登录记录")
    PageResult<List<UcAccountActivity>> getAccountActivityList(@RequestBody @Valid GetAccountActivityReq req);

    @GetMapping("users/account_activity/last/{userId}")
    @ApiOperation("根据用户ID查询最后一次登录记录")
    BaseResult<UcAccountActivity> getAccountActivityLast(@PathVariable @ApiParam("用户ID") Integer userId,
                                                         @RequestParam(required = false) @ApiParam("设备类型") Integer platform,
                                                         @RequestParam(required = false) @ApiParam("产品类型") Integer productType);

    @GetMapping("users/account-activity/lastLogin")
    @ApiOperation("根据用户ID查询最后一次登录记录")
    BaseResult<UcAccountActivity> getAccountLastLogin(@RequestParam @ApiParam("用户ID") Integer userId,
                                                      @RequestParam(required = false) @ApiParam("产品类型") List<Integer> productTypeList);

    @PostMapping("users/account_activity/last/batch")
    @ApiOperation("批量查询用户最后一次登录记录")
    BaseResult<Map<Integer, UcAccountActivity>> batchAccountActivityMap(@RequestParam(required = false) @ApiParam("设备类型") Integer platform,
                                                                        @RequestBody @Valid BatchReq<Integer> req);

    @PostMapping("users/account_activity")
    @ApiOperation("创建登录记录")
    BaseResult<UcAccountActivity> createAccountActivity(@RequestBody @Valid CreateAccountActivityReq req);

    /**
     * 获取用户绑定记录
     *
     * @param changeType 换绑类型
     * @param userId     用户id
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param current    页码
     * @param size       分页数量
     * @return 用户绑定列表
     */
    @GetMapping("users/changeRecord")
    @ApiOperation("获取用户绑定记录")
    PageResult<List<UserChangeRecordResp>> getUserChangeRecordList(
            @RequestParam(required = false) @ApiParam("换绑类型") Integer changeType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    /**
     * 校验验证码
     *
     * @param req 请求参数
     * @return 是否通过
     */
    @PostMapping("code/verify")
    @ApiOperation("校验验证码")
    BaseResult<String> verify(@RequestBody @Valid ImageCodeReq req);

    @PostMapping("mobile-verify")
    @ApiOperation("验证手机号")
    BaseResult<String> verify(
            @RequestParam @ApiParam("手机号") String mobile,
            @RequestParam @ApiParam("手机号ID") Long mobileId);

    @GetMapping("code/check_code")
    @ApiOperation("登录校验验证码")
    BaseResult<Boolean> checkCode(
            @RequestParam @ApiParam("用户手机号") String mobile,
            @RequestParam @ApiParam("验证码") String validateNo);

    @GetMapping("code/check_code/userId")
    @ApiOperation("登录校验验证码")
    @Operation(summary = "登录校验验证码")
    BaseResult<Boolean> checkCodeByUserId(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("验证码") String validateNo);

    @GetMapping("wx/user/open_id")
    @ApiOperation("查询用户相关信息")
    BaseResult<UserWxAccount> findByOpenId(
            @RequestParam @ApiParam("微信accountType") Integer accountType,
            @RequestParam @ApiParam("openid") String openId);

    @PostMapping("users/bindWx")
    @ApiOperation("绑定微信")
    BaseResult<Integer> bindWx(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("产品类型") Integer productType,
            @RequestParam @ApiParam("手机号") String mobile,
            @RequestParam(required = false) @ApiParam("用户昵称") String nickName,
            @RequestParam(required = false) @ApiParam("微信头像") String headImgUrl,
            @RequestParam(required = false) @ApiParam("来源") String source);

    @PostMapping("id_card/verifyIdCard")
    @ApiOperation("三要素认证")
    BaseResult<Boolean> verifyIdCard(
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("身份证号") String identityNumber,
            @RequestParam @ApiParam("姓名") String name,
            @RequestParam @ApiParam("userid") Integer userId);

    @PostMapping("id_card/verifyOther")
    @ApiOperation("非三要素认证")
    BaseResult<Boolean> verifyOther(
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("身份证类型") Integer idCardType,
            @RequestParam @ApiParam("身份证号") String identityNumber,
            @RequestParam @ApiParam("姓名") String name,
            @RequestParam @ApiParam("userid") Integer userId);

    @PostMapping("code/createMobileVerifySession")
    @ApiOperation("创建验证码记录")
    BaseResult<Boolean> createMobileVerifySession(
            @RequestBody UcMobileVerifyCode mobileVerifyCode);

    @GetMapping("id_card/getIdCardResult")
    @ApiOperation("获取认证结果")
    BaseResult<Boolean> getIdCardResult(
            @RequestParam @ApiParam("身份证号") String idNumber);

//    @GetMapping("users/get_first")
//    @ApiOperation("根据用户手机号查询第一个用户")
//    BaseResult<UcUsers> getFirstByMobile(
//            @RequestParam @ApiParam("用户手机号") String mobile,
//            @RequestParam @ApiParam("公司类型") Integer companyType);

    @GetMapping("id_card/record")
    @ApiOperation("获取认证记录")
    BaseResult<List<UcIdCardRecord>> getRecord(
            @RequestParam @ApiParam("手机号") String mobile);

    @GetMapping("users/getCustomerList")
    @ApiOperation("获取客户管理列表")
    PageResult<List<CustomerInfoResp>> getCustomerList(
            @RequestParam(required = false) @ApiParam("用户id,微信id,手机号") String parameter,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("wx_track/{wxId}")
    @ApiOperation("用户行为追踪")
    BaseResult<String> uploadWxTrackEvent(
            @PathVariable @ApiParam("客户wxId") Integer wxId,
            @RequestParam @ApiParam("Referer") String referer,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("事件（直播室/微信服务号等）") String category,
            @RequestParam @ApiParam("行为（浏览/收藏等）") String action,
            @RequestParam(required = false) @ApiParam("具体描述") String label,
            @RequestParam(required = false) @ApiParam("销售number") String number);

    @PostMapping("wx/getInfo")
    @ApiOperation("查询微信账号信息")
    List<WxAccountResp> filterByUserIds(@RequestBody @Valid FilterWxAccountReq req);

    @GetMapping("wx/accountList")
    @ApiOperation("查询微信账号信息")
    List<UcWxAccount> findAccountByWxIdList(
            @RequestParam @ApiParam("微信accountType") Integer accountType,
            @RequestParam(required = false) @ApiParam("微信id") Set<Integer> wxIds);

    @GetMapping("wx/accountList/wxIds")
    @ApiOperation("查询微信账号信息")
    List<UcWxAccount> findAccountByWxIdList(
            @RequestParam(required = false) @ApiParam("微信id") Set<Integer> wxIds);

    @GetMapping("wx/findById")
    @ApiOperation("根据id查询UcWxExt")
    BaseResult<UcWxExt> findById(@RequestParam Integer wxId);

    /**
     * 获取有归属的用户列表
     *
     * @return 用户列表
     */
    @GetMapping("customer/follow/up/customerList")
    @ApiOperation("获取有归属的用户列表")
    List<Integer> getCustomerList();

    /**
     * 客户服务-搜索
     *
     * @param userId   用户id
     * @param belongId 归属人id
     * @return 客户服务信息
     */
    @GetMapping("customer/follow/up/search")
    @ApiOperation("客户服务-搜索")
    BaseResult<List<ServiceCustomerDto>> serviceCustomer(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("belongId") Integer belongId);

    /**
     * 我的客户服务-搜索
     *
     * @param userId   用户id
     * @param belongId 归属人id
     * @return 客户服务信息
     */
    @GetMapping("customer/follow/up/my/search")
    @ApiOperation("我的客户服务-搜索")
    BaseResult<ServiceCustomerDto> myServiceCustomer(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("belongId") Integer belongId);

    /**
     * 通过id,手机号搜索用户
     *
     * @param content     搜索内容
     * @param companyType 公司类型
     * @return 用户
     */
    @GetMapping("users/search")
    @ApiOperation("通过id搜索用户")
    BaseResult<UcUsers> searchUser(
            @RequestParam @ApiParam("用户id") String content,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户类型") Integer userType);

    default BaseResult<UcUsers> searchUser(String content, Integer companyType) {
        return searchUser(content, companyType, null);
    }

    /**
     * 客户服务归属管理 - 已认领
     *
     * @param req 筛选
     * @return 已认领列表
     */
    @PostMapping("customer/follow/up/claimed")
    @ApiOperation("客户服务-已认领")
    PageResult<List<ServiceCustomerDto>> claimedPage(@RequestBody @Valid ClaimedCustomerReq req);

    @GetMapping("customer/follow/up/belong/customer")
    @ApiOperation("获取客户服务归属关系列表")
    BaseResult<List<UcCustomerFollowUpRelation>> getRelationByUser(@RequestParam Integer userId);

    @GetMapping("customer/follow/up/belong/role")
    @ApiOperation("根据角色获取客户服务归属关系")
    BaseResult<List<UcCustomerFollowUpRelation>> getCustomerFollowUpBelongRelationByRole(@RequestParam Integer userId,
                                                                                         @RequestParam Long roleId);

    @GetMapping("customer/follow/up/belong/{belongId}")
    @ApiOperation("获取员工服务归属关系列表")
    BaseResult<List<UcCustomerFollowUpRelation>> getRelationByBelongId(@PathVariable Integer belongId);

    @PostMapping("customer/follow/up/belong")
    @ApiOperation("创建客户服务归属")
    BaseResult<UcCustomerFollowUpRelation> createCustomerFollowUpBelong(@RequestBody @Valid CreateCustomerFollowUpBelongReq req);

    @PostMapping("customer/follow/up/belong/batch")
    @ApiOperation("批量创建客户服务归属")
    BaseResult<String> batchCreateCustomerFollowUpBelong(@RequestBody @Valid BatchCreateCustomerFollowUpBelongReq req);

    @GetMapping("customer/tags/groups")
    @ApiOperation("获取后台标签组")
    BaseResult<List<UcCustomerTagGroup>> getCustomerTagGroupList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) Boolean isGlobal,
            @RequestParam(required = false) Boolean isUnion,
            @RequestParam(required = false) Boolean isRadio,
            @RequestParam(required = false) Boolean enabled);

    @GetMapping("customer/tags")
    @ApiOperation("获取用户后台标签")
    BaseResult<List<UcCustomerTag>> getCustomerTagList(@RequestParam Integer userId);

    /**
     * 获取后台标签组以及组内标签
     *
     * @param companyType  公司类型
     * @param isGlobal     是否全局(销售可编辑)
     * @param isRadio      是否单选(单选时为互斥)
     * @param groupEnabled 标签组是否启用
     * @param tagEnabled   标签是否启用
     * @return 后台标签组以及组内标签
     */
    @GetMapping("customer/tags/groupTags")
    @ApiOperation("获取后台标签组以及组内标签")
    BaseResult<List<CustomerTagResp>> getGroupTagList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) @ApiParam("角色id") Long roleId,
            @RequestParam(required = false) @ApiParam("是否全局(销售可编辑)") Boolean isGlobal,
            @RequestParam(required = false) @ApiParam("是否单选(单选时为互斥)") Boolean isRadio,
            @RequestParam(required = false) @ApiParam("标签组是否启用") Boolean groupEnabled,
            @RequestParam(required = false) @ApiParam("标签是否启用") Boolean tagEnabled);

    /**
     * 修改用户标签
     *
     * @param companyType 公司类型
     * @param userId      用户id
     * @param tagList     标签列表
     * @return 操作结果
     */
    @PostMapping("customer/tags/update/customer")
    @ApiOperation("修改用户标签")
    BaseResult<Boolean> updateCustomerTag(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("操作人") Integer operator,
            @RequestParam(required = false) @ApiParam("标签列表") List<Integer> tagList);

    /**
     * 批量用户打标签
     */
    @PostMapping("customer/tags/create/customer/batch")
    @ApiOperation("批量用户打标签")
    BaseResult<Boolean> createBatchCustomerTag(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id列表") List<Integer> userIdList,
            @RequestParam @ApiParam("操作人") Integer operator,
            @RequestParam @ApiParam("标签列表") List<Integer> tagList);

    /**
     * 根据操作人获取后台标签记录
     */
    @GetMapping("customer/tags/getTagRecordByOperator")
    @ApiOperation("获取员工打标签记录")
    PageResult<List<CustomerTagRecordResp>> getTagRecordByOperator(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    /**
     * 服务跟进
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @PostMapping("customer/follow/up")
    @ApiOperation("创建客户服务跟进")
    BaseResult<Boolean> createCustomerFollowUp(@RequestBody @Valid CreateCustomerFollowUpReq req);

    /**
     * 获取跟进记录列表
     *
     * @param req 请求参数
     * @return 跟进记录页面
     */
    @PostMapping("customer/follow/up/getPage")
    @ApiOperation("获取跟进记录列表")
    PageResult<List<UcCustomerFollowUpRecord>> getCustomerFollowUpRecordPage(
            @RequestBody @Validated CustomerFollowUpRecordReq req);

    /**
     * 获取认领记录列表
     *
     * @param req 请求参数
     * @return 认领记录
     */
    @PostMapping("customer/follow/up/getBelongPage")
    @ApiOperation("获取认领记录列表")
    PageResult<List<UcCustomerFollowUpBelongRecord>> getCustomerFollowUpBelongRecordPage(
            @RequestBody @Validated CustomerFollowUpRecordReq req);

    @GetMapping("customer/follow/up/getBelongRecordByOperator")
    @ApiOperation("根据操作人获取认领记录列表")
    PageResult<List<UcCustomerFollowUpBelongRecord>> getBelongRecordByOperator(
            @RequestHeader @ApiIgnore Integer operator,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @PostMapping("customer/tags/group")
    @ApiOperation("获取标签组下的标签")
    BaseResult<List<UcCustomerTag>> getCustomerTagList(
            @RequestParam Integer groupId,
            @RequestParam(required = false) Boolean groupEnabled,
            @RequestParam(required = false) Boolean tagEnabled);

    @PostMapping("customer/tags/union")
    @ApiOperation("创建标签并且移除标签")
    BaseResult<Void> addAndRemoveCustomerTag(@RequestBody @Valid AddAndRemoveCustomerTagReq req);

    /**
     * 通过id获取标签组
     *
     * @param id id
     * @return 标签组信息
     */
    @GetMapping("customer/tags/{id}")
    @ApiOperation("通过ID获取标签组")
    BaseResult<UcCustomerTagGroup> getCustomerTagGroup(@PathVariable Integer id);

    /**
     * 创建标签组
     *
     * @param req 信息
     * @return 标签组信息
     */
    @PostMapping("customer/tags")
    @ApiOperation("创建标签组")
    BaseResult<UcCustomerTagGroup> createCustomerTagGroup(@RequestBody CreateTagGroupReq req);

    /**
     * 更新标签组状态
     *
     * @param groupId 标签组id
     * @param enabled 是否启用
     * @return 无返回
     */
    @PutMapping("customer/tags/update")
    @ApiOperation("更新标签组状态")
    BaseResult<Void> updateCustomerTagGroup(
            @RequestParam @ApiParam("标签组id") Integer groupId,
            @RequestParam @ApiParam("是否启用") Boolean enabled);

    /**
     * 创建标签
     *
     * @param req 参数
     * @return 标签信息
     */
    @PostMapping("customer/tags/tag")
    @ApiOperation("创建标签")
    BaseResult<Void> createCustomerTag(@RequestBody CreateTagReq req);

    /**
     * 获取后台标签组以及组内标签-分页
     *
     * @param companyType  公司类型
     * @param groupEnabled 标签组是否启用
     * @param tagEnabled   标签是否启用
     * @param asc          是否升序
     * @param current      页码
     * @param size         分页数量
     * @return 标签页面
     */
    @GetMapping("customer/tags/groupTagPage")
    @ApiOperation("获取后台标签组以及组内标签-分页")
    PageResult<List<CustomerTagResp>> getGroupTagPage(
            @RequestParam Integer companyType,
            @RequestParam(required = false) @ApiParam("标签组是否启用") Boolean groupEnabled,
            @RequestParam(required = false) @ApiParam("标签是否启用") Boolean tagEnabled,
            @RequestParam(defaultValue = "true") @ApiParam("排序") Boolean asc,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("wwx_robot/device_status")
    @ApiOperation("查询用户绑定设备状态")
    BaseResult<UsErrorCode> getDeviceStatus(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("登录设备地址") String deviceId);

    @PostMapping("wwx_robot/create_one")
    @ApiOperation("创建设备绑定信息")
    BaseResult<String> createDeviceInfo(@RequestBody @Valid CreateDeviceInfoReq req);

    @GetMapping("users/findOrCreateByMobile")
    @ApiOperation("根据手机号查找或创建一个新用户")
    BaseResult<UcUsers> findOrCreateByMobile(
            @RequestParam @ApiParam("用户手机号") String mobile,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("产品类型") Integer productType,
            @RequestParam(required = false) @ApiParam("昵称") String nickName,
            @RequestParam(required = false) @ApiParam("头像") String avatarUrl,
            @RequestParam(required = false) @ApiParam("注册来源") String source);

    @GetMapping("users/getRandomNickName")
    @ApiOperation("随机生成用户昵称")
    BaseResult<String> getRandomNickName();

    @GetMapping("users/getNickNameConf")
    @ApiOperation("获取用户昵称配置")
    BaseResult<UserNickNameConfResp> getNickNameConf();

    @GetMapping("users/getUserAvatarConf")
    @ApiOperation("获取初始用户头像配置")
    BaseResult<UserAvatarConfResp> getUserAvatarConf();

    @PostMapping("users/updateUserNickNameOrAvatar")
    @ApiOperation("更新App用户昵称与头像信息")
    BaseResult<String> updateUserNickNameOrAvatar(@RequestBody @Valid UpdateAppUserInfoReq req);

    @PostMapping("wx/app/unbindWx")
    @ApiOperation("app用户解绑微信")
    BaseResult<String> appUnbindWx(
            @RequestParam @ApiParam("微信Id") Integer wxId);

    /**
     * 用户更换手机号
     *
     * @param userId      用户id
     * @param companyType 公司类型
     * @param mobile      手机号
     * @param validateNo  验证码
     * @return 操作结果
     */
    @ApiOperation("更换手机号")
    @PostMapping("users/changeMobile")
    BaseResult<Boolean> changeMobile(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("手机号") String mobile,
            @RequestParam @ApiParam("验证码") String validateNo,
            @RequestParam(required = false) @ApiParam("产品类型") Integer productType);


    /**
     * 首次登录重置密码
     *
     * @param req 参数
     * @return 操作结果
     */
    @PostMapping("users/modify/password")
    @ApiOperation("修改密码")
    BaseResult<Boolean> modifyPassword(
            @RequestBody @Valid ModifyPasswordReq req);


    /**
     * 更新密码
     *
     * @param userId      用户id
     * @param oldPassword 老密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    @PostMapping("users/update/password")
    @ApiOperation("更新密码")
    BaseResult<Boolean> updatePassword(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("老密码") String oldPassword,
            @RequestParam @ApiParam("新密码") String newPassword);

    @PostMapping("users/switch/password")
    @ApiOperation("更换密码")
    BaseResult<Boolean> switchPassword(
            @RequestBody @Valid SwitchPasswordReq req);

    @ApiOperation("拨打电话")
    @PostMapping("users/makingCall")
    BaseResult<Void> makingCall(@RequestBody @Valid MakingCallReq req);

//    @GetMapping("call/log/list")
//    @ApiOperation("获取通话列表")
//    PageResult<List<CallLogResp>> findCallLogByFilter(
//            @RequestParam @ApiParam("公司类型") Integer companyType,
//            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
//            @RequestParam(required = false) @ApiParam("通话类型") Boolean callType,
//            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
//            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
//            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
//            @RequestParam(required = false) @ApiParam("搜索手机号") String mobile,
//            @RequestParam(required = false) @ApiParam("客户number") String number,
//            @RequestParam(required = false) @ApiParam("callId") String callId,
//            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
//            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
//            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("call/log/statistics")
    @ApiOperation("获取销售通话次数")
    BaseResult<List<SalesCallStatisticsResp>> getSalesCallStatistics(@RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime, @RequestParam @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime);

    @GetMapping("users/findGwServerInfo")
    @ApiOperation("查询官网客服")
    BaseResult<GwServerInfoResp> findGwServerInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer serverId);

    @GetMapping("customer/tags/customerTagRecord")
    @ApiOperation("获取标签记录")
    PageResult<List<CustomerTagRecordResp>> getCustomerTagRecord(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("role/batch")
    @ApiOperation("根据用户ID批量查询用户角色map")
    BaseResult<Map<Integer, UcRole>> batchGetUserRoleMap(@RequestBody BatchReq<Integer> req);

    @GetMapping("role/userList/{roleId}")
    @ApiOperation("获取所属角色的员工")
    BaseResult<List<Integer>> getUserListByRole(@PathVariable Long roleId);

    @GetMapping("staff/role/list")
    @ApiOperation("员工角色列表")
    PageResult<List<UcRole>> getStaffRoleList(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("staff/role")
    @ApiOperation("新建/编辑角色")
    BaseResult<String> createOrUpdateRole(
            @RequestBody @Valid CreateOrUpdateRoleReq req);

    @PostMapping("customer/tags/set/operateRange")
    @ApiOperation("设置标签组操作范围")
    BaseResult<Void> setOperateRange(
            @RequestParam @ApiParam("标签组id") Integer groupId,
            @RequestParam(required = false) @ApiParam("角色列表") List<Long> roleList);

    @GetMapping("customer/tags/get/operateRange")
    @ApiOperation("获取标签组操作范围")
    BaseResult<List<UcCustomerTagGroupRoleRelation>> getOperateRange(
            @RequestParam @ApiParam("标签组id") Integer groupId);

    @PostMapping("users/batchSearchUserByUnionId")
    @ApiOperation("按unionId批量查询用户")
    BaseResult<List<UcUsers>> batchSearchUserByUnionId(
            @RequestBody @Valid @ApiParam("union列表") BatchReq<String> req);

    @PostMapping("users/batchByNumber")
    @ApiOperation("按number批量查询用户")
    BaseResult<Map<String, UcUsers>> batchUserByNumber(
            @RequestBody @Valid @ApiParam("number列表") BatchReq<String> req);

    @PostMapping("users/batchSearchUserByUnionIdAndContent")
    @ApiOperation("按unionId批量查询用户")
    BaseResult<List<UcUsers>> batchSearchUserByUnionIdAndContent(
            @RequestBody @Valid BatchSearchUserByUnionIdAndContentReq req);

    @PostMapping("users/batchSearchUnionIdMapByUserId")
    @ApiOperation("根据userId查询unionIdMap")
    BaseResult<Map<Integer, String>> batchSearchUnionIdMapByUserId(
            @RequestBody @Valid @ApiParam("userId列表") BatchReq<Integer> req);

    @GetMapping("role/info/{userId}")
    @ApiOperation("获取员工角色信息")
    BaseResult<UcRole> getRoleInfo(@PathVariable Integer userId);

    @PostMapping("customer/follow/up/claimedServiceCustomer")
    @ApiOperation("服务客户列表-已接入")
    PageResult<List<ServiceCustomerDto>> claimedServiceCustomerPage(@RequestBody @Valid ServiceCustomerReq req);

    @GetMapping("customer/follow/up/getCustomerTagList")
    @ApiOperation("获取用户标签列表")
    BaseResult<List<CustomerTagDto>> getCustomerTagList(@RequestParam Set<Integer> userSet);

    @PostMapping("users/createOrUpdateSubUser")
    @ApiOperation("创建/更新子账号")
    BaseResult<Void> createOrUpdateSubUser(
            @RequestBody @Valid CreateOrUpdateSubUserReq req);

    @PostMapping("users/createOrUpdateStaffExt")
    @ApiOperation("创建/更新员工信息")
    BaseResult<Void> createOrUpdateStaffExt(
            @RequestBody @Valid CreateOrUpdateStaffExtReq req);

    @GetMapping("users/getStaffExt")
    @ApiOperation("获取指定员工信息")
    BaseResult<UcStaffExt> getStaffExt(@RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("users/findSubServerByFilter")
    @ApiOperation("获取子账号列表")
    PageResult<List<SubServerResp>> findSubServerByFilter(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("账号状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("部门id") Integer departmentId,
            @RequestParam(required = false) @ApiParam("角色id") Long roleId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("users/findSalesDeptChatVisibleByFilter")
    @ApiOperation("获取聊天记录子账号可见部门列表")
    PageResult<List<SalesDeptChatVisibleResp>> findSalesDeptChatVisibleByFilter(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("users/deptChat/visible")
    @ApiOperation("修改聊天记录子账号可见范围")
    BaseResult<Void> updateDeptChatVisibleConfig(@RequestBody @Valid UpdateDeptChatVisibleConfigReq req);

    @GetMapping("users/findDeptInfoChatVisibleBySales")
    @ApiOperation("获取销售聊天记录可见部门")
    BaseResult<List<DepartmentInfoResp>> findDeptInfoChatVisibleBySales(
            @RequestParam @ApiParam("销售id") Integer salesId);

    @GetMapping("users/findDeptChatVisibleSales")
    @ApiOperation("获取聊天记录可见部门销售")
    BaseResult<List<Integer>> findDeptChatVisibleSales(
            @RequestParam @ApiParam("销售id") Integer salesId);

    @GetMapping("users/findMainServerByFilter")
    @ApiOperation("获取主账号列表")
    PageResult<List<MainServerResp>> findMainServerByFilter(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("账号状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("users/syncStaffInfo")
    @ApiOperation("同步主账号信息")
    BaseResult<Void> syncStaffInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PostMapping("users/createMainStaff")
    @ApiOperation("创建主账号信息")
    BaseResult<String> createMainStaff(@RequestBody @Valid CreateMainStaffReq req);

    @PostMapping("users/quit")
    @ApiOperation("离职/取消离职")
    BaseResult<Void> quit(
            @RequestParam @ApiParam("主账号ID") Integer id,
            @RequestParam @ApiParam("离职/取消离职") Boolean enabled);

    @GetMapping("users/staff/info")
    @ApiOperation("获取员工详细信息")
    BaseResult<StaffInfoResp> getStaffInfo(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @GetMapping("customer/interest/tag/{userId}")
    @ApiOperation("通过userId获取用户兴趣标签")
    BaseResult<List<CustomerInterestTagResp>> findCustomerInterestTag(@PathVariable Integer userId);

    @PostMapping("checkIn")
    @ApiOperation("用户签到")
    BaseResult<UcCheckInRecord> checkIn(@RequestBody @Valid CheckInReq checkInReq);

    @GetMapping("checkIn/status")
    @ApiOperation("获取今日签到状态")
    BaseResult<UcCheckInRecord> getTodayCheckInRecord(@RequestParam Integer userId);

    @GetMapping("checkIn/record")
    @ApiOperation("获取用户签到记录")
    BaseResult<List<UcCheckInRecord>> getUserCheckInRecordList(
            @RequestParam Integer userId,
            @RequestParam Instant startCheckInDate,
            @RequestParam Instant endCheckInDate
    );

    @GetMapping("reward/rules")
    @ApiOperation("获取签到积分规则")
    BaseResult<RewardSignInRulesResp> getRewardSingInRules();

    @GetMapping("reward/summary/{userId}")
    @ApiOperation("获取积分")
    BaseResult<UcRewardSummary> getRewardSummary(@PathVariable Integer userId);

    @GetMapping("reward/batch/summary")
    @ApiOperation("根据userId批量获取积分")
    BaseResult<List<UcRewardSummary>> getRewardSummaryList(@RequestBody BatchReq<Integer> req);

    @GetMapping("reward/record/{userId}")
    @ApiOperation("获取积分明细")
    PageResult<List<UcRewardRecord>> findRewardRecordByFilter(
            @PathVariable Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("reward/month/summary")
    @ApiOperation("获取用户月度积分汇总列表")
    BaseResult<List<UcRewardMonthSummary>> getRewardMonthSummaryList(
            @RequestParam Integer userId,
            @RequestParam Instant startDate,
            @RequestParam Instant endDate
    );

    @GetMapping("reward/month/record")
    @ApiOperation("获取用户月度积分记录")
    BaseResult<List<UcRewardRecord>> getRewardMonthRecordList(
            @RequestParam Integer userId,
            @RequestParam Instant month
    );

    @GetMapping("track/page/list")
    @ApiOperation("页面管理列表")
    PageResult<List<UcTrackPage>> getPageList(
            @RequestParam @ApiParam("应用id") Long appId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @PostMapping("track/page/create")
    @ApiOperation("添加页面映射")
    BaseResult<String> createTrackPage(
            @RequestBody @Valid EditTrackPageReq req);

    @PostMapping("track/page/update")
    @ApiOperation("修改页面映射")
    BaseResult<String> updateTrackPage(
            @RequestBody @Valid EditTrackPageReq req);

    @PostMapping("track/page/{id}/delete")
    @ApiOperation("删除页面映射")
    BaseResult<String> deleteTrackPage(@PathVariable Long id);

    @GetMapping("track/page/{appId}/list")
    @ApiOperation("根据应用id获取页面列表")
    BaseResult<List<UcTrackPage>> getPageListByAppId(@PathVariable Long appId);

    @PostMapping("customer/track/upload/enven")
    @ApiOperation("用户行为上报")
    BaseResult<Void> uploadCustomerTrackEvent(
            @RequestBody CreateTrackEventReq req);

    @PostMapping("customer/track/upload/heartbeat")
    @ApiOperation("用户心跳上报")
    BaseResult<Void> uploadCustomerHeartbeat(
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("应用id") String appId,
            @RequestParam(required = false) @ApiParam("页面id") String pageId);

    @GetMapping("customer/track/detail/list")
    @ApiOperation("用户访问明细")
    PageResult<List<LdCustomerTrackEvent>> getCustomerTrackEvent(
            @RequestParam(required = false) @ApiParam("wxId") Integer wxId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("userId") Integer userId,
            @RequestParam(required = false) @ApiParam("应用id") String appId,
            @RequestParam(required = false) @ApiParam("页面id") String pageId,
            @RequestParam(required = false) @ApiParam("销售id") Integer salesId,
            @RequestParam(required = false) @ApiParam("开始时间") Long startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Long endTime,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @PostMapping("customer/track/summary/list")
    @ApiOperation("获取用户访问总览")
    PageResult<List<CustomerTrackEventSummaryDto>> getCustomerTrackEventSummary(
            @RequestBody @Validated CustomerTrackEventSummaryReq req);

    @GetMapping("customer/track/list")
    @ApiOperation("分页筛选用户行为记录")
    PageResult<List<LdCustomerTrackEvent>> filterCustomerTrackEventPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("应用id") String appId,
            @RequestParam(required = false) @ApiParam("页面id") String pageId,
            @RequestParam(required = false) @ApiParam("开始时间") Long startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Long endTime,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @PostMapping("track/app/edit")
    @ApiOperation("应用编辑")
    BaseResult<Void> editTrackApp(
            @RequestBody @Valid EditTrackAppReq req);

    @PostMapping("track/app/edit/open")
    @ApiOperation("应用编辑开放状态")
    BaseResult<Void> editTrackAppOpenStatus(
            @RequestParam @ApiParam("应用id") Long appId,
            @RequestParam @ApiParam("开放状态") Boolean isOpen);

    @GetMapping("track/app/list")
    @ApiOperation("应用列表")
    PageResult<List<UcTrackApp>> getAppList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @GetMapping("track/app/infoByAppId")
    @ApiOperation("根据应用Id获取应用详情")
    BaseResult<UcTrackApp> infoByAppId(@RequestParam @ApiParam("应用id") String appId);

    @GetMapping("track/app/filterPageByAppId")
    @ApiOperation("根据应用Id获取页面信息")
    BaseResult<List<UcTrackPage>> filterPageByAppId(@RequestParam @ApiParam("应用id") String appId);

    @GetMapping("track/app/getAppPageInfo")
    @ApiOperation("根据应用Id获取应用及页面信息")
    BaseResult<AppPageInfoResp> getAppPageInfo(@RequestParam @ApiParam("应用id") String appId);

    @GetMapping("users/getServerExtById")
    @ApiOperation("根据id查询员工扩展信息")
    BaseResult<UcServerExt> getServerExtById(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("customer/track/page/statistics")
    @ApiOperation("页面统计数量")
    BaseResult<PageStatistics> getPageStatistics(
            @RequestParam(required = false) @ApiParam("应用id") String appId,
            @RequestParam(required = false) @ApiParam("页面id") String pageId,
            @RequestParam(required = false) @ApiParam("开始时间") Long startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Long endTime);

    @GetMapping("customer/track/page/statistics/day")
    @ApiOperation("页面统计数量")
    BaseResult<PageStatistics> getPageStatistics(
            @RequestParam(required = false) @ApiParam("应用id") String appId,
            @RequestParam(required = false) @ApiParam("页面id") String pageId,
            @RequestParam(required = false) @ApiParam("时间") Long flagTime);

    @GetMapping("customer/track/page/trackEventStat")
    @ApiOperation("获取页面统计数据")
    BaseResult<List<LdCustomerTrackEventStat>> getCustomerTrackEventStat(
            @RequestParam(required = false) @ApiParam("应用id") String appId,
            @RequestParam(required = false) @ApiParam("页面id") String pageId,
            @RequestParam(required = false) @ApiParam("开始时间") Long startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Long endTime);

    @PostMapping("content_team/createOrUpdate")
    @ApiOperation("创建/更新内容主信息")
    BaseResult<Void> createOrUpdateContentTeam(
            @RequestBody @Valid CreateOrUpdateContentTeamReq req);

    @PostMapping("content_team/enable")
    @ApiOperation("更新内容主状态")
    BaseResult<Void> updateContentTeamStatus(
            @RequestParam @ApiParam("内容主id") Long teamId,
            @RequestParam @ApiParam("状态") Boolean enabled);

    @PostMapping("content_team/update")
    @ApiOperation("更新内容主装修信息")
    BaseResult<Void> updateContentTeam(
            @RequestBody @Valid UpdateContentTeamReq req);

    @PostMapping("content_team/manager")
    @ApiOperation("配置内容主管理员")
    BaseResult<Void> editContentTeamManager(
            @RequestParam @ApiParam("内容主id") Long teamId,
            @RequestParam(required = false) @ApiParam("管理人ids") List<Integer> managerIds);

    @PostMapping("content_team/module")
    @ApiOperation("配置内容主模块")
    BaseResult<Void> editContentTeamModule(
            @RequestParam @ApiParam("内容主id") Long teamId,
            @RequestParam(required = false) @ApiParam("模块ids") List<Integer> moduleIds);

    @GetMapping("content_team/list")
    @ApiOperation("获取内容主列表")
    PageResult<List<ContentTeamResp>> getContentTeamList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("内容主") List<Long> teamIds,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("管理员id") Integer managerId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("content_team/list/ids")
    @ApiOperation("获取内容主列表")
    BaseResult<List<ContentTeamResp>> getContentTeamListByIds(
            @RequestParam @ApiParam("内容主id") Set<Long> teamIds);

    @GetMapping("content_team/list/front")
    @ApiOperation("获取内容主列表(不分页)")
    BaseResult<List<UcContentTeam>> getContentTeamList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("内容主编号") List<String> teamIds,
            @RequestParam(required = false) @ApiParam("内容主id") Boolean enabled);

    @GetMapping("content_team/{number}")
    @ApiOperation("获取内容主详情")
    BaseResult<ContentTeamResp> getContentTeam(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @PathVariable String number);

    @PostMapping("content_team/batch")
    @ApiOperation("内容主批量查询")
    BaseResult<Map<Long, UcContentTeam>> batchContentTeamMap(@RequestBody BatchReq<Long> req);

    @GetMapping("content_team/batch/list")
    @ApiOperation("按编号批量查询内容主")
    BaseResult<List<UcContentTeam>> getContentTeamList(
            @RequestParam Integer companyType,
            @RequestBody BatchReq<String> batchReq
    );

    @GetMapping("content_team/{id}/info")
    @ApiOperation("获取内容主详情")
    BaseResult<UcContentTeam> getContentTeam(
            @PathVariable Long id);

    @GetMapping("users/byNumber")
    @ApiOperation("根据number查询用户")
    BaseResult<UcUsers> getUserByNumber(@RequestParam Integer companyType,
                                        @RequestParam Integer userType,
                                        @RequestParam String number,
                                        @RequestParam(required = false) Boolean enabled);

    @GetMapping("/search/customer/aggregate")
    @ApiOperation("搜索客户")
    BaseResult<OpenSearchResult<CustomerAggregateItem>> searchCustomerAggregate(
            @RequestParam String appName,
            @RequestParam Integer companyType,
            @RequestParam String keyword,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size,
            @RequestParam boolean isGlobalSearch);

    @PostMapping("content_team/visible/dept/set")
    @ApiOperation("设置部门可见范围")
    BaseResult<Boolean> setDeptVisible(@RequestBody ContentTeamVisibleReq req);

    @PostMapping("content_team/visible/dept/get")
    @ApiOperation("获取内容主部门可见范围")
    BaseResult<List<Integer>> getDeptVisible(@RequestParam Long teamId);

    @GetMapping("content_team/getVisibleByUser")
    @ApiOperation("获取某个人可见的内容主")
    BaseResult<List<Long>> getVisibleByUser(@RequestParam Integer userId);


    @PostMapping("staff/track/upload/enven")
    @ApiOperation("员工行为上报")
    BaseResult<Void> uploadStaffTrackEvent(@RequestBody CreateStaffTrackEventReq req);

    @GetMapping("staff/track/list")
    @ApiOperation("分页筛选员工行为记录")
    PageResult<List<LdStaffTrackEvent>> filterStaffTrackEventPage(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("应用id") String appId,
            @RequestParam(required = false) @ApiParam("页面id") String pageId,
            @RequestParam(required = false) @ApiParam("开始时间") Long startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Long endTime,
            @RequestParam(required = false) @ApiParam("行为类型") Integer trackType,
            @RequestParam(required = false) @ApiParam("员工id") Integer staffId,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @GetMapping("staff/track/page/list")
    @ApiOperation("页面管理列表")
    PageResult<List<UcStaffTrackPage>> getStaffTrackPageList(
            @RequestParam @ApiParam("应用id") Long appId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @PostMapping("staff/track/page/create")
    @ApiOperation("添加页面映射")
    BaseResult<String> createStaffTrackPage(@RequestBody @Valid EditStaffTrackPageReq req);

    @PostMapping("staff/track/page/update")
    @ApiOperation("修改页面映射")
    BaseResult<String> updateStaffTrackPage(@RequestBody @Valid EditStaffTrackPageReq req);

    @PostMapping("staff/track/page/{id}/delete")
    @ApiOperation("删除页面映射")
    BaseResult<String> deleteStaffTrackPage(@PathVariable Long id);

    @GetMapping("staff/track/page/{appId}/list")
    @ApiOperation("根据应用id获取页面列表")
    BaseResult<List<UcStaffTrackPage>> getStaffTrackPageListByAppId(@PathVariable Long appId);

    @GetMapping("staff/track/app/getAppPageInfo")
    @ApiOperation("根据应用Id或应用编号获取应用及页面信息")
    BaseResult<StaffAppPageInfoResp> getStaffAppPageInfo(
            @RequestParam(required = false) @ApiParam("应用id") Long appId,
            @RequestParam(required = false) @ApiParam("应用id") String appNumber);

    @GetMapping("staff/track/app/list")
    @ApiOperation("应用列表")
    PageResult<List<UcStaffTrackApp>> getStaffAppList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @PostMapping("staff/track/app/edit")
    @ApiOperation("应用编辑")
    BaseResult<Void> editStaffTrackApp(@RequestBody @Valid EditStaffTrackAppReq req);

    @GetMapping("users/byDdId")
    @ApiOperation("根据DdId查询用户")
    BaseResult<UcUsers> getUserByDd(@RequestParam Long ddId);

    @GetMapping("staff/byUserId")
    @ApiOperation(value = "根据子账号id查询主账号")
    BaseResult<UcMainStaff> getStaffByUserId(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户名") Integer userId);

    @PostMapping("users/userBindDd")
    @ApiOperation("用户绑定钉钉账号")
    BaseResult<Void> userBindDd(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("钉钉id") Long ddId,
            @RequestParam @ApiParam("钉钉名称") String name,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PostMapping("users/ddBindUser")
    @ApiOperation("钉钉账号绑定用户")
    BaseResult<Void> ddBindUser(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("钉钉id") Long ddId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("操作人") Integer operatorId);

    @GetMapping("users/serverExtMap/by/ddId")
    @ApiOperation("获取serverExtMap")
    Map<Long, StaffServerExtResp> getServerExtMap(@RequestBody BatchReq<Long> req);

    @ApiOperation("钉钉账号解绑用户")
    @PostMapping("users/ddUnBindUser")
    BaseResult<Void> ddUnBindUser(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("操作人") Integer operatorId
    );

    @GetMapping("users/bind/record")
    @ApiOperation("钉钉绑定记录")
    PageResult<List<UcServerDdBindRecord>> getDdBindRecord(
            @RequestParam(required = false) @ApiParam("操作类型") Integer operatorType,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("分页数量") Integer size);

    @GetMapping("departments/getByUserId")
    @ApiOperation("根据userId获取部门信息")
    BaseResult<UcDepartment> getDepartmentByUserId(@RequestParam @ApiParam("用户id") Integer userId);

    @ApiOperation("友睿智能诊股")
    @PostMapping(value = "yourui/stock")
    BaseResult<Void> yrExamingStockCreateOne(@RequestBody @ApiParam("诊股信息") CreateUserYrExamingStockReq req);

    @ApiOperation("获取友睿智能诊股列表")
    @GetMapping(value = "yourui/stock/list")
    PageResult<List<UcUserYrExaminingStock>> getYrExaminingStockPage(
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("staff/device/list")
    @ApiOperation(value = "查询员工设备列表")
    BaseResult<List<UcStaffDevice>> getStaffDeviceList(
            @RequestParam(required = false) @ApiParam("员工id") Integer staffId,
            @RequestParam(required = false) @ApiParam("应用number") String appNumber);

    @PostMapping("staff/device/{id}")
    @ApiOperation("设备修改备注")
    BaseResult<Void> editRemark(
            @PathVariable @ApiParam("设备id") Long id,
            @RequestParam @ApiParam("备注") String remark);

    @PostMapping("users/edit/{id}/name")
    @ApiOperation("修改主账号员工姓名")
    BaseResult<String> editName(
            @PathVariable Integer id,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("员工姓名") String name);

    @PostMapping("users/edit/{id}/mobile")
    @ApiOperation("修改主账号手机号")
    BaseResult<String> editMobile(
            @PathVariable Integer id,
            @RequestParam @ApiParam("手机号") String mobile);

    @GetMapping("ip_geo")
    @ApiOperation("获取ip地理信息")
    BaseResult<UcIpGeo> getIpGeo(
            @RequestParam @ApiParam("ip") String ip);

    @PostMapping("ip_geo/batch")
    @ApiOperation("批量获取ip地理信息")
    BaseResult<Map<String, UcIpGeo>> batchIpGeoMap(@RequestBody BatchReq<String> req);

    @PostMapping("staff/device/{id}/remove")
    @ApiOperation("移除设备")
    BaseResult<Void> removeDevice(
            @PathVariable @ApiParam("设备id") Long id,
            @RequestParam @ApiParam("操作人") Integer operatorId);

    @GetMapping("staff/device/record/page")
    @ApiOperation(value = "获取移除设备记录")
    PageResult<List<StaffDeviceDto>> getStaffDeviceRecordPage(
            @RequestParam @ApiParam("员工id") Integer staffId,
            @RequestParam @ApiParam("应用number") String appNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("staff/device/active")
    @ApiOperation("激活设备")
    BaseResult<Void> activeDevice(
            @RequestParam @ApiParam("员工id") Integer staffId,
            @RequestParam @ApiParam("应用id") String appId,
            @RequestParam @ApiParam("设备号") String deviceNumber);

    @PostMapping("staff/device/update")
    @ApiOperation("更新设备信息")
    BaseResult<Void> updateDevice(
            @RequestParam @ApiParam("员工id") Integer staffId,
            @RequestParam @ApiParam("应用id") String appId,
            @RequestParam @ApiParam("设备号") String deviceNumber,
            @RequestParam @ApiParam("ip") String ip,
            @RequestParam @ApiParam("userAgent") String userAgent);

    @GetMapping("staff/device/{deviceNumber}")
    @ApiOperation(value = "查询设备")
    BaseResult<UcStaffDevice> findStaffDevice(
            @RequestParam @ApiParam("应用id") String appId,
            @PathVariable @ApiParam("设备号") String deviceNumber);

    @GetMapping("staff/device")
    @ApiOperation(value = "查询登录设备信息")
    BaseResult<UcStaffDevice> findStaffLoginDevice(
            @RequestParam @ApiParam("员工id") Integer staffId,
            @RequestParam @ApiParam("应用id") String appId,
            @RequestParam @ApiParam("设备号") String deviceNumber);

    @GetMapping("departments/{userId}/isManager")
    @ApiOperation("根据userId获取user是否部门管理员")
    BaseResult<UcServer2department> getServer2department(@PathVariable @ApiParam("用户id") Integer userId);

    @GetMapping("staff/password/list")
    @ApiOperation(value = "查询员工历史密码列表")
    BaseResult<List<UcStaffPasswordRecord>> getStaffPasswordRecordList(
            @RequestParam @ApiParam("员工id") Integer staffId);

    @PostMapping("staff/password/create")
    @ApiOperation("创建员工密码记录")
    BaseResult<Void> createStaffPasswordRecord(
            @RequestParam @ApiParam("员工id") Integer staffId,
            @RequestParam @ApiParam("密码") String password);

    @GetMapping("departments/user_id/byDeptList")
    @ApiOperation("根据部门列表获取用户ID列表")
    BaseResult<List<Integer>> getUserIdByDepartmentList(@RequestParam @ApiParam("部门列表") List<Integer> deptList);

    @PostMapping("one/key/login/tokenValidate")
    @ApiOperation("token校验")
    BaseResult<TokenValidateResp> tokenValidate(
            @RequestBody @Validated TokenValidateReq req);

    @PostMapping("id_card/update/face-auth")
    @ApiOperation("e签宝人脸识别结果更新")
    BaseResult<Void> updateFaceAuthIdCard(
            @RequestParam @ApiParam("测评id") Integer evaluationId,
            @RequestParam @ApiParam("身份证号") String identityNumber,
            @RequestParam @ApiParam("姓名") String name,
            @RequestParam @ApiParam("是否成功") Boolean isSuccess);

    @GetMapping("users/batch_get/all_user")
    @ApiOperation("批量获取全部用户")
    BaseResult<List<Integer>> batchGetAllUser(
            @RequestParam(required = false) @ApiParam("开始id") Integer startId,
            @RequestParam @ApiParam("用户类型") Integer userType,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("数量") Integer size);

    @GetMapping("one/key/login/certificate")
    @ApiOperation("获取一键登录凭证")
    BaseResult<OneKeyLoginCertificate> getCertificate();

    @GetMapping("departments/user/byDeptIds")
    @ApiOperation("根据部门Ids获取用户列表")
    BaseResult<List<UcUsers>> getUsersByDepartmentIds(@RequestParam @ApiParam("部门Ids") List<Integer> deptIds);

    @RequestMapping("apple/account")
    @ApiOperation("根据公司类型sub获取苹果用户信息")
    BaseResult<UcAppleAccount> getAppleAccount(
            @RequestParam String sub,
            @RequestParam Integer companyType);

    @PostMapping("apple/bind")
    @ApiOperation("绑定苹果用户")
    BaseResult<Void> bindAppleAccount(@RequestBody @Valid UcAppleAccount appleAccount);

    @PostMapping("apple/createOrUpdate")
    @ApiOperation("添加/更新苹果用户信息")
    BaseResult<Void> createOrUpdateAppleAccount(@RequestBody @Valid UcAppleAccount appleAccount);

    @GetMapping("apple/byUserId")
    @ApiOperation("通过用户id获取苹果用户信息")
    BaseResult<UcAppleAccount> getAppleAccountByUserId(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PostMapping("apple/cancel")
    @ApiOperation("苹果注销")
    BaseResult<Void> cancelAppleAccount(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @PostMapping("one/key/login/tokenValidateGetMobile")
    @ApiOperation("token校验获取手机号")
    BaseResult<TokenValidateResp> tokenValidateGetMobile(
            @RequestBody @Validated TokenValidateReq req);

    @PostMapping("users/set/is_Server")
    @ApiOperation("设置白名单")
    BaseResult<Void> setIsServer(
            @RequestBody @Valid SetCustomerWhitelistReq req);

    @GetMapping("users/whitelist/operate_record")
    @ApiOperation("获取白名单操作记录")
    PageResult<List<UcCustomerWhitelistOperateRecord>> getWhitelistOperateRecordList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("users/whitelist")
    @ApiOperation("查询白名单用户列表")
    PageResult<List<UcUsers>> getWhitelistUserList(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("userId") Integer userId,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("是否后台用户") Boolean isServer,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("users/search/sever")
    @ApiOperation("搜索后台用户")
    PageResult<List<SeverResp>> searchServer(
            @RequestParam @ApiParam("工号或者姓名") String searchContent,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("tencent/ccc/call-record-callback")
    @ApiOperation("电话录音数据推送")
    BaseResult<TxCallRecordCallbackResp> handleTxCallRecordCallback(@RequestBody TxCallRecordCallbackReq callbackReq);

    @PostMapping("tencent/ccc/sync-call-seat")
    @ApiOperation("腾讯云通话坐席同步")
    BaseResult<String> syncTxCallSeat();

    @GetMapping("call/tencent/call-list")
    @ApiOperation("获取腾讯云通话记录")
    PageResult<List<TxCallRecordResp>> getTxCallRecordList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("客户手机号") Long mobileId,
            @RequestParam(required = false) @ApiParam("通话类型（呼入/呼出）") Boolean isCallIn,
            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
            @RequestParam(required = false) @ApiParam("通话时间（开始时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("通话时间（结束时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchText,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @GetMapping("call/tencent/call-config")
    @ApiOperation("获取打电话配置")
    BaseResult<TxCallConfigResp> getCallConfig(
            @RequestParam @ApiParam("坐席邮箱") String seatMail);

    @GetMapping("call/just/call-list")
    @ApiOperation("获取集时云通话记录")
    PageResult<List<JustCallRecordResp>> getJustCallRecordList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("客户手机号id") Long mobileId,
            @RequestParam(required = false) @ApiParam("通话类型（呼入/呼出）") Boolean isCallIn,
            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
            @RequestParam(required = false) @ApiParam("通话时间（开始时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("通话时间（结束时间）") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size);

    @GetMapping("call/user-just-seat/info")
    @ApiOperation("获取用户集时云坐席信息")
    BaseResult<UcJustCallSeatInfo> getUserJustCallSeatInfo(
            @RequestParam(required = false) @ApiParam("用户id") Integer userId);

    @GetMapping("call/user-tx-seat/info")
    @ApiOperation("获取用户集时云坐席信息")
    BaseResult<UcTxCallSeatInfo> getUserTxCallSeatInfo(
            @RequestParam(required = false) @ApiParam("用户id") Integer userId);

    @PostMapping("call/seat/bind")
    @ApiOperation("绑定或解绑坐席用户")
    BaseResult<String> bindSeatUser(
            @RequestParam @ApiParam("坐席id") Long seatId,
            @RequestParam @ApiParam("坐席类型") Integer seatType,
            @RequestParam(required = false) @ApiParam("销售id（该参数不传即为解绑操作）") Integer serverId);

    @GetMapping("call/seat/tx-call/list")
    @ApiOperation("获取腾讯云坐席列表")
    PageResult<List<TencentSeatResp>> getTencentCallSeatList(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("call/seat/just-call/list")
    @ApiOperation("获取集时云坐席列表")
    PageResult<List<JustCallSeatResp>> getJustCallSeatList(
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size);

    @PostMapping("call/seat/just-call/create")
    @ApiOperation("添加集时云坐席")
    BaseResult<String> createSeat(
            @RequestBody @Valid CreateJustCallSeatReq req);

    @PostMapping("call/seat/tencent/update")
    @ApiOperation("更新腾讯云坐席信息")
    BaseResult<String> updateTxSeat(@RequestParam @ApiParam("销售id") Integer serverId);

    @GetMapping("call/seat/tencent/get-update-result")
    @ApiOperation("查询腾讯云坐席更新结果")
    BaseResult<UpdateTxSeatResult> getUpdateTxResult();

    @GetMapping("call/just/get-call-record")
    @ApiOperation("获取通话记录")
    BaseResult<UcJustCallRecord> getJustCallRecord(
            @RequestParam @ApiParam("callId") String callId);

    @PostMapping("call/seat/tencent/update-assigned-phone")
    @ApiOperation("更新腾讯云坐席分配号码")
    BaseResult<Void> updateTxSeatAssignedPhone(@RequestParam @ApiParam("坐席id") Long seatId,
                                               @RequestParam(required = false) @ApiParam("分配号码") String assignedPhone);

    @ApiOperation("按通话id获取腾讯云通话记录")
    @GetMapping("call/tencent/record-info")
    BaseResult<TxCallRecordResp> getTxCallRecordByCallId(
            @RequestParam @ApiParam("callId") Long callId);

    @ApiOperation("按通话id对应客户电话号码获取腾讯云通话记录列表")
    @GetMapping("call/tencent/record-list/by-call-id")
    PageResult<List<TxCallRecordResp>> getTxCallRecordByCustomerMobileOfCallId(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = true) @ApiParam("callId") Long callId,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size);

    @PostMapping("app-action-log/create")
    @ApiOperation("app行为上报")
    BaseResult<Void> createAppActionLog(
            @RequestBody CreatePcAppActionReq req);

    @GetMapping("users/pay-company/find-or-update")
    @ApiOperation("获取或更新用户主体")
    BaseResult<Long> findOrUpdatePayCompany(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("users/pay-company/find")
    @ApiOperation("获取用户主体")
    BaseResult<Long> findPayCompany(
            @RequestParam @ApiParam("用户id") Integer userId);


    @GetMapping("verify-intelligent/captcha/config")
    @ApiOperation("获取验证码智能验证配置")
    BaseResult<VerifyIntelligentCaptchaConfigResp> getVerifyCaptchaConfig(
            @RequestParam(required = false,defaultValue = "h5") @ApiParam("应用类型") String type);

    @PostMapping("verify-intelligent/captcha")
    @ApiOperation("验证码智能验证")
    BaseResult<VerifyIntelligentCaptchaResp> getVerifyIntelligentCaptcha(
            @RequestBody @Valid VerifyIntelligentCaptchaReq req);

    @GetMapping("users/search/user-wechat-info")
    @ApiOperation("湖仓搜索用户及微信信息")
    PageResult<List<UserAndWxInfoResp>> searchUserAndWxInfo(
            @RequestParam(required = false) @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("用户code") String userCode,
            @RequestParam(required = false) @ApiParam("手机号") String mobile,
            @RequestParam(required = false) @ApiParam("真实姓名") String realName,
            @RequestParam(required = false) @ApiParam("用户昵称") String userNickName,
            @RequestParam(required = false) @ApiParam("微信昵称") String wxNickName,
            @RequestParam(required = false) @ApiParam("unionIdSet") Set<String> unionIdSet,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("users/by-real-name")
    @ApiOperation("根据真实姓名获取用户信息")
    BaseResult<List<UcUsers>> getUserByRealName(@RequestParam @ApiParam("真实姓名") String realName);


    @ApiOperation("获取手机号修改次数")
    @GetMapping("customer_change/change-mobile-check")
    BaseResult<Void> changeMobileCheck(@RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("customer_change/mobile-change-record")
    @ApiOperation("获取用户手机号变更记录")
    PageResult<List<CustomerChangeRecordResp>> getCustomerChangeRecordList(
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("customer_change/wx-change-record")
    @ApiOperation("获取微信换绑记录")
    PageResult<List<WxChangeRecordResp>> getWxChangeRecordList(
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("tencent/ccc/user-info")
    @ApiOperation("sessionId查询用户信息")
    BaseResult<UcUsers> getTencentCccUserInfo(@RequestParam Integer companyType, @RequestParam String sessionId);

    @GetMapping("ad/bind-mobile")
    @ApiOperation("广告页绑定手机号")
    BaseResult<Void> adBindMobile(
            @RequestParam String mobile,
            @RequestParam(required = false) String ip,
            @RequestParam(required = false) String userAgent);

    @PostMapping("staff-role/role/create")
    @ApiOperation("新建角色")
    BaseResult<Void> createStaffRole(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色名称") String name,
            @RequestParam @ApiParam("角色编号") String number);

    @PostMapping("staff-role/role/enable")
    @ApiOperation("启用/禁用角色")
    BaseResult<Void> enableStaffRole(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色id") Long roleId,
            @RequestParam @ApiParam("启用/禁用") Boolean enabled);

    @GetMapping("staff-role/role/list")
    @ApiOperation("角色列表")
    PageResult<List<StaffRoleResp>> getStaffRoleList(
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size);

    @PostMapping("staff-role/role/batch")
    @ApiOperation("根据角色ID批量获取角色map")
    BaseResult<Map<Long, UcStaffRole>> batchGetStaffRoleMap(@RequestBody BatchReq<Long> req);

    @GetMapping("staff-role/role/record/list")
    @ApiOperation("角色操作列表")
    PageResult<List<UcStaffRoleRecord>> getStaffRoleRecordList(
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size);

    @PostMapping("staff-role/relation/create")
    @ApiOperation("新增角色成员")
    BaseResult<Void> createStaffRoleRelation(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色id") Long roleId,
            @RequestParam @ApiParam("成员id列表") List<Integer> userIdList);

    @PostMapping("staff-role/relation/disable")
    @ApiOperation("删除角色成员")
    BaseResult<Void> disableStaffRoleRelation(
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestParam @ApiParam("角色id") Long roleId,
            @RequestParam @ApiParam("成员id") Integer userId);

    @GetMapping("staff-role/relation/list")
    @ApiOperation("角色成员列表")
    PageResult<List<UcStaffRoleRelation>> getStaffRoleRelationList(
            @RequestParam(required = false) @ApiParam("角色编号列表") List<String> roleNumberList,
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size);

    @GetMapping("staff-role/relation/list/by-number")
    @ApiOperation("根据角色编号获取角色成员列表")
    BaseResult<List<UcStaffRoleRelation>> getStaffRoleRelationListByNumber(
            @RequestParam(required = false) @ApiParam("角色编号") String roleNumber,
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled);

    @GetMapping("staff-role/relation/role/list")
    @ApiOperation("按角色获取角色成员列表")
    BaseResult<List<UcStaffRoleRelation>> getStaffRoleRelationListByRoleId(
            @RequestParam @ApiParam("角色id") Long roleId);

    @GetMapping("staff-role/relation/record/list")
    @ApiOperation("角色成员操作列表")
    PageResult<List<UcStaffRoleRelationRecord>> getStaffRoleRelationRecordList(
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size);

    @ApiOperation("测评问卷版本列表")
    @GetMapping("evaluation/version/list")
    PageResult<List<EvaluationVersionResp>> getEvaluationVersionList(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("测评问卷版本详情")
    @GetMapping("evaluation/version/detail")
    BaseResult<EvaluationVersionDetailResp> getEvaluationVersionDetail(
            @RequestParam @ApiParam("版本id") Long id);

    @ApiOperation("查看测评问卷配置")
    @GetMapping("evaluation/version/config-info")
    BaseResult<List<EvaluationVersionConfigResp>> getEvaluationVersionConfigInfo(
            @RequestParam @ApiParam("版本id") Long id);

    @ApiOperation("新建测评问卷")
    @PostMapping("evaluation/version/add")
    BaseResult<Void> addEvaluationVersion(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestBody @Valid AddEvaluationVersionReq req);

    @ApiOperation("测评分数区间配置")
    @PostMapping("evaluation/version/score-config")
    BaseResult<Void> editEvaluationVersionScoreConfig(
            @RequestBody @Valid EvaluationVersionScoreConfigReq req);

    @ApiOperation("测评文件模板配置")
    @PostMapping("evaluation/version/template-config")
    BaseResult<Void> editEvaluationVersionTemplateConfig(
            @RequestBody @Valid EvaluationVersionTemplateConfigReq req);

    @ApiOperation("发布测评问卷版本")
    @PostMapping("evaluation/version/publish")
    BaseResult<Void> editEvaluationVersionPublish(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("版本id") Long id);

    @ApiOperation("测评问卷版本操作记录")
    @GetMapping("evaluation/version/operate-record")
    PageResult<List<EvaluationVersionOperateRecordResp>> getEvaluationVersionOperateRecord(
            @RequestParam @ApiParam("版本id") Long id,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("evaluation/start")
    @ApiOperation("创建测评")
    BaseResult<StartOrRedoEvaluationResp> startEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("verify/start")
    @ApiOperation("创建认证")
    BaseResult<CreateVerifyResp> startVerify(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("verify/info")
    @ApiOperation("获取用户的认证信息")
    BaseResult<UcVerifyInfo> getVerifyInfo(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("verify/latest-info")
    @ApiOperation("获取用户的认证信息")
    BaseResult<UcVerifyInfo> latestVerify(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("是否废弃") Boolean isDeprecated
    );

    @PostMapping("verify/submit-id")
    @ApiOperation("三要素身份证校验")
    BaseResult<Void> verifySubmitId(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("认证id") Long verifyId,
            @RequestParam @ApiParam("身份证号") String identityNumber,
            @RequestParam @ApiParam("姓名") String name);

    @PostMapping("verify/submit-other")
    @ApiOperation("其他身份校验")
    BaseResult<Void> verifySubmitOther(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("认证id") Long verifyId,
            @RequestParam @ApiParam("身份证类型") Integer idCardType,
            @RequestParam @ApiParam("身份证号") String identityNumber,
            @RequestParam @ApiParam("姓名") String name);

    @PostMapping("verify/submit-esign-face")
    @ApiOperation("提交签字人脸识别认证")
    BaseResult<FaceAuthResp> verifySubmitEsignFace(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestBody @Valid CreateFaceAuthReq req);

    @ApiOperation("获取人脸识别结果")
    @GetMapping(value = "verify/face-verify/result")
    BaseResult<FaceVerifyResultResp> getFaceVerifyResult(
            @RequestParam @ApiParam("上下文id") String contextId);

    @PostMapping("verify/deprecate")
    @ApiOperation("废弃认证")
    BaseResult<Void> deprecateVerify(
            @RequestParam @ApiParam("认证id") Long verifyId,
            @RequestParam @ApiParam("操作人id") Integer userId);

    @GetMapping("profile/info")
    @ApiOperation("获取客户评估基本信息")
    BaseResult<UcProfileInfo> getProfileInfo(
            @RequestParam @ApiParam("用户id") Integer userId);

    @PostMapping("profile/submit")
    @ApiOperation("提交客户评估基本信息")
    BaseResult<Void> submitProfile(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestBody @Valid SubmitProfileReq req);

    @PostMapping("profile/delete")
    @ApiOperation("清空用户基本信息")
    BaseResult<Void> deleteProfile(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("evaluation/survey/topic-list")
    @ApiOperation("获取题目列表")
    BaseResult<List<EvaluationTopicInfoResp>> getSurveyTopicList(
            @RequestParam @ApiParam("测评number") String evaluationNumber);

    @GetMapping("evaluation/survey/answer-info")
    @ApiOperation("获取测评的答案选项")
    BaseResult<SurveyAnswerInfoResp> getSurveyAnswerInfo(
            @RequestParam @ApiParam("测评number") String evaluationNumber);

    @PostMapping("evaluation/survey/submit-answer")
    @ApiOperation("保存测评选项")
    BaseResult<Void> submitSurveyAnswer(
            @RequestBody @Valid SubmitSurveyAnswerReq req);

    @GetMapping("evaluation/deprecate")
    @ApiOperation("废弃测评")
    BaseResult<Void> deprecateEvaluation(
            @RequestParam @ApiParam("操作人id") Integer userId,
            @RequestParam @ApiParam("测评number") String evaluationNumber,
            @RequestParam @ApiParam("是否删除测评次数") Boolean isDelete);

    @GetMapping("evaluation/info")
    @ApiOperation("获取用户的测评信息")
    BaseResult<UcEvaluationInfo> getEvaluationInfo(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("evaluation/latest-info")
    @ApiOperation("获取用户的测评信息")
    BaseResult<UcEvaluationInfo> getLatestEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("是否废弃") Boolean isDeprecated
    );

    @PostMapping("evaluation/finish")
    @ApiOperation("提交客户评估")
    BaseResult<Void> finishEvaluation(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("测评number") String evaluationNumber);

    @GetMapping("evaluation/choice/options")
    @ApiOperation("获取评测题目选项")
    BaseResult<List<UcEvaluationChoice>> getEvaluationChoiceList(
            @RequestParam @ApiParam("评测版本id") Long versionId,
            @RequestParam @ApiParam("评测id") Long evaluationId,
            @RequestParam @ApiParam("第几题") Integer order);

    @ApiOperation("获取客户测评操作记录")
    @GetMapping("evaluation/record-list")
    PageResult<List<EvaluationRecordResp>> getEvaluationRecordList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("获取客户认证操作记录")
    @GetMapping("verify/record-list")
    PageResult<List<VerifyRecordResp>> getVerifyRecordList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("根据题目内容获取选项答案信息")
    @GetMapping("evaluation/choice/info")
    BaseResult<UcEvaluationChoice> getEvaluationChoiceInfo(
            @RequestParam @ApiParam("测评id") Long evaluationId,
            @RequestParam @ApiParam("题目内容") String content);

    @ApiOperation("获取客户测评记录")
    @GetMapping("evaluation/list")
    PageResult<List<UcEvaluationInfo>> getEvaluationList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @ApiOperation("批量获取版本")
    @GetMapping("evaluation/batch/version")
    BaseResult<List<UcEvaluationVersion>> getVersionBatch(
            @RequestParam @ApiParam("版本id") Set<Long> versionIdList);

    @ApiOperation("获取客户认证记录")
    @GetMapping("verify/list")
    PageResult<List<UcVerifyInfo>> getVerifyList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @PostMapping("evaluation/batch/user")
    @ApiOperation("根据userid批量获取客户评估")
    BaseResult<List<UcEvaluationInfo>> getEvaluationInfoList(
            @RequestBody BatchReq<Integer> req);

    @GetMapping("verify/batch/user")
    @ApiOperation("根据userid批量获取认证信息")
    BaseResult<List<UcVerifyInfo>> getVerificationInfoList(
            @RequestBody  BatchReq<Integer> req
    );

    @GetMapping("profile/batch/user")
    @ApiOperation("根据userid客户品策基本信息")
    BaseResult<List<UcProfileInfo>> getProfileInfoList(
            @RequestBody BatchReq<Integer> req);

    @PostMapping("evaluation/check-count")
    @ApiOperation("校验用户的测评次数")
    BaseResult<CheckEvaluationCountResp> checkEvaluationCount(
            @RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("oss/generate-presign-url")
    @ApiOperation("生成预签名url")
    BaseResult<URL> generatePresignUrl(@RequestParam @ApiParam("业务分类number") Integer number,
                                       @RequestParam @ApiParam("originUrl") String originUrl);

    @GetMapping("oss/config-info")
    @ApiOperation("获取相关oss配置")
    BaseResult<OssConfigInfoResp> getOssConfigInfo(@RequestParam @ApiParam("业务分类number") Integer number);

    @PostMapping("customer/follow/up/belong/belongs")
    @ApiOperation("根据归属销售id批量查询服务归属关系")
    BaseResult<List<UcCustomerFollowUpRelation>> getRelationByBelongIds(@RequestBody BatchReq<Integer> req);

    @PostMapping("customer/follow/up/belong/users")
    @ApiOperation("根据用户id批量查询服务归属关系")
    BaseResult<List<UcCustomerFollowUpRelation>> getRelationByUserIds(@RequestBody BatchReq<Integer> req);

    @GetMapping("oss/content")
    @ApiOperation("获取文件内容")
    BaseResult<String> getOssContent(@RequestParam @ApiParam("业务分类number") Integer number,
                                     @RequestParam @ApiParam("originUrl") String originUrl,
                                     @RequestParam @ApiParam("charset") String charset);


    @PostMapping("customer-new/global-search-new")
    @ApiOperation("湖仓搜索虚假客户")
    PageResult<List<UcFakeUser>> globalSearchFakeByLakeHouse(@RequestBody GlobalSearchFakeReq req);

    @GetMapping("customer-new/fake-properties")
    @ApiOperation("获取虚假客户数据配置")
    BaseResult<FakeDataPropertyResp> getFakeUserProperties();

    @GetMapping("users/search/user-batch")
    @ApiOperation("搜索用户及微信信息")
    BaseResult<List<UserAndWxInfoResp>> getUserAndWxInfoBatch(@RequestParam @ApiParam("用户id列表") List<Integer> userIds);

    @GetMapping("users/search/user-info")
    @ApiOperation("用户code、用户昵称、微信昵称搜索用户及微信信息")
    BaseResult<List<UserAndWxInfoResp>> getUserAndWxInfo(@RequestParam @ApiParam("用户code、用户昵称、微信昵称") String searchContent);
}

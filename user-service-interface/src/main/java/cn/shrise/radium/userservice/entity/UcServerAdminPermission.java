package cn.shrise.radium.userservice.entity;

import cn.shrise.radium.userservice.constant.AdminPermissionTypeConstant;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Table(name = "uc_server_admin_permission", indexes = {
        @Index(name = "UserID", columnList = "UserID")
})
@Entity
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UcServerAdminPermission implements Serializable {
    private static final long serialVersionUID = -6545539029536288106L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "UserID")
    private Integer userId;

    /**
     * @see AdminPermissionTypeConstant
     */
    @Column(name = "PermissionType")
    private Integer permissionType;

    @Column(name = "ExpireTime")
    private Instant expireTime;

    @Column(name = "CreateTime", insertable = false, updatable = false)
    private Instant createTime;

    @Column(name = "UpdateTime", insertable = false, updatable = false)
    private Instant updateTime;

}
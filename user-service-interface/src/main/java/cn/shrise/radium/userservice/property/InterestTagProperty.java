package cn.shrise.radium.userservice.property;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "interest")
@EnableConfigurationProperties
public class InterestTagProperty {

    private List<InterestGroup> groups;
    private List<InterestTag> tags;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterestGroup{
        private String id;
        private String name;
        private List<InterestGroup> groups;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterestTag{
        private String code;
        private String name;
        private String group;
        private String label;
    }
}

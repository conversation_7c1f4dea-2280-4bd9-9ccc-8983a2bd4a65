package cn.shrise.radium.userservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Table(name = "uc_department_rank_plan_relation", indexes = {
        @Index(name = "UserID", columnList = "UserID")
})
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class UcDepartmentRankPlanRelation {
    @EmbeddedId
    private UcDepartmentRankPlanRelationId id;

    @Column(name = "CreateTime")
    private Instant createTime;
}
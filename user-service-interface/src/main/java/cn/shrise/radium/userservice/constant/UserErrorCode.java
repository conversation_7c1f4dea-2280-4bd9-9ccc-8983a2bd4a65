package cn.shrise.radium.userservice.constant;


import cn.shrise.radium.common.base.BaseError;

/**
 * 公共异常枚举类
 */
public enum UserErrorCode implements BaseError {

    FAILURE(-1, "Failure"),
    SUCCESS(0, "Success"),

    MAIN_ACCOUNT_NOT_FOUND(********, "主账户不存在"),
    ACCOUNT_NOT_FOUND(********, "账户不存在"),
    NOT_FOUND(********, "记录不存在"),
    INVALID_PARAM(********, "参数不合法"),
    RECORD_EXISTED(********, "记录已存在"),
    USER_EXISTED(8103006, "用户已存在"),
    USER_NOT_EXISTED(8103007, "用户不存在"),

    SECURITY_CODE_FREQUENCY_LIMIT(8103008, "验证码请求频繁"),

    SECURITY_CODE_EXPIRED(8103009, "验证码过期"),

    SECURITY_CODE_NOT_VALID(8103010, "无效验证码"),

    ID_VERIFY_FAILURE(8103011, "手机号/身份证/姓名必须为同一人，请检查后重新填写"),

    SECURITY_CODE_ERROR(8103012, "验证码错误"),
    HEAD_UPLOAD_UNKNOWN_ERROR(********, "头像上传失败"),
    ID_NAME_VERIFY_FAILURE(********, "您的姓名和身份证件号码不匹配，请重新填写！"),

    TAG_GROUP_EXISTED(********, "标签组已存在"),

    TAG_EXISTED(********, "标签已存在"),

    PASSWORD_ERROR(********, "密码错误"),

    PAGE_ID_EXISTED(********, "页面ID已存在"),

    PAGE_NAME_NOT_EMPTY(81030018, "页面名称不能为空"),
    SECURITY_CODE_FREQUENCY_DURATION_LIMIT(8103019, "验证码获取次数超过上限！请稍后再试"),
    SECURITY_CODE_FREQUENCY_DURATION_LIMIT_PARTNER(8103024, "今日已达发送验证码上限（10 次），请次日重试"),
    SECURITY_CODE_MAX_ERROR_VERIFY_COUNT(8103020, "验证码错误次数过多，请重新获取验证码"),
    RESET_PASSWORD_REPEAT(8103021, "密码不能与前5次相同！请修改"),
    PASSWORD_CANNOT_CONTAIN_USERNAME(8103022, "用户登录密码不可包含用户名"),
    INTELLIGENT_CAPTCHA_INVALID(81150001, "阿里验证码参数验证失败"),
    MOBILE_CHANGE_FREQUENCY_LIMIT(81030023, "更改手机号次数达到限制"),
    ;

    private final Integer code;
    private final String msg;

    UserErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

}

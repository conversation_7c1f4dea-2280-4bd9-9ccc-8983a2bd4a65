package cn.shrise.radium.userservice.property;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "datamet")
@EnableConfigurationProperties
public class DataMetProperty {

    /**
     * 请求地址
     */
    private String url;

    /**
     * appCode
     */
    private String appCode;

    /**
     * appKey
     */
    private String appKey;

    /**
     * appSecret
     */
    private String appSecret;

    /**
     * stage
     */
    private String stage;

    /**
     * idTypes
     */
    private String[] idTypes;

    /**
     * 手机号盐
     */
    private String salt;

    /**
     * 系统
     */
    private String os;

}

package cn.shrise.radium.userservice.req;

import cn.shrise.radium.common.base.BasePageReq;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel(description = "认领")
@NoArgsConstructor
@AllArgsConstructor
public class ClaimedCustomerReq extends BasePageReq {

    @ApiModelProperty(value = "时间类型(1:认领时间,2:最后跟进时间)")
    private Integer dateType;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    @ApiModelProperty(value = "用户列表")
    private List<Integer> userList;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Integer userId;

    @ApiModelProperty(value = "公司类型", hidden = true)
    private Integer companyType;

    @ApiModelProperty(value = "标签条件(是否并集)")
    private Boolean isUnion;

    @ApiModelProperty(value = "标签列表")
    private List<Integer> tagList;

    @ApiModelProperty(value = "是否过期")
    private Boolean isExpired;

}

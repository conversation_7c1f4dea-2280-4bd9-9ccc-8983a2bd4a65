package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "uc_evaluation_choice", indexes = {
        @Index(name = "idx_topic_id", columnList = "topic_id")
})
public class UcEvaluationChoice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "topic_id", nullable = false)
    private Long topicId;

    @Lob
    @Column(name = "content")
    private String content;

    @Column(name = "score")
    private Integer score;

}
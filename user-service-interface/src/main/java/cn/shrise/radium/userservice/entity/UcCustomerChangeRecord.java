package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Getter
@Setter
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "uc_customer_change_record", indexes = {
        @Index(name = "idx_customer_id", columnList = "customer_id")
})
public class UcCustomerChangeRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "customer_id", nullable = false)
    private Integer customerId;

    @Column(name = "change_type", nullable = false)
    private Integer changeType;

    @Column(name = "content", length = 64)
    private String content;

    @Column(name = "mobile_id")
    private Long mobileId;

    @Column(name = "product_type")
    private Integer productType;
}
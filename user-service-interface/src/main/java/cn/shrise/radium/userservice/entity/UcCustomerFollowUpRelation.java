package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Objects;

@Entity
@Table(name = "uc_customer_follow_up_relation", schema = "auth_db", catalog = "")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UcCustomerFollowUpRelation {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id", nullable = false)
    private Long id;
    @Basic
    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;
    @Basic
    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;
    @Basic
    @Column(name = "user_id", nullable = false)
    private Integer userId;
    @Basic
    @Column(name = "belong_id", nullable = true)
    private Integer belongId;
    @Basic
    @Column(name = "last_belong_time", nullable = true)
    private Instant lastBelongTime;
    @Basic
    @Column(name = "last_follow_up_time", nullable = true)
    private Instant lastFollowUpTime;
    @Basic
    @Column(name = "company_type", nullable = false)
    private Integer companyType;
    @Basic
    @Column(name = "enabled", nullable = false, insertable = false)
    private Boolean enabled;

}

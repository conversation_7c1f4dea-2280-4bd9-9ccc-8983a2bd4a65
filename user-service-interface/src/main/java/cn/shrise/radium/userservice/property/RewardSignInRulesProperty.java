package cn.shrise.radium.userservice.property;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "sign-in-rules")
@EnableConfigurationProperties
public class RewardSignInRulesProperty {

    private Rules general;
    private Rules high;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rules {
        private Long day1;
        private Long week3;
        private Long week5;
        private Long month20;
    }
}

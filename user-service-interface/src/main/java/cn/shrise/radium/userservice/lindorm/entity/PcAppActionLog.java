package cn.shrise.radium.userservice.lindorm.entity;

import lombok.*;
import org.springframework.data.domain.Persistable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "pc_app_action_log")
public class PcAppActionLog implements Persistable {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "gmt_create")
    private Long gmtCreate;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "category")
    private String category;

    @Column(name = "action")
    private String action;

    @Column(name = "label")
    private String label;

    @Column(name = "content")
    private String content;

    @Column(name = "ip")
    private String ip;

    @Column(name = "computer_name")
    private String computerName;

    @Column(name = "mac")
    private String mac;

    @Column(name = "company_type")
    private Integer companyType;

    @Override
    public boolean isNew() {
        return true;
    }
}

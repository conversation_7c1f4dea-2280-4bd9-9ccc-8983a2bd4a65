package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
        name = "uc_check_in_record",
        schema = "auth_db",
        indexes = {
                @Index(name = "unique_idx", columnList = "user_id,check_in_date", unique = true)
        }
)
public class UcCheckInRecord {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;
    @Basic
    @Column(name = "gmt_create", insertable = false, updatable = false)
    private Instant gmtCreate;
    @Basic
    @Column(name = "gmt_modified", insertable = false, updatable = false)
    private Instant gmtModified;
    @Basic
    @Column(name = "user_id")
    private Integer userId;
    @Basic
    @Column(name = "check_in_date")
    private Instant checkInDate;
    @Basic
    @Column(name = "weekly_count")
    private Integer weeklyCount;
    @Basic
    @Column(name = "monthly_count")
    private Integer monthlyCount;
    
}

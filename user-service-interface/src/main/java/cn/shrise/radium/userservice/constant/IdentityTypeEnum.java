package cn.shrise.radium.userservice.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum IdentityTypeEnum {
    /**
     * 证件类型枚举
     */
    ID_CARD(100,"身份证"),
    PASSPORT(200,"中国护照"),
    TW_CARD(300,"台湾来往大陆通行证"),
    HK_CARD(310,"香港来往大陆通行证"),
    MC_CARD(320,"澳门来往大陆通行证"),
    GREEN_CARD(390,"中国永久居留证"),
    FOREIGN_CARD(400,"外籍证件"),
    OTHER(999,"其它");

    private final Integer code;
    private final String value;

    IdentityTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getIdentityType(Integer identityType){
        for (IdentityTypeEnum identityTypeEnum : IdentityTypeEnum.values()){
            if (identityTypeEnum.code.equals(identityType)){
                return identityTypeEnum.value;
            }
        }
        return null;
    }

}

package cn.shrise.radium.userservice.property;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/16 17:31
 * @Desc:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "tencent.ccc")
public class TencentCccProperties {
    private String secretId;
    private String secretKey;
    private String endpoint;
    private String region;
    private Long sdkAppId;
}

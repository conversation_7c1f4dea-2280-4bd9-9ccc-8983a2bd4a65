package cn.shrise.radium.userservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("创建标签组")
public class CreateTagGroupReq {

    @ApiModelProperty("标签组名称")
    private String name;

    @ApiModelProperty("是否全局(是否销售可编辑)")
    private Boolean isGlobal;

    @ApiModelProperty("是否单选(单选即为互斥)")
    private Boolean isRadio;

    @ApiModelProperty(value = "公司类型", hidden = true)
    private Integer companyType;
}

package cn.shrise.radium.userservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "uc_department_staff_history", indexes = {
        @Index(name = "uni_department_date", columnList = "DepartmentID, FlagDate", unique = true)
})
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class UcDepartmentStaffHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "DepartmentID", nullable = false)
    private Integer departmentId;

    @Column(name = "FlagDate", nullable = false)
    private Instant flagDate;

    @Lob
    @Column(name = "UserList")
    private String userList;

    @Lob
    @Column(name = "WorkUserList")
    private String workUserList;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

}
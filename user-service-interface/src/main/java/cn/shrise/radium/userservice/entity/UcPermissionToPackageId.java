package cn.shrise.radium.userservice.entity;

import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@EqualsAndHashCode
@Embeddable
public class UcPermissionToPackageId implements Serializable {
    private static final long serialVersionUID = 2290997520521729720L;
    @Column(name = "PackageID", nullable = false)
    private Integer packageId;
    @Column(name = "PermissionID", nullable = false)
    private Integer permissionId;

    public Integer getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(Integer permissionID) {
        this.permissionId = permissionID;
    }

    public Integer getPackageId() {
        return packageId;
    }

    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

}
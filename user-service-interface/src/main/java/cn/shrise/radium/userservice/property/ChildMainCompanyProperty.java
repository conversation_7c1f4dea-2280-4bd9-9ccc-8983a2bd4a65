package cn.shrise.radium.userservice.property;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "child-main-company")
@EnableConfigurationProperties
public class ChildMainCompanyProperty {

    private Map<Integer, MainConfig> config;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MainConfig {

        private Integer company;
        private Integer account;
    }
}

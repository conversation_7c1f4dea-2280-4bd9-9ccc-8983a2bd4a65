package cn.shrise.radium.userservice.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewardChangeResp<T> {

    @ApiModelProperty("userId")
    private Integer userId;

    @ApiModelProperty("积分来源")
    private Integer source;

    @ApiModelProperty("消费类型")
    private Integer consumeType;

    private T msg;
}

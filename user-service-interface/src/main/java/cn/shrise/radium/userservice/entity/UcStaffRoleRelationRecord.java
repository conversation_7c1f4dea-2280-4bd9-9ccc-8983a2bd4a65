package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.Instant;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "uc_staff_role_relation_record", schema = "auth_db")
public class UcStaffRoleRelationRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "role_id", nullable = false)
    private Long roleId;

    @Column(name = "operator_id", nullable = false)
    private Integer operatorId;

    @Lob
    @Column(name = "content")
    private String content;

}
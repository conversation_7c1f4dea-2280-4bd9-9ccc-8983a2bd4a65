package cn.shrise.radium.userservice.lindorm.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "customer_track_event_stat")
public class LdCustomerTrackEventStat {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "company_type")
    private Integer companyType;

    @Column(name = "flag_time")
    private Long flagTime;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "pv")
    private Long pv;

    @Column(name = "uv")
    private Long uv;

}

package cn.shrise.radium.userservice.entity;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "uc_server_permission_package", indexes = {
        @Index(name = "PackageID", columnList = "PackageID")
})
@Entity
public class UcServerPermissionPackage implements Serializable {
    private static final long serialVersionUID = 4547955749936304174L;
    @EmbeddedId
    private UcServerPermissionPackageId id;

    public UcServerPermissionPackageId getId() {
        return id;
    }

    public void setId(UcServerPermissionPackageId id) {
        this.id = id;
    }
}
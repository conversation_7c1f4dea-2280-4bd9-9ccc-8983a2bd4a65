package cn.shrise.radium.userservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class TxCallRecordRespDto {

    private Long callId;

    private Instant callTime;

    private String phone;

    private Integer serverId;

    private String salesName;

    private String workNumber;

    private String name;

    private String customerMobile;

    private Integer userId;

    private Boolean isCallIn;

    private Integer callStatus;

    private String customRecordUrl;

    private Long seatId;

    private Long mobileId;
}

package cn.shrise.radium.userservice.lindorm.entity;

import lombok.*;
import org.springframework.data.domain.Persistable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ld_customer_track_event")
public class LdCustomerTrackEvent implements Persistable {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "gmt_create")
    private Long gmtCreate;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "wx_id")
    private Integer wxId;

    @Column(name = "account_id")
    private Integer accountId;

    @Column(name = "sales_id")
    private Integer salesId;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "category")
    private String category;

    @Column(name = "action")
    private String action;

    @Column(name = "label")
    private String label;

    @Column(name = "url")
    private String url;

    @Column(name = "ip")
    private String ip;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "company_type")
    private Integer companyType;

    @Override
    public boolean isNew() {
        return true;
    }
}

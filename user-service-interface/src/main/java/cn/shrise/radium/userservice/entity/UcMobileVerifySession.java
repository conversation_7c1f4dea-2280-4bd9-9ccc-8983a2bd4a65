package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "uc_mobile_verify_session")
public class UcMobileVerifySession {
    @Id
    @Column(name = "Mobile", nullable = false, length = 50)
    private String mobile;

    @Column(name = "VerifyNo", nullable = false, length = 50)
    private String verifyNo;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "RepeatCount")
    private Integer repeatCount;

    @Column(name = "RepeatTime")
    private Instant repeatTime;

    @Column(name = "VerifyCount")
    private Integer verifyCount;
}
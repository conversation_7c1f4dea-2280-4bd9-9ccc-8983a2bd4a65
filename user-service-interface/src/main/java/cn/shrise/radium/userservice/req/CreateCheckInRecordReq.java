package cn.shrise.radium.userservice.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateCheckInRecordReq {

    private Integer userId;

    private Instant checkInDate;

    private Integer weeklyCount;

    private Integer monthlyCount;
}

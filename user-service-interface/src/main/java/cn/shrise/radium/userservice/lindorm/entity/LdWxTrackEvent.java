package cn.shrise.radium.userservice.lindorm.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ld_wx_track_event")
public class LdWxTrackEvent {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "wx_id")
    private Integer wxId;

    @Column(name = "category")
    private String category;

    @Column(name = "action")
    private String action;

    @Column(name = "label")
    private String label;

    @Column(name = "create_time")
    private Long createTime;

    @Column(name = "company_type")
    private Integer companyType;

    @Column(name = "url")
    private String url;

    @Column(name = "sales_id")
    private Integer salesId;
}

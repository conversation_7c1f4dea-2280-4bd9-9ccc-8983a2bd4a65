package cn.shrise.radium.userservice.entity;

import cn.hutool.core.text.CharSequenceUtil;
import cn.shrise.radium.userservice.resp.TrackEventDetail;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Builder
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "uc_customer_track_event_summary")
public class UcCustomerTrackEventSummary {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "wx_id")
    private Integer wxId;

    @Column(name = "account_id")
    private Integer accountId;

    @Column(name = "sales_id")
    private Integer salesId;

    @Size(max = 128)
    @Column(name = "app_id", length = 128)
    private String appId;

    @Size(max = 128)
    @Column(name = "page_id", length = 128)
    private String pageId;

    @Size(max = 128)
    @Column(name = "category", length = 128)
    private String category;

    @Size(max = 128)
    @Column(name = "action", length = 128)
    private String action;

    @Size(max = 256)
    @Column(name = "label", length = 256)
    private String label;

    @Column(name = "access_time")
    private Instant accessTime;

    @Column(name = "company_type")
    private Integer companyType;

    @Size(max = 128)
    @Column(name = "last_page_id", length = 128)
    private String lastPageId;

    @Column(name = "last_sales_id")
    private Integer lastSalesId;

    public void setDetail(TrackEventDetail detail) {
        this.category = detail.getCategory();
        this.action = detail.getAction();
        this.label = detail.getLabel();
        this.accessTime = detail.getAccessTime();
        this.companyType = detail.getCompanyType();
    }

    public void setKey(String[] split) {
        this.userId = Integer.valueOf(split[0]);
        this.wxId = Integer.valueOf(split[1]);
        this.accountId = Integer.valueOf(split[2]);
        this.appId = split[3];
        this.pageId = split[4];
        this.salesId = Integer.valueOf(split[5]);
    }

    public String getCustomerTrackEventSummaryKey() {
        return CharSequenceUtil.format("{}_{}_{}_{}_{}_{}", this.userId, this.wxId, this.accountId, this.appId, this.pageId, this.salesId);
    }
}
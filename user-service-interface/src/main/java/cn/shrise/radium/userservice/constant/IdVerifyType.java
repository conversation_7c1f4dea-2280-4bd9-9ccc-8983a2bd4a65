package cn.shrise.radium.userservice.constant;

/**
 * <AUTHOR>
 */
public class IdVerifyType {

    /**
     * 未知
     */
    public static final int IVT_UNKNOWN = 0;
    /**
     * 三要素（手机/身份证号/名字）
     */
    public static final int IVT_THREE = 10;
    /**
     * 二要素（身份证号/名字）
     */
    public static final int IVT_TWO = 20;
    /**
     * 人脸认证
     */
    public static final int IVT_FACE = 30;
    /**
     * 未验证
     */
    public static final int IVT_UNVERIFIED = 99;
}

package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.Instant;

@Builder
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "uc_customer_whitelist_operate_record", schema = "auth_db", indexes = {
        @Index(name = "idx_user_id", columnList = "user_id")
})
public class UcCustomerWhitelistOperateRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @NotNull
    @Column(name = "user_id", nullable = false)
    private Integer userId;

    @Column(name = "operator_id")
    private Integer operatorId;

    @Size(max = 256)
    @Column(name = "operator_content", length = 256)
    private String operatorContent;

}
package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "uc_wx_ext", indexes = {
        @Index(name = "ix_auth_db_uc_wx_ext_FirstSubTime", columnList = "FirstSubTime"),
        @Index(name = "UserID", columnList = "UserID", unique = true),
        @Index(name = "ix_auth_db_uc_wx_ext_AuthTime", columnList = "AuthTime"),
        @Index(name = "UnionID", columnList = "UnionID"),
        @Index(name = "ChannelID", columnList = "ChannelID"),
        @Index(name = "ix_auth_db_uc_wx_ext_AccessTime", columnList = "AccessTime"),
        @Index(name = "unique_union_company", columnList = "UnionID, CompanyType", unique = true)
})
@Getter
@Setter
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class UcWxExt {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "UnionID", length = 128)
    private String unionId;

    @Column(name = "UserID")
    private Integer userId;

    @Column(name = "CompanyType", nullable = false)
    private Integer companyType;

    @Column(name = "Nickname", length = 256)
    private String nickname;

    @Column(name = "Sex")
    private Integer sex;

    @Column(name = "City", length = 256)
    private String city;

    @Column(name = "Country", length = 256)
    private String country;

    @Column(name = "Province", length = 256)
    private String province;

    @Column(name = "Language", length = 256)
    private String language;

    @Column(name = "HeadImgUrl", length = 256)
    private String headImgUrl;

    @Column(name = "ProfileTime")
    private Instant profileTime;

    @Column(name = "ChannelID")
    private Integer channelId;

    @Column(name = "CreateTime")
    private Instant createTime;

    @Column(name = "UpdateTime")
    private Instant updateTime;

    @Column(name = "RelatedUID")
    private Integer relatedUid;

    @Column(name = "FirstSubTime")
    private Instant firstSubTime;

    @Column(name = "AuthTime")
    private Instant authTime;

    @Column(name = "AccessTime")
    private Instant accessTime;

    public Instant getAccessTime() {
        return accessTime;
    }

    public void setAccessTime(Instant accessTime) {
        this.accessTime = accessTime;
    }

    public Instant getAuthTime() {
        return authTime;
    }

    public void setAuthTime(Instant authTime) {
        this.authTime = authTime;
    }

    public Instant getFirstSubTime() {
        return firstSubTime;
    }

    public void setFirstSubTime(Instant firstSubTime) {
        this.firstSubTime = firstSubTime;
    }

    public Integer getRelatedUid() {
        return relatedUid;
    }

    public void setRelatedUid(Integer relatedUid) {
        this.relatedUid = relatedUid;
    }

    public Instant getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Instant getProfileTime() {
        return profileTime;
    }

    public void setProfileTime(Instant profileTime) {
        this.profileTime = profileTime;
    }

    public String getHeadImgUrl() {
        return headImgUrl;
    }

    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public Integer getCompanyType() {
        return companyType;
    }

    public void setCompanyType(Integer companyType) {
        this.companyType = companyType;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
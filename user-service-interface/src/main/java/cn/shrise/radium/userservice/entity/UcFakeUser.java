package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * @Author: tang<PERSON>ajun
 * @Date: 2025/6/16 15:23
 * @Desc:
 **/
@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "uc_fake_user", indexes = {
        @Index(name = "uk_user_id", columnList = "user_id", unique = true),
        @Index(name = "uk_user_code", columnList = "user_code", unique = true),
        @Index(name = "uk_wx_id", columnList = "wx_id", unique = true),
        @Index(name = "uk_mobile", columnList = "mobile", unique = true)})
public class UcFakeUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create")
    private Instant gmtCreate;

    @Column(name = "gmt_modified")
    private Instant gmtModified;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "user_code", nullable = false)
    private String userCode;

    @Column(name = "real_name")
    private String realName;

    @Column(name = "nickname")
    private String nickName;

    @Column(name = "avatar")
    private String avatar;

    @Column(name = "create_time")
    private Instant createTime;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "identity_number")
    private String identityNumber;

    @Column(name = "evaluation_level")
    private String evaluationLevel;

    @Column(name = "evaluation_expire_time")
    private Instant evaluationExpireTime;

    @Column(name = "wx_id")
    private Long wxId;

    @Column(name = "pay_company_id")
    private Long payCompanyId;

}

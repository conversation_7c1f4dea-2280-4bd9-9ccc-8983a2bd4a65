package cn.shrise.radium.userservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "uc_evaluation_version_record", indexes = {
        @Index(name = "idx_version_id", columnList = "version_id"),
        @Index(name = "idx_operator_id", columnList = "operator_id")
})
public class UcEvaluationVersionRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "version_id", nullable = false)
    private Long versionId;

    @Column(name = "operator_id")
    private Integer operatorId;

    @Column(name = "content", length = 256)
    private String content;

}
package cn.shrise.radium.userservice.property;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "justcall")
@EnableConfigurationProperties
public class JustCallProperty {

    // 域名
    private String host;

    // 工号
    private String workNumber;

    // 用户名
    private String account;

    // 密码
    private String password;

    // 分机
    private String ext;

    // 分机密码
    private String extPassword;
}

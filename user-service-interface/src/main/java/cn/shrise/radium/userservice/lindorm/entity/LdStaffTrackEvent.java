package cn.shrise.radium.userservice.lindorm.entity;

import lombok.*;
import org.springframework.data.domain.Persistable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ld_staff_track_event")
public class LdStaffTrackEvent implements Persistable {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "gmt_create")
    private Long gmtCreate;

    @Column(name = "staff_id")
    private Integer staffId;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "url")
    private String url;

    @Column(name = "ip")
    private String ip;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "company_type")
    private Integer companyType;

    @Column(name = "track_type")
    private Integer trackType;

    @Column(name = "client_version")
    private String clientVersion;

    @Override
    public boolean isNew() {
        return true;
    }
}

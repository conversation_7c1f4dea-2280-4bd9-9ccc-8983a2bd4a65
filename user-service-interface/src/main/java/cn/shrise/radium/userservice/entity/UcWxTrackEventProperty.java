package cn.shrise.radium.userservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "uc_wx_track_event_property")
public class UcWxTrackEventProperty {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "Type", length = 128)
    private String type;

    @Column(name = "Value", length = 128)
    private String value;

    @Column(name = "CompanyType", nullable = false)
    private Integer companyType;

    @Column(name = "CreateTime")
    private Instant createTime;
}
package cn.shrise.radium.authservice.req;

import cn.shrise.radium.userservice.req.AppInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppleRegisterReq {

    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty("mobile")
    private String mobile;

    @NotBlank(message = "验证码不能为空")
    @ApiModelProperty("验证码")
    private String validateNo;

    @NotBlank(message = "identityToken不能为空")
    @ApiModelProperty("identityToken")
    private String identityToken;

    @NotNull(message = "companyType不能为空")
    @ApiModelProperty("companyType")
    private Integer companyType;

    private Integer productType;

    @ApiModelProperty(value = "App登录设备信息")
    private AppInfo appInfo;

    @ApiModelProperty(hidden = true)
    private String ip;

}

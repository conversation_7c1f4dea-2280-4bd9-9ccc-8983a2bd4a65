package cn.shrise.radium.authservice.req;

import cn.shrise.radium.userservice.req.AppInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppLoginReq {

    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty("手机号")
    private String mobile;

    @NotNull(message = "公司类型不能为空")
    @ApiModelProperty("公司类型")
    private Integer companyType;

    @NotBlank(message = "验证码不能为空")
    @ApiModelProperty("验证码")
    private String validateNo;

    @ApiModelProperty("记住我")
    private Boolean remember = true;

    @ApiModelProperty("产品类型")
    private Integer productType;

    @ApiModelProperty("设备信息")
    private String deviceInfo;

//    @ApiModelProperty("注册来源")
//    private String source;

    @ApiModelProperty(value = "App登录设备信息")
    private AppInfo appInfo;

    @ApiModelProperty(hidden = true)
    private String ip;

}

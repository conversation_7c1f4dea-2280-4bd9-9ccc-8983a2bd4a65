package cn.shrise.radium.authservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminVerifyReq {

    @NotNull(message = "公司类型不能为空")
    @ApiModelProperty("公司类型")
    private Integer companyType;

    @NotBlank(message = "用户名不能为空")
    @ApiModelProperty("用户名")
    private String userName;

    @NotBlank(message = "密码不能为空")
    @ApiModelProperty("密码")
    private String password;
}

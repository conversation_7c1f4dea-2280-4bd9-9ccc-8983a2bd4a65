package cn.shrise.radium.authservice;

import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.entity.AccessTokenPayload;
import cn.shrise.radium.authservice.req.*;
import cn.shrise.radium.authservice.resp.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.constant.ServiceConstant;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Duration;
import java.util.Map;

@FeignClient(value = ServiceConstant.AUTH_SERVICE)
public interface AuthClient {

    @ApiOperation("后台用户登录")
    @PostMapping("auth/admin/login")
    BaseResult<AdminLoginResp> adminLogin(@RequestBody @Valid AdminLoginReq req);

    @ApiOperation("后台钉钉登录")
    @PostMapping("auth/admin/login/dingding")
    BaseResult<AdminLoginResp> adminDdLogin(@RequestBody @Valid AdminDdLoginReq req);

    @ApiOperation("后台用户校验账号信息")
    @PostMapping("auth/admin/verify")
    BaseResult<AdminVerifyResp> adminVerify(@RequestBody @Valid AdminVerifyReq req);

    @ApiOperation("微信授权登录")
    @PostMapping("auth/wx/login")
    BaseResult<WxLoginResp> wxLogin(@RequestBody @Valid WxLoginReq req);

    @ApiOperation("微信授权登录")
    @PostMapping("auth/wx/pcLogin")
    BaseResult<GenerateTokenResp> wxPcLogin(@RequestBody @Valid WxLoginReq req);

    @ApiOperation("企业微信授权登录")
    @PostMapping("auth/work_wx/login")
    BaseResult<WorkWxLoginResp> workWxLogin(@RequestBody @Valid WorkWxLoginReq req);

    @PostMapping("token")
    @ApiOperation("生成AccessToken和RefreshToken")
    BaseResult<GenerateTokenResp> generateToken(@RequestBody @Valid GenerateAccessTokenReq req);

    @PostMapping("token/users")
    @ApiOperation("生成用户的AccessToken")
    BaseResult<AccessToken> generateUserAccessToken(@RequestBody @Valid GenerateUserAccessTokenReq req);

    @PutMapping("token")
    @ApiOperation("刷新token")
    @Deprecated
    BaseResult<AccessToken> refreshToken(@RequestBody @Valid RefreshTokenReq req);

    @PostMapping("token/refresh")
    @ApiOperation("刷新AccessToken")
    BaseResult<AccessToken> refreshAccessToken(@RequestBody @Valid RefreshAccessTokenReq req);

    @GetMapping("token/decode")
    @ApiOperation("解密AccessToken")
    BaseResult<AccessTokenPayload> decodeAccessToken(
            @RequestParam String tokenType,
            @RequestParam String accessToken);

    @GetMapping("token/decode")
    @ApiOperation("解密AccessToken")
    BaseResult<AccessTokenPayload> decodeAccessToken(
            @RequestParam String tokenType,
            @RequestParam String accessToken,
            @RequestParam Long acceptExpiresAt);

    @GetMapping("token/refresh-expire")
    @ApiOperation("获取refreshToken过期时间")
    BaseResult<Duration> getRefreshExpire(@RequestParam String tokenType);

    @GetMapping("token/wx-ext-payload")
    @ApiOperation("获取微信拓展信息")
    BaseResult<Map<String, Object>> wxExtPayload(
            @RequestParam Integer userId,
            @RequestParam(required = false) Integer accountType,
            @RequestParam(required = false) Integer wxId,
            @RequestParam(required = false) String openId);

    @ApiOperation("App用户登录")
    @PostMapping("auth/app/login")
    BaseResult<AppLoginResp> appLogin(@RequestBody @Valid AppLoginReq req);

    @ApiOperation("App微信登录")
    @PostMapping("auth/app/wxLogin")
    BaseResult<AppWxLoginResp> appWxLogin(@RequestBody @Valid AppWxLoginReq req);

    @ApiOperation("App微信注册")
    @PostMapping("auth/app/wxRegister")
    BaseResult<AppWxLoginResp> appWxRegister(@RequestBody @Valid AppWxRegisterReq req);

    @ApiOperation("App微信授权后一键注册")
    @PostMapping("auth/app/wxOneKeyRegister")
    BaseResult<AppWxLoginResp> appWxOneKeyRegister(@RequestBody @Valid AppWxRegisterReq req);

    @ApiOperation("app设备互踢检查")
    @PostMapping("auth/app/check-login-status")
    BaseResult<CheckLoginStatusResp> appCheckLoginStatus(
            @RequestParam Integer userId,
            @RequestParam String deviceId
    );

    @PostMapping("token/app/refresh")
    @ApiOperation("刷新AccessToken（App）")
    BaseResult<AccessToken> refreshAppAccessToken(@RequestBody @Valid RefreshAccessTokenReq req);

    @ApiOperation("生成App用户绑定wx的AccessToken和RefreshToken")
    @PostMapping("token/app/bindwx")
    BaseResult<GenerateTokenResp> appBindWxAccessToken(@RequestBody @Valid GenerateAppUserBindWxAccessTokenReq req);

    @ApiOperation("微信小程序授权登录")
    @PostMapping("auth/ma/login")
    BaseResult<WxMaLoginResp> wxMaLogin(@RequestBody @Valid WxMaLoginReq req);

    @ApiOperation("小程序默认手机号注册")
    @PostMapping("auth/ma/register")
    BaseResult<WxMaLoginResp> wxMaMobileRegister(@RequestBody @Valid WxMaRegisterReq req);

    @ApiOperation("用户手机号登录")
    @PostMapping("auth/mobile/login")
    BaseResult<UserMobileLoginResp> userMobileLogin(@RequestBody @Valid UserMobileLoginReq req);

    @ApiOperation("获取设备激活信息")
    @GetMapping("auth/device_info")
    BaseResult<String> getDeviceInfo(@RequestParam String activeCode);

    @ApiOperation("Apple授权登录")
    @PostMapping("auth/apple/login")
    BaseResult<AppleLoginResp> appleLogin(@RequestBody @Valid AppleLoginReq req);

    @ApiOperation("Apple注册")
    @PostMapping("auth/apple/register")
    BaseResult<AppleLoginResp> appleRegister(@RequestBody @Valid AppleRegisterReq req);
}

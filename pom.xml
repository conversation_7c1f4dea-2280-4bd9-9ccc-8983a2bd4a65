<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.shrise.radium</groupId>
        <artifactId>radium-parent</artifactId>
        <version>1.0.18</version>
        <relativePath/>
    </parent>

    <artifactId>wx-service</artifactId>
    <packaging>pom</packaging>
    <version>5.051402.5.0</version>
    <modules>
        <module>wx-service-interface</module>
        <module>wx-service-implement</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>common-lib</artifactId>
            <version>5.051402.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.shrise.radium</groupId>
        <artifactId>radium-parent</artifactId>
        <version>1.0.18</version>
        <relativePath/>
    </parent>

    <artifactId>admin-api</artifactId>
    <name>admin-api</name>
    <version>5.0625.3.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>common-lib</artifactId>
            <version>5.0611.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>auth-service-interface</artifactId>
            <version>5.0207.3.1</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.shrise.radium</groupId>
                    <artifactId>user-service-interface</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>user-service-interface</artifactId>
            <version>5.0625.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>workwx-service-interface</artifactId>
            <version>5.0625.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>workwx-robot-service-interface</artifactId>
            <version>5.0102.5.3</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>rpa-service-interface</artifactId>
            <version>4.1218.5.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>douyin-service-interface</artifactId>
            <version>5.0414.3.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>statistics-service-interface</artifactId>
            <version>5.0609.3.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>order-service-interface</artifactId>
            <version>5.0625.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>content-service-interface</artifactId>
            <version>5.0611.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>wx-service-interface</artifactId>
            <version>5.051402.5.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>notification-service-interface</artifactId>
            <version>5.0508.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>trade-service-interface</artifactId>
            <version>5.0507.4.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>cd-adviser-service-interface</artifactId>
            <version>5.0529.5.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>marketing-service-interface</artifactId>
            <version>5.0609.3.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>broker-service-interface</artifactId>
            <version>3.0616.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ua-parser</groupId>
            <artifactId>uap-java</artifactId>
            <version>1.5.3</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-resolver-dns-native-macos</artifactId>
            <version>4.1.85.Final</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>im-service-interface</artifactId>
            <version>5.061901.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-artifact</artifactId>
            <version>3.9.1</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>quote-service-interface</artifactId>
            <version>4.0204.5.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>dingding-service-interface</artifactId>
            <version>5.0423.5.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>wei-mob-service-interface</artifactId>
            <version>4.0812.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>robo-adviser-service-interface</artifactId>
            <version>5.0508.2.2</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>secure-service-interface</artifactId>
            <version>5.0410.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>strategy-stock-service-interface</artifactId>
            <version>4.0827.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>workwx-chat-service-interface</artifactId>
            <version>5.0318.4.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>partner-service-interface</artifactId>
            <version>5.0226.5.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>stock-ai-diagnose-service-interface</artifactId>
            <version>5.0318.4.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba.arms.apm</groupId>-->
        <!--            <artifactId>arms-sdk</artifactId>-->
        <!--            <version>1.7.3</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>workwx-chat-image-material-service-interface</artifactId>
            <version>5.0227.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>compliance-service-interface</artifactId>
            <version>5.0625.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>

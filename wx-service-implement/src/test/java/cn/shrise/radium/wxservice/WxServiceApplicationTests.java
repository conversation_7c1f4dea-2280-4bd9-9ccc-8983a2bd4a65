package cn.shrise.radium.wxservice;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.wxservice.ext.WxMpMarketingServiceExtImpl;
import cn.shrise.radium.wxservice.ext.WxMpUserActionExt;
import cn.shrise.radium.wxservice.properties.MultiWxMpProperties;
import cn.shrise.radium.wxservice.service.WxConfigService;
import cn.shrise.radium.wxservice.service.WxOauth2Service;
import cn.shrise.radium.wxservice.util.WxCommonUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class WxServiceApplicationTests {

    @Autowired
    private WxMpService wxService;

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private MultiWxMpProperties properties;

    @Autowired
    private WxOauth2Service wxOauth2Service;

    @Autowired
    private WxCommonUtil wxCommonUtil;
    @Autowired
    private WxConfigService wxConfigService;

    @Test
    void contextLoads() {
        String result;
        List<WxMpUserActionExt> actions = new ArrayList<>();
        WxMpUserActionExt action = new WxMpUserActionExt(888888888L, "http://wx.shrise.cn",
                Convert.toLong(DateUtil.currentSeconds()), "REGISTER", "wxfb6c1a0b77a9acdc", "or3Is5vaOS5" +
                "-JTnRkK6vU6nQEXtY", 1);
        actions.add(action);
        WxMpMarketingServiceExtImpl marketingService =
                (WxMpMarketingServiceExtImpl)wxService.switchoverTo("wxfb6c1a0b77a9acdc").getMarketingService();
        try {
            marketingService.addUserActionExt(actions);
            result = "success";
        } catch (WxErrorException e) {
            result = e.getError().getJson();
            System.out.println(result);
        }
    }

    @Test
    void testCode() {
        try {
            wxMaService.switchoverTo(String.valueOf(2099)).getUserService().getNewPhoneNoInfo("43821f47cb13debfa19555f98c48c5797d81b0e29aadc8866942329480b1b221");
            WxMaPhoneNumberInfo phoneNumberInfo = wxMaService.getUserService().getNewPhoneNoInfo("43821f47cb13debfa19555f98c48c5797d81b0e29aadc8866942329480b1b221");
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }

    }

    @Test
    void testMethod() {
        wxCommonUtil.desensitizePhone("云姐人力***********");
    }

    @Test
    void test3() {
        wxOauth2Service.getWechatMiniPayerConfig(2099, 128280, 2048);
    }

    @Test
    void testOauth() {
        wxConfigService.getAuthorizerConfig(45, "product");
    }

    @Test
    void testGetConfig() {
        wxConfigService.getAppIdByAccountType(2099);
    }
}
